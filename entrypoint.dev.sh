#!/bin/bash

export TERM=xterm
export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token \
        --domain multiplier-artifacts \
        --domain-owner ************ \
        --query authorizationToken \
        --output text`
export AWS_ACCOUNT_ID=************

MONITOR_DIR="./app/src"

echo "Starting app, monitoring $MONITOR_DIR for changes"

gradle app:bootRun -x test &
while inotifywait --event modify --event move --event create --event delete \
            -r $MONITOR_DIR; do
      gradle build -x test
done
