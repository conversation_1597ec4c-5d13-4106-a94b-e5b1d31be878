environments:
  GRPC_CLIENT_COMPANYSERVICE_NEGOTIATIONTYPE: TLS
  GRPC_CLIENT_CORESERVICE_NEGOTIATIONTYPE: TLS
  GRPC_CLIENT_COUNTRYSERVICE_NEGOTIATIONTYPE: TLS
  GRPC_CLIENT_MEMBERSERVICE_NEGOTIATIONTYPE: TLS
  GRPC_SERVER_PORT: '9090'
  JAVA_HEAP_MAX_MEM: -Xmx1g
  SENTRY_DSN: https://<EMAIL>/4505074144772096
  SENTRY_ENVIRONMENT: staging
  SERVER_PORT: '8080'
  SPRING_PROFILES_ACTIVE: stage
  awslogs-group: /stg/app/tech/contractOnboardingService/cloudwatchLogGroup
  awslogs-stream-prefix: ecs
kind: v2
name: contractOnboardingService
resources:
  cpu: 1024
  memory: 2048
secrets:
  APM_SERVER_URL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/apmServer/param
  APM_TOKEN: arn:aws:secretsmanager:ap-southeast-1:133139256227:secret:/mgt/monitoring/devops/elasticsearch/apmtoken/secret-qmnKQx
  GROWTHBOOK_BASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/growthbook/param
  GROWTHBOOK_ENVKEY: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/growthbook/env/key/param
  GRPC_CLIENT_AUTHORITYSERVICE_ADDRESS: /stg/app/tech/services/shared/url/grpc/userService/param
  GRPC_CLIENT_COMPANYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/companyService/param
  GRPC_CLIENT_CONTRACTSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/contractService/param
  GRPC_CLIENT_CORESERVICE_ADDRESS: /stg/app/tech/services/shared/url/grpc/coreService/param
  GRPC_CLIENT_COUNTRYSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/countryService/param
  GRPC_CLIENT_MEMBERSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/memberService/param
  GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS: /stg/app/tech/services/shared/url/grpc/orgManagementService/param
  GRPC_CLIENT_PAYROLLSERVICE_ADDRESS: /stg/app/tech/services/shared/url/grpc/payrollService/param
  GRPC_CLIENT_PAYSE_ADDRESS: /stg/app/tech/services/shared/url/grpc/paySeService/param
  GRPC_CLIENT_TIMEOFFSERVICE_ADDRESS: /stg/app/tech/services/shared/url/grpc/timeoffService/param
  GRPC_CLIENT_COMPENSATIONSERVICE_ADDRESS: /stg/app/tech/services/shared/url/grpc/compensationService/param
  GRPC_CLIENT_PIGEONSERVICE_ADDRESS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/grpc/pigeonService/param
  JWT_PUBLIC_KEY: /stg/app/tech/services/shared/employee/jwt/publicKey/param
  PLATFORM_DOCGEN_BASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/platform/docgenService/param
  PLATFORM_FRONTEND_BASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/platform/frontend/param
  PLATFORM_NOTIFICATION_SLACK_CHANNEL_BULK-ONBOARDING: /stg/app/tech/services/shared/email/slack/bulkOnboarding/param
  PLATFORM_CONTRACT_ONBOARDING_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_PIGEON_KAFKA_BOOTSTRAPSERVERS: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/kafka/bootstrapServers/param
  PLATFORM_USERSERVICE_BASEURL: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/url/platform/userService/param
  SPRING_DATASOURCE_PASSWORD: /stg/app/tech/services/contractOnboardingService/db/password
  SPRING_DATASOURCE_URL: /stg/app/tech/services/coreService/db/url/param
  SPRING_DATASOURCE_USERNAME: /stg/app/tech/services/contractOnboardingService/db/user
  USER_SERVICE_PASSWORD: arn:aws:ssm:ap-southeast-1:027283923462:parameter/stg/app/tech/services/shared/platform/userService/password/param
