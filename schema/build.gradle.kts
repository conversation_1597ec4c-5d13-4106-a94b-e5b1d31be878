import com.google.protobuf.gradle.generateProtoTasks
import com.google.protobuf.gradle.id
import com.google.protobuf.gradle.plugins
import com.google.protobuf.gradle.protobuf
import com.google.protobuf.gradle.protoc
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    kotlin("jvm")

    alias(multiplier.plugins.publish.with.summary)
    id("com.google.protobuf")
}

java.sourceCompatibility = JavaVersion.VERSION_17
java.targetCompatibility = JavaVersion.VERSION_17

dependencies {
    api("com.google.protobuf:protobuf-kotlin")
    api("io.grpc:grpc-kotlin-stub")
    implementation("io.grpc:grpc-protobuf")
    implementation("com.multiplier.grpc:grpc-common")
}

val protobufVersion: String by extra
val grpcVersion: String by extra
val grpcKotlinVersion: String by extra
protobuf {
    generatedFilesBaseDir = "$projectDir/build/generated"
    tasks.getByName<Delete>("clean") {
        delete(generatedFilesBaseDir)
    }
    protoc {
        artifact = "com.google.protobuf:protoc:$protobufVersion"
    }
    plugins {
        id("grpc") {
            artifact = "io.grpc:protoc-gen-grpc-java:$grpcVersion"
        }
        id("grpckt") {
            artifact = "io.grpc:protoc-gen-grpc-kotlin:$grpcKotlinVersion:jdk8@jar"
        }
    }
    generateProtoTasks {
        all().forEach {
            it.plugins {
                id("grpc")
                id("grpckt")
            }
            it.builtins {
                id("kotlin")
            }
            it.doFirst {
                delete(it.outputs)
            }
        }
    }
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict", "-opt-in=kotlin.RequiresOptIn")
        jvmTarget = "17"
    }
}
