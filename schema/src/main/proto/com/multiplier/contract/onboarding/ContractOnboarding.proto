syntax = "proto3";

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/type/date.proto";
import "com/multiplier/grpc/common/country/v2/country.proto";
import "com/multiplier/grpc/common/contract/v2/contract.proto";
import "com/multiplier/grpc/common/bulkupload/v1/bulk_upload_field_requirements.proto";
import "com/multiplier/grpc/common/bulkupload/v1/bulk_upload_validation.proto";
import "com/multiplier/grpc/common/bulkupload/v1/bulk_upload_upsert.proto";
import "com/multiplier/grpc/common/bulkupload/v1/bulk_upload_field_data.proto";

package com.multiplier.contract.onboarding.schema;

option java_multiple_files = true;

message BulkOnboardRequest {
  repeated BulkOnboardInput inputs = 1;
  BulkOnboardOption option = 2;
}

message BulkOnboardInput {
  int32 requestId = 1;
  map<string, string> properties = 2;
  optional string group = 3; // EMPLOYMENT_DATA, MEMBER_DATA, COMPENSATION_DATA
}

message BulkOnboardOption {
  com.multiplier.grpc.common.contract.v2.ContractType contractType = 1;
  int64 companyId = 2;
  string context = 3; // GLOBAL_PAYROLL, HRIS_PROFILE_DATA
  optional int64 entityId = 4;
  optional com.multiplier.grpc.common.country.v2.CountryCode countryCode = 5;
}

message BulkOnboardValidationResponse {
  repeated BulkOnboardValidationResult validationResults = 1;
}

message BulkOnboardValidationResult {
  int32 requestId = 1;
  map<string, string> validatedProperties = 2;
  bool success = 3;
  repeated string errors = 4;
}

message BulkOnboardResponse {
  repeated BulkOnboardResult results = 1;
}

message BulkOnboardResult {
  int32 requestId = 1;
  int64 contractId = 2;
}

message BulkOnboardDataSpecsResponse {
  repeated BulkOnboardDataSpec specs = 1;
}

message BulkOnboardDataSpec {
  string key = 1;
  bool required = 2;
  repeated string values = 3;
  string label = 4;
  string group = 5;
}

message ActivationCutoffRequest {
  repeated  int64 contractIds = 1;
}

message ActivationCutoffResponse {
  repeated  ContractCutoff contractsCutoffResponse = 1;
}

message ContractCutoff {
  int64 contractId = 1;
  string activationCutoff = 2; // Format: "yyyy-MM-dd"
}

service ContractOnboardingService {
  rpc validateBulkOnboarding(BulkOnboardRequest) returns (BulkOnboardValidationResponse) {}
  rpc bulkOnboard(BulkOnboardRequest) returns (BulkOnboardResponse) {}
  rpc getBulkOnboardDataSpecs(BulkOnboardOption) returns (BulkOnboardDataSpecsResponse) {}
  rpc getActivationCutoffDataForContracts(ActivationCutoffRequest) returns (ActivationCutoffResponse) {}

  rpc getFieldRequirements(multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest) returns (multiplier.grpc.common.bulkupload.v1.FieldRequirementsResponse);
  rpc getFieldData(multiplier.grpc.common.bulkupload.v1.BulkDataRequest) returns (multiplier.grpc.common.bulkupload.v1.BulkDataResponse);
  rpc bulkValidateUpsertInput(multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkRequest) returns (multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse);
  rpc bulkUpsert(multiplier.grpc.common.bulkupload.v1.UpsertBulkRequest) returns (multiplier.grpc.common.bulkupload.v1.UpsertBulkResponse);
}
