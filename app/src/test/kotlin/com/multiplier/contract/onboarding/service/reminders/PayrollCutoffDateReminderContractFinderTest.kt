package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.PayrollCyclesService
import com.multiplier.contract.onboarding.service.toGrpcDate
import com.multiplier.contract.onboarding.service.toTimeStamp
import com.multiplier.contract.onboarding.testing.getPayrollCycleDTO
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractStatus
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import java.time.*
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@ExtendWith(MockKExtension::class)
class PayrollCutoffDateReminderContractFinderTest {

    private val onboardingServiceAdapter = mockk<OnboardingServiceAdapter>()
    private val contractServiceAdapter = mockk<ContractServiceAdapter>()
    private val payrollCyclesService = mockk<PayrollCyclesService>()
    private val compensationServiceAdapter = mockk<CompensationServiceAdapter>()
    private val featureFlagService = mockk<FeatureFlagService>()

    private val reminderContractFinderHelper =
        ReminderContractFinderHelper(
            onboardingServiceAdapter,
            contractServiceAdapter,
            payrollCyclesService,
            compensationServiceAdapter,
            featureFlagService,
        )

    private val underTest =
        PayrollCutoffDateReminderContractFinder(
            contractServiceAdapter,
            reminderContractFinderHelper,
        )

    @BeforeEach
    fun beforeEach() {
        every {
            payrollCyclesService.getPayrollCyclesForContracts(
                any<LocalDate>(), any<List<Contract>>())
        } answers
            {
                val sendingDate = firstArg<LocalDate>()
                val contracts = secondArg<List<Contract>>()

                contracts.associate { contract ->
                    contract.id to
                        getPayrollCycleDTO(
                            contract, sendingDate, PayrollCycleDTO.PayFrequency.MONTHLY)
                }
            }
    }

    @ParameterizedTest
    @CsvSource(
        "2023, 01, 26",
        "2023, 02,  1",
        "2023, 02,  9",
        "2023, 02, 11",
    )
    fun `returns correct payroll cycles with contracts`(
        year: Int,
        month: Int,
        day: Int,
    ) {
        val sendingDate = LocalDate.of(year, month, day)
        val maxStartDate = LocalDate.of(year, 2, 20)

        mockEligibleContractWithStartOnRange(sendingDate, maxStartDate)
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).hasSize(1)
        assertThat(result[0].payrollMonth.month).isEqualTo(Month.FEBRUARY)
        assertThat(result[0].contractIds).hasSize(daysBetween(sendingDate, maxStartDate))
    }

    @ParameterizedTest
    @CsvSource(
        "2023, 01, 25",
        "2023, 01, 31",
        "2023, 02, 10",
    )
    fun `returns empty when there is no matching payroll cycles`(
        year: Int,
        month: Int,
        day: Int,
    ) {
        val sendingDate = LocalDate.of(year, month, day)

        mockEligibleContractWithStartOnRange(sendingDate, sendingDate.plusDays(21))
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).isEmpty()
    }

    @ParameterizedTest
    @CsvSource(
        "2023, 11, 24",
        "2023, 12,  1",
        "2023, 12,  4",
    )
    fun `for December - returns correct payroll cycles with contracts`(
        year: Int,
        month: Int,
        day: Int,
    ) {
        val sendingDate = LocalDate.of(year, month, day)
        val maxStartDate = LocalDate.of(year, 12, 20)

        mockEligibleContractWithStartOnRange(sendingDate, maxStartDate)
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).hasSize(1)
        assertThat(result[0].payrollMonth.month).isEqualTo(Month.DECEMBER)
        assertThat(result[0].contractIds).hasSize(daysBetween(sendingDate, maxStartDate))
    }

    @ParameterizedTest
    @CsvSource(
        "2023, 11, 26",
        "2023, 11, 30",
    )
    fun `for December - returns empty when there is no matching payroll cycles`(
        year: Int,
        month: Int,
        day: Int,
    ) {
        val sendingDate = LocalDate.of(year, month, day)

        mockEligibleContractWithStartOnRange(sendingDate, sendingDate.plusDays(21))
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).isEmpty()
    }

    private fun turnFeatureFlagOn() {
        every {
            featureFlagService.feature(eq(FeatureFlags.ONBOARDING_NOTIFICATION), any())
        } returns
            GBFeatureResult(
                value = true,
                on = true,
                off = false,
                source = GBFeatureSource.force,
                experiment = null,
                experimentResult = null)
    }

    private fun mockEligibleContractWithStartOnRange(
        minStartDate: LocalDate,
        maxStartDate: LocalDate,
        createdDate: LocalDate = LocalDate.of(2023, 1, 1),
    ): List<Contract> {
        val contracts: LinkedList<Contract> = LinkedList<Contract>()
        var contractId: Long = 1
        var startDate = minStartDate
        while (!startDate.isAfter(maxStartDate)) {
            contracts.add(
                Contract.newBuilder()
                    .setId(contractId++)
                    .setStatus(ContractStatus.ONBOARDING)
                    .setCompanyId(11L)
                    .setStartOn(startDate.atStartOfDay().toGrpcDate())
                    .setWorkStatus(ContractOuterClass.CountryWorkStatus.RESIDENT)
                    .setCreatedOn(createdDate.atStartOfDay().toTimeStamp())
                    .build())
            startDate = startDate.plusDays(1)
        }

        every { contractServiceAdapter.getContractsBy(any()) } returns contracts

        val contractIds = contracts.map { it.id }

        every { onboardingServiceAdapter.getAllOnboardingsByContractIds(any(), any()) } returns
            contractIds.associateWith {
                Onboarding(
                    id = 111L,
                    status = OnboardingStatus.CREATED,
                    currentStep = OnboardingStep.ONBOARDING_REVIEW,
                    contractId = it,
                    experience = "company",
                )
            }

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns
            contractIds.map {
                Compliance(
                    contractId = it,
                    agreementId = 123,
                    type = ComplianceType.MULTIPLIER,
                    agreementType = ContractAgreementType.MULTIPLIER_TEMPLATE,
                )
            }

        every { compensationServiceAdapter.getCurrentCompensationByContractIds(any()) } returns
            contractIds.associateWith {
                Compensation.newBuilder()
                    .setPostProbationBasePay(
                        CompensationOuterClass.CompensationPayComponent.newBuilder()
                            .setPayFrequency(
                                CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY)
                            .setAmountType(CompensationOuterClass.PayAmountType.FIXED_AMOUNT)
                            .build())
                    .build()
            }

        return contracts
    }
}

private fun daysBetween(start: LocalDate, end: LocalDate): Int = Period.between(start, end).days + 1
