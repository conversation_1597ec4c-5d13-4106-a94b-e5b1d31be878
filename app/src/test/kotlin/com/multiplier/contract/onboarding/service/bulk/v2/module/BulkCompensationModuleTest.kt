package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.CompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.CompensationSourceServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkCompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkPayrollServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.country.CompensationStandard
import com.multiplier.contract.onboarding.domain.model.country.CountryState
import com.multiplier.contract.onboarding.types.*
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkCompensationModuleTest {

    @MockK private lateinit var compensationServiceAdapter: CompensationServiceAdapter
    @MockK private lateinit var bulkCompensationServiceAdapter: BulkCompensationServiceAdapter
    @MockK private lateinit var payrollServiceAdapter: BulkPayrollServiceAdapter
    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter
    @MockK private lateinit var bulkCompensationModuleV2: BulkCompensationModuleV2
    @MockK(relaxed = true)
    private lateinit var compensationSourceServiceAdapter: CompensationSourceServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkCompensationModule

    @Nested
    inner class GetDataSpecs {

        @BeforeEach
        fun setUp() {
            every { payrollServiceAdapter.getAllowanceStructure(any()) } returns emptyList()
        }

        @Test
        fun `should combine all payroll frequencies and rate frequencies from all states in US`() {
            val california = CountryState(CountryCode.USA, "CA", "California")
            val newyork = CountryState(CountryCode.USA, "NY", "New York")
            val usaStates =
                mapOf(
                    CountryCode.USA to
                        listOf(
                            california,
                            newyork,
                        ))
            every { countryServiceAdapter.getCountryStates(setOf(CountryCode.USA)) } returns
                usaStates
            val usaCompensationStandards =
                mapOf(
                    california to
                        CompensationStandard(
                            listOf(PayFrequency.MONTHLY), listOf(RateFrequency.MONTHLY)),
                    newyork to
                        CompensationStandard(
                            listOf(PayFrequency.SEMIMONTHLY), listOf(RateFrequency.ANNUALLY)),
                )
            every {
                countryServiceAdapter.getCompensationStandards(
                    setOf(
                        california,
                        newyork,
                    ))
            } returns usaCompensationStandards

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        CountryCode.USA,
                        ContractType.HR_MEMBER,
                        1,
                        2,
                        BulkOnboardingContext.GLOBAL_PAYROLL),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "payrollFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("MONTHLY", "SEMIMONTHLY")),
                    DataSpec(
                        key = "rateFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("MONTHLY", "ANNUALLY")),
                )
        }

        @Test
        fun `should use payroll frequencies and rate frequencies from SG`() {
            val singapore = CountryState(CountryCode.SGP, "")
            every { countryServiceAdapter.getCountryStates(setOf(CountryCode.SGP)) } returns
                emptyMap()
            every { countryServiceAdapter.getCompensationStandards(setOf(singapore)) } returns
                mapOf(
                    singapore to
                        CompensationStandard(
                            listOf(PayFrequency.SEMIMONTHLY), listOf(RateFrequency.MONTHLY)),
                )

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        CountryCode.SGP,
                        ContractType.HR_MEMBER,
                        1,
                        2,
                        BulkOnboardingContext.GLOBAL_PAYROLL),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "payrollFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("SEMIMONTHLY")),
                    DataSpec(
                        key = "rateFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("MONTHLY")),
                )
        }

        @Test
        fun `should add HOURLY billing frequency for GP in TWN`() {
            val taiwan = CountryState(CountryCode.TWN, "")
            every { countryServiceAdapter.getCountryStates(setOf(CountryCode.TWN)) } returns
                emptyMap()
            every { countryServiceAdapter.getCompensationStandards(setOf(taiwan)) } returns
                mapOf(
                    taiwan to
                        CompensationStandard(
                            listOf(PayFrequency.SEMIMONTHLY), listOf(RateFrequency.MONTHLY)),
                )

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        CountryCode.TWN,
                        ContractType.HR_MEMBER,
                        1,
                        2,
                        BulkOnboardingContext.GLOBAL_PAYROLL),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "payrollFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("SEMIMONTHLY")),
                    DataSpec(
                        key = "rateFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("HOURLY", "MONTHLY")),
                )
        }

        @Test
        fun `should not add HOURLY billing frequency for HRIS in TWN`() {
            val taiwan = CountryState(CountryCode.TWN, "")
            every { countryServiceAdapter.getCountryStates(setOf(CountryCode.TWN)) } returns
                emptyMap()
            every { countryServiceAdapter.getCompensationStandards(setOf(taiwan)) } returns
                mapOf(
                    taiwan to
                        CompensationStandard(
                            listOf(PayFrequency.SEMIMONTHLY), listOf(RateFrequency.MONTHLY)),
                )

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        CountryCode.TWN,
                        ContractType.HR_MEMBER,
                        1,
                        2,
                        BulkOnboardingContext.HRIS_PROFILE_DATA),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "payrollFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("SEMIMONTHLY")),
                    DataSpec(
                        key = "rateFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("MONTHLY")),
                )
        }

        @Test
        fun `should add WEEKLY and BIWEEKLY pay frequency for GP in CAN`() {
            val canada = CountryState(CountryCode.CAN, "")
            every { countryServiceAdapter.getCountryStates(setOf(CountryCode.CAN)) } returns
                emptyMap()
            every { countryServiceAdapter.getCompensationStandards(setOf(canada)) } returns
                mapOf(
                    canada to
                        CompensationStandard(
                            listOf(PayFrequency.SEMIMONTHLY), listOf(RateFrequency.MONTHLY)),
                )

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        CountryCode.CAN,
                        ContractType.HR_MEMBER,
                        1,
                        2,
                        BulkOnboardingContext.GLOBAL_PAYROLL),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "payrollFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("SEMIMONTHLY", "WEEKLY", "BIWEEKLY")),
                    DataSpec(
                        key = "rateFrequency",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("MONTHLY")),
                )
        }
    }
}
