package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberAddressServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.Address
import com.multiplier.contract.onboarding.domain.model.AddressDetail
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressCitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressCountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressLine1Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressLine2Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressPostalCodeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressStateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressCitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressCountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressLine1Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressLine2Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressPostalCodeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressStateSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.member.schema.UpsertAddressInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberAddressDataServiceTest {

    @MockK private lateinit var memberServiceAdapterMock: MemberServiceAdapter

    @MockK private lateinit var bulkMemberAddressServiceAdapterMock: BulkMemberAddressServiceAdapter

    @InjectMockKs private lateinit var bulkMemberAddressDataService: BulkMemberAddressDataService

    @Test
    fun `should return correct validation result when validate addresses`() {
        val employeeData =
            listOf(
                EmployeeData(
                    EmployeeIdentification(rowNumber = 2),
                    mapOf(CurrentAddressLine1Spec.key to "266 DDN")))
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                .build()

        val expected =
            listOf(
                GrpcValidationResult(
                    success = true,
                    errors = emptyList(),
                    validationId = "2",
                    input = UpsertAddressInput.newBuilder().setRequestId("2").build(),
                ))
        every {
            bulkMemberAddressServiceAdapterMock.validateUpsertAddresses(employeeData, options)
        } returns expected
        assertEquals(expected, bulkMemberAddressDataService.validate(employeeData, options))
    }

    @Test
    fun `should return error messages when upsert address failed`() {
        val expectedErrors = listOf("Not good")
        every { bulkMemberAddressServiceAdapterMock.upsertAddresses(any()) } returns
            listOf(CreationResult.Companion.error("1", expectedErrors))
        val inputs =
            listOf(
                ValidInput(
                    validationId = "1",
                    input = UpsertAddressInput.newBuilder().build(),
                ))

        val memberContext =
            MemberContext(
                mapOf(
                    "1" to 1,
                    "2" to 2,
                ))
        assertEquals(
            expectedErrors,
            bulkMemberAddressDataService.upsertAddresses(
                inputs, memberContext, BulkOnboardingOptions()))
    }

    @Test
    fun `should return correct member data context when get data for specs`() {
        val memberIds = setOf(1L, 2L)
        val addressDetails =
            listOf(
                AddressDetail(
                    memberId = 1,
                    currentAddress =
                        Address(
                            street = "TCV",
                            line1 = "196",
                            line2 = "Tam Thuan",
                            city = "Da Nang",
                            state = "Son Tra",
                            province = "Quang Nam",
                            country = CountryCode.VNM,
                            zipCode = "550001",
                            postalCode = "550002",
                        ),
                    permanentAddress =
                        Address(
                            street = "DDN",
                            line1 = "266",
                            line2 = "An Hai Bac",
                            city = "Little Elm",
                            state = "Texas",
                            province = "Dallas",
                            country = CountryCode.USA,
                            zipCode = "550003",
                            postalCode = "550004",
                        ),
                ),
                AddressDetail(
                    memberId = 2,
                    currentAddress =
                        Address(
                            street = "DDN",
                            line1 = "266",
                            line2 = "An Hai Bac",
                            city = "Da Nang",
                            state = "Son Tra",
                            province = "Quang Nam",
                            country = CountryCode.VNM,
                            zipCode = "550005",
                            postalCode = "550006",
                        ),
                ))
        every { memberServiceAdapterMock.getMemberAddressDetails(memberIds) } returns addressDetails

        val dataSpecs =
            listOf(
                CurrentAddressLine1Spec,
                CurrentAddressLine2Spec,
                CurrentAddressCitySpec,
                CurrentAddressStateSpec,
                CurrentAddressCountrySpec,
                CurrentAddressPostalCodeSpec,
                PermanentAddressLine1Spec,
                PermanentAddressLine2Spec,
                PermanentAddressCitySpec,
                PermanentAddressStateSpec,
                PermanentAddressCountrySpec,
                PermanentAddressPostalCodeSpec,
            )
        val result = bulkMemberAddressDataService.getDataForSpecs(dataSpecs, memberIds)

        val expected =
            MemberDataContext(
                mapOf(
                    1L to
                        EmployeeDataChunk(
                            mapOf(
                                CurrentAddressLine1Spec.key to "196",
                                CurrentAddressLine2Spec.key to "Tam Thuan",
                                CurrentAddressCitySpec.key to "Da Nang",
                                CurrentAddressStateSpec.key to "Son Tra",
                                CurrentAddressCountrySpec.key to "VNM",
                                CurrentAddressPostalCodeSpec.key to "550002",
                                PermanentAddressLine1Spec.key to "266",
                                PermanentAddressLine2Spec.key to "An Hai Bac",
                                PermanentAddressCitySpec.key to "Little Elm",
                                PermanentAddressStateSpec.key to "Texas",
                                PermanentAddressCountrySpec.key to "USA",
                                PermanentAddressPostalCodeSpec.key to "550004",
                            )),
                    2L to
                        EmployeeDataChunk(
                            mapOf(
                                CurrentAddressLine1Spec.key to "266",
                                CurrentAddressLine2Spec.key to "An Hai Bac",
                                CurrentAddressCitySpec.key to "Da Nang",
                                CurrentAddressStateSpec.key to "Son Tra",
                                CurrentAddressCountrySpec.key to "VNM",
                                CurrentAddressPostalCodeSpec.key to "550006",
                            ))))
        assertEquals(expected, result)
    }
}
