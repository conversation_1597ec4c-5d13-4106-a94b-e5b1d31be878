package com.multiplier.contract.onboarding.service.helpers

import com.multiplier.contract.onboarding.domain.model.Member
import com.multiplier.contract.onboarding.domain.model.Persona
import com.multiplier.contract.onboarding.types.Gender

object MemberTestDataFactory {

    fun createMember(
        id: Long = 1234,
        firstName: String = "",
        lastName: String = "Doe",
        email: String = "<EMAIL>",
        fullName: String = "John Doe",
        userId: String = "123",
        persona: Persona = Person<PERSON>("Member"),
        gender: Gender = Gender.UNSPECIFIED
    ): Member {
        return Member(
            id = id,
            firstName = firstName,
            lastName = lastName,
            email = email,
            fullName = fullName,
            userId = userId,
            persona = persona,
            gender = gender)
    }
}
