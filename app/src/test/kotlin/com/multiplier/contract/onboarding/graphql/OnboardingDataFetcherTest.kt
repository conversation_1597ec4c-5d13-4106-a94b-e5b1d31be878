package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.ContractOnboardingApplication
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.Clock
import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import com.multiplier.contract.onboarding.domain.model.OnboardingTask
import com.multiplier.contract.onboarding.domain.model.Person
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.service.OnboardingTasksService
import com.multiplier.contract.onboarding.service.PayrollCyclesService
import com.multiplier.contract.onboarding.testing.*
import com.multiplier.contract.onboarding.types.Urgency
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import com.netflix.graphql.dgs.autoconfig.DgsAutoConfiguration
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration

@ActiveProfiles("integration-test")
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class])
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [ContractOnboardingApplication::class, DgsAutoConfiguration::class])
class OnboardingDataFetcherTest(
    @LocalServerPort private val port: Int,
) {
    @MockkBean private lateinit var payrollCyclesService: PayrollCyclesService
    @MockkBean private lateinit var onboardingTasksService: OnboardingTasksService
    @MockkBean private lateinit var featureFlagService: FeatureFlagService
    @MockkBean private lateinit var countryCache: CountryCache
    @MockkBean private lateinit var clock: Clock

    private val graphqlClient = GraphQLTestClient(port)

    @BeforeEach
    fun setup() {
        every { featureFlagService.feature(any(), any()) } returns
            GBFeatureResult(value = null, source = GBFeatureSource.defaultValue)
    }

    @Test
    fun `get onboarding tasks`() {
        every { onboardingTasksService.getOnboardingTasksByOnboardingId(setOf(123L)) } returns
            mapOf(
                Pair(
                    123L,
                    listOf(
                        OnboardingTask(
                            name = OnboardingTaskName.SIGN_CONTRACT,
                            completed = true,
                            pendingOn = somePersonDto(234)))))

        val response =
            graphqlClient.execute(
                query = "/queries/GetOnboardingTasks.graphql",
                variables = contractOnboardingParentParams(123))

        val tasks = response.jsonAt("/data/_entities/0/tasks")

        assertThat(tasks)
            .isEqualTo(
                "[{\"name\":\"SIGN_CONTRACT\",\"order\":0,\"completed\":true,\"pendingOn\":{\"id\":\"234\"}}]")
    }

    @Nested
    inner class ActivationCutoffData {
        @Test
        fun returns_correct_data() {
            mockDate("2023-05-01")
            every {
                payrollCyclesService.getPayrollCyclesByOnboardingId(eq(setOf(123L)), any())
            } returns
                mapOf(
                    Pair(
                        123L,
                        OnboardingTaskHelper.buildPayrollCycleDTO(
                            cutoffDate = LocalDate.parse("2023-05-06"))))

            val response =
                graphqlClient.execute(
                    query = "/queries/GetActivationCutoffData.graphql",
                    variables = contractOnboardingParentParams(123))

            val cutoff =
                LocalDate.parse(
                    response
                        .jsonAt("/data/_entities/0/activationCutoffData/date")
                        .replace("\"", ""))
            val urgency =
                Urgency.valueOf(
                    response
                        .jsonAt("/data/_entities/0/activationCutoffData/urgency")
                        .replace("\"", ""))

            assertAll({
                assertThat(cutoff).isEqualTo(LocalDate.of(2023, 5, 6))
                assertThat(urgency).isEqualTo(Urgency.URGENT)
            })
        }
    }

    @Nested
    inner class GetActivationCutoff {
        @Test
        fun returns_correct_data() {
            mockDate("2023-05-01")
            every {
                payrollCyclesService.getPayrollCyclesByOnboardingId(eq(setOf(123L)), any())
            } returns
                mapOf(
                    Pair(
                        123L,
                        OnboardingTaskHelper.buildPayrollCycleDTO(
                            cutoffDate = LocalDate.parse("2023-05-06"))))

            val response =
                graphqlClient.execute(
                    query = "/queries/GetActivationCutoff.graphql",
                    variables = contractOnboardingParentParams(123))

            val cutoff =
                LocalDate.parse(
                    response.jsonAt("/data/_entities/0/activationCutoff").replace("\"", ""))

            assertAll({ assertThat(cutoff).isEqualTo(LocalDate.of(2023, 5, 6)) })
        }
    }

    private fun mockDate(dateString: String) {
        every { clock.today() } returns LocalDate.parse(dateString)
    }

    private fun contractOnboardingParentParams(contractOnboardingId: Long) =
        mapOf(
            "representations" to
                listOf(
                    mapOf("__typename" to "ContractOnboarding", "id" to "$contractOnboardingId")),
        )

    private fun somePersonDto(id: Long): Person =
        Person(
            id = id,
            persona = Person.Persona.MEMBER,
            firstName = faker.random.randomString(),
            lastName = faker.random.randomString(),
            userId = faker.random.randomString())
}
