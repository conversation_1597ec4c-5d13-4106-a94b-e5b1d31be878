package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.OnboardingServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.contants.OnboardingExperience
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationResults
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.CountryWorkStatus
import com.multiplier.contract.schema.contract.ContractOuterClass.CreateContractInput
import com.multiplier.country.schema.Country.GrpcCountryCode
import com.multiplier.member.schema.CountryCode
import com.multiplier.member.schema.Member.MemberNationalogy
import com.multiplier.member.schema.Member.Nationalogy
import com.multiplier.member.schema.MemberCreateInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkOnboardingDataServiceTest {

    @MockK
    private lateinit var bulkOnboardingBuilderForHRISService: BulkOnboardingBuilderForHRISService
    @MockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter

    @InjectMockKs private lateinit var bulkOnboardingDataService: BulkOnboardingDataService

    private val CONTRACT_ID_LIST = listOf(111111L, 222222L)
    private val CONTRACT_CONTEXT = ContractContext(mapOf("1" to 111111L))

    @BeforeEach
    fun setUp() {
        every {
            onboardingServiceAdapter.getAllOnboardingsByContractIdsAndStatus(
                any(), ContractOnboardingStatus.DRAFT, OnboardingExperience.COMPANY.value)
        } returns
            mapOf(
                CONTRACT_ID_LIST[0] to
                    Onboarding(
                        contractId = CONTRACT_ID_LIST[0],
                        experience = OnboardingExperience.COMPANY.value,
                        status = OnboardingStatus.DRAFT,
                    ))
    }

    @Nested
    inner class UpdateOnboardingForDraftContracts {

        @Test
        fun `should use 'created' status and 'onboarding review' step when updating draft onboarding`() {
            bulkOnboardingDataService.updateOnboardingForDraftContracts(
                CONTRACT_ID_LIST,
                CONTRACT_CONTEXT,
                buildEmployeeValidationResults(GrpcCountryCode.VNM))

            verify(exactly = 1) {
                onboardingServiceAdapter.upsertOnboardings(
                    withArg {
                        assertThat(it).hasSize(1)
                        assertThat(it.first().contractId).isEqualTo(CONTRACT_ID_LIST[0])
                        assertThat(it.first().experience)
                            .isEqualTo(OnboardingExperience.COMPANY.value)
                        assertThat(it.first().status).isEqualTo(OnboardingStatus.CREATED)
                        assertThat(it.first().currentStep)
                            .isEqualTo(OnboardingStep.ONBOARDING_REVIEW)
                    })
            }
        }

        @Test
        fun `should use 'preparing confirmation' status and 'contract preparing confirmation' step when updating draft onboarding`() {
            bulkOnboardingDataService.updateOnboardingForDraftContracts(
                CONTRACT_ID_LIST,
                CONTRACT_CONTEXT,
                buildEmployeeValidationResults(GrpcCountryCode.SGP))

            verify(exactly = 1) {
                onboardingServiceAdapter.upsertOnboardings(
                    withArg {
                        assertThat(it).hasSize(1)
                        assertThat(it.first().contractId).isEqualTo(CONTRACT_ID_LIST[0])
                        assertThat(it.first().experience)
                            .isEqualTo(OnboardingExperience.COMPANY.value)
                        assertThat(it.first().status)
                            .isEqualTo(OnboardingStatus.CONTRACT_PREPARING_CONFIRMATION)
                        assertThat(it.first().currentStep)
                            .isEqualTo(OnboardingStep.ONBOARDING_CONTRACT_PREPARING_CONFIRMATION)
                    })
            }
        }
    }

    private fun buildEmployeeValidationResults(
        contractCountry: GrpcCountryCode
    ): EmployeeValidationResults {
        return EmployeeValidationResults.EMPTY.copy(
            members =
                listOf(
                    GrpcValidationResult(
                        success = true,
                        validationId = "1",
                        input =
                            MemberCreateInput.newBuilder()
                                .setNationality(
                                    MemberNationalogy.newBuilder()
                                        .setType(Nationalogy.CITIZEN)
                                        .setCountry(CountryCode.VNM))
                                .build(),
                        errors = null)),
            contracts =
                listOf(
                    GrpcValidationResult(
                        success = true,
                        validationId = "1",
                        input =
                            CreateContractInput.newBuilder()
                                .setCountry(contractCountry)
                                .setWorkStatus(CountryWorkStatus.RESIDENT)
                                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                                .build(),
                        errors = null)))
    }
}
