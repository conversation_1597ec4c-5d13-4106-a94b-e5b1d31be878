package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.service.helpers.CompanyTestDataFactory.createCompany
import com.multiplier.contract.onboarding.service.helpers.ContractTestDataFactory.createContract
import com.multiplier.contract.onboarding.service.helpers.JpaOnboardingTestDataFactory.createJpaOnboarding
import com.multiplier.contract.onboarding.service.helpers.MemberTestDataFactory.createMember
import com.multiplier.contract.onboarding.service.helpers.OperationsUserTestDataFactory.createOperationsUser
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class USAOpsMemberSignContractReminderCtaProcessorTest {

    private val underTest = USAOpsMemberSignContractReminderCtaProcessor()

    @Test
    fun should_return_correct_notification_type() {
        val result = underTest.notificationType()
        assertThat(result).isEqualTo(NotificationType.USAOpsMemberSignContractReminderCta)
    }

    @Test
    fun should_validate_true_for_usa_employee_contract_with_signature_sent() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.EMPLOYEE,
                country = CountryCode.USA,
                onboardingStatus = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT,
                onboardingStep = OnboardingStep.ONBOARDING_SIGNING)

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    @Test
    fun should_validate_false_for_non_usa_contract() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.EMPLOYEE,
                country = CountryCode.GBR,
                onboardingStatus = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT,
                onboardingStep = OnboardingStep.ONBOARDING_SIGNING)

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_false_for_contractor_contract() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.CONTRACTOR,
                country = CountryCode.USA,
                onboardingStatus = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT,
                onboardingStep = OnboardingStep.ONBOARDING_SIGNING)

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_return_only_cutoff_param() {
        val emailTemplateData = createEmailTemplateData()
        val result = underTest.editableParams(emailTemplateData)

        assertThat(result).hasSize(1)
        assertThat(result[0]).isInstanceOf(CutoffParam::class.java)
        assertThat(result[0].key).isEqualTo(EditableParamConstants.CUTOFF_DATE_KEY)
    }

    @Test
    fun should_validate_false_when_multiFrequencySupportEnabled_is_false() {
        val emailTemplateData = createEmailTemplateData(multiFrequencySupportEnabled = false)

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_false_when_multiFrequencySupportEnabled_is_true_but_country_is_not_USA() {
        val emailTemplateData =
            createEmailTemplateData(country = CountryCode.SGP, multiFrequencySupportEnabled = true)

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_true_when_multiFrequencySupportEnabled_is_true_and_country_is_USA() {
        val emailTemplateData =
            createEmailTemplateData(country = CountryCode.USA, multiFrequencySupportEnabled = true)

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    private fun createEmailTemplateData(
        contractType: ContractOuterClass.ContractType = ContractOuterClass.ContractType.EMPLOYEE,
        country: CountryCode = CountryCode.USA,
        onboardingStatus: ContractOnboardingStatus =
            ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT,
        onboardingStep: OnboardingStep = OnboardingStep.ONBOARDING_SIGNING,
        multiFrequencySupportEnabled: Boolean = true
    ): EmailTemplateData {
        val contract = createContract(type = contractType, country = country)
        val company = createCompany()
        val member = createMember()
        val operationsUser = createOperationsUser()
        val onboardingCompany =
            createJpaOnboarding(
                contractId = contract.id,
                experience = "company",
                status = onboardingStatus,
                currentStep = onboardingStep)

        return EmailTemplateData(
            company = company,
            member = member,
            operationsUser = operationsUser,
            onboardingCompany = onboardingCompany,
            onboardingMember = null,
            contract = contract,
            submittedLegalDocument = emptySet(),
            payrollFormRequirements = emptyMap(),
            guideLink = "https://guide.link",
            preregistrationRequiredCountry = false,
            payFrequency = "MONTHLY",
            multiFrequencySupportEnabled = multiFrequencySupportEnabled)
    }
}
