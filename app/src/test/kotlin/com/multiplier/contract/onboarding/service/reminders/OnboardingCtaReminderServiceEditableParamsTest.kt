package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.contract.onboarding.domain.model.Company
import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.domain.model.Member
import com.multiplier.contract.onboarding.domain.model.OperationsUser
import com.multiplier.contract.onboarding.service.BaseReminderCtaTemplateProcessor
import com.multiplier.contract.onboarding.service.CutoffParam
import com.multiplier.contract.onboarding.service.EditableParam
import com.multiplier.contract.onboarding.service.EditableParamConstants
import com.multiplier.contract.onboarding.service.MonthYearParam
import com.multiplier.contract.onboarding.service.OnboardingReminderService
import com.multiplier.contract.onboarding.service.OpsMemberSignContractReminderCtaProcessor
import com.multiplier.contract.onboarding.service.ReminderCtaHelper
import com.multiplier.contract.onboarding.service.ReminderCtaTemplateProcessor
import com.multiplier.contract.onboarding.types.NotificationType
import io.mockk.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class OnboardingCtaReminderServiceEditableParamsTest {

    private val emailTemplateDataService = mockk<EmailTemplateDataService>()
    private val onboardingReminderService = mockk<OnboardingReminderService>()
    private val notificationService =
        mockk<com.multiplier.contract.onboarding.service.notifications.NotificationService>()
    private val pigeonServiceAdapter =
        mockk<com.multiplier.contract.onboarding.adapter.PigeonServiceAdapter>()
    private val testProcessor = TestReminderProcessor()

    private val underTest =
        OnboardingCtaReminderService(
            processors = listOf(testProcessor),
            onboardingReminderService = onboardingReminderService,
            notificationService = notificationService,
            pigeonServiceAdapter = pigeonServiceAdapter,
            emailTemplateDataService = emailTemplateDataService,
            ccEmails = "<EMAIL>")

    private val contractId = 123L
    private val notificationType = NotificationType.OpsMemberSignContractReminderCta
    private val mockEmailTemplateData = mockk<EmailTemplateData>()
    private val mockContract =
        mockk<com.multiplier.contract.schema.contract.ContractOuterClass.Contract>()

    @BeforeEach
    fun setup() {
        // Reset the test processor state
        testProcessor.editableParamsCalled = false
        testProcessor.editableParamsToReturn = emptyList()
        testProcessor.dataToReturn = emptyMap()

        every { emailTemplateDataService.fetchEmailTemplateData(contractId) } returns
            mockEmailTemplateData
        every { onboardingReminderService.createOrUpdateReminder(any(), any(), any()) } returns
            mockk()

        // Mock the contract with proper getId() method
        every { mockContract.id } returns contractId
        every { mockEmailTemplateData.contract } returns mockContract

        // Mock the sendReminderToMemberForTemplate method by mocking its dependencies
        every { notificationService.send(any()) } just Runs
        every { pigeonServiceAdapter.getEmailTemplateDetails(any(), any()) } returns
            mockk { every { subject } returns "Test Subject" }
        every { mockEmailTemplateData.member } returns
            mockk { every { email } returns "<EMAIL>" }
        every { mockEmailTemplateData.operationsUser } returns
            mockk { every { email } returns "<EMAIL>" }

        // Default mocking for new feature flag fields
        every { mockEmailTemplateData.multiFrequencySupportEnabled } returns true
        every { mockEmailTemplateData.preregistrationRequiredCountry } returns false
        every { mockEmailTemplateData.payFrequency } returns "MONTHLY"
    }

    @Test
    fun `sendReminderForTemplate should validate and save editable params successfully`() {
        // Given
        val editableParams = listOf(CutoffParam("payrollCutoffDate"))
        val editableTemplateParams = mapOf("payrollCutoffDate" to "15th January 2030")
        val originalEmailData = mapOf("existingKey" to "existingValue")

        testProcessor.editableParamsToReturn = editableParams
        testProcessor.dataToReturn = originalEmailData

        // When
        underTest.sendReminderForTemplate(contractId, notificationType, editableTemplateParams)

        // Then - verify that editable params were used
        assertThat(testProcessor.editableParamsCalled).isTrue()

        // Verify that reminder was saved with editable template params
        verify {
            onboardingReminderService.createOrUpdateReminder(
                mockContract, notificationType, editableTemplateParams)
        }

        // Verify that notification was sent (indicating the flow completed successfully)
        verify { notificationService.send(any()) }
    }

    @Test
    fun `sendReminderForTemplate should override fetchData with editable params`() {
        // Given
        val editableParams = listOf(CutoffParam("payrollCutoffDate"))
        val editableTemplateParams = mapOf("payrollCutoffDate" to "15th January 2030")

        // Original data from fetchData contains the same key with different value
        val originalEmailData =
            mapOf("payrollCutoffDate" to "old cutoff date", "otherKey" to "otherValue")

        testProcessor.editableParamsToReturn = editableParams
        testProcessor.dataToReturn = originalEmailData

        // When
        underTest.sendReminderForTemplate(contractId, notificationType, editableTemplateParams)

        // Then - verify that editable params were used for validation
        assertThat(testProcessor.editableParamsCalled).isTrue()

        // Verify that reminder was saved with editable template params
        verify {
            onboardingReminderService.createOrUpdateReminder(
                mockContract, notificationType, editableTemplateParams)
        }

        // Verify that notification was sent with merged data (editable params override fetchData)
        verify {
            notificationService.send(
                match<com.multiplier.contract.onboarding.service.reminders.Notification> {
                    notification ->
                    // The notification data should contain the overridden value from editable
                    // params
                    notification.data["payrollCutoffDate"] ==
                        "15th January 2030" && // Overridden value
                    notification.data["otherKey"] == "otherValue" && // Original value preserved
                        notification.to == "<EMAIL>" &&
                        notification.from == "<EMAIL>" &&
                        notification.type == notificationType
                })
        }
    }

    @Test
    fun `sendReminderForTemplate should work without editable params`() {
        // Given
        val originalEmailData = mapOf("existingKey" to "existingValue")

        testProcessor.editableParamsToReturn = emptyList()
        testProcessor.dataToReturn = originalEmailData

        // When
        underTest.sendReminderForTemplate(contractId, notificationType, null)

        // Then - verify that editable params validation was skipped
        assertThat(testProcessor.editableParamsCalled).isFalse()

        // Verify that reminder was saved without editable template params
        verify {
            onboardingReminderService.createOrUpdateReminder(mockContract, notificationType, null)
        }

        // Verify that notification was sent
        verify { notificationService.send(any()) }
    }

    @Test
    fun `sendReminderForTemplate should fail validation for missing required parameter`() {
        // Given
        val editableParams = listOf(CutoffParam("payrollCutoffDate")) // required = true by default
        val editableTemplateParams = emptyMap<String, String>() // Missing required parameter

        testProcessor.editableParamsToReturn = editableParams

        // When & Then
        val exception =
            assertThrows<MplBusinessException> {
                underTest.sendReminderForTemplate(
                    contractId, notificationType, editableTemplateParams)
            }

        assertThat(exception.message).contains("Required parameter 'payrollCutoffDate' is missing")

        // Verify that notification was not sent and reminder was not saved
        verify(exactly = 0) { notificationService.send(any()) }
        verify(exactly = 0) {
            onboardingReminderService.createOrUpdateReminder(any(), any(), any())
        }
    }

    @Test
    fun `sendReminderForTemplate should fail validation for invalid parameter value`() {
        // Given
        val editableParams = listOf(CutoffParam("payrollCutoffDate"))
        val editableTemplateParams = mapOf("payrollCutoffDate" to "invalid date format")

        testProcessor.editableParamsToReturn = editableParams

        // When & Then
        val exception =
            assertThrows<MplBusinessException> {
                underTest.sendReminderForTemplate(
                    contractId, notificationType, editableTemplateParams)
            }

        assertThat(exception.message)
            .contains("Invalid value 'invalid date format' for parameter 'payrollCutoffDate'")

        // Verify that notification was not sent and reminder was not saved
        verify(exactly = 0) { notificationService.send(any()) }
        verify(exactly = 0) {
            onboardingReminderService.createOrUpdateReminder(any(), any(), any())
        }
    }

    @Test
    fun `sendReminderForTemplate should fail validation for unexpected parameter`() {
        // Given
        val editableParams = listOf(CutoffParam("payrollCutoffDate"))
        val editableTemplateParams =
            mapOf("payrollCutoffDate" to "15th January 2030", "unexpectedParam" to "value")

        testProcessor.editableParamsToReturn = editableParams

        // When & Then
        val exception =
            assertThrows<MplBusinessException> {
                underTest.sendReminderForTemplate(
                    contractId, notificationType, editableTemplateParams)
            }

        assertThat(exception.message).contains("Invalid editable parameter 'unexpectedParam'")

        // Verify that notification was not sent and reminder was not saved
        verify(exactly = 0) { notificationService.send(any()) }
        verify(exactly = 0) {
            onboardingReminderService.createOrUpdateReminder(any(), any(), any())
        }
    }

    @Test
    fun `sendReminderForTemplate should fail validation for past date in CutoffParam`() {
        // Given
        val editableParams = listOf(CutoffParam("payrollCutoffDate"))
        val editableTemplateParams = mapOf("payrollCutoffDate" to "15th January 2020") // Past date

        testProcessor.editableParamsToReturn = editableParams

        // When & Then
        val exception =
            assertThrows<MplBusinessException> {
                underTest.sendReminderForTemplate(
                    contractId, notificationType, editableTemplateParams)
            }

        assertThat(exception.message)
            .contains("Invalid value '15th January 2020' for parameter 'payrollCutoffDate'")

        // Verify that notification was not sent and reminder was not saved
        verify(exactly = 0) { notificationService.send(any()) }
        verify(exactly = 0) {
            onboardingReminderService.createOrUpdateReminder(any(), any(), any())
        }
    }

    // ========================================
    // MONTH YEAR PARAM TESTS
    // ========================================

    @Test
    fun `sendReminderForTemplate should validate and save MonthYearParam successfully`() {
        // Given
        val editableParams = listOf(MonthYearParam("payrollMonth"))
        val editableTemplateParams = mapOf("payrollMonth" to "January 2025")
        val originalEmailData = mapOf("existingKey" to "existingValue")

        testProcessor.editableParamsToReturn = editableParams
        testProcessor.dataToReturn = originalEmailData

        // When
        underTest.sendReminderForTemplate(contractId, notificationType, editableTemplateParams)

        // Then - verify that editable params were used
        assertThat(testProcessor.editableParamsCalled).isTrue()

        // Verify that reminder was saved with editable template params
        verify {
            onboardingReminderService.createOrUpdateReminder(
                mockContract, notificationType, editableTemplateParams)
        }

        // Verify that notification was sent (indicating the flow completed successfully)
        verify { notificationService.send(any()) }
    }

    @Test
    fun `sendReminderForTemplate should fail validation for invalid month name`() {
        // Given
        val editableParams = listOf(MonthYearParam("payrollMonth"))
        val editableTemplateParams = mapOf("payrollMonth" to "InvalidMonth 2025")

        testProcessor.editableParamsToReturn = editableParams

        // When & Then
        val exception =
            assertThrows<MplBusinessException> {
                underTest.sendReminderForTemplate(
                    contractId, notificationType, editableTemplateParams)
            }

        assertThat(exception.message)
            .contains("Invalid value 'InvalidMonth 2025' for parameter 'payrollMonth'")

        // Verify that notification was not sent and reminder was not saved
        verify(exactly = 0) { notificationService.send(any()) }
        verify(exactly = 0) {
            onboardingReminderService.createOrUpdateReminder(any(), any(), any())
        }
    }

    @Test
    fun `sendReminderForTemplate should handle multiple editable params including MonthYearParam`() {
        // Given
        val editableParams =
            listOf(CutoffParam("payrollCutoffDate"), MonthYearParam("payrollMonth"))
        val editableTemplateParams =
            mapOf("payrollCutoffDate" to "15th January 2030", "payrollMonth" to "February 2025")
        val originalEmailData = mapOf("existingKey" to "existingValue")

        testProcessor.editableParamsToReturn = editableParams
        testProcessor.dataToReturn = originalEmailData

        // When
        underTest.sendReminderForTemplate(contractId, notificationType, editableTemplateParams)

        // Then - verify that editable params were used
        assertThat(testProcessor.editableParamsCalled).isTrue()

        // Verify that reminder was saved with both editable template params
        verify {
            onboardingReminderService.createOrUpdateReminder(
                mockContract, notificationType, editableTemplateParams)
        }

        // Verify that notification was sent (indicating the flow completed successfully)
        verify { notificationService.send(any()) }
    }

    @Test
    fun `sendReminderForTemplate should validate case-insensitive month and year combinations`() {
        // Given
        val editableParams = listOf(MonthYearParam("payrollMonth"))
        val originalEmailData = mapOf("existingKey" to "existingValue")

        testProcessor.editableParamsToReturn = editableParams
        testProcessor.dataToReturn = originalEmailData

        // Test different case variations of month and year combinations
        val testCases = listOf("january 2025", "FEBRUARY 2025", "March 2025", "APRIL 2025")

        testCases.forEach { monthYearValue ->
            val editableTemplateParams = mapOf("payrollMonth" to monthYearValue)

            // When
            underTest.sendReminderForTemplate(contractId, notificationType, editableTemplateParams)

            // Then - should succeed for all valid month and year combinations regardless of case
            verify(atLeast = 1) { notificationService.send(any()) }
        }
    }

    // ========================================
    // TRANSFORM EDITABLE PARAMS TESTS
    // ========================================

    @Test
    fun `sendReminderForTemplate should transform MonthYearParam values and save transformed values to DB`() {
        // Given
        val editableParams = listOf(MonthYearParam("payrollMonth"))
        val editableTemplateParams = mapOf("payrollMonth" to "january 2025") // lowercase input
        val originalEmailData = mapOf("existingKey" to "existingValue")

        testProcessor.editableParamsToReturn = editableParams
        testProcessor.dataToReturn = originalEmailData

        // When
        underTest.sendReminderForTemplate(contractId, notificationType, editableTemplateParams)

        // Then - verify that reminder was saved with TRANSFORMED editable template params
        val expectedTransformedParams =
            mapOf("payrollMonth" to "January 2025") // transformed to proper case
        verify {
            onboardingReminderService.createOrUpdateReminder(
                mockContract, notificationType, expectedTransformedParams)
        }

        // Verify that notification was sent (indicating the flow completed successfully)
        verify { notificationService.send(any()) }
    }

    @Test
    fun `sendReminderForTemplate should use default transform for CutoffParam and save unchanged values`() {
        // Given
        val editableParams = listOf(CutoffParam())
        val editableTemplateParams =
            mapOf(EditableParamConstants.CUTOFF_DATE_KEY to "15th January 2030")
        val originalEmailData = mapOf("existingKey" to "existingValue")

        testProcessor.editableParamsToReturn = editableParams
        testProcessor.dataToReturn = originalEmailData

        // When
        underTest.sendReminderForTemplate(contractId, notificationType, editableTemplateParams)

        // Then - should succeed with default transformation (no change) and save the same values
        verify { notificationService.send(any()) }
        verify {
            onboardingReminderService.createOrUpdateReminder(
                mockContract,
                notificationType,
                editableTemplateParams) // Same values since no transformation
        }
    }

    @Test
    fun `sendReminderForTemplate should handle mixed param types with different transforms and save transformed values`() {
        // Given
        val editableParams = listOf(CutoffParam(), MonthYearParam("payrollMonth"))
        val editableTemplateParams =
            mapOf(
                EditableParamConstants.CUTOFF_DATE_KEY to
                    "15th January 2030", // Should remain unchanged
                "payrollMonth" to "february 2025" // Should be transformed to "February 2025"
                )
        val originalEmailData = mapOf("existingKey" to "existingValue")

        testProcessor.editableParamsToReturn = editableParams
        testProcessor.dataToReturn = originalEmailData

        // When
        underTest.sendReminderForTemplate(contractId, notificationType, editableTemplateParams)

        // Then - should succeed and save transformed values
        val expectedTransformedParams =
            mapOf(
                EditableParamConstants.CUTOFF_DATE_KEY to
                    "15th January 2030", // Unchanged (default transform)
                "payrollMonth" to "February 2025" // Transformed to proper case
                )
        verify { notificationService.send(any()) }
        verify {
            onboardingReminderService.createOrUpdateReminder(
                mockContract, notificationType, expectedTransformedParams)
        }
    }

    @Test
    fun `sendReminderForTemplate should save null when no editable params provided`() {
        // Given
        val editableParams = listOf(MonthYearParam("payrollMonth"))
        val originalEmailData = mapOf("existingKey" to "existingValue")

        testProcessor.editableParamsToReturn = editableParams
        testProcessor.dataToReturn = originalEmailData

        // When - no editable template params provided
        underTest.sendReminderForTemplate(contractId, notificationType, null)

        // Then - should save null (no params to transform)
        verify { notificationService.send(any()) }
        verify {
            onboardingReminderService.createOrUpdateReminder(mockContract, notificationType, null)
        }
    }

    // Test processor implementation
    private class TestReminderProcessor : ReminderCtaTemplateProcessor {
        var editableParamsToReturn: List<EditableParam> = emptyList()
        var dataToReturn: Map<String, String> = emptyMap()
        var editableParamsCalled: Boolean = false

        override fun notificationType(): NotificationType =
            NotificationType.OpsMemberSignContractReminderCta
        override fun validate(preFetchedData: EmailTemplateData): Boolean = true

        override fun fetchDataAndOverrideEditableParams(
            preFetchedData: EmailTemplateData,
            editableParams: Map<String, String>?
        ): Map<String, String> {
            // Simulate the base class behavior: merge editable params and add keys
            val result = dataToReturn.toMutableMap()
            if (editableParams != null) {
                result += editableParams
                // Add editable parameter keys for frontend identification
                editableParams.keys.forEach { paramName -> result["${paramName}Key"] = paramName }
            }
            return result
        }

        override fun editableParams(preFetchedData: EmailTemplateData): List<EditableParam> {
            editableParamsCalled = true
            return editableParamsToReturn
        }
    }

    // Test processor that extends BaseReminderCtaTemplateProcessor for testing the new behavior
    private class TestBaseReminderProcessor : BaseReminderCtaTemplateProcessor() {
        override fun notificationType(): NotificationType =
            NotificationType.OpsMemberSignContractReminderCta
        override fun validate(preFetchedData: EmailTemplateData): Boolean = true

        override fun editableParams(preFetchedData: EmailTemplateData): List<EditableParam> {
            // Return both types of editable params to test key generation
            return listOf(CutoffParam(), MonthYearParam())
        }
    }

    // ========================================
    // PAY FREQUENCY CONDITIONAL TESTS
    // ========================================

    @Test
    fun `editableParams should include MonthParam when all conditions are met`() {
        // Given - processor with all conditions met for both parameters
        val processor = OpsMemberSignContractReminderCtaProcessor()

        // Mock data with all conditions met
        every { mockEmailTemplateData.multiFrequencySupportEnabled } returns true
        every { mockEmailTemplateData.preregistrationRequiredCountry } returns false
        every { mockEmailTemplateData.payFrequency } returns "MONTHLY"

        // When - get editable params
        val editableParams = processor.editableParams(mockEmailTemplateData)

        // Then - should include both CutoffParam and MonthYearParam
        assertThat(editableParams.size).isEqualTo(2)
        assertThat(editableParams.map { it.key })
            .containsExactly(
                EditableParamConstants.CUTOFF_DATE_KEY,
                EditableParamConstants.NEXT_PAYROLL_MONTH_YEAR_KEY)
    }

    @Test
    fun `editableParams should exclude MonthYearParam when payFrequency is SEMIMONTHLY`() {
        // Given - processor with SEMIMONTHLY frequency
        val processor = OpsMemberSignContractReminderCtaProcessor()

        // Mock data with SEMIMONTHLY frequency
        every { mockEmailTemplateData.multiFrequencySupportEnabled } returns true
        every { mockEmailTemplateData.preregistrationRequiredCountry } returns false
        every { mockEmailTemplateData.payFrequency } returns "SEMIMONTHLY"

        // When - get editable params
        val editableParams = processor.editableParams(mockEmailTemplateData)

        // Then - should only include CutoffParam
        assertThat(editableParams.size).isEqualTo(1)
        assertThat(editableParams.map { it.key })
            .containsExactly(EditableParamConstants.CUTOFF_DATE_KEY)
    }

    @Test
    fun `editableParams should return empty list when multiFrequencySupportEnabled is false`() {
        // Given - processor with multiFrequencySupportEnabled disabled
        val processor = OpsMemberSignContractReminderCtaProcessor()

        // Mock data with multiFrequencySupportEnabled = false
        every { mockEmailTemplateData.multiFrequencySupportEnabled } returns false
        every { mockEmailTemplateData.preregistrationRequiredCountry } returns false
        every { mockEmailTemplateData.payFrequency } returns "MONTHLY"

        // When - get editable params
        val editableParams = processor.editableParams(mockEmailTemplateData)

        // Then - should return empty list
        assertThat(editableParams).isEmpty()
    }

    @Test
    fun `editableParams should exclude MonthYearParam when preregistrationRequiredCountry is true`() {
        // Given - processor with preregistrationRequiredCountry enabled
        val processor = OpsMemberSignContractReminderCtaProcessor()

        // Mock data with preregistrationRequiredCountry = true
        every { mockEmailTemplateData.multiFrequencySupportEnabled } returns true
        every { mockEmailTemplateData.preregistrationRequiredCountry } returns true
        every { mockEmailTemplateData.payFrequency } returns "MONTHLY"

        // When - get editable params
        val editableParams = processor.editableParams(mockEmailTemplateData)

        // Then - should only include CutoffParam
        assertThat(editableParams.size).isEqualTo(1)
        assertThat(editableParams.map { it.key })
            .containsExactly(EditableParamConstants.CUTOFF_DATE_KEY)
    }

    // ========================================
    // EDITABLE PARAMS KEYS TESTS
    // ========================================

    @Test
    fun `fetchData should include editable parameter keys for frontend identification`() {
        // Given - test processor that simulates BaseReminderCtaTemplateProcessor behavior
        testProcessor.dataToReturn = mapOf("baseField" to "baseValue")

        // When - fetch data with editable parameters
        val editableParams =
            mapOf(
                EditableParamConstants.CUTOFF_DATE_KEY to "15th January 2030",
                EditableParamConstants.NEXT_PAYROLL_MONTH_YEAR_KEY to "February 2025")
        val result =
            testProcessor.fetchDataAndOverrideEditableParams(mockEmailTemplateData, editableParams)

        // Then - should include both the editable parameters and their keys
        assertThat(result["baseField"]).isEqualTo("baseValue")
        assertThat(result[EditableParamConstants.CUTOFF_DATE_KEY]).isEqualTo("15th January 2030")
        assertThat(result[EditableParamConstants.NEXT_PAYROLL_MONTH_YEAR_KEY])
            .isEqualTo("February 2025")
        assertThat(result["${EditableParamConstants.CUTOFF_DATE_KEY}Key"])
            .isEqualTo(EditableParamConstants.CUTOFF_DATE_KEY)
        assertThat(result["${EditableParamConstants.NEXT_PAYROLL_MONTH_YEAR_KEY}Key"])
            .isEqualTo(EditableParamConstants.NEXT_PAYROLL_MONTH_YEAR_KEY)
    }

    @Test
    fun `fetchData should not include editable parameter keys when no editable params provided`() {
        // Given - test processor that simulates BaseReminderCtaTemplateProcessor behavior
        testProcessor.dataToReturn = mapOf("baseField" to "baseValue")

        // When - fetch data without editable parameters
        val result = testProcessor.fetchDataAndOverrideEditableParams(mockEmailTemplateData, null)

        // Then - should not include any key identifiers
        assertThat(result["baseField"]).isEqualTo("baseValue")
        assertThat(result.keys.filter { it.endsWith("Key") }).isEmpty()
    }

    @Test
    fun `BaseReminderCtaTemplateProcessor should include editable parameter keys when processor has editable params but no saved params provided`() {
        // Given - a real BaseReminderCtaTemplateProcessor with editable params
        val processor = TestBaseReminderProcessor()

        // Setup additional mocks required for baseFields() method
        val mockMember = mockk<Member>()
        val mockCompany = mockk<Company>()
        val mockOpsUser = mockk<OperationsUser>()

        every { mockMember.fullName } returns "Test Member"
        every { mockCompany.displayName } returns "Test Company"
        every { mockOpsUser.firstName } returns "Ops"
        every { mockOpsUser.lastName } returns "User"

        every { mockEmailTemplateData.member } returns mockMember
        every { mockEmailTemplateData.company } returns mockCompany
        every { mockEmailTemplateData.operationsUser } returns mockOpsUser
        every { mockEmailTemplateData.guideLink } returns "https://guide.link"

        // When - fetch data without providing saved editable parameters
        val result = processor.fetchDataAndOverrideEditableParams(mockEmailTemplateData, null)

        // Then - should include editable parameter keys for frontend identification
        assertThat(result["${EditableParamConstants.CUTOFF_DATE_KEY}Key"])
            .isEqualTo(EditableParamConstants.CUTOFF_DATE_KEY)
        assertThat(result["${EditableParamConstants.NEXT_PAYROLL_MONTH_YEAR_KEY}Key"])
            .isEqualTo(EditableParamConstants.NEXT_PAYROLL_MONTH_YEAR_KEY)

        // Should also include base fields
        assertThat(result[ReminderCtaHelper.MEMBER_NAME]).isEqualTo("Test Member")
        assertThat(result[ReminderCtaHelper.CLIENT_NAME]).isEqualTo("Test Company")
        assertThat(result[ReminderCtaHelper.ONBOARDING_SPECIALIST]).isEqualTo("Ops User")
    }

    // ========================================
    // MULTI FREQUENCY SUPPORT FEATURE FLAG TESTS
    // ========================================

    @Test
    fun `addAdditionalFields should include multiFrequencySupportEnabled when feature flag is enabled`() {
        // Given - EmailTemplateData with multiFrequencySupportEnabled = true
        every { mockEmailTemplateData.multiFrequencySupportEnabled } returns true
        every { mockEmailTemplateData.preregistrationRequiredCountry } returns false
        every { mockEmailTemplateData.payFrequency } returns "MONTHLY"

        val data = mutableMapOf<String, String>()

        // When - add additional fields
        ReminderCtaHelper.addMultiFrequencyFields(data, mockEmailTemplateData)

        // Then - should include multiFrequencySupportEnabled as "true"
        assertThat(data["multiFrequencySupportEnabled"]).isEqualTo("true")
        assertThat(data["isPreRegistrationCountry"]).isEqualTo("false")
        assertThat(data["payFrequency"]).isEqualTo("MONTHLY")
    }

    @Test
    fun `addAdditionalFields should include multiFrequencySupportEnabled when feature flag is disabled`() {
        // Given - EmailTemplateData with multiFrequencySupportEnabled = false
        every { mockEmailTemplateData.multiFrequencySupportEnabled } returns false
        every { mockEmailTemplateData.preregistrationRequiredCountry } returns true
        every { mockEmailTemplateData.payFrequency } returns "SEMIMONTHLY"

        val data = mutableMapOf<String, String>()

        // When - add additional fields
        ReminderCtaHelper.addMultiFrequencyFields(data, mockEmailTemplateData)

        // Then - should include multiFrequencySupportEnabled as "false"
        assertThat(data["multiFrequencySupportEnabled"]).isEqualTo("false")
        assertThat(data["isPreRegistrationCountry"]).isEqualTo("true")
        assertThat(data["payFrequency"]).isEqualTo("SEMIMONTHLY")
    }

    // ========================================
    // VALIDATION LOGIC CLARITY TESTS
    // ========================================

    @Test
    fun `validation logic should be clear for required and optional parameters`() {
        // Test the improved validation logic clarity
        val cutoffParam = CutoffParam()
        val monthYearParam = MonthYearParam()

        // Required parameter with null value should fail
        assertThat(cutoffParam.validate(null)).isFalse()
        assertThat(cutoffParam.validate("")).isFalse()
        assertThat(cutoffParam.validate("   ")).isFalse()

        // Required parameter with valid value should pass
        assertThat(cutoffParam.validate("15th January 2030")).isTrue()

        // Optional parameter (if we had one) with null value should pass
        // This demonstrates the logic: if (required) false else true

        // Present value should always be validated regardless of required flag
        assertThat(cutoffParam.validate("invalid-date")).isFalse()
        assertThat(monthYearParam.validate("InvalidMonth 2025")).isFalse()
        assertThat(monthYearParam.validate("January 2025")).isTrue()
    }
}
