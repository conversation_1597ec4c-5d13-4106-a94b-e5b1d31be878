package com.multiplier.contract.onboarding.graphql

import com.google.common.io.Files
import com.multiplier.contract.onboarding.testing.HttpClient
import com.multiplier.contract.onboarding.testing.HttpConfig
import com.multiplier.contract.onboarding.testing.jsonAt
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingJob
import com.multiplier.contract.onboarding.types.BulkOnboardingJobStatus
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.netflix.graphql.dgs.client.GraphQLResponse
import java.io.File
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("context-test")
@Disabled("test running on github can't connect to staging servers")
class BulkOnboardingDataMutatorStagingTest(
    @LocalServerPort private val port: Int,
) {

    private val client = HttpClient(port)

    @Test
    fun `validate GP bulk onboarding template`() {
        validate(
            BulkOnboardingOptions(
                CountryCode.CAN,
                ContractType.HR_MEMBER,
                508726,
                10258606,
                BulkOnboardingContext.GLOBAL_PAYROLL),
            "/Users/<USER>/workspaces/contract-onboarding-service/bulk-onboarding-testsuite/templates/global_payroll/can-validation-ok.xlsx")
    }

    @Test
    fun `validate EOR bulk onboarding template`() {
        validate(
            BulkOnboardingOptions(
                CountryCode.CAN, ContractType.EMPLOYEE, 508726, 0, BulkOnboardingContext.EOR),
            "/Users/<USER>/workspaces/contract-onboarding-service/bulk-onboarding-testsuite/templates/eor/can-validation-ok.xlsx")
    }

    fun validate(options: BulkOnboardingOptions, filePath: String) {
        val response =
            client.graphQLFileUpload<String>(
                variables = mapOf("options" to options),
                query = "/queries/BulkOnboardingValidate.graphql",
                file = File(filePath),
                httpConfig = HttpConfig(permissions = listOf("*.operations.**")),
                fileParamName = "employeeDataFile")

        println(response.body)
        val blob =
            GraphQLResponse(response.body!!)
                .jsonAt("/data/bulkOnboardingValidate/validationResultFile/blob")
                .removeSurrounding("\"")

        if (blob.isNotBlank()) {
            val tempFile = File.createTempFile("test", ".xlsx")
            Files.write(Base64.getDecoder().decode(blob), tempFile)
            openPath(tempFile.absolutePath)
            fail { "Validation failed" }
        }
        val error = GraphQLResponse(response.body!!).jsonAt("/errors")
        if (error.isNotBlank()) {
            fail { "Validation failed" }
        }
    }

    @Test
    fun `trigger GP bulk onboarding`() {
        onboard(
            BulkOnboardingOptions(
                CountryCode.SGP,
                ContractType.HR_MEMBER,
                508726,
                10258349,
                BulkOnboardingContext.GLOBAL_PAYROLL),
            "/Users/<USER>/workspaces/contract-onboarding-service/bulk-onboarding-testsuite/templates/global_payroll/sgp-validation-ok.xlsx")
    }

    @Test
    fun `trigger HRIS bulk onboarding`() {
        onboard(
            BulkOnboardingOptions(
                CountryCode.SGP,
                ContractType.HR_MEMBER,
                508666,
                2224429,
                BulkOnboardingContext.HRIS_PROFILE_DATA),
            "/Users/<USER>/workspaces/contract-onboarding-service/bulk-onboarding-testsuite/templates/hris/HRIS-onboard-30.xlsx")
    }

    @Test
    fun `trigger EOR bulk onboarding`() {
        onboard(
            BulkOnboardingOptions(
                CountryCode.CAN, ContractType.EMPLOYEE, 508726, 0, BulkOnboardingContext.EOR),
            "/Users/<USER>/workspaces/contract-onboarding-service/bulk-onboarding-testsuite/templates/eor/can-validation-ok.xlsx")
    }

    @Test
    fun `trigger AOR bulk onboarding`() {
        onboard(
            BulkOnboardingOptions(
                CountryCode.PHL, ContractType.CONTRACTOR, 508665, 0, BulkOnboardingContext.AOR),
            "/Users/<USER>/temp/test-bulk-template-aor-phl2.xlsx")
    }

    fun onboard(options: BulkOnboardingOptions, filePath: String) {
        val response =
            client.graphQLFileUpload<String>(
                variables = mapOf("options" to options),
                query = "/queries/BulkOnboardingTrigger.graphql",
                file = File(filePath),
                httpConfig = HttpConfig(userId = 538351, permissions = listOf("*.operations.**")),
                fileParamName = "employeeDataFile")
        val result =
            GraphQLResponse(response.body!!)
                .extractValueAsObject("data.bulkOnboardingTrigger", BulkOnboardingJob::class.java)
        println(result)
        val blob =
            GraphQLResponse(response.body!!)
                .jsonAt("/data/bulkOnboardingTrigger/validationResult/validationResultFile/blob")
                .removeSurrounding("\"")
        if (blob.isNotBlank()) {
            val tempFile = File.createTempFile("test", ".xlsx")
            Files.write(Base64.getDecoder().decode(blob), tempFile)
            openPath(tempFile.absolutePath)
        }
        assertThat(result.status).isEqualTo(BulkOnboardingJobStatus.SUCCESS)
    }
}
