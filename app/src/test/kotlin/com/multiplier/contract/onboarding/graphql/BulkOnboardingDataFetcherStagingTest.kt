package com.multiplier.contract.onboarding.graphql

import com.google.common.io.Files
import com.multiplier.contract.onboarding.testing.GraphQLTestClient
import com.multiplier.contract.onboarding.testing.HttpConfig
import com.multiplier.contract.onboarding.testing.jsonAt
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import java.io.File
import java.io.IOException
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("context-test")
@Disabled("test running on github can't connect to staging servers")
class BulkOnboardingDataFetcherStagingTest(
    @LocalServerPort private val port: Int,
) {

    private val graphqlClient = GraphQLTestClient(port)
    private val downloader = BulkOnboardingTemplateDownloader(graphqlClient)

    @Test
    fun `download GP bulk onboarding template`() {
        downloader.downloadBulkOnboardingTemplate(
            BulkOnboardingContext.GLOBAL_PAYROLL, 508726, 10258349, CountryCode.SGP)
    }

    @Test
    fun `download HRIS bulk onboarding template`() {
        downloader.downloadBulkOnboardingTemplate(
            BulkOnboardingContext.HRIS_PROFILE_DATA, 508726, 10258349, CountryCode.SGP)
    }

    @Test
    fun `download EOR bulk onboarding template`() {
        downloader.downloadBulkOnboardingTemplate(
            BulkOnboardingContext.EOR, 508726, 10258349, CountryCode.MEX)
    }

    @Test
    fun `download AOR bulk onboarding template`() {
        downloader.downloadBulkOnboardingTemplate(
            BulkOnboardingContext.AOR, 508726, 10258349, CountryCode.IND)
    }
}

class BulkOnboardingTemplateDownloader(private val graphqlClient: GraphQLTestClient) {
    fun downloadBulkOnboardingTemplate(
        onboardingContext: BulkOnboardingContext,
        companyId: Long,
        legalEntityId: Long,
        countryCode: CountryCode
    ) {
        val contractType =
            when (onboardingContext) {
                BulkOnboardingContext.EOR -> ContractType.EMPLOYEE
                BulkOnboardingContext.AOR -> ContractType.CONTRACTOR
                else -> ContractType.HR_MEMBER
            }
        val response =
            graphqlClient.execute(
                query = "/queries/DownloadBulkOnboardingTemplate.graphql",
                variables =
                    mapOf(
                        "options" to
                            BulkOnboardingOptions(
                                countryCode,
                                contractType,
                                companyId,
                                legalEntityId,
                                onboardingContext)),
                HttpConfig(permissions = listOf("*.operations.**")))

        val name =
            response.jsonAt("/data/bulkOnboardingTemplateDownload/name").removeSurrounding("\"")
        val blob =
            response.jsonAt("/data/bulkOnboardingTemplateDownload/blob").removeSurrounding("\"")
        assertThat(blob).isNotEmpty

        val tempFile = File.createTempFile(name, ".xlsx")
        Files.write(Base64.getDecoder().decode(blob), tempFile)
        openPath(tempFile.absolutePath)
    }
}

fun openPath(path: String) {
    try {
        val processBuilder = ProcessBuilder("open", path)
        processBuilder.inheritIO()
        processBuilder.start()
    } catch (e: IOException) {
        println("Failed to execute command: ${e.message}")
    } catch (e: InterruptedException) {
        println("Command execution interrupted: ${e.message}")
    }
}
