package com.multiplier.contract.onboarding.utils

import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.country.schema.Country.GrpcCountryCode
import io.mockk.mockkStatic
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.testcontainers.shaded.org.apache.commons.lang3.EnumUtils

class EnumMapperTest {

    enum class GrpcTestEnum {
        VALUE_ONE,
        VALUE_TWO,
        UNRECOGNIZED
    }

    enum class TestEnum {
        VALUE_ONE,
        VALUE_TWO
    }

    @Test
    fun `fromGrpcEnum should return corresponding TEnum value`() {
        val result = EnumMapper.fromGrpcEnum<GrpcTestEnum, TestEnum>(GrpcTestEnum.VALUE_ONE)
        assertEquals(TestEnum.VALUE_ONE, result)
    }

    @Test
    fun `fromGrpcEnum should return null for nullValues`() {
        val result =
            EnumMapper.fromGrpcEnum<GrpcTestEnum, TestEnum>(
                GrpcTestEnum.UNRECOGNIZED, GrpcTestEnum.UNRECOGNIZED)
        assertNull(result)
    }

    @Test
    fun `fromGrpcEnum should return null if source enum name is not in target enum`() {
        val result = EnumMapper.fromGrpcEnum<GrpcTestEnum, TestEnum>(GrpcTestEnum.VALUE_TWO)
        assertEquals(TestEnum.VALUE_TWO, result) // Should be mapped correctly

        // Simulating an invalid enum case
        mockkStatic(EnumUtils::class)
        io.mockk.every { EnumUtils.isValidEnum(TestEnum::class.java, "INVALID_VALUE") } returns
            false

        val invalidResult =
            EnumMapper.fromGrpcEnum<GrpcTestEnum, TestEnum>(GrpcTestEnum.UNRECOGNIZED)
        assertNull(invalidResult)
    }

    @Test
    fun `mapFromGrpcCountryCode should return correct CountryCode`() {
        val grpcCountryCode = GrpcCountryCode.USA
        val expected = CountryCode.USA

        val result = EnumMapper.mapFromGrpcCountryCode(grpcCountryCode)
        assertEquals(expected, result)
    }

    @Test
    fun `mapFromGrpcCountryCode should return null for UNRECOGNIZED value`() {
        val result = EnumMapper.mapFromGrpcCountryCode(GrpcCountryCode.UNRECOGNIZED)
        assertNull(result)
    }
}
