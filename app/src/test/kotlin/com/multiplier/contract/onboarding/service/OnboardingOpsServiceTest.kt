package com.multiplier.contract.onboarding.service

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.contants.ContractRevokedExperience
import com.multiplier.contract.onboarding.repository.JpaOnboardingRepository
import com.multiplier.contract.onboarding.repository.model.JpaOnboarding
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.ContractOnboardingStep
import com.multiplier.contract.onboarding.types.ContractOnboardingUpdateStepInput
import com.multiplier.contract.onboarding.types.TaskResponse
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
internal class OnboardingOpsServiceTest {
    @MockK lateinit var jpaOnboardingRepository: JpaOnboardingRepository

    @InjectMockKs lateinit var onboardingOpsService: OnboardingOpsService

    private val companyExperience = "company"
    @Test
    fun `should throw entity not found exception when onboarding record not found`() {
        // given
        val updateStepInput: ContractOnboardingUpdateStepInput =
            ContractOnboardingUpdateStepInput.newBuilder()
                .contractId(1L)
                .revokedBy("NONE")
                .experience(companyExperience)
                .build()

        every {
            jpaOnboardingRepository.findByContractIdInAndExperience(setOf(1L), companyExperience)
        } returns emptyList()

        // then
        val result = onboardingOpsService.updateContractOnboardingStep(updateStepInput)
        assertFalse(result.success)
        assertEquals(
            "Cannot find any onboarding entity with contractId=1 and experience=company",
            result.message)
        verify(exactly = 0) { jpaOnboardingRepository.save(any()) }
    }

    @Test
    fun `should throw exception when revoked by is incorrect`() {
        // given
        val updateStepInput: ContractOnboardingUpdateStepInput =
            ContractOnboardingUpdateStepInput.newBuilder()
                .contractId(1L)
                .experience(companyExperience)
                .revokedBy("Something")
                .build()

        // when
        val exception =
            assertThrows(MplBusinessException::class.java) {
                onboardingOpsService.updateContractOnboardingStep(updateStepInput)
            }

        // then
        assertEquals(
            "Invalid revokedBy. It should be one of [COMPANY, OPERATIONS, NONE]. Provided revokedBy = Something",
            exception.message)
        verify(exactly = 0) { jpaOnboardingRepository.save(any()) }
    }

    @Test
    fun `should throw exception when experience by is incorrect`() {
        // given
        val updateStepInput: ContractOnboardingUpdateStepInput =
            ContractOnboardingUpdateStepInput.newBuilder()
                .contractId(1L)
                .experience("some_experience")
                .build()

        // when
        val exception =
            assertThrows(MplBusinessException::class.java) {
                onboardingOpsService.updateContractOnboardingStep(updateStepInput)
            }

        // then
        assertEquals(
            "Invalid experience. It should be either 'member' or 'company'. Provided experience = some_experience",
            exception.message)
        verify(exactly = 0) { jpaOnboardingRepository.save(any()) }
    }

    @Test
    fun `should update onboarding record successfully`() {
        // given
        val updateStepInput: ContractOnboardingUpdateStepInput =
            ContractOnboardingUpdateStepInput.newBuilder()
                .contractId(1L)
                .experience(companyExperience)
                .currentStep(ContractOnboardingStep.START)
                .status(ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT)
                .revokedBy("NONE")
                .build()

        val existingOnboarding =
            JpaOnboarding(
                id = 2L,
                contractId = 1L,
                experience = companyExperience,
                currentStep = OnboardingStep.ONBOARDED,
                status = ContractOnboardingStatus.CONTRACT_UPDATING,
                revokedBy = ContractRevokedExperience.COMPANY)

        every {
            jpaOnboardingRepository.findByContractIdInAndExperience(setOf(1L), companyExperience)
        } returns listOf(existingOnboarding)
        every { jpaOnboardingRepository.save(any()) } returns existingOnboarding

        // when
        val response: TaskResponse =
            onboardingOpsService.updateContractOnboardingStep(updateStepInput)

        // then
        val captor = slot<JpaOnboarding>()
        verify { jpaOnboardingRepository.save(capture(captor)) }

        val capturedOnboarding = captor.captured
        assertEquals(companyExperience, capturedOnboarding.experience)
        assertEquals(1L, capturedOnboarding.contractId)
        assertEquals(OnboardingStep.START, capturedOnboarding.currentStep)
        assertEquals(ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT, capturedOnboarding.status)
        assertEquals(ContractRevokedExperience.NONE, capturedOnboarding.revokedBy)

        assertTrue(response.success)
        assertEquals(
            "Success with notes: [{CurrentStep was updated to START}, {Status was updated to SIGNATURE_EMPLOYEE_SENT}, {RevokedBy was updated to NONE}]",
            response.message)
    }

    @Test
    fun `should update only currentStep successfully`() {
        // given
        val updateStepInput: ContractOnboardingUpdateStepInput =
            ContractOnboardingUpdateStepInput.newBuilder()
                .contractId(1L)
                .experience(companyExperience)
                .currentStep(ContractOnboardingStep.DEFINITION_BANK_DETAILS)
                .build()

        val existingOnboarding =
            JpaOnboarding(
                id = 2L,
                contractId = 1L,
                experience = companyExperience,
                currentStep = OnboardingStep.ONBOARDED,
                status = ContractOnboardingStatus.CONTRACT_UPDATING,
                revokedBy = ContractRevokedExperience.COMPANY)

        every {
            jpaOnboardingRepository.findByContractIdInAndExperience(setOf(1L), companyExperience)
        } returns listOf(existingOnboarding)
        every { jpaOnboardingRepository.save(any()) } returns existingOnboarding

        // when
        val response: TaskResponse =
            onboardingOpsService.updateContractOnboardingStep(updateStepInput)

        // then
        val captor = slot<JpaOnboarding>()
        verify { jpaOnboardingRepository.save(capture(captor)) }

        val capturedOnboarding = captor.captured
        assertEquals(companyExperience, capturedOnboarding.experience)
        assertEquals(1L, capturedOnboarding.contractId)
        assertEquals(OnboardingStep.DEFINITION_BANK_DETAILS, capturedOnboarding.currentStep)
        assertEquals(ContractOnboardingStatus.CONTRACT_UPDATING, capturedOnboarding.status)
        assertEquals(ContractRevokedExperience.COMPANY, capturedOnboarding.revokedBy)

        assertTrue(response.success)
        assertEquals(
            "Success with notes: [{CurrentStep was updated to DEFINITION_BANK_DETAILS}]",
            response.message)
    }
}
