package com.multiplier.contract.onboarding.adapter

import com.multiplier.core.schema.common.Common
import com.multiplier.core.schema.multiplier.OperationsUserOuterClass.GetOperationsUsersByUserIdsRequest
import com.multiplier.core.schema.multiplier.OperationsUserOuterClass.OperationsUser
import com.multiplier.core.schema.multiplier.OperationsUserOuterClass.OperationsUsers
import com.multiplier.core.schema.multiplier.OperationsUserServiceGrpc
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class OperationsUserServiceAdapterTest {

    @MockK
    private lateinit var blockingStub: OperationsUserServiceGrpc.OperationsUserServiceBlockingStub

    @InjectMockKs private lateinit var underTest: OperationsUserServiceAdapter

    @Test
    fun getOperationsUsersByUserId() {
        every { blockingStub.getOperationsUsersByUserIds(any()) } answers
            {
                OperationsUsers.newBuilder()
                    .addAllUsers(
                        firstArg<GetOperationsUsersByUserIdsRequest>().userIdsList.map {
                            OperationsUser.newBuilder()
                                .setId(it)
                                .addEmails(
                                    Common.EmailAddress.newBuilder()
                                        .setEmail("<EMAIL>")
                                        .setType("PRIMARY")
                                        .build())
                                .build()
                        })
                    .build()
            }

        val userId = 101L

        val result = underTest.getOperationsUsersByUserId(userId)!!

        assertAll({
            assert(result.id == 101L)
            assert(result.email == "<EMAIL>")
        })
    }
}
