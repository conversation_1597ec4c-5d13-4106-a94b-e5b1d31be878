package com.multiplier.contract.onboarding.service

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class EditableParamTest {

    @Test
    fun `EditableParam should handle common required check`() {
        // Create a simple test implementation
        val testParam =
            object : EditableParam {
                override val key = "testKey"
                override val required = false
                override fun validateValue(value: String) = value == "valid"
            }

        // Non-required param should allow null/empty
        assertThat(testParam.validate(null)).isTrue()
        assertThat(testParam.validate("")).isTrue()
        assertThat(testParam.validate("   ")).isTrue()

        // But should still validate non-empty values
        assertThat(testParam.validate("valid")).isTrue()
        assertThat(testParam.validate("invalid")).isFalse()
    }

    @Test
    fun `EditableParam default transform should return value unchanged`() {
        // Test with a simple implementation
        val testParam =
            object : EditableParam {
                override val key: String = "test"
                override fun validateValue(value: String): Boolean = value == "valid"
            }

        assertThat(testParam.transform("valid")).isEqualTo("valid")
        assertThat(testParam.transform("any value")).isEqualTo("any value")
        assertThat(testParam.transform("")).isEqualTo("")
    }

    // ========================================
    // CUTOFF PARAM TESTS
    // ========================================

    @Test
    fun `CutoffParam should validate only specific date format and future dates`() {
        val cutoffParam = CutoffParam()

        // Valid format and future dates (using a date far in the future)
        assertThat(cutoffParam.validate("15th January 2030")).isTrue()
        assertThat(cutoffParam.validate("1st February 2030")).isTrue()
        assertThat(cutoffParam.validate("22nd March 2030")).isTrue()
        assertThat(cutoffParam.validate("3rd April 2030")).isTrue()

        // Invalid formats (not supported)
        assertThat(cutoffParam.validate("15 January 2030")).isFalse()
        assertThat(cutoffParam.validate("01 February 2030")).isFalse()
        assertThat(cutoffParam.validate("15 Jan 2030")).isFalse()
        assertThat(cutoffParam.validate("01 Feb 2030")).isFalse()
        assertThat(cutoffParam.validate("2030-01-15")).isFalse()
        assertThat(cutoffParam.validate("15/01/2030")).isFalse()
        assertThat(cutoffParam.validate("01/15/2030")).isFalse()

        // Invalid formats and other invalid cases
        assertThat(cutoffParam.validate("invalid date")).isFalse()
        assertThat(cutoffParam.validate("32nd January 2030")).isFalse()
        assertThat(cutoffParam.validate("")).isFalse()
        assertThat(cutoffParam.validate(null)).isFalse()
        assertThat(cutoffParam.validate("January 15")).isFalse()

        // Past dates should be invalid
        assertThat(cutoffParam.validate("15th January 2020")).isFalse()
        assertThat(cutoffParam.validate("1st January 2023")).isFalse()
    }

    @Test
    fun `CutoffParam should parse valid dates correctly`() {
        val cutoffParam = CutoffParam()

        assertThat(cutoffParam.parseDate("15th January 2030")).isEqualTo(LocalDate.of(2030, 1, 15))
        assertThat(cutoffParam.parseDate("1st February 2030")).isEqualTo(LocalDate.of(2030, 2, 1))
        assertThat(cutoffParam.parseDate("22nd March 2030")).isEqualTo(LocalDate.of(2030, 3, 22))
        assertThat(cutoffParam.parseDate("3rd April 2030")).isEqualTo(LocalDate.of(2030, 4, 3))

        // Invalid formats should return null
        assertThat(cutoffParam.parseDate("2030-03-22")).isNull()
        assertThat(cutoffParam.parseDate("15/04/2030")).isNull()
        assertThat(cutoffParam.parseDate("invalid")).isNull()
    }

    @Test
    fun `CutoffParam should have correct default properties`() {
        val cutoffParam = CutoffParam()

        assertThat(cutoffParam.key).isEqualTo(EditableParamConstants.CUTOFF_DATE_KEY)
        assertThat(cutoffParam.required).isTrue()
    }

    @Test
    fun `CutoffParam should allow custom key`() {
        val cutoffParam = CutoffParam("customCutoffDate")

        assertThat(cutoffParam.key).isEqualTo("customCutoffDate")
        assertThat(cutoffParam.validate("15th January 2030")).isTrue()
    }

    @Test
    fun `CutoffParam should handle required validation correctly`() {
        val cutoffParam = CutoffParam()

        // Required param with null/empty value should be invalid
        assertThat(cutoffParam.required).isTrue()
        assertThat(cutoffParam.validate(null)).isFalse()
        assertThat(cutoffParam.validate("")).isFalse()
        assertThat(cutoffParam.validate("   ")).isFalse()
    }

    @Test
    fun `CutoffParam should use default transform behavior`() {
        val cutoffParam = CutoffParam()

        // Default transform should return the value unchanged
        assertThat(cutoffParam.transform("15th January 2025")).isEqualTo("15th January 2025")
        assertThat(cutoffParam.transform("31st December 2024")).isEqualTo("31st December 2024")
        assertThat(cutoffParam.transform("1st March 2025")).isEqualTo("1st March 2025")
    }

    // ========================================
    // MONTH YEAR PARAM TESTS
    // ========================================

    @Test
    fun `MonthYearParam should validate only valid month and year combinations`() {
        val monthYearParam = MonthYearParam()

        // Valid month and year combinations (case-insensitive)
        assertThat(monthYearParam.validate("January 2025")).isTrue()
        assertThat(monthYearParam.validate("february 2025")).isTrue()
        assertThat(monthYearParam.validate("MARCH 2025")).isTrue()
        assertThat(monthYearParam.validate("april 2025")).isTrue()
        assertThat(monthYearParam.validate("May 2025")).isTrue()
        assertThat(monthYearParam.validate("june 2025")).isTrue()
        assertThat(monthYearParam.validate("July 2025")).isTrue()
        assertThat(monthYearParam.validate("AUGUST 2025")).isTrue()
        assertThat(monthYearParam.validate("september 2025")).isTrue()
        assertThat(monthYearParam.validate("October 2025")).isTrue()
        assertThat(monthYearParam.validate("november 2025")).isTrue()
        assertThat(monthYearParam.validate("DECEMBER 2025")).isTrue()

        // Valid years
        assertThat(monthYearParam.validate("January 2020")).isTrue()
        assertThat(monthYearParam.validate("January 2050")).isTrue()

        // Invalid formats
        assertThat(monthYearParam.validate("January")).isFalse() // Missing year
        assertThat(monthYearParam.validate("2025")).isFalse() // Missing month
        assertThat(monthYearParam.validate("invalid 2025")).isFalse() // Invalid month
        assertThat(monthYearParam.validate("January 19")).isFalse() // Invalid year (too short)
        assertThat(monthYearParam.validate("January 2019")).isFalse() // Year too early
        assertThat(monthYearParam.validate("January 2051")).isFalse() // Year too late
        assertThat(monthYearParam.validate("jan 2025")).isFalse() // Abbreviated month
        assertThat(monthYearParam.validate("feb 2025")).isFalse() // Abbreviated month
        assertThat(monthYearParam.validate("")).isFalse() // Empty
        assertThat(monthYearParam.validate("January2025")).isFalse() // No space
        assertThat(monthYearParam.validate("January  2025")).isFalse() // Multiple spaces
    }

    @Test
    fun `MonthYearParam should handle whitespace correctly`() {
        val monthYearParam = MonthYearParam()

        // Valid with whitespace around the entire string
        assertThat(monthYearParam.validate("  January 2025  ")).isTrue()
        assertThat(monthYearParam.validate("\tfebruary 2025\t")).isTrue()
        assertThat(monthYearParam.validate(" MARCH 2025 ")).isTrue()

        // Invalid - only whitespace
        assertThat(monthYearParam.validate("   ")).isFalse()
        assertThat(monthYearParam.validate("\t\t")).isFalse()

        // Invalid - extra whitespace between month and year
        assertThat(monthYearParam.validate("January  2025")).isFalse()
        assertThat(monthYearParam.validate("January\t2025")).isFalse()
    }

    @Test
    fun `MonthYearParam should have correct default properties`() {
        val monthYearParam = MonthYearParam()

        assertThat(monthYearParam.key).isEqualTo(EditableParamConstants.NEXT_PAYROLL_MONTH_YEAR_KEY)
        assertThat(monthYearParam.required).isTrue()
    }

    @Test
    fun `MonthYearParam should allow custom key`() {
        val monthYearParam = MonthYearParam("customMonth")

        assertThat(monthYearParam.key).isEqualTo("customMonth")
        assertThat(monthYearParam.validate("January 2025")).isTrue()
    }

    @Test
    fun `MonthYearParam should handle required validation correctly`() {
        val monthYearParam = MonthYearParam()

        // Required param with null/empty value should be invalid
        assertThat(monthYearParam.required).isTrue()
        assertThat(monthYearParam.validate(null)).isFalse()
        assertThat(monthYearParam.validate("")).isFalse()
        assertThat(monthYearParam.validate("   ")).isFalse()
    }

    @Test
    fun `MonthYearParam should transform month and year to proper case`() {
        val monthYearParam = MonthYearParam()

        assertThat(monthYearParam.transform("january 2025")).isEqualTo("January 2025")
        assertThat(monthYearParam.transform("FEBRUARY 2025")).isEqualTo("February 2025")
        assertThat(monthYearParam.transform("March 2025")).isEqualTo("March 2025")
        assertThat(monthYearParam.transform("  april 2025  ")).isEqualTo("April 2025")
        assertThat(monthYearParam.transform("MAY 2025")).isEqualTo("May 2025")
        assertThat(monthYearParam.transform("june 2025")).isEqualTo("June 2025")
        assertThat(monthYearParam.transform("JULY 2025")).isEqualTo("July 2025")
        assertThat(monthYearParam.transform("august 2025")).isEqualTo("August 2025")
        assertThat(monthYearParam.transform("September 2025")).isEqualTo("September 2025")
        assertThat(monthYearParam.transform("OCTOBER 2025")).isEqualTo("October 2025")
        assertThat(monthYearParam.transform("november 2025")).isEqualTo("November 2025")
        assertThat(monthYearParam.transform("DECEMBER 2025")).isEqualTo("December 2025")

        // Test different years
        assertThat(monthYearParam.transform("january 2024")).isEqualTo("January 2024")
        assertThat(monthYearParam.transform("december 2030")).isEqualTo("December 2030")

        // Invalid inputs should return original value (shouldn't happen after validation)
        assertThat(monthYearParam.transform("invalid 2025")).isEqualTo("invalid 2025")
        assertThat(monthYearParam.transform("jan 2025")).isEqualTo("jan 2025")
        assertThat(monthYearParam.transform("January")).isEqualTo("January")
    }
}
