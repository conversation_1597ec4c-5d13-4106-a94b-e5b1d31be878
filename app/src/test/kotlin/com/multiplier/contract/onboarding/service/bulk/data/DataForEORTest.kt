package com.multiplier.contract.onboarding.service.bulk.data

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.adapter.bulk.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.contants.ContractRevokedExperience
import com.multiplier.contract.onboarding.domain.contants.OnboardingExperience
import com.multiplier.contract.onboarding.domain.model.Address
import com.multiplier.contract.onboarding.domain.model.AddressDetail
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.JoiningBonusAmountSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.JoiningBonusPayOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.VariableBonusFrequencySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.VariableBonusPayOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.ProbationUnitSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.ProbationValueSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.PageRequest
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.country.schema.Country
import com.multiplier.grpc.common.toDate
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class DataForEORTest {

    @MockK private lateinit var bulkContractServiceAdapter: BulkContractServiceAdapter
    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter
    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter
    @MockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter
    @MockK private lateinit var bulkComplianceServiceAdapter: BulkComplianceServiceAdapter
    @MockK private lateinit var compensationServiceAdapter: CompensationServiceAdapter
    @MockK private lateinit var bulkCompensationServiceAdapter: BulkCompensationServiceAdapter
    @MockK private lateinit var bulkPayrollServiceAdapter: BulkPayrollServiceAdapter
    @MockK private lateinit var memberServiceAdapter: MemberServiceAdapter
    @MockK private lateinit var bulkMemberAddressServiceAdapter: BulkMemberAddressServiceAdapter

    @InjectMockKs private lateinit var bulkContractDataService: BulkContractDataService

    @InjectMockKs private lateinit var bulkComplianceDataService: BulkComplianceDataService

    @InjectMockKs private lateinit var bulkCompensationDataService: BulkCompensationDataService

    @InjectMockKs private lateinit var bulkMemberAddressDataService: BulkMemberAddressDataService

    private val COMPANY_ID = 12345L
    private val CONTRACT_ID_LIST = listOf(11111L, 22222L)

    @BeforeEach
    fun setUp() {
        every { countryServiceAdapter.getCountryStates(any()) } returns emptyMap()
        every {
            contractServiceAdapter.getContractsBy(match { it.companyIds == listOf(COMPANY_ID) })
        } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(CONTRACT_ID_LIST[0])
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setStartOn(LocalDate.now().toDate())
                    .build(),
                ContractOuterClass.Contract.newBuilder()
                    .setId(CONTRACT_ID_LIST[1])
                    .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                    .setStartOn(LocalDate.now().toDate())
                    .build())
        every {
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                CONTRACT_ID_LIST, OnboardingExperience.COMPANY.value)
        } returns
            mapOf(
                CONTRACT_ID_LIST[0] to
                    Onboarding(
                        id = 1,
                        contractId = CONTRACT_ID_LIST[0],
                        experience = "Should be returned",
                        status = OnboardingStatus.CREATED,
                        revokedBy = ContractRevokedExperience.NONE,
                        currentStep = OnboardingStep.START),
                CONTRACT_ID_LIST[1] to
                    Onboarding(
                        id = 2,
                        contractId = CONTRACT_ID_LIST[1],
                        experience = "Filtered out",
                        status = OnboardingStatus.CREATED,
                        revokedBy = ContractRevokedExperience.NONE,
                        currentStep = OnboardingStep.ONBOARDED))
        every { companyServiceAdapter.getCompanyLegalEntities(COMPANY_ID) } returns emptyList()
        every { countryServiceAdapter.getCompensationStandards(any()) } returns emptyMap()
    }

    @Test
    fun `bulk contract data with filter status`() {
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .countryCode(CountryCode.SGP)
                .companyId(COMPANY_ID)
                .build()
        val dataSpecs = bulkContractDataService.getDataSpecs(options)
        val pageRequest = PageRequest.newBuilder().pageSize(10).pageNumber(0).build()

        val expected = bulkContractDataService.getDataForSpecs(dataSpecs, options, pageRequest)
        assertThat(expected).isNotNull
        assertThat(expected.contractIds).hasSize(1)
        assertThat(expected.contractIds).contains(CONTRACT_ID_LIST[0])
    }

    @Test
    fun `eor template should include joining bonus and variable bonus`() {
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .countryCode(CountryCode.SGP)
                .companyId(COMPANY_ID)
                .build()
        val dataSpecs = bulkCompensationDataService.getDataSpecs(options)

        assertThat(dataSpecs).isNotEmpty
        assertThat(dataSpecs.firstOrNull { it.key == JoiningBonusAmountSpec.key }).isNotNull
        assertThat(dataSpecs.firstOrNull { it.key == JoiningBonusPayOnSpec.key }).isNotNull
        assertThat(dataSpecs.firstOrNull { it.key == VariableBonusFrequencySpec.key }).isNotNull
        assertThat(dataSpecs.firstOrNull { it.key == VariableBonusPayOnSpec.key }).isNotNull
    }

    @Test
    fun `eor should generate configurable compliance fields`() {
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .countryCode(CountryCode.SGP)
                .companyId(COMPANY_ID)
                .build()

        every {
            countryServiceAdapter.getComplianceDefinitionForCountry(
                CountryCode.SGP, ContractType.EMPLOYEE)
        } returns
            Country.GrpcComplianceRequirementDefinition.newBuilder()
                .addParamDefinitions(
                    Country.GrpcComplianceParamDefinition.newBuilder()
                        .setParam(
                            Country.GrpcComplianceParam.newBuilder()
                                .setComplianceParamPeriodLimit(
                                    Country.GrpcComplianceParam.GrpcComplianceParamPeriodLimit
                                        .newBuilder()
                                        .setKey("probationPolicy")
                                        .setUnit(Country.GrpcComplianceParamPeriodUnit.MONTHS)
                                        .build())
                                .build())
                        .setEnabled(true)
                        .build())
                .addParamDefinitions(
                    Country.GrpcComplianceParamDefinition.newBuilder()
                        .setParam(
                            Country.GrpcComplianceParam.newBuilder()
                                .setComplianceParamPeriodLimit(
                                    Country.GrpcComplianceParam.GrpcComplianceParamPeriodLimit
                                        .newBuilder()
                                        .setKey("nonCompete")
                                        .setUnit(Country.GrpcComplianceParamPeriodUnit.MONTHS)
                                        .build())
                                .build())
                        .setEnabled(true)
                        .build())
                .build()
        val dataSpecs = bulkComplianceDataService.getDataSpecs(options)

        assertThat(dataSpecs).isNotEmpty
        assertThat(dataSpecs).hasSize(4)
        assertThat(dataSpecs.firstOrNull { it.key == ProbationValueSpec.key }).isNotNull
        assertThat(dataSpecs.firstOrNull { it.key == ProbationUnitSpec.key }).isNotNull
    }

    @Test
    fun `should generate data with legal data address`() {
        val memberId = 11101L
        val memberIdSet = setOf(memberId)
        val dataSpecs =
            listOf(
                AddressLegalData.AddressLine1Spec,
                AddressLegalData.AddressLine2Spec,
                AddressLegalData.AddressStateSpec,
                AddressLegalData.AddressCitySpec,
                AddressLegalData.AddressProvinceSpec,
                AddressLegalData.AddressCountrySpec,
                AddressLegalData.AddressPostalCodeSpec,
                AddressLegalData.AddressZipCodeSpec,
            )

        every { memberServiceAdapter.getMemberAddressDetails(memberIdSet) } returns
            listOf(
                AddressDetail(
                    memberId = memberId,
                    currentAddress =
                        Address(
                            line1 = "Address_line1",
                            line2 = "Address_line2",
                            state = "Florida",
                            street = "Sun Avenue",
                            city = "New York",
                            province = "",
                            country = CountryCode.SGP,
                            postalCode = "100858",
                            zipCode = "111858",
                        )))

        val expected = bulkMemberAddressDataService.getDataForSpecs(dataSpecs, memberIdSet)

        assertThat(expected).isNotNull
        assertThat(expected.memberIdToMemberData).isNotEmpty
        assertThat(expected.memberIdToMemberData[memberId]).isNotNull
        assertThat(expected.memberIdToMemberData[memberId]?.data).isNotNull
        assertThat(
                expected.memberIdToMemberData[memberId]
                    ?.data
                    ?.get(AddressLegalData.AddressLine1Spec.key))
            .isEqualTo("Address_line1")
        assertThat(
                expected.memberIdToMemberData[memberId]
                    ?.data
                    ?.get(AddressLegalData.AddressLine2Spec.key))
            .isEqualTo("Address_line2")
        assertThat(
                expected.memberIdToMemberData[memberId]
                    ?.data
                    ?.get(AddressLegalData.AddressStateSpec.key))
            .isEqualTo("Florida")
        assertThat(
                expected.memberIdToMemberData[memberId]
                    ?.data
                    ?.get(AddressLegalData.AddressCitySpec.key))
            .isEqualTo("New York")
        assertThat(
                expected.memberIdToMemberData[memberId]
                    ?.data
                    ?.get(AddressLegalData.AddressPostalCodeSpec.key))
            .isEqualTo("100858")
        assertThat(
                expected.memberIdToMemberData[memberId]
                    ?.data
                    ?.get(AddressLegalData.AddressZipCodeSpec.key))
            .isEqualTo("111858")
        assertThat(
                expected.memberIdToMemberData[memberId]
                    ?.data
                    ?.get(AddressLegalData.AddressCountrySpec.key))
            .isEqualTo(CountryCode.SGP.name)
    }
}
