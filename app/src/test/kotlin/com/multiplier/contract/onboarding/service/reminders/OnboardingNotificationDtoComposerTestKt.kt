package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.company.schema.grpc.CompanyOuterClass.*
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.domain.model.OnboardingTask
import com.multiplier.contract.onboarding.domain.model.Persona
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.ContractOnboardingStep
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.schema.common.Common
import com.multiplier.member.schema.CountryCode
import io.mockk.every
import io.mockk.impl.annotations.MockK
import java.time.LocalDate
import java.time.YearMonth
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
internal class OnboardingNotificationDtoComposerTestKt {

    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter

    @MockK private lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter

    @MockK private lateinit var documentServiceAdapter: DocumentServiceAdapter

    @MockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter

    @MockK private lateinit var operationsUserServiceAdapter: OperationsUserServiceAdapter

    @MockK private lateinit var userServiceAdapter: UserServiceAdapter

    @MockK private lateinit var onboardingLinkCreator: MemberOnboardingLinkCreator

    private lateinit var underTest: OnboardingNotificationDtoComposer

    private val memberActivationKey = "member-activation-key"
    private val onboardingLink = "https://onboardinglink.com"

    private val defaultCompany =
        com.multiplier.contract.onboarding.domain.model.Company(
            id = 11,
            displayName = "Multiplier",
            logoId = 99,
            countryFullName = "Vietnam",
            countryCode = com.multiplier.contract.onboarding.types.CountryCode.VNM,
            msaSigned = false,
            isTest = false,
        )

    @BeforeEach
    fun beforeEach() {
        underTest =
            OnboardingNotificationDtoComposer(
                contractServiceAdapter = contractServiceAdapter,
                companyServiceAdapter = companyServiceAdapter,
                memberServiceAdapter = memberServiceAdapter,
                countryServiceAdapter = countryServiceAdapter,
                documentServiceAdapter = documentServiceAdapter,
                onboardingServiceAdapter = onboardingServiceAdapter,
                operationsUserServiceAdapter = operationsUserServiceAdapter,
                memberOnboardingLinkCreator = onboardingLinkCreator,
                baseUrl = "https://app.usemultiplier.com",
            )
    }

    private val payrollCycleContractsList =
        listOf(
            OnboardingReminders.ContractsForCutoffDate(
                cutoffDate = LocalDate.now(),
                contractIds = listOf(1L),
                payrollMonth = YearMonth.now(),
            ))

    fun initMocks(
        onboardingStatus: ContractOnboardingStatus = ContractOnboardingStatus.MEMBER_INVITED
    ) {
        every { contractServiceAdapter.getNonDeletedNonEndedContracts(setOf(1L)) }
            .returns(
                listOf(
                    Contract.newBuilder()
                        .setId(1L)
                        .setCompanyId(11L)
                        .setMemberId(21L)
                        .setCountry(CountryCode.VNM.name)
                        .setCreatedBy(31L)
                        .setCreatedBy(31L)
                        .build()))

        every { companyServiceAdapter.getCompanies(setOf(11L)) } returns (listOf(defaultCompany))

        every { memberServiceAdapter.getMembers(setOf(21L)) } returns
            (listOf(
                com.multiplier.contract.onboarding.domain.model.Member(
                    id = 21L,
                    fullName = "Test Member1",
                    firstName = "Test",
                    lastName = "Member1",
                    email = "<EMAIL>",
                    userId = "21",
                    persona = Persona("MEMBER"),
                )))

        every { documentServiceAdapter.getDocuments(listOf(91L)) } returns
            (listOf(DocumentResponse(id = 91L, viewUrl = "http://image.com/logo11")))

        every { documentServiceAdapter.getCompanyLogoLinks(listOf(99)) } returns
            mapOf(
                defaultCompany.logoId to
                    DocumentResponse(id = 99, viewUrl = "http://multiplierlogo.png"))

        every { companyServiceAdapter.getCompanyUsersBy(any()) }
            .returns(
                listOf(
                    createCompanyUser(
                        id = 41L,
                        userId = 31L,
                        companyId = 11L,
                        firstName = "Contract creator1",
                        email = "<EMAIL>"),
                    createCompanyUser(
                        id = 42L,
                        userId = 32L,
                        companyId = 11L,
                        firstName = "Signatory1",
                        email = "<EMAIL>",
                        capabilities = listOf(CompanyUserCapability.SIGNATORY)),
                    createCompanyUser(
                        id = 43L,
                        userId = 33L,
                        companyId = 11L,
                        firstName = "Billing contact1",
                        email = "<EMAIL>",
                        capabilities = listOf(CompanyUserCapability.BILLING_CONTACT)),
                    createCompanyUser(
                        id = 44L,
                        userId = null,
                        companyId = 11L,
                        firstName = null,
                        email = "<EMAIL>",
                        roles = listOf(CompanyUserRole.PRIMARY_ADMIN),
                    )))

        every {
                countryServiceAdapter.getCountryNamesByCodes(
                    listOf(com.multiplier.contract.onboarding.types.CountryCode.VNM))
            }
            .returns(mapOf(com.multiplier.contract.onboarding.types.CountryCode.VNM to "Vietnam"))

        every { onboardingServiceAdapter.getAllOnboardingsByContractIds(setOf(1L), "company") }
            .returns(
                mapOf(
                    1L to
                        Onboarding(
                            status = onboardingStatus.toDomain(),
                            currentStep = OnboardingStep.ONBOARDING_MEMBER,
                            experience = "company",
                            id = 1L,
                            contractId = 1L)))

        every { onboardingServiceAdapter.getAllOnboardingsByContractIds(setOf(1L), "member") }
            .returns(
                mapOf(
                    1L to
                        Onboarding(
                            status = onboardingStatus.toDomain(),
                            currentStep = OnboardingStep.MEMBER_WELCOME,
                            experience = "member",
                            id = 1L,
                            contractId = 1L)))

        every { contractServiceAdapter.getDepositPaidContractIds(setOf(1L)) } returns emptySet()

        // missing mocks
        every { onboardingServiceAdapter.getAllByIds(any()) } returns emptyList()
        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns emptyMap()
        every {
            countryServiceAdapter.getRequiredDocumentKeysGroupByCountryCode(any(), any())
        } returns emptyMap()
        every { operationsUserServiceAdapter.getSalesUsersForCompanies(any()) } returns emptyMap()
        every { operationsUserServiceAdapter.getCsmUsersForCompanies(any()) } returns emptyMap()
        every { operationsUserServiceAdapter.getOperationsUsersForContracts(any()) } returns
            emptyMap()
        every { userServiceAdapter.getUserByEmail(any()) } returns
            User(activationKey = memberActivationKey)

        every {
            onboardingLinkCreator.createOnboardingLink(any(), any(), any(), any(), any(), any())
        } returns onboardingLink
        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns emptyList()
        every { contractServiceAdapter.getContractMembersEmailByContractIds(any()) } returns
            emptyMap()
    }

    @Test
    fun `should include pending onboarding details in company reminder`() {
        // given
        initMocks()
        underTest.init(payrollCycleContractsList)

        // when
        val dto: CompanyOnboardingNotificationEmailDto =
            underTest.composeCompanyEmailDto(payrollCycleContractsList[0], 11L, LocalDate.now())!!

        val pendingTasks: List<OnboardingTask> = dto.pendingOnboardings[0].onboardingTasks

        assertThat(dto.companyUserEmails)
            .hasSameElementsAs(
                listOf(
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"))

        assertThat(dto.companyLogoLink).isEqualTo("http://multiplierlogo.png")
        assertThat(dto.companyName).isEqualTo("Multiplier")
        assertThat(dto.companyCountry).isEqualTo("Vietnam")
        assertThat(dto.onboardingCount).isEqualTo(1)
        assertThat(dto.employeeFullNames).isEqualTo(listOf("Test Member1"))
        assertThat(dto.payrollMonth).isEqualTo(YearMonth.now())
        assertThat(dto.cutoffDate).isEqualTo(LocalDate.now())
        assertThat(dto.daysToCutoff).isEqualTo(0)
        assertThat(dto.contractOnboardingsLink)
            .isEqualTo("https://app.usemultiplier.com/company/team?tab=ONBOARDING")
        assertThat(dto.pendingOnboardings.size).isEqualTo(1)
        assertThat(dto.pendingOnboardings[0].contractOnboardingLinkForCompany)
            .isEqualTo("https://app.usemultiplier.com/company/member-onboard/1/onboarding/")
        assertThat(dto.pendingOnboardings[0].employeeFullName).isEqualTo("Test Member1")
        assertThat(dto.pendingOnboardings[0].pendingStepCount).isEqualTo(4)
        assertThat(pendingTasks.size).isEqualTo(4)
        verifyCompanyTask(pendingTasks[0], OnboardingTaskName.SIGN_MSA, "Multiplier")
        verifyCompanyTask(pendingTasks[1], OnboardingTaskName.PAY_DEPOSIT, "Billing contact1")
        verifyCompanyTask(pendingTasks[2], OnboardingTaskName.PLATFORM_ONBOARDING, "Test Member1")
        verifyCompanyTask(
            pendingTasks[3], OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS, "Test Member1")
    }

    @Test
    fun `should not include contract which has no pending tasks`() {
        initMocks()

        every {
            onboardingServiceAdapter.getAllOnboardingsByContractIds(setOf(1L), "company")
        } returns
            mapOf(
                1L to
                    createOnboarding(
                        status = ContractOnboardingStatus.MEMBER_DATA_ADDED,
                        currentStep = ContractOnboardingStep.MEMBER_REVIEW,
                    ))

        every {
            onboardingServiceAdapter.getAllOnboardingsByContractIds(setOf(1L), "member")
        } returns
            mapOf(
                1L to
                    createOnboarding(
                        status = ContractOnboardingStatus.MEMBER_COMPLETED,
                        currentStep = ContractOnboardingStep.MEMBER_REVIEW,
                    ))

        every { contractServiceAdapter.getDepositPaidContractIds(setOf(1L)) } returns setOf(1L)

        every { companyServiceAdapter.getCompanies(setOf(defaultCompany.id)) } returns
            listOf(defaultCompany.copy(msaSigned = true))

        underTest.init(payrollCycleContractsList)
        val dto =
            underTest.composeCompanyEmailDto(payrollCycleContractsList[0], 11L, LocalDate.now())!!

        assertThat(dto.pendingOnboardings).isEmpty()
        assertThat(dto.onboardingCount).isZero()
        assertThat(dto.employeeFullNames).isEmpty()
    }

    private fun createOnboarding(
        id: Long = 1L,
        contractId: Long = 1L,
        experience: String = "company",
        status: ContractOnboardingStatus,
        currentStep: ContractOnboardingStep? = null
    ): Onboarding =
        Onboarding(
            id = id,
            contractId = contractId,
            experience = experience,
            status = status.toDomain(),
            currentStep = currentStep?.toDomain())

    private fun verifyCompanyTask(
        pendingTask: OnboardingTask,
        expectedName: OnboardingTaskName,
        expectedPendingOn: String
    ) {
        assertThat(pendingTask.name).isEqualTo(expectedName)
        assertThat(pendingTask.completed).isFalse()
        assertThat(pendingTask.pendingOn?.fullName).isEqualTo(expectedPendingOn)
    }

    @Test
    fun `should add payroll forms task shown to company if required but not yet completed`() {
        initMocks()

        every {
            countryServiceAdapter.getRequiredDocumentKeysGroupByCountryCode(any(), any())
        } returns
            mapOf(
                CountryAndState(com.multiplier.contract.onboarding.types.CountryCode.VNM) to
                    setOf("payrollForms"))

        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns
            mapOf(21L to setOf("payrollForms"))

        underTest.init(payrollCycleContractsList)
        val dto =
            underTest.composeCompanyEmailDto(payrollCycleContractsList[0], 11L, LocalDate.now())!!
        val pendingTasks = dto.pendingOnboardings[0].onboardingTasks

        assertThat(dto.pendingOnboardings[0].pendingStepCount).isEqualTo(5)
        assertThat(pendingTasks).hasSize(5)
        verifyCompanyTask(
            pendingTasks[4], OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS, "Test Member1")
    }

    @Test
    fun `should not add sign msa task when it is completed`() {
        initMocks()

        every { companyServiceAdapter.getCompanies(setOf(defaultCompany.id)) } returns
            listOf(defaultCompany.copy(msaSigned = true))

        underTest.init(payrollCycleContractsList)
        val dto =
            underTest.composeCompanyEmailDto(payrollCycleContractsList[0], 11L, LocalDate.now())!!
        val pendingTasks = dto.pendingOnboardings[0].onboardingTasks

        assertThat(pendingTasks).hasSize(3)
        verifyCompanyTask(pendingTasks[0], OnboardingTaskName.PAY_DEPOSIT, "Billing contact1")
    }

    @Test
    fun `should include pending onboarding details in member reminder`() {
        initMocks()
        every { contractServiceAdapter.getContractMembersEmailByContractIds(any()) } returns
            mapOf(1L to "<EMAIL>")

        underTest.init(payrollCycleContractsList)
        val dto = underTest.composeMemberEmailDto(payrollCycleContractsList[0], 1L, LocalDate.now())
        val pendingTasks = dto.onboardingTasks

        assertThat(dto.memberEmail).isEqualTo("<EMAIL>")
        assertThat(dto.companyLogoLink).isEqualTo("http://multiplierlogo.png")
        assertThat(dto.companyName).isEqualTo("Multiplier")
        assertThat(dto.companyCountry).isEqualTo("Vietnam")
        assertThat(dto.employeeFirstName).isEqualTo("Test")
        assertThat(dto.payrollMonth).isEqualTo(YearMonth.now())
        assertThat(dto.cutoffDate).isEqualTo(LocalDate.now())
        assertThat(dto.contractOnboardingLink).isEqualTo(onboardingLink)
        assertThat(dto.pendingStepCount).isEqualTo(2)
        assertThat(pendingTasks).hasSize(3)
        verifyMemberTask(pendingTasks[0], OnboardingTaskName.SIGN_CONTRACT, true)
        verifyMemberTask(pendingTasks[1], OnboardingTaskName.PLATFORM_ONBOARDING, false)
        verifyMemberTask(pendingTasks[2], OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS, false)
    }

    @Test
    fun `should add payroll forms task to member if required but not yet completed`() {
        initMocks()

        every {
            countryServiceAdapter.getRequiredDocumentKeysGroupByCountryCode(any(), any())
        } returns
            mapOf(
                CountryAndState(com.multiplier.contract.onboarding.types.CountryCode.VNM) to
                    setOf("payrollForms"))

        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns
            mapOf(21L to setOf("payrollForms"))

        underTest.init(payrollCycleContractsList)
        val dto = underTest.composeMemberEmailDto(payrollCycleContractsList[0], 1L, LocalDate.now())
        val pendingTasks = dto.onboardingTasks

        assertThat(dto.pendingStepCount).isEqualTo(3)
        assertThat(pendingTasks).hasSize(4)
        verifyMemberTask(pendingTasks[3], OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS, false)
    }

    @Nested
    inner class PostContractRevision {

        @Test
        fun `the sign contract task should be incomplete when the contract is ready to sign again`() {
            initMocks()

            mockCompanyAndMemberOnboarding(
                OnboardingStatus.SIGNATURE_EMPLOYEE_SENT, OnboardingStep.ONBOARDING_SIGNING)

            underTest.init(payrollCycleContractsList)
            val dto =
                underTest.composeMemberEmailDto(payrollCycleContractsList[0], 1L, LocalDate.now())

            verifyMemberTask(dto.onboardingTasks[0], OnboardingTaskName.SIGN_CONTRACT, false)
        }

        @Test
        fun `the sign contract task should remain complete when the contract is not ready to sign again`() {
            initMocks()

            mockCompanyAndMemberOnboarding(
                OnboardingStatus.CREATED, OnboardingStep.ONBOARDING_REVIEW)

            underTest.init(payrollCycleContractsList)
            val dto =
                underTest.composeMemberEmailDto(payrollCycleContractsList[0], 1L, LocalDate.now())

            verifyMemberTask(dto.onboardingTasks[0], OnboardingTaskName.SIGN_CONTRACT, true)
        }

        private fun mockCompanyAndMemberOnboarding(
            onboardingStatus: OnboardingStatus,
            onboardingStep: OnboardingStep
        ) {
            every { onboardingServiceAdapter.getAllOnboardingsByContractIds(setOf(1L), "company") }
                .returns(
                    mapOf(
                        1L to
                            Onboarding(
                                status = onboardingStatus,
                                currentStep = onboardingStep,
                                experience = "company",
                                id = 1L,
                                contractId = 1L)))

            every { onboardingServiceAdapter.getAllOnboardingsByContractIds(setOf(1L), "member") }
                .returns(
                    mapOf(
                        1L to
                            Onboarding(
                                status = onboardingStatus,
                                currentStep = onboardingStep,
                                experience = "member",
                                id = 1L,
                                contractId = 1L)))
        }
    }

    @ParameterizedTest
    @CsvSource(
        value =
            [
                "0, ThirdContractOnboardingReminderToCompany, URGENT",
                "1, ThirdContractOnboardingReminderToCompany, URGENT",
                "2, ThirdContractOnboardingReminderToCompany, URGENT",
                "4, SecondContractOnboardingReminderToCompany, Action Needed",
                "6, SecondContractOnboardingReminderToCompany, Action Needed",
                "8, SecondContractOnboardingReminderToCompany, Action Needed",
                "11, FirstContractOnboardingReminderToCompany, Action Pending",
                "14, FirstContractOnboardingReminderToCompany, Action Pending",
                "17, FirstContractOnboardingReminderToCompany, Action Pending",
                "20, FirstContractOnboardingReminderToCompany, Action Pending",
            ])
    fun `should compose notification type and subject to company based on days to cutoff`(
        daysToCutoff: Int,
        notificationName: String,
        subjectText: String,
    ) {
        initMocks()

        underTest.init(payrollCycleContractsList)
        val dto =
            underTest.composeCompanyEmailDto(
                payrollCycleContractsList[0],
                11L,
                LocalDate.now().minusDays(daysToCutoff.toLong()))!!

        assertThat(dto.notificationType).isEqualTo(NotificationType.valueOf(notificationName))
        assertThat(dto.subject).contains(subjectText)
    }

    @ParameterizedTest
    @CsvSource(
        value =
            [
                "0, ThirdContractOnboardingReminderToMember, URGENT",
                "1, ThirdContractOnboardingReminderToMember, URGENT",
                "2, ThirdContractOnboardingReminderToMember, URGENT",
                "4, SecondContractOnboardingReminderToMember, Action Pending",
                "6, SecondContractOnboardingReminderToMember, Action Pending",
                "8, SecondContractOnboardingReminderToMember, Action Pending",
                "11, FirstContractOnboardingReminderToMember, Complete",
                "14, FirstContractOnboardingReminderToMember, Complete",
                "17, FirstContractOnboardingReminderToMember, Complete",
                "20, FirstContractOnboardingReminderToMember, Complete",
            ])
    fun `should compose notification type and subject to member based on days to cutoff`(
        daysToCutoff: Int,
        notificationName: String,
        subjectText: String,
    ) {
        initMocks()

        underTest.init(payrollCycleContractsList)
        val dto =
            underTest.composeMemberEmailDto(
                payrollCycleContractsList[0], 1L, LocalDate.now().minusDays(daysToCutoff.toLong()))

        assertThat(dto.notificationType).isEqualTo(NotificationType.valueOf(notificationName))
        assertThat(dto.subject).contains(subjectText)
    }

    @Nested
    inner class MemberInvitedToSignContract {

        @Test
        fun `returns true when contract onboarding status is after SIGNATURE_EMPLOYEE_SENT`() {
            initMocks(ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT)

            underTest.init(payrollCycleContractsList)
            val dto =
                underTest.composeMemberEmailDto(payrollCycleContractsList[0], 1L, LocalDate.now())

            assertThat(dto.memberInvitedToSignContract).isTrue()
        }
    }

    private fun verifyMemberTask(
        actualTask: OnboardingTask,
        expectedName: OnboardingTaskName,
        expectedCompleted: Boolean
    ) {
        assertThat(actualTask.name).isEqualTo(expectedName)
        assertThat(actualTask.completed).isEqualTo(expectedCompleted)
    }

    private fun createCompanyUser(
        id: Long,
        userId: Long?,
        companyId: Long,
        firstName: String?,
        email: String,
        capabilities: List<CompanyUserCapability> = emptyList(),
        roles: List<CompanyUserRole> = emptyList()
    ) =
        CompanyUser.newBuilder()
            .setId(id)
            .setUserId(userId?.toString() ?: "11111")
            .setCompanyId(companyId)
            .setFirstName(firstName ?: "")
            .addAllCompanyUserCapability(emptyList())
            .addAllCompanyUserCapability(capabilities)
            .addAllRoles(roles)
            .addAllEmails(
                listOf(Common.EmailAddress.newBuilder().setType("primary").setEmail(email).build()))
            .build()
}
