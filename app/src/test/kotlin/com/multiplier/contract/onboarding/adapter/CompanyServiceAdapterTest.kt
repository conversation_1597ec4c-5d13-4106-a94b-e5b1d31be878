package com.multiplier.contract.onboarding.adapter

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyServiceGrpc.CompanyServiceBlockingStub
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.core.schema.common.Common
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class CompanyServiceAdapterTest {

    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter

    @MockK private lateinit var featureFlagService: FeatureFlagService

    @MockK private lateinit var blockingStub: CompanyServiceBlockingStub

    @InjectMockKs private lateinit var underTest: CompanyServiceAdapterImpl

    @Test
    fun `should use V1 endpoint when feature flag is disabled`() {
        // Given
        val userId = 123L
        val mockCompanyUser = createMockCompanyUser()

        every {
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING)
        } returns false
        every { blockingStub.getCompanyUserByUserId(any()) } returns mockCompanyUser

        // When
        val result = underTest.getCompanyUserByUserId(userId)

        // Then
        assertThat(result.id).isEqualTo(1L)
        assertThat(result.companyId).isEqualTo(100L)
        assertThat(result.firstName).isEqualTo("John")
        assertThat(result.lastName).isEqualTo("Doe")
        assertThat(result.email).isEqualTo("<EMAIL>")

        verify {
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING)
        }
        verify { blockingStub.getCompanyUserByUserId(any()) }
        verify(exactly = 0) { blockingStub.getCompanyUserByUserIdV2(any()) }
    }

    @Test
    fun `should use V2 endpoint when feature flag is enabled and success is true`() {
        // Given
        val userId = 123L
        val mockCompanyUser = createMockCompanyUser()
        val mockV2Response = createMockV2Response(success = true, companyUser = mockCompanyUser)

        every {
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING)
        } returns true
        every { blockingStub.getCompanyUserByUserIdV2(any()) } returns mockV2Response

        // When
        val result = underTest.getCompanyUserByUserId(userId)

        // Then
        assertThat(result.id).isEqualTo(1L)
        assertThat(result.companyId).isEqualTo(100L)
        assertThat(result.firstName).isEqualTo("John")
        assertThat(result.lastName).isEqualTo("Doe")
        assertThat(result.email).isEqualTo("<EMAIL>")

        verify {
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING)
        }
        verify { blockingStub.getCompanyUserByUserIdV2(any()) }
        verify(exactly = 0) { blockingStub.getCompanyUserByUserId(any()) }
    }

    @Test
    fun `should throw ERROR_FINDING_COMPANY_USER when V2 endpoint returns success false`() {
        // Given
        val userId = 123L
        val mockV2Response = createMockV2Response(success = false, companyUser = null)

        every {
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING)
        } returns true
        every { blockingStub.getCompanyUserByUserIdV2(any()) } returns mockV2Response

        // When & Then
        val exception =
            assertThrows<MplBusinessException> { underTest.getCompanyUserByUserId(userId) }

        assertThat(exception.errorCode).isEqualTo(ErrorCodes.ERROR_FINDING_COMPANY_USER)
        assertThat(exception.message).contains("Failed to get company user by user ID: $userId")
        assertThat(exception.context).containsEntry("userId", userId)

        verify {
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING)
        }
        verify { blockingStub.getCompanyUserByUserIdV2(any()) }
        verify(exactly = 0) { blockingStub.getCompanyUserByUserId(any()) }
    }

    @Test
    fun `should use correct request types for V1 and V2 endpoints`() {
        // Given
        val userId = 123L
        val mockCompanyUser = createMockCompanyUser()

        // Test V1 request type
        every {
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING)
        } returns false
        every { blockingStub.getCompanyUserByUserId(any()) } returns mockCompanyUser

        // When
        underTest.getCompanyUserByUserId(userId)

        // Then
        verify {
            blockingStub.getCompanyUserByUserId(
                match<CompanyOuterClass.GetCompanyUserByUserIdRequest> { it.userId == userId })
        }

        // Test V2 request type
        val mockV2Response = createMockV2Response(success = true, companyUser = mockCompanyUser)
        every {
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING)
        } returns true
        every { blockingStub.getCompanyUserByUserIdV2(any()) } returns mockV2Response

        // When
        underTest.getCompanyUserByUserId(userId)

        // Then
        verify {
            blockingStub.getCompanyUserByUserIdV2(
                match<CompanyOuterClass.GetCompanyUserByUserIdV2Request> { it.userId == userId })
        }
    }

    private fun createMockCompanyUser(): CompanyOuterClass.CompanyUser {
        return CompanyOuterClass.CompanyUser.newBuilder()
            .setId(1L)
            .setCompanyId(100L)
            .setFirstName("John")
            .setLastName("Doe")
            .addEmails(
                Common.EmailAddress.newBuilder()
                    .setEmail("<EMAIL>")
                    .setType("primary")
                    .build())
            .build()
    }

    private fun createMockV2Response(
        success: Boolean,
        companyUser: CompanyOuterClass.CompanyUser?
    ): CompanyOuterClass.GetCompanyUserByUserIdV2Response {
        val builder =
            CompanyOuterClass.GetCompanyUserByUserIdV2Response.newBuilder().setSuccess(success)

        if (success && companyUser != null) {
            builder.setCompanyUser(companyUser)
        }

        return builder.build()
    }
}
