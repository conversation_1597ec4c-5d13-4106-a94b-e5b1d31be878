package com.multiplier.contract.onboarding.usecase

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.contract.onboarding.adapter.CompensationSourceServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingNotificationService
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingServiceFactoryCreator
import com.multiplier.contract.onboarding.service.bulk.HrisProfileBulkOnboardingServiceFactory
import com.multiplier.contract.onboarding.service.bulk.creation.BulkCreationForHRISService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmailSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.FirstNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.GenderSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastNameSpec
import com.multiplier.contract.onboarding.service.bulk.validation.*
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.utils.getFileFromResources
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkOnboardingServiceTest {

    @MockK
    private lateinit var bulkOnboardingServiceFactoryCreatorMock:
        BulkOnboardingServiceFactoryCreator
    @MockK
    private lateinit var hrisProfileBulkOnboardingServiceFactoryMock:
        HrisProfileBulkOnboardingServiceFactory
    @MockK private lateinit var hrisBulkValidationServiceMock: BulkValidationForHRISService
    @MockK private lateinit var hrisBulkCreationServiceMock: BulkCreationForHRISService

    @MockK private lateinit var bulkValidationHelperMock: BulkValidationHelper
    @MockK private lateinit var notificationServiceMock: BulkOnboardingNotificationService

    @MockK private lateinit var bulkOnboardingServiceV2Mock: BulkOnboardingServiceV2

    @MockK private lateinit var compensationSourceServiceAdapter: CompensationSourceServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkOnboardingService

    @BeforeEach
    fun setUp() {
        every {
            bulkOnboardingServiceFactoryCreatorMock.getServiceFactory(
                BulkOnboardingContext.HRIS_PROFILE_DATA)
        } returns hrisProfileBulkOnboardingServiceFactoryMock

        every { hrisProfileBulkOnboardingServiceFactoryMock.bulkValidationService() } returns
            hrisBulkValidationServiceMock

        every { hrisProfileBulkOnboardingServiceFactoryMock.bulkCreationService() } returns
            hrisBulkCreationServiceMock
    }

    private val bulkOnboardProperties =
        mapOf(
            "employeeId" to "1",
            "contractId" to "11",
            FirstNameSpec.key to "Johnny",
            LastNameSpec.key to "Depp",
            EmailSpec.key to "<EMAIL>",
            GenderSpec.key to "MALE")

    private val inputEmployeeData =
        listOf(
            EmployeeData(
                EmployeeIdentification(
                    employeeId = "1",
                    contractId = 11,
                    firstName = "Johnny",
                    lastName = "Depp",
                    email = "<EMAIL>",
                    rowNumber = 1),
                data = bulkOnboardProperties))

    private val completeEmployeeData =
        inputEmployeeData.map {
            EmployeeData(
                it.identification,
                it.data +
                    mapOf(
                        "type" to "HR_MEMBER",
                        "country" to "USA",
                        "companyId" to "11",
                        "legalEntityId" to "111",
                        "workStatus" to "RESIDENT"))
        }

    @Nested
    inner class ParseEmployeeDataFile {

        @Test
        fun `should parse employee data file correctly for global payroll context`() {
            val employeeDataFile = getGlobalPayrollTestEmployeeDataFile()

            val employeeData = parseEmployeeDataFile(employeeDataFile).employeeData

            assertThat(employeeData).allSatisfy {
                assertThat(it.data["employeeId"]).isEqualTo("1")
                assertThat(it.data["firstName"]).isEqualTo("Robert")
                assertThat(it.data["lastName"]).isEqualTo("Oppenheimer")
                assertThat(it.data["email"]).isEqualTo("<EMAIL>")
                assertThat(it.data["salary"]).isEqualTo("10000.00")
                assertThat(it.data["gender"]).isEqualTo("OTHER")
                assertThat(it.data["workStatus"]).isEqualTo("RESIDENT")
                assertThat(it.data["term"]).isEqualTo("PERMANENT")
                assertThat(it.data["startOn"]).isEqualTo("2024-01-01")
                assertThat(it.data["position"]).isEqualTo("SWE")
            }
        }

        @Test
        fun `should parse employee data file correctly for hris profile data context`() {
            val employeeDataFile = getHrisProfileTestEmployeeDataFile()

            val employeeData = parseEmployeeDataFile(employeeDataFile).employeeData

            val firstEmployee = employeeData[0].data
            assertThat(firstEmployee["employeeId"]).isEqualTo("SGHR001")
            assertThat(firstEmployee["firstName"]).isEqualTo("FirstSGHR001")
            assertThat(firstEmployee["middleName"]).isEqualTo("MidSGHR001")
            assertThat(firstEmployee["lastName"]).isEqualTo("LastSGHR001")
            assertThat(firstEmployee["email"])
                .isEqualTo("<EMAIL>")
            assertThat(firstEmployee["gender"]).isEqualTo("MALE")
            assertThat(firstEmployee["legalEntityId"]).isEqualTo("2 - Test1")
            assertThat(firstEmployee["startOn"]).isEqualTo("2024-03-11")
            val secondEmployee = employeeData[1].data
            assertThat(secondEmployee["employeeId"]).isEqualTo("SGHR002")
            assertThat(secondEmployee["firstName"]).isEqualTo("FirstSGHR002")
            assertThat(secondEmployee["middleName"]).isEqualTo("MidSGHR002")
            assertThat(secondEmployee["lastName"]).isEqualTo("LastSGHR002")
            assertThat(secondEmployee["email"])
                .isEqualTo("<EMAIL>")
            assertThat(secondEmployee["gender"]).isEqualTo("OTHER")
            assertThat(secondEmployee["legalEntityId"]).isEqualTo("3 - Test2 - Test Name")
            assertThat(secondEmployee["startOn"]).isEqualTo("2024-03-11")
        }

        private fun getGlobalPayrollTestEmployeeDataFile(): ByteArray {
            return getFileFromResources("/bulk/global-payroll/bulk-upload-1-member.xlsx")
                .readBytes()
        }

        private fun getHrisProfileTestEmployeeDataFile(): ByteArray {
            return getFileFromResources("/bulk/hris-profile/bulk-upload-2-members.xlsx").readBytes()
        }
    }

    @Nested
    inner class ValidationFailures {

        @Test
        fun `should fail onboard when source is compensation service`() {
            val options =
                BulkOnboardingOptions.newBuilder()
                    .context(BulkOnboardingContext.EOR)
                    .contractType(ContractType.EMPLOYEE)
                    .countryCode(CountryCode.SGP)
                    .companyId(1L)
                    .build()

            every {
                compensationSourceServiceAdapter.isNewCompensationSourceEnabledForCountries(
                    any(), any())
            } returns mapOf(CountryCode.SGP to true)

            val exception =
                Assertions.assertThrows(MplBusinessException::class.java) {
                    underTest.onboard(
                        getFileFromResources("/bulk/hris-profile/bulk-upload-2-members.xlsx")
                            .inputStream(),
                        options)
                }

            Assertions.assertEquals(
                "New compensation service enabled for country SGP for type EMPLOYEE",
                exception.message)
        }

        @Test
        fun `should fail validate when source is compensation service`() {
            val options =
                BulkOnboardingOptions.newBuilder()
                    .context(BulkOnboardingContext.EOR)
                    .contractType(ContractType.EMPLOYEE)
                    .countryCode(CountryCode.SGP)
                    .companyId(1L)
                    .build()

            every {
                compensationSourceServiceAdapter.isNewCompensationSourceEnabledForCountries(
                    any(), any())
            } returns mapOf(CountryCode.SGP to true)

            val exception =
                Assertions.assertThrows(MplBusinessException::class.java) {
                    underTest.validate(
                        getFileFromResources("/bulk/hris-profile/bulk-upload-2-members.xlsx")
                            .inputStream(),
                        options,
                        "GraphQL")
                }

            Assertions.assertEquals(
                "New compensation service enabled for country SGP for type EMPLOYEE",
                exception.message)
        }

        @Test
        fun `should fail download template when source is compensation service`() {
            val options =
                BulkOnboardingOptions.newBuilder()
                    .context(BulkOnboardingContext.EOR)
                    .contractType(ContractType.EMPLOYEE)
                    .countryCode(CountryCode.SGP)
                    .companyId(1L)
                    .build()

            every {
                compensationSourceServiceAdapter.isNewCompensationSourceEnabledForCountries(
                    any(), any())
            } returns mapOf(CountryCode.SGP to true)

            val exception =
                Assertions.assertThrows(MplBusinessException::class.java) {
                    underTest.generateBulkOnboardingTemplate(
                        options, PageRequest.newBuilder().pageSize(10).pageNumber(0).build())
                }

            Assertions.assertEquals(
                "New compensation service enabled for country SGP for type EMPLOYEE",
                exception.message)
        }
    }
}
