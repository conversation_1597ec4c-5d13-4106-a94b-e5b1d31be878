package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberEmergencyDataServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.EmergencyContact
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmergencyContactNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmergencyContactPhoneNumberSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmergencyContactRelationshipSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.MemberEmergencyContact
import com.multiplier.member.schema.PhoneNumer
import com.multiplier.member.schema.UpsertEmergencyContactInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberEmergencyDataServiceTest {

    @MockK
    private lateinit var bulkMemberEmergencyDataServiceAdapterMock:
        BulkMemberEmergencyDataServiceAdapter

    @MockK private lateinit var memberServiceAdapter: MemberServiceAdapter

    @InjectMockKs
    private lateinit var bulkMemberEmergencyDataService: BulkMemberEmergencyDataService

    @Test
    fun `should return correct grpc validation result`() {
        val employeeData =
            listOf(EmployeeData(EmployeeIdentification(rowNumber = 1), data = emptyMap()))
        val options = BulkOnboardingOptions.newBuilder().entityId(1).build()
        val grpcResult =
            listOf(
                GrpcValidationResult<UpsertEmergencyContactInput>(
                    success = true,
                    errors = listOf("Some error"),
                    validationId = "1",
                    input = null,
                ))
        every {
            bulkMemberEmergencyDataServiceAdapterMock.validateUpsertEmergencyContactDataInputs(
                employeeData, options)
        } returns grpcResult

        assertEquals(grpcResult, bulkMemberEmergencyDataService.validate(employeeData, options))
    }

    @Test
    fun `should return correct errors when upsert emergency contact data`() {
        val inputs =
            listOf<ValidInput<UpsertEmergencyContactInput>>(
                ValidInput(
                    validationId = "1",
                    input =
                        UpsertEmergencyContactInput.newBuilder()
                            .setRequestId("1")
                            .setEmergencyContact(
                                MemberEmergencyContact.newBuilder()
                                    .setMemberId(1)
                                    .setName("Johnny")
                                    .setRelationship("Stranger")
                                    .setPhoneNumber(
                                        PhoneNumer.newBuilder().setPhoneNo("123123").build())
                                    .build())
                            .build()))
        val memberContext = MemberContext(mapOf("1" to 1))

        val errors = listOf("some errors")
        every {
            bulkMemberEmergencyDataServiceAdapterMock.upsertEmergencyContactData(any())
        } returns listOf(CreationResult.error(requestId = "1", errors = errors))

        assertEquals(
            errors,
            bulkMemberEmergencyDataService.upsertEmergencyContacts(
                inputs, memberContext, BulkOnboardingOptions()))
    }

    @Test
    fun `should filter out default grpc instance when upsert emergency contact data`() {
        val inputs =
            listOf<ValidInput<UpsertEmergencyContactInput>>(
                ValidInput(
                    validationId = "1", input = UpsertEmergencyContactInput.getDefaultInstance()))
        val memberContext = MemberContext(mapOf("1" to 1))

        every {
            bulkMemberEmergencyDataServiceAdapterMock.upsertEmergencyContactData(emptyList())
        } returns emptyList()

        assertEquals(
            emptyList<String>(),
            bulkMemberEmergencyDataService.upsertEmergencyContacts(
                inputs, memberContext, BulkOnboardingOptions()))
    }

    @Test
    fun `should return correct member data context when get data for specs`() {
        val emergencyContacts =
            listOf(
                EmergencyContact(1, "Johnny", "Father", "5555-555"),
                EmergencyContact(2, "Barry", "Super Hero", "7777-777"),
            )
        every { memberServiceAdapter.getMemberEmergencyContacts(any()) } returns emergencyContacts

        val expected =
            MemberDataContext(
                mapOf(
                    1L to
                        EmployeeDataChunk(
                            mapOf(
                                EmergencyContactNameSpec.key to "Johnny",
                                EmergencyContactRelationshipSpec.key to "Father",
                                EmergencyContactPhoneNumberSpec.key to "5555-555",
                            )),
                    2L to
                        EmployeeDataChunk(
                            mapOf(
                                EmergencyContactNameSpec.key to "Barry",
                                EmergencyContactRelationshipSpec.key to "Super Hero",
                                EmergencyContactPhoneNumberSpec.key to "7777-777",
                            ))))

        val dataSpecs =
            listOf(
                EmergencyContactNameSpec,
                EmergencyContactRelationshipSpec,
                EmergencyContactPhoneNumberSpec)
        assertEquals(
            expected, bulkMemberEmergencyDataService.getDataForSpecs(dataSpecs, emptySet()))
    }
}
