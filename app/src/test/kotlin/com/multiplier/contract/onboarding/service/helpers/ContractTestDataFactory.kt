package com.multiplier.contract.onboarding.service.helpers

import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.contract.ContractOuterClass

object ContractTestDataFactory {
    fun createContract(
        id: Long = 123L,
        companyId: Long = 101L,
        memberId: Long = 1L,
        country: CountryCode = CountryCode.IND,
        workStatus: ContractOuterClass.CountryWorkStatus =
            ContractOuterClass.CountryWorkStatus.RESIDENT,
        type: ContractOuterClass.ContractType = ContractOuterClass.ContractType.EMPLOYEE
    ): ContractOuterClass.Contract {
        return ContractOuterClass.Contract.newBuilder()
            .setId(id)
            .setCompanyId(companyId)
            .setMemberId(memberId)
            .setCountry(country.name)
            .setWorkStatus(workStatus)
            .setType(type)
            .build()
    }
}
