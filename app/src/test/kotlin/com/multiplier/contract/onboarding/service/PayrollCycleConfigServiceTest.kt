package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationOuterClass.PayFrequency
import com.multiplier.contract.schema.compensation.CompensationOuterClass.PaymentFrequencyDate
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import java.time.LocalDate
import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class PayrollCycleConfigServiceTest {
    @RelaxedMockK private lateinit var contractServiceAdapter: ContractServiceAdapter
    @RelaxedMockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter
    @RelaxedMockK private lateinit var compensationServiceAdapter: CompensationServiceAdapter
    @RelaxedMockK private lateinit var onboardingAudServiceAdapter: OnboardingAudServiceAdapter
    @RelaxedMockK private lateinit var companyServiceAdapter: CompanyServiceAdapter
    @InjectMockKs private lateinit var underTest: PayrollCycleConfigService

    @Nested
    inner class GetPayrollCycleConfig {
        @Test
        fun returns_correct_payroll_cycle_config() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-03-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY,
                paymentFrequencyDates = listOf(10, 25))
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-04-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))
            mockOnboarding(
                onboardingId = 103L,
                contractId = 203L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))

            runMocks()

            val result =
                underTest.getPayrollCycleConfigByOnboardingId(
                    setOf(101L, 102L, 103L), LocalDate.parse("2023-05-01"))

            assertAll({
                assertEquals(LocalDate.of(2023, 3, 15), result[101L]?.cutoffDate)
                assertEquals(LocalDate.of(2023, 5, 15), result[102L]?.cutoffDate)
                assertEquals(LocalDate.of(2023, 4, 15), result[103L]?.cutoffDate)
            })
        }

        @Test
        fun for_December_returns_correct_payroll_cycle_config() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-10-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY,
                paymentFrequencyDates = listOf(10, 25))
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-11-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))
            mockOnboarding(
                onboardingId = 103L,
                contractId = 203L,
                startOn = "2023-10-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))

            runMocks()

            val result =
                underTest.getPayrollCycleConfigByOnboardingId(
                    setOf(101L, 102L, 103L), LocalDate.parse("2023-12-01"))

            assertAll({
                // semi-monthly payroll stay the same
                assertEquals(LocalDate.of(2023, 10, 15), result[101L]?.cutoffDate)
                // other months monthly payroll stay the same
                assertEquals(LocalDate.of(2023, 11, 15), result[103L]?.cutoffDate)
                // December monthly payroll cutoff day changed to 5
                assertEquals(LocalDate.of(2023, 12, 5), result[102L]?.cutoffDate)
            })
        }

        @Test
        fun add_1_cycle_for_contract_that_start_after_cutoff_date() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-04-20",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-03-17",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(10, 25))

            runMocks()

            val result =
                underTest.getPayrollCycleConfigByOnboardingId(
                    setOf(101L, 102L), LocalDate.parse("2023-05-01"))

            assertAll({
                assertEquals(LocalDate.of(2023, 5, 15), result[101L]?.cutoffDate)
                assertEquals(LocalDate.of(2023, 4, 15), result[102L]?.cutoffDate)
            })
        }

        @Test
        fun returns_correctly_for_contract_of_previous_month_which_have_paid_deposit() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-04-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY,
                paymentFrequencyDates = listOf(10, 25))

            mockDepositPaid(setOf(201L, 202L))

            runMocks()

            val result =
                underTest.getPayrollCycleConfigByOnboardingId(
                    setOf(101L, 102L), LocalDate.parse("2023-05-01"))

            assertAll({
                assertEquals(LocalDate.of(2023, 5, 15), result[101L]?.cutoffDate)
                assertEquals(LocalDate.of(2023, 5, 15), result[102L]?.cutoffDate)
            })
        }

        @Test
        fun returns_correctly_for_contract_of_previous_month_which_have_signed_msa_and_employee_signed() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                companyId = 301L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                companyId = 302L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))
            mockOnboarding(
                onboardingId = 103L,
                contractId = 203L,
                companyId = 302L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))
            mockOnboarding(
                onboardingId = 104L,
                contractId = 204L,
                companyId = 303L,
                startOn = "2023-04-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY,
                paymentFrequencyDates = listOf(10, 25))
            mockOnboarding(
                onboardingId = 105L,
                contractId = 205L,
                companyId = 302L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25))

            mockEmployeeSigned(setOf(203L, 204L, 205L))
            mockMsaSigned(setOf(302L, 303L))

            runMocks()

            val result =
                underTest.getPayrollCycleConfigByOnboardingId(
                    setOf(101L, 102L, 103L, 104L, 105L), LocalDate.parse("2023-05-01"))

            assertAll({ assertEquals(LocalDate.of(2023, 4, 15), result[101L]?.cutoffDate) })
            assertAll({ assertEquals(LocalDate.of(2023, 4, 15), result[102L]?.cutoffDate) })
            assertAll({ assertEquals(LocalDate.of(2023, 5, 15), result[103L]?.cutoffDate) })
            assertAll({ assertEquals(LocalDate.of(2023, 5, 15), result[104L]?.cutoffDate) })
            assertAll({ assertEquals(LocalDate.of(2023, 5, 15), result[105L]?.cutoffDate) })
        }

        @Test
        fun does_not_returns_cutoff_date_for_freelancer() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-03-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY,
                paymentFrequencyDates = listOf(10, 25),
                contractType = ContractType.EMPLOYEE)
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-04-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25),
                contractType = ContractType.HR_MEMBER)
            mockOnboarding(
                onboardingId = 103L,
                contractId = 203L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                paymentFrequencyDates = listOf(25),
                contractType = ContractType.FREELANCER)

            runMocks()

            val result =
                underTest.getPayrollCycleConfigByOnboardingId(
                    setOf(101L, 102L, 103L), LocalDate.parse("2023-05-01"))

            assertAll({
                assertEquals(2, result.size)
                assertEquals(null, result[103L]?.cutoffDate)
                assertEquals(LocalDate.of(2023, 3, 15), result[101L]?.cutoffDate)
                assertEquals(LocalDate.of(2023, 5, 15), result[102L]?.cutoffDate)
            })
        }
    }

    private var onboardings = mutableListOf<Onboarding>()
    private var contracts = mutableListOf<Contract>()
    private var compensationMap = mutableMapOf<Long, CompensationOuterClass.Compensation>()

    private fun runMocks() {
        val onboardingIds = onboardings.mapNotNull { it.id }.toSet()
        val contractIds = contracts.map { it.id }.toSet()
        every { onboardingServiceAdapter.getAllByIds(onboardingIds) } returns onboardings

        every { contractServiceAdapter.getNonDeletedNonEndedContracts(contractIds) } returns
            contracts

        every { compensationServiceAdapter.getCurrentCompensationByContractIds(any()) } returns
            compensationMap
    }

    private fun mockDepositPaid(contractIds: Set<Long>) {
        every { contractServiceAdapter.getDepositPaidContractIds(any()) } returns contractIds
    }

    private fun mockEmployeeSigned(contractIds: Set<Long>) {
        every {
            onboardingAudServiceAdapter.getAllContractIdsOfOnboardingAuditsWithExperienceAndStatus(
                any(), eq("company"), eq(ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SIGNED))
        } returns contractIds
    }

    private fun mockMsaSigned(companyIds: Set<Long>) {
        every { companyServiceAdapter.getMSASignedCompanyIds(any()) } returns companyIds
    }

    private fun mockOnboarding(
        onboardingId: Long,
        contractId: Long,
        companyId: Long = 999L,
        startOn: String,
        payFrequency: PayFrequency,
        paymentFrequencyDates: List<Int>,
        contractType: ContractType = ContractType.EMPLOYEE
    ) {
        onboardings.add(
            Onboarding(
                id = onboardingId,
                contractId = contractId,
                experience = "company",
                status = OnboardingStatus.SIGNATURE_EMPLOYEE_SENT))

        contracts.add(
            Contract.newBuilder()
                .setId(contractId)
                .setStartOn(LocalDate.parse(startOn).toGrpcDate())
                .setCountry("IND")
                .setCompanyId(companyId)
                .setType(contractType)
                .build())

        if (contractType != ContractType.FREELANCER) {
            compensationMap[contractId] =
                CompensationOuterClass.Compensation.newBuilder()
                    .setPostProbationBasePay(
                        CompensationOuterClass.CompensationPayComponent.newBuilder()
                            .setPayFrequency(payFrequency)
                            .addAllPaymentFrequencyDates(
                                paymentFrequencyDates.map {
                                    PaymentFrequencyDate.newBuilder().setDateOfMonth(it).build()
                                }))
                    .setPayType(CompensationOuterClass.PayType.FIXED)
                    .build()
        }
    }
}
