package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.company.schema.grpc.CompanyOfferingOuterClass.CompanyOffering
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.CompensationSourceServiceAdapter
import com.multiplier.contract.onboarding.adapter.DepartmentServiceAdapter
import com.multiplier.contract.onboarding.domain.model.Department
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.COMPENSATION_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.EMPLOYMENT_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.module.*
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.onboarding.usecase.ModuleParams
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

@ExtendWith(MockKExtension::class)
class GlobalPayrollBulkOnboardingUseCaseV2Test {

    @MockK private lateinit var bulkContractModule: BulkContractModule
    @MockK private lateinit var bulkMemberModule: BulkMemberModule
    @MockK private lateinit var bulkContractOnboardingModule: BulkContractOnboardingModule
    @MockK private lateinit var bulkCompensationModule: BulkCompensationModuleV2
    @MockK private lateinit var bulkMemberLegalDataModule: BulkMemberLegalDataModule
    @MockK private lateinit var bulkMemberBankDataModule: BulkMemberBankDataModule
    @MockK private lateinit var bulkOrgManagementModule: BulkOrgManagementModule
    @MockK private lateinit var bulkTimeOffModule: BulkTimeOffModule
    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter
    @MockK private lateinit var departmentServiceAdapter: DepartmentServiceAdapter
    @MockK private lateinit var compensationSourceServiceAdapter: CompensationSourceServiceAdapter
    @MockK private lateinit var bulkContractActivationModule: BulkContractActivationModule
    @InjectMockKs private lateinit var underTest: GlobalPayrollBulkOnboardingUseCaseV2

    private val companyId = 1L
    private val entityId = 2L
    private val options =
        BulkOnboardingOptions.newBuilder()
            .context(BulkOnboardingContext.GLOBAL_PAYROLL)
            .companyId(companyId)
            .entityId(entityId)
            .build()

    @BeforeEach
    fun setUp() {
        every { departmentServiceAdapter.getDepartments(any()) } returns
            listOf(
                Department(1, companyId, "Department 1"),
            )
    }

    @Test
    fun `should throw exception when company doesn't have GP offering`() {
        setUpEntityCapabilities(settings = EntityCapabilitySettings.NO_GP_OFFERING)
        assertThrows<MplBusinessException> { underTest.getDataSpecs(options, null) }
    }

    @Test
    fun `should throw exception when entity doesn't have GP capabilities`() {
        setUpEntityCapabilities(
            settings = EntityCapabilitySettings.HAS_GP_OFFERING_BUT_NO_GP_CAPABILITIES)
        assertThrows<MplBusinessException> { underTest.getDataSpecs(options, null) }
    }

    @ParameterizedTest
    @ValueSource(strings = ["GROSS_TO_NET", "SALARY_DISBURSEMENT"])
    fun `should not throw exception when entity has GP capabilities`(capability: String) {
        setUpEntityCapabilities(
            settings =
                EntityCapabilitySettings(
                    offerings = listOf(OfferingCode.GLOBAL_PAYROLL),
                    capabilities = listOf(Capability.valueOf(capability))))
        every { bulkContractModule.getDataSpecs(options, null) } returns emptyList()
        every { bulkMemberModule.getDataSpecs(options, null) } returns emptyList()
        every { bulkContractOnboardingModule.getDataSpecs(options, null) } returns emptyList()
        every { bulkCompensationModule.getDataSpecs(options, null) } returns emptyList()
        every { bulkMemberLegalDataModule.getDataSpecs(options, null) } returns emptyList()
        every { bulkMemberBankDataModule.getDataSpecs(options, null) } returns emptyList()
        every { bulkOrgManagementModule.getDataSpecs(options, null) } returns emptyList()
        every { bulkTimeOffModule.getDataSpecs(options, null) } returns emptyList()
        every { bulkContractActivationModule.getDataSpecs(options, null) } returns emptyList()

        underTest.getDataSpecs(options, null)
    }

    @Test
    fun `should throw exception when company doesn't have any department`() {
        setUpEntityCapabilities(
            settings = EntityCapabilitySettings.HAS_GP_OFFERING_AND_GP_CAPABILITIES)
        every { departmentServiceAdapter.getDepartments(any()) } returns emptyList()
        assertThrows<MplBusinessException> { underTest.getDataSpecs(options, null) }
    }

    @Test
    fun `should remove contractId from input data before validating in order to disable upsert capability`() {
        setUpEntityCapabilities(
            settings =
                EntityCapabilitySettings(
                    offerings = listOf(OfferingCode.GLOBAL_PAYROLL),
                    capabilities = listOf(Capability.GROSS_TO_NET)))
        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification = EmployeeIdentification(rowNumber = 1),
                            data = mapOf("contractId" to "123", "email" to "<EMAIL>"))))
        setUpEmptyValidation()

        underTest.validate(input, options)
        verify {
            bulkContractModule.validate(
                withArg { argument ->
                    assertThat(argument).allMatch { employeeData ->
                        !employeeData.data.containsKey("contractId")
                    }
                },
                any(),
                any())
        }
    }

    @Test
    fun `should add contract params for compensation request`() {

        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    employeeId = "EMP1",
                                    rowNumber = 1,
                                    rowIdentifierFromSheet = "11"),
                            data =
                                mapOf(
                                    "contractId" to "123",
                                    "email" to "<EMAIL>",
                                    "type" to "HR_MEMBER",
                                    "startOn" to "2024-05-17",
                                    "endOn" to "2024-07-17"),
                            group = EMPLOYMENT_DATA_GROUP),
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    employeeId = "EMP1",
                                    rowNumber = 1,
                                    rowIdentifierFromSheet = "11"),
                            data = mapOf(),
                            group = COMPENSATION_DATA_GROUP)))
        setUpEmptyValidation()
        setUpEntityCapabilities(
            settings = EntityCapabilitySettings.HAS_GP_OFFERING_AND_GP_CAPABILITIES)

        underTest.validate(
            input,
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.GLOBAL_PAYROLL)
                .companyId(companyId)
                .countryCode(CountryCode.IND)
                .entityId(2L)
                .build())
        verify {
            bulkCompensationModule.validate(
                withArg { argument ->
                    argument.forEach { employeeData ->
                        assertThat(employeeData.data["CONTRACT_START_ON"]).isEqualTo("2024-05-17")
                        assertThat(employeeData.data["CONTRACT_TERM"]).isEmpty()
                        assertThat(employeeData.data["CONTRACT_STATUS"])
                            .isEqualTo(ContractStatus.ONBOARDING.name)
                        assertThat(employeeData.data["CONTRACT_TYPE"])
                            .isEqualTo(ContractType.HR_MEMBER.name)
                    }
                },
                any(),
                any())
        }
    }

    @Test
    fun `should not override module params`() {
        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    employeeId = "EMP1",
                                    rowNumber = 1,
                                    rowIdentifierFromSheet = "11"),
                            data =
                                mapOf(
                                    "contractId" to "123",
                                    "email" to "<EMAIL>",
                                    "type" to "HR_MEMBER",
                                    "startOn" to "2024-05-17",
                                    "endOn" to "2024-07-17"),
                            group = EMPLOYMENT_DATA_GROUP),
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    employeeId = "EMP1",
                                    rowNumber = 1,
                                    rowIdentifierFromSheet = "11"),
                            data = mapOf(),
                            group = COMPENSATION_DATA_GROUP)))
        setUpEmptyValidation()
        setUpEntityCapabilities(
            settings = EntityCapabilitySettings.HAS_GP_OFFERING_AND_GP_CAPABILITIES)

        val capturedModuleParams = slot<ModuleParams>()

        underTest.validate(
            input,
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.GLOBAL_PAYROLL)
                .companyId(companyId)
                .countryCode(CountryCode.IND)
                .entityId(2L)
                .build())

        verify {
            bulkCompensationModule.validate(
                withArg { argument ->
                    argument.forEach { employeeData ->
                        assertThat(employeeData.data["CONTRACT_START_ON"]).isEqualTo("2024-05-17")
                        assertThat(employeeData.data["CONTRACT_TERM"]).isEmpty()
                        assertThat(employeeData.data["CONTRACT_STATUS"])
                            .isEqualTo(ContractStatus.ONBOARDING.name)
                        assertThat(employeeData.data["CONTRACT_TYPE"])
                            .isEqualTo(ContractType.HR_MEMBER.name)
                    }
                },
                any(),
                capture(capturedModuleParams))
        }

        assertThat(capturedModuleParams.captured.workPlaceEntityId).isEqualTo(entityId)

        // Verify company service was not called
        verify(exactly = 0) { companyServiceAdapter.getCompanyPrimaryLegalEntity(companyId) }
    }

    private fun setUpEmptyValidation() {
        every { bulkMemberModule.identifier() } returns BulkMemberModule.MODULE_NAME
        every { bulkContractModule.identifier() } returns BulkContractModule.MODULE_NAME
        every { bulkContractOnboardingModule.identifier() } returns
            BulkContractOnboardingModule.MODULE_NAME
        every { bulkCompensationModule.identifier() } returns BulkCompensationModuleV2.MODULE_NAME
        every { bulkMemberLegalDataModule.identifier() } returns
            BulkMemberLegalDataModule.MODULE_NAME
        every { bulkMemberBankDataModule.identifier() } returns BulkMemberBankDataModule.MODULE_NAME
        every { bulkOrgManagementModule.identifier() } returns BulkOrgManagementModule.MODULE_NAME
        every { bulkTimeOffModule.identifier() } returns BulkTimeOffModule.MODULE_NAME
        every { bulkContractActivationModule.identifier() } returns
            BulkContractActivationModule.MODULE_NAME

        every { bulkContractModule.validate(any(), any(), any()) } answers
            {
                println(invocation.args[0])
                emptyList()
            }
        every { bulkMemberModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkContractOnboardingModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkCompensationModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkMemberLegalDataModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkMemberBankDataModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkOrgManagementModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkTimeOffModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkContractActivationModule.validate(any(), any(), any()) } returns emptyList()
    }

    data class EntityCapabilitySettings(
        val offerings: List<OfferingCode>,
        val capabilities: List<Capability>
    ) {
        companion object {
            val NO_GP_OFFERING = EntityCapabilitySettings(emptyList(), emptyList())
            val HAS_GP_OFFERING_BUT_NO_GP_CAPABILITIES =
                EntityCapabilitySettings(listOf(OfferingCode.GLOBAL_PAYROLL), emptyList())
            val HAS_GP_OFFERING_AND_GP_CAPABILITIES =
                EntityCapabilitySettings(
                    listOf(OfferingCode.GLOBAL_PAYROLL),
                    listOf(Capability.GROSS_TO_NET, Capability.SALARY_DISBURSEMENT))
        }
    }

    private fun setUpEntityCapabilities(
        companyId: Long = this.companyId,
        entityId: Long = this.entityId,
        settings: EntityCapabilitySettings
    ) {
        every { companyServiceAdapter.getCompanyOfferings(companyId) } returns
            settings.offerings
                .map { CompanyOffering.newBuilder().setOfferingCode(it.name).build() }
                .toMutableList()
        if (settings.offerings.contains(OfferingCode.GLOBAL_PAYROLL)) {
            every { companyServiceAdapter.getLegalEntity(entityId) } returns
                LegalEntity.newBuilder()
                    .addAllCapabilities(settings.capabilities.map { it.name })
                    .build()
        }
    }
}
