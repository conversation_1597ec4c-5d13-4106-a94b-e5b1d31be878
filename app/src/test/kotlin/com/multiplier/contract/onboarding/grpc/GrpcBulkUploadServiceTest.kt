package com.multiplier.contract.onboarding.grpc

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulkupload.*
import com.multiplier.contract.onboarding.service.bulkupload.FieldRequirement
import com.multiplier.contract.onboarding.service.bulkupload.RowData
import com.multiplier.grpc.common.bulkupload.v1.*
import io.grpc.internal.testing.StreamRecorder
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class GrpcBulkUploadServiceTest {

    @MockK private lateinit var bulkUploadService: BulkUploadService

    @InjectMockKs private lateinit var service: GrpcBulkUploadService

    @Nested
    inner class GetFieldRequirements {

        @Test
        fun `returns field requirements`() {
            val responseRecorder = StreamRecorder.create<FieldRequirementsResponse>()
            val request = fieldRequirementsRequest {}

            every { bulkUploadService.getFieldRequirements(any()) } returns
                listOf(
                    FieldRequirement(
                        key = "key 1",
                    ),
                    FieldRequirement(
                        key = "key 2",
                    ))

            service.getFieldRequirements(request, responseRecorder)

            verify(exactly = 1) { bulkUploadService.getFieldRequirements(any()) }
            assertEquals(2, responseRecorder.values.first().fieldRequirementsList.size)
            assertEquals("key 1", responseRecorder.values.first().fieldRequirementsList[0].key)
            assertEquals("key 2", responseRecorder.values.first().fieldRequirementsList[1].key)
        }
    }

    @Nested
    inner class GetFieldData {

        @Test
        fun `returns field requirements`() {
            val responseRecorder = StreamRecorder.create<BulkDataResponse>()
            val request = bulkDataRequest {}

            every { bulkUploadService.getFieldData(any()) } returns
                listOf(
                    RowData(
                        key = PlatformKeys.newBuilder().setCompanyId(101L).build(),
                        group = EmployeeData.EMPLOYMENT_DATA_GROUP,
                        data = mapOf("key 1" to "value 1", "key 2" to "value 2")),
                    RowData(
                        key = PlatformKeys.newBuilder().setCompanyId(101L).build(),
                        group = EmployeeData.COMPENSATION_DATA_GROUP,
                        data = mapOf("key 3" to "value 3", "key 4" to "value 4")))

            service.getFieldData(request, responseRecorder)

            verify(exactly = 1) { bulkUploadService.getFieldData(any()) }

            assertEquals(2, responseRecorder.values.first().rowsList.size)
            assertEquals(101L, responseRecorder.values.first().rowsList[0].keys.companyId)
            assertEquals(
                EmployeeData.EMPLOYMENT_DATA_GROUP,
                responseRecorder.values.first().rowsList[0].group)
            assertEquals("value 1", responseRecorder.values.first().rowsList[0].dataMap["key 1"])
            assertEquals("value 2", responseRecorder.values.first().rowsList[0].dataMap["key 2"])

            assertEquals(101L, responseRecorder.values.first().rowsList[1].keys.companyId)
            assertEquals(
                EmployeeData.COMPENSATION_DATA_GROUP,
                responseRecorder.values.first().rowsList[1].group)
            assertEquals("value 3", responseRecorder.values.first().rowsList[1].dataMap["key 3"])
            assertEquals("value 4", responseRecorder.values.first().rowsList[1].dataMap["key 4"])
        }
    }

    @Nested
    inner class BulkValidateUpsertInput {
        @Test
        fun `validate bulk upsert input`() {
            val responseRecorder = StreamRecorder.create<ValidateUpsertInputBulkResponse>()
            val request = validateUpsertInputBulkRequest {
                useCase = "GLOBAL_PAYROLL"
                inputs.addAll(
                    listOf(
                        validateUpsertInputRequest {
                            inputId = "123"
                            keys = PlatformKeys.newBuilder().setCompanyId(101L).build()
                            data.putAll(mapOf("key 1" to "value 1", "key 2" to "value 2"))
                            group = EmployeeData.EMPLOYMENT_DATA_GROUP
                        },
                        validateUpsertInputRequest {
                            inputId = "456"
                            keys = PlatformKeys.newBuilder().setCompanyId(101L).build()
                            data.putAll(mapOf("key 3" to "value 3", "key 4" to "value 4"))
                            group = EmployeeData.COMPENSATION_DATA_GROUP
                        }))
                jsonCustomParams = "{\"a\":\"b\", \"c\":\"d\"}}"
            }

            every { bulkUploadService.validate(any()) } returns
                listOf(
                    ValidationResult(
                        inputIds = listOf("123"),
                        success = true,
                        validatedData = mapOf("key 1" to "value 1", "key 2" to "value 2"),
                        group = EmployeeData.EMPLOYMENT_DATA_GROUP,
                    ),
                    ValidationResult(
                        inputIds = listOf("456"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    key = "key 3", value = "value 3", errors = listOf("error 3"))),
                        validatedData = mapOf(),
                        group = EmployeeData.COMPENSATION_DATA_GROUP,
                    ))

            service.bulkValidateUpsertInput(request, responseRecorder)

            verify(exactly = 1) { bulkUploadService.validate(any()) }

            assertEquals(2, responseRecorder.values.first().resultsList.size)
            assertEquals("123", responseRecorder.values.first().resultsList[0].inputIdsList[0])
            assertEquals(true, responseRecorder.values.first().resultsList[0].success)
            assertEquals(
                mapOf("key 1" to "value 1", "key 2" to "value 2"),
                responseRecorder.values.first().resultsList[0].validatedInputDataMap)
            assertEquals(
                EmployeeData.EMPLOYMENT_DATA_GROUP,
                responseRecorder.values.first().resultsList[0].group)
            assertEquals(0, responseRecorder.values.first().resultsList[0].messagesList.size)

            assertEquals("456", responseRecorder.values.first().resultsList[1].inputIdsList[0])
            assertEquals(false, responseRecorder.values.first().resultsList[1].success)
            assertEquals(
                mapOf<String, String>(),
                responseRecorder.values.first().resultsList[1].validatedInputDataMap)
            assertEquals(
                EmployeeData.COMPENSATION_DATA_GROUP,
                responseRecorder.values.first().resultsList[1].group)
            assertEquals(
                listOf("error 3"),
                responseRecorder.values.first().resultsList[1].messagesList[0].errorsList)
        }

        @Test
        fun `fail when custom params are not key value`() {
            val responseRecorder = StreamRecorder.create<ValidateUpsertInputBulkResponse>()
            val request = validateUpsertInputBulkRequest {
                useCase = "GLOBAL_PAYROLL"
                inputs.addAll(
                    listOf(
                        validateUpsertInputRequest {
                            inputId = "123"
                            keys = PlatformKeys.newBuilder().setCompanyId(101L).build()
                            data.putAll(mapOf("key 1" to "value 1", "key 2" to "value 2"))
                            group = EmployeeData.EMPLOYMENT_DATA_GROUP
                        },
                        validateUpsertInputRequest {
                            inputId = "456"
                            keys = PlatformKeys.newBuilder().setCompanyId(101L).build()
                            data.putAll(mapOf("key 3" to "value 3", "key 4" to "value 4"))
                            group = EmployeeData.COMPENSATION_DATA_GROUP
                        }))
                jsonCustomParams = "{\"a\":{\"b\": \"c\"}}"
            }

            val exception =
                assertThrows(MplBusinessException::class.java) {
                    service.bulkValidateUpsertInput(request, responseRecorder)
                }

            assertEquals("unable to parse key value", exception.message)
        }

        @Test
        fun `fail when custom params are not sent`() {
            val responseRecorder = StreamRecorder.create<ValidateUpsertInputBulkResponse>()
            val request = validateUpsertInputBulkRequest {
                useCase = "GLOBAL_PAYROLL"
                inputs.addAll(
                    listOf(
                        validateUpsertInputRequest {
                            inputId = "123"
                            keys = PlatformKeys.newBuilder().setCompanyId(101L).build()
                            data.putAll(mapOf("key 1" to "value 1", "key 2" to "value 2"))
                            group = EmployeeData.EMPLOYMENT_DATA_GROUP
                        },
                        validateUpsertInputRequest {
                            inputId = "456"
                            keys = PlatformKeys.newBuilder().setCompanyId(101L).build()
                            data.putAll(mapOf("key 3" to "value 3", "key 4" to "value 4"))
                            group = EmployeeData.COMPENSATION_DATA_GROUP
                        }))
                jsonCustomParams = ""
            }

            every { bulkUploadService.validate(any()) } returns
                listOf(
                    ValidationResult(
                        inputIds = listOf("123"),
                        success = true,
                        validatedData = mapOf("key 1" to "value 1", "key 2" to "value 2"),
                        group = EmployeeData.EMPLOYMENT_DATA_GROUP,
                    ),
                    ValidationResult(
                        inputIds = listOf("456"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    key = "key 3", value = "value 3", errors = listOf("error 3"))),
                        validatedData = mapOf(),
                        group = EmployeeData.COMPENSATION_DATA_GROUP,
                    ))

            service.bulkValidateUpsertInput(request, responseRecorder)

            verify(exactly = 1) { bulkUploadService.validate(any()) }

            assertEquals(2, responseRecorder.values.first().resultsList.size)
            assertEquals("123", responseRecorder.values.first().resultsList[0].inputIdsList[0])
            assertEquals(true, responseRecorder.values.first().resultsList[0].success)
            assertEquals(
                mapOf("key 1" to "value 1", "key 2" to "value 2"),
                responseRecorder.values.first().resultsList[0].validatedInputDataMap)
            assertEquals(
                EmployeeData.EMPLOYMENT_DATA_GROUP,
                responseRecorder.values.first().resultsList[0].group)
            assertEquals(0, responseRecorder.values.first().resultsList[0].messagesList.size)

            assertEquals("456", responseRecorder.values.first().resultsList[1].inputIdsList[0])
            assertEquals(false, responseRecorder.values.first().resultsList[1].success)
            assertEquals(
                mapOf<String, String>(),
                responseRecorder.values.first().resultsList[1].validatedInputDataMap)
            assertEquals(
                EmployeeData.COMPENSATION_DATA_GROUP,
                responseRecorder.values.first().resultsList[1].group)
            assertEquals(
                listOf("error 3"),
                responseRecorder.values.first().resultsList[1].messagesList[0].errorsList)
        }
    }

    @Nested
    inner class BulkUpsertInput {
        @Test
        fun `bulk upsert input`() {
            val responseRecorder = StreamRecorder.create<UpsertBulkResponse>()
            val request = upsertBulkRequest {
                jobId = 111L
                useCase = "GLOBAL_PAYROLL"
                inputs.addAll(
                    listOf(
                        upsertRequest {
                            inputId = "123"
                            data.putAll(mapOf("key 1" to "value 1", "key 2" to "value 2"))
                            group = EmployeeData.EMPLOYMENT_DATA_GROUP
                        },
                        upsertRequest {
                            inputId = "456"
                            data.putAll(mapOf("key 3" to "value 3", "key 4" to "value 4"))
                            group = EmployeeData.COMPENSATION_DATA_GROUP
                        }))
            }

            every { bulkUploadService.upsert(any()) } returns
                listOf(
                    UpsertResult(
                        inputId = "123",
                        success = true,
                        upsertedData = mapOf("contractId" to "201"),
                        group = EmployeeData.EMPLOYMENT_DATA_GROUP,
                    ),
                    UpsertResult(
                        inputId = "456",
                        success = false,
                        errors = listOf("error 3"),
                        upsertedData = mapOf(),
                        group = EmployeeData.COMPENSATION_DATA_GROUP,
                    ))

            service.bulkUpsert(request, responseRecorder)

            verify(exactly = 1) { bulkUploadService.upsert(any()) }

            assertEquals(2, responseRecorder.values.first().resultsList.size)
            assertEquals("123", responseRecorder.values.first().resultsList[0].inputId)
            assertEquals(true, responseRecorder.values.first().resultsList[0].success)
            assertEquals(
                mapOf("contractId" to "201"),
                responseRecorder.values.first().resultsList[0].upsertedDataMap)
            assertEquals(
                EmployeeData.EMPLOYMENT_DATA_GROUP,
                responseRecorder.values.first().resultsList[0].group)
            assertEquals(0, responseRecorder.values.first().resultsList[0].errorsList.size)

            assertEquals("456", responseRecorder.values.first().resultsList[1].inputId)
            assertEquals(false, responseRecorder.values.first().resultsList[1].success)
            assertEquals(
                mapOf<String, String>(),
                responseRecorder.values.first().resultsList[1].upsertedDataMap)
            assertEquals(
                EmployeeData.COMPENSATION_DATA_GROUP,
                responseRecorder.values.first().resultsList[1].group)
            assertEquals(
                listOf("error 3"), responseRecorder.values.first().resultsList[1].errorsList)
        }
    }
}
