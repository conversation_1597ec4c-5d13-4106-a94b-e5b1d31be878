package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.BulkContractServiceGrpc
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkComplianceServiceAdapterTest {
    @MockK private lateinit var bulkService: BulkContractServiceGrpc.BulkContractServiceBlockingStub

    @InjectMockKs private lateinit var underTest: BulkComplianceServiceAdapter

    @Test
    fun `validateUpdateComplianceInputs should correctly convert and return results`() {
        val mockEmployeeData =
            listOf(
                EmployeeData(
                    EmployeeIdentification(rowNumber = 2),
                    mapOf(
                        "noticeAfterProbation.value" to "30",
                        "noticeAfterProbation.unit" to "DAYS",
                    )))
        val mockOptions =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                .build()
        val mockResponse =
            BulkContract.ValidateUpdateComplianceInputsResponse.newBuilder()
                .addResults(
                    BulkContract.UpdateComplianceValidationResult.newBuilder()
                        .setSuccess(true)
                        .setRequestId("2")
                        .setValidatedInput(BulkContract.UpdateComplianceInput.newBuilder().build())
                        .build())
                .build()

        every { bulkService.validateUpdateComplianceInputs(any()) } returns mockResponse

        val results = underTest.validateUpdateComplianceInputs(mockEmployeeData, mockOptions)

        verify { bulkService.validateUpdateComplianceInputs(any()) }
        assertEquals(1, results.size)
        assertEquals("2", results.first().validationId)
    }

    @Test
    fun `updateCompliances should return empty list when inputs are empty`() {
        val results =
            underTest.updateCompliances(
                emptyList(),
                BulkOnboardingOptions.newBuilder()
                    .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                    .build())
        assertEquals(0, results.size)
    }

    @Test
    fun `updateCompliances should handle success cases`() {
        val mockInputs =
            listOf(
                CreationInput(
                    requestId = "test",
                    contractId = 123,
                    data =
                        BulkContract.UpdateComplianceInput.newBuilder()
                            .setContractId(123L)
                            .build()))
        val mockOptions =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                .build()
        val mockResult =
            BulkContract.UpdateCompliancesResponse.newBuilder()
                .addResults(
                    BulkContract.UpdateComplianceResult.newBuilder().setSuccess(true).build())
                .build()

        every { bulkService.updateCompliances(any()) } returns mockResult

        val results = underTest.updateCompliances(mockInputs, mockOptions)

        verify { bulkService.updateCompliances(any()) }
        assertThat(results).allSatisfy { it.success }
    }

    @Test
    fun `updateCompliances should handle gRPC service exceptions`() {
        val mockInputs =
            listOf(
                CreationInput(
                    requestId = "test",
                    contractId = 123,
                    data =
                        BulkContract.UpdateComplianceInput.newBuilder()
                            .setContractId(123L)
                            .build()))
        val mockOptions =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                .build()

        every { bulkService.updateCompliances(any()) } throws RuntimeException("Service failure")

        val results = underTest.updateCompliances(mockInputs, mockOptions)

        verify { bulkService.updateCompliances(any()) }

        assertEquals(1, results.size)
        assertThat(results.flatMap { it.errors })
            .containsExactly(
                "Update compliance failed due to an internal error: unknown exception occurred")
    }
}
