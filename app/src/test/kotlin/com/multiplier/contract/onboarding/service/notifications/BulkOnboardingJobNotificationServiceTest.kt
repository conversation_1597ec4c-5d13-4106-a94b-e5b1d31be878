package com.multiplier.contract.onboarding.service.notifications

import com.multiplier.bulk.upload.schema.kafka.BulkUploadJob
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.company.schema.grpc.legalEntity
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.DocumentResponse
import com.multiplier.contract.onboarding.adapter.DocumentServiceAdapter
import com.multiplier.contract.onboarding.adapter.OperationsUserServiceAdapter
import com.multiplier.contract.onboarding.domain.model.Company
import com.multiplier.contract.onboarding.domain.model.CompanyUser
import com.multiplier.contract.onboarding.service.extensions.userId
import com.multiplier.contract.onboarding.types.NotificationType
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkOnboardingJobNotificationServiceTest {
    @MockK(relaxed = true) private lateinit var notificationService: NotificationService
    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter
    @MockK private lateinit var documentServiceAdapter: DocumentServiceAdapter
    @MockK private lateinit var operationsUserServiceAdapter: OperationsUserServiceAdapter
    @MockK(relaxed = true) private lateinit var currentUser: CurrentUser
    private val baseUrl = "http://platform.base.url"
    private val systemNotificationEmail = "<EMAIL>"

    private val underTest: BulkOnboardingJobNotificationService by lazy {
        BulkOnboardingJobNotificationService(
            notificationService,
            companyServiceAdapter,
            documentServiceAdapter,
            operationsUserServiceAdapter,
            currentUser,
            baseUrl,
            systemNotificationEmail,
        )
    }

    @BeforeEach
    fun setUp() {
        every { currentUser.userId } returns 101L
        every { companyServiceAdapter.getCompany(any<Long>()) } answers
            {
                mockk<Company> {
                    every { id } returns firstArg<Long>()
                    every { displayName } returns "Company name"
                    every { logoId } returns 301L
                    every { countryFullName } returns "Singapore"
                }
            }
        every { companyServiceAdapter.getLegalEntity(any<Long>()) } answers
            {
                legalEntity {
                    id = firstArg<Long>()
                    legalName = "Legal entity name"
                }
            }
        every { documentServiceAdapter.getCompanyLogoLinks(any<List<Long>>()) } answers
            {
                firstArg<List<Long>>().associateWith {
                    DocumentResponse(
                        id = it,
                        viewUrl = "https://test.com/document_$it.pdf",
                    )
                }
            }
        every { companyServiceAdapter.getCompanyUserByUserId(any<Long>()) } answers
            {
                mockk<CompanyUser> {
                    every { id } returns firstArg<Long>()
                    every { email } returns "<EMAIL>"
                    every { firstName } returns "First name"
                }
            }
        every { operationsUserServiceAdapter.getOperationsUsersByUserId(any<Long>()) } returns null
    }

    @Test
    fun notifyOnboardingFileUploadedSuccessfully() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("GLOBAL_PAYROLL_MEMBER_ONBOARDING")
                .build()

        underTest.notifyOnboardingFileUploadedSuccessfully(job)

        verify {
            notificationService.send(
                withArg {
                    assertAll(
                        {
                            assert(
                                it.subject ==
                                    "Bulk upload of onboarding details completed for Legal entity name")
                        },
                        { assert(it.from == "Multiplier <$systemNotificationEmail>") },
                        { assert(it.to == "<EMAIL>") },
                        {
                            assert(
                                it.type == NotificationType.BulkOnboardingFileUploadedSuccessfully)
                        },
                        {
                            assert(
                                it.data["companyLogoLink"] == "https://test.com/document_301.pdf")
                        },
                        { assert(it.data["companyName"] == "Company name") },
                        { assert(it.data["entityName"] == "Legal entity name") },
                        { assert(it.data["companyCountry"] == "Singapore") },
                        { assert(it.data["adminName"] == "First name") },
                        { assert(it.data["module"] == "GLOBAL_PAYROLL_MEMBER_ONBOARDING") },
                        {
                            assert(
                                it.data["jobSummaryLink"] ==
                                    "$baseUrl/company/member-onboard/bulk-upload/${job.id}/summary")
                        })
                })
        }
    }

    @Test
    fun skipNotificationForUnknownModule() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("UNKNOWN_MODULE")
                .build()

        underTest.notifyOnboardingFileUploadedSuccessfully(job)

        verify(exactly = 0) { notificationService.send(any()) }
    }

    @Test
    fun notifyOnboardingFileUploadedSuccessfullyFreelancer() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("FREELANCER_MEMBER_ONBOARDING")
                .build()

        underTest.notifyOnboardingFileUploadedSuccessfully(job)

        verify {
            notificationService.send(
                withArg {
                    assertAll(
                        {
                            assert(
                                it.subject ==
                                    "Bulk upload of onboarding details completed for Legal entity name")
                        },
                        { assert(it.from == "Multiplier <$systemNotificationEmail>") },
                        { assert(it.to == "<EMAIL>") },
                        {
                            assert(
                                it.type == NotificationType.BulkOnboardingFileUploadedSuccessfully)
                        },
                        {
                            assert(
                                it.data["companyLogoLink"] == "https://test.com/document_301.pdf")
                        },
                        { assert(it.data["companyName"] == "Company name") },
                        { assert(it.data["entityName"] == "Legal entity name") },
                        { assert(it.data["companyCountry"] == "Singapore") },
                        { assert(it.data["adminName"] == "First name") },
                        { assert(it.data["module"] == "FREELANCER_MEMBER_ONBOARDING") },
                        {
                            assert(
                                it.data["jobSummaryLink"] ==
                                    "$baseUrl/company/member-onboard/aor-contractor-bulk-upload/freelancer/bulk-upload/${job.id}/summary")
                        })
                })
        }
    }

    @Test
    fun notifyOnboardingFileUploadedSuccessfullyAor() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("AOR_MEMBER_ONBOARDING")
                .build()

        underTest.notifyOnboardingFileUploadedSuccessfully(job)

        verify {
            notificationService.send(
                withArg {
                    assertAll(
                        {
                            assert(
                                it.subject ==
                                    "Bulk upload of onboarding details completed for Legal entity name")
                        },
                        { assert(it.from == "Multiplier <$systemNotificationEmail>") },
                        { assert(it.to == "<EMAIL>") },
                        {
                            assert(
                                it.type == NotificationType.BulkOnboardingFileUploadedSuccessfully)
                        },
                        {
                            assert(
                                it.data["companyLogoLink"] == "https://test.com/document_301.pdf")
                        },
                        { assert(it.data["companyName"] == "Company name") },
                        { assert(it.data["entityName"] == "Legal entity name") },
                        { assert(it.data["companyCountry"] == "Singapore") },
                        { assert(it.data["adminName"] == "First name") },
                        { assert(it.data["module"] == "AOR_MEMBER_ONBOARDING") },
                        {
                            assert(
                                it.data["jobSummaryLink"] ==
                                    "$baseUrl/company/member-onboard/aor-contractor-bulk-upload/aor/bulk-upload/${job.id}/summary")
                        })
                })
        }
    }

    @Test
    fun skipNotifyOnboardingFileUploadedEor() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("EOR_MEMBER_ONBOARDING")
                .build()

        underTest.notifyOnboardingFileUploadedSuccessfully(job)

        verify(exactly = 0) { notificationService.send(any()) }
    }

    @Test
    fun notifyOnboardingFileUploadedFailedWhenUnknownModulePassed() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("UNKNOWN")
                .build()

        underTest.notifyOnboardingFileUploadedSuccessfully(job)

        verify(exactly = 0) { notificationService.send(any()) }
    }

    @Test
    fun notifyOnboardingFileUploadedFailedWhenMoreThanOneModule() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("GLOBAL_PAYROLL_MEMBER_ONBOARDING")
                .addModules("FREELANCER_MEMBER_ONBOARDING")
                .build()

        underTest.notifyOnboardingFileUploadedSuccessfully(job)

        verify(exactly = 0) { notificationService.send(any()) }
    }

    @Test
    fun notifyOnboardingEmployeesAddedToTeam() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("GLOBAL_PAYROLL_MEMBER_ONBOARDING")
                .build()

        val employeeAddedCount = 1234

        underTest.notifyBulkOnboardingEmployeesAddedToTeam(job, employeeAddedCount)

        verify {
            notificationService.send(
                withArg {
                    assertAll(
                        {
                            assert(
                                it.subject ==
                                    "Employee addition successful: $employeeAddedCount employees added!")
                        },
                        { assert(it.from == "Multiplier <$systemNotificationEmail>") },
                        { assert(it.to == "<EMAIL>") },
                        { assert(it.type == NotificationType.BulkOnboardingEmployeeAddedToTeam) },
                        {
                            assert(
                                it.data["companyLogoLink"] == "https://test.com/document_301.pdf")
                        },
                        { assert(it.data["module"] == "GLOBAL_PAYROLL_MEMBER_ONBOARDING") },
                        { assert(it.data["companyName"] == "Company name") },
                        { assert(it.data["entityName"] == "Legal entity name") },
                        { assert(it.data["companyCountry"] == "Singapore") },
                        { assert(it.data["adminName"] == "First name") },
                        { assert(it.data["employeeCount"] == employeeAddedCount) },
                        {
                            assert(
                                it.data["jobReviewLink"] ==
                                    "$baseUrl/company/member-onboard/bulk-upload/${job.id}/review?tab=added")
                        })
                })
        }
    }

    @Test
    fun notifyOnboardingEmployeesAddedToTeamForFreelancer() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("FREELANCER_MEMBER_ONBOARDING")
                .build()

        val employeeAddedCount = 1234

        underTest.notifyBulkOnboardingEmployeesAddedToTeam(job, employeeAddedCount)

        verify {
            notificationService.send(
                withArg {
                    assertAll(
                        {
                            assert(
                                it.subject ==
                                    "Contractor addition successful: $employeeAddedCount contractors added!")
                        },
                        { assert(it.from == "Multiplier <$systemNotificationEmail>") },
                        { assert(it.to == "<EMAIL>") },
                        { assert(it.type == NotificationType.BulkOnboardingEmployeeAddedToTeam) },
                        {
                            assert(
                                it.data["companyLogoLink"] == "https://test.com/document_301.pdf")
                        },
                        { assert(it.data["companyName"] == "Company name") },
                        { assert(it.data["entityName"] == "Legal entity name") },
                        { assert(it.data["companyCountry"] == "Singapore") },
                        { assert(it.data["adminName"] == "First name") },
                        { assert(it.data["employeeCount"] == employeeAddedCount) },
                        { assert(it.data["module"] == "FREELANCER_MEMBER_ONBOARDING") },
                        {
                            assert(
                                it.data["jobReviewLink"] ==
                                    "$baseUrl/company/team?sortBy=createdOn&sortDir=desc&subtab=DEFAULT&page=1&rows=10&tab=ONBOARDING")
                        })
                })
        }
    }

    @Test
    fun skipNotifyOnboardingEmployeesAddedToTeamForEor() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("EOR_MEMBER_ONBOARDING")
                .build()

        val employeeAddedCount = 1234

        underTest.notifyBulkOnboardingEmployeesAddedToTeam(job, employeeAddedCount)

        verify(exactly = 0) { notificationService.send(any()) }
    }

    @Test
    fun notifyOnboardingEmployeesAddedToTeamForAor() {
        val job =
            BulkUploadJob.newBuilder()
                .setId(201L)
                .setCompanyId(201L)
                .setEntityId(301L)
                .addModules("AOR_MEMBER_ONBOARDING")
                .build()

        val employeeAddedCount = 1234

        underTest.notifyBulkOnboardingEmployeesAddedToTeam(job, employeeAddedCount)

        verify {
            notificationService.send(
                withArg {
                    assertAll(
                        {
                            assert(
                                it.subject ==
                                    "Aor Contractor addition successful: $employeeAddedCount contractors added!")
                        },
                        { assert(it.from == "Multiplier <$systemNotificationEmail>") },
                        { assert(it.to == "<EMAIL>") },
                        { assert(it.type == NotificationType.BulkOnboardingEmployeeAddedToTeam) },
                        {
                            assert(
                                it.data["companyLogoLink"] == "https://test.com/document_301.pdf")
                        },
                        { assert(it.data["companyName"] == "Company name") },
                        { assert(it.data["entityName"] == "Legal entity name") },
                        { assert(it.data["companyCountry"] == "Singapore") },
                        { assert(it.data["adminName"] == "First name") },
                        { assert(it.data["employeeCount"] == employeeAddedCount) },
                        { assert(it.data["module"] == "AOR_MEMBER_ONBOARDING") },
                        {
                            assert(
                                it.data["jobReviewLink"] ==
                                    "$baseUrl/company/team?sortBy=createdOn&sortDir=desc&subtab=DEFAULT&page=1&rows=10&tab=ONBOARDING")
                        })
                })
        }
    }
}
