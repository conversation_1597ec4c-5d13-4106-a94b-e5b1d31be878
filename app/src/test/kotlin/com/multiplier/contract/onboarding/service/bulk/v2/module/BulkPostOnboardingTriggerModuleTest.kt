package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.bulk.BulkContractServiceAdapter
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkPostOnboardingTriggerModuleTest {
    @MockK private lateinit var bulkContractServiceAdapter: BulkContractServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkPostOnboardingTriggerModule

    @Test
    fun `should create onboarding for freelancer when benefits are enabled`() {
        val options =
            BulkOnboardingOptions(null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { bulkContractServiceAdapter.triggerPostOnboardingActions(any(), options) } returns
            emptyList()

        underTest.create(emptyList(), bulkCreationResult, options)

        verify { bulkContractServiceAdapter.triggerPostOnboardingActions(listOf(123), options) }
    }

    @Test
    fun `should throw error when error occured while calling contract service`() {
        val options =
            BulkOnboardingOptions(null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR)

        val bulkCreationResult =
            BulkCreationResultV2(
                newContractIds = listOf(123), requestIdToContractId = mapOf("11" to 123))

        every { bulkContractServiceAdapter.triggerPostOnboardingActions(any(), options) } throws
            IllegalStateException("failure in contract svc")

        val result = underTest.create(emptyList(), bulkCreationResult, options)

        assertEquals(
            listOf(
                "Post onboarding actions failed due to an internal error: unknown exception occurred"),
            result.errors.first().errors)
    }
}
