package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.member.schema.*
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberServiceAdapterTest {
    @MockK private lateinit var bulkService: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    @InjectMockKs private lateinit var underTest: BulkMemberServiceAdapter

    @Test
    fun validateMemberCreateInputs_calls_rpc_services_and_parse_response() {
        val inputs =
            listOf(
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 3),
                    data = mapOf("employeeId" to "101")),
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 4),
                    data = mapOf("employeeId" to "102")))

        every { bulkService.validateMemberCreateInputs(any()) } answers
            {
                ValidateMemberCreateInputsResponse.newBuilder()
                    .addAllValidationResults(
                        listOf(
                            MemberCreateValidationResult.newBuilder()
                                .setRequestId(inputs[0].identification.validationId)
                                .setSuccess(true)
                                .build(),
                            MemberCreateValidationResult.newBuilder()
                                .setRequestId(inputs[1].identification.validationId)
                                .setSuccess(false)
                                .addAllErrors(listOf("error1", "error2"))
                                .build(),
                        ))
                    .build()
            }

        val result =
            underTest.validateMemberCreateInputs(
                inputs,
                BulkOnboardingOptions.newBuilder()
                    .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                    .build())

        verify(exactly = 1) {
            bulkService.validateMemberCreateInputs(
                withArg {
                    assertEquals(2, it.inputsList.size)
                    assertEquals("HRIS_PROFILE_DATA", it.context)

                    assertEquals(inputs[0].identification.validationId, it.inputsList[0].requestId)
                    assertEquals(
                        inputs[0].data["employeeId"], it.inputsList[0].propertiesMap["employeeId"])

                    assertEquals(inputs[1].identification.validationId, it.inputsList[1].requestId)
                    assertEquals(
                        inputs[1].data["employeeId"], it.inputsList[1].propertiesMap["employeeId"])
                })
        }

        assertEquals(
            listOf(
                GrpcValidationResult(
                    success = true,
                    errors = emptyList(),
                    validationId = "rn_3",
                    input = MemberCreateInput.getDefaultInstance()),
                GrpcValidationResult(
                    success = false,
                    errors = listOf("error1", "error2"),
                    validationId = "rn_4",
                    input = MemberCreateInput.getDefaultInstance())),
            result)
    }

    @Test
    fun createMembers() {
        val inputs =
            listOf(
                CreationInput(
                    requestId = "3",
                    data = MemberCreateInput.newBuilder().setEmail("<EMAIL>").build()),
                CreationInput(
                    requestId = "4",
                    data = MemberCreateInput.newBuilder().setEmail("<EMAIL>").build()),
                CreationInput(
                    requestId = "5",
                    data = MemberCreateInput.newBuilder().setEmail("<EMAIL>").build()),
            )

        every { bulkService.createMembers(any()) } answers
            {
                CreateMembersResponse.newBuilder()
                    .addAllResults(
                        listOf(
                            MemberCreateResult.newBuilder()
                                .setSuccess(true)
                                .setRequestId(inputs[0].requestId)
                                .setMemberId(101L)
                                .build(),
                            MemberCreateResult.newBuilder()
                                .setSuccess(true)
                                .setRequestId(inputs[1].requestId)
                                .setMemberId(102L)
                                .build(),
                            MemberCreateResult.newBuilder()
                                .setSuccess(false)
                                .setRequestId(inputs[2].requestId)
                                .addAllErrors(listOf("error1", "error2"))
                                .build(),
                        ))
                    .build()
            }

        val result =
            underTest.createMembers(
                inputs,
                BulkOnboardingOptions(
                    CountryCode.IND,
                    ContractType.HR_MEMBER,
                    201,
                    201,
                    BulkOnboardingContext.HRIS_PROFILE_DATA))

        verify(exactly = 1) {
            bulkService.createMembers(
                withArg {
                    assertEquals(3, it.inputsList.size)
                    assertEquals("HRIS_PROFILE_DATA", it.context)

                    assertEquals("3", it.inputsList[0].requestId)
                    assertEquals("<EMAIL>", it.inputsList[0].email)

                    assertEquals("4", it.inputsList[1].requestId)
                    assertEquals("<EMAIL>", it.inputsList[1].email)

                    assertEquals("5", it.inputsList[2].requestId)
                    assertEquals("<EMAIL>", it.inputsList[2].email)
                })
        }

        assertEquals(
            listOf(
                CreationResult(requestId = "3", success = true, upsertedIds = listOf(101L)),
                CreationResult(requestId = "4", success = true, upsertedIds = listOf(102L)),
                CreationResult.error("5", listOf("error1", "error2"))),
            result)
    }
}
