package com.multiplier.contract.onboarding.service.bulk.excel

import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.graphql.BulkTemplateDataRow
import com.multiplier.contract.onboarding.service.bulk.dataSpec.DataSpecForEORService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService
import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationError
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.utils.base64StringToByteArray
import com.multiplier.contract.onboarding.utils.getFileFromResources
import com.multiplier.contract.onboarding.utils.projectTmpFolder
import com.multiplier.contract.onboarding.utils.writeFile
import java.io.ByteArrayInputStream
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class BulkUploadExcelTest {

    @Test
    fun `should parse into employee data`() {
        val file = getFileFromResources("/bulk/excel/dummy-bulk-upload-1-member.xlsx")

        val result = BulkUploadExcel.parse(file.readBytes()).employeeData

        val expected =
            EmployeeData(
                identification =
                    EmployeeIdentification(
                        employeeId = "1",
                        contractId = null,
                        firstName = "John",
                        lastName = "Wayne",
                        email = "<EMAIL>",
                        rowNumber = 3),
                data =
                    mapOf(
                        "salary" to "10000.00",
                        "gender" to "female",
                        "employeeId" to "1",
                        "rowIdentifier" to "",
                        "contractId" to "",
                        "firstName" to "John",
                        "lastName" to "Wayne",
                        "email" to "<EMAIL>",
                        "startOn" to "2024-12-18",
                        "rowNumber" to "3"),
                group = "Sheet1")

        assertThat(result).hasSize(1)
        assertThat(result.first()).isEqualTo(expected)
    }

    @Test
    fun `should parse employee data and group by row_identifier`() {
        val file = getFileFromResources("/bulk/excel/dummy-bulk-upload-with-row-identifier.xlsx")

        val result = BulkUploadExcel.parse(file.readBytes()).employeeData

        val expected =
            EmployeeData(
                identification =
                    EmployeeIdentification(
                        rowIdentifier = "ROW_1",
                        employeeId = null,
                        contractId = null,
                        firstName = "John",
                        lastName = "Wayne",
                        email = "<EMAIL>",
                        rowNumber = 3),
                data =
                    mapOf(
                        "salary" to "10000.00",
                        "gender" to "female",
                        "employeeId" to "",
                        "rowIdentifier" to "ROW_1",
                        "contractId" to "",
                        "firstName" to "John",
                        "lastName" to "Wayne",
                        "email" to "<EMAIL>",
                        "startOn" to "2024-07",
                        "rowNumber" to "3"),
                group = "Sheet1")

        assertThat(result).hasSize(2)
        assertThat(result.first()).isEqualTo(expected)
    }

    @Test
    fun `should return empty list when the sheet is empty`() {
        val file = getFileFromResources("/bulk/excel/dummy-bulk-upload-empty.xlsx")

        val result = BulkUploadExcel.parse(file.readBytes()).employeeData

        assertThat(result).isEmpty()
    }

    @Test
    fun `should be able to deserialize when identification columns are missing`() {
        val file = getFileFromResources("/bulk/excel/dummy-bulk-upload-missing-columns.xlsx")

        val result = BulkUploadExcel.parse(file.readBytes()).employeeData

        assertThat(result).hasSize(1)
    }

    @Test
    fun `can parse SGP employee data`() {
        val file = getFileFromResources("/bulk/global-payroll/Singapore-Template.xlsx")

        val result = BulkUploadExcel.parse(file.readBytes()).employeeData

        assertThat(result).hasSize(2)
    }

    @Test
    fun `should add errors to the appropriate rows while retaining the original column and value order, including empty cells`() {
        val input =
            getFileFromResources("/bulk/excel/bulk-upload-sheet-with-many-empty-columns.xlsx")

        val row2Error1 = "this is a very long error message and the sheet should update the width"
        val row2Error2 = "first name is missing"
        val row4Error1 = "salary is negative"

        val updatedByteArray =
            BulkUploadExcel.addValidationErrorsToInputSheet(
                inputSheet = input,
                validationErrors =
                    listOf(
                        EmployeeValidationError(
                            error = row2Error1,
                            rowNumber = 3,
                            columnName = "",
                            employeeName = "",
                        ),
                        EmployeeValidationError(
                            error = row2Error2,
                            rowNumber = 3,
                            columnName = "",
                            employeeName = "",
                        ),
                        EmployeeValidationError(
                            error = row4Error1,
                            rowNumber = 5,
                            columnName = "",
                            employeeName = "",
                        )))

        writeFile("${projectTmpFolder()}/download.xlsx", updatedByteArray)

        val rows = updatedByteArray.parseXlsx<BulkTemplateDataRow>()

        // poiji doesn't parse the header row so index is -1 compared to error row
        assertThat(rows[2].valueAtColumnOne).isEqualTo("1234")
        assertThat(rows[2].valueAtColumnZero).isEqualTo("$row2Error1\n$row2Error2")
        assertThat(rows[2].data["payFrequency"]).isEqualTo("MONTHLY")
        assertThat(rows[2].data["currency"]).isEqualTo("CHF")
        assertThat(rows[2].data["startOn"]).isEqualTo("2024-01-06")
        assertThat(rows[3].valueAtColumnZero).isEmpty()
        assertThat(rows[4].valueAtColumnZero).isEqualTo(row4Error1)
    }

    @Test
    fun `re-uploading an error report should not re-add the error column`() {
        val input = getFileFromResources("/bulk/excel/re-upload-error-report-test-file.xlsx")

        val updatedByteArray =
            BulkUploadExcel.addValidationErrorsToInputSheet(
                inputSheet = input,
                validationErrors =
                    listOf(
                        EmployeeValidationError(
                            error = "some error",
                            rowNumber = 2,
                            columnName = "",
                            employeeName = "",
                        ),
                    ))

        val rows = updatedByteArray.parseXlsx<BulkTemplateDataRow>()

        assertThat(rows[1].valueAtColumnOne).isEqualTo("12345")
    }

    @Test
    fun `should add a field & description column at the beginning of the file and each spec's description in the appropriate column`() {

        val options =
            BulkOnboardingOptions.newBuilder()
                .countryCode(CountryCode.SGP)
                .context(BulkOnboardingContext.GLOBAL_PAYROLL)
                .build()
        val dataSpecs =
            listOf(
                DataSpec(
                    key = "key1",
                    label = "label1",
                    type = DataSpecType.TEXT,
                    mandatory = true,
                    description = "some description"),
                DataSpec(
                    key = "key2",
                    label = "label2",
                    type = DataSpecType.TEXT,
                    mandatory = true,
                    description = null))

        val file = BulkUploadExcel.generate(options, dataSpecs)
        val byteArray = base64StringToByteArray(file.blob)

        writeFile("${projectTmpFolder()}/download.xlsx", byteArray)

        val rows = byteArray.parseXlsx<BulkTemplateDataRow>()

        assertThat(file.name)
            .isEqualTo(
                "Bulk Onboarding Template - GLOBAL_PAYROLL - SGP - " +
                    templateVersion(BulkOnboardingContext.GLOBAL_PAYROLL))
        assertThat(rows[0].valueAtColumnZero).isEqualTo("Field")
        assertThat(rows[1].valueAtColumnZero).isEqualTo("Description")
        assertThat(rows[1].valueAtColumnOne).isEqualTo("Mandatory - some description")
        assertThat(rows[1].valueAtColumnTwo).isEqualTo("Mandatory")
    }

    @Test
    fun `should add country code and template version as custom properties to Xlxs file and should be able to retrieve them`() {
        val options =
            BulkOnboardingOptions.newBuilder()
                .countryCode(CountryCode.SGP)
                .context(BulkOnboardingContext.GLOBAL_PAYROLL)
                .build()
        val dataSpecs =
            listOf(
                DataSpec(
                    key = "key",
                    label = "label",
                    type = DataSpecType.TEXT,
                    mandatory = true,
                    description = "some description"))

        val file = BulkUploadExcel.generate(options, dataSpecs)
        val byteArray = base64StringToByteArray(file.blob)

        val result = BulkUploadExcel.parse(byteArray)

        assertThat(result.templateData?.countryCode).isEqualTo(CountryCode.SGP.name)
        assertThat(result.templateData?.templateVersion)
            .isEqualTo(templateVersion(BulkOnboardingContext.GLOBAL_PAYROLL))
    }

    @Test
    fun `the generated template should have correct styling for header and description and default cell`() {
        val options =
            BulkOnboardingOptions.newBuilder()
                .countryCode(CountryCode.SGP)
                .context(BulkOnboardingContext.GLOBAL_PAYROLL)
                .build()
        val dataSpecs =
            listOf(
                DataSpec(
                    key = "key",
                    label = "label",
                    type = DataSpecType.TEXT,
                    mandatory = true,
                    description = "some description"),
                DataSpec(
                    key = "key",
                    label = "label",
                    type = DataSpecType.TEXT,
                    mandatory = true,
                    description = null))

        val file = BulkUploadExcel.generate(options, dataSpecs)
        val byteArray = base64StringToByteArray(file.blob)
        val inputStream = ByteArrayInputStream(byteArray)
        val workbook = XSSFWorkbook(inputStream)
        val sheet = workbook.getSheetAt(0)

        val expectedFontName = "Calibri"
        val expectedFontSize = 11 * PIXEL_TO_POINTS_RATIO
        val expectedFontSizeDescription = 9 * PIXEL_TO_POINTS_RATIO
        val expectedBold = false
        val expectedBoldHeader = true

        // for default cell
        val defaultCell = sheet.createRow(3).createCell(0)
        val defaultFont = workbook.getFontAt(defaultCell.cellStyle.fontIndex)

        assertEquals(expectedFontName, defaultFont.fontName)
        assertEquals(expectedFontSize, defaultFont.fontHeight.toInt())
        assertEquals(expectedBold, defaultFont.bold)

        // for header cell
        val headerCell = sheet.getRow(1).getCell(0)
        val headerFont = workbook.getFontAt(headerCell.cellStyle.fontIndex)

        assertEquals(expectedFontName, headerFont.fontName)
        assertEquals(expectedFontSize, headerFont.fontHeight.toInt())
        assertEquals(expectedBoldHeader, headerFont.bold)

        // for description cell
        val descriptionCell = sheet.getRow(2).getCell(1)
        val descriptionFont = workbook.getFontAt(descriptionCell.cellStyle.fontIndex)

        assertEquals(expectedFontName, descriptionFont.fontName)
        assertEquals(expectedFontSizeDescription, descriptionFont.fontHeight.toInt())
        assertEquals(expectedBold, descriptionFont.bold)
    }

    @Test
    fun `should not include country state`() {
        val options =
            BulkOnboardingOptions.newBuilder()
                .countryCode(CountryCode.SGP)
                .context(BulkOnboardingContext.EOR)
                .build()
        val dataSpecs = emptyList<DataSpec>()

        val file = BulkUploadExcel.generate(options, dataSpecs)
        val byteArray = base64StringToByteArray(file.blob)

        val rows = byteArray.parseXlsx<BulkTemplateDataRow>()

        assertThat(rows).isNotNull
        assertThat(rows.first().valueAtColumnOne)
            .isEqualTo(DataSpecForEORService.RowIdentifierSpec.label)
        assertThat(rows.first().data)
            .doesNotContainKeys(BulkContractDataService.StateOfEmploymentSpec.key)
    }
}
