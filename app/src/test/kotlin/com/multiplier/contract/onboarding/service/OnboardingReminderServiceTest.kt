package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.repository.JpaOnboardingReminderRepository
import com.multiplier.contract.onboarding.repository.model.JpaOnboardingReminder
import com.multiplier.contract.onboarding.testing.PostgreSQLContainerInitializer
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import kotlin.jvm.optionals.getOrNull
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayNameGeneration
import org.junit.jupiter.api.DisplayNameGenerator
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
@ActiveProfiles("integration-test")
@DataJpaTest
@ContextConfiguration(
    initializers = [PostgreSQLContainerInitializer::class],
    classes = [OnboardingReminderService::class])
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@EnableJpaRepositories("com.multiplier.contract.onboarding")
@EntityScan(basePackages = ["com.multiplier.contract.onboarding"])
class OnboardingReminderServiceTest {

    @Autowired lateinit var underTest: OnboardingReminderService

    @Autowired lateinit var onboardingReminderRepository: JpaOnboardingReminderRepository

    @Test
    fun `should create onboarding reminder successfully`() {
        val contract = Contract.newBuilder().setId(123L).setCompanyId(456).setMemberId(2).build()

        underTest.createOrUpdateReminder(
            contract, NotificationType.OpsMemberSignContractReminderCta)

        assertThat(
                onboardingReminderRepository
                    .findByContractIdAndTemplateName(
                        123L, NotificationType.OpsMemberSignContractReminderCta)
                    .getOrNull())
            .isNotNull()
        assertThat(
                onboardingReminderRepository
                    .findByContractIdAndTemplateName(
                        123L, NotificationType.OpsMemberSignContractReminderCta)
                    .get()
                    .count)
            .isEqualTo(1)
    }

    @Test
    fun `should update onboarding reminder successfully`() {
        val contract = Contract.newBuilder().setId(123L).setCompanyId(456).setMemberId(2).build()

        onboardingReminderRepository.save(
            JpaOnboardingReminder(
                count = 1,
                contractId = contract.id,
                templateName = NotificationType.OpsMemberSignContractReminderCta))

        val reminder =
            underTest.createOrUpdateReminder(
                contract, NotificationType.OpsMemberSignContractReminderCta)

        assertThat(reminder.count).isEqualTo(2)
        assertThat(reminder.templateName.toString())
            .isEqualTo(NotificationType.OpsMemberSignContractReminderCta.toString())
        assertThat(reminder.contractId).isEqualTo(contract.id)

        assertThat(
                onboardingReminderRepository
                    .findByContractIdAndTemplateName(
                        contract.id, NotificationType.OpsMemberSignContractReminderCta)
                    .getOrNull())
            .isNotNull()
        assertThat(
                onboardingReminderRepository
                    .findByContractIdAndTemplateName(
                        contract.id, NotificationType.OpsMemberSignContractReminderCta)
                    .get()
                    .count)
            .isEqualTo(2)
    }

    @Test
    fun `should fetch reminder when present`() {
        val contract = Contract.newBuilder().setId(123L).setCompanyId(456).setMemberId(2).build()

        onboardingReminderRepository.save(
            JpaOnboardingReminder(
                count = 1,
                contractId = contract.id,
                templateName = NotificationType.OpsMemberSignContractReminderCta))

        val reminder =
            underTest.findByContractIdAndTemplateName(
                contract.id, NotificationType.OpsMemberSignContractReminderCta)

        assertThat(reminder.get()).isNotNull()
        if (reminder.isPresent) {
            assertThat(reminder.get().templateName.toString())
                .isEqualTo(NotificationType.OpsMemberSignContractReminderCta.toString())
            assertThat(reminder.get().contractId).isEqualTo(contract.id)
            assertThat(reminder.get().count).isEqualTo(1)
        }
    }

    @Test
    fun `should return null when reminder not present`() {
        val contract = Contract.newBuilder().setId(123L).setCompanyId(456).setMemberId(2).build()

        val reminder =
            underTest.findByContractIdAndTemplateName(
                contract.id, NotificationType.OpsMemberSignContractReminderCta)

        assertThat(reminder.isPresent).isFalse()
    }
}
