package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.adapter.CountryAndState
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.service.helpers.CompanyTestDataFactory.createCompany
import com.multiplier.contract.onboarding.service.helpers.ContractTestDataFactory.createContract
import com.multiplier.contract.onboarding.service.helpers.JpaOnboardingTestDataFactory.createJpaOnboarding
import com.multiplier.contract.onboarding.service.helpers.MemberTestDataFactory.createMember
import com.multiplier.contract.onboarding.service.helpers.OperationsUserTestDataFactory.createOperationsUser
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class USAOpsMemberUpdatePayrollFormsReminderCtaProcessorTest {

    private val underTest = USAOpsMemberUpdatePayrollFormsReminderCtaProcessor()

    @Test
    fun should_return_correct_notification_type() {
        val result = underTest.notificationType()
        assertThat(result).isEqualTo(NotificationType.USAOpsMemberUpdatePayrollFormsReminderCta)
    }

    @Test
    fun should_validate_true_for_usa_employee_contract_with_required_payroll_forms_not_submitted() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.EMPLOYEE,
                country = CountryCode.USA,
                payrollFormRequirements =
                    mapOf(CountryAndState(CountryCode.USA) to setOf("W4", "I9")),
                submittedLegalDocuments = setOf("W4") // Missing I9
                )

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    @Test
    fun should_validate_false_for_usa_employee_contract_with_all_payroll_forms_submitted() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.EMPLOYEE,
                country = CountryCode.USA,
                payrollFormRequirements =
                    mapOf(CountryAndState(CountryCode.USA) to setOf("W4", "I9")),
                submittedLegalDocuments = setOf("W4", "I9") // All submitted
                )

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_false_for_usa_employee_contract_with_no_payroll_form_requirements() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.EMPLOYEE,
                country = CountryCode.USA,
                payrollFormRequirements = emptyMap())

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_false_for_non_usa_contract() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.EMPLOYEE,
                country = CountryCode.GBR,
                payrollFormRequirements = mapOf(CountryAndState(CountryCode.GBR) to setOf("P45")),
                submittedLegalDocuments = emptySet())

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_false_for_contractor_contract() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.CONTRACTOR,
                country = CountryCode.USA,
                payrollFormRequirements =
                    mapOf(CountryAndState(CountryCode.USA) to setOf("W4", "I9")),
                submittedLegalDocuments = emptySet())

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_false_when_no_operations_user() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.EMPLOYEE,
                country = CountryCode.USA,
                hasOperationsUser = false,
                payrollFormRequirements =
                    mapOf(CountryAndState(CountryCode.USA) to setOf("W4", "I9")),
                submittedLegalDocuments = emptySet())

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_true_when_some_payroll_forms_missing() {
        val emailTemplateData =
            createEmailTemplateData(
                contractType = ContractOuterClass.ContractType.EMPLOYEE,
                country = CountryCode.USA,
                payrollFormRequirements =
                    mapOf(CountryAndState(CountryCode.USA) to setOf("W4", "I9", "StateForm")),
                submittedLegalDocuments = setOf("W4") // Missing I9 and StateForm
                )

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    @Test
    fun should_return_only_cutoff_param() {
        val emailTemplateData = createEmailTemplateData()
        val result = underTest.editableParams(emailTemplateData)

        assertThat(result).hasSize(1)
        assertThat(result[0]).isInstanceOf(CutoffParam::class.java)
        assertThat(result[0].key).isEqualTo(EditableParamConstants.CUTOFF_DATE_KEY)
    }

    @Test
    fun should_validate_false_when_multiFrequencySupportEnabled_is_false() {
        val emailTemplateData =
            createEmailTemplateData(
                payrollFormRequirements = mapOf(CountryAndState(CountryCode.USA) to setOf("W4")),
                submittedLegalDocuments = emptySet(), // Missing W4
                multiFrequencySupportEnabled = false)

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_false_when_multiFrequencySupportEnabled_is_true_but_country_is_not_USA() {
        val emailTemplateData =
            createEmailTemplateData(
                country = CountryCode.SGP,
                payrollFormRequirements = mapOf(CountryAndState(CountryCode.SGP) to setOf("Form1")),
                submittedLegalDocuments = emptySet(), // Missing Form1
                multiFrequencySupportEnabled = true)

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun should_validate_true_when_multiFrequencySupportEnabled_is_true_and_country_is_USA() {
        val emailTemplateData =
            createEmailTemplateData(
                country = CountryCode.USA,
                payrollFormRequirements = mapOf(CountryAndState(CountryCode.USA) to setOf("W4")),
                submittedLegalDocuments = emptySet(), // Missing W4
                multiFrequencySupportEnabled = true)

        val result = underTest.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    private fun createEmailTemplateData(
        contractType: ContractOuterClass.ContractType = ContractOuterClass.ContractType.EMPLOYEE,
        country: CountryCode = CountryCode.USA,
        hasOperationsUser: Boolean = true,
        payrollFormRequirements: Map<CountryAndState, Set<String>> = emptyMap(),
        submittedLegalDocuments: Set<String> = emptySet(),
        multiFrequencySupportEnabled: Boolean = true
    ): EmailTemplateData {
        val contract = createContract(type = contractType, country = country)
        val company = createCompany()
        val member = createMember()
        val operationsUser = if (hasOperationsUser) createOperationsUser() else null
        val onboardingCompany =
            createJpaOnboarding(
                contractId = contract.id,
                experience = "company",
                status = ContractOnboardingStatus.MEMBER_INVITED)

        return EmailTemplateData(
            company = company,
            member = member,
            operationsUser = operationsUser,
            onboardingCompany = onboardingCompany,
            onboardingMember = null,
            contract = contract,
            submittedLegalDocument = submittedLegalDocuments,
            payrollFormRequirements = payrollFormRequirements,
            guideLink = "https://guide.link",
            preregistrationRequiredCountry = false,
            payFrequency = "MONTHLY",
            multiFrequencySupportEnabled = multiFrequencySupportEnabled)
    }
}
