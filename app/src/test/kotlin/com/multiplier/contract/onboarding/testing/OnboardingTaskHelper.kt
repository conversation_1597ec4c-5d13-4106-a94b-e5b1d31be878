package com.multiplier.contract.onboarding.testing

import com.multiplier.company.schema.grpc.CompanyOuterClass.CompanyUser
import com.multiplier.company.schema.grpc.CompanyOuterClass.CompanyUserCapability
import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.core.schema.common.Common
import com.multiplier.core.schema.common.Common.Persona
import java.time.LocalDate
import java.time.YearMonth

class OnboardingTaskHelper {
    companion object {
        fun stubOnboardingTaskDTO(): OnboardingTask {
            return OnboardingTask(
                name = OnboardingTaskName.SIGN_CONTRACT,
                completed = true,
                pendingOn =
                    Person(
                        id = 101L,
                        firstName = "first",
                        lastName = "last",
                        userId = "user_id",
                        persona = Person.Persona.MEMBER))
        }

        fun buildCompanyUser(
            id: Long,
            companyId: Long,
            namePrefix: String,
            capability: Set<CompanyUserCapability>,
            persona: Persona? = Persona.COMPANY
        ): CompanyUser {
            return CompanyUser.newBuilder()
                .setId(id)
                .setUserId("1${id}")
                .setCompanyId(companyId)
                .setPersona(persona)
                .setFirstName("${namePrefix}_first_name")
                .setLastName("${namePrefix}_last_name")
                .addAllEmails(
                    listOf(
                        Common.EmailAddress.newBuilder()
                            .setEmail("${namePrefix}@email")
                            .setType("primary")
                            .build()))
                .addAllPhoneNos(
                    listOf(
                        Common.PhoneNumber.newBuilder()
                            .setPhoneNo("${namePrefix}_phone_no")
                            .setType("primary")
                            .build()))
                .setIsSignatory(capability.contains(CompanyUserCapability.SIGNATORY))
                .setIsBillingContact(capability.contains(CompanyUserCapability.BILLING_CONTACT))
                .addAllCompanyUserCapability(capability)
                .build()
        }

        fun buildMember(
            id: Long,
            userId: Long,
            namePrefix: String,
        ): Member {
            return Member(
                id = id,
                userId = "$userId",
                persona =
                    com.multiplier.contract.onboarding.domain.model.Persona(Persona.MEMBER.name),
                firstName = "${namePrefix}_first_name",
                lastName = "${namePrefix}_last_name",
                fullName = "${namePrefix}_full_name",
                email = "${namePrefix}@email",
            )
        }

        fun buildPayrollCycle(
            payFrequency: PayrollCycle.PayFrequency = PayrollCycle.PayFrequency.MONTHLY,
            payDay: PayrollCycle.PayDay = PayrollCycle.PayDay.Monthly,
            payrollMonth: YearMonth = YearMonth.of(2023, 2),
            payDate: LocalDate = LocalDate.of(2023, 2, 25),
            cutoffDate: LocalDate = LocalDate.of(2023, 2, 10),
            contractStartFrom: LocalDate = LocalDate.of(2023, 1, 21),
            contractStartTo: LocalDate = LocalDate.of(2023, 2, 20)
        ): PayrollCycle {
            return PayrollCycle(
                payFrequency = payFrequency,
                payDay = payDay,
                payrollMonth = payrollMonth,
                payDate = payDate,
                cutoffDate = cutoffDate,
                contractStartFrom = contractStartFrom,
                contractStartTo = contractStartTo)
        }

        fun buildPayrollCycleDTO(
            payFrequency: PayrollCycleDTO.PayFrequency = PayrollCycleDTO.PayFrequency.MONTHLY,
            payrollMonth: YearMonth = YearMonth.of(2023, 2),
            cutoffDate: LocalDate = LocalDate.of(2023, 2, 10),
            contractStartFrom: LocalDate = LocalDate.of(2023, 1, 21),
            contractStartTo: LocalDate = LocalDate.of(2023, 2, 20)
        ): PayrollCycleDTO {
            return PayrollCycleDTO(
                payFrequency = payFrequency,
                payrollMonth = payrollMonth,
                cutoffDate = cutoffDate,
                startDate = contractStartFrom,
                endDate = contractStartTo)
        }
    }
}
