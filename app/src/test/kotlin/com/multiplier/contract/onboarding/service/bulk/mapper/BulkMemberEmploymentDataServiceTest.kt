package com.multiplier.contract.onboarding.service.bulk.mapper

import com.google.type.Date
import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberEmploymentServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.EmployerDetail
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerEndDateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerPositionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerStartDateSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.MemberPreviousEmployerDetail
import com.multiplier.member.schema.MemberPreviousEmployerDetails
import com.multiplier.member.schema.UpsertPreviousEmployerDetailsInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import java.time.LocalDate
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberEmploymentDataServiceTest {

    @MockK private lateinit var memberServiceAdapterMock: MemberServiceAdapter

    @MockK
    private lateinit var bulkMemberEmploymentServiceAdapterMock: BulkMemberEmploymentServiceAdapter

    @InjectMockKs
    private lateinit var bulkMemberEmploymentDataService: BulkMemberEmploymentDataService

    @Test
    fun `should return validation result when validate employment data`() {
        val employeeData =
            listOf(
                EmployeeData(
                    EmployeeIdentification(rowNumber = 2), mapOf("lastEmployerName" to "Google")))
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                .build()

        val expected =
            listOf(
                GrpcValidationResult(
                    success = true,
                    errors = emptyList(),
                    validationId = "2",
                    input =
                        UpsertPreviousEmployerDetailsInput.newBuilder().setRequestId("2").build()))
        every {
            bulkMemberEmploymentServiceAdapterMock.validateUpsertPreviousEmployerDetails(
                employeeData, options)
        } returns expected
        assertEquals(expected, bulkMemberEmploymentDataService.validate(employeeData, options))
    }

    @Test
    fun `should return correct messages when upsert previous employer details`() {
        val inputs =
            listOf(
                ValidInput(
                    validationId = "1",
                    input =
                        UpsertPreviousEmployerDetailsInput.newBuilder()
                            .setRequestId("1")
                            .setPreviousEmployerDetails(
                                MemberPreviousEmployerDetails.newBuilder()
                                    .setMemberId(1)
                                    .addAllPreviousEmployerDetails(
                                        listOf(
                                            MemberPreviousEmployerDetail.newBuilder()
                                                .setEmployerName("Google")
                                                .setDesignation("Senior Software Engineer")
                                                .setStartDate(
                                                    Date.newBuilder()
                                                        .setYear(2020)
                                                        .setMonth(1)
                                                        .setDay(1)
                                                        .build())
                                                .setEndDate(
                                                    Date.newBuilder()
                                                        .setYear(2023)
                                                        .setMonth(12)
                                                        .setDay(31)
                                                        .build())
                                                .build()))
                                    .build())
                            .build()))

        val messages = listOf("Not good")
        every {
            bulkMemberEmploymentServiceAdapterMock.upsertPreviousEmployerDetails(any())
        } returns listOf(CreationResult.error(requestId = "1", errors = messages))

        val memberContext = MemberContext(mapOf("1" to 1L))
        assertEquals(
            messages,
            bulkMemberEmploymentDataService.upsertPreviousEmployerDetails(
                inputs, memberContext, BulkOnboardingOptions()))
    }

    @Test
    fun `should return correct member data context when get data for specs`() {
        val employerDetails =
            listOf(
                EmployerDetail(
                    memberId = 1,
                    lastEmployerName = "Google",
                    lastEmployerPosition = "Senior Software Engineer",
                    lastEmployerStartDate = LocalDate.of(2020, 1, 1),
                    lastEmployerEndDate = LocalDate.of(2023, 12, 31),
                ))
        every { memberServiceAdapterMock.getMemberLastEmployerDetails(any()) } returns
            employerDetails

        val expected =
            MemberDataContext(
                mapOf(
                    1L to
                        EmployeeDataChunk(
                            mapOf(
                                LastEmployerNameSpec.key to "Google",
                                LastEmployerPositionSpec.key to "Senior Software Engineer",
                                LastEmployerStartDateSpec.key to "2020-01-01",
                                LastEmployerEndDateSpec.key to "2023-12-31"))))
        val dataSpecs =
            listOf(
                LastEmployerNameSpec,
                LastEmployerPositionSpec,
                LastEmployerStartDateSpec,
                LastEmployerEndDateSpec)
        assertEquals(
            expected, bulkMemberEmploymentDataService.getDataForSpecs(dataSpecs, emptySet()))
    }
}
