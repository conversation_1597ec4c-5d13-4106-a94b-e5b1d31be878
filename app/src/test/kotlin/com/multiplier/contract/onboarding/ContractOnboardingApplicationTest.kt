package com.multiplier.contract.onboarding

import com.multiplier.contract.onboarding.cache.CountryCache
import com.ninjasquad.springmockk.MockkBean
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.http.HttpStatus
import org.springframework.test.context.ActiveProfiles
import org.springframework.web.client.RestTemplate

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("context-test")
class ContractOnboardingApplicationTest {

    @MockkBean private lateinit var countryCache: CountryCache

    @LocalServerPort private var port: Int = 0

    @Test
    fun `should load the app and health check returns ok`() {
        val entity =
            RestTemplate()
                .getForEntity("http://localhost:$port/actuator/health", String::class.java)
        assertEquals(HttpStatus.OK, entity.statusCode)
    }
}
