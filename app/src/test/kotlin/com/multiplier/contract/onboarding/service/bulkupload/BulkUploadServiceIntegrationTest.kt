package com.multiplier.contract.onboarding.service.bulkupload

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.company.schema.grpc.CompanyOfferingOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.model.Company
import com.multiplier.contract.onboarding.domain.model.Department
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.AOR_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.BASIC_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.COMPENSATION_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.COMPLIANCE_AND_LEAVES_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.CONTRACTOR_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.EMPLOYMENT_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.EOR_COMPENSATION_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.MEMBER_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.country.CountryState
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmployeeIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.MemberIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.FirstNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.NationalitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberLegalDataService.Companion.LEGAL_SPEC_DATA
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService.Companion.DepartmentSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.ContractDataContext
import com.multiplier.contract.onboarding.service.bulk.v2.*
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule.Companion.STATIC_BANK_DETAILS_SPEC
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule.Companion.StaticBankDataSpec.BankNameSpec
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberLegalDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkOrgManagementModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.DataForContract
import com.multiplier.contract.onboarding.service.bulkupload.BulkUploadService.Companion.AOR_MEMBER_ONBOARDING
import com.multiplier.contract.onboarding.service.bulkupload.BulkUploadService.Companion.EOR_MEMBER_ONBOARDING
import com.multiplier.contract.onboarding.service.bulkupload.BulkUploadService.Companion.FREELANCER_MEMBER_ONBOARDING
import com.multiplier.contract.onboarding.service.bulkupload.BulkUploadService.Companion.GLOBAL_PAYROLL_MEMBER_ONBOARDING
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.schema.contract.BulkContract.UpdateComplianceInput
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.ComplianceParam
import com.multiplier.contract.schema.contract.ContractOuterClass.CreateContractInput
import com.multiplier.country.schema.Country
import com.multiplier.country.schema.Country.GrpcComplianceParamDefinition
import com.multiplier.country.schema.Country.GrpcComplianceRequirementDefinition
import com.multiplier.member.schema.MemberCreateInput
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.core.task.TaskExecutor
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("context-test")
class BulkUploadServiceIntegrationTest {

    @MockkBean private lateinit var memberLegalDataModule: BulkMemberLegalDataModule
    @MockkBean private lateinit var memberBankDataModule: BulkMemberBankDataModule
    @MockkBean private lateinit var compensationModule: BulkCompensationModuleV2
    @MockkBean private lateinit var orgManagementModule: BulkOrgManagementModule
    @MockkBean private lateinit var featureFlagService: FeatureFlagService
    @MockkBean private lateinit var departmentServiceAdapter: DepartmentServiceAdapter
    @MockkBean private lateinit var companyServiceAdapter: CompanyServiceAdapter
    @MockkBean private lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockkBean private lateinit var memberServiceAdapter: MemberServiceAdapter
    @MockkBean private lateinit var countryServiceAdapter: CountryServiceAdapter
    @MockkBean private lateinit var companyCache: CountryCache
    @MockkBean
    private lateinit var compensationSourceServiceAdapter: CompensationSourceServiceAdapter
    @Autowired private lateinit var underTest: BulkUploadService

    @TestConfiguration
    class TestConfig {
        @Bean
        @Primary
        fun testTaskExecutor(): TaskExecutor {
            return mockk<TaskExecutor>(relaxed = true)
        }
    }

    @BeforeEach
    fun setUp() {
        every { compensationModule.getDataSpecs(any(), any()) } returns
            listOf(
                    DataSpec(key = "EMPLOYEE_FULL_NAME"),
                    DataSpec(key = "COMPONENT_NAME", type = DataSpecType.SELECT),
                    DataSpec(key = "CURRENCY", type = DataSpecType.SELECT),
                    DataSpec(key = "BILLING_RATE_TYPE", type = DataSpecType.SELECT),
                    DataSpec(key = "BILLING_RATE", type = DataSpecType.NUMBER),
                )
                .map { it.copy(source = BulkCompensationModuleV2.COMPENSATION_SCHEMA_DATA_SPEC) }
        every { memberLegalDataModule.getDataSpecs(any(), any()) } returns
            listOf(
                DataSpec(key = "phoneNumber", source = LEGAL_SPEC_DATA),
                DataSpec(key = "nationality", source = LEGAL_SPEC_DATA),
                DataSpec(key = "passportNumber", source = LEGAL_SPEC_DATA),
            )
        every { memberBankDataModule.getDataSpecs(any(), any()) } returns
            listOf(
                DataSpec(
                    key = "accountHolderName", prefix = "bank", source = STATIC_BANK_DETAILS_SPEC),
                DataSpec(key = "bankName", prefix = "bank", source = STATIC_BANK_DETAILS_SPEC),
                DataSpec(key = "accountNumber", prefix = "bank", source = STATIC_BANK_DETAILS_SPEC),
            )

        every { companyCache.getCountryName(any()) } returns "India"
        every { companyCache.getCountryCode(any()) } returns CountryCode.IND
        every { orgManagementModule.getDataSpecs(any(), any()) } returns
            listOf(DataSpec(key = "department", type = DataSpecType.TEXT))
        every { departmentServiceAdapter.getDepartments(any()) } returns
            listOf(Department(id = 1, companyId = 1, name = "Department 1"))
        every { featureFlagService.isFeatureOn(any(), any()) } returns true

        every { memberLegalDataModule.identifier() } returns BulkMemberLegalDataModule.MODULE_NAME

        every { memberBankDataModule.identifier() } returns BulkMemberBankDataModule.MODULE_NAME

        every { orgManagementModule.identifier() } returns BulkOrgManagementModule.MODULE_NAME

        every { compensationModule.identifier() } returns BulkCompensationModuleV2.MODULE_NAME
        every { compensationModule.getDataForSpecs(any(), any(), any()) } returns
            DataForContract.EMPTY

        every { contractServiceAdapter.getContractsBy(any()) } returns emptyList()

        every { companyServiceAdapter.getCompanyLegalEntities(any()) } returns emptyList()

        every {
            memberServiceAdapter.getMembersWithAddressesAndBankAccountsAndLegalData(any())
        } returns emptyList()

        every { companyServiceAdapter.getLegalEntity(any()) } returns
            LegalEntity.newBuilder()
                .setId(1)
                .setCompanyId(1)
                .addCapabilities(Capability.GROSS_TO_NET.name)
                .setAddress(CompanyOuterClass.Address.newBuilder().setCountry("USA").build())
                .build()
        every { companyServiceAdapter.getCompanyOfferings(any()) } returns
            mutableListOf(
                CompanyOfferingOuterClass.CompanyOffering.newBuilder()
                    .setOfferingCode(OfferingCode.GLOBAL_PAYROLL.name)
                    .build())
        every { companyServiceAdapter.getCompany(any()) } returns
            mockk<Company> {
                every { id } returns 1L
                every { displayName } returns "Company name"
                every { logoId } returns 301L
                every { countryFullName } returns "Singapore"
            }
        every {
            compensationSourceServiceAdapter.isNewCompensationSourceEnabledForEntityId(any())
        } returns true
        every {
            compensationSourceServiceAdapter.isNewCompensationSourceEnabledForCountries(
                any(), any())
        } returns mapOf(CountryCode.IND to true)

        every { countryServiceAdapter.getCountryStates(any()) } returns
            mapOf(CountryCode.IND to listOf(CountryState(CountryCode.IND, "KA", "Karnataka")))

        every {
            countryServiceAdapter.getComplianceDefinitionForCountry(any(), ContractType.EMPLOYEE)
        } returns
            GrpcComplianceRequirementDefinition.newBuilder()
                .addParamDefinitions(
                    GrpcComplianceParamDefinition.newBuilder()
                        .setParam(
                            Country.GrpcComplianceParam.newBuilder()
                                .setComplianceParamPeriodLimit(
                                    Country.GrpcComplianceParam.GrpcComplianceParamPeriodLimit
                                        .newBuilder()
                                        .setKey("probationPolicy")
                                        .setUnit(Country.GrpcComplianceParamPeriodUnit.MONTHS)
                                        .build())
                                .build())
                        .setEnabled(true)
                        .setRequired(false)
                        .build())
                .build()

        val legalEntityId = 12345L
        every { companyServiceAdapter.getCompanyPrimaryLegalEntity(any()) } returns
            LegalEntity.newBuilder().setId(legalEntityId).build()
    }

    @Test
    fun `getFieldRequirements should return a list of field requirements`() {
        // given
        val request =
            FieldRequirementsRequest(
                useCase = "GLOBAL_PAYROLL_MEMBER_ONBOARDING",
                entityId = 1L,
                companyId = 1L,
                countryCode = "USA")

        // when
        val fieldRequirements = underTest.getFieldRequirements(request)

        // then
        val expectedFieldRequirements =
            contractDataFields + memberDataFields + compensationDataFields
        assertThat(fieldRequirements).containsExactlyInAnyOrderElementsOf(expectedFieldRequirements)
    }

    @Test
    fun `getFieldRequirements should return a list of field requirements for freelancer`() {
        // given
        val request =
            FieldRequirementsRequest(
                useCase = "FREELANCER_MEMBER_ONBOARDING",
                entityId = null,
                companyId = 1L,
                countryCode = null)

        // when
        val fieldRequirements = underTest.getFieldRequirements(request)

        // then
        val expectedFieldRequirements = freelancerExpectedData
        assertThat(fieldRequirements).containsExactlyInAnyOrderElementsOf(expectedFieldRequirements)
    }

    @Test
    fun `getFieldRequirements should return a list of field requirements for aor`() {
        // given
        val request =
            FieldRequirementsRequest(
                useCase = "AOR_MEMBER_ONBOARDING",
                entityId = null,
                companyId = 1L,
                countryCode = null)

        // when
        val fieldRequirements = underTest.getFieldRequirements(request)

        // then
        val expectedFieldRequirements = aorExpectedData
        assertThat(fieldRequirements).containsExactlyInAnyOrderElementsOf(expectedFieldRequirements)
    }

    @Test
    fun `getFieldRequirements should return a list of field requirements for eor`() {
        // given
        val request =
            FieldRequirementsRequest(
                useCase = "EOR_MEMBER_ONBOARDING",
                entityId = null,
                companyId = 1L,
                countryCode = CountryCode.IND.name)

        // when
        val fieldRequirements = underTest.getFieldRequirements(request)

        // then
        val expectedFieldRequirements = eorExpectedData
        assertThat(fieldRequirements).containsExactlyInAnyOrderElementsOf(expectedFieldRequirements)
    }

    @Test
    fun `getFieldRequirements should return empty list when feature flag is disabled`() {
        // given
        val request =
            FieldRequirementsRequest(
                useCase = "EOR_MEMBER_ONBOARDING",
                entityId = null,
                companyId = 1L,
                countryCode = CountryCode.IND.name)

        every { featureFlagService.isFeatureOn(FeatureFlags.ONBOARDING_BULK_EOR, any()) } returns
            false

        val fieldRequirements = underTest.getFieldRequirements(request)

        assertThat(fieldRequirements).isEmpty()
    }

    @Test
    fun `getFieldRequirements should fail for eor when country not passed in input`() {
        // given
        val request =
            FieldRequirementsRequest(
                useCase = "EOR_MEMBER_ONBOARDING",
                entityId = null,
                companyId = 1L,
                countryCode = null)

        // when
        val exception =
            assertThrows<MplBusinessException> { underTest.getFieldRequirements(request) }

        assertThat(exception.message).contains("Country code is required for EOR")
        assertThat(exception.errorCode).isEqualTo(ErrorCodes.VALIDATION_FAILED)
    }

    @Test
    fun `getFieldRequirements should fail for eor when country is not enabled for new compensation service`() {
        // given
        val request =
            FieldRequirementsRequest(
                useCase = "EOR_MEMBER_ONBOARDING",
                entityId = null,
                companyId = 1L,
                countryCode = CountryCode.USA.name)

        // when
        val exception =
            assertThrows<MplBusinessException> { underTest.getFieldRequirements(request) }

        assertThat(exception.message)
            .contains("New compensation service not enabled for country USA for type EMPLOYEE")
        assertThat(exception.errorCode).isEqualTo(ErrorCodes.UNSUPPORTED_OPERATION)
    }

    @Test
    fun `getFieldRequirements should fail for eor when country is invalid`() {
        // given
        val request =
            FieldRequirementsRequest(
                useCase = "EOR_MEMBER_ONBOARDING",
                entityId = null,
                companyId = 1L,
                countryCode = "IN")

        // when
        val exception =
            assertThrows<MplBusinessException> { underTest.getFieldRequirements(request) }

        assertThat(exception.message).contains("Invalid country code: IN")
        assertThat(exception.errorCode).isEqualTo(ErrorCodes.VALIDATION_FAILED)
    }

    private val freelancerExpectedData =
        listOf(
            FieldRequirement(
                key = "country",
                type = FieldType.SELECT,
                description = "Contractor's tax residency",
                allowedValues = listOf("India"),
                group = CONTRACTOR_DETAILS_GROUP),
            FieldRequirement(
                key = "employeeId",
                label = "Contractor Id",
                description = "Unique identifier for a contractor",
                mandatory = false,
                group = CONTRACTOR_DETAILS_GROUP),
            FieldRequirement(
                key = "position",
                label = "Designation",
                description = "Job title of the contractor",
                group = CONTRACTOR_DETAILS_GROUP),
            FieldRequirement(
                key = "startOn",
                type = FieldType.DATE,
                label = "Contract Start Date",
                description = "Date of joining of the contractor in YYYY-MM-DD format",
                group = CONTRACTOR_DETAILS_GROUP),
            FieldRequirement(
                key = "endOn",
                type = FieldType.DATE,
                label = "Contract End Date",
                mandatory = false,
                description = "Contract end date for the contractor in YYYY-MM-DD format",
                group = CONTRACTOR_DETAILS_GROUP),
            FieldRequirement(
                key = "scope",
                label = "Job Description",
                description = "Job Description of the Contractor",
                mandatory = false,
                type = FieldType.TEXT,
                group = CONTRACTOR_DETAILS_GROUP),
            FieldRequirement(
                key = "firstName",
                description = "First name of the contractor",
                group = CONTRACTOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "lastName",
                description = "Last name of the contractor",
                group = CONTRACTOR_DETAILS_GROUP),
            FieldRequirement(
                key = "email",
                description = "Email address of the contractor",
                group = CONTRACTOR_DETAILS_GROUP),
            FieldRequirement(
                key = "gender",
                type = FieldType.SELECT,
                allowedValues = listOf("FEMALE", "MALE", "OTHER", "ALL"),
                description = "Gender of the contractor",
                group = CONTRACTOR_DETAILS_GROUP),
            FieldRequirement(
                key = "EMPLOYEE_FULL_NAME",
                group = CONTRACTOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "COMPONENT_NAME",
                type = FieldType.SELECT,
                group = CONTRACTOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "CURRENCY",
                type = FieldType.SELECT,
                group = CONTRACTOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "BILLING_RATE_TYPE",
                type = FieldType.SELECT,
                group = CONTRACTOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "BILLING_RATE",
                type = FieldType.NUMBER,
                group = CONTRACTOR_DETAILS_GROUP,
            ),
        )

    private val aorExpectedData =
        listOf(
            FieldRequirement(
                key = "country",
                type = FieldType.SELECT,
                allowedValues = listOf("India"),
                description = "Contractor's tax residency",
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "employeeId",
                label = "AOR Contractor Id",
                description = "Unique identifier for a contractor",
                mandatory = false,
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "position",
                label = "Designation",
                description = "Job title of the contractor",
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "startOn",
                type = FieldType.DATE,
                label = "Contract Start Date",
                description = "Date of joining of the contractor in YYYY-MM-DD format",
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "endOn",
                type = FieldType.DATE,
                label = "Contract End Date",
                description = "Contract end date for the contractor in YYYY-MM-DD format",
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "scope",
                label = "Job Description",
                description = "Job Description of the Contractor",
                type = FieldType.TEXT,
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "firstName",
                description = "First name of the contractor",
                group = AOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "lastName",
                description = "Last name of the contractor",
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "email",
                description = "Email address of the contractor",
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "gender",
                type = FieldType.SELECT,
                allowedValues = listOf("FEMALE", "MALE", "OTHER", "ALL"),
                description = "Gender of the contractor",
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "noticeAfterProbation.value",
                type = FieldType.NUMBER,
                description = "Notice period for the contractor after probation",
                mandatory = false,
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "noticeAfterProbation.unit",
                type = FieldType.SELECT,
                description = "Unit of Notice period",
                mandatory = false,
                allowedValues = listOf("DAYS"),
                group = AOR_DETAILS_GROUP),
            FieldRequirement(
                key = "EMPLOYEE_FULL_NAME",
                group = AOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "COMPONENT_NAME",
                type = FieldType.SELECT,
                group = AOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "CURRENCY",
                type = FieldType.SELECT,
                group = AOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "BILLING_RATE_TYPE",
                type = FieldType.SELECT,
                group = AOR_DETAILS_GROUP,
            ),
            FieldRequirement(
                key = "BILLING_RATE",
                type = FieldType.NUMBER,
                group = AOR_DETAILS_GROUP,
            ),
        )

    private val eorExpectedData =
        listOf(
            FieldRequirement(
                key = "rowIdentifier",
                label = "Row Identifier",
                type = FieldType.TEXT,
                description = "Unique row identifier for employee data across upload sheets",
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "state",
                label = "Country State of Employment",
                type = FieldType.SELECT,
                allowedValues = listOf("KA"),
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "workStatus",
                label = "Work Status",
                type = FieldType.SELECT,
                allowedValues = listOf("RESIDENT", "REQUIRES_VISA"),
                mandatory = false,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "position",
                label = "Job Title",
                type = FieldType.TEXT,
                description = "Job title of the employee",
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "scope",
                label = "Job Description",
                type = FieldType.TEXT,
                mandatory = false,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "startOn",
                label = "Start On",
                type = FieldType.DATE,
                description = "Date of joining of the employee in YYYY-MM-DD format",
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "endOn",
                label = "End On",
                type = FieldType.DATE,
                mandatory = false,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "firstName",
                label = "First Name",
                type = FieldType.TEXT,
                description = "First name of the employee",
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "lastName",
                label = "Last Name",
                type = FieldType.TEXT,
                description = "Last name of the employee",
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "email",
                label = "Email",
                type = FieldType.TEXT,
                description = "Email address of the employee",
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "gender",
                label = "Gender",
                type = FieldType.SELECT,
                description = "Gender of the employee",
                allowedValues = listOf("FEMALE", "MALE", "OTHER", "ALL"),
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "phoneNumber",
                label = "Phone Number",
                type = FieldType.TEXT,
                description = "Phone number of the employee",
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "nationality",
                label = "Nationality",
                type = FieldType.TEXT,
                description = "Nationality of the employee",
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "passportNumber",
                label = "Passport Number",
                type = FieldType.TEXT,
                description = "Passport number of the employee",
                mandatory = true,
                group = BASIC_DETAILS_GROUP),
            FieldRequirement(
                key = "rowIdentifier",
                label = "Row Identifier",
                type = FieldType.TEXT,
                description = "Unique row identifier for employee data across upload sheets",
                mandatory = true,
                group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
            FieldRequirement(
                key = "probationPolicy.value",
                label = "Probation Policy Value",
                type = FieldType.NUMBER,
                mandatory = false,
                group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
            FieldRequirement(
                key = "probationPolicy.unit",
                label = "Probation Policy Unit",
                type = FieldType.SELECT,
                allowedValues = listOf("MONTHS"),
                mandatory = false,
                group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
            FieldRequirement(
                key = "EMPLOYEE_FULL_NAME",
                label = "Employee Full Name",
                type = FieldType.TEXT,
                mandatory = true,
                group = COMPENSATION_DATA_GROUP),
            FieldRequirement(
                key = "COMPONENT_NAME",
                label = "Component Name",
                type = FieldType.SELECT,
                mandatory = true,
                group = COMPENSATION_DATA_GROUP),
            FieldRequirement(
                key = "CURRENCY",
                label = "Currency",
                type = FieldType.SELECT,
                mandatory = true,
                group = COMPENSATION_DATA_GROUP),
            FieldRequirement(
                key = "BILLING_RATE_TYPE",
                label = "Billing Rate Type",
                type = FieldType.SELECT,
                mandatory = true,
                group = COMPENSATION_DATA_GROUP),
            FieldRequirement(
                key = "BILLING_RATE",
                label = "Billing Rate",
                type = FieldType.NUMBER,
                mandatory = true,
                group = COMPENSATION_DATA_GROUP))

    private val contractDataFields =
        listOf(
            FieldRequirement(
                key = "employeeId",
                description = "Unique identifier for an employee",
                group = "EMPLOYMENT_DATA",
            ),
            FieldRequirement(
                key = "firstName",
                group = "EMPLOYMENT_DATA",
                description = "First name of the employee",
            ),
            FieldRequirement(
                key = "lastName",
                group = "EMPLOYMENT_DATA",
                description = "Last name of the employee",
            ),
            FieldRequirement(
                key = "email",
                group = "EMPLOYMENT_DATA",
                description = "Email address of the employee"),
            FieldRequirement(
                key = "gender",
                type = FieldType.SELECT,
                allowedValues = listOf("FEMALE", "MALE", "OTHER", "ALL"),
                group = "EMPLOYMENT_DATA",
                description = "Gender of the employee"),
            FieldRequirement(key = "department", type = FieldType.TEXT, group = "EMPLOYMENT_DATA"),
            FieldRequirement(
                key = "position",
                label = "Designation",
                group = "EMPLOYMENT_DATA",
                description = "Job title of the employee"),
            FieldRequirement(
                key = "startOn",
                type = FieldType.DATE,
                group = "EMPLOYMENT_DATA",
                description = "Date of joining of the employee in YYYY-MM-DD format"),
        )

    private val memberDataFields =
        listOf(
            FieldRequirement(
                key = "phoneNumber",
                group = "MEMBER_DATA",
                description = "Phone number of the employee"),
            FieldRequirement(
                key = "nationality",
                group = "MEMBER_DATA",
                description = "Nationality of the employee"),
            FieldRequirement(
                key = "passportNumber",
                group = "MEMBER_DATA",
                description = "Passport number of the employee"),
            FieldRequirement(
                key = "bank.accountHolderName",
                label = "Account Holder Name",
                group = "MEMBER_DATA",
                description = "Name on employee's bank account for salary payment"),
            FieldRequirement(
                key = "bank.bankName",
                label = "Bank Name",
                group = "MEMBER_DATA",
                description = "Name of employee's bank for salary payment"),
            FieldRequirement(
                key = "bank.accountNumber",
                label = "Account Number",
                group = "MEMBER_DATA",
                description = "Employee's bank account number for salary payment"),
        )

    private val compensationDataFields =
        listOf(
            FieldRequirement(
                key = "EMPLOYEE_FULL_NAME",
                group = "COMPENSATION_DATA",
            ),
            FieldRequirement(
                key = "COMPONENT_NAME",
                type = FieldType.SELECT,
                group = "COMPENSATION_DATA",
            ),
            FieldRequirement(
                key = "CURRENCY",
                type = FieldType.SELECT,
                group = "COMPENSATION_DATA",
            ),
            FieldRequirement(
                key = "BILLING_RATE_TYPE",
                type = FieldType.SELECT,
                group = "COMPENSATION_DATA",
            ),
            FieldRequirement(
                key = "BILLING_RATE",
                type = FieldType.NUMBER,
                group = "COMPENSATION_DATA",
            ),
        )

    @Nested
    inner class GetFieldData {

        @Test
        fun `should return row data list`() {
            // Given
            val contractId = 3L
            val memberId = 4L
            val usa = "USA"
            val employeeId = "ABC123"
            every { memberLegalDataModule.getContractDataForSpecs(any(), any(), any()) } returns
                ContractDataContext(
                    contractIdToContractData =
                        mapOf(
                            contractId to
                                buildEmployeeDataChunk(
                                    contractId, mapOf(MemberIdSpec.key to memberId.toString()))))
            every { memberLegalDataModule.getDataForSpecs(any(), any(), any()) } returns
                buildDataForContract(
                    contractId,
                    mapOf(
                        NationalitySpec.key to usa,
                        MemberIdSpec.key to memberId.toString(),
                        ContractIdSpec.key to contractId.toString(),
                        EmployeeIdSpec.key to employeeId,
                        FirstNameSpec.key to "John",
                        LastNameSpec.key to "Doe"))

            val bankName = "Bank of Singapore"
            every { memberBankDataModule.getDataForSpecs(any(), any(), any()) } returns
                buildDataForContract(
                    contractId,
                    mapOf(
                        BankNameSpec.keyWithPrefix() to bankName,
                        EmployeeIdSpec.key to employeeId,
                        ContractIdSpec.key to contractId.toString()))

            val basicSalary = "Basic Salary"
            val bonus = "Bonus"
            every { compensationModule.getFieldData(any(), any()) } returns
                listOf(
                    EmployeeDataChunk(
                        mapOf(
                            COMPONENT_NAME to basicSalary,
                            ContractIdSpec.key to contractId.toString(),
                        )),
                    EmployeeDataChunk(
                        mapOf(
                            COMPONENT_NAME to bonus,
                            ContractIdSpec.key to contractId.toString(),
                        )))

            every { orgManagementModule.getDataForSpecs(any(), any(), any()) } returns
                buildDataForContract(contractId, mapOf(DepartmentSpec.key to "Department"))

            val companyId = 1L
            val entityId = 2L
            val request =
                FieldDataRequest(
                    useCase = GLOBAL_PAYROLL_MEMBER_ONBOARDING,
                    listOf(entityId),
                    listOf(companyId),
                    listOf(usa),
                    pageRequest = PageRequest.newBuilder().pageSize(100).pageNumber(0).build())

            // Action
            val dataRows = underTest.getFieldData(request)
            val contractDataRow = dataRows.first { it.group == EMPLOYMENT_DATA_GROUP }
            val memberDataRow = dataRows.first { it.group == MEMBER_DATA_GROUP }
            val compensationDataRows = dataRows.filter { it.group == COMPENSATION_DATA_GROUP }

            // Assertions
            assertThat(dataRows.size).isEqualTo(4)

            assertThat(contractDataRow.key.contractId).isEqualTo(contractId)
            assertThat(contractDataRow.group).isEqualTo(EMPLOYMENT_DATA_GROUP)
            assertThat(contractDataRow.data[DepartmentSpec.key]).isEqualTo("Department")

            assertThat(memberDataRow.key.contractId).isEqualTo(contractId)
            assertThat(memberDataRow.group).isEqualTo(MEMBER_DATA_GROUP)
            assertThat(memberDataRow.data[NationalitySpec.key]).isEqualTo(usa)
            assertThat(memberDataRow.data[BankNameSpec.keyWithPrefix()]).isEqualTo(bankName)

            assertThat(compensationDataRows[0].key.contractId).isEqualTo(contractId)
            assertThat(compensationDataRows[0].group).isEqualTo(COMPENSATION_DATA_GROUP)
            assertThat(compensationDataRows[0].data[COMPONENT_NAME]).isEqualTo(basicSalary)
            assertThat(compensationDataRows[0].data[EMPLOYEE_FULL_NAME]).isEqualTo("John Doe")

            assertThat(compensationDataRows[1].key.contractId).isEqualTo(contractId)
            assertThat(compensationDataRows[1].group).isEqualTo(COMPENSATION_DATA_GROUP)
            assertThat(compensationDataRows[1].data[COMPONENT_NAME]).isEqualTo(bonus)
            assertThat(compensationDataRows[1].data[EMPLOYEE_FULL_NAME]).isEqualTo("John Doe")
        }

        @Test
        fun `should return empty list if use case is not supported`() {
            val request =
                FieldDataRequest(
                    useCase = "UNKNOWN",
                    entityIds = emptyList(),
                    companyIds = emptyList(),
                    countryCodes = emptyList(),
                    pageRequest = null)
            val result = underTest.getFieldData(request)
            assertThat(result).isEmpty()
        }
    }

    private fun buildDataForContract(contractId: Long, data: Map<String, String>) =
        DataForContract(
            mapOf(
                contractId to
                    EmployeeDataChunk(mapOf(ContractIdSpec.key to contractId.toString()) + data)))

    private fun buildEmployeeDataChunk(contractId: Long, data: Map<String, String>) =
        EmployeeDataChunk(mapOf(ContractIdSpec.key to contractId.toString()) + data)

    companion object {
        private const val COMPONENT_NAME = "COMPONENT_NAME"
        private const val EMPLOYEE_FULL_NAME = "EMPLOYEE_FULL_NAME"
    }

    @Nested
    inner class ValidateData {

        @Test
        fun `should validate data successfully for freelancer`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkValidationRequest(
                    useCase = FREELANCER_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            ValidationInput(
                                id = "2",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                    )),
                            ValidationInput(
                                id = "3",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                    ))))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            every { mockFreelancerBulkOnboardingUseCase.validate(any(), any()) } returns
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setEmployeeId("E001")
                                            .setType(ContractOuterClass.ContractType.FREELANCER)
                                            .build(),
                                    groupName = CONTRACTOR_DETAILS_GROUP),
                            ),
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        MemberCreateInput.newBuilder()
                                            .setFirstName("John")
                                            .setLastName("Doe")
                                            .build(),
                                    groupName = CONTRACTOR_DETAILS_GROUP))))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.validate(request)

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("2", "3"),
                        success = true,
                        validatedData =
                            mapOf(
                                "CONTRACT_MODULE->companyId" to "1",
                                "CONTRACT_MODULE->type" to "FREELANCER",
                                "CONTRACT_MODULE->employeeId" to "E001",
                                "CONTRACT_MODULE->dataClass" to
                                    getGrpcClassName(CreateContractInput.newBuilder().build()),
                                "MEMBER_MODULE->firstName" to "John",
                                "MEMBER_MODULE->lastName" to "Doe",
                                "MEMBER_MODULE->dataClass" to
                                    getGrpcClassName(MemberCreateInput.newBuilder().build()),
                                "companyId" to "1",
                                "contractType" to "FREELANCER",
                                "context" to "FREELANCER")))

            assertEquals(expected, actual)
        }

        @Test
        fun `should validate data when success false response is returned`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkValidationRequest(
                    useCase = FREELANCER_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            ValidationInput(
                                id = "2",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                    )),
                            ValidationInput(
                                id = "3",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                    ))))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            every { mockFreelancerBulkOnboardingUseCase.validate(any(), any()) } returns
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = false,
                                    errors = listOf("some error"),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setEmployeeId("E001")
                                            .setType(ContractOuterClass.ContractType.FREELANCER)
                                            .build(),
                                    groupName = CONTRACTOR_DETAILS_GROUP),
                            ),
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        MemberCreateInput.newBuilder()
                                            .setFirstName("John")
                                            .setLastName("Doe")
                                            .build(),
                                    groupName = CONTRACTOR_DETAILS_GROUP))))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.validate(request)

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("2"),
                        success = false,
                        errors = listOf(ValidationError("", "", listOf("some error"))),
                        group = CONTRACTOR_DETAILS_GROUP),
                    ValidationResult(
                        inputIds = listOf("3"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf(
                                        "Please resolve validation errors of the same employee in other modules"))),
                        group = CONTRACTOR_DETAILS_GROUP))

            assertEquals(expected, actual)
        }

        @Test
        fun `should validate data successfully for aor`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkValidationRequest(
                    useCase = AOR_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            ValidationInput(
                                id = "2",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                    )),
                            ValidationInput(
                                id = "3",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                    ))),
                    jsonCustomParams =
                        mapOf(
                            "complianceConsent.aor.workingPracticesMatchAorAgreement" to "true",
                            "complianceConsent.aor.workerCharacterizationSupported" to "true"))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            val employeeDataSlot = slot<EmployeeDataInput>()

            every {
                mockAorBulkOnboardingUseCase.validate(capture(employeeDataSlot), any())
            } returns
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setEmployeeId("E001")
                                            .setType(ContractOuterClass.ContractType.CONTRACTOR)
                                            .build(),
                                    groupName = AOR_DETAILS_GROUP),
                            ),
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        MemberCreateInput.newBuilder()
                                            .setFirstName("John")
                                            .setLastName("Doe")
                                            .build(),
                                    groupName = AOR_DETAILS_GROUP))))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.validate(request)

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("2", "3"),
                        success = true,
                        validatedData =
                            mapOf(
                                "CONTRACT_MODULE->companyId" to "1",
                                "CONTRACT_MODULE->type" to "CONTRACTOR",
                                "CONTRACT_MODULE->employeeId" to "E001",
                                "CONTRACT_MODULE->dataClass" to
                                    getGrpcClassName(CreateContractInput.newBuilder().build()),
                                "MEMBER_MODULE->firstName" to "John",
                                "MEMBER_MODULE->lastName" to "Doe",
                                "MEMBER_MODULE->dataClass" to
                                    getGrpcClassName(MemberCreateInput.newBuilder().build()),
                                "companyId" to "1",
                                "contractType" to "CONTRACTOR",
                                "context" to "AOR")))

            assertEquals(expected, actual)

            // verify that custom params are sent to validate
            val capturedInput = employeeDataSlot.captured
            assertThat(capturedInput.customParams)
                .containsExactlyInAnyOrderEntriesOf(
                    mapOf(
                        "complianceConsent.aor.workingPracticesMatchAorAgreement" to "true",
                        "complianceConsent.aor.workerCharacterizationSupported" to "true"))
        }

        @Test
        fun `should validate data when success false response is returned for aor`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkValidationRequest(
                    useCase = AOR_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            ValidationInput(
                                id = "2",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                    )),
                            ValidationInput(
                                id = "3",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                    ))))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            every { mockAorBulkOnboardingUseCase.validate(any(), any()) } returns
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = false,
                                    errors = listOf("some error"),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setEmployeeId("E001")
                                            .setType(ContractOuterClass.ContractType.CONTRACTOR)
                                            .build(),
                                    groupName = AOR_DETAILS_GROUP),
                            ),
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        MemberCreateInput.newBuilder()
                                            .setFirstName("John")
                                            .setLastName("Doe")
                                            .build(),
                                    groupName = AOR_DETAILS_GROUP))))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.validate(request)

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("2"),
                        success = false,
                        errors = listOf(ValidationError("", "", listOf("some error"))),
                        group = AOR_DETAILS_GROUP),
                    ValidationResult(
                        inputIds = listOf("3"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf(
                                        "Please resolve validation errors of the same employee in other modules"))),
                        group = AOR_DETAILS_GROUP))

            assertEquals(expected, actual)
        }

        @Test
        fun `should validate data successfully for eor`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkValidationRequest(
                    useCase = EOR_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            ValidationInput(
                                id = "2",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "rowIdentifier" to "E001",
                                    ),
                                countryCode = "IND",
                                group = BASIC_DETAILS_GROUP),
                            ValidationInput(
                                id = "3",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "rowIdentifier" to "E001",
                                    ),
                                countryCode = "IND",
                                group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
                            ValidationInput(
                                id = "4",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "rowIdentifier" to "E001",
                                    ),
                                countryCode = "IND",
                                group = EOR_COMPENSATION_DATA_GROUP)),
                    jsonCustomParams = mapOf("COUNTRY_CODE" to "IND"))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            val employeeDataSlot = slot<EmployeeDataInput>()

            every {
                mockEorBulkOnboardingUseCase.validate(capture(employeeDataSlot), any())
            } returns
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setEmployeeId("E001")
                                            .setType(ContractOuterClass.ContractType.CONTRACTOR)
                                            .build(),
                                    groupName = BASIC_DETAILS_GROUP),
                            ),
                        "COMPENSATION_SCHEMA_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input = mapOf("CURRENCY" to "INR"),
                                    groupName = EOR_COMPENSATION_DATA_GROUP)),
                        "COMPLIANCE_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_4_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        UpdateComplianceInput.newBuilder()
                                            .addComplianceParams(
                                                0,
                                                ComplianceParam.newBuilder()
                                                    .setKey("probation")
                                                    .setUnit("DAYS")
                                                    .setValue(60))
                                            .addComplianceParams(
                                                1,
                                                ComplianceParam.newBuilder()
                                                    .setKey("noticePeriod")
                                                    .setUnit("MONTHS")
                                                    .setValue(2))
                                            .setContractId(123L)
                                            .setRequestId("request_id")
                                            .build(),
                                    groupName = COMPLIANCE_AND_LEAVES_DATA_GROUP))))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.validate(request)

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("2", "3", "4"),
                        success = true,
                        validatedData =
                            mapOf(
                                "CONTRACT_MODULE->companyId" to "1",
                                "CONTRACT_MODULE->type" to "CONTRACTOR",
                                "CONTRACT_MODULE->employeeId" to "E001",
                                "CONTRACT_MODULE->dataClass" to
                                    getGrpcClassName(CreateContractInput.newBuilder().build()),
                                "COMPENSATION_SCHEMA_MODULE->CURRENCY" to "INR",
                                "COMPENSATION_SCHEMA_MODULE->dataClass" to "Map",
                                "COMPLIANCE_MODULE->requestId" to "request_id",
                                "COMPLIANCE_MODULE->contractId" to "123",
                                "COMPLIANCE_MODULE->complianceParams->[0]->key" to "probation",
                                "COMPLIANCE_MODULE->complianceParams->[0]->value" to "60",
                                "COMPLIANCE_MODULE->complianceParams->[0]->unit" to "DAYS",
                                "COMPLIANCE_MODULE->complianceParams->[1]->key" to "noticePeriod",
                                "COMPLIANCE_MODULE->complianceParams->[1]->value" to "2",
                                "COMPLIANCE_MODULE->complianceParams->[1]->unit" to "MONTHS",
                                "COMPLIANCE_MODULE->dataClass" to
                                    getGrpcClassName(UpdateComplianceInput.newBuilder().build()),
                                "companyId" to "1",
                                "contractType" to "EMPLOYEE",
                                "countryCode" to "IND",
                                "context" to "EOR")))

            assertEquals(expected, actual)

            // verify that custom params are sent to validate
            val capturedInput = employeeDataSlot.captured
            assertThat(capturedInput.customParams)
                .containsExactlyInAnyOrderEntriesOf(mapOf("COUNTRY_CODE" to "IND"))
        }

        @Test
        fun `should validate data when success false response is returned for eor`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkValidationRequest(
                    useCase = EOR_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            ValidationInput(
                                id = "2",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "rowIdentifier" to "E001",
                                    ),
                                countryCode = "IND",
                                group = BASIC_DETAILS_GROUP),
                            ValidationInput(
                                id = "3",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "rowIdentifier" to "E001",
                                    ),
                                countryCode = "IND",
                                group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
                            ValidationInput(
                                id = "4",
                                companyId = 0,
                                data =
                                    mapOf(
                                        "rowIdentifier" to "E001",
                                    ),
                                countryCode = "IND",
                                group = EOR_COMPENSATION_DATA_GROUP)))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            every { mockEorBulkOnboardingUseCase.validate(any(), any()) } returns
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = false,
                                    errors = listOf("some error"),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setEmployeeId("E001")
                                            .setType(ContractOuterClass.ContractType.CONTRACTOR)
                                            .build(),
                                    groupName = BASIC_DETAILS_GROUP),
                            ),
                        "COMPENSATION_SCHEMA_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input = mapOf("CURRENCY" to "INR"),
                                    groupName = COMPLIANCE_AND_LEAVES_DATA_GROUP)),
                        "COMPLIANCE_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_4_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        UpdateComplianceInput.newBuilder()
                                            .addComplianceParams(
                                                0,
                                                ComplianceParam.newBuilder()
                                                    .setKey("probation")
                                                    .setUnit("DAYS")
                                                    .setValue(60))
                                            .addComplianceParams(
                                                1,
                                                ComplianceParam.newBuilder()
                                                    .setKey("noticePeriod")
                                                    .setUnit("MONTHS")
                                                    .setValue(2))
                                            .setContractId(123L)
                                            .setRequestId("request_id")
                                            .build(),
                                    groupName = EOR_COMPENSATION_DATA_GROUP))))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.validate(request)

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("2"),
                        success = false,
                        errors = listOf(ValidationError("", "", listOf("some error"))),
                        group = BASIC_DETAILS_GROUP),
                    ValidationResult(
                        inputIds = listOf("3"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf(
                                        "Please resolve validation errors of the same employee in other modules"))),
                        group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
                    ValidationResult(
                        inputIds = listOf("4"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf(
                                        "Please resolve validation errors of the same employee in other modules"))),
                        group = EOR_COMPENSATION_DATA_GROUP))

            assertEquals(expected, actual)
        }
    }

    @Test
    fun `should fail when country code not passed in input`() {
        every { companyServiceAdapter.getCompany(any()) } returns
            Company(
                id = 1L,
                logoId = 1L,
                displayName = "Test Company",
                countryFullName = "Test Country",
                countryCode = CountryCode.VNM,
                msaSigned = true,
                isTest = true,
            )

        val request =
            BulkValidationRequest(
                useCase = EOR_MEMBER_ONBOARDING,
                inputs =
                    listOf(
                        ValidationInput(
                            id = "2",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            group = BASIC_DETAILS_GROUP),
                        ValidationInput(
                            id = "3",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
                        ValidationInput(
                            id = "4",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            group = EOR_COMPENSATION_DATA_GROUP)))

        val mockGlobalPayrollBulkOnboardingUseCase = mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
        val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
        val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
        val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

        val underTest =
            BulkUploadService(
                mockGlobalPayrollBulkOnboardingUseCase,
                mockFreelancerBulkOnboardingUseCase,
                mockAorBulkOnboardingUseCase,
                mockEorBulkOnboardingUseCase,
                companyServiceAdapter,
                compensationSourceServiceAdapter,
                featureFlagService)

        val exception = assertThrows<MplBusinessException> { underTest.validate(request) }

        assertThat(exception.message).contains("Single Country code is required for EOR got: []")
        assertThat(exception.errorCode).isEqualTo(ErrorCodes.VALIDATION_FAILED)
    }

    @Test
    fun `should fail when country code is not supported by compensation service`() {
        every { companyServiceAdapter.getCompany(any()) } returns
            Company(
                id = 1L,
                logoId = 1L,
                displayName = "Test Company",
                countryFullName = "Test Country",
                countryCode = CountryCode.VNM,
                msaSigned = true,
                isTest = true,
            )

        val request =
            BulkValidationRequest(
                useCase = EOR_MEMBER_ONBOARDING,
                inputs =
                    listOf(
                        ValidationInput(
                            id = "2",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            countryCode = "USA",
                            group = BASIC_DETAILS_GROUP),
                        ValidationInput(
                            id = "3",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            countryCode = "USA",
                            group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
                        ValidationInput(
                            id = "4",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            countryCode = "USA",
                            group = EOR_COMPENSATION_DATA_GROUP)))

        val mockGlobalPayrollBulkOnboardingUseCase = mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
        val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
        val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
        val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

        val underTest =
            BulkUploadService(
                mockGlobalPayrollBulkOnboardingUseCase,
                mockFreelancerBulkOnboardingUseCase,
                mockAorBulkOnboardingUseCase,
                mockEorBulkOnboardingUseCase,
                companyServiceAdapter,
                compensationSourceServiceAdapter,
                featureFlagService)

        val exception = assertThrows<MplBusinessException> { underTest.validate(request) }

        assertThat(exception.message)
            .contains("New compensation service not enabled for country USA for type EMPLOYEE")
        assertThat(exception.errorCode).isEqualTo(ErrorCodes.UNSUPPORTED_OPERATION)
    }

    @Test
    fun `should fail when multiple country codes present in input`() {
        every { companyServiceAdapter.getCompany(any()) } returns
            Company(
                id = 1L,
                logoId = 1L,
                displayName = "Test Company",
                countryFullName = "Test Country",
                countryCode = CountryCode.VNM,
                msaSigned = true,
                isTest = true,
            )

        val request =
            BulkValidationRequest(
                useCase = EOR_MEMBER_ONBOARDING,
                inputs =
                    listOf(
                        ValidationInput(
                            id = "2",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            countryCode = "IND",
                            group = BASIC_DETAILS_GROUP),
                        ValidationInput(
                            id = "3",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            countryCode = "IND",
                            group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
                        ValidationInput(
                            id = "4",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            countryCode = "USA",
                            group = EOR_COMPENSATION_DATA_GROUP)))

        val mockGlobalPayrollBulkOnboardingUseCase = mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
        val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
        val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
        val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

        val underTest =
            BulkUploadService(
                mockGlobalPayrollBulkOnboardingUseCase,
                mockFreelancerBulkOnboardingUseCase,
                mockAorBulkOnboardingUseCase,
                mockEorBulkOnboardingUseCase,
                companyServiceAdapter,
                compensationSourceServiceAdapter,
                featureFlagService)

        val exception = assertThrows<MplBusinessException> { underTest.validate(request) }

        assertThat(exception.message)
            .contains("Single Country code is required for EOR got: [IND, USA]")
        assertThat(exception.errorCode).isEqualTo(ErrorCodes.VALIDATION_FAILED)
    }

    @Test
    fun `should fail when invalid country code`() {
        every { companyServiceAdapter.getCompany(any()) } returns
            Company(
                id = 1L,
                logoId = 1L,
                displayName = "Test Company",
                countryFullName = "Test Country",
                countryCode = CountryCode.VNM,
                msaSigned = true,
                isTest = true,
            )

        val request =
            BulkValidationRequest(
                useCase = EOR_MEMBER_ONBOARDING,
                inputs =
                    listOf(
                        ValidationInput(
                            id = "2",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            countryCode = "IN",
                            group = BASIC_DETAILS_GROUP),
                        ValidationInput(
                            id = "3",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            countryCode = "IN",
                            group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
                        ValidationInput(
                            id = "4",
                            companyId = 0,
                            data =
                                mapOf(
                                    "rowIdentifier" to "E001",
                                ),
                            countryCode = "IN",
                            group = EOR_COMPENSATION_DATA_GROUP)))

        val mockGlobalPayrollBulkOnboardingUseCase = mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
        val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
        val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
        val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

        val underTest =
            BulkUploadService(
                mockGlobalPayrollBulkOnboardingUseCase,
                mockFreelancerBulkOnboardingUseCase,
                mockAorBulkOnboardingUseCase,
                mockEorBulkOnboardingUseCase,
                companyServiceAdapter,
                compensationSourceServiceAdapter,
                featureFlagService)

        val exception = assertThrows<MplBusinessException> { underTest.validate(request) }

        assertThat(exception.message).contains("Invalid country code: IN")
        assertThat(exception.errorCode).isEqualTo(ErrorCodes.VALIDATION_FAILED)
    }

    @Nested
    inner class UpsertData {

        @Test
        fun `should return success for freelancer when upsert is success for freelancer`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkUpsertRequest(
                    jobId = 1,
                    useCase = FREELANCER_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            UpsertInput(
                                id = "2",
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                        "companyId" to "1",
                                    ),
                                group = CONTRACTOR_DETAILS_GROUP),
                            UpsertInput(
                                id = "3",
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                        "companyId" to "1",
                                    ),
                                group = CONTRACTOR_DETAILS_GROUP)))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            every { mockFreelancerBulkOnboardingUseCase.create(any(), any()) } returns
                BulkCreationResultV2(requestIdToContractId = mapOf("2" to 1, "3" to 2))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.upsert(request)

            val expected =
                listOf(
                    UpsertResult(
                        inputId = "2",
                        success = true,
                        errors = emptyList(),
                        upsertedData = mapOf("contractId" to "1"),
                    ),
                    UpsertResult(
                        inputId = "3",
                        success = true,
                        errors = emptyList(),
                        upsertedData = mapOf("contractId" to "2"),
                    ))

            assertEquals(expected, actual)
            verify {
                mockFreelancerBulkOnboardingUseCase.create(
                    withArg {},
                    withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.FREELANCER) })
            }
        }

        @Test
        fun `should return success for freelancer when upsert is success for aor`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkUpsertRequest(
                    jobId = 1,
                    useCase = AOR_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            UpsertInput(
                                id = "2",
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                        "companyId" to "1",
                                    ),
                                group = AOR_DETAILS_GROUP),
                            UpsertInput(
                                id = "3",
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                        "companyId" to "1",
                                    ),
                                group = AOR_DETAILS_GROUP)))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            every { mockAorBulkOnboardingUseCase.create(any(), any()) } returns
                BulkCreationResultV2(requestIdToContractId = mapOf("2" to 1, "3" to 2))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.upsert(request)

            val expected =
                listOf(
                    UpsertResult(
                        inputId = "2",
                        success = true,
                        errors = emptyList(),
                        upsertedData = mapOf("contractId" to "1"),
                    ),
                    UpsertResult(
                        inputId = "3",
                        success = true,
                        errors = emptyList(),
                        upsertedData = mapOf("contractId" to "2"),
                    ))

            assertEquals(expected, actual)
            verify {
                mockAorBulkOnboardingUseCase.create(
                    withArg {},
                    withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.AOR) })
            }
        }

        @Test
        fun `should return success when upsert is success for eor`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkUpsertRequest(
                    jobId = 1,
                    useCase = EOR_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            UpsertInput(
                                id = "2",
                                data =
                                    mapOf(
                                        "rowIdentifier" to "E001",
                                        "companyId" to "1",
                                    ),
                                group = BASIC_DETAILS_GROUP),
                            UpsertInput(
                                id = "3",
                                data =
                                    mapOf(
                                        "rowIdentifier" to "E0012",
                                        "companyId" to "1",
                                    ),
                                group = COMPLIANCE_AND_LEAVES_DATA_GROUP)))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            every { mockEorBulkOnboardingUseCase.create(any(), any()) } returns
                BulkCreationResultV2(requestIdToContractId = mapOf("2" to 1, "3" to 2))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.upsert(request)

            val expected =
                listOf(
                    UpsertResult(
                        inputId = "2",
                        success = true,
                        errors = emptyList(),
                        upsertedData = mapOf("contractId" to "1"),
                    ),
                    UpsertResult(
                        inputId = "3",
                        success = true,
                        errors = emptyList(),
                        upsertedData = mapOf("contractId" to "2"),
                    ))

            assertEquals(expected, actual)
            verify {
                mockEorBulkOnboardingUseCase.create(
                    withArg {},
                    withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
            }
        }

        @Test
        fun `should return success false when response not returned in BulkCreationResultV2 for freelancer`() {
            every { companyServiceAdapter.getCompany(any()) } returns
                Company(
                    id = 1L,
                    logoId = 1L,
                    displayName = "Test Company",
                    countryFullName = "Test Country",
                    countryCode = CountryCode.VNM,
                    msaSigned = true,
                    isTest = true,
                )

            val request =
                BulkUpsertRequest(
                    jobId = 1,
                    useCase = FREELANCER_MEMBER_ONBOARDING,
                    inputs =
                        listOf(
                            UpsertInput(
                                id = "2",
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                        "companyId" to "1",
                                    ),
                                group = CONTRACTOR_DETAILS_GROUP),
                            UpsertInput(
                                id = "3",
                                data =
                                    mapOf(
                                        "employeeId" to "E001",
                                        "companyId" to "1",
                                    ),
                                group = CONTRACTOR_DETAILS_GROUP)))

            val mockGlobalPayrollBulkOnboardingUseCase =
                mockk<GlobalPayrollBulkOnboardingUseCaseV2>()
            val mockFreelancerBulkOnboardingUseCase = mockk<FreelancerBulkOnboardingUseCase>()
            val mockAorBulkOnboardingUseCase = mockk<AorBulkOnboardingUseCase>()
            val mockEorBulkOnboardingUseCase = mockk<EorBulkOnboardingUseCaseV2>()

            every { mockFreelancerBulkOnboardingUseCase.create(any(), any()) } returns
                BulkCreationResultV2(requestIdToContractId = mapOf("2" to 1))

            val underTest =
                BulkUploadService(
                    mockGlobalPayrollBulkOnboardingUseCase,
                    mockFreelancerBulkOnboardingUseCase,
                    mockAorBulkOnboardingUseCase,
                    mockEorBulkOnboardingUseCase,
                    companyServiceAdapter,
                    compensationSourceServiceAdapter,
                    featureFlagService)

            val actual = underTest.upsert(request)

            val expected =
                listOf(
                    UpsertResult(
                        inputId = "2",
                        success = true,
                        errors = emptyList(),
                        upsertedData = mapOf("contractId" to "1"),
                    ),
                    UpsertResult(
                        inputId = "3",
                        success = false,
                        errors = emptyList(),
                    ))

            assertEquals(expected, actual)
        }
    }
}
