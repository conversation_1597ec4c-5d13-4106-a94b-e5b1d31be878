package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.domain.model.PayrollCycle
import com.multiplier.contract.onboarding.domain.model.PayrollCycleDTO
import com.multiplier.contract.onboarding.domain.model.PayrollCycleQuery
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationOuterClass.PayFrequency
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.RelaxedMockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import io.mockk.verifyOrder
import java.time.LocalDate
import java.time.YearMonth
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class PayrollCyclesServiceTest {
    @RelaxedMockK private lateinit var contractServiceAdapter: ContractServiceAdapter
    @RelaxedMockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter
    @RelaxedMockK private lateinit var compensationServiceAdapter: CompensationServiceAdapter
    @RelaxedMockK private lateinit var onboardingAudServiceAdapter: OnboardingAudServiceAdapter
    @RelaxedMockK private lateinit var companyServiceAdapter: CompanyServiceAdapter
    @RelaxedMockK private lateinit var payrollServiceAdapter: DefaultPayrollServiceAdapter
    @RelaxedMockK private lateinit var featureFlagService: FeatureFlagService
    @RelaxedMockK private lateinit var payrollCycleConfigService: PayrollCycleConfigService
    @InjectMockKs private lateinit var underTest: PayrollCyclesService

    @Nested
    inner class GetPayrollCycleConfig {
        @Test
        fun returns_correct_payroll_cycle_config() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-03-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY)
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-04-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)
            mockOnboarding(
                onboardingId = 103L,
                contractId = 203L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)

            runMocks()

            val checkingDate = LocalDate.parse("2023-05-01")

            val result =
                underTest.getPayrollCyclesByOnboardingId(
                    onboardings.mapNotNull { it.id }.toSet(), checkingDate)

            assertAll(
                { assertEquals(LocalDate.of(2023, 3, 16), result[101L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 5, 15), result[102L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 4, 15), result[103L]?.cutoffDate) },
                {
                    verify(exactly = 1) {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(any())
                    }
                },
                {
                    verifyOrder {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(
                            withArg {
                                assertEquals(
                                    setOf(
                                        PayrollCycleQuery(
                                            201L.toString(), 201L, LocalDate.of(2023, 3, 1)),
                                        PayrollCycleQuery(
                                            202L.toString(), 202L, LocalDate.of(2023, 4, 21)),
                                        PayrollCycleQuery(
                                            203L.toString(), 203L, LocalDate.of(2023, 3, 21))),
                                    it)
                            })
                    }
                })
        }

        @Test
        fun `HR_MEMBER with start date in the past use checkingDate as ref date to get cutoff date`() {
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2022-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                contractType = ContractType.HR_MEMBER)

            runMocks()

            val checkingDate = LocalDate.parse("2023-05-01")

            val result =
                underTest.getPayrollCyclesByOnboardingId(
                    onboardings.mapNotNull { it.id }.toSet(), checkingDate)

            assertAll(
                { assertEquals(LocalDate.of(2023, 5, 15), result[102L]?.cutoffDate) },
                {
                    verify(exactly = 1) {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(any())
                    }
                },
                {
                    verifyOrder {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(
                            withArg {
                                assertEquals(
                                    setOf(
                                        PayrollCycleQuery(
                                            202L.toString(), 202L, LocalDate.of(2023, 5, 1))),
                                    it)
                            })
                    }
                })
        }

        @Test
        fun add_1_cycle_for_contract_that_start_after_cutoff_date() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-04-20",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-03-17",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)

            runMocks()

            val result =
                underTest.getPayrollCyclesByOnboardingId(
                    setOf(101L, 102L), LocalDate.parse("2023-05-01"))

            assertAll(
                { assertEquals(LocalDate.of(2023, 5, 15), result[101L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 4, 15), result[102L]?.cutoffDate) },
                {
                    verify(exactly = 1) {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(any())
                    }
                },
                {
                    verifyOrder {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(
                            withArg {
                                assertEquals(
                                    setOf(
                                        PayrollCycleQuery(
                                            201L.toString(), 201L, LocalDate.of(2023, 4, 20)),
                                        PayrollCycleQuery(
                                            202L.toString(), 202L, LocalDate.of(2023, 3, 17))),
                                    it)
                            })
                    }
                },
            )
        }

        @Test
        fun returns_correctly_for_contract_of_previous_month_which_have_paid_deposit() {
            // paid deposit but cutoff date is more than a month ago
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-03-15",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                depositPaid = true)

            // paid deposit contracts
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                depositPaid = true)

            runMocks()

            val result =
                underTest.getPayrollCyclesByOnboardingId(
                    setOf(101L, 102L), LocalDate.parse("2023-05-01"))

            assertAll(
                { assertEquals(LocalDate.of(2023, 3, 15), result[101L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 5, 15), result[102L]?.cutoffDate) },
                {
                    verify(exactly = 2) {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(any())
                    }
                },
                {
                    verifyOrder {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(
                            withArg {
                                assertEquals(
                                    setOf(
                                        PayrollCycleQuery(
                                            201L.toString(), 201L, LocalDate.of(2023, 3, 15)),
                                        PayrollCycleQuery(
                                            202L.toString(), 202L, LocalDate.of(2023, 3, 21))),
                                    it)
                            })

                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(
                            withArg {
                                assertEquals(
                                    setOf(
                                        PayrollCycleQuery(
                                            202L.toString(), 202L, LocalDate.of(2023, 5, 1))),
                                    it)
                            })
                    }
                },
            )
        }

        @Test
        fun returns_correctly_for_contract_of_previous_month_which_have_signed_msa_and_employee_signed() {
            // not signed anything yet
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                companyId = 301L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)

            // signed but not paid msa
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                companyId = 302L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                employeeSigned = true)

            // paid msa but not signed
            mockOnboarding(
                onboardingId = 103L,
                contractId = 203L,
                companyId = 303L,
                startOn = "2023-04-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY)

            // paid msa and signed
            mockOnboarding(
                onboardingId = 104L,
                contractId = 204L,
                companyId = 304L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                employeeSigned = true)
            mockOnboarding(
                onboardingId = 105L,
                contractId = 205L,
                companyId = 305L,
                startOn = "2023-04-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY,
                employeeSigned = true)
            mockOnboarding(
                onboardingId = 106L,
                contractId = 206L,
                companyId = 304L,
                startOn = "2023-04-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY,
                employeeSigned = true)

            mockMsaSigned(setOf(303L, 304L, 305L))

            runMocks()

            val result =
                underTest.getPayrollCyclesByOnboardingId(
                    setOf(101L, 102L, 103L, 104L, 105L, 106L), LocalDate.parse("2023-05-01"))

            assertAll(
                { assertEquals(LocalDate.of(2023, 4, 15), result[101L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 4, 15), result[102L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 4, 16), result[103L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 5, 15), result[104L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 5, 16), result[105L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 5, 16), result[106L]?.cutoffDate) },
                {
                    verify(exactly = 2) {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(any())
                    }
                },
                {
                    verifyOrder {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(
                            withArg {
                                assertEquals(
                                    setOf(
                                        PayrollCycleQuery(
                                            201L.toString(), 201L, LocalDate.of(2023, 3, 21)),
                                        PayrollCycleQuery(
                                            202L.toString(), 202L, LocalDate.of(2023, 3, 21)),
                                        PayrollCycleQuery(
                                            203L.toString(), 203L, LocalDate.of(2023, 4, 1)),
                                        PayrollCycleQuery(
                                            204L.toString(), 204L, LocalDate.of(2023, 3, 21)),
                                        PayrollCycleQuery(
                                            205L.toString(), 205L, LocalDate.of(2023, 4, 1)),
                                        PayrollCycleQuery(
                                            206L.toString(), 206L, LocalDate.of(2023, 4, 1))),
                                    it)
                            })

                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(
                            withArg {
                                assertEquals(
                                    setOf(
                                        PayrollCycleQuery(
                                            204L.toString(), 204L, LocalDate.of(2023, 5, 1)),
                                        PayrollCycleQuery(
                                            205L.toString(), 205L, LocalDate.of(2023, 5, 1)),
                                        PayrollCycleQuery(
                                            206L.toString(), 206L, LocalDate.of(2023, 5, 1))),
                                    it)
                            })
                    }
                })
        }

        @Test
        fun does_not_check_for_next_cycles_if_cutoff_date_is_too_old() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-03-15",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-03-31",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY)

            runMocks()

            val result =
                underTest.getPayrollCyclesByOnboardingId(
                    setOf(101L, 102L), LocalDate.parse("2023-05-01"))

            assertAll(
                { assertEquals(LocalDate.of(2023, 3, 15), result[101L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 3, 31), result[102L]?.cutoffDate) },
                {
                    verify(exactly = 1) {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(any())
                    }
                },
                {
                    verify {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(
                            withArg {
                                assertEquals(
                                    setOf(
                                        PayrollCycleQuery(
                                            201L.toString(), 201L, LocalDate.of(2023, 3, 15)),
                                        PayrollCycleQuery(
                                            202L.toString(), 202L, LocalDate.of(2023, 3, 31)),
                                    ),
                                    it)
                            })
                    }
                })
        }

        @Test
        fun does_not_returns_cutoff_date_for_freelancer() {
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                contractType = ContractType.FREELANCER)
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY,
                contractType = ContractType.FREELANCER)

            runMocks()

            val result =
                underTest.getPayrollCyclesByOnboardingId(
                    setOf(101L, 102L), LocalDate.parse("2023-05-01"))

            assertTrue(result.isEmpty())
        }

        @Test
        fun `call and merge results from legacy service for contracts without FF or missing data`() {
            every { featureFlagService.isFeatureOn(any(), any()) } answers
                {
                    secondArg<Map<String, Long>>().getValue(FeatureFlags.Params.COMPANY) !in
                        COMPANY_IDS_WITHOUT_FF
                }

            val sendingDate = LocalDate.parse("2023-05-01")

            // contracts with FF
            mockOnboarding(
                onboardingId = 101L,
                contractId = 201L,
                startOn = "2023-03-01",
                payFrequency = PayFrequency.PAY_FREQUENCY_SEMIMONTHLY)
            mockOnboarding(
                onboardingId = 102L,
                contractId = 202L,
                startOn = "2023-04-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)
            mockOnboarding(
                onboardingId = 103L,
                contractId = 203L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)

            // contracts without FF
            mockOnboarding(
                companyId = COMPANY_IDS_WITHOUT_FF.first(),
                onboardingId = 104L,
                contractId = 204L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)
            mockOnboarding(
                companyId = COMPANY_IDS_WITHOUT_FF.last(),
                onboardingId = 105L,
                contractId = 205L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY)

            mockOnboarding(
                onboardingId = 106L,
                contractId = 206L,
                startOn = "2023-03-21",
                payFrequency = PayFrequency.PAY_FREQUENCY_MONTHLY,
                missingDataInPayrollService = true)

            runMocks()

            val result =
                underTest.getPayrollCyclesByOnboardingId(
                    setOf(101L, 102L, 103L, 104L, 105L, 106L), sendingDate)

            assertAll(
                { assertEquals(6, result.size) },
                { assertEquals(LocalDate.of(2023, 3, 16), result[101L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 5, 15), result[102L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 4, 15), result[103L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 4, 19), result[104L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 4, 19), result[105L]?.cutoffDate) },
                { assertEquals(LocalDate.of(2023, 4, 19), result[106L]?.cutoffDate) },
                {
                    verify(exactly = 1) {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(any())
                    }
                },
                {
                    verifyOrder {
                        payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(
                            withArg {
                                assertEquals(
                                    setOf(
                                        PayrollCycleQuery(
                                            201L.toString(), 201L, LocalDate.of(2023, 3, 1)),
                                        PayrollCycleQuery(
                                            202L.toString(), 202L, LocalDate.of(2023, 4, 21)),
                                        PayrollCycleQuery(
                                            203L.toString(), 203L, LocalDate.of(2023, 3, 21)),
                                        PayrollCycleQuery(
                                            206L.toString(), 206L, LocalDate.of(2023, 3, 21))),
                                    it)
                            })

                        payrollCycleConfigService.getPayrollCyclesByContractId(
                            withArg { assertEquals(sendingDate, it) },
                            withArg {
                                assertEquals(listOf(204L, 205L, 206L), it.map(Contract::getId))
                            })
                    }
                })
        }
    }

    private lateinit var onboardings: MutableList<Onboarding>
    private lateinit var contracts: MutableList<Contract>
    private lateinit var compensationMap: MutableMap<Long, CompensationOuterClass.Compensation>
    private lateinit var employeeSignedContractIds: MutableSet<Long>
    private lateinit var depositPaidContractIds: MutableSet<Long>
    private lateinit var contractMissingDataInPayrollService: MutableSet<Long>

    @BeforeEach
    fun setup() {
        onboardings = mutableListOf()
        contracts = mutableListOf()
        compensationMap = mutableMapOf()
        employeeSignedContractIds = mutableSetOf()
        depositPaidContractIds = mutableSetOf()
        contractMissingDataInPayrollService = mutableSetOf()

        every { featureFlagService.isFeatureOn(any(), any()) } returns true

        every { payrollServiceAdapter.getPayrollCyclesForContractsByReferenceDate(any()) } answers
            {
                firstArg<Set<PayrollCycleQuery>>()
                    .filter { it.contractId !in contractMissingDataInPayrollService }
                    .associateBy(
                        { it },
                        {
                            val payFrequency =
                                compensationMap
                                    .getValue(it.contractId)
                                    .postProbationBasePay
                                    .payFrequency
                            val refDate = it.referenceDate

                            when (payFrequency) {
                                PayFrequency.PAY_FREQUENCY_MONTHLY -> {
                                    val cutoffDate =
                                        if (refDate.dayOfMonth > 15)
                                            refDate.withDayOfMonth(15).plusMonths(1)
                                        else refDate.withDayOfMonth(15)

                                    PayrollCycleDTO(
                                        payFrequency = PayrollCycleDTO.PayFrequency.MONTHLY,
                                        payrollMonth = YearMonth.from(cutoffDate),
                                        cutoffDate = cutoffDate,
                                        startDate = cutoffDate.minusMonths(1).withDayOfMonth(21),
                                        endDate = cutoffDate.withDayOfMonth(20))
                                }
                                PayFrequency.PAY_FREQUENCY_SEMIMONTHLY -> {
                                    val cutoffDate =
                                        if (refDate.dayOfMonth > 16)
                                            refDate.plusMonths(1).withDayOfMonth(1).minusDays(1)
                                        else refDate.withDayOfMonth(16)

                                    PayrollCycleDTO(
                                        payFrequency = PayrollCycleDTO.PayFrequency.SEMIMONTHLY,
                                        payrollMonth = YearMonth.from(cutoffDate),
                                        cutoffDate = cutoffDate,
                                        startDate = cutoffDate,
                                        endDate = cutoffDate)
                                }
                                PayFrequency.PAY_FREQUENCY_BIWEEKLY -> {
                                    PayrollCycleDTO(
                                        payFrequency = PayrollCycleDTO.PayFrequency.BIWEEKLY,
                                        payrollMonth = YearMonth.from(refDate),
                                        cutoffDate = refDate,
                                        startDate = refDate,
                                        endDate = refDate.plusDays(13))
                                }
                                PayFrequency.PAY_FREQUENCY_WEEKLY -> {
                                    PayrollCycleDTO(
                                        payFrequency = PayrollCycleDTO.PayFrequency.WEEKLY,
                                        payrollMonth = YearMonth.from(refDate),
                                        cutoffDate = refDate,
                                        startDate = refDate,
                                        endDate = refDate.plusDays(6))
                                }
                                else ->
                                    throw IllegalArgumentException(
                                        "Unsupported pay frequency: $payFrequency")
                            }
                        })
            }
    }

    companion object {
        private const val COMPANY_ID = 999L
        private val COMPANY_IDS_WITHOUT_FF = listOf(777L, 888L)
    }

    private fun runMocks() {
        val onboardingIds = onboardings.mapNotNull { it.id }.toSet()
        val contractIds = contracts.map { it.id }.toSet()
        every { onboardingServiceAdapter.getAllByIds(onboardingIds) } returns onboardings

        every { contractServiceAdapter.getNonDeletedNonEndedContracts(contractIds) } returns
            contracts

        every { compensationServiceAdapter.getCurrentCompensationByContractIds(any()) } returns
            compensationMap

        mockEmployeeSigned(employeeSignedContractIds)
        mockDepositPaid(depositPaidContractIds)

        if (contracts.any { it.companyId in COMPANY_IDS_WITHOUT_FF }) {
            every { payrollCycleConfigService.getPayrollCyclesByContractId(any(), any()) } answers
                {
                    secondArg<List<Contract>>().associate { contract ->
                        val cutoffDate = LocalDate.of(2023, 4, 19)

                        contract.id to
                            PayrollCycle(
                                payFrequency = PayrollCycle.PayFrequency.MONTHLY,
                                payrollMonth = YearMonth.from(cutoffDate),
                                cutoffDate = cutoffDate,
                                payDay = PayrollCycle.PayDay.Monthly,
                                payDate = cutoffDate.withDayOfMonth(15),
                                contractStartFrom = cutoffDate.minusMonths(1).withDayOfMonth(21),
                                contractStartTo = cutoffDate.withDayOfMonth(20))
                    }
                }
        }
    }

    private fun mockDepositPaid(contractIds: Set<Long>) {
        every { contractServiceAdapter.getDepositPaidContractIds(any()) } answers
            {
                firstArg<Set<Long>>().intersect(contractIds)
            }
    }

    private fun mockEmployeeSigned(contractIds: Set<Long>) {
        every {
            onboardingAudServiceAdapter.getAllContractIdsOfOnboardingAuditsWithExperienceAndStatus(
                any(), eq("company"), eq(ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SIGNED))
        } answers { firstArg<Set<Long>>().intersect(contractIds) }
    }

    private fun mockMsaSigned(companyIds: Set<Long>) {
        every { companyServiceAdapter.getMSASignedCompanyIds(any()) } answers
            {
                firstArg<Set<Long>>().intersect(companyIds)
            }
    }

    private fun mockOnboarding(
        onboardingId: Long,
        contractId: Long,
        companyId: Long = 999L,
        startOn: String,
        payFrequency: PayFrequency,
        contractType: ContractType = ContractType.EMPLOYEE,
        employeeSigned: Boolean = false,
        depositPaid: Boolean = false,
        missingDataInPayrollService: Boolean = false
    ) {
        onboardings.add(
            Onboarding(
                id = onboardingId,
                contractId = contractId,
                experience = "company",
                status = OnboardingStatus.SIGNATURE_EMPLOYEE_SENT))

        contracts.add(
            Contract.newBuilder()
                .setId(contractId)
                .setStartOn(LocalDate.parse(startOn).toGrpcDate())
                .setCountry("IND")
                .setCompanyId(companyId)
                .setType(contractType)
                .build())

        if (contractType != ContractType.FREELANCER) {
            compensationMap[contractId] =
                CompensationOuterClass.Compensation.newBuilder()
                    .setPostProbationBasePay(
                        CompensationOuterClass.CompensationPayComponent.newBuilder()
                            .setPayFrequency(payFrequency))
                    .setPayType(CompensationOuterClass.PayType.FIXED)
                    .build()
        }

        if (employeeSigned) {
            employeeSignedContractIds.add(contractId)
        }

        if (depositPaid) {
            depositPaidContractIds.add(contractId)
        }

        if (missingDataInPayrollService) {
            contractMissingDataInPayrollService.add(contractId)
        }
    }
}
