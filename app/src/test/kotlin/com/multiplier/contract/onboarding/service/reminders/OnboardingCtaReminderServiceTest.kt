package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.contract.onboarding.adapter.PigeonServiceAdapter
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.Company
import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.domain.model.Member
import com.multiplier.contract.onboarding.domain.model.Persona
import com.multiplier.contract.onboarding.repository.model.JpaOnboarding
import com.multiplier.contract.onboarding.repository.model.JpaOnboardingReminder
import com.multiplier.contract.onboarding.service.OnboardingReminderService
import com.multiplier.contract.onboarding.service.ReminderCtaTemplateProcessor
import com.multiplier.contract.onboarding.service.notifications.NotificationService
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.onboarding.types.Reminder
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.CountryWorkStatus
import com.multiplier.pigeonservice.schema.grpc.GetEmailTemplateResponse
import io.mockk.*
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import java.time.LocalDateTime
import java.util.*
import kotlin.collections.HashMap
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.*
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
class OnboardingCtaReminderServiceTest {

    @MockK lateinit var emailTemplateDataService: EmailTemplateDataService

    @MockK private lateinit var mockProcessor: ReminderCtaTemplateProcessor

    @MockK private lateinit var onboardingReminderService: OnboardingReminderService

    @MockK private lateinit var notificationService: NotificationService

    @MockK private lateinit var pigeonServiceAdapter: PigeonServiceAdapter

    lateinit var underTest: OnboardingCtaReminderService

    private val ccEmails = "<EMAIL>"

    @BeforeEach
    fun setup() {
        // Mock the notificationType method to return the expected notification type
        every { mockProcessor.notificationType() } returns
            NotificationType.OpsMemberSignContractReminderCta

        // Create a list with our mock processor
        val processors = listOf(mockProcessor)

        // Create the service with the list of processors
        underTest =
            OnboardingCtaReminderService(
                processors,
                onboardingReminderService,
                notificationService,
                pigeonServiceAdapter,
                emailTemplateDataService,
                ccEmails)
    }

    @Test
    fun should_fetch_email_templates_successfully() {
        val contract =
            Contract.newBuilder()
                .setId(123L)
                .setCompanyId(101L)
                .setMemberId(1L)
                .setCountry(CountryCode.IND.name)
                .setWorkStatus(CountryWorkStatus.RESIDENT)
                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                .build()

        val jpaOnboarding =
            JpaOnboarding(
                id = 2L,
                contractId = contract.id,
                experience = "company",
                currentStep = OnboardingStep.ONBOARDED,
                status = ContractOnboardingStatus.ACTIVE)

        val jpaReminder =
            JpaOnboardingReminder(
                id = 3L,
                contractId = contract.id,
                templateName = NotificationType.OpsMemberSignContractReminderCta,
                count = 2,
                updatedOn = LocalDateTime.now())

        val reminder =
            Reminder(
                jpaReminder.templateName.toString(),
                jpaReminder.count,
                jpaReminder.updatedOn,
                jpaReminder.editableTemplateParams)

        every { onboardingReminderService.findByContractIdAndTemplateName(any(), any()) } returns
            (Optional.of(jpaReminder))

        every { emailTemplateDataService.fetchEmailTemplateData(any()) } returns
            mockk(relaxed = true)

        val templateData =
            GetEmailTemplateResponse.newBuilder()
                .setTemplateDocId("123")
                .setTemplateBody("<html></html>")
                .setTemplateName(NotificationType.OpsMemberSignContractReminderCta.name)
                .setSubject("subject")
                .build()

        every { pigeonServiceAdapter.getEmailTemplateDetails(any(), any()) } returns templateData

        every { mockProcessor.validate(any()) } returns true
        every { mockProcessor.editableParams(any()) } returns emptyList()

        val emailData: MutableMap<String, String> = HashMap()
        emailData["name"] = "random"

        every { mockProcessor.fetchDataAndOverrideEditableParams(any(), any()) } returns emailData

        val response = underTest.fetchAllCtaTemplatesWithData(contract.id)

        val subject = "[${contract.id}] subject"
        assertThat(response.get(0).subject).isEqualTo(subject)
        assertThat(response.get(0).templateName)
            .isEqualTo(NotificationType.OpsMemberSignContractReminderCta)
        assertThat(response.get(0).reminder.count).isEqualTo(2)
        assertThat(response.get(0).reminder.lastSentOn).isEqualTo(reminder.lastSentOn)
        assertThat(response.get(0).reminder.templateName).isEqualTo(reminder.templateName)
    }

    @Test
    fun should_return_empty_data_when_no_templates_valid() {
        val contract =
            Contract.newBuilder()
                .setId(123L)
                .setCompanyId(101L)
                .setMemberId(1L)
                .setCountry(CountryCode.IND.name)
                .setWorkStatus(CountryWorkStatus.RESIDENT)
                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                .build()

        val jpaReminder =
            JpaOnboardingReminder(
                id = 3L,
                contractId = contract.id,
                templateName = NotificationType.OpsMemberSignContractReminderCta,
                count = 2,
                updatedOn = LocalDateTime.now())

        every { onboardingReminderService.findByContractIdAndTemplateName(any(), any()) } returns
            (Optional.of(jpaReminder))

        every { emailTemplateDataService.fetchEmailTemplateData(any()) } returns
            mockk(relaxed = true)

        every { mockProcessor.validate(any()) } returns false

        val response = underTest.fetchAllCtaTemplatesWithData(contract.id)

        assertThat(response.isEmpty()).isEqualTo(true)
    }

    @Test
    fun should_return_reminder_as_empty_when_no_reminders_were_sent() {
        every { onboardingReminderService.findByContractIdAndTemplateName(any(), any()) } returns
            (Optional.empty())

        every { emailTemplateDataService.fetchEmailTemplateData(any()) } returns
            mockk(relaxed = true)

        val templateData =
            GetEmailTemplateResponse.newBuilder()
                .setTemplateDocId("123")
                .setTemplateBody("<html></html>")
                .setTemplateName(NotificationType.OpsMemberSignContractReminderCta.name)
                .setSubject("subject")
                .build()

        every { pigeonServiceAdapter.getEmailTemplateDetails(any(), any()) } returns templateData

        every { mockProcessor.validate(any()) } returns true
        every { mockProcessor.editableParams(any()) } returns emptyList()

        val emailData: MutableMap<String, String> = HashMap()
        emailData["name"] = "random"

        every { mockProcessor.fetchDataAndOverrideEditableParams(any(), any()) } returns emailData

        val response = underTest.fetchAllCtaTemplatesWithData(123L)

        assertThat(response[0].reminder).isEqualTo(null)
        assertThat(response[0].templateProperties.isEditable).isEqualTo(false)
        assertThat(response[0].templateProperties.editableParams).isEmpty()
    }

    @Test
    fun should_set_templateProperties_isEditable_true_when_multiFrequencySupport_enabled_and_editableParams_exist() {
        every { onboardingReminderService.findByContractIdAndTemplateName(any(), any()) } returns
            (Optional.empty())

        val emailTemplateData = mockk<EmailTemplateData>(relaxed = true)
        every { emailTemplateData.multiFrequencySupportEnabled } returns true
        every { emailTemplateDataService.fetchEmailTemplateData(any()) } returns emailTemplateData

        val templateData =
            GetEmailTemplateResponse.newBuilder()
                .setTemplateDocId("123")
                .setTemplateBody("<html></html>")
                .setTemplateName(NotificationType.OpsMemberSignContractReminderCta.name)
                .setSubject("subject")
                .build()

        every { pigeonServiceAdapter.getEmailTemplateDetails(any(), any()) } returns templateData

        every { mockProcessor.validate(any()) } returns true

        val mockEditableParam = mockk<com.multiplier.contract.onboarding.service.EditableParam>()
        every { mockEditableParam.key } returns "testParam"
        every { mockProcessor.editableParams(any()) } returns listOf(mockEditableParam)

        val emailData: MutableMap<String, String> = HashMap()
        emailData["name"] = "random"

        every { mockProcessor.fetchDataAndOverrideEditableParams(any(), any()) } returns emailData

        val response = underTest.fetchAllCtaTemplatesWithData(123L)

        assertThat(response[0].templateProperties.isEditable).isEqualTo(true)
        assertThat(response[0].templateProperties.editableParams).hasSize(1)
        assertThat(response[0].templateProperties.editableParams[0]).isEqualTo("testParam")
    }

    @Test
    fun should_send_email_successfully() {
        val jpaReminder =
            JpaOnboardingReminder(
                id = 3L,
                contractId = 123L,
                templateName = NotificationType.OpsMemberSignContractReminderCta,
                count = 2,
                updatedOn = LocalDateTime.now())

        every { onboardingReminderService.findByContractIdAndTemplateName(any(), any()) } returns
            (Optional.of(jpaReminder))

        every { emailTemplateDataService.fetchEmailTemplateData(any()) } returns
            mockk(relaxed = true)

        every { mockProcessor.validate(any()) } returns true

        val emailData: MutableMap<String, String> = HashMap()
        emailData["name"] = "random"

        every { mockProcessor.fetchDataAndOverrideEditableParams(any(), any()) } returns emailData

        every { notificationService.send(any()) } just Runs

        val templateData =
            GetEmailTemplateResponse.newBuilder()
                .setTemplateDocId("123")
                .setTemplateBody("<html></html>")
                .setTemplateName(NotificationType.OpsMemberSignContractReminderCta.name)
                .setSubject("subject")
                .build()

        every { pigeonServiceAdapter.getEmailTemplateDetails(any(), any()) } returns templateData

        jpaReminder.count += 1

        every { onboardingReminderService.createOrUpdateReminder(any(), any()) } returns jpaReminder

        underTest.sendReminderForTemplate(123L, NotificationType.OpsMemberSignContractReminderCta)
    }

    @Test
    fun should_return_error_when_invalid_notification_type() {
        val jpaReminder =
            JpaOnboardingReminder(
                id = 3L,
                contractId = 123L,
                templateName = NotificationType.OpsMemberSignContractReminderCta,
                count = 2,
                updatedOn = LocalDateTime.now())

        every { onboardingReminderService.findByContractIdAndTemplateName(any(), any()) } returns
            (Optional.of(jpaReminder))

        every { emailTemplateDataService.fetchEmailTemplateData(any()) } returns
            mockk(relaxed = true)

        val exception =
            Assertions.assertThrows(MplBusinessException::class.java) {
                underTest.sendReminderForTemplate(123L, NotificationType.AutoDebitPreNotification)
            }

        Assertions.assertEquals(
            "Invalid template for reminder cta AutoDebitPreNotification", exception.message)
    }

    @Test
    fun should_not_send_email_when_validate_returns_false() {
        val jpaReminder =
            JpaOnboardingReminder(
                id = 3L,
                contractId = 123L,
                templateName = NotificationType.OpsMemberSignContractReminderCta,
                count = 2,
                updatedOn = LocalDateTime.now())

        every { onboardingReminderService.findByContractIdAndTemplateName(any(), any()) } returns
            (Optional.of(jpaReminder))

        every { emailTemplateDataService.fetchEmailTemplateData(any()) } returns
            mockk(relaxed = true)

        every { mockProcessor.validate(any()) } returns false

        verify(exactly = 0) { mockProcessor.fetchDataAndOverrideEditableParams(any(), any()) }

        verify(exactly = 0) { notificationService.send(any()) }

        verify(exactly = 0) { onboardingReminderService.createOrUpdateReminder(any(), any()) }

        val exception =
            Assertions.assertThrows(MplBusinessException::class.java) {
                underTest.sendReminderForTemplate(
                    123L, NotificationType.OpsMemberSignContractReminderCta)
            }

        Assertions.assertEquals(
            "Validation failed for reminder template OpsMemberSignContractReminderCta",
            exception.message)
    }

    @Test
    fun should_throw_error_when_ops_user_not_set() {
        val contract =
            Contract.newBuilder()
                .setId(123L)
                .setCompanyId(101L)
                .setMemberId(1L)
                .setCountry(CountryCode.IND.name)
                .setWorkStatus(CountryWorkStatus.RESIDENT)
                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                .build()

        val jpaOnboarding =
            JpaOnboarding(
                id = 2L,
                contractId = 123L,
                experience = "company",
                currentStep = OnboardingStep.ONBOARDED,
                status = ContractOnboardingStatus.ACTIVE)

        val jpaReminder =
            JpaOnboardingReminder(
                id = 3L,
                contractId = 123L,
                templateName = NotificationType.OpsMemberSignContractReminderCta,
                count = 2,
                updatedOn = LocalDateTime.now())

        every { onboardingReminderService.findByContractIdAndTemplateName(any(), any()) } returns
            (Optional.of(jpaReminder))

        every { emailTemplateDataService.fetchEmailTemplateData(any()) } returns
            EmailTemplateData(
                company = someCompany(),
                contract = contract,
                onboardingCompany = jpaOnboarding,
                onboardingMember = null,
                member = someMember(),
                operationsUser = null,
                submittedLegalDocument = emptySet(),
                payrollFormRequirements = emptyMap(),
                guideLink = "TODO()",
                preregistrationRequiredCountry = false)

        every { mockProcessor.validate(any()) } returns true

        val emailData: MutableMap<String, String> = HashMap()
        emailData["name"] = "random"

        every { mockProcessor.fetchDataAndOverrideEditableParams(any(), any()) } returns emailData

        verify(exactly = 0) { notificationService.send(any()) }

        verify(exactly = 0) { onboardingReminderService.createOrUpdateReminder(any(), any()) }
        val exception =
            Assertions.assertThrows(MplBusinessException::class.java) {
                underTest.sendReminderForTemplate(
                    123L, NotificationType.OpsMemberSignContractReminderCta)
            }
        assertThat(exception)
            .isInstanceOf(MplBusinessException::class.java)
            .hasMessage("Failed to send reminder: Operations User is not set for contract 123")
    }

    private fun someMember() =
        Member(
            id = 1,
            email = "<EMAIL>",
            firstName = "freddy",
            lastName = "fred",
            fullName = "freddy fred",
            userId = "1",
            persona = Persona("MEMBER"))

    private fun someCompany() =
        Company(
            id = 1,
            logoId = 2,
            displayName = "empty",
            countryFullName = "empty",
            countryCode = CountryCode.CXR,
            msaSigned = false,
            isTest = false,
        )
}
