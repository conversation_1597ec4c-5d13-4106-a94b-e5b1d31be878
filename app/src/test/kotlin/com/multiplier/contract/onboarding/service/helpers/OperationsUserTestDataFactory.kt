package com.multiplier.contract.onboarding.service.helpers

import com.multiplier.contract.onboarding.domain.model.OperationsUser

object OperationsUserTestDataFactory {
    fun createOperationsUser(
        id: Long = 1,
        email: String = "<EMAIL>",
        firstName: String = "firstName",
        lastName: String = "lastName"
    ): OperationsUser {
        return OperationsUser(id = id, email = email, firstName = firstName, lastName = lastName)
    }
}
