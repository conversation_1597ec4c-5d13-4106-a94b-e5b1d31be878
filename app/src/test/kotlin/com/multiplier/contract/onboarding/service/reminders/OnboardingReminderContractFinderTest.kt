package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.CompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.OnboardingServiceAdapter
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.Compliance
import com.multiplier.contract.onboarding.domain.model.ComplianceType
import com.multiplier.contract.onboarding.domain.model.ContractAgreementType
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.toGrpcDate
import com.multiplier.contract.onboarding.service.toLocalDate
import com.multiplier.contract.onboarding.service.toTimeStamp
import com.multiplier.contract.onboarding.testing.StringToIntegerListConverter
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractStatus
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import java.time.LocalDate
import java.time.Month
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.converter.ConvertWith
import org.junit.jupiter.params.provider.CsvSource
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class OnboardingReminderContractFinderTest {

    @MockK private lateinit var payrollCycleContractService: InternalPayrollCycleContractService

    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter

    @MockK private lateinit var compensationServiceAdapter: CompensationServiceAdapter

    @MockK private lateinit var featureFlagService: FeatureFlagService

    @InjectMockKs private lateinit var contractFinder: OnboardingReminderContractFinder

    @BeforeEach
    fun beforeEach() {
        every {
            payrollCycleContractService.getPayrollCycleContractsByCutoffDatesAndCompanyIds(
                any(), any(), any())
        } returns emptyList()

        every { contractServiceAdapter.getContractsBy(any()) } returns emptyList()

        every { contractServiceAdapter.getNonDeletedNonEndedContracts(any()) } returns emptyList()

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns emptyList()

        every { onboardingServiceAdapter.getAllOnboardingsByContractIds(any(), any()) } returns
            emptyMap()

        mockFeatureFlag(on = true)
    }

    @Nested
    inner class FindContractsForPayrollCutoffDateReminder {

        @Test
        fun `should return empty list given no eor onboarding contracts found`() {
            every {
                payrollCycleContractService.getPayrollCycleContractsByCutoffDatesAndCompanyIds(
                    any(), any(), any())
            } returns emptyList()

            val result =
                contractFinder.findContractsForPayrollCutoffDateReminder(
                    LocalDate.now(), emptyList())

            assertThat(result).isEmpty()
        }

        @Test
        fun `should query for 10 cutoff dates that are notification candidates`() {
            val date = LocalDate.now()
            contractFinder.findContractsForPayrollCutoffDateReminder(date, emptyList())

            verify {
                payrollCycleContractService.getPayrollCycleContractsByCutoffDatesAndCompanyIds(
                    any(), match { it.size == 10 && it[0] == date }, any())
            }
        }

        @Test
        fun `for December should query for 10 cutoff dates that are notification candidates`() {
            val date = LocalDate.now().withMonth(12)
            contractFinder.findContractsForPayrollCutoffDateReminder(date, emptyList())

            verify {
                payrollCycleContractService.getPayrollCycleContractsByCutoffDatesAndCompanyIds(
                    any(), match { it.size == 10 && it[0] == date }, any())
            }
        }

        @Test
        fun `should query for cutoff dates that are relative to given simulated sending date`() {
            val sendingDate = LocalDate.now().minusWeeks(1)
            contractFinder.findContractsForPayrollCutoffDateReminder(sendingDate, emptyList())

            verify {
                payrollCycleContractService.getPayrollCycleContractsByCutoffDatesAndCompanyIds(
                    any(), match { it.size == 10 && it[0] == sendingDate }, any())
            }
        }

        @Test
        fun `for December should query for cutoff dates that are relative to given simulated sending date`() {
            val sendingDate = LocalDate.now().withMonth(12).minusWeeks(1)
            contractFinder.findContractsForPayrollCutoffDateReminder(sendingDate, emptyList())

            verify {
                payrollCycleContractService.getPayrollCycleContractsByCutoffDatesAndCompanyIds(
                    any(), match { it.size == 10 && it[0] == sendingDate }, any())
            }
        }
    }

    @Nested
    inner class FindContractsForStartDateReminder {

        @ParameterizedTest
        @CsvSource(
            "2023, 01, 14, '21'",
            "2023, 01, 15, '22'",
            "2023, 01, 16, '23'",
            "2023, 01, 17, '24'",
            "2023, 01, 18, '21, 25'",
            "2023, 01, 19, '22, 26'",
        )
        fun `should pick contract with start date before eligible payroll cutoff date and sending date not in payroll cutoff reminder date range`(
            year: Int,
            month: Int,
            day: Int,
            @ConvertWith(StringToIntegerListConverter::class) expectedStartDays: List<Int>
        ) {
            val sendingDate = LocalDate.of(year, month, day)
            val minStartDate = sendingDate.plusDays(3)
            val maxStartDate = sendingDate.plusDays(7)

            val contracts =
                mockEligibleContractWithStartOnRange(
                    minStartDate, maxStartDate, LocalDate.of(2023, 1, 1))
            turnFeatureFlagOn()

            val result = contractFinder.findContractsForStartDateReminder(sendingDate, emptyList())

            assertThat(result).hasSize(1)
            assertThat(result[0].payrollMonth.month).isEqualTo(Month.FEBRUARY)
            val pickedUpContractIds = result[0].contractIds
            val pickedUpStartDays =
                contracts
                    .filter { pickedUpContractIds.contains(it.id) }
                    .map { it.startOn.toLocalDate().dayOfMonth }

            assertThat(pickedUpStartDays).hasSameElementsAs(expectedStartDays)
        }

        @ParameterizedTest
        @CsvSource(
            "2023, 01, 25",
            "2023, 01, 26",
            "2023, 01, 31",
            "2023, 02,  1",
            "2023, 02,  9",
            "2023, 02, 10",
            "2023, 02, 11",
        )
        fun `should return empty list given sending date in payroll cutoff reminder date range`(
            year: Int,
            month: Int,
            day: Int,
        ) {
            val sendingDate = LocalDate.of(year, month, day)
            val minStartDate = sendingDate.plusDays(3)
            val maxStartDate = sendingDate.plusDays(7)

            mockEligibleContractWithStartOnRange(
                minStartDate, maxStartDate, LocalDate.of(2023, 1, 1))
            turnFeatureFlagOn()

            val result = contractFinder.findContractsForStartDateReminder(sendingDate, emptyList())

            assertThat(result).isEmpty()
        }

        @ParameterizedTest
        @CsvSource(
            "2023, 11, 21",
            "2023, 11, 26",
            "2023, 11, 30",
            "2023, 12,  1",
            "2023, 12,  4",
            "2023, 12, 5",
        )
        fun `for December should return empty list given sending date in payroll cutoff reminder date range`(
            year: Int,
            month: Int,
            day: Int,
        ) {
            val sendingDate = LocalDate.of(year, month, day)
            val minStartDate = sendingDate.plusDays(3)
            val maxStartDate = sendingDate.plusDays(7)

            mockEligibleContractWithStartOnRange(
                minStartDate, maxStartDate, LocalDate.of(2023, 11, 1))
            turnFeatureFlagOn()

            val result = contractFinder.findContractsForStartDateReminder(sendingDate, emptyList())

            assertThat(result).isEmpty()
        }

        @ParameterizedTest
        @CsvSource(
            "2023, 01, 16, '19, 23'",
            "2023, 01, 17, '20, 24'",
            "2023, 01, 18, '21, 25'",
            "2023, 01, 19, '22, 26'",
            "2023, 01, 20, '23, 27'",
            "2023, 01, 21, '24, 28'",
            "2023, 01, 22, '25, 29'",
        )
        fun `should pick contract created on or after payroll cutoff date but still eligible for that payroll then merge into next payroll cycle`(
            year: Int,
            month: Int,
            day: Int,
            @ConvertWith(StringToIntegerListConverter::class) expectedStartDays: List<Int>
        ) {
            val sendingDate = LocalDate.of(year, month, day)
            val minStartDate = sendingDate.plusDays(3)
            val maxStartDate = sendingDate.plusDays(7)

            val contracts =
                mockEligibleContractWithStartOnRange(
                    minStartDate, maxStartDate, LocalDate.of(2023, 1, 16))
            turnFeatureFlagOn()

            val result = contractFinder.findContractsForStartDateReminder(sendingDate, emptyList())

            assertThat(result).hasSize(1)
            assertThat(result[0].payrollMonth.month).isEqualTo(Month.FEBRUARY)
            val pickedUpContractIds = result[0].contractIds
            val pickedUpStartDays =
                contracts
                    .filter { pickedUpContractIds.contains(it.id) }
                    .map { it.startOn.toLocalDate().dayOfMonth }

            assertThat(pickedUpStartDays).hasSameElementsAs(expectedStartDays)
        }

        @ParameterizedTest
        @CsvSource(
            "2023, 12, 6, '9, 13'",
            "2023, 12, 7, '10, 14'",
            "2023, 12, 8, '11, 15'",
            "2023, 12, 9, '12, 16'",
            "2023, 12, 10, '13, 17'",
            "2023, 12, 11, '14, 18'",
            "2023, 12, 12, '15, 19'",
        )
        fun `for December should pick contract created on or after payroll cutoff date but still eligible for that payroll then merge into next payroll cycle`(
            year: Int,
            month: Int,
            day: Int,
            @ConvertWith(StringToIntegerListConverter::class) expectedStartDays: List<Int>
        ) {
            val sendingDate = LocalDate.of(year, month, day)
            val minStartDate = sendingDate.plusDays(3)
            val maxStartDate = sendingDate.plusDays(7)

            val contracts =
                mockEligibleContractWithStartOnRange(
                    minStartDate, maxStartDate, LocalDate.of(2023, 12, 6))
            turnFeatureFlagOn()

            val result = contractFinder.findContractsForStartDateReminder(sendingDate, emptyList())

            assertThat(result).hasSize(1)
            assertThat(result[0].payrollMonth.month).isEqualTo(Month.JANUARY)
            val pickedUpContractIds = result[0].contractIds
            val pickedUpStartDays =
                contracts
                    .filter { pickedUpContractIds.contains(it.id) }
                    .map { it.startOn.toLocalDate().dayOfMonth }

            assertThat(pickedUpStartDays).hasSameElementsAs(expectedStartDays)
        }

        @Test
        fun `should not pick contract created on payroll cutoff date given sending date is payroll cutoff date`() {
            val sendingDate = LocalDate.of(2023, 1, 10)
            val minStartDate = sendingDate.plusDays(3)
            val maxStartDate = sendingDate.plusDays(5)

            mockEligibleContractWithStartOnRange(
                minStartDate, maxStartDate, LocalDate.of(2023, 1, 10))
            turnFeatureFlagOn()

            val result = contractFinder.findContractsForStartDateReminder(sendingDate, emptyList())

            assertThat(result).isEmpty()
        }

        @Test
        fun `for December should not pick contract created on payroll cutoff date given sending date is payroll cutoff date`() {
            val sendingDate = LocalDate.of(2023, 12, 5)
            val minStartDate = sendingDate.plusDays(3)
            val maxStartDate = sendingDate.plusDays(5)

            mockEligibleContractWithStartOnRange(
                minStartDate, maxStartDate, LocalDate.of(2023, 12, 5))
            turnFeatureFlagOn()

            val result = contractFinder.findContractsForStartDateReminder(sendingDate, emptyList())

            assertThat(result).isEmpty()
        }
    }

    @Nested
    inner class FilterEligibleContractIds {

        @Test
        fun `should drop contract which is not in onboarding state`() {
            val contractId = 1L

            mockContractOnboardingData(
                ContractOnboardingData(
                    contractId = contractId,
                    contractStatus = ContractStatus.ACTIVE,
                    onboardingStatus = OnboardingStatus.CREATED,
                    onboardingStep = OnboardingStep.ONBOARDING_REVIEW,
                ))

            val result = contractFinder.filterEligibleContractIds(setOf(contractId))

            assertThat(result).doesNotContain(contractId)
        }

        @Test
        fun `drops revoked contracts`() {
            val contractId = 1L

            mockContractOnboardingData(
                ContractOnboardingData(
                    contractId = contractId,
                    contractStatus = ContractStatus.ONBOARDING,
                    onboardingStatus = OnboardingStatus.REVOKED,
                    onboardingStep = OnboardingStep.ONBOARDING_REVIEW,
                ))

            val result = contractFinder.filterEligibleContractIds(setOf(contractId))

            assertThat(result).doesNotContain(contractId)
        }

        @Test
        fun `should drop contract when feature flag for its company is off`() {
            val contractId = 1L
            val companyId = 222L

            mockContractOnboardingData(
                ContractOnboardingData(
                    contractId = contractId,
                    contractStatus = ContractStatus.ONBOARDING,
                    onboardingStatus = OnboardingStatus.CREATED,
                    onboardingStep = OnboardingStep.ONBOARDING_REVIEW,
                    companyId = companyId,
                ))

            mockFeatureFlag(false, companyId)

            val result = contractFinder.filterEligibleContractIds(setOf(contractId))

            assertThat(result).doesNotContain(contractId)
        }

        @Test
        fun `should drop contract which does not pass compensation definition step`() {
            val contractId = 1L
            val companyId = 222L

            mockContractOnboardingData(
                ContractOnboardingData(
                    contractId = contractId,
                    contractStatus = ContractStatus.ONBOARDING,
                    onboardingStatus = OnboardingStatus.DRAFT,
                    onboardingStep = OnboardingStep.DEFINITION_COMPENSATION_DETAILS,
                    companyId = companyId,
                ))

            val result = contractFinder.filterEligibleContractIds(setOf(contractId))

            assertThat(result).doesNotContain(contractId)
        }

        @Test
        fun `should drop contract which is not using multiplier template and is not under multiplier entity`() {
            val contractIds = listOf(1L, 2L, 3L)

            mockContractOnboardingData(
                ContractOnboardingData(
                    contractId = contractIds[0],
                    contractStatus = ContractStatus.ONBOARDING,
                    onboardingStatus = OnboardingStatus.CREATED,
                    onboardingStep = OnboardingStep.ONBOARDING_REVIEW,
                    complianceType = ComplianceType.CLIENT,
                    agreementType = ContractAgreementType.CUSTOM_TEMPLATE,
                ),
                ContractOnboardingData(
                    contractId = contractIds[1],
                    contractStatus = ContractStatus.ONBOARDING,
                    onboardingStatus = OnboardingStatus.CREATED,
                    onboardingStep = OnboardingStep.ONBOARDING_REVIEW,
                    complianceType = ComplianceType.MULTIPLIER,
                    agreementType = ContractAgreementType.UPLOADED_FINAL,
                ),
            )

            val result = contractFinder.filterEligibleContractIds(contractIds)

            assertThat(result).isEmpty()
        }

        @Test
        fun `should return eligible contracts`() {
            val contractId = 1L

            mockContractOnboardingData(
                ContractOnboardingData(
                    contractId = contractId,
                    contractStatus = ContractStatus.ONBOARDING,
                    onboardingStatus = OnboardingStatus.CREATED,
                    onboardingStep = OnboardingStep.ONBOARDING_REVIEW,
                ))

            val result = contractFinder.filterEligibleContractIds(setOf(contractId))

            assertThat(result).contains(contractId)
        }
    }

    private fun mockFeatureFlag(on: Boolean, companyId: Long? = null) {
        every {
            val companyIdMatcher =
                if (companyId == null) any<Map<String, Any>>() else mapOf("company" to companyId)
            featureFlagService.feature(eq(FeatureFlags.ONBOARDING_NOTIFICATION), companyIdMatcher)
        } returns
            GBFeatureResult(
                value = true,
                on = on,
                off = false,
                source = GBFeatureSource.force,
                experiment = null,
                experimentResult = null)
    }

    private data class ContractOnboardingData(
        val contractId: Long,
        val contractStatus: ContractStatus,
        val onboardingStatus: OnboardingStatus,
        val onboardingStep: OnboardingStep,
        val companyId: Long = 11,
        val complianceType: ComplianceType = ComplianceType.MULTIPLIER,
        val agreementType: ContractAgreementType = ContractAgreementType.MULTIPLIER_TEMPLATE,
    )

    private fun mockContractOnboardingData(vararg contracts: ContractOnboardingData) {
        val contractIds = contracts.map { it.contractId }.toSet()

        every { contractServiceAdapter.getNonDeletedNonEndedContracts(contractIds) } returns
            contracts.map {
                Contract.newBuilder()
                    .setId(it.contractId)
                    .setStatus(it.contractStatus)
                    .setCompanyId(it.companyId)
                    .build()
            }

        every {
            onboardingServiceAdapter.getAllOnboardingsByContractIds(contractIds, "company")
        } returns
            contracts.associate {
                it.contractId to
                    Onboarding(
                        id = 123,
                        status = it.onboardingStatus,
                        currentStep = it.onboardingStep,
                        contractId = it.contractId,
                        experience = "company",
                    )
            }

        every { contractServiceAdapter.getComplianceByContractIds(contractIds) } returns
            contracts.map {
                Compliance(
                    contractId = it.contractId,
                    agreementId = 444,
                    type = it.complianceType,
                    agreementType = it.agreementType)
            }
    }

    private fun turnFeatureFlagOn() {
        every {
            featureFlagService.feature(eq(FeatureFlags.ONBOARDING_NOTIFICATION), any())
        } returns
            GBFeatureResult(
                value = true,
                on = true,
                off = false,
                source = GBFeatureSource.force,
                experiment = null,
                experimentResult = null)
    }

    private fun mockEligibleContractWithStartOnRange(
        minStartDate: LocalDate,
        maxStartDate: LocalDate,
        createdDate: LocalDate
    ): List<Contract> {
        val contracts: LinkedList<Contract> = LinkedList<Contract>()
        var contractId: Long = 1
        var startDate = minStartDate
        while (!startDate.isAfter(maxStartDate)) {
            contracts.add(
                Contract.newBuilder()
                    .setId(contractId++)
                    .setStatus(ContractStatus.ONBOARDING)
                    .setCompanyId(11L)
                    .setStartOn(startDate.atStartOfDay().toGrpcDate())
                    .setWorkStatus(ContractOuterClass.CountryWorkStatus.RESIDENT)
                    .setCreatedOn(createdDate.atStartOfDay().toTimeStamp())
                    .build())
            startDate = startDate.plusDays(1)
        }

        every { contractServiceAdapter.getContractsBy(any()) } returns contracts

        val contractIds = contracts.map { it.id }

        every { onboardingServiceAdapter.getAllOnboardingsByContractIds(any(), any()) } returns
            contractIds.associateWith {
                Onboarding(
                    id = 111L,
                    status = OnboardingStatus.CREATED,
                    currentStep = OnboardingStep.ONBOARDING_REVIEW,
                    contractId = it,
                    experience = "company",
                )
            }

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns
            contractIds.map {
                Compliance(
                    contractId = it,
                    agreementId = 123,
                    type = ComplianceType.MULTIPLIER,
                    agreementType = ContractAgreementType.MULTIPLIER_TEMPLATE,
                )
            }

        every { compensationServiceAdapter.getCurrentCompensationByContractIds(any()) } returns
            contractIds.associateWith {
                Compensation.newBuilder()
                    .setPostProbationBasePay(
                        CompensationOuterClass.CompensationPayComponent.newBuilder()
                            .setPayFrequency(
                                CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY)
                            .setAmountType(CompensationOuterClass.PayAmountType.FIXED_AMOUNT)
                            .build())
                    .build()
            }

        return contracts
    }
}
