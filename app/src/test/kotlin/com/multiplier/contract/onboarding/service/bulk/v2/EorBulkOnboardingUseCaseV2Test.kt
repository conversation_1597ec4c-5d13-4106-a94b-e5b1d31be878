package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.BASIC_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.COMPLIANCE_AND_LEAVES_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.EOR_COMPENSATION_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.PayrollFrequencySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.NoticeAfterProbationValueSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmployeeIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.FirstNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.NationalitySpec
import com.multiplier.contract.onboarding.service.bulk.v2.module.*
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.contract.schema.contract.BulkContract.UpdateComplianceInput
import com.multiplier.contract.schema.contract.ContractOuterClass.CreateContractInput
import com.multiplier.core.schema.contract.Contract.ContractTerm
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class EorBulkOnboardingUseCaseV2Test {

    @MockK private lateinit var bulkContractModule: BulkContractModule

    @MockK private lateinit var bulkMemberModule: BulkMemberModule

    @MockK private lateinit var bulkContractOnboardingModule: BulkContractOnboardingModule

    @MockK private lateinit var bulkCompensationModule: BulkCompensationModuleV2

    @MockK private lateinit var bulkMemberLegalDataModule: BulkMemberLegalDataModule

    @MockK private lateinit var bulkComplianceModule: BulkComplianceModule

    @MockK private lateinit var bulkTimeOffModule: BulkTimeOffModule

    @MockK private lateinit var bulkPostOnboardingTriggerModule: BulkPostOnboardingTriggerModule

    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter

    @InjectMockKs private lateinit var underTest: EorBulkOnboardingUseCaseV2

    private val companyId = 1L
    private val options =
        BulkOnboardingOptions.newBuilder()
            .context(BulkOnboardingContext.FREELANCER)
            .companyId(companyId)
            .entityId(null)
            .build()

    @BeforeEach
    fun setUp() {
        every { bulkContractModule.getDataSpecs(any(), any()) } returns listOf(EmployeeIdSpec)
        every { bulkMemberModule.getDataSpecs(any(), any()) } returns listOf(FirstNameSpec)
        every { bulkContractOnboardingModule.getDataSpecs(any(), any()) } returns listOf()
        every { bulkCompensationModule.getDataSpecs(any(), any()) } returns
            listOf(PayrollFrequencySpec.copy(source = "COMPENSATION_SCHEMA_DATA_SPEC"))
        every { bulkMemberLegalDataModule.getDataSpecs(any(), any()) } returns
            listOf(NationalitySpec)
        every { bulkComplianceModule.getDataSpecs(any(), any()) } returns
            listOf(NoticeAfterProbationValueSpec.copy(source = "COMPLIANCE_SCHEMA_DATA_SPEC"))
        every { bulkTimeOffModule.getDataSpecs(any(), any()) } returns listOf()
        every { bulkPostOnboardingTriggerModule.getDataSpecs(any(), any()) } returns listOf()
        val legalEntityId = 12345L
        every { companyServiceAdapter.getCompanyPrimaryLegalEntity(companyId) } returns
            LegalEntity.newBuilder().setId(legalEntityId).build()
    }

    @Test
    fun `should verify that group is added for EOR`() {
        val dataSpecs = underTest.getDataSpecs(options, null)
        val groupMap = dataSpecs.associateBy({ it.key }, { it.group })
        assertEquals(BASIC_DETAILS_GROUP, groupMap[EmployeeIdSpec.key])
        assertEquals(BASIC_DETAILS_GROUP, groupMap[FirstNameSpec.key])
        assertEquals(BASIC_DETAILS_GROUP, groupMap[NationalitySpec.key])

        assertEquals(EmployeeData.EOR_COMPENSATION_DATA_GROUP, groupMap[PayrollFrequencySpec.key])
        assertEquals(
            EmployeeData.COMPLIANCE_AND_LEAVES_DATA_GROUP,
            groupMap[NoticeAfterProbationValueSpec.key])
    }

    @Test
    fun `should remove contractId and add type, term, country code to input data before validating in order to enrich input`() {

        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification = EmployeeIdentification(rowNumber = 1),
                            data = mapOf("contractId" to "123", "email" to "<EMAIL>"))))
        setUpEmptyValidation()

        underTest.validate(
            input,
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .companyId(companyId)
                .countryCode(CountryCode.IND)
                .entityId(null)
                .build())
        verify {
            bulkContractModule.validate(
                withArg { argument ->
                    argument.forEach { employeeData ->
                        assertThat(employeeData.data).doesNotContainKey("contractId")
                        assertThat(employeeData.data["term"]).isEqualTo(ContractTerm.PERMANENT.name)
                        assertThat(employeeData.data["type"]).isEqualTo(ContractType.EMPLOYEE.name)
                        assertThat(employeeData.data["country"]).isEqualTo("IND")
                    }
                },
                any(),
                any())
        }
    }

    @Test
    fun `should deduce correct term based on end date`() {

        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification = EmployeeIdentification(rowNumber = 1),
                            data =
                                mapOf(
                                    "contractId" to "123",
                                    "email" to "<EMAIL>",
                                    "endOn" to "2024-07-17"),
                            group = BASIC_DETAILS_GROUP)))
        setUpEmptyValidation()

        underTest.validate(
            input,
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .companyId(companyId)
                .countryCode(CountryCode.IND)
                .entityId(null)
                .build())
        verify {
            bulkContractModule.validate(
                withArg { argument ->
                    argument.forEach { employeeData ->
                        assertThat(employeeData.data).doesNotContainKey("contractId")
                        assertThat(employeeData.data["term"]).isEqualTo(ContractTerm.FIXED.name)
                        assertThat(employeeData.data["type"]).isEqualTo(ContractType.EMPLOYEE.name)
                        assertThat(employeeData.data["country"]).isEqualTo("IND")
                    }
                },
                any(),
                any())
        }
    }

    @Test
    fun `should add row identifier as employee id and contract params for compensation request`() {

        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    rowNumber = 1, rowIdentifierFromSheet = "11"),
                            data =
                                mapOf(
                                    "contractId" to "123",
                                    "email" to "<EMAIL>",
                                    "type" to "EMPLOYEE",
                                    "startOn" to "2024-05-17",
                                    "endOn" to "2024-07-17"),
                            group = BASIC_DETAILS_GROUP),
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    rowNumber = 1, rowIdentifierFromSheet = "11"),
                            data = mapOf(),
                            group = EOR_COMPENSATION_DATA_GROUP)))
        setUpEmptyValidation()

        underTest.validate(
            input,
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .companyId(companyId)
                .countryCode(CountryCode.IND)
                .entityId(null)
                .build())
        verify {
            bulkCompensationModule.validate(
                withArg { argument ->
                    argument.forEach { employeeData ->
                        assertThat(employeeData.data["employeeId"]).isEqualTo("11")
                        assertThat(employeeData.data["CONTRACT_START_ON"]).isEqualTo("2024-05-17")
                        assertThat(employeeData.data["CONTRACT_TERM"]).isEqualTo("FIXED")
                        assertThat(employeeData.data["CONTRACT_STATUS"])
                            .isEqualTo(ContractStatus.ONBOARDING.name)
                        assertThat(employeeData.data["CONTRACT_TYPE"])
                            .isEqualTo(ContractType.EMPLOYEE.name)
                    }
                },
                any(),
                any())
        }
    }

    @Test
    fun `should add group name in response`() {

        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    rowNumber = 1, rowIdentifierFromSheet = "11"),
                            data =
                                mapOf(
                                    "contractId" to "123",
                                    "email" to "<EMAIL>",
                                    "type" to "EMPLOYEE",
                                    "startOn" to "2024-05-17",
                                    "endOn" to "2024-07-17"),
                            group = BASIC_DETAILS_GROUP),
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    rowNumber = 1, rowIdentifierFromSheet = "11"),
                            data =
                                mapOf("probationValue" to "2", "probationAfterNoticePeriod" to "4"),
                            group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    rowNumber = 1, rowIdentifierFromSheet = "11"),
                            data = mapOf("CURRENCY" to "INR"),
                            group = EOR_COMPENSATION_DATA_GROUP)))
        setUpValidation()
        val result =
            underTest.validate(
                input,
                BulkOnboardingOptions.newBuilder()
                    .context(BulkOnboardingContext.EOR)
                    .companyId(companyId)
                    .countryCode(CountryCode.IND)
                    .entityId(null)
                    .build())

        assertThat(result.validationResults["CONTRACT_MODULE"]?.get(0)?.groupName ?: "")
            .isEqualTo(BASIC_DETAILS_GROUP)
        assertThat(result.validationResults["COMPENSATION_SCHEMA_MODULE"]?.get(0)?.groupName ?: "")
            .isEqualTo(EOR_COMPENSATION_DATA_GROUP)
        assertThat(result.validationResults["COMPLIANCE_MODULE"]?.get(0)?.groupName ?: "")
            .isEqualTo(COMPLIANCE_AND_LEAVES_DATA_GROUP)
    }

    @Test
    fun `should filter compliance params`() {

        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    rowNumber = 1, rowIdentifierFromSheet = "11"),
                            data =
                                mapOf("probationValue" to "2", "probationAfterNoticePeriod" to "4"),
                            group = COMPLIANCE_AND_LEAVES_DATA_GROUP),
                    ))
        setUpEmptyValidation()

        underTest.validate(
            input,
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .companyId(companyId)
                .countryCode(CountryCode.IND)
                .entityId(null)
                .build())
        verify {
            bulkComplianceModule.validate(
                withArg { argument ->
                    argument.forEach { employeeData ->
                        assertThat(employeeData.data["probationValue"]).isEqualTo("2")
                        assertThat(employeeData.data["probationAfterNoticePeriod"]).isEqualTo("4")
                    }
                },
                any(),
                any())
        }
    }

    @Test
    fun `should filter basic details`() {

        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    rowNumber = 1, rowIdentifierFromSheet = "11"),
                            data =
                                mapOf(
                                    "firstName" to "John",
                                    "lastName" to "Doe",
                                    "country" to "IND",
                                    "state" to "Karnataka"),
                            group = BASIC_DETAILS_GROUP),
                    ))
        setUpEmptyValidation()

        underTest.validate(
            input,
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .companyId(companyId)
                .countryCode(CountryCode.IND)
                .entityId(null)
                .build())
        verify {
            bulkContractModule.validate(
                withArg { argument ->
                    argument.forEach { employeeData ->
                        assertThat(employeeData.data["firstName"]).isEqualTo("John")
                        assertThat(employeeData.data["lastName"]).isEqualTo("Doe")
                        assertThat(employeeData.data["country"]).isEqualTo("IND")
                        assertThat(employeeData.data["state"]).isEqualTo("Karnataka")
                    }
                },
                any(),
                any())
        }
    }

    @Test
    fun `should override module params with workPlaceEntityId from company service`() {
        // Setup
        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    rowNumber = 1, rowIdentifierFromSheet = "11"),
                            data =
                                mapOf(
                                    "firstName" to "John",
                                    "lastName" to "Doe",
                                    "country" to "IND",
                                    "state" to "Karnataka"),
                            group = BASIC_DETAILS_GROUP),
                    ))

        setUpEmptyValidation()

        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .companyId(companyId)
                .countryCode(CountryCode.IND)
                .entityId(null)
                .build()

        val legalEntityId = 12345L

        val capturedModuleParams = slot<ModuleParams>()

        underTest.validate(input, options)

        verify {
            bulkContractModule.validate(
                withArg { employeeData ->
                    employeeData.forEach {
                        assertThat(it.data["firstName"]).isEqualTo("John")
                        assertThat(it.data["lastName"]).isEqualTo("Doe")
                        assertThat(it.data["country"]).isEqualTo("IND")
                        assertThat(it.data["state"]).isEqualTo("Karnataka")
                    }
                },
                eq(options),
                capture(capturedModuleParams))
        }

        assertThat(capturedModuleParams.captured.workPlaceEntityId).isEqualTo(legalEntityId)

        // Verify company service was called
        verify(exactly = 1) { companyServiceAdapter.getCompanyPrimaryLegalEntity(companyId) }
    }

    @Test
    fun `should not override module params when primary entity is null`() {
        // Setup
        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification =
                                EmployeeIdentification(
                                    rowNumber = 1, rowIdentifierFromSheet = "11"),
                            data =
                                mapOf(
                                    "firstName" to "John",
                                    "lastName" to "Doe",
                                    "country" to "IND",
                                    "state" to "Karnataka"),
                            group = BASIC_DETAILS_GROUP),
                    ))

        setUpEmptyValidation()

        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.EOR)
                .companyId(companyId)
                .countryCode(CountryCode.IND)
                .entityId(12345L)
                .build()

        // Mock the company service to return a legal entity
        val legalEntityId = 12345L

        val capturedModuleParams = slot<ModuleParams>()

        underTest.validate(input, options)

        verify {
            bulkContractModule.validate(
                withArg { employeeData ->
                    employeeData.forEach {
                        assertThat(it.data["firstName"]).isEqualTo("John")
                        assertThat(it.data["lastName"]).isEqualTo("Doe")
                        assertThat(it.data["country"]).isEqualTo("IND")
                        assertThat(it.data["state"]).isEqualTo("Karnataka")
                    }
                },
                eq(options),
                capture(capturedModuleParams))
        }

        assertThat(capturedModuleParams.captured.workPlaceEntityId).isEqualTo(legalEntityId)

        // Verify company service was called
        verify(exactly = 1) { companyServiceAdapter.getCompanyPrimaryLegalEntity(companyId) }
    }

    private fun setUpEmptyValidation() {
        every { bulkMemberModule.identifier() } returns BulkMemberModule.MODULE_NAME
        every { bulkContractModule.identifier() } returns BulkContractModule.MODULE_NAME
        every { bulkContractOnboardingModule.identifier() } returns
            BulkContractOnboardingModule.MODULE_NAME
        every { bulkCompensationModule.identifier() } returns BulkCompensationModuleV2.MODULE_NAME
        every { bulkMemberLegalDataModule.identifier() } returns
            BulkMemberLegalDataModule.MODULE_NAME
        every { bulkPostOnboardingTriggerModule.identifier() } returns
            BulkPostOnboardingTriggerModule.MODULE_NAME
        every { bulkComplianceModule.identifier() } returns BulkComplianceModule.MODULE_NAME
        every { bulkTimeOffModule.identifier() } returns BulkTimeOffModule.MODULE_NAME

        every { bulkContractModule.validate(any(), any(), any()) } answers
            {
                println(invocation.args[0])
                emptyList()
            }
        every { bulkMemberModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkContractOnboardingModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkCompensationModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkMemberLegalDataModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkComplianceModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkPostOnboardingTriggerModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkTimeOffModule.validate(any(), any(), any()) } returns emptyList()
    }

    private fun setUpValidation() {
        every { bulkMemberModule.identifier() } returns BulkMemberModule.MODULE_NAME
        every { bulkContractModule.identifier() } returns BulkContractModule.MODULE_NAME
        every { bulkContractOnboardingModule.identifier() } returns
            BulkContractOnboardingModule.MODULE_NAME
        every { bulkCompensationModule.identifier() } returns BulkCompensationModuleV2.MODULE_NAME
        every { bulkMemberLegalDataModule.identifier() } returns
            BulkMemberLegalDataModule.MODULE_NAME
        every { bulkPostOnboardingTriggerModule.identifier() } returns
            BulkPostOnboardingTriggerModule.MODULE_NAME
        every { bulkComplianceModule.identifier() } returns BulkComplianceModule.MODULE_NAME
        every { bulkTimeOffModule.identifier() } returns BulkTimeOffModule.MODULE_NAME

        every { bulkContractModule.validate(any(), any(), any()) } returns
            listOf(
                GrpcValidationResult(
                    success = true,
                    errors = emptyList(),
                    validationId = "rn_3",
                    input = CreateContractInput.getDefaultInstance()))

        every { bulkMemberModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkContractOnboardingModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkCompensationModule.validate(any(), any(), any()) } returns
            listOf(
                GrpcValidationResult(
                    success = true,
                    errors = emptyList(),
                    validationId = "rn_3",
                    input = mapOf("CURRENCY" to "INR")))
        every { bulkMemberLegalDataModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkComplianceModule.validate(any(), any(), any()) } returns
            listOf(
                GrpcValidationResult(
                    success = true,
                    errors = emptyList(),
                    validationId = "rn_3",
                    input = UpdateComplianceInput.getDefaultInstance()))
        every { bulkPostOnboardingTriggerModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkTimeOffModule.validate(any(), any(), any()) } returns emptyList()
    }
}
