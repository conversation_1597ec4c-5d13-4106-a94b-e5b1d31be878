package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.service.helpers.CompanyTestDataFactory.createCompany
import com.multiplier.contract.onboarding.service.helpers.ContractTestDataFactory.createContract
import com.multiplier.contract.onboarding.service.helpers.JpaOnboardingTestDataFactory.createJpaOnboarding
import com.multiplier.contract.onboarding.service.helpers.MemberTestDataFactory.createMember
import com.multiplier.contract.onboarding.service.helpers.OperationsUserTestDataFactory.createOperationsUser
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayNameGeneration
import org.junit.jupiter.api.DisplayNameGenerator
import org.junit.jupiter.api.Test

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
class OpsMemberUpdateDetailsContractorReminderCtaProcessorTest {

    private val processor = OpsMemberUpdateDetailsContractorReminderCtaProcessor()

    @Test
    fun validate_should_return_true_when_onboardingCompany_status_is_member_invited_for_freelancer() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep =
                            com.multiplier.contract.onboarding.domain.OnboardingStep
                                .ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(type = ContractOuterClass.ContractType.FREELANCER),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_true_when_onboardingCompany_status_is_member_invited_for_contractor() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep =
                            com.multiplier.contract.onboarding.domain.OnboardingStep
                                .ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(type = ContractOuterClass.ContractType.CONTRACTOR),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_true_when_onboardingMember_status_is_in_progress() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep =
                            com.multiplier.contract.onboarding.domain.OnboardingStep
                                .ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember =
                    createJpaOnboarding(
                        experience = "member",
                        currentStep =
                            com.multiplier.contract.onboarding.domain.OnboardingStep
                                .ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_STARTED),
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(type = ContractOuterClass.ContractType.FREELANCER),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_false_when_contract_is_not_freelancer_or_contractor() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep =
                            com.multiplier.contract.onboarding.domain.OnboardingStep
                                .ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(type = ContractOuterClass.ContractType.EMPLOYEE),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_false_when_opsUser_is_not_assigned() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep =
                            com.multiplier.contract.onboarding.domain.OnboardingStep
                                .ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = null,
                company = createCompany(),
                contract = createContract(type = ContractOuterClass.ContractType.FREELANCER),
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_false_when_onboardingCompany_and_onboardingMember_status_is_incorrect() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep =
                            com.multiplier.contract.onboarding.domain.OnboardingStep
                                .ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember =
                    createJpaOnboarding(
                        experience = "member",
                        currentStep =
                            com.multiplier.contract.onboarding.domain.OnboardingStep
                                .ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.MEMBER_VERIFICATION_IN_PROGRESS),
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(type = ContractOuterClass.ContractType.FREELANCER),
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun fetchData_should_return_correct_data_fields() {
        val member = createMember()
        val company = createCompany()
        val opsUser = createOperationsUser()

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep =
                            com.multiplier.contract.onboarding.domain.OnboardingStep
                                .ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = member,
                operationsUser = opsUser,
                company = company,
                contract = createContract(type = ContractOuterClass.ContractType.FREELANCER),
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.fetchDataAndOverrideEditableParams(emailTemplateData)

        assertThat(result.size).isEqualTo(3)
        assertThat(result["memberName"]).isEqualTo(member.fullName)
        assertThat(result["onboardingSpecialist"])
            .isEqualTo(opsUser.firstName + " " + opsUser.lastName)
        assertThat(result["clientName"]).isEqualTo(company.displayName)

        // Verify that guideLink and monthPayDate are not included
        assertThat(result.containsKey("guideLink")).isFalse()
        assertThat(result.containsKey("monthPayDate")).isFalse()
    }

    @Test
    fun notificationType_should_return_correct_type() {
        assertThat(processor.notificationType())
            .isEqualTo(NotificationType.OpsMemberUpdateDetailsContractorReminderCta)
    }
}
