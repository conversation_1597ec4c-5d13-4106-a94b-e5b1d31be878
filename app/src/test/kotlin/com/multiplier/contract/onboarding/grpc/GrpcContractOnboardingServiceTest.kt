package com.multiplier.contract.onboarding.grpc

import com.multiplier.contract.onboarding.domain.Clock
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.schema.*
import com.multiplier.contract.onboarding.schema.BulkOnboardResult as GrpcBulkOnboardResult
import com.multiplier.contract.onboarding.schema.BulkOnboardValidationResult as GrpcBulkOnboardValidationResult
import com.multiplier.contract.onboarding.service.ActivationCutoffService
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingJob
import com.multiplier.contract.onboarding.service.bulk.creation.BulkCreationResult
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmailSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.FirstNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.GenderSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.MaritalStatusSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.ContractContext
import com.multiplier.contract.onboarding.service.bulk.mapper.MemberContext
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule.Companion.StaticBankDataSpec.BankNameSpec
import com.multiplier.contract.onboarding.service.bulk.validation.*
import com.multiplier.contract.onboarding.service.grpc.GrpcExceptionWrapper
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.usecase.BulkOnboardingService
import com.multiplier.contract.onboarding.usecase.BulkOnboardingServiceFactory
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.grpc.common.contract.v2.Contract.ContractType.CONTRACT_TYPE_HR_MEMBER
import com.multiplier.grpc.common.country.v2.Country.CountryCode.COUNTRY_CODE_USA
import io.grpc.internal.testing.StreamRecorder
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import java.time.LocalDate
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class GrpcContractOnboardingServiceTest {

    @MockK private lateinit var bulkOnboardingServiceMock: BulkOnboardingService
    @MockK private lateinit var bulkOnboardingServiceFactory: BulkOnboardingServiceFactory
    @MockK private lateinit var grpcBulkUploadService: GrpcBulkUploadService
    @MockK private lateinit var activationCutoffService: ActivationCutoffService
    @MockK private lateinit var clock: Clock

    @InjectMockKs private lateinit var service: GrpcContractOnboardingService

    private val bulkOnboardProperties =
        mapOf(
            "employeeId" to "11",
            "contractId" to "111",
            FirstNameSpec.key to "Johnny",
            LastNameSpec.key to "Depp",
            EmailSpec.key to "<EMAIL>",
            GenderSpec.key to "MALE")

    private val employeeData =
        listOf(
            EmployeeData(
                EmployeeIdentification(
                    employeeId = "11",
                    contractId = 111,
                    firstName = "Johnny",
                    lastName = "Depp",
                    email = "<EMAIL>",
                    rowNumber = 1),
                data = bulkOnboardProperties,
                group = EmployeeData.EMPLOYMENT_DATA_GROUP))

    private val bulkOnboardOption =
        BulkOnboardingOptions.newBuilder()
            .context(BulkOnboardingContext.GLOBAL_PAYROLL)
            .contractType(ContractType.HR_MEMBER)
            .companyId(1)
            .entityId(1)
            .countryCode(CountryCode.USA)
            .build()

    private val grpcBulkOnboardOption =
        BulkOnboardOption.newBuilder()
            .setContext("GLOBAL_PAYROLL")
            .setContractType(CONTRACT_TYPE_HR_MEMBER)
            .setCompanyId(1L)
            .setEntityId(1L)
            .setCountryCode(COUNTRY_CODE_USA)
            .build()

    private val employeeDataInput = EmployeeDataInput(employeeData = employeeData, source = "gRPC")

    private val bulkOnboardRequest =
        BulkOnboardRequest.newBuilder()
            .addInputs(
                BulkOnboardInput.newBuilder()
                    .setRequestId(1)
                    .putAllProperties(bulkOnboardProperties)
                    .setGroup(EmployeeData.EMPLOYMENT_DATA_GROUP))
            .setOption(grpcBulkOnboardOption)
            .build()

    private val activationCutoffRequest =
        ActivationCutoffRequest.newBuilder()
            .addAllContractIds(listOf(553687L, 553579L, 521223L))
            .build()

    private val contractIdToCutoffDate =
        mapOf(
            553687L to LocalDate.of(2025, 4, 15),
            553579L to LocalDate.of(2025, 4, 15),
            521223L to LocalDate.of(2025, 10, 31))

    private val contractIdToCutoffDatePartialMap =
        mapOf(
            553687L to LocalDate.of(2025, 5, 20),
            553579L to LocalDate.of(2025, 6, 25),
        )

    @BeforeEach
    fun setUp() {
        every { bulkOnboardingServiceFactory.getBulkOnboardingService(any()) } returns
            bulkOnboardingServiceMock
    }

    @Nested
    inner class ValidateBulkOnboarding {

        @Test
        fun `should validate successfully`() {
            val responseRecorder = StreamRecorder.create<BulkOnboardValidationResponse>()
            val validationResults =
                ValidationResults(
                    employeeValidationResults = EmployeeValidationResults.EMPTY,
                    validationReport =
                        BulkValidationReport(
                            GenericValidationResult.EMPTY,
                            AggregatedEmployeeValidationResult.EMPTY))

            every {
                bulkOnboardingServiceMock.validate(employeeDataInput, bulkOnboardOption)
            } returns validationResults

            service.validateBulkOnboarding(bulkOnboardRequest, responseRecorder)

            val expected =
                BulkOnboardValidationResponse.newBuilder()
                    .addValidationResults(
                        GrpcBulkOnboardValidationResult.newBuilder()
                            .setRequestId(1)
                            .setSuccess(true)
                            .addAllErrors(emptyList())
                            .putAllValidatedProperties(bulkOnboardProperties))
                    .build()
            assertEquals(expected, responseRecorder.values[0])
        }

        @Test
        fun `should return error when an error occurred`() {
            val responseRecorder = StreamRecorder.create<BulkOnboardValidationResponse>()
            val request = BulkOnboardRequest.newBuilder().build()
            service.validateBulkOnboarding(request, responseRecorder)
            assertEquals(GrpcExceptionWrapper::class, responseRecorder.error!!::class)
        }
    }

    @Nested
    inner class BulkOnboard {

        @Test
        fun `should bulk onboard successfully`() {
            val bulkOnboardingJob = mockk<BulkOnboardingJob>()
            every { bulkOnboardingJob.creationResult } returns
                BulkCreationResult(
                    MemberContext(emptyMap()),
                    ContractContext(
                        mapOf(
                            employeeDataInput.employeeData.first().identification.validationId to
                                11)),
                    errors = emptyList())

            every {
                bulkOnboardingServiceMock.onboard(employeeDataInput, bulkOnboardOption)
            } returns bulkOnboardingJob

            val responseRecorder = StreamRecorder.create<BulkOnboardResponse>()
            service.bulkOnboard(bulkOnboardRequest, responseRecorder)

            val expected =
                BulkOnboardResponse.newBuilder()
                    .addResults(
                        GrpcBulkOnboardResult.newBuilder().setRequestId(1).setContractId(11))
                    .build()
            assertEquals(expected, responseRecorder.values[0])
        }

        @Test
        fun `should return error when an error occurred`() {
            val responseRecorder = StreamRecorder.create<BulkOnboardResponse>()
            val request = BulkOnboardRequest.newBuilder().build()
            service.bulkOnboard(request, responseRecorder)
            assertEquals(GrpcExceptionWrapper::class, responseRecorder.error!!::class)
        }
    }

    @Nested
    inner class GetBulkOnboardDataSpecs {

        @Test
        fun `should return correct response`() {
            val dataSpecs = listOf(FirstNameSpec, LastNameSpec, MaritalStatusSpec, BankNameSpec)
            every { bulkOnboardingServiceMock.getDataSpec(bulkOnboardOption) } returns dataSpecs

            val responseRecorder = StreamRecorder.create<BulkOnboardDataSpecsResponse>()
            service.getBulkOnboardDataSpecs(grpcBulkOnboardOption, responseRecorder)

            val expected =
                BulkOnboardDataSpecsResponse.newBuilder()
                    .addSpecs(
                        BulkOnboardDataSpec.newBuilder()
                            .setKey(FirstNameSpec.key)
                            .setLabel(FirstNameSpec.label)
                            .setRequired(FirstNameSpec.mandatory))
                    .addSpecs(
                        BulkOnboardDataSpec.newBuilder()
                            .setKey(LastNameSpec.key)
                            .setLabel(LastNameSpec.label)
                            .setRequired(LastNameSpec.mandatory)
                            .addAllValues(LastNameSpec.allowedValues))
                    .addSpecs(
                        BulkOnboardDataSpec.newBuilder()
                            .setKey(MaritalStatusSpec.key)
                            .setLabel(MaritalStatusSpec.label)
                            .setRequired(MaritalStatusSpec.mandatory)
                            .addAllValues(MaritalStatusSpec.allowedValues))
                    .addSpecs(
                        BulkOnboardDataSpec.newBuilder()
                            .setKey(BankNameSpec.keyWithPrefix())
                            .setLabel(BankNameSpec.label)
                            .setRequired(BankNameSpec.mandatory)
                            .addAllValues(BankNameSpec.allowedValues))
                    .build()
            assertEquals(expected, responseRecorder.values[0])
        }

        @Test
        fun `should return error when an error occurred`() {
            val request = BulkOnboardOption.newBuilder().build()
            val responseRecorder = StreamRecorder.create<BulkOnboardDataSpecsResponse>()
            service.getBulkOnboardDataSpecs(request, responseRecorder)
            assertEquals(GrpcExceptionWrapper::class, responseRecorder.error!!::class)
        }
    }

    @Nested
    inner class GetActivationCutoffDataForContracts {

        @Test
        fun `should return correct response`() {
            val responseRecorder = StreamRecorder.create<ActivationCutoffResponse>()

            every { clock.today() } returns LocalDate.of(2025, 10, 15)
            every {
                activationCutoffService.getActivationCutoffDateByContractId(
                    activationCutoffRequest.contractIdsList, LocalDate.of(2025, 10, 15))
            } returns contractIdToCutoffDate

            service.getActivationCutoffDataForContracts(activationCutoffRequest, responseRecorder)

            val expected =
                ActivationCutoffResponse.newBuilder()
                    .addContractsCutoffResponse(
                        ContractCutoff.newBuilder()
                            .setContractId(553687L)
                            .setActivationCutoff("2025-04-15")
                            .build())
                    .addContractsCutoffResponse(
                        ContractCutoff.newBuilder()
                            .setContractId(553579L)
                            .setActivationCutoff("2025-04-15")
                            .build())
                    .addContractsCutoffResponse(
                        ContractCutoff.newBuilder()
                            .setContractId(521223L)
                            .setActivationCutoff("2025-10-31")
                            .build())
                    .build()

            assertEquals(expected, responseRecorder.values[0])
        }

        @Test
        fun `returns only contracts with non-null cutoff dates and drop off contracts with null cutoff data`() {
            val responseRecorder = StreamRecorder.create<ActivationCutoffResponse>()

            every { clock.today() } returns LocalDate.of(2025, 10, 15)
            every {
                activationCutoffService.getActivationCutoffDateByContractId(
                    activationCutoffRequest.contractIdsList, LocalDate.of(2025, 10, 15))
            } returns contractIdToCutoffDatePartialMap

            service.getActivationCutoffDataForContracts(activationCutoffRequest, responseRecorder)

            val expected =
                ActivationCutoffResponse.newBuilder()
                    .addContractsCutoffResponse(
                        ContractCutoff.newBuilder()
                            .setContractId(553687L)
                            .setActivationCutoff("2025-05-20")
                            .build())
                    .addContractsCutoffResponse(
                        ContractCutoff.newBuilder()
                            .setContractId(553579L)
                            .setActivationCutoff("2025-06-25")
                            .build())
                    .build()

            assertEquals(expected, responseRecorder.values[0])
        }

        @Test
        fun `should return error when an error occurred`() {
            val request = ActivationCutoffRequest.newBuilder().build()
            val responseRecorder = StreamRecorder.create<ActivationCutoffResponse>()

            every { clock.today() } returns LocalDate.of(2025, 10, 15)
            every {
                activationCutoffService.getActivationCutoffDateByContractId(
                    any(), LocalDate.of(2025, 10, 15))
            } throws RuntimeException("Test Exception")

            service.getActivationCutoffDataForContracts(request, responseRecorder)
            assertEquals(GrpcExceptionWrapper::class, responseRecorder.error!!::class)
        }
    }
}
