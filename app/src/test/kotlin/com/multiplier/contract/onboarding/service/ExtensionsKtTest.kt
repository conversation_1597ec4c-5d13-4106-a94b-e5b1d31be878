package com.multiplier.contract.onboarding.service

import com.google.type.Date
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.ComplianceParam
import com.multiplier.contract.schema.contract.ContractOuterClass.CreateContractInput
import com.multiplier.country.schema.Country
import com.multiplier.member.schema.TransferType
import com.multiplier.member.schema.UpsertBankDetailsInput
import com.multiplier.member.schema.memberBankDynamicDetail
import com.multiplier.member.schema.upsertBankDetailsInput
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ExtensionsKtTest {

    @Nested
    inner class ConvertToMap {

        @Test
        fun `should convert an object to a property map`() {
            val obj =
                object {
                    val key = "value"
                }

            val actual = obj.convertToMap()

            val expected = mapOf("key" to "value")
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should consider overrides`() {
            val obj =
                object {
                    val key = "value"
                }

            val actual = obj.convertToMap(overrides = mapOf("key" to "overridden"))

            val expected = mapOf("key" to "overridden")
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should convert nested objects appropriately`() {
            val obj =
                object {
                    val nested =
                        object {
                            val key = "value"
                        }
                }

            val actual = obj.convertToMap()

            val expected = mapOf("nested" to mapOf("key" to "value"))
            assertThat(actual).isEqualTo(expected)
        }
    }

    @Nested
    inner class ToWords {

        @Test
        fun `should convert camelCase string to words`() {
            val actual = "camelCase".toWords()
            val expected = "Camel Case"
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should convert dot notation string to words`() {
            val actual = "dot.notation".toWords()
            val expected = "Dot Notation"
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should convert combination of camelCase and dot notation string to words`() {
            val actual = "camelCase.dot.notation".toWords()
            val expected = "Camel Case Dot Notation"
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should convert CAPITAL_SNAKE_CASE to words`() {
            val actual = "CAPITAL_SNAKE_CASE".toWords()
            val expected = "Capital Snake Case"
            assertThat(actual).isEqualTo(expected)
        }
    }

    @Nested
    inner class ToCamelCase {

        @Test
        fun `should convert words to camelCase`() {
            val actual = "words to camel case".toCamelCase()
            val expected = "wordsToCamelCase"
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should convert Capital Words to camelCase`() {
            val actual = "Capital Words to camel case".toCamelCase()
            val expected = "capitalWordsToCamelCase"
            assertThat(actual).isEqualTo(expected)
        }
    }

    @Nested
    inner class ToSnakeCase {
        @Test
        fun `should convert camelCase to snake_case`() {
            val actual = "camelCase".toSnakeCase()
            val expected = "camel_case"
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should convert Capital Words to snake_case`() {
            val actual = "Capital Words".toSnakeCase()
            val expected = "capital_words"
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should convert dot notation to snake_case`() {
            val actual = "dot.notation".toSnakeCase()
            val expected = "dot_notation"
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should convert combination of camelCase and dot notation to snake_case`() {
            val actual = "camelCase.dot.notation".toSnakeCase()
            val expected = "camel_case_dot_notation"
            assertThat(actual).isEqualTo(expected)
        }

        @Test
        fun `should convert normal sentence to snake_case`() {
            val actual = "This is a normal sentence".toSnakeCase()
            val expected = "this_is_a_normal_sentence"
            assertThat(actual).isEqualTo(expected)
        }
    }

    @Nested
    inner class MapToProtoMessage {

        @Test
        fun `should convert a map to a proto message`() {
            val map =
                mapOf(
                    "companyId" to "123",
                    "legalEntityId" to "456",
                    "country" to "SGP",
                    "type" to "HR_MEMBER",
                    "term" to "PERMANENT",
                    "startOn->year" to "2024",
                    "startOn->month" to "1",
                    "startOn->day" to "1",
                )
            val expectedInput =
                CreateContractInput.newBuilder()
                    .setCompanyId(123)
                    .setLegalEntityId(456)
                    .setCountry(Country.GrpcCountryCode.SGP)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .setTerm(ContractOuterClass.ContractTerm.PERMANENT)
                    .setStartOn(Date.newBuilder().setYear(2024).setMonth(1).setDay(1))
                    .build()

            val createContractInput = map.toProtoMessage(CreateContractInput.newBuilder())

            assertThat(createContractInput).isEqualTo(expectedInput)
        }

        @Test
        fun `can convert multi-level nested map`() {
            val map =
                mapOf(
                    "requestId" to "24, 25",
                    "dynamicDetail->transferType" to "FIAT",
                    "dynamicDetail->sourceCurrency" to "SGD",
                    "dynamicDetail->targetCurrency" to "SGD",
                    "dynamicDetail->detailFields->bankName" to "BoS",
                    "dynamicDetail->detailFields->swiftCode" to "SWIFTCED",
                    "dynamicDetail->detailFields->accountNumber" to "*********",
                    "dynamicDetail->detailFields->accountHolderName" to "Test",
                    "dynamicDetail->detailFields->address.country" to "SG",
                    "dynamicDetail->paymentAccountRequirementType" to "Local Bank Account")
            val expectedInput = upsertBankDetailsInput {
                requestId = "24, 25"
                dynamicDetail = memberBankDynamicDetail {
                    transferType = TransferType.FIAT
                    sourceCurrency = "SGD"
                    targetCurrency = "SGD"
                    detailFields.putAll(
                        mapOf(
                            "bankName" to "BoS",
                            "swiftCode" to "SWIFTCED",
                            "accountNumber" to "*********",
                            "accountHolderName" to "Test",
                            "address.country" to "SG"))
                    paymentAccountRequirementType = "Local Bank Account"
                }
            }

            val upsertBankDetailsInput = map.toProtoMessage(UpsertBankDetailsInput.newBuilder())
            assertThat(upsertBankDetailsInput).isEqualTo(expectedInput)
        }

        @Test
        fun `can convert map with list to proto`() {
            val map =
                mapOf(
                    "requestId" to "request_id",
                    "contractId" to "123",
                    "complianceParams->[0]->key" to "probation",
                    "complianceParams->[0]->unit" to "DAYS",
                    "complianceParams->[0]->value" to "60",
                    "complianceParams->[1]->key" to "noticePeriod",
                    "complianceParams->[1]->unit" to "MONTHS",
                    "complianceParams->[1]->value" to "2")

            val expectedInput =
                BulkContract.UpdateComplianceInput.newBuilder()
                    .addComplianceParams(
                        0,
                        ComplianceParam.newBuilder()
                            .setKey("probation")
                            .setUnit("DAYS")
                            .setValue(60))
                    .addComplianceParams(
                        1,
                        ComplianceParam.newBuilder()
                            .setKey("noticePeriod")
                            .setUnit("MONTHS")
                            .setValue(2))
                    .setContractId(123L)
                    .setRequestId("request_id")
                    .build()

            val upsertComplianceInput =
                map.toProtoMessage(BulkContract.UpdateComplianceInput.newBuilder())
            assertThat(upsertComplianceInput).isEqualTo(expectedInput)
        }
    }

    @Nested
    inner class ProtoMessageToMap {

        @Test
        fun `should convert proto message to a map`() {
            val expectedResult =
                mapOf(
                    "companyId" to "123",
                    "legalEntityId" to "456",
                    "country" to "SGP",
                    "type" to "HR_MEMBER",
                    "term" to "PERMANENT",
                    "startOn->year" to "2024",
                    "startOn->month" to "1",
                    "startOn->day" to "1",
                )
            val protoMessage =
                CreateContractInput.newBuilder()
                    .setCompanyId(123)
                    .setLegalEntityId(456)
                    .setCountry(Country.GrpcCountryCode.SGP)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .setTerm(ContractOuterClass.ContractTerm.PERMANENT)
                    .setStartOn(Date.newBuilder().setYear(2024).setMonth(1).setDay(1))
                    .build()

            val protoMap = protoMessageToMap(protoMessage)

            assertThat(expectedResult).isEqualTo(protoMap)
        }

        @Test
        fun `can convert multi-level nested map`() {
            val expectedResult =
                mapOf(
                    "requestId" to "24, 25",
                    "dynamicDetail->transferType" to "FIAT",
                    "dynamicDetail->sourceCurrency" to "SGD",
                    "dynamicDetail->targetCurrency" to "SGD",
                    "dynamicDetail->detailFields->bankName" to "BoS",
                    "dynamicDetail->detailFields->swiftCode" to "SWIFTCED",
                    "dynamicDetail->detailFields->accountNumber" to "*********",
                    "dynamicDetail->detailFields->accountHolderName" to "Test",
                    "dynamicDetail->detailFields->address.country" to "SG",
                    "dynamicDetail->paymentAccountRequirementType" to "Local Bank Account")
            val protoMessage = upsertBankDetailsInput {
                requestId = "24, 25"
                dynamicDetail = memberBankDynamicDetail {
                    transferType = TransferType.FIAT
                    sourceCurrency = "SGD"
                    targetCurrency = "SGD"
                    detailFields.putAll(
                        mapOf(
                            "bankName" to "BoS",
                            "swiftCode" to "SWIFTCED",
                            "accountNumber" to "*********",
                            "accountHolderName" to "Test",
                            "address.country" to "SG"))
                    paymentAccountRequirementType = "Local Bank Account"
                }
            }

            val protoMap = protoMessageToMap(protoMessage)
            assertThat(expectedResult).isEqualTo(protoMap)
        }

        @Test
        fun `can convert proto with list`() {
            val expectedResult =
                mapOf(
                    "requestId" to "request_id",
                    "contractId" to "123",
                    "complianceParams->[0]->key" to "probation",
                    "complianceParams->[0]->unit" to "DAYS",
                    "complianceParams->[0]->value" to "60",
                    "complianceParams->[1]->key" to "noticePeriod",
                    "complianceParams->[1]->unit" to "MONTHS",
                    "complianceParams->[1]->value" to "2")

            val protoMessage =
                BulkContract.UpdateComplianceInput.newBuilder()
                    .addComplianceParams(
                        0,
                        ComplianceParam.newBuilder()
                            .setKey("probation")
                            .setUnit("DAYS")
                            .setValue(60))
                    .addComplianceParams(
                        1,
                        ComplianceParam.newBuilder()
                            .setKey("noticePeriod")
                            .setUnit("MONTHS")
                            .setValue(2))
                    .setContractId(123L)
                    .setRequestId("request_id")
                    .build()

            val protoMap = protoMessageToMap(protoMessage)
            assertThat(expectedResult).isEqualTo(protoMap)
        }
    }
}
