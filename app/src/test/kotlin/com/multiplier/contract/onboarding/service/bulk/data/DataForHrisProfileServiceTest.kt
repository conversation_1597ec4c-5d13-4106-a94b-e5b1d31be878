package com.multiplier.contract.onboarding.service.bulk.data

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.NoticeAfterProbationUnitSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.NoticeAfterProbationValueSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.MemberIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressCitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressCountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressLine1Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressLine2Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressPostalCodeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressStateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.DateOfBirthSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmergencyContactNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmergencyContactRelationshipSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.FirstNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerEndDateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerPositionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerStartDateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolDegreeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolGpaSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolYearOfPassingSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.MaritalStatusSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.NationalitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressCitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressCountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressLine1Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressLine2Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressPostalCodeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressStateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PhoneNumberSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEducationDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEmergencyDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEmploymentDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberLegalDataService.Companion.NationalIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberLegalDataService.Companion.PassportNumberSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberLegalDataService.Companion.ReligionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.ContractDataContext
import com.multiplier.contract.onboarding.service.bulk.mapper.MemberDataContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class DataForHrisProfileServiceTest {

    @MockK private lateinit var memberDataServiceMock: BulkMemberDataService

    @MockK private lateinit var contractDataServiceMock: BulkContractDataService

    @MockK private lateinit var emergencyDataServiceMock: BulkMemberEmergencyDataService

    @MockK private lateinit var bulkOrgManagementDataServiceMock: BulkOrgManagementDataService

    @MockK private lateinit var educationDataServiceMock: BulkMemberEducationDataService

    @MockK private lateinit var employmentDataService: BulkMemberEmploymentDataService

    @MockK private lateinit var complianceDataService: BulkComplianceDataService

    @MockK private lateinit var addressDataServiceMock: BulkMemberAddressDataService

    @InjectMockKs private lateinit var dataForHrisProfileService: DataForHrisProfileService

    @Nested
    inner class GetDataForSpecs {

        @Test
        fun `should return correct employee data`() {
            mockGetDataForSpecs()

            val expected =
                listOf(
                    EmployeeDataChunk(
                        mapOf(
                            MemberIdSpec.key to "1",
                            FirstNameSpec.key to "Johnny",
                            LastNameSpec.key to "Depp",
                            DateOfBirthSpec.key to "1990-01-01",
                            NationalitySpec.key to "VNM",
                            MaritalStatusSpec.key to "MARRIED",
                            PhoneNumberSpec.key to "+84 374 123 456",
                            ReligionSpec.key to "NONE",
                            NationalIdSpec.key to "079*********",
                            PassportNumberSpec.key to "*********",
                            EmergencyContactNameSpec.key to "Lily",
                            EmergencyContactRelationshipSpec.key to "Daughter",
                            LastEmployerNameSpec.key to "Google",
                            LastEmployerPositionSpec.key to "Software Engineer",
                            LastEmployerStartDateSpec.key to "2017-01-01",
                            LastEmployerEndDateSpec.key to "2022-12-31",
                            NoticeAfterProbationValueSpec.key to "4",
                            NoticeAfterProbationUnitSpec.key to "WEEKS",
                        )),
                    EmployeeDataChunk(
                        mapOf(
                            MemberIdSpec.key to "2",
                            FirstNameSpec.key to "Barry",
                            LastNameSpec.key to "Allen",
                            LastSchoolNameSpec.key to "Harvard",
                            LastSchoolDegreeSpec.key to "Computer Science",
                            LastSchoolYearOfPassingSpec.key to "2017",
                            LastSchoolGpaSpec.key to "4.0",
                            CurrentAddressLine1Spec.key to "266 DDN",
                            CurrentAddressLine2Spec.key to "Son Tra",
                            CurrentAddressCitySpec.key to "Da Nang",
                            CurrentAddressStateSpec.key to "Da Nang",
                            CurrentAddressCountrySpec.key to "Vietnam",
                            CurrentAddressPostalCodeSpec.key to "550000",
                            PermanentAddressLine1Spec.key to "196 TCV",
                            PermanentAddressLine2Spec.key to "Thanh Khe",
                            PermanentAddressCitySpec.key to "Da Nang",
                            PermanentAddressStateSpec.key to "Da Nang",
                            PermanentAddressCountrySpec.key to "Vietnam",
                            PermanentAddressPostalCodeSpec.key to "550002",
                        )))
            assertEquals(
                expected,
                dataForHrisProfileService.getDataForSpecs(
                    emptyList(), BulkOnboardingOptions(), null))
        }

        private fun mockGetDataForSpecs() {
            val memberId1 = 1L
            val memberId2 = 2L
            val memberId3 = 3L
            val memberId4 = 4L
            val memberId5 = 5L
            val memberId6 = 6L
            val contractId1 = 11L
            val contractId2 = 12L
            val contractId3 = 13L
            val contractId4 = 14L
            val contractId5 = 15L
            val contractId6 = 16L

            val contractDataContext =
                ContractDataContext(
                    mapOf(
                        contractId1 to
                            EmployeeDataChunk(mapOf(MemberIdSpec.key to memberId1.toString())),
                        contractId2 to
                            EmployeeDataChunk(mapOf(MemberIdSpec.key to memberId2.toString())),
                        contractId3 to
                            EmployeeDataChunk(mapOf(MemberIdSpec.key to memberId3.toString())),
                        contractId4 to
                            EmployeeDataChunk(mapOf(MemberIdSpec.key to memberId4.toString())),
                        contractId5 to
                            EmployeeDataChunk(mapOf(MemberIdSpec.key to memberId5.toString())),
                        contractId6 to
                            EmployeeDataChunk(mapOf(MemberIdSpec.key to memberId6.toString()))))
            every { contractDataServiceMock.getDataForSpecs(any(), any(), any()) } returns
                contractDataContext

            val memberDataChunk1 =
                EmployeeDataChunk(
                    mapOf(
                        FirstNameSpec.key to "Johnny",
                        LastNameSpec.key to "Depp",
                        DateOfBirthSpec.key to "1990-01-01",
                        NationalitySpec.key to "VNM",
                        MaritalStatusSpec.key to "MARRIED",
                        PhoneNumberSpec.key to "+84 374 123 456",
                        ReligionSpec.key to "NONE",
                        NationalIdSpec.key to "079*********",
                        PassportNumberSpec.key to "*********",
                    ))
            val memberDataChunk2 =
                EmployeeDataChunk(
                    mapOf(
                        FirstNameSpec.key to "Barry",
                        LastNameSpec.key to "Allen",
                    ))
            val memberDataContext =
                MemberDataContext(
                    mapOf(
                        memberId1 to memberDataChunk1,
                        memberId2 to memberDataChunk2,
                    ))
            every { memberDataServiceMock.getDataForSpecs(any(), any()) } returns memberDataContext

            val emergencyDataChunk1 =
                EmployeeDataChunk(
                    mapOf(
                        EmergencyContactNameSpec.key to "Lily",
                        EmergencyContactRelationshipSpec.key to "Daughter",
                    ))
            val emergencyDataChunk2 =
                EmployeeDataChunk(
                    mapOf(
                        EmergencyContactNameSpec.key to "Amber",
                        EmergencyContactRelationshipSpec.key to "Stranger",
                    ))
            val emergencyDataContext =
                MemberDataContext(
                    mapOf(
                        memberId1 to emergencyDataChunk1,
                        memberId3 to emergencyDataChunk2,
                    ))
            every { emergencyDataServiceMock.getDataForSpecs(any(), any()) } returns
                emergencyDataContext

            every { bulkOrgManagementDataServiceMock.getDataForSpecs(any(), any(), any()) } returns
                OrgManagementDataContext(emptyMap())

            val educationDataChunk1 =
                EmployeeDataChunk(
                    mapOf(
                        LastSchoolNameSpec.key to "Harvard",
                        LastSchoolDegreeSpec.key to "Computer Science",
                        LastSchoolYearOfPassingSpec.key to "2017",
                        LastSchoolGpaSpec.key to "4.0",
                    ))
            val educationDataChunk2 =
                EmployeeDataChunk(
                    mapOf(
                        LastSchoolNameSpec.key to "MIT",
                        LastSchoolDegreeSpec.key to "Information Technology",
                        LastSchoolYearOfPassingSpec.key to "2018",
                        LastSchoolGpaSpec.key to "3.9",
                    ))
            val educationDataContext =
                MemberDataContext(
                    mapOf(
                        memberId2 to educationDataChunk1,
                        memberId4 to educationDataChunk2,
                    ))
            every { educationDataServiceMock.getDataForSpecs(any(), any()) } returns
                educationDataContext

            val previousEmploymentDatChunk1 =
                EmployeeDataChunk(
                    mapOf(
                        LastEmployerNameSpec.key to "Google",
                        LastEmployerPositionSpec.key to "Software Engineer",
                        LastEmployerStartDateSpec.key to "2017-01-01",
                        LastEmployerEndDateSpec.key to "2022-12-31",
                    ))
            val previousEmploymentDatChunk2 =
                EmployeeDataChunk(
                    mapOf(
                        LastEmployerNameSpec.key to "Facebook",
                        LastEmployerPositionSpec.key to "Senior Software Engineer",
                        LastEmployerStartDateSpec.key to "2020-01-01",
                        LastEmployerEndDateSpec.key to "2024-04-30",
                    ))
            val previousEmploymentContext =
                MemberDataContext(
                    mapOf(
                        memberId1 to previousEmploymentDatChunk1,
                        memberId5 to previousEmploymentDatChunk2,
                    ))
            every { employmentDataService.getDataForSpecs(any(), any()) } returns
                previousEmploymentContext

            every { complianceDataService.getDataForSpecs(any(), any()) } returns
                ContractDataContext(
                    mapOf(
                        contractId1 to
                            EmployeeDataChunk(
                                mapOf(
                                    NoticeAfterProbationValueSpec.key to "4",
                                    NoticeAfterProbationUnitSpec.key to "WEEKS"))))

            val addressDataChunk1 =
                EmployeeDataChunk(
                    mapOf(
                        CurrentAddressLine1Spec.key to "266 DDN",
                        CurrentAddressLine2Spec.key to "Son Tra",
                        CurrentAddressCitySpec.key to "Da Nang",
                        CurrentAddressStateSpec.key to "Da Nang",
                        CurrentAddressCountrySpec.key to "Vietnam",
                        CurrentAddressPostalCodeSpec.key to "550000",
                        PermanentAddressLine1Spec.key to "196 TCV",
                        PermanentAddressLine2Spec.key to "Thanh Khe",
                        PermanentAddressCitySpec.key to "Da Nang",
                        PermanentAddressStateSpec.key to "Da Nang",
                        PermanentAddressCountrySpec.key to "Vietnam",
                        PermanentAddressPostalCodeSpec.key to "550002",
                    ))
            val addressDataChunk2 =
                EmployeeDataChunk(
                    mapOf(
                        CurrentAddressLine1Spec.key to "266 DDNN",
                        CurrentAddressLine2Spec.key to "Son Traa",
                        CurrentAddressCitySpec.key to "Da Nangg",
                        CurrentAddressStateSpec.key to "Da Nangg",
                        CurrentAddressCountrySpec.key to "Vietnam",
                        CurrentAddressPostalCodeSpec.key to "550001",
                        PermanentAddressLine1Spec.key to "196 TCVV",
                        PermanentAddressLine2Spec.key to "Thanh Khee",
                        PermanentAddressCitySpec.key to "Da Nangg",
                        PermanentAddressStateSpec.key to "Da Nangg",
                        PermanentAddressCountrySpec.key to "Vietnam",
                        PermanentAddressPostalCodeSpec.key to "550001",
                    ))
            val addressDataContext =
                MemberDataContext(
                    mapOf(
                        memberId2 to addressDataChunk1,
                        memberId6 to addressDataChunk2,
                    ))
            every { addressDataServiceMock.getDataForSpecs(any(), any()) } returns
                addressDataContext
        }
    }
}
