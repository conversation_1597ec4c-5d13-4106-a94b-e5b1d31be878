package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberLegalDataServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.member.ApplyTo
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataDefinition
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataType
import com.multiplier.contract.onboarding.types.*
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberLegalDataModuleTest {
    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter
    @MockK private lateinit var bulkMemberLegalDataAdapter: BulkMemberLegalDataServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkMemberLegalDataModule

    @Nested
    inner class GetDataSpecs {

        @Test
        fun `should return only CONTRACT_GENERATION stage and LEGAL_DATA data specs for EMPLOYEE contract type`() {
            val options =
                BulkOnboardingOptions(
                    CountryCode.IND, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

            val definition1 =
                MemberLegalDataDefinition(
                    key = "field1",
                    type = MemberLegalDataType.TEXT,
                    mandatory = true,
                    description = "Field 1 description",
                    fetchStage = FetchStage.CONTRACT_GENERATION,
                    applyTo = ApplyTo.ALL,
                    allowedValues = listOf(),
                    domainType = DomainType.LEGAL_DATA)

            val definition2 =
                MemberLegalDataDefinition(
                    key = "field2",
                    type = MemberLegalDataType.SELECT,
                    mandatory = true,
                    description = "Field 2 description",
                    fetchStage = FetchStage.MEMBER_LEGAL_DATA_CAPTURE, // should be excluded
                    applyTo = ApplyTo.ALL,
                    allowedValues = listOf("A", "B"),
                    domainType = DomainType.LEGAL_DATA)

            val definition3 =
                MemberLegalDataDefinition(
                    key = "field1", // duplicate key, should be deduped
                    type = MemberLegalDataType.TEXT,
                    mandatory = true,
                    description = "Field 1 again",
                    fetchStage = FetchStage.CONTRACT_GENERATION,
                    applyTo = ApplyTo.ALL,
                    allowedValues = listOf(),
                    domainType = DomainType.LEGAL_DATA)

            val definition4 =
                MemberLegalDataDefinition(
                    key = "field3",
                    type = MemberLegalDataType.TEXT,
                    mandatory = true,
                    description = "Field 1 again",
                    fetchStage = FetchStage.CONTRACT_GENERATION,
                    applyTo = ApplyTo.ALL,
                    allowedValues = listOf(),
                    domainType = DomainType.MEMBER_DATA) // should be filtered

            every {
                countryServiceAdapter.getMemberLegalDataDefinitions(
                    CountryCode.IND, ContractType.EMPLOYEE)
            } returns listOf(definition1, definition2, definition3, definition4)

            val expectedSpec =
                DataSpec(
                    key = "field1",
                    label = "Field 1 description",
                    mandatory = true,
                    source = "LEGAL_SPEC",
                    type = DataSpecType.TEXT)

            val result = underTest.getDataSpecs(options, null)

            assertThat(result).containsExactly(expectedSpec)
        }
    }
}
