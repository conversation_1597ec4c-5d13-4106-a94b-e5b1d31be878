package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.PayrollCyclesService
import com.multiplier.contract.onboarding.service.toGrpcDate
import com.multiplier.contract.onboarding.service.toLocalDate
import com.multiplier.contract.onboarding.service.toTimeStamp
import com.multiplier.contract.onboarding.testing.StringToIntegerListConverter
import com.multiplier.contract.onboarding.testing.getPayrollCycleDTO
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractStatus
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import io.mockk.every
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import java.time.*
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.converter.ConvertWith
import org.junit.jupiter.params.provider.CsvSource

@ExtendWith(MockKExtension::class)
class StartDateReminderContractFinderTest {

    private val onboardingServiceAdapter = mockk<OnboardingServiceAdapter>()
    private val contractServiceAdapter = mockk<ContractServiceAdapter>()
    private val payrollCyclesService = mockk<PayrollCyclesService>()
    private val compensationServiceAdapter = mockk<CompensationServiceAdapter>()
    private val featureFlagService = mockk<FeatureFlagService>()

    private val reminderContractFinderHelper =
        ReminderContractFinderHelper(
            onboardingServiceAdapter,
            contractServiceAdapter,
            payrollCyclesService,
            compensationServiceAdapter,
            featureFlagService,
        )

    private val underTest =
        StartDateReminderContractFinder(
            contractServiceAdapter,
            reminderContractFinderHelper,
        )

    @BeforeEach
    fun beforeEach() {
        every {
            payrollCyclesService.getPayrollCyclesForContracts(
                any<LocalDate>(), any<List<Contract>>())
        } answers
            {
                val sendingDate = firstArg<LocalDate>()
                val contracts = secondArg<List<Contract>>()

                contracts.associate { contract ->
                    contract.id to
                        getPayrollCycleDTO(
                            contract, sendingDate, PayrollCycleDTO.PayFrequency.MONTHLY)
                }
            }
    }

    @ParameterizedTest
    @CsvSource(
        "2023, 01, 14, '21'",
        "2023, 01, 15, '22'",
        "2023, 01, 16, '23'",
        "2023, 01, 17, '24'",
        "2023, 01, 18, '21, 25'",
        "2023, 01, 19, '22, 26'",
    )
    fun `should pick contract with start date before eligible payroll cutoff date and sending date not in payroll cutoff reminder date range`(
        year: Int,
        month: Int,
        day: Int,
        @ConvertWith(StringToIntegerListConverter::class) expectedStartDays: List<Int>
    ) {
        val sendingDate = LocalDate.of(year, month, day)
        val minStartDate = sendingDate.plusDays(3)
        val maxStartDate = sendingDate.plusDays(7)

        val contracts =
            mockEligibleContractWithStartOnRange(
                minStartDate, maxStartDate, LocalDate.of(2023, 1, 1))
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).hasSize(1)
        assertThat(result[0].payrollMonth.month).isEqualTo(Month.FEBRUARY)
        val pickedUpContractIds = result[0].contractIds
        val pickedUpStartDays =
            contracts
                .filter { pickedUpContractIds.contains(it.id) }
                .map { it.startOn.toLocalDate().dayOfMonth }

        assertThat(pickedUpStartDays).hasSameElementsAs(expectedStartDays)
    }

    @ParameterizedTest
    @CsvSource(
        "2023, 01, 25",
        "2023, 01, 26",
        "2023, 01, 31",
        "2023, 02,  1",
        "2023, 02,  9",
        "2023, 02, 10",
        "2023, 02, 11",
    )
    fun `should return empty list given sending date in payroll cutoff reminder date range`(
        year: Int,
        month: Int,
        day: Int,
    ) {
        val sendingDate = LocalDate.of(year, month, day)
        val minStartDate = sendingDate.plusDays(3)
        val maxStartDate = sendingDate.plusDays(7)

        mockEligibleContractWithStartOnRange(minStartDate, maxStartDate, LocalDate.of(2023, 1, 1))
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).isEmpty()
    }

    @ParameterizedTest
    @CsvSource(
        "2023, 11, 21",
        "2023, 11, 26",
        "2023, 11, 30",
        "2023, 12,  1",
        "2023, 12,  4",
        "2023, 12, 5",
    )
    fun `for December should return empty list given sending date in payroll cutoff reminder date range`(
        year: Int,
        month: Int,
        day: Int,
    ) {
        val sendingDate = LocalDate.of(year, month, day)
        val minStartDate = sendingDate.plusDays(3)
        val maxStartDate = sendingDate.plusDays(7)

        mockEligibleContractWithStartOnRange(minStartDate, maxStartDate, LocalDate.of(2023, 11, 1))
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).isEmpty()
    }

    @ParameterizedTest
    @CsvSource(
        "2023, 01, 16, '19, 23'",
        "2023, 01, 17, '20, 24'",
        "2023, 01, 18, '21, 25'",
        "2023, 01, 19, '22, 26'",
        "2023, 01, 20, '23, 27'",
        "2023, 01, 21, '24, 28'",
        "2023, 01, 22, '25, 29'",
    )
    fun `should pick contract created on or after payroll cutoff date but still eligible for that payroll then merge into next payroll cycle`(
        year: Int,
        month: Int,
        day: Int,
        @ConvertWith(StringToIntegerListConverter::class) expectedStartDays: List<Int>
    ) {
        val sendingDate = LocalDate.of(year, month, day)
        val minStartDate = sendingDate.plusDays(3)
        val maxStartDate = sendingDate.plusDays(7)

        val contracts =
            mockEligibleContractWithStartOnRange(
                minStartDate, maxStartDate, LocalDate.of(2023, 1, 16))
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).hasSize(1)
        assertThat(result[0].payrollMonth.month).isEqualTo(Month.FEBRUARY)
        val pickedUpContractIds = result[0].contractIds
        val pickedUpStartDays =
            contracts
                .filter { pickedUpContractIds.contains(it.id) }
                .map { it.startOn.toLocalDate().dayOfMonth }

        assertThat(pickedUpStartDays).hasSameElementsAs(expectedStartDays)
    }

    @ParameterizedTest
    @CsvSource(
        "2023, 12, 6, '9, 13'",
        "2023, 12, 7, '10, 14'",
        "2023, 12, 8, '11, 15'",
        "2023, 12, 9, '12, 16'",
        "2023, 12, 10, '13, 17'",
        "2023, 12, 11, '14, 18'",
        "2023, 12, 12, '15, 19'",
    )
    fun `for December should pick contract created on or after payroll cutoff date but still eligible for that payroll then merge into next payroll cycle`(
        year: Int,
        month: Int,
        day: Int,
        @ConvertWith(StringToIntegerListConverter::class) expectedStartDays: List<Int>
    ) {
        val sendingDate = LocalDate.of(year, month, day)
        val minStartDate = sendingDate.plusDays(3)
        val maxStartDate = sendingDate.plusDays(7)

        val contracts =
            mockEligibleContractWithStartOnRange(
                minStartDate, maxStartDate, LocalDate.of(2023, 12, 6))
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).hasSize(1)
        assertThat(result[0].payrollMonth.month).isEqualTo(Month.JANUARY)
        val pickedUpContractIds = result[0].contractIds
        val pickedUpStartDays =
            contracts
                .filter { pickedUpContractIds.contains(it.id) }
                .map { it.startOn.toLocalDate().dayOfMonth }

        assertThat(pickedUpStartDays).hasSameElementsAs(expectedStartDays)
    }

    @Test
    fun `should not pick contract created on payroll cutoff date given sending date is payroll cutoff date`() {
        val sendingDate = LocalDate.of(2023, 1, 10)
        val minStartDate = sendingDate.plusDays(3)
        val maxStartDate = sendingDate.plusDays(5)

        mockEligibleContractWithStartOnRange(minStartDate, maxStartDate, LocalDate.of(2023, 1, 10))
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).isEmpty()
    }

    @Test
    fun `for December should not pick contract created on payroll cutoff date given sending date is payroll cutoff date`() {
        val sendingDate = LocalDate.of(2023, 12, 5)
        val minStartDate = sendingDate.plusDays(3)
        val maxStartDate = sendingDate.plusDays(5)

        mockEligibleContractWithStartOnRange(minStartDate, maxStartDate, LocalDate.of(2023, 12, 5))
        turnFeatureFlagOn()

        val result = underTest.findBy(sendingDate, emptySet())

        assertThat(result).isEmpty()
    }

    private fun turnFeatureFlagOn() {
        every {
            featureFlagService.feature(eq(FeatureFlags.ONBOARDING_NOTIFICATION), any())
        } returns
            GBFeatureResult(
                value = true,
                on = true,
                off = false,
                source = GBFeatureSource.force,
                experiment = null,
                experimentResult = null)
    }

    private fun mockEligibleContractWithStartOnRange(
        minStartDate: LocalDate,
        maxStartDate: LocalDate,
        createdDate: LocalDate
    ): List<Contract> {
        val contracts: LinkedList<Contract> = LinkedList<Contract>()
        var contractId: Long = 1
        var startDate = minStartDate
        while (!startDate.isAfter(maxStartDate)) {
            contracts.add(
                Contract.newBuilder()
                    .setId(contractId++)
                    .setStatus(ContractStatus.ONBOARDING)
                    .setCompanyId(11L)
                    .setStartOn(startDate.atStartOfDay().toGrpcDate())
                    .setWorkStatus(ContractOuterClass.CountryWorkStatus.RESIDENT)
                    .setCreatedOn(createdDate.atStartOfDay().toTimeStamp())
                    .build())
            startDate = startDate.plusDays(1)
        }

        every { contractServiceAdapter.getContractsBy(any()) } returns contracts

        val contractIds = contracts.map { it.id }

        every { onboardingServiceAdapter.getAllOnboardingsByContractIds(any(), any()) } returns
            contractIds.associateWith {
                Onboarding(
                    id = 111L,
                    status = OnboardingStatus.CREATED,
                    currentStep = OnboardingStep.ONBOARDING_REVIEW,
                    contractId = it,
                    experience = "company",
                )
            }

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns
            contractIds.map {
                Compliance(
                    contractId = it,
                    agreementId = 123,
                    type = ComplianceType.MULTIPLIER,
                    agreementType = ContractAgreementType.MULTIPLIER_TEMPLATE,
                )
            }

        every { compensationServiceAdapter.getCurrentCompensationByContractIds(any()) } returns
            contractIds.associateWith {
                Compensation.newBuilder()
                    .setPostProbationBasePay(
                        CompensationOuterClass.CompensationPayComponent.newBuilder()
                            .setPayFrequency(
                                CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY)
                            .setAmountType(CompensationOuterClass.PayAmountType.FIXED_AMOUNT)
                            .build())
                    .build()
            }

        return contracts
    }
}
