package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkDataModule
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class BulkOnboardingUseCaseTest {

    @Test
    fun `should chain validation calls and use returned additional properties of previous validation for the next validation`() {
        setUpValidationMocks()

        underTest.validate(
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification = EmployeeIdentification(rowNumber = 1),
                            data = mapOf("key" to "value")))),
            BulkOnboardingOptions.newBuilder().build())

        verify {
            mockModule2.validate(
                withArg {
                    assertThat(it.first().data)
                        .containsAllEntriesOf(
                            mapOf("key" to "value", "module1AddedKey" to "value1"))
                },
                any(),
                any())
        }

        verify {
            mockModule3.validate(
                withArg {
                    assertThat(it.first().data)
                        .containsAllEntriesOf(
                            mapOf(
                                "key" to "value",
                                "module1AddedKey" to "value1",
                                "module2AddedKey" to "value2"))
                },
                any(),
                any())
        }
    }

    @Test
    fun `should chain creation calls and use returned creation results of previous creation for next creation`() {
        val validationResult =
            BulkValidationResultV2(
                validationResults =
                    mapOf(
                        "module2" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "2",
                                    success = true,
                                    input = "input2",
                                    errors = emptyList(),
                                )),
                        "module3" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "3",
                                    success = true,
                                    input = "input3",
                                    errors = emptyList(),
                                )),
                    ))

        every { mockModule1.create(any(), any(), any()) } returns
            BulkCreationResultV2(requestIdToContractId = mapOf("test1" to 1))

        every { mockModule2.create(any(), any(), any()) } returns
            BulkCreationResultV2(requestIdToMemberId = mapOf("test2" to 2))

        every { mockModule3.create(any(), any(), any()) } returns BulkCreationResultV2.EMPTY

        underTest.create(validationResult, BulkOnboardingOptions.newBuilder().build())

        verify {
            mockModule2.create(
                withArg { assertThat(it.first().input).isEqualTo("input2") },
                withArg { assertThat(it.getContractId("test1")).isEqualTo(1) },
                any())
        }

        verify {
            mockModule3.create(
                withArg { assertThat(it.first().input).isEqualTo("input3") },
                withArg { assertThat(it.getMemberId("test2")).isEqualTo(2) },
                any())
        }
    }

    private fun setUpValidationMocks() {
        every { mockModule1.validate(any(), any(), any()) } returns
            listOf(
                GrpcValidationResult(
                    validationId = "rn_1",
                    success = true,
                    input = "input",
                    errors = emptyList(),
                    additionalInputProperties = mapOf("module1AddedKey" to "value1")))

        every { mockModule2.validate(any(), any(), any()) } returns
            listOf(
                GrpcValidationResult(
                    validationId = "rn_1",
                    success = true,
                    input = "input",
                    errors = emptyList(),
                    additionalInputProperties = mapOf("module2AddedKey" to "value2")))

        every { mockModule3.validate(any(), any(), any()) } returns emptyList()
    }

    private val underTest =
        object : BulkOnboardingUseCase() {

            override fun getBulkOnboardingModules(): List<BulkDataModule> {
                return listOf(mockModule1, mockModule2, mockModule3)
            }
        }

    private val mockModule1 =
        mockk<BulkDataModule>().also { every { it.identifier() } returns "module1" }
    private val mockModule2 =
        mockk<BulkDataModule>().also { every { it.identifier() } returns "module2" }
    private val mockModule3 =
        mockk<BulkDataModule>().also { every { it.identifier() } returns "module3" }
}
