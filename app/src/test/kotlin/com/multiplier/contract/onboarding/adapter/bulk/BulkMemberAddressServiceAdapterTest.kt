package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressLine1Spec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.Address
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.BulkUpsertAddressResult
import com.multiplier.member.schema.BulkUpsertAddressesResponse
import com.multiplier.member.schema.BulkValidateRequest
import com.multiplier.member.schema.CountryCode
import com.multiplier.member.schema.MemberAddressDetails
import com.multiplier.member.schema.UpsertAddressInput
import com.multiplier.member.schema.UpsertAddressesValidationResult
import com.multiplier.member.schema.ValidateUpsertAddressesResponse
import com.multiplier.member.schema.ValidationInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberAddressServiceAdapterTest {

    @MockK private lateinit var bulkMemberServiceMock: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    @InjectMockKs
    private lateinit var bulkMemberAddressServiceAdapter: BulkMemberAddressServiceAdapter

    @Test
    fun `should return correct validation result when validate addresses`() {
        val employeeData =
            listOf(
                EmployeeData(
                    EmployeeIdentification(rowNumber = 2),
                    mapOf(CurrentAddressLine1Spec.key to "266 DDN")))
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                .build()

        val grpcRequest =
            BulkValidateRequest.newBuilder()
                .setContext(options.context.name)
                .addAllInputs(employeeData.map { it.toGrpc() })
                .build()
        val grpcResponse =
            ValidateUpsertAddressesResponse.newBuilder()
                .addAllValidationResults(
                    listOf(
                        UpsertAddressesValidationResult.newBuilder()
                            .setRequestId("2")
                            .setSuccess(true)
                            .addAllErrors(emptyList())
                            .build()))
                .build()
        every { bulkMemberServiceMock.validateUpsertAddresses(grpcRequest) } returns grpcResponse

        val expected =
            grpcResponse.validationResultsList.map {
                GrpcValidationResult(
                    success = it.success,
                    errors = it.errorsList,
                    validationId = it.requestId,
                    input = it.validatedInput,
                )
            }

        assertEquals(
            expected,
            bulkMemberAddressServiceAdapter.validateUpsertAddresses(employeeData, options))
    }

    @Nested
    inner class UpsertAddresses {

        private val inputs =
            listOf(
                CreationInput(
                    requestId = "2",
                    memberId = 1,
                    data =
                        UpsertAddressInput.newBuilder()
                            .setAddressDetails(
                                MemberAddressDetails.newBuilder()
                                    .setCurrentAddress(
                                        Address.newBuilder()
                                            .setKey("Current")
                                            .setLine1("266 DDN")
                                            .setCity("Da Nang")
                                            .setState("Da Nang")
                                            .setProvince("Da Nang")
                                            .setCountry(CountryCode.VNM)
                                            .setPostalCode("550000")
                                            .setZipcode("550001")
                                            .build())
                                    .setPermanentAddress(
                                        Address.newBuilder()
                                            .setKey("Permanent")
                                            .setLine1("196 TCV")
                                            .setCity("Da Nang 1")
                                            .setState("Da Nang 1")
                                            .setProvince("Da Nang 1")
                                            .setCountry(CountryCode.VNM)
                                            .setPostalCode("550002")
                                            .setZipcode("550003")
                                            .build()))
                            .build()))

        @Test
        fun `should return empty error list when upsert addresses successfully`() {
            val grpcResponse =
                BulkUpsertAddressesResponse.newBuilder()
                    .addAllResults(
                        listOf(BulkUpsertAddressResult.newBuilder().setSuccess(true).build()))
                    .build()
            every { bulkMemberServiceMock.bulkUpsertAddresses(any()) } returns grpcResponse
            assertEquals(
                emptyList<String>(),
                bulkMemberAddressServiceAdapter.upsertAddresses(inputs).flatMap { it.errors })
            verify {
                bulkMemberServiceMock.bulkUpsertAddresses(
                    withArg { grpcRequest ->
                        val firstInput = grpcRequest.inputsList.first()
                        assertAll(
                            { assertThat(grpcRequest.inputsList).hasSize(1) },
                            { assertThat(firstInput.requestId).isEqualTo("2") },
                            { assertThat(firstInput.addressDetails.memberId).isEqualTo(1) },
                            {
                                assertThat(firstInput.addressDetails.currentAddress.line1)
                                    .isEqualTo("266 DDN")
                            },
                            {
                                assertThat(firstInput.addressDetails.permanentAddress.line1)
                                    .isEqualTo("196 TCV")
                            })
                    })
            }
        }

        @Test
        fun `should return empty error list when upsert addresses with empty parameter`() {
            assertEquals(
                emptyList<String>(), bulkMemberAddressServiceAdapter.upsertAddresses(emptyList()))
        }

        @Test
        fun `should return error list when upsert addresses`() {
            val errorMessages = listOf("Some error")
            val grpcResponse =
                BulkUpsertAddressesResponse.newBuilder()
                    .addAllResults(
                        listOf(
                            BulkUpsertAddressResult.newBuilder()
                                .setSuccess(false)
                                .addAllErrors(errorMessages)
                                .build()))
                    .build()
            every { bulkMemberServiceMock.bulkUpsertAddresses(any()) } returns grpcResponse
            assertEquals(
                errorMessages,
                bulkMemberAddressServiceAdapter.upsertAddresses(inputs).flatMap { it.errors })
        }

        @Test
        fun `should return error list when upsert addresses with error occurs`() {
            every { bulkMemberServiceMock.bulkUpsertAddresses(any()) } throws
                IllegalArgumentException()

            val expected =
                listOf(
                    "Upsert address details failed due to an internal error: unknown exception occurred")
            assertEquals(
                expected,
                bulkMemberAddressServiceAdapter.upsertAddresses(inputs).flatMap { it.errors })
        }
    }
}

private fun EmployeeData.toGrpc(): ValidationInput =
    ValidationInput.newBuilder()
        .setRequestId(this.identification.validationId)
        .putAllProperties(this.data)
        .build()
