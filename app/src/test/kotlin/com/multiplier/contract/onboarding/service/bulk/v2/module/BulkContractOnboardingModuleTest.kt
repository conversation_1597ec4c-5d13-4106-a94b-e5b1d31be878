package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.google.type.Date
import com.multiplier.contract.onboarding.adapter.ContractBenefitAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.OnboardingServiceAdapter
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.ComplianceType
import com.multiplier.contract.onboarding.domain.model.ContractAgreementType
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.OnboardingTasksServiceTest.Companion.CONTRACT_ID
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.schema.contract.ContractOuterClass
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkContractOnboardingModuleTest {
    @MockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter

    @MockK private lateinit var contractBenefitAdapter: ContractBenefitAdapter

    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK private lateinit var featureFlagService: FeatureFlagService

    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkContractOnboardingModule

    @Test
    fun `should create onboarding for freelancer when benefits are enabled`() {
        val options =
            BulkOnboardingOptions(
                null, ContractType.FREELANCER, 1, null, BulkOnboardingContext.FREELANCER)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("SGP")
                    .build())

        every { contractBenefitAdapter.getBenefitRestrictedCountryListFR() } returns
            listOf(CountryCode.IND)

        underTest.create(emptyList(), bulkCreationResult, options)

        verify {
            onboardingServiceAdapter.upsertOnboardings(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().currentStep)
                        .isEqualTo(OnboardingStep.DEFINITION_BENEFITS_DETAILS)
                })
        }
    }

    @Test
    fun `should create onboarding for freelancer when benefits are disabled`() {
        val options =
            BulkOnboardingOptions(
                null, ContractType.FREELANCER, 1, null, BulkOnboardingContext.FREELANCER)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("IND")
                    .build())

        every { contractBenefitAdapter.getBenefitRestrictedCountryListFR() } returns
            listOf(CountryCode.IND)

        underTest.create(emptyList(), bulkCreationResult, options)

        verify {
            onboardingServiceAdapter.upsertOnboardings(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().currentStep)
                        .isEqualTo(OnboardingStep.DEFINITION_COMPENSATION_DETAILS)
                })
        }
    }

    @Test
    fun `should create onboarding for eor without visa flow for a resident`() {
        val options =
            BulkOnboardingOptions(null, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setWorkStatus(ContractOuterClass.CountryWorkStatus.RESIDENT)
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("IND")
                    .build())

        every {
            featureFlagService.isFeatureOn(FeatureFlags.ONBOARDING_VISA_WORKFLOW, any())
        } returns true

        every { countryServiceAdapter.isOnboardingEnabled(any()) } returns true

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns
            listOf(
                com.multiplier.contract.onboarding.domain.model.Compliance(
                    contractId = CONTRACT_ID,
                    agreementType = ContractAgreementType.MULTIPLIER_TEMPLATE,
                    type = ComplianceType.MULTIPLIER,
                    agreementId = 999,
                ))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        underTest.create(emptyList(), bulkCreationResult, options)

        verify {
            onboardingServiceAdapter.upsertOnboardings(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().currentStep).isEqualTo(OnboardingStep.ONBOARDING_REVIEW)
                    assertThat(it.first().status).isEqualTo(OnboardingStatus.CREATED)
                    assertThat(it.first().isBulkOnboarded).isEqualTo(true)
                })
        }
    }

    @Test
    fun `should create onboarding for eor with visa flow when member non resident and visa flag enabled`() {
        val options =
            BulkOnboardingOptions(null, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setWorkStatus(ContractOuterClass.CountryWorkStatus.REQUIRES_VISA)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("IND")
                    .build())

        every {
            featureFlagService.isFeatureOn(FeatureFlags.ONBOARDING_VISA_WORKFLOW, any())
        } returns true

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        underTest.create(emptyList(), bulkCreationResult, options)

        verify {
            onboardingServiceAdapter.upsertOnboardings(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().currentStep)
                        .isEqualTo(OnboardingStep.ONBOARDING_CONTRACT_PREPARING_CONFIRMATION)
                    assertThat(it.first().status)
                        .isEqualTo(OnboardingStatus.CONTRACT_PREPARING_CONFIRMATION)
                    assertThat(it.first().isBulkOnboarded).isEqualTo(true)
                })
        }
    }

    @Test
    fun `should create onboarding for eor with offline visa flow when member non resident and visa flag disabled`() {
        val options =
            BulkOnboardingOptions(null, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setWorkStatus(ContractOuterClass.CountryWorkStatus.REQUIRES_VISA)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("IND")
                    .build())

        every {
            featureFlagService.isFeatureOn(FeatureFlags.ONBOARDING_VISA_WORKFLOW, any())
        } returns false

        every { countryServiceAdapter.isOnboardingEnabled(any()) } returns true

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns
            listOf(
                com.multiplier.contract.onboarding.domain.model.Compliance(
                    contractId = 123,
                    agreementType = ContractAgreementType.MULTIPLIER_TEMPLATE,
                    type = ComplianceType.MULTIPLIER,
                    agreementId = 999,
                ))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        underTest.create(emptyList(), bulkCreationResult, options)

        verify {
            onboardingServiceAdapter.upsertOnboardings(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().currentStep)
                        .isEqualTo(OnboardingStep.ONBOARDING_CONTRACT_PREPARING_CONFIRMATION)
                    assertThat(it.first().status)
                        .isEqualTo(OnboardingStatus.CONTRACT_PREPARING_CONFIRMATION)
                    assertThat(it.first().isBulkOnboarded).isEqualTo(true)
                })
        }
    }

    @Test
    fun `should create onboarding for eor with review step when member resident`() {
        val options =
            BulkOnboardingOptions(null, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setWorkStatus(ContractOuterClass.CountryWorkStatus.RESIDENT)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("IND")
                    .build())

        every {
            featureFlagService.isFeatureOn(FeatureFlags.ONBOARDING_VISA_WORKFLOW, any())
        } returns true

        every { countryServiceAdapter.isOnboardingEnabled(any()) } returns true

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns
            listOf(
                com.multiplier.contract.onboarding.domain.model.Compliance(
                    contractId = 123,
                    agreementType = ContractAgreementType.CUSTOM_TEMPLATE,
                    type = ComplianceType.MULTIPLIER,
                    agreementId = 999,
                ))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        underTest.create(emptyList(), bulkCreationResult, options)

        verify {
            onboardingServiceAdapter.upsertOnboardings(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().currentStep).isEqualTo(OnboardingStep.ONBOARDING_REVIEW)
                    assertThat(it.first().status).isEqualTo(OnboardingStatus.CREATED)
                    assertThat(it.first().isBulkOnboarded).isEqualTo(true)
                })
        }
    }

    @Test
    fun `should create onboarding for eor with offline flow when member resident and country not enabled with multiplier`() {
        val options =
            BulkOnboardingOptions(null, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setWorkStatus(ContractOuterClass.CountryWorkStatus.RESIDENT)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("IND")
                    .build())

        every {
            featureFlagService.isFeatureOn(FeatureFlags.ONBOARDING_VISA_WORKFLOW, any())
        } returns true

        every { countryServiceAdapter.isOnboardingEnabled(any()) } returns false

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns
            listOf(
                com.multiplier.contract.onboarding.domain.model.Compliance(
                    contractId = CONTRACT_ID,
                    agreementType = ContractAgreementType.MULTIPLIER_TEMPLATE,
                    type = ComplianceType.MULTIPLIER,
                    agreementId = 999,
                ))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        underTest.create(emptyList(), bulkCreationResult, options)

        verify {
            onboardingServiceAdapter.upsertOnboardings(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().currentStep)
                        .isEqualTo(OnboardingStep.ONBOARDING_CONTRACT_PREPARING_CONFIRMATION)
                    assertThat(it.first().status)
                        .isEqualTo(OnboardingStatus.CONTRACT_PREPARING_CONFIRMATION)
                    assertThat(it.first().isBulkOnboarded).isEqualTo(true)
                })
        }
    }

    @Test
    fun `should create onboarding for eor without visa flow when member is citizen but visa flow not enabled`() {
        val options =
            BulkOnboardingOptions(null, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setWorkStatus(ContractOuterClass.CountryWorkStatus.RESIDENT)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("IND")
                    .build())

        every {
            featureFlagService.isFeatureOn(FeatureFlags.ONBOARDING_VISA_WORKFLOW, any())
        } returns false

        every { countryServiceAdapter.isOnboardingEnabled(any()) } returns true

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns
            listOf(
                com.multiplier.contract.onboarding.domain.model.Compliance(
                    contractId = CONTRACT_ID,
                    agreementType = ContractAgreementType.MULTIPLIER_TEMPLATE,
                    type = ComplianceType.MULTIPLIER,
                    agreementId = 999,
                ))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        underTest.create(emptyList(), bulkCreationResult, options)

        verify {
            onboardingServiceAdapter.upsertOnboardings(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().currentStep).isEqualTo(OnboardingStep.ONBOARDING_REVIEW)
                    assertThat(it.first().status).isEqualTo(OnboardingStatus.CREATED)
                    assertThat(it.first().isBulkOnboarded).isEqualTo(true)
                })
        }
    }

    @Test
    fun `should create onboarding for aor`() {
        val options =
            BulkOnboardingOptions(null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR)

        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("IND")
                    .build())

        every { contractBenefitAdapter.getBenefitRestrictedCountryListFR() } returns
            listOf(CountryCode.IND)

        underTest.create(emptyList(), bulkCreationResult, options)

        verify {
            onboardingServiceAdapter.upsertOnboardings(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().currentStep).isEqualTo(OnboardingStep.ONBOARDING_REVIEW)
                })
        }
    }

    //    @Test
    //    fun `should create onboarding for aor with description of work`() {
    //        val options =
    //            BulkOnboardingOptions(null, ContractType.CONTRACTOR, 1, null,
    // BulkOnboardingContext.AOR)
    //
    //        val bulkCreationResult = BulkCreationResultV2(newContractIds = listOf(123))
    //
    //        every { onboardingServiceAdapter.upsertOnboardings(any()) }
    //
    //        every { contractServiceAdapter.getContractsBy(any()) } returns
    //            listOf(
    //                ContractOuterClass.Contract.newBuilder()
    //                    .setId(123)
    //                    .setEmployeeId("EMP123")
    //                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
    //                    .setMemberId(222)
    //                    .setCountry("IND")
    //                    .build())
    //
    //        every { contractBenefitAdapter.getBenefitRestrictedCountryListFR() } returns
    //            listOf(CountryCode.IND)
    //
    //        every { featureFlagService.isFeatureOn(FeatureFlags.DESCRIPTION_OF_WORK, any()) }
    // returns
    //            true
    //
    //        underTest.create(emptyList(), bulkCreationResult, options)
    //
    //        verify {
    //            onboardingServiceAdapter.upsertOnboardings(
    //                withArg {
    //                    assertThat(it).hasSize(1)
    //                    assertThat(it.first().currentStep)
    //                        .isEqualTo(OnboardingStep.DEFINITION_COMPLIANCE_DETAILS)
    //                })
    //        }
    //    }

    @Test
    fun `should throw error when contract not found`() {
        val options =
            BulkOnboardingOptions(
                null, ContractType.FREELANCER, 1, null, BulkOnboardingContext.FREELANCER)

        val bulkCreationResult =
            BulkCreationResultV2(
                newContractIds = listOf(1221), requestIdToContractId = mapOf("11" to 1221))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("IND")
                    .build())

        every { contractBenefitAdapter.getBenefitRestrictedCountryListFR() } returns
            listOf(CountryCode.IND)

        val result = underTest.create(emptyList(), bulkCreationResult, options)

        assertEquals(
            listOf(
                "Create onboarding entity failed due to an internal error: unknown exception occurred"),
            result.errors.first().errors)
    }

    @Test
    fun `should throw error invalid country code`() {
        val options =
            BulkOnboardingOptions(
                null, ContractType.FREELANCER, 1, null, BulkOnboardingContext.FREELANCER)

        val bulkCreationResult =
            BulkCreationResultV2(
                newContractIds = listOf(123), requestIdToContractId = mapOf("11" to 123))

        every { onboardingServiceAdapter.upsertOnboardings(any()) }

        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .setCountry("INDI")
                    .build())

        every { contractBenefitAdapter.getBenefitRestrictedCountryListFR() } returns
            listOf(CountryCode.IND)

        val result = underTest.create(emptyList(), bulkCreationResult, options)

        assertEquals(
            listOf(
                "Create onboarding entity failed due to an internal error: unknown exception occurred"),
            result.errors.first().errors)
    }
}
