package com.multiplier.contract.onboarding.service.helpers

import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.repository.model.JpaOnboarding
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus

object JpaOnboardingTestDataFactory {
    fun createJpaOnboarding(
        id: Long = 2L,
        contractId: Long = 123L,
        experience: String = "company",
        currentStep: OnboardingStep = OnboardingStep.ONBOARDING_SIGNING,
        status: ContractOnboardingStatus = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT
    ): JpaOnboarding {
        return JpaOnboarding(
            id = id,
            contractId = contractId,
            experience = experience,
            currentStep = currentStep,
            status = status)
    }
}
