package com.multiplier.contract.onboarding.domain

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll

class OnboardingStepTest {

    @Test
    fun contains_all_values_from_external_ContractOnboardingStep() {
        val domainValueNames = OnboardingStep.values().map { it.name }.toSet()

        assertAll(
            {
                assertEquals(
                    setOf("UNRECOGNIZED"),
                    com.multiplier.contract.schema.onboarding.Onboarding.ContractOnboardingStep
                        .values()
                        .map { it.name }
                        .toSet() - domainValueNames)
            },
            {
                assertEquals(
                    emptySet<String>(),
                    com.multiplier.contract.onboarding.types.ContractOnboardingStep.values()
                        .map { it.name }
                        .toSet() - domainValueNames)
            })
    }
}
