package com.multiplier.contract.onboarding.testing

import com.multiplier.contract.onboarding.domain.model.PayrollCycleDTO
import com.multiplier.contract.onboarding.service.toLocalDate
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import java.time.LocalDate
import java.time.YearMonth

private fun getCutoffDayOfMonth(month: Int): Int = if (month == 12) 5 else 15

fun getPayrollCycleDTO(
    contract: Contract,
    sendingDate: LocalDate,
    payFrequency: PayrollCycleDTO.PayFrequency = PayrollCycleDTO.PayFrequency.MONTHLY,
): PayrollCycleDTO {
    val startDate = contract.startOn.toLocalDate()

    val cutoffDate =
        startDate
            .let { date -> if (date.dayOfMonth >= 21) date.plusMonths(1) else date }
            .let { date ->
                if (date.monthValue == sendingDate.monthValue - 1 &&
                    sendingDate.dayOfMonth <= getCutoffDayOfMonth(sendingDate.monthValue))
                    date.plusMonths(1)
                else date
            }
            .let { date -> date.withDayOfMonth(getCutoffDayOfMonth(date.monthValue)) }

    val payrollMonth = YearMonth.from(cutoffDate)

    return PayrollCycleDTO(
        payFrequency = payFrequency,
        payrollMonth = payrollMonth,
        cutoffDate = cutoffDate,
        startDate = startDate,
        endDate = startDate,
    )
}
