package com.multiplier.contract.onboarding.testing

import com.fasterxml.jackson.databind.ObjectMapper
import com.multiplier.contract.onboarding.utils.getResourceAsText
import com.netflix.graphql.dgs.client.GraphQLResponse
import com.netflix.graphql.dgs.client.MonoGraphQLClient
import io.github.serpro69.kfaker.Faker
import java.io.File
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.ResponseEntity
import org.springframework.util.LinkedMultiValueMap
import org.springframework.web.client.RestTemplate
import org.springframework.web.reactive.function.client.WebClient

val faker = Faker()

data class UserContext(
    val companyId: Long? = null,
)

data class HttpConfig(
    val userId: Long? = faker.random.nextLong(),
    val userName: String = faker.name.name(),
    val permissions: List<String>? = null,
    val userContext: UserContext? = null,
    val experience: String = "operations",
)

class HttpClient(val port: Int) {

    val objectMapper = ObjectMapper()

    inline fun <reified T> post(httpConfig: HttpConfig): ResponseEntity<T> {
        return RestTemplate()
            .postForEntity(
                "http://localhost:$port/graphql",
                HttpEntity("body", HttpHeaders().addUserContext(httpConfig)),
                T::class.java)
    }

    inline fun <reified T> graphQLFileUpload(
        httpConfig: HttpConfig = HttpConfig(),
        query: String,
        variables: Map<String, Any>? = null,
        file: File,
        fileParamName: String = "file"
    ): ResponseEntity<T> {
        val variablesWithFile =
            if (variables == null) mapOf(fileParamName to null)
            else variables + (fileParamName to null)
        val q = getResourceAsText(query) ?: query

        val params = mapOf("variables" to variablesWithFile, "query" to q)

        val bytes = file.readBytes()

        val resource =
            object : ByteArrayResource(bytes) {
                override fun getFilename(): String = file.name
            }

        val formData = LinkedMultiValueMap<String, Any>()
        formData.set("operations", objectMapper.writeValueAsString(params))
        formData.set("map", "{\"file\": [\"variables.${fileParamName}\"]}")
        formData.set("file", resource)

        return RestTemplate()
            .postForEntity(
                "http://localhost:$port/graphql",
                HttpEntity(formData, HttpHeaders().addUserContext(httpConfig).addPreflightHeader()),
                T::class.java)
    }
}

class GraphQLTestClient(port: Int) {
    private val webClient: WebClient =
        WebClient.builder()
            .baseUrl("http://localhost:$port/graphql")
            .codecs { it.defaultCodecs().maxInMemorySize(10 * 1024 * 1024) }
            .build()

    fun execute(
        query: String,
        variables: Map<String, Any>? = null,
        config: HttpConfig = HttpConfig(),
    ): GraphQLResponse {
        val client =
            MonoGraphQLClient.createWithWebClient(webClient) { headers ->
                headers.addUserContext(config)
            }

        val q = getResourceAsText(query) ?: query

        if (variables != null) {
            return client.reactiveExecuteQuery(q, variables).block()!!
        }

        return client.reactiveExecuteQuery(q).block()!!
    }
}

fun HttpHeaders.addPreflightHeader(): HttpHeaders {
    this["apollo-require-preflight"] = faker.random.randomString()

    return this
}

fun HttpHeaders.addUserContext(httpConfig: HttpConfig): HttpHeaders {
    this["user-context-user-name"] = httpConfig.userName

    httpConfig.userId?.let { this["user-context-user-id"] = it.toString() }
    httpConfig.userContext?.companyId?.let { this["user-context-company-id"] = it.toString() }

    httpConfig.permissions?.let {
        this["user-context-auth"] = httpConfig.permissions.joinToString(",")
    }
    this["user-context-experience"] = httpConfig.experience
    this["current-experience"] = httpConfig.experience

    return this
}
