package com.multiplier.contract.onboarding.domain

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll

class OnboardingStatusTest {

    @Test
    fun contains_all_values_from_external_ContractOnboardingStatus() {
        val domainValueNames = OnboardingStatus.values().map { it.name }.toSet()

        assertAll(
            {
                assertEquals(
                    setOf<String>("UNRECOGNIZED"),
                    com.multiplier.contract.schema.onboarding.Onboarding.ContractOnboardingStatus
                        .values()
                        .map { it.name }
                        .toSet() - domainValueNames)
            },
            {
                assertEquals(
                    setOf<String>("UNRECOGNIZED"),
                    com.multiplier.contract.schema.onboarding.Onboarding.ContractOnboardingStatus
                        .values()
                        .map { it.name }
                        .toSet() - domainValueNames)
            },
            {
                assertEquals(
                    setOf<String>(),
                    com.multiplier.contract.onboarding.types.ContractOnboardingStatus.values()
                        .map { it.name }
                        .toSet() - domainValueNames)
            })
    }
}
