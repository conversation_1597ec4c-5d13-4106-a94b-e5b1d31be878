package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.OnboardingServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkContractServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.PageRequest
import com.multiplier.contract.schema.contract.ContractOuterClass
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.apache.commons.collections4.MapUtils
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkContractDataServiceTest {

    @MockK private lateinit var bulkContractServiceAdapter: BulkContractServiceAdapter
    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter
    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter
    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkContractDataService

    @Nested
    inner class GetDataSpecs {

        @Test
        fun `Should include position`() {
            val position = "MULTIPLIER ETD"
            val id = 512345L
            every { companyServiceAdapter.getCompanyLegalEntities(any()) } returns emptyList()
            every { contractServiceAdapter.getContractsBy(any()) } returns
                arrayListOf(
                    ContractOuterClass.Contract.newBuilder()
                        .setId(id)
                        .setPosition(position)
                        .build())

            val contractData =
                underTest.getDataForSpecs(
                    arrayListOf(
                        DataSpec(key = "position", type = DataSpecType.TEXT, mandatory = true)),
                    BulkOnboardingOptions.newBuilder()
                        .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                        .companyId(1L)
                        .build(),
                    PageRequest.newBuilder().pageSize(100).pageNumber(0).build())

            assertThat(contractData).isNotNull
            assertThat(contractData.contractIdToContractData[id]).isNotNull
            assertThat(
                    MapUtils.getString(contractData.contractIdToContractData[id]?.data, "position"))
                .isEqualTo(position)
        }

        @Nested
        inner class HRISCase {

            @BeforeEach
            fun setup() {
                every { companyServiceAdapter.getCompanyLegalEntities(any()) } returns
                    listOf(
                        CompanyOuterClass.LegalEntity.newBuilder()
                            .setId(101L)
                            .setLegalName("entity 1")
                            .build(),
                        CompanyOuterClass.LegalEntity.newBuilder()
                            .setId(102L)
                            .setLegalName("entity 2")
                            .build(),
                    )
            }

            @Test
            fun `should return data specs for hris profile context`() {
                val dataSpecs =
                    underTest.getDataSpecs(
                        BulkOnboardingOptions(
                            CountryCode.SGP,
                            ContractType.HR_MEMBER,
                            1,
                            2,
                            BulkOnboardingContext.HRIS_PROFILE_DATA))

                val dataSpecsByKey = dataSpecs.associateBy { it.key }

                assertThat(dataSpecsByKey.keys)
                    .containsAll(listOf("employeeId", "contractId", "legalEntityId", "startOn"))

                assertThat(dataSpecsByKey.getValue("legalEntityId").allowedValues)
                    .hasSameElementsAs(listOf("101 - entity 1", "102 - entity 2"))
            }
        }
    }
}
