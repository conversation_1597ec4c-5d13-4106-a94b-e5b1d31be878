package com.multiplier.contract.onboarding.service.bulkupload

import com.google.type.Date
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.BASIC_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.COMPENSATION_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.COMPLIANCE_AND_LEAVES_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.CONTRACTOR_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.EMPLOYMENT_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.MEMBER_DATA_GROUP
import com.multiplier.contract.onboarding.service.bulk.v2.BulkValidationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkContractModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberLegalDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberModule
import com.multiplier.contract.onboarding.service.bulkupload.ValidationResultHelper.Companion.mergeValidationResults
import com.multiplier.contract.onboarding.service.bulkupload.ValidationResultHelper.Companion.unmergeToBulkValidationResults
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode as ContractCountryCode
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.CreateContractInput
import com.multiplier.country.schema.Country
import com.multiplier.member.schema.Address
import com.multiplier.member.schema.CountryCode
import com.multiplier.member.schema.Gender
import com.multiplier.member.schema.MemberBankDynamicDetail
import com.multiplier.member.schema.MemberCreateInput
import com.multiplier.member.schema.PhoneNumer
import com.multiplier.member.schema.TransferType
import com.multiplier.member.schema.UpdateMemberLegalDataInput
import com.multiplier.member.schema.UpsertBankDetailsInput
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ValidationResultHelperTest {

    @Nested
    inner class MergeValidationResults {

        @Test
        fun `should merge validated when employee id is not present based on row identifier`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "1",
                        companyId = 0,
                        data =
                            mapOf(
                                "type" to "HR_MEMBER",
                            )),
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "name" to "name",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_1_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setType(ContractOuterClass.ContractType.HR_MEMBER)
                                            .build(),
                                    groupName = EMPLOYMENT_DATA_GROUP),
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setType(ContractOuterClass.ContractType.HR_MEMBER)
                                            .build(),
                                    groupName = EMPLOYMENT_DATA_GROUP)),
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_1_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        MemberCreateInput.newBuilder()
                                            .setFirstName("John")
                                            .setLastName("Doe")
                                            .build(),
                                    groupName = MEMBER_DATA_GROUP),
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        MemberCreateInput.newBuilder()
                                            .setFirstName("John")
                                            .setLastName("Doe1")
                                            .build(),
                                    groupName = MEMBER_DATA_GROUP)),
                        "COMPENSATION_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_1_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        mapOf(
                                            "componentName" to "Base pay",
                                            "amount" to "1000",
                                            "currency" to "USD",
                                        ),
                                    groupName = COMPENSATION_DATA_GROUP),
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        mapOf(
                                            "componentName" to "Base pay",
                                            "amount" to "10000",
                                            "currency" to "USD",
                                        ),
                                    groupName = COMPENSATION_DATA_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_1_rn_0"),
                        success = true,
                        validatedData =
                            mapOf(
                                "CONTRACT_MODULE->companyId" to "1",
                                "CONTRACT_MODULE->type" to "HR_MEMBER",
                                "CONTRACT_MODULE->dataClass" to
                                    getGrpcClassName(CreateContractInput.newBuilder().build()),
                                "MEMBER_MODULE->firstName" to "John",
                                "MEMBER_MODULE->lastName" to "Doe",
                                "MEMBER_MODULE->dataClass" to
                                    getGrpcClassName(MemberCreateInput.newBuilder().build()),
                                "COMPENSATION_MODULE->componentName" to "Base pay",
                                "COMPENSATION_MODULE->amount" to "1000",
                                "COMPENSATION_MODULE->currency" to "USD",
                                "COMPENSATION_MODULE->dataClass" to "Map",
                            )),
                    ValidationResult(
                        inputIds = listOf("ri_2_rn_0"),
                        success = true,
                        validatedData =
                            mapOf(
                                "CONTRACT_MODULE->companyId" to "1",
                                "CONTRACT_MODULE->type" to "HR_MEMBER",
                                "CONTRACT_MODULE->dataClass" to
                                    getGrpcClassName(CreateContractInput.newBuilder().build()),
                                "MEMBER_MODULE->firstName" to "John",
                                "MEMBER_MODULE->lastName" to "Doe1",
                                "MEMBER_MODULE->dataClass" to
                                    getGrpcClassName(MemberCreateInput.newBuilder().build()),
                                "COMPENSATION_MODULE->componentName" to "Base pay",
                                "COMPENSATION_MODULE->amount" to "10000",
                                "COMPENSATION_MODULE->currency" to "USD",
                                "COMPENSATION_MODULE->dataClass" to "Map",
                            )))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions())
            assertEquals(expected, actual)
        }

        @Test
        fun `should merge validated data into one`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "1",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "3",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_1_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setEmployeeId("E001")
                                            .setType(ContractOuterClass.ContractType.HR_MEMBER)
                                            .build(),
                                    groupName = EMPLOYMENT_DATA_GROUP),
                            ),
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        MemberCreateInput.newBuilder()
                                            .setFirstName("John")
                                            .setLastName("Doe")
                                            .build(),
                                    groupName = MEMBER_DATA_GROUP)),
                        "COMPENSATION_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        mapOf(
                                            "componentName" to "Base pay",
                                            "amount" to "1000",
                                            "currency" to "USD",
                                        ),
                                    groupName = COMPENSATION_DATA_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_1_rn_0", "ri_2_rn_0", "ri_3_rn_0"),
                        success = true,
                        validatedData =
                            mapOf(
                                "CONTRACT_MODULE->companyId" to "1",
                                "CONTRACT_MODULE->employeeId" to "E001",
                                "CONTRACT_MODULE->type" to "HR_MEMBER",
                                "CONTRACT_MODULE->dataClass" to
                                    getGrpcClassName(CreateContractInput.newBuilder().build()),
                                "MEMBER_MODULE->firstName" to "John",
                                "MEMBER_MODULE->lastName" to "Doe",
                                "MEMBER_MODULE->dataClass" to
                                    getGrpcClassName(MemberCreateInput.newBuilder().build()),
                                "COMPENSATION_MODULE->componentName" to "Base pay",
                                "COMPENSATION_MODULE->amount" to "1000",
                                "COMPENSATION_MODULE->currency" to "USD",
                                "COMPENSATION_MODULE->dataClass" to "Map",
                            )))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions())
            assertEquals(expected, actual)
        }

        @Test
        fun `should add index to key if multiple results in the same module`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "1",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "3",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "4",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_1_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setEmployeeId("E001")
                                            .setType(ContractOuterClass.ContractType.HR_MEMBER)
                                            .build(),
                                    groupName = EMPLOYMENT_DATA_GROUP),
                            ),
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        MemberCreateInput.newBuilder()
                                            .setFirstName("John")
                                            .setLastName("Doe")
                                            .build(),
                                    groupName = MEMBER_DATA_GROUP)),
                        "COMPENSATION_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        mapOf(
                                            "componentName" to "Base pay 1",
                                            "amount" to "1000",
                                            "currency" to "USD",
                                        ),
                                    groupName = COMPENSATION_DATA_GROUP),
                                GrpcValidationResult(
                                    validationId = "ri_4_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        mapOf(
                                            "componentName" to "Base pay 2",
                                            "amount" to "2000",
                                            "currency" to "USD",
                                        ),
                                    groupName = COMPENSATION_DATA_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_1_rn_0", "ri_2_rn_0", "ri_3_rn_0", "ri_4_rn_0"),
                        success = true,
                        validatedData =
                            mapOf(
                                "CONTRACT_MODULE->companyId" to "1",
                                "CONTRACT_MODULE->employeeId" to "E001",
                                "CONTRACT_MODULE->type" to "HR_MEMBER",
                                "CONTRACT_MODULE->dataClass" to
                                    getGrpcClassName(CreateContractInput.newBuilder().build()),
                                "MEMBER_MODULE->firstName" to "John",
                                "MEMBER_MODULE->lastName" to "Doe",
                                "MEMBER_MODULE->dataClass" to
                                    getGrpcClassName(MemberCreateInput.newBuilder().build()),
                                "COMPENSATION_MODULE->[0]->componentName" to "Base pay 1",
                                "COMPENSATION_MODULE->[0]->amount" to "1000",
                                "COMPENSATION_MODULE->[0]->currency" to "USD",
                                "COMPENSATION_MODULE->[0]->dataClass" to "Map",
                                "COMPENSATION_MODULE->[1]->componentName" to "Base pay 2",
                                "COMPENSATION_MODULE->[1]->amount" to "2000",
                                "COMPENSATION_MODULE->[1]->currency" to "USD",
                                "COMPENSATION_MODULE->[1]->dataClass" to "Map",
                            )))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions())
            assertEquals(expected, actual)
        }

        @Test
        fun `should fail all validation results of single employee if one fails`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "1",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "3",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_1_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input = null,
                                    groupName = EMPLOYMENT_DATA_GROUP)),
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = false,
                                    errors = listOf("First name is required"),
                                    input = null,
                                    groupName = MEMBER_DATA_GROUP)),
                        "COMPENSATION_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input = null,
                                    groupName = COMPENSATION_DATA_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_1_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf(
                                        "Please resolve validation errors of the same employee in other modules"))),
                        validatedData = emptyMap(),
                        group = EMPLOYMENT_DATA_GROUP),
                    ValidationResult(
                        inputIds = listOf("ri_2_rn_0"),
                        success = false,
                        errors = listOf(ValidationError("", "", listOf("First name is required"))),
                        validatedData = emptyMap(),
                        group = MEMBER_DATA_GROUP),
                    ValidationResult(
                        inputIds = listOf("ri_3_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf(
                                        "Please resolve validation errors of the same employee in other modules"))),
                        validatedData = emptyMap(),
                        group = COMPENSATION_DATA_GROUP))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions())
            assertEquals(expected, actual)
        }

        @Test
        fun `should fail all validation results of single employee if there are insufficient validation results for all 3 groups`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "3",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input = null,
                                    groupName = MEMBER_DATA_GROUP)),
                        "COMPENSATION_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input = null,
                                    groupName = COMPENSATION_DATA_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_2_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf("Employment data for same employee is missing"))),
                        validatedData = emptyMap(),
                        group = MEMBER_DATA_GROUP),
                    ValidationResult(
                        inputIds = listOf("ri_3_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf("Employment data for same employee is missing"))),
                        validatedData = emptyMap(),
                        group = COMPENSATION_DATA_GROUP))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions())
            assertEquals(expected, actual)
        }

        @Test
        fun `should fail all validation results of employees if there are missing group for freelancer in case of failed and success validation results`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "3",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E002",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = false,
                                    errors = listOf("designation is missing"),
                                    input = null,
                                    groupName = CONTRACTOR_DETAILS_GROUP)),
                        "COMPENSATION_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input = null,
                                    groupName = MEMBER_DATA_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_2_rn_0"),
                        success = false,
                        errors = listOf(ValidationError("", "", listOf("designation is missing"))),
                        validatedData = emptyMap(),
                        group = CONTRACTOR_DETAILS_GROUP),
                    ValidationResult(
                        inputIds = listOf("ri_3_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf("CONTRACTOR_DETAILS for same employee is missing"))),
                        validatedData = emptyMap(),
                        group = MEMBER_DATA_GROUP))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions(
                        ContractCountryCode.SGP,
                        ContractType.FREELANCER,
                        508736,
                        10258349,
                        BulkOnboardingContext.FREELANCER))
            assertEquals(expected, actual)
        }

        @Test
        fun `should merge result for freelancer when there is success validation result`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input = null,
                                    groupName = CONTRACTOR_DETAILS_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_2_rn_0"),
                        success = true,
                        errors = emptyList(),
                        validatedData = emptyMap()))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions(
                        ContractCountryCode.SGP,
                        ContractType.FREELANCER,
                        508736,
                        10258349,
                        BulkOnboardingContext.FREELANCER))
            assertEquals(expected, actual)
        }

        @Test
        fun `should merge result for eor when there is success validation result based on sheet row identifier`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "1",
                        companyId = 0,
                        data =
                            mapOf(
                                "rowIdentifier" to "333",
                            )),
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "rowIdentifier" to "333",
                            )),
                    ValidationInput(
                        id = "3",
                        companyId = 0,
                        data =
                            mapOf(
                                "rowIdentifier" to "333",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "CONTRACT_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_1_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        CreateContractInput.newBuilder()
                                            .setCompanyId(1)
                                            .setEmployeeId("E001")
                                            .setType(ContractOuterClass.ContractType.HR_MEMBER)
                                            .build(),
                                    groupName = BASIC_DETAILS_GROUP),
                            ),
                        "COMPLIANCE_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        MemberCreateInput.newBuilder()
                                            .setFirstName("John")
                                            .setLastName("Doe")
                                            .build(),
                                    groupName = COMPLIANCE_AND_LEAVES_DATA_GROUP)),
                        "COMPENSATION_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = true,
                                    errors = emptyList(),
                                    input =
                                        mapOf(
                                            "componentName" to "Base pay",
                                            "amount" to "1000",
                                            "currency" to "USD",
                                        ),
                                    groupName = COMPENSATION_DATA_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_1_rn_0", "ri_2_rn_0", "ri_3_rn_0"),
                        success = true,
                        validatedData =
                            mapOf(
                                "CONTRACT_MODULE->companyId" to "1",
                                "CONTRACT_MODULE->employeeId" to "E001",
                                "CONTRACT_MODULE->type" to "HR_MEMBER",
                                "CONTRACT_MODULE->dataClass" to
                                    getGrpcClassName(CreateContractInput.newBuilder().build()),
                                "COMPLIANCE_MODULE->firstName" to "John",
                                "COMPLIANCE_MODULE->lastName" to "Doe",
                                "COMPLIANCE_MODULE->dataClass" to
                                    getGrpcClassName(MemberCreateInput.newBuilder().build()),
                                "COMPENSATION_MODULE->componentName" to "Base pay",
                                "COMPENSATION_MODULE->amount" to "1000",
                                "COMPENSATION_MODULE->currency" to "USD",
                                "COMPENSATION_MODULE->dataClass" to "Map",
                            )))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions(
                        ContractCountryCode.SGP,
                        ContractType.EMPLOYEE,
                        508736,
                        10258349,
                        BulkOnboardingContext.EOR))
            assertEquals(expected, actual)
        }

        @Test
        fun `should not filter legal error for freelancer`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = false,
                                    errors = listOf("address is missing"),
                                    input = null,
                                    groupName = EMPLOYMENT_DATA_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_2_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError("", "", listOf("address is missing")),
                                ValidationError(
                                    "",
                                    "",
                                    listOf("CONTRACTOR_DETAILS for same employee is missing"))),
                        validatedData = emptyMap(),
                        group = EMPLOYMENT_DATA_GROUP))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions(
                        ContractCountryCode.SGP,
                        ContractType.FREELANCER,
                        508736,
                        10258349,
                        BulkOnboardingContext.FREELANCER))
            assertEquals(expected, actual)
        }

        @Test
        fun `should not filter error for eor and return error is some module data is missing`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "rowIdentifier" to "1",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = false,
                                    errors = listOf("address is missing"),
                                    input = null,
                                    groupName = BASIC_DETAILS_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_2_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError("", "", listOf("address is missing")),
                                ValidationError(
                                    "",
                                    "",
                                    listOf(
                                        "COMPLIANCE_AND_LEAVES_DATA for same employee is missing",
                                        "Compensation data for same employee is missing"))),
                        validatedData = emptyMap(),
                        group = BASIC_DETAILS_GROUP))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions(
                        ContractCountryCode.SGP,
                        ContractType.EMPLOYEE,
                        508736,
                        10258349,
                        BulkOnboardingContext.EOR))
            assertEquals(expected, actual)
        }

        @Test
        fun `should return module missing error for eor`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "rowIdentifier" to "1",
                            )))
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        "MEMBER_MODULE" to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors = null,
                                    input = null,
                                    groupName = EMPLOYMENT_DATA_GROUP))))

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_2_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "",
                                    "",
                                    listOf(
                                        "BASIC_DETAILS for same employee is missing",
                                        "COMPLIANCE_AND_LEAVES_DATA for same employee is missing",
                                        "Compensation data for same employee is missing"))),
                        validatedData = emptyMap(),
                        group = EMPLOYMENT_DATA_GROUP))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions(
                        ContractCountryCode.SGP,
                        ContractType.EMPLOYEE,
                        508736,
                        10258349,
                        BulkOnboardingContext.EOR))
            assertEquals(expected, actual)
        }

        @Test
        fun `should relocate additional member data errors to member data group`() {
            val inputs =
                listOf(
                    ValidationInput(
                        id = "1",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "2",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "3",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E001",
                            )),
                    ValidationInput(
                        id = "4",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E002",
                            )),
                    ValidationInput(
                        id = "5",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E002",
                            )),
                    ValidationInput(
                        id = "6",
                        companyId = 0,
                        data =
                            mapOf(
                                "employeeId" to "E002",
                            )),
                )
            val bulkValidationResult =
                BulkValidationResultV2(
                    mapOf(
                        BulkContractModule.MODULE_NAME to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_1_rn_0",
                                    success = false,
                                    errors = listOf("contract start date is missing"),
                                    input = null,
                                    groupName = EMPLOYMENT_DATA_GROUP),
                                GrpcValidationResult(
                                    validationId = "ri_4_rn_0",
                                    success = false,
                                    errors = listOf("designation is missing"),
                                    input = null,
                                    groupName = EMPLOYMENT_DATA_GROUP),
                            ),
                        BulkMemberModule.MODULE_NAME to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_1_rn_0",
                                    success = false,
                                    errors =
                                        listOf(
                                            "phone number is required",
                                            "date of birth is required",
                                            "nationality is required",
                                            "marital status is required"),
                                    input = null,
                                    groupName = EMPLOYMENT_DATA_GROUP),
                                GrpcValidationResult(
                                    validationId = "ri_4_rn_0",
                                    success = false,
                                    errors =
                                        listOf(
                                            "first name is missing",
                                            "address line 1 is required",
                                            "address city is required",
                                            "address country is required",
                                            "address postal code is required"),
                                    input = null,
                                    groupName = EMPLOYMENT_DATA_GROUP),
                            ),
                        BulkMemberLegalDataModule.MODULE_NAME to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_2_rn_0",
                                    success = true,
                                    errors =
                                        listOf(
                                            "immigration status is required",
                                            "passport number is required"),
                                    input = null,
                                    groupName = MEMBER_DATA_GROUP),
                                GrpcValidationResult(
                                    validationId = "ri_5_rn_0",
                                    success = false,
                                    errors =
                                        listOf(
                                            "bank account holder name is required",
                                            "bank account number is required"),
                                    input = null,
                                    groupName = MEMBER_DATA_GROUP),
                            ),
                        BulkCompensationModuleV2.MODULE_NAME to
                            listOf(
                                GrpcValidationResult(
                                    validationId = "ri_3_rn_0",
                                    success = false,
                                    errors = listOf("base pay is missing"),
                                    input = null,
                                    groupName = COMPENSATION_DATA_GROUP),
                                GrpcValidationResult(
                                    validationId = "ri_6_rn_0",
                                    success = false,
                                    errors = listOf("base pay amount is negative"),
                                    input = null,
                                    groupName = COMPENSATION_DATA_GROUP),
                            ),
                    ),
                )

            val expected =
                listOf(
                    ValidationResult(
                        inputIds = listOf("ri_1_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError("", "", listOf("contract start date is missing"))),
                        validatedData = emptyMap(),
                        group = EMPLOYMENT_DATA_GROUP,
                    ),
                    ValidationResult(
                        inputIds = listOf("ri_2_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError("", "", listOf("immigration status is required")),
                                ValidationError("", "", listOf("passport number is required")),
                                ValidationError("", "", listOf("phone number is required")),
                                ValidationError("", "", listOf("date of birth is required")),
                                ValidationError("", "", listOf("nationality is required")),
                                ValidationError("", "", listOf("marital status is required")),
                            ),
                        validatedData = emptyMap(),
                        group = MEMBER_DATA_GROUP,
                    ),
                    ValidationResult(
                        inputIds = listOf("ri_3_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError("", "", listOf("base pay is missing")),
                            ),
                        validatedData = emptyMap(),
                        group = COMPENSATION_DATA_GROUP,
                    ),
                    ValidationResult(
                        inputIds = listOf("ri_4_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError("", "", listOf("designation is missing")),
                                ValidationError("", "", listOf("first name is missing")),
                            ),
                        validatedData = emptyMap(),
                        group = EMPLOYMENT_DATA_GROUP,
                    ),
                    ValidationResult(
                        inputIds = listOf("ri_5_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError(
                                    "", "", listOf("bank account holder name is required")),
                                ValidationError("", "", listOf("bank account number is required")),
                                ValidationError("", "", listOf("address line 1 is required")),
                                ValidationError("", "", listOf("address city is required")),
                                ValidationError("", "", listOf("address country is required")),
                                ValidationError("", "", listOf("address postal code is required")),
                            ),
                        validatedData = emptyMap(),
                        group = MEMBER_DATA_GROUP,
                    ),
                    ValidationResult(
                        inputIds = listOf("ri_6_rn_0"),
                        success = false,
                        errors =
                            listOf(
                                ValidationError("", "", listOf("base pay amount is negative")),
                            ),
                        validatedData = emptyMap(),
                        group = COMPENSATION_DATA_GROUP,
                    ))

            val actual =
                mergeValidationResults(
                    bulkValidationResult,
                    inputs.map { it.toEmployeeData() },
                    getBulkOnboardingOptions())
            assertEquals(expected, actual)
        }

        fun getBulkOnboardingOptions(
            countryCode: ContractCountryCode = ContractCountryCode.SGP,
            contractType: ContractType = ContractType.HR_MEMBER,
            companyId: Long = 508726,
            entityId: Long = 10258349,
            context: BulkOnboardingContext = BulkOnboardingContext.GLOBAL_PAYROLL
        ): BulkOnboardingOptions {
            return BulkOnboardingOptions(countryCode, contractType, companyId, entityId, context)
        }
    }

    @Nested
    inner class UnmergeToBulkValidationResults {

        @Test
        fun `should unmerge upsert input to validated data`() {
            val dataMap =
                mapOf(
                    "COMPENSATION_SCHEMA_MODULE->BILLING_RATE_TYPE" to "Value",
                    "COMPENSATION_SCHEMA_MODULE->COMPONENT_NAME" to "Base Salary",
                    "COMPENSATION_SCHEMA_MODULE->CURRENCY" to "SGD",
                    "COMPENSATION_SCHEMA_MODULE->START_DATE" to "2024-11-06",
                    "COMPENSATION_SCHEMA_MODULE->dataClass" to "Map",
                    "CONTRACT_MODULE->companyId" to "500221",
                    "CONTRACT_MODULE->country" to "SGP",
                    "CONTRACT_MODULE->employeeId" to "GP-E0001",
                    "CONTRACT_MODULE->legalEntityId" to "27895",
                    "CONTRACT_MODULE->position" to "Tester",
                    "CONTRACT_MODULE->startOn->day" to "6",
                    "CONTRACT_MODULE->startOn->month" to "11",
                    "CONTRACT_MODULE->startOn->year" to "2024",
                    "CONTRACT_MODULE->term" to "PERMANENT",
                    "CONTRACT_MODULE->type" to "HR_MEMBER",
                    "CONTRACT_MODULE->dataClass" to
                        getGrpcClassName(CreateContractInput.newBuilder().build()),
                    "MEMBER_BANK_DATA_MODULE->dynamicDetail->detailFields->accountHolderName" to
                        "Test",
                    "MEMBER_BANK_DATA_MODULE->dynamicDetail->detailFields->accountNumber" to
                        "*********",
                    "MEMBER_BANK_DATA_MODULE->dynamicDetail->detailFields->address.country" to "SG",
                    "MEMBER_BANK_DATA_MODULE->dynamicDetail->detailFields->bankName" to "BoS",
                    "MEMBER_BANK_DATA_MODULE->dynamicDetail->detailFields->swiftCode" to "SWIFTCED",
                    "MEMBER_BANK_DATA_MODULE->dynamicDetail->paymentAccountRequirementType" to
                        "Local Bank Account",
                    "MEMBER_BANK_DATA_MODULE->dynamicDetail->sourceCurrency" to "SGD",
                    "MEMBER_BANK_DATA_MODULE->dynamicDetail->targetCurrency" to "SGD",
                    "MEMBER_BANK_DATA_MODULE->dynamicDetail->transferType" to "FIAT",
                    "MEMBER_BANK_DATA_MODULE->dataClass" to
                        getGrpcClassName(UpsertBankDetailsInput.newBuilder().build()),
                    "MEMBER_LEGAL_DATA_MODULE->legalDataInputs->nid" to "NID123343",
                    "MEMBER_LEGAL_DATA_MODULE->legalDataInputs->passportNumber" to "",
                    "MEMBER_LEGAL_DATA_MODULE->dataClass" to
                        getGrpcClassName(UpdateMemberLegalDataInput.newBuilder().build()),
                    "MEMBER_MODULE->address->city" to "city",
                    "MEMBER_MODULE->address->country" to "SGP",
                    "MEMBER_MODULE->address->key" to "CURRENT_ADDRESS",
                    "MEMBER_MODULE->address->line1" to "line 1",
                    "MEMBER_MODULE->address->postalCode" to "1232434",
                    "MEMBER_MODULE->email" to "<EMAIL>",
                    "MEMBER_MODULE->firstName" to "Test",
                    "MEMBER_MODULE->gender" to "MALE",
                    "MEMBER_MODULE->lastName" to "GP",
                    "MEMBER_MODULE->phoneNumber->phoneNo" to "+134343 234234",
                    "MEMBER_MODULE->phoneNumber->type" to "primary",
                    "MEMBER_MODULE->dataClass" to
                        getGrpcClassName(MemberCreateInput.newBuilder().build()),
                )
            val upsertInput =
                UpsertInput(
                    id = "1",
                    data = dataMap,
                )

            val result = unmergeToBulkValidationResults(listOf(upsertInput)).validationResults
            val expected =
                mapOf(
                        "MEMBER_MODULE" to
                            MemberCreateInput.newBuilder()
                                .setFirstName("Test")
                                .setLastName("GP")
                                .setEmail("<EMAIL>")
                                .setGender(Gender.MALE)
                                .setPhoneNumber(
                                    PhoneNumer.newBuilder()
                                        .setType("primary")
                                        .setPhoneNo("+134343 234234")
                                        .build())
                                .setAddress(
                                    Address.newBuilder()
                                        .setLine1("line 1")
                                        .setCity("city")
                                        .setCountry(CountryCode.SGP)
                                        .setPostalCode("1232434")
                                        .setKey("CURRENT_ADDRESS")
                                        .build())
                                .build(),
                        "CONTRACT_MODULE" to
                            CreateContractInput.newBuilder()
                                .setCompanyId(500221L)
                                .setEmployeeId("GP-E0001")
                                .setLegalEntityId(27895L)
                                .setCountry(Country.GrpcCountryCode.SGP)
                                .setPosition("Tester")
                                .setType(ContractOuterClass.ContractType.HR_MEMBER)
                                .setTerm(ContractOuterClass.ContractTerm.PERMANENT)
                                .setStartOn(
                                    Date.newBuilder().setDay(6).setMonth(11).setYear(2024).build())
                                .setPosition("Tester")
                                .build(),
                        "MEMBER_LEGAL_DATA_MODULE" to
                            UpdateMemberLegalDataInput.newBuilder()
                                .putAllLegalDataInputs(
                                    mapOf(
                                        "nid" to "NID123343",
                                        "passportNumber" to "",
                                    ))
                                .build(),
                        "MEMBER_BANK_DATA_MODULE" to
                            UpsertBankDetailsInput.newBuilder()
                                .setDynamicDetail(
                                    MemberBankDynamicDetail.newBuilder()
                                        .setTransferType(TransferType.FIAT)
                                        .setSourceCurrency("SGD")
                                        .setTargetCurrency("SGD")
                                        .setPaymentAccountRequirementType("Local Bank Account")
                                        .putAllDetailFields(
                                            mapOf(
                                                "accountNumber" to "*********",
                                                "accountHolderName" to "Test",
                                                "bankName" to "BoS",
                                                "swiftCode" to "SWIFTCED",
                                                "address.country" to "SG",
                                            ))
                                        .build())
                                .build(),
                        "COMPENSATION_SCHEMA_MODULE" to
                            mapOf(
                                "COMPONENT_NAME" to "Base Salary",
                                "CURRENCY" to "SGD",
                                "BILLING_RATE_TYPE" to "Value",
                                "START_DATE" to "2024-11-06",
                            ),
                    )
                    .mapValues {
                        listOf(
                            GrpcValidationResult(
                                validationId = "1",
                                success = true,
                                errors = emptyList(),
                                input = it.value,
                            ))
                    }
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `can unmerge array of objects`() {
            val dataMap =
                mapOf(
                    "COMPENSATION_SCHEMA_MODULE->[0]->BILLING_RATE" to "1",
                    "COMPENSATION_SCHEMA_MODULE->[0]->COMPONENT_NAME" to "Base Salary",
                    "COMPENSATION_SCHEMA_MODULE->[0]->CURRENCY" to "SGD",
                    "COMPENSATION_SCHEMA_MODULE->[0]->START_DATE" to "2024-11-06",
                    "COMPENSATION_SCHEMA_MODULE->[0]->dataClass" to "Map",
                    "COMPENSATION_SCHEMA_MODULE->[1]->BILLING_RATE" to "2",
                    "COMPENSATION_SCHEMA_MODULE->[1]->COMPONENT_NAME" to "Base Salary",
                    "COMPENSATION_SCHEMA_MODULE->[1]->CURRENCY" to "SGD",
                    "COMPENSATION_SCHEMA_MODULE->[1]->START_DATE" to "2024-11-06",
                    "COMPENSATION_SCHEMA_MODULE->[1]->dataClass" to "Map",
                    "COMPENSATION_SCHEMA_MODULE->[2]->BILLING_RATE" to "3",
                    "COMPENSATION_SCHEMA_MODULE->[2]->COMPONENT_NAME" to "Base Salary",
                    "COMPENSATION_SCHEMA_MODULE->[2]->CURRENCY" to "SGD",
                    "COMPENSATION_SCHEMA_MODULE->[2]->START_DATE" to "2024-11-06",
                    "COMPENSATION_SCHEMA_MODULE->[2]->dataClass" to "Map",
                )
            val upsertInput =
                UpsertInput(
                    id = "1",
                    data = dataMap,
                )

            val result = unmergeToBulkValidationResults(listOf(upsertInput)).validationResults
            val expected =
                mapOf(
                        "COMPENSATION_SCHEMA_MODULE" to
                            listOf(
                                mapOf(
                                    "COMPONENT_NAME" to "Base Salary",
                                    "CURRENCY" to "SGD",
                                    "BILLING_RATE" to "1",
                                    "START_DATE" to "2024-11-06",
                                ),
                                mapOf(
                                    "COMPONENT_NAME" to "Base Salary",
                                    "CURRENCY" to "SGD",
                                    "BILLING_RATE" to "2",
                                    "START_DATE" to "2024-11-06",
                                ),
                                mapOf(
                                    "COMPONENT_NAME" to "Base Salary",
                                    "CURRENCY" to "SGD",
                                    "BILLING_RATE" to "3",
                                    "START_DATE" to "2024-11-06",
                                )),
                    )
                    .mapValues {
                        it.value.map { map ->
                            GrpcValidationResult(
                                validationId = "1",
                                success = true,
                                errors = emptyList(),
                                input = map,
                            )
                        }
                    }
            assertThat(result).isEqualTo(expected)
        }

        @Test
        fun `can unmerge multiple upsert inputs`() {
            val dataMap1 =
                mapOf(
                    "COMPENSATION_SCHEMA_MODULE->COMPONENT_NAME" to "Base Salary 1",
                    "COMPENSATION_SCHEMA_MODULE->dataClass" to "Map",
                    "CONTRACT_MODULE->term" to "PERMANENT",
                    "CONTRACT_MODULE->type" to "HR_MEMBER",
                    "CONTRACT_MODULE->position" to "Tester 1",
                    "CONTRACT_MODULE->dataClass" to
                        getGrpcClassName(CreateContractInput.newBuilder().build()),
                )
            val dataMap2 =
                mapOf(
                    "COMPENSATION_SCHEMA_MODULE->COMPONENT_NAME" to "Base Salary 2",
                    "COMPENSATION_SCHEMA_MODULE->dataClass" to "Map",
                    "CONTRACT_MODULE->term" to "PERMANENT",
                    "CONTRACT_MODULE->type" to "HR_MEMBER",
                    "CONTRACT_MODULE->position" to "Tester 2",
                    "CONTRACT_MODULE->dataClass" to
                        getGrpcClassName(CreateContractInput.newBuilder().build()),
                )
            val upsertInput1 =
                UpsertInput(
                    id = "1",
                    data = dataMap1,
                )
            val upsertInput2 =
                UpsertInput(
                    id = "2",
                    data = dataMap2,
                )

            val result =
                unmergeToBulkValidationResults(listOf(upsertInput1, upsertInput2)).validationResults
            val expected =
                mapOf(
                    "CONTRACT_MODULE" to
                        listOf(
                            GrpcValidationResult(
                                validationId = "1",
                                success = true,
                                errors = emptyList(),
                                input =
                                    CreateContractInput.newBuilder()
                                        .setType(ContractOuterClass.ContractType.HR_MEMBER)
                                        .setTerm(ContractOuterClass.ContractTerm.PERMANENT)
                                        .setPosition("Tester 1")
                                        .build()),
                            GrpcValidationResult(
                                validationId = "2",
                                success = true,
                                errors = emptyList(),
                                input =
                                    CreateContractInput.newBuilder()
                                        .setType(ContractOuterClass.ContractType.HR_MEMBER)
                                        .setTerm(ContractOuterClass.ContractTerm.PERMANENT)
                                        .setPosition("Tester 2")
                                        .build())),
                    "COMPENSATION_SCHEMA_MODULE" to
                        listOf(
                            GrpcValidationResult(
                                validationId = "1",
                                success = true,
                                errors = emptyList(),
                                input =
                                    mapOf(
                                        "COMPONENT_NAME" to "Base Salary 1",
                                    )),
                            GrpcValidationResult(
                                validationId = "2",
                                success = true,
                                errors = emptyList(),
                                input =
                                    mapOf(
                                        "COMPONENT_NAME" to "Base Salary 2",
                                    ))),
                )
            assertThat(result).isEqualTo(expected)
        }
    }
}
