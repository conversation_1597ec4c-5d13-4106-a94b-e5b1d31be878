package com.multiplier.contract.onboarding.adapter

import com.google.type.Date
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.domain.model.EducationDetail
import com.multiplier.contract.onboarding.domain.model.EmergencyContact
import com.multiplier.contract.onboarding.domain.model.EmployerDetail
import com.multiplier.contract.onboarding.service.toTimeStamp
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.member.schema.*
import com.multiplier.member.schema.Address as GrpcAddress
import com.multiplier.member.schema.CountryCode as GrpcCountryCode
import com.multiplier.member.schema.Member
import com.multiplier.member.schema.Member.MemberNationalogy
import com.multiplier.member.schema.MemberServiceGrpc.MemberServiceBlockingStub
import com.multiplier.member.schema.Persona
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import java.time.LocalDate
import java.time.LocalDateTime
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentMatchers.anySet

@ExtendWith(MockKExtension::class)
class MemberServiceAdapterImplTest {

    @MockK private lateinit var memberServiceStubMock: MemberServiceBlockingStub

    @InjectMockKs private lateinit var memberServiceAdapter: MemberServiceAdapterImpl

    @Test
    fun `should return correct domain list when get emergency contacts`() {
        val grpcResponse =
            GetMemberEmergencyContactsResponse.newBuilder()
                .addAllEmergencyContacts(
                    listOf(
                        MemberEmergencyContact.newBuilder()
                            .setMemberId(1)
                            .setName("Johnny")
                            .setRelationship("Father")
                            .setPhoneNumber(PhoneNumer.newBuilder().setPhoneNo("555-5555").build())
                            .build()))
                .build()
        every { memberServiceStubMock.getMemberEmergencyContacts(any()) } returns grpcResponse

        val expected =
            grpcResponse.emergencyContactsList.map {
                EmergencyContact(
                    memberId = it.memberId,
                    name = it.name,
                    relationship = it.relationship,
                    phoneNumber = it.phoneNumber.phoneNo,
                )
            }
        assertEquals(expected, memberServiceAdapter.getMemberEmergencyContacts(emptySet()))
    }

    @Test
    fun `should return correct domain list when getting education details`() {
        val grpcResponse =
            GetMemberEducationDetailsResponse.newBuilder()
                .addAllEducationDetails(
                    listOf(
                        MemberEducationDetails.newBuilder()
                            .setMemberId(1)
                            .addAllEducationDetails(
                                listOf(
                                    MemberEducationDetail.newBuilder()
                                        .setInstitutionName("Harvard University")
                                        .setDegree("Computer Science")
                                        .setYearOfPassing(2017)
                                        .setGpa(4f)
                                        .setGrade("Good")
                                        .build(),
                                    MemberEducationDetail.newBuilder()
                                        .setInstitutionName("California High School")
                                        .setDegree("High School")
                                        .setYearOfPassing(2012)
                                        .setGpa(3.8f)
                                        .build()))
                            .build()))
                .build()

        every { memberServiceStubMock.getMemberEducationDetails(any()) } returns grpcResponse

        val expected =
            listOf(
                EducationDetail(
                    memberId = 1,
                    lastSchoolName = "Harvard University",
                    lastSchoolDegree = "Computer Science",
                    lastSchoolYearOfPassing = 2017,
                    lastSchoolGpa = 4f,
                    lastSchoolGrade = "Good"))
        assertEquals(expected, memberServiceAdapter.getMemberEducationDetails(emptySet()))
    }

    @Test
    fun `should return correct domain list when getting previous employment details`() {
        val grpcResponse =
            GetMemberPreviousEmployerDetailsResponse.newBuilder()
                .addAllPreviousEmployerDetails(
                    listOf(
                        MemberPreviousEmployerDetails.newBuilder()
                            .setMemberId(1)
                            .addAllPreviousEmployerDetails(
                                listOf(
                                    MemberPreviousEmployerDetail.newBuilder()
                                        .setEmployerName("Google")
                                        .setDesignation("Senior Software Engineer")
                                        .setStartDate(
                                            Date.newBuilder().setYear(2020).setMonth(1).setDay(1))
                                        .setEndDate(
                                            Date.newBuilder()
                                                .setYear(2023)
                                                .setMonth(12)
                                                .setDay(31)
                                                .build())
                                        .build(),
                                    MemberPreviousEmployerDetail.newBuilder()
                                        .setEmployerName("Facebook")
                                        .setDesignation("Software Engineer")
                                        .setStartDate(
                                            Date.newBuilder().setYear(2017).setMonth(1).setDay(1))
                                        .setEndDate(
                                            Date.newBuilder()
                                                .setYear(2019)
                                                .setMonth(12)
                                                .setDay(31)
                                                .build())
                                        .build(),
                                ))
                            .build()))
                .build()
        every { memberServiceStubMock.getMemberPreviousEmployerDetails(any()) } returns grpcResponse

        val expected =
            listOf(
                EmployerDetail(
                    memberId = 1,
                    lastEmployerName = "Google",
                    lastEmployerPosition = "Senior Software Engineer",
                    lastEmployerStartDate = LocalDate.of(2020, 1, 1),
                    lastEmployerEndDate = LocalDate.of(2023, 12, 31),
                ))
        assertEquals(expected, memberServiceAdapter.getMemberLastEmployerDetails(anySet()))
    }

    @Nested
    inner class GetMembersWithAddressesAndBankAccountsAndLegalData {

        @Test
        fun `returns correct data map`() {
            val grpcResponse =
                MembersList.newBuilder()
                    .addAllMembers(
                        listOf(
                            Member.newBuilder()
                                .setId(1)
                                .addAllEmails(
                                    listOf(
                                        EmailAddress.newBuilder()
                                            .setType("primary")
                                            .setEmail("member@email")
                                            .build()))
                                .setFirstName("FName")
                                .setLastName("LName")
                                .setPersona(Persona.MEMBER)
                                .setUserId("user_id")
                                .setGender(Gender.UNSPECIFIED)
                                .setDateOfBirth(LocalDateTime.of(1993, 9, 2, 0, 0, 0).toTimeStamp())
                                .addAllNationalities(
                                    listOf(
                                        MemberNationalogy.newBuilder()
                                            .setCountry(GrpcCountryCode.VNM)
                                            .build()))
                                .setMartialStatus(Member.MaritalStatus.MARRIED)
                                .addAllPhoneNos(
                                    listOf(PhoneNumer.newBuilder().setPhoneNo("*********").build()))
                                .addAllLegalData(
                                    listOf(
                                        LegalData.newBuilder()
                                            .setKey("religion")
                                            .setValue("NONE")
                                            .build(),
                                        LegalData.newBuilder()
                                            .setKey("nationalId")
                                            .setValue("0*********")
                                            .build(),
                                        LegalData.newBuilder()
                                            .setKey("passportNumber")
                                            .setValue("ABCD12345")
                                            .build()))
                                .build()))
                    .build()
            every {
                memberServiceStubMock.getMembersWithAddressesAndBankAccountsAndLegalData(any())
            } returns grpcResponse

            val expected =
                listOf(
                    com.multiplier.contract.onboarding.domain.model.Member(
                        id = 1,
                        email = "member@email",
                        firstName = "FName",
                        lastName = "LName",
                        fullName = "FName LName",
                        persona = com.multiplier.contract.onboarding.domain.model.Persona("MEMBER"),
                        userId = "user_id",
                        gender = com.multiplier.contract.onboarding.types.Gender.UNSPECIFIED,
                        dateOfBirth = LocalDate.of(1993, 9, 2),
                        nationality = "VNM",
                        maritalStatus = "MARRIED",
                        phoneNumber = "*********",
                        religion = "NONE",
                        nationalId = "0*********",
                        passportNumber = "ABCD12345",
                        legalDataProps =
                            mapOf(
                                "religion" to "NONE",
                                "nationalId" to "0*********",
                                "passportNumber" to "ABCD12345",
                            )))
            assertEquals(
                expected,
                memberServiceAdapter.getMembersWithAddressesAndBankAccountsAndLegalData(anySet()))
        }
    }

    @Nested
    inner class GetMemberAddressDetails {

        @Test
        fun `should return correct domain list when getting address details`() {
            val grpcResponse =
                GetMemberAddressDetailsResponse.newBuilder()
                    .addAddressDetails(
                        MemberAddressDetails.newBuilder()
                            .setMemberId(1L)
                            .setCurrentAddress(
                                GrpcAddress.newBuilder()
                                    .setKey("current")
                                    .setStreet("TCV")
                                    .setLine1("196")
                                    .setLine2("Tam Thuan")
                                    .setCity("Da Nang")
                                    .setState("Son Tra")
                                    .setProvince("Quang Nam")
                                    .setCountry(GrpcCountryCode.VNM)
                                    .setZipcode("550001")
                                    .setPostalCode("550002"))
                            .setPermanentAddress(
                                GrpcAddress.newBuilder()
                                    .setKey("permanent")
                                    .setStreet("DDN")
                                    .setLine1("266")
                                    .setLine2("An Hai Bac")
                                    .setCity("Little Elm")
                                    .setState("Texas")
                                    .setProvince("Dallas")
                                    .setCountry(GrpcCountryCode.USA)
                                    .setZipcode("550003")
                                    .setPostalCode("550004")))
                    .addAddressDetails(
                        MemberAddressDetails.newBuilder()
                            .setMemberId(2L)
                            .setCurrentAddress(
                                GrpcAddress.newBuilder()
                                    .setKey("current")
                                    .setStreet("DDN")
                                    .setLine1("266")
                                    .setLine2("An Hai Bac")
                                    .setCity("Da Nang")
                                    .setState("Son Tra")
                                    .setProvince("Quang Nam")
                                    .setCountry(GrpcCountryCode.VNM)
                                    .setZipcode("550005")
                                    .setPostalCode("550006"))
                            .setPermanentAddress(GrpcAddress.getDefaultInstance()))
                    .build()
            every { memberServiceStubMock.getMemberAddressDetails(any()) } returns grpcResponse

            val expected =
                listOf(
                    AddressDetail(
                        memberId = 1,
                        currentAddress =
                            Address(
                                street = "TCV",
                                line1 = "196",
                                line2 = "Tam Thuan",
                                city = "Da Nang",
                                state = "Son Tra",
                                province = "Quang Nam",
                                country = CountryCode.VNM,
                                zipCode = "550001",
                                postalCode = "550002",
                            ),
                        permanentAddress =
                            Address(
                                street = "DDN",
                                line1 = "266",
                                line2 = "An Hai Bac",
                                city = "Little Elm",
                                state = "Texas",
                                province = "Dallas",
                                country = CountryCode.USA,
                                zipCode = "550003",
                                postalCode = "550004",
                            ),
                    ),
                    AddressDetail(
                        memberId = 2,
                        currentAddress =
                            Address(
                                street = "DDN",
                                line1 = "266",
                                line2 = "An Hai Bac",
                                city = "Da Nang",
                                state = "Son Tra",
                                province = "Quang Nam",
                                country = CountryCode.VNM,
                                zipCode = "550005",
                                postalCode = "550006",
                            ),
                    ))

            assertEquals(expected, memberServiceAdapter.getMemberAddressDetails(setOf(1L, 2L)))
        }

        @Test
        fun `should return empty list when passing empty member id set`() {
            assertEquals(
                emptyList<AddressDetail>(),
                memberServiceAdapter.getMemberAddressDetails(emptySet()))
        }
    }
}
