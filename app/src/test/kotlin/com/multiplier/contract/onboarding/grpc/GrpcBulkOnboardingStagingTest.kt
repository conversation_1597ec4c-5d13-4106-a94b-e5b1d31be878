package com.multiplier.contract.onboarding.grpc

import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpecsResponse
import com.multiplier.contract.onboarding.schema.BulkOnboardInput
import com.multiplier.contract.onboarding.schema.BulkOnboardOption
import com.multiplier.contract.onboarding.schema.BulkOnboardRequest
import com.multiplier.contract.onboarding.schema.BulkOnboardResponse
import com.multiplier.contract.onboarding.schema.BulkOnboardValidationResponse
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.PositionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StartOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmailSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.FirstNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.GenderSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PhoneNumberSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService.Companion.DepartmentSpec
import com.multiplier.contract.onboarding.service.bulkupload.BulkUploadService.Companion.GLOBAL_PAYROLL_MEMBER_ONBOARDING
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.grpc.common.bulkupload.v1.ValidateUpsertInputBulkResponse
import com.multiplier.grpc.common.bulkupload.v1.platformKeys
import com.multiplier.grpc.common.bulkupload.v1.validateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.validateUpsertInputRequest
import com.multiplier.grpc.common.contract.v2.Contract.ContractType.CONTRACT_TYPE_HR_MEMBER
import com.multiplier.grpc.common.country.v2.Country.CountryCode.COUNTRY_CODE_SGP
import io.grpc.internal.testing.StreamRecorder
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

/** This test runs with data on staging env */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("context-test")
@Disabled("test running on github can't connect to staging servers")
class GrpcBulkOnboardingStagingTest {

    @Autowired private lateinit var service: GrpcContractOnboardingService

    private val contractDataProperties =
        mapOf(
            "employeeId" to "CompensationIntegrationTest01",
            FirstNameSpec.key to "Johnny",
            LastNameSpec.key to "Depp",
            EmailSpec.key to "<EMAIL>",
            GenderSpec.key to "MALE",
            PositionSpec.key to "tester",
            StartOnSpec.key to "2022-01-03",
            //            DepartmentSpec.key to "312 - Engineer",
            DepartmentSpec.key to "285 - Test",
        )

    private val memberDataProperties =
        mapOf(
            "employeeId" to "CompensationIntegrationTest01",
            PhoneNumberSpec.key to "+*********",
            "address.line1" to "address line 1",
            "address.city" to "city",
            "address.country" to "SGP",
            "address.postalCode" to "123456",
            "race" to "test",
            "immigrationStatus" to "Citizen",
            "nid" to "test",
            "bank.accountHolderName" to "test",
            "bank.accountNumber" to "**********",
            "bank.swiftCode" to "WIAHTSDT",
            "bank.bankName" to "test",
            "bank.address.country" to "SG")

    private val employeeDataProperties = contractDataProperties + memberDataProperties

    private val compensationDataV1Properties =
        mapOf(
            "payrollFrequency" to "MONTHLY",
            "rateFrequency" to "MONTHLY",
            "basePay" to "99999",
            "medicalAllowance" to "1000",
            "internetAllowance" to "2000",
            "carAllowance" to "3000",
            "transportAllowance" to "4000",
            "insuranceAllowance" to "5000",
            "phoneAllowance" to "6000",
            "rentalAllowance" to "7000",
            "mealAllowance" to "8000",
            "homeOfficeAllowance" to "9000",
        )

    private val compensationDataV2Properties1 =
        mapOf(
            "employeeId" to "CompensationIntegrationTest01",
            "EMPLOYEE_FULL_NAME" to "Johnny Depp",
            "COMPONENT_NAME" to "Base Salary",
            "CURRENCY" to "SGD",
            "BILLING_RATE_TYPE" to "Value",
            "BILLING_RATE" to "10000",
            "BILLING_FREQUENCY" to "MONTHLY",
            "PAY_SCHEDULE_NAME" to "Monthly-01",
            "IS_INSTALLMENT" to "No",
            "START_DATE" to "2022-01-03",
            "END_DATE" to "2023-01-03",
            "NUMBER_OF_INSTALLMENTS" to "0")

    private val compensationDataV2Properties2 =
        mapOf(
            "employeeId" to "CompensationIntegrationTest01",
            "EMPLOYEE_FULL_NAME" to "Johnny Depp",
            "COMPONENT_NAME" to "HRA",
            "CURRENCY" to "SGD",
            "BILLING_RATE_TYPE" to "% of Base Pay",
            "BILLING_RATE" to "40",
            "BILLING_FREQUENCY" to "MONTHLY",
            "PAY_SCHEDULE_NAME" to "Monthly-05",
            "IS_INSTALLMENT" to "No",
            "START_DATE" to "2022-01-03",
            "END_DATE" to "2023-01-03",
            "NUMBER_OF_INSTALLMENTS" to "0")

    private val compensationDataV2Properties3 =
        mapOf(
            "employeeId" to "CompensationIntegrationTest01",
            "EMPLOYEE_FULL_NAME" to "Johnny Depp",
            "COMPONENT_NAME" to "Salary advance deduction",
            "CURRENCY" to "SGD",
            "BILLING_RATE_TYPE" to "% of Base Pay",
            "BILLING_RATE" to "10",
            "BILLING_FREQUENCY" to "MONTHLY",
            "PAY_SCHEDULE_NAME" to "Monthly-05",
            "IS_INSTALLMENT" to "No",
            "START_DATE" to "2022-01-03",
            "END_DATE" to "2023-01-03",
            "NUMBER_OF_INSTALLMENTS" to "0")

    private val contractDataProperties2 =
        mapOf(
            "employeeId" to "CompensationIntegrationTest02",
            FirstNameSpec.key to "Jane",
            LastNameSpec.key to "Doe",
            EmailSpec.key to "<EMAIL>",
            GenderSpec.key to "FEMALE",
            PositionSpec.key to "tester",
            StartOnSpec.key to "2022-01-03",
            DepartmentSpec.key to "312 - Engineer",
        )

    private val memberDataProperties2 =
        mapOf(
            "employeeId" to "CompensationIntegrationTest02",
            PhoneNumberSpec.key to "+*********",
            "address.line1" to "address line 1",
            "address.city" to "city",
            "address.country" to "SGP",
            "address.postalCode" to "123456",
            "race" to "test",
            "immigrationStatus" to "Citizen",
            "nid" to "test",
            "bank.accountHolderName" to "test",
            "bank.accountNumber" to "**********",
            "bank.swiftCode" to "WIAHTSDT",
            "bank.bankName" to "test",
            "bank.address.country" to "SG")

    private val employeeDataProperties2 = contractDataProperties2 + memberDataProperties2

    private val compensationDataV2Properties21 =
        mapOf(
            "employeeId" to "CompensationIntegrationTest02",
            "EMPLOYEE_FULL_NAME" to "Johnny Depp",
            "COMPONENT_NAME" to "Base Salary",
            "CURRENCY" to "SGD",
            "BILLING_RATE_TYPE" to "Value",
            "BILLING_RATE" to "10000",
            "BILLING_FREQUENCY" to "ANNUALLY",
            "PAY_SCHEDULE_NAME" to "Monthly-02",
            "IS_INSTALLMENT" to "Yes",
            "START_DATE" to "2022-01-03",
            "NUMBER_OF_INSTALLMENTS" to "12")

    private val compensationDataV2Properties22 =
        mapOf(
            "employeeId" to "CompensationIntegrationTest02",
            "EMPLOYEE_FULL_NAME" to "Johnny Depp",
            "COMPONENT_NAME" to "HRA",
            "CURRENCY" to "SGD",
            "BILLING_RATE_TYPE" to "% of Base Pay",
            "BILLING_RATE" to "40",
            "BILLING_FREQUENCY" to "MONTHLY",
            "PAY_SCHEDULE_NAME" to "Monthly-05",
            "IS_INSTALLMENT" to "No",
            "START_DATE" to "2022-01-03",
            "END_DATE" to "2023-01-03",
            "NUMBER_OF_INSTALLMENTS" to "0")

    private val compensationDataV2Properties23 =
        mapOf(
            "employeeId" to "CompensationIntegrationTest02",
            "EMPLOYEE_FULL_NAME" to "Johnny Depp",
            "COMPONENT_NAME" to "Salary advance deduction",
            "CURRENCY" to "SGD",
            "BILLING_RATE_TYPE" to "% of Base Pay",
            "BILLING_RATE" to "10",
            "BILLING_FREQUENCY" to "MONTHLY",
            "PAY_SCHEDULE_NAME" to "Monthly-05",
            "IS_INSTALLMENT" to "No",
            "START_DATE" to "2022-01-03",
            "END_DATE" to "2023-01-03",
            "NUMBER_OF_INSTALLMENTS" to "0")

    private val grpcBulkOnboardOptionV1 =
        BulkOnboardOption.newBuilder()
            .setContext(BulkOnboardingContext.GLOBAL_PAYROLL.name)
            .setContractType(CONTRACT_TYPE_HR_MEMBER)
            .setCompanyId(508665)
            .setEntityId(2209318)
            .setCountryCode(COUNTRY_CODE_SGP)
            .build()

    private val bulkOnboardRequestV1 =
        BulkOnboardRequest.newBuilder()
            .addAllInputs(
                listOf(
                    BulkOnboardInput.newBuilder()
                        .setRequestId(1)
                        .putAllProperties(employeeDataProperties + compensationDataV1Properties)
                        .build(),
                ))
            .setOption(grpcBulkOnboardOptionV1)
            .build()

    private val grpcBulkOnboardOptionV2 =
        BulkOnboardOption.newBuilder()
            .setContext(BulkOnboardingContext.GLOBAL_PAYROLL.name)
            .setContractType(CONTRACT_TYPE_HR_MEMBER)
            .setCompanyId(866846)
            .setEntityId(10616797)
            .setCountryCode(COUNTRY_CODE_SGP)
            .build()

    private val bulkOnboardRequestV2 =
        BulkOnboardRequest.newBuilder()
            .addAllInputs(
                listOf(
                    BulkOnboardInput.newBuilder()
                        .setRequestId(1)
                        .putAllProperties(employeeDataProperties)
                        .setGroup("EMPLOYMENT_DATA")
                        .build(),
                    BulkOnboardInput.newBuilder()
                        .setRequestId(2)
                        .putAllProperties(compensationDataV2Properties1)
                        .setGroup("COMPENSATION_DATA")
                        .build(),
                    //                    BulkOnboardInput.newBuilder()
                    //                        .setRequestId(3)
                    //                        .putAllProperties(compensationDataProperties2)
                    //                        .build(),
                    //                    BulkOnboardInput.newBuilder()
                    //                        .setRequestId(4)
                    //                        .putAllProperties(compensationDataProperties3)
                    //                        .build(),
                ))
            .setOption(grpcBulkOnboardOptionV2)
            .build()

    @Nested
    inner class BulkOnboardingAPI {

        @Nested
        inner class WithContractServiceAsCompensationSource {
            @Test
            fun `test get data specs with allowance fields`() {
                val responseRecorder = StreamRecorder.create<BulkOnboardDataSpecsResponse>()
                service.getBulkOnboardDataSpecs(grpcBulkOnboardOptionV1, responseRecorder)
                val response = responseRecorder.values.first()
                val allowanceSpecs =
                    response.specsList.filter { it.key.contains("allowance", true) }
                assertThat(allowanceSpecs).hasSize(9)
            }

            @Test
            fun `run GP bulk validate using bulk onboarding API`() {
                val responseRecorder = StreamRecorder.create<BulkOnboardValidationResponse>()
                service.validateBulkOnboarding(bulkOnboardRequestV1, responseRecorder)
                val validationResults = responseRecorder.values.first().validationResultsList
                assertAll({ assertThat(validationResults).allMatch { it.success } })
            }

            @Test
            fun `run GP bulk onboarding`() {
                val responseRecorder = StreamRecorder.create<BulkOnboardResponse>()
                service.bulkOnboard(bulkOnboardRequestV1, responseRecorder)

                val results = responseRecorder.values.map { it.resultsList }.flatten()
                assertAll({
                    assertThat(results).allSatisfy { assertThat(it.contractId).isPositive() }
                })
            }
        }

        @Nested
        inner class WithNewCompensationServiceAsCompensationSource {

            @Test
            fun `test get data specs with new compensation schema fields`() {
                val responseRecorder = StreamRecorder.create<BulkOnboardDataSpecsResponse>()
                service.getBulkOnboardDataSpecs(grpcBulkOnboardOptionV2, responseRecorder)
                val response = responseRecorder.values.first()
                val componentNameSpec = response.specsList.find { it.key == "COMPONENT_NAME" }
                assertThat(componentNameSpec?.valuesList).isNotEmpty()
            }

            @Test
            fun `run GP bulk validate using bulk onboarding API`() {
                val responseRecorder = StreamRecorder.create<BulkOnboardValidationResponse>()
                service.validateBulkOnboarding(bulkOnboardRequestV2, responseRecorder)
                val validationResults = responseRecorder.values.first().validationResultsList
                assertAll({ assertThat(validationResults).allMatch { it.success } })
            }

            @Test
            fun `run GP bulk onboarding`() {
                val responseRecorder = StreamRecorder.create<BulkOnboardResponse>()
                service.bulkOnboard(bulkOnboardRequestV2, responseRecorder)

                val results = responseRecorder.values.map { it.resultsList }.flatten()
                assertAll({
                    assertThat(results).allSatisfy { assertThat(it.contractId).isPositive() }
                })
            }
        }
    }

    @Nested
    inner class BulkUploadAPI {

        @Test
        fun `run GP bulk validate using bulk upload API`() {
            val responseRecorder = StreamRecorder.create<ValidateUpsertInputBulkResponse>()
            service.bulkValidateUpsertInput(bulkValidateRequest, responseRecorder)
            val validationResults = responseRecorder.values.first().resultsList
            assertAll(
                { assertThat(validationResults).hasSize(2) },
                { assertThat(validationResults).allMatch { it.success } })
        }

        private val bulkValidateRequest = validateUpsertInputBulkRequest {
            this.inputs.addAll(
                listOf(
                    validateUpsertInputRequest {
                        inputId = "1"
                        data.putAll(contractDataProperties)
                        keys = platformKeys {
                            companyId = 866846L
                            entityId = 10616797L
                        }
                        group = "EMPLOYMENT_DATA"
                    },
                    validateUpsertInputRequest {
                        inputId = "2"
                        data.putAll(memberDataProperties)
                        keys = platformKeys {
                            companyId = 866846L
                            entityId = 10616797L
                        }
                        group = "MEMBER_DATA"
                    },
                    validateUpsertInputRequest {
                        inputId = "3"
                        data.putAll(compensationDataV2Properties1)
                        keys = platformKeys {
                            companyId = 866846L
                            entityId = 10616797L
                        }
                        group = "COMPENSATION_DATA"
                    },
                    //                validateUpsertInputRequest {
                    //                    inputId = "4"
                    //                    data.putAll(compensationDataProperties2)
                    //                    keys = platformKeys {
                    //                        companyId = 866846L
                    //                        entityId = 10616797L
                    //                    }
                    //                    group = "COMPENSATION_DATA"
                    //                },
                    //                validateUpsertInputRequest {
                    //                    inputId = "5"
                    //                    data.putAll(compensationDataProperties3)
                    //                    keys = platformKeys {
                    //                        companyId = 866846L
                    //                        entityId = 10616797L
                    //                    }
                    //                    group = "COMPENSATION_DATA"
                    //                },
                    validateUpsertInputRequest {
                        inputId = "6"
                        data.putAll(contractDataProperties2)
                        keys = platformKeys {
                            companyId = 866846L
                            entityId = 10616797L
                        }
                        group = "EMPLOYMENT_DATA"
                    },
                    validateUpsertInputRequest {
                        inputId = "7"
                        data.putAll(memberDataProperties2)
                        keys = platformKeys {
                            companyId = 866846L
                            entityId = 10616797L
                        }
                        group = "MEMBER_DATA"
                    },
                    validateUpsertInputRequest {
                        inputId = "8"
                        data.putAll(compensationDataV2Properties21)
                        keys = platformKeys {
                            companyId = 866846L
                            entityId = 10616797L
                        }
                        group = "COMPENSATION_DATA"
                    },
                    //                validateUpsertInputRequest {
                    //                    inputId = "9"
                    //                    data.putAll(compensationDataProperties22)
                    //                    keys = platformKeys {
                    //                        companyId = 866846L
                    //                        entityId = 10616797L
                    //                    }
                    //                    group = "COMPENSATION_DATA"
                    //                },
                    //                validateUpsertInputRequest {
                    //                    inputId = "10"
                    //                    data.putAll(compensationDataProperties23)
                    //                    keys = platformKeys {
                    //                        companyId = 866846L
                    //                        entityId = 10616797L
                    //                    }
                    //                    group = "COMPENSATION_DATA"
                    //                },
                ))
            useCase = GLOBAL_PAYROLL_MEMBER_ONBOARDING
        }
    }
}
