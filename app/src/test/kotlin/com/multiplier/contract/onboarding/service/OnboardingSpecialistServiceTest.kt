package com.multiplier.contract.onboarding.service

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.OperationsUserServiceAdapter
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.service.notifications.NotificationService
import com.multiplier.contract.onboarding.service.notifications.OpsEmailResolver
import com.multiplier.contract.onboarding.service.reminders.Notification
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.NotificationType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@ExtendWith(MockKExtension::class)
internal class OnboardingSpecialistServiceTest {
    @MockK lateinit var operationsUserService: OperationsUserServiceAdapter

    @MockK lateinit var memberService: MemberServiceAdapter

    @MockK lateinit var companyService: CompanyServiceAdapter

    @MockK lateinit var notificationService: NotificationService

    @MockK lateinit var currentUser: CurrentUser

    @MockK lateinit var opsEmailResolver: OpsEmailResolver

    @MockK lateinit var contractService: ContractServiceAdapter

    @InjectMockKs lateinit var onboardingSpecialistService: OnboardingSpecialistService

    @BeforeEach
    fun beforeEach() {
        every { operationsUserService.getOperationsUsersForContracts(any()) } returns
            mapOf(123L to mockk(relaxed = true))

        every { currentUser.context?.experience } returns "company"

        every { opsEmailResolver.getOnboardingSpecialistTestEmail() } returns
            "<EMAIL>"
        every { opsEmailResolver.getSenderEmail() } returns "<EMAIL>"

        every { contractService.getContractById(123L) } returns
            mockk(relaxed = true) { every { companyId } returns 1L }
        every { companyService.getCompany(1L) } returns
            mockk(relaxed = true) { every { isTest } returns false }
    }

    private val contractId = 123L

    @Test
    internal fun sendEmailToOnboardingSpecialist() {
        val message = "Hello, onboarding specialist!"
        val recipient = "<EMAIL>"

        every { operationsUserService.getOperationsUsersForContracts(listOf(contractId)) } returns
            mapOf(
                contractId to
                    OperationsUser(
                        1L, email = recipient, firstName = "firstName", lastName = "lastName"))

        every { currentUser.context } returns
            mockk(relaxed = true) {
                every { id } returns 1L
                every { experience } returns "company"
            }

        every { companyService.getCompanyUserByUserId(1L) } returns
            CompanyUser(
                id = 100L,
                companyId = 1L,
                firstName = "John",
                lastName = "Doe",
                email = "<EMAIL>")

        every { notificationService.send(any()) } returns Unit

        val response =
            onboardingSpecialistService.sendEmailToOnboardingSpecialist(contractId, message)

        // Verify that the notification was sent with the correct parameters
        val notificationSlot = slot<Notification>()
        verify { notificationService.send(capture(notificationSlot)) }
        val notification = notificationSlot.captured
        assertEquals(NotificationType.OnboardingSpecialistEmail, notification.type)
        assertEquals(
            "Onboarding Specialist Help Required for ContractID $contractId", notification.subject)
        assertEquals("<EMAIL>", notification.from)
        assertEquals(recipient, notification.to)
        assertEquals(listOf("John Doe <<EMAIL>>"), notification.cc)
        assertEquals(emptyList<String>(), notification.attachments)
        assertEquals(
            mapOf(
                "message" to message,
                "contractId" to contractId.toString(),
                "requesterType" to "customer"),
            notification.data)
        assertEquals("<EMAIL>", response)
    }

    @Test
    internal fun sendOnboardingSpecialistEmail_ForTestCompany() {
        every { companyService.getCompany(any()) } returns
            Company(
                id = 1L,
                logoId = 1L,
                displayName = "Test Company",
                countryFullName = "Test Country",
                countryCode = CountryCode.VNM,
                msaSigned = true,
                isTest = true,
            )
        every { currentUser.context } returns
            mockk(relaxed = true) {
                every { id } returns 1L
                every { experience } returns "member"
            }
        every { memberService.getMemberByUserId(1L) } returns
            Member(
                id = 100L,
                userId = "200",
                firstName = "John",
                lastName = "Doe",
                fullName = "John Doe",
                email = "<EMAIL>",
                persona = Persona("member"))
        every { notificationService.send(any()) } returns Unit

        onboardingSpecialistService.sendEmailToOnboardingSpecialist(123L, "Hello")

        val notificationSlot = slot<Notification>()
        verify { notificationService.send(capture(notificationSlot)) }
        val notification = notificationSlot.captured

        assertEquals(opsEmailResolver.getOnboardingSpecialistTestEmail(), notification.to)
    }

    @Test
    internal fun getOnboardingSpecialistEmail_notFound() {
        every { operationsUserService.getOperationsUsersForContracts(listOf(contractId)) } returns
            emptyMap()

        assertThrows(MplBusinessException::class.java) {
            onboardingSpecialistService.sendEmailToOnboardingSpecialist(contractId, "Hello")
        }
    }

    @ParameterizedTest
    @CsvSource("company, customer", "member, member")
    internal fun sendEmailToOnboardingSpecialist_asCompanyUser(
        userExperience: String,
        requesterType: String
    ) {
        val message = "Hello, onboarding specialist!"
        val recipient = "<EMAIL>"

        every { operationsUserService.getOperationsUsersForContracts(listOf(contractId)) } returns
            mapOf(
                contractId to
                    OperationsUser(
                        1L, email = recipient, firstName = "firstName", lastName = "lastName"))

        every { currentUser.context } returns
            mockk(relaxed = true) {
                every { id } returns 1L
                every { experience } returns userExperience
            }

        every { companyService.getCompanyUserByUserId(1L) } returns
            CompanyUser(
                id = 100L,
                companyId = 1L,
                firstName = "John",
                lastName = "Doe",
                email = "${userExperience}@company.com")

        every { memberService.getMemberByUserId(1L) } returns
            Member(
                id = 100L,
                userId = "200",
                firstName = "John",
                lastName = "Doe",
                fullName = "John Doe",
                email = "${userExperience}@company.com",
                persona = Persona("member"))

        every { notificationService.send(any()) } returns Unit

        onboardingSpecialistService.sendEmailToOnboardingSpecialist(contractId, message)

        // Verify that the notification was sent with the correct parameters
        val notificationSlot = slot<Notification>()
        verify { notificationService.send(capture(notificationSlot)) }
        val notification = notificationSlot.captured
        assertEquals("<EMAIL>", notification.from)
        assertEquals(listOf("John Doe <${userExperience}@company.com>"), notification.cc)
        assertEquals(
            mapOf(
                "message" to message,
                "contractId" to contractId.toString(),
                "requesterType" to requesterType),
            notification.data)
    }

    @Test
    internal fun throwException_experienceNotFound() {
        every { currentUser.context?.experience } returns null

        assertThrows(MplBusinessException::class.java) {
            onboardingSpecialistService.sendEmailToOnboardingSpecialist(contractId, "Hello")
        }
    }

    @Test
    internal fun throwException_experienceOperations() {
        every { currentUser.context?.experience } returns "operations"

        assertThrows(MplBusinessException::class.java) {
            onboardingSpecialistService.sendEmailToOnboardingSpecialist(contractId, "Hello")
        }
    }
}
