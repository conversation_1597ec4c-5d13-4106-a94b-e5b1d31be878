package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.contract.onboarding.adapter.bulk.BulkCompensationSchemaServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkCompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkCompensationModuleV2Test {
    @MockK
    private lateinit var bulkCompensationSchemaServiceAdapter: BulkCompensationSchemaServiceAdapter

    @MockK private lateinit var bulkCompensationServiceAdapter: BulkCompensationServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkCompensationModuleV2

    @Nested
    inner class GetDataSpecs {

        @Test
        fun `should return specific fields for freelancer`() {

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        null, ContractType.FREELANCER, 1, null, BulkOnboardingContext.FREELANCER),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "payrollFrequency",
                        type = DataSpecType.SELECT,
                        description = "Expected payout frequency",
                        allowedValues =
                            listOf(PayFrequency.MONTHLY.name, PayFrequency.SEMIMONTHLY.name)),
                    DataSpec(
                        key = "rateFrequency",
                        type = DataSpecType.SELECT,
                        description = "Billing Rate frequency of the contractor",
                        allowedValues =
                            listOf(
                                RateFrequency.HOURLY.name,
                                RateFrequency.DAILY.name,
                                RateFrequency.SEMIMONTHLY.name,
                                RateFrequency.MONTHLY.name)),
                    DataSpec(
                        key = "currency",
                        type = DataSpecType.SELECT,
                        description = "Billing currency of the contractor",
                        allowedValues = enumValues<CurrencyCode>().map { it.name }),
                    DataSpec(
                        key = "basePay",
                        description = "Base Pay Rate of the contractor",
                        type = DataSpecType.NUMBER),
                )
        }

        @Test
        fun `should return specific fields for aor`() {

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "payrollFrequency",
                        type = DataSpecType.SELECT,
                        description = "Expected payout frequency",
                        allowedValues =
                            listOf(PayFrequency.MONTHLY.name, PayFrequency.SEMIMONTHLY.name)),
                    DataSpec(
                        key = "rateFrequency",
                        type = DataSpecType.SELECT,
                        description = "Billing Rate frequency of the contractor",
                        allowedValues =
                            listOf(
                                RateFrequency.HOURLY.name,
                                RateFrequency.DAILY.name,
                                RateFrequency.SEMIMONTHLY.name,
                                RateFrequency.MONTHLY.name)),
                    DataSpec(
                        key = "currency",
                        type = DataSpecType.SELECT,
                        description = "Billing currency of the contractor",
                        allowedValues = enumValues<CurrencyCode>().map { it.name }),
                    DataSpec(
                        key = "basePay",
                        description = "Base Pay Rate of the contractor",
                        type = DataSpecType.NUMBER),
                )
        }

        @Test
        fun `should return specific fields for eor and remove employee id spec`() {

            every {
                bulkCompensationSchemaServiceAdapter.getDataSpecs(
                    options = any(), moduleParams = any())
            } returns
                listOf(
                    DataSpec(
                        key = "CURRENCY",
                        type = DataSpecType.SELECT,
                    ),
                    DataSpec(
                        key = "employeeId",
                        type = DataSpecType.SELECT,
                    ))

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        null, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "rowIdentifier",
                        type = DataSpecType.TEXT,
                        label = "Row Identifier",
                        description =
                            "Unique row identifier for employee data across upload sheets",
                        source = "COMPENSATION_SCHEMA_DATA_SPEC",
                        mandatory = true),
                    DataSpec(
                        key = "CURRENCY",
                        source = "COMPENSATION_SCHEMA_DATA_SPEC",
                        type = DataSpecType.SELECT))
        }

        @Test
        fun `should throw error for unsupported context`() {
            val exception =
                Assertions.assertThrows(MplBusinessException::class.java) {
                    underTest.getDataSpecs(
                        BulkOnboardingOptions(
                            null,
                            ContractType.HR_MEMBER,
                            1,
                            null,
                            BulkOnboardingContext.HRIS_PROFILE_DATA),
                        null)
                }

            Assertions.assertEquals(
                "Bulk Onboarding context value HRIS_PROFILE_DATA not supported yet",
                exception.message)
        }
    }

    @Nested
    inner class Validate {

        @Test
        fun `should call bulkCompensationServiceAdapter for freelancer`() {

            val input =
                listOf(
                    EmployeeData(
                        identification = EmployeeIdentification(rowNumber = 1),
                        data = mapOf("contractId" to "123", "email" to "<EMAIL>")))
            val options =
                BulkOnboardingOptions(
                    null, ContractType.FREELANCER, 1, null, BulkOnboardingContext.FREELANCER)

            every {
                bulkCompensationServiceAdapter.validateCompensationCreateInputs(input, options)
            } returns emptyList()

            underTest.validate(input, options, null)

            verify {
                bulkCompensationServiceAdapter.validateCompensationCreateInputs(input, options)
            }
        }

        @Test
        fun `should call bulkCompensationServiceAdapter for aor`() {

            val input =
                listOf(
                    EmployeeData(
                        identification = EmployeeIdentification(rowNumber = 1),
                        data = mapOf("contractId" to "123", "email" to "<EMAIL>")))
            val options =
                BulkOnboardingOptions(
                    null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR)

            every {
                bulkCompensationServiceAdapter.validateCompensationCreateInputs(input, options)
            } returns emptyList()

            underTest.validate(input, options, null)

            verify {
                bulkCompensationServiceAdapter.validateCompensationCreateInputs(input, options)
            }
        }
    }

    @Nested
    inner class CreateData {

        @Test
        fun `should call bulkCompensationServiceAdapter for freelancer upsert`() {

            val validationResult =
                listOf(
                    GrpcValidationResult(
                        validationId = "rn_1",
                        success = true,
                        input = CompensationOuterClass.CreateCompensationInput.newBuilder().build(),
                        errors = emptyList(),
                        additionalInputProperties = mapOf("module1AddedKey" to "value1")))
            val options =
                BulkOnboardingOptions(
                    null, ContractType.FREELANCER, 1, null, BulkOnboardingContext.FREELANCER)

            every { bulkCompensationServiceAdapter.createCompensations(any(), options) } returns
                emptyList()

            val creationResult = BulkCreationResultV2(requestIdToMemberId = mapOf("test2" to 2))

            underTest.create(validationResult, creationResult, options)

            verify {
                bulkCompensationServiceAdapter.createCompensations(
                    withArg { assertThat(it).hasSize(1) },
                    withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.FREELANCER) })
            }
        }

        @Test
        fun `should call bulkCompensationServiceAdapter for aor upsert`() {

            val validationResult =
                listOf(
                    GrpcValidationResult(
                        validationId = "rn_1",
                        success = true,
                        input = CompensationOuterClass.CreateCompensationInput.newBuilder().build(),
                        errors = emptyList(),
                        additionalInputProperties = mapOf("module1AddedKey" to "value1")))
            val options =
                BulkOnboardingOptions(
                    null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR)

            every { bulkCompensationServiceAdapter.createCompensations(any(), options) } returns
                emptyList()

            val creationResult = BulkCreationResultV2(requestIdToMemberId = mapOf("test2" to 2))

            underTest.create(validationResult, creationResult, options)

            verify {
                bulkCompensationServiceAdapter.createCompensations(
                    withArg { assertThat(it).hasSize(1) },
                    withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.AOR) })
            }
        }
    }
}
