import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.types.CountryCode
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks

@ExtendWith(MockKExtension::class)
class CountryCacheTest {

    @MockK private lateinit var countryService: CountryServiceAdapter

    @InjectMocks private lateinit var underTest: CountryCache

    @BeforeEach
    fun setUp() {
        every { countryService.getCountryNamesByCodes(any()) } returns
            mapOf(
                CountryCode.USA to "United States",
                CountryCode.GBR to "United Kingdom",
                CountryCode.IND to "India")

        underTest = CountryCache(countryService)
        underTest.populateCache() // Simulate @PostConstruct execution
    }

    @Test
    fun `should populate cache correctly`() {
        assertEquals("United States", underTest.getCountryName(CountryCode.USA))
        assertEquals("United Kingdom", underTest.getCountryName(CountryCode.GBR))
        assertEquals("India", underTest.getCountryName(CountryCode.IND))
    }

    @Test
    fun `should return correct country code when name is provided`() {
        assertEquals(CountryCode.USA, underTest.getCountryCode("United States"))
        assertEquals(
            CountryCode.GBR, underTest.getCountryCode("united kingdom")) // Case insensitive
        assertEquals(CountryCode.IND, underTest.getCountryCode("INDIA")) // Case insensitive
    }

    @Test
    fun `should return null for unknown country code or name`() {
        assertNull(underTest.getCountryName(CountryCode.PAK))
        assertNull(underTest.getCountryCode("Unknown Country"))
    }

    @Test
    fun `should not cache or return blank country name`() {
        every { countryService.getCountryNamesByCodes(listOf(CountryCode.CAN)) } returns
            mapOf(CountryCode.CAN to "")

        val result = underTest.getCountryName(CountryCode.CAN)

        assertNull(result)
        underTest.getCountryName(CountryCode.CAN)

        verify(exactly = 2) { countryService.getCountryNamesByCodes(listOf(CountryCode.CAN)) }
    }

    @Test
    fun `should clear cache on PreDestroy`() {
        underTest.clearCache()
        assertNull(underTest.getCountryCode("United States"))
    }

    @AfterEach
    fun tearDown() {
        verify(atLeast = 1) { countryService.getCountryNamesByCodes(any()) }
    }
}
