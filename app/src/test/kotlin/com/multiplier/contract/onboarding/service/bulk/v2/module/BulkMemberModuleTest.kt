package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberServiceAdapter
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.member.ApplyTo
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataDefinition
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataType
import com.multiplier.contract.onboarding.types.*
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberModuleTest {
    @MockK private lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockK private lateinit var bulkMemberServiceAdapter: BulkMemberServiceAdapter

    @MockK private lateinit var countryCache: CountryCache

    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkMemberModule

    @Nested
    inner class GetDataSpecs {

        @Test
        fun `should return specific fields for aor`() {
            every { countryCache.getCountryName(any()) } returns "India"

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(key = "firstName", description = "First name of the contractor"),
                    DataSpec(key = "lastName", description = "Last name of the contractor"),
                    DataSpec(key = "email", description = "Email address of the contractor"),
                    DataSpec(
                        key = "gender",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("FEMALE", "MALE", "OTHER", "ALL"),
                        description = "Gender of the contractor"),
                )
        }

        @Test
        fun `should return specific fields for eor`() {
            // Setup
            val options =
                BulkOnboardingOptions(
                    CountryCode.IND, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

            val legalDataDefinition =
                MemberLegalDataDefinition(
                    key = "customField",
                    type = MemberLegalDataType.TEXT,
                    mandatory = true,
                    description = "Custom field from country service",
                    fetchStage = FetchStage.CONTRACT_GENERATION,
                    applyTo = ApplyTo.ALL,
                    domainType = DomainType.MEMBER_DATA)

            every {
                countryServiceAdapter.getMemberLegalDataDefinitions(
                    CountryCode.IND, ContractType.EMPLOYEE)
            } returns listOf(legalDataDefinition)

            // Execute
            val dataSpecs = underTest.getDataSpecs(options, null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(key = "firstName", description = "First name of the employee"),
                    DataSpec(key = "lastName", description = "Last name of the employee"),
                    DataSpec(key = "email", description = "Email address of the employee"),
                    DataSpec(
                        key = "gender",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("FEMALE", "MALE", "OTHER", "ALL"),
                        description = "Gender of the employee"),
                    DataSpec(
                        key = "customField",
                        type = DataSpecType.TEXT,
                        label = "Custom field from country service",
                        source = "LEGAL_SPEC",
                        mandatory = true),
                )

            verify(exactly = 1) {
                countryServiceAdapter.getMemberLegalDataDefinitions(
                    CountryCode.IND, ContractType.EMPLOYEE)
            }
        }

        @Test
        fun `should handle exceptions from country service for eor`() {
            // Setup
            val options =
                BulkOnboardingOptions(
                    CountryCode.IND, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

            every {
                countryServiceAdapter.getMemberLegalDataDefinitions(
                    CountryCode.IND, ContractType.EMPLOYEE)
            } throws RuntimeException("Service unavailable")

            // Execute
            val dataSpecs = underTest.getDataSpecs(options, null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(key = "firstName", description = "First name of the employee"),
                    DataSpec(key = "lastName", description = "Last name of the employee"),
                    DataSpec(key = "email", description = "Email address of the employee"),
                    DataSpec(
                        key = "gender",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("FEMALE", "MALE", "OTHER", "ALL"),
                        description = "Gender of the employee"),
                )

            verify(exactly = 1) {
                countryServiceAdapter.getMemberLegalDataDefinitions(
                    CountryCode.IND, ContractType.EMPLOYEE)
            }
        }

        @Test
        fun `should filter member data definitions by fetch stage and domain type`() {
            // Setup
            val options =
                BulkOnboardingOptions(
                    CountryCode.IND, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR)

            val validDefinition =
                MemberLegalDataDefinition(
                    key = "customField",
                    type = MemberLegalDataType.TEXT,
                    mandatory = true,
                    description = "Custom field from country service",
                    fetchStage = FetchStage.CONTRACT_GENERATION,
                    applyTo = ApplyTo.ALL,
                    domainType = DomainType.MEMBER_DATA)

            val wrongFetchStageDefinition =
                MemberLegalDataDefinition(
                    key = "wrongStageField",
                    type = MemberLegalDataType.TEXT,
                    mandatory = true,
                    description = "Wrong stage field",
                    fetchStage = FetchStage.MEMBER_LEGAL_DATA_CAPTURE, // Different fetch stage
                    applyTo = ApplyTo.ALL,
                    domainType = DomainType.MEMBER_DATA)

            val wrongDomainTypeDefinition =
                MemberLegalDataDefinition(
                    key = "wrongDomainField",
                    type = MemberLegalDataType.TEXT,
                    mandatory = true,
                    description = "Wrong domain field",
                    fetchStage = FetchStage.CONTRACT_GENERATION,
                    applyTo = ApplyTo.ALL,
                    domainType = DomainType.LEGAL_DATA // Different domain type
                    )

            every {
                countryServiceAdapter.getMemberLegalDataDefinitions(
                    CountryCode.IND, ContractType.EMPLOYEE)
            } returns listOf(validDefinition, wrongFetchStageDefinition, wrongDomainTypeDefinition)

            // Execute
            val dataSpecs = underTest.getDataSpecs(options, null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(key = "firstName", description = "First name of the employee"),
                    DataSpec(key = "lastName", description = "Last name of the employee"),
                    DataSpec(key = "email", description = "Email address of the employee"),
                    DataSpec(
                        key = "gender",
                        type = DataSpecType.SELECT,
                        allowedValues = listOf("FEMALE", "MALE", "OTHER", "ALL"),
                        description = "Gender of the employee"),
                    DataSpec(
                        key = "customField",
                        type = DataSpecType.TEXT,
                        label = "Custom field from country service",
                        source = "LEGAL_SPEC",
                        mandatory = true),
                )
        }
    }
}
