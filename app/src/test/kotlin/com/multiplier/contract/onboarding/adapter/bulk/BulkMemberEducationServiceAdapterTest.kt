package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.BulkUpsertEducationDetailsResponse
import com.multiplier.member.schema.BulkUpsertEmergencyContactResult
import com.multiplier.member.schema.BulkValidateRequest
import com.multiplier.member.schema.MemberEducationDetail
import com.multiplier.member.schema.MemberEducationDetails
import com.multiplier.member.schema.UpsertEducationDetailsInput
import com.multiplier.member.schema.UpsertEducationDetailsValidationResult
import com.multiplier.member.schema.ValidateUpsertEducationDetailsResponse
import com.multiplier.member.schema.ValidationInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberEducationServiceAdapterTest {

    @MockK private lateinit var bulkMemberServiceMock: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    @InjectMockKs private lateinit var bulkEducationAdapter: BulkMemberEducationServiceAdapter

    @Test
    fun `should return correct validated result when validate education details`() {
        val employeeData =
            listOf(
                EmployeeData(
                    EmployeeIdentification(rowNumber = 1),
                    data = mapOf("lastSchoolName" to "Harvard University")))

        val grpcRequest =
            BulkValidateRequest.newBuilder()
                .setContext(BulkOnboardingContext.HRIS_PROFILE_DATA.name)
                .addAllInputs(employeeData.map { it.toGrpc() })
                .build()
        val grpcResponse =
            ValidateUpsertEducationDetailsResponse.newBuilder()
                .addAllValidationResults(
                    listOf(
                        UpsertEducationDetailsValidationResult.newBuilder()
                            .setSuccess(true)
                            .addAllErrors(emptyList())
                            .setRequestId("1")
                            .setValidatedInput(
                                UpsertEducationDetailsInput.newBuilder().setRequestId("1").build())
                            .build()))
                .build()

        every { bulkMemberServiceMock.validateUpsertEducationDetails(grpcRequest) } returns
            grpcResponse

        val expected =
            grpcResponse.validationResultsList.map {
                GrpcValidationResult(
                    success = it.success,
                    errors = it.errorsList,
                    validationId = it.requestId,
                    input = it.validatedInput,
                )
            }

        val option =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                .build()
        assertEquals(
            expected, bulkEducationAdapter.validateUpsertEducationDetails(employeeData, option))
    }

    @Test
    fun `should return empty error list when upsert education details data`() {
        val inputs =
            listOf(
                CreationInput(
                    requestId = "1",
                    memberId = 2,
                    data =
                        UpsertEducationDetailsInput.newBuilder()
                            .setEducationDetails(
                                MemberEducationDetails.newBuilder()
                                    .addEducationDetails(
                                        MemberEducationDetail.newBuilder()
                                            .setInstitutionName("Harvard University")
                                            .build())
                                    .build())
                            .build()))

        val grpcResponse =
            BulkUpsertEducationDetailsResponse.newBuilder()
                .addAllResults(
                    listOf(BulkUpsertEmergencyContactResult.newBuilder().setSuccess(true).build()))
                .build()
        every { bulkMemberServiceMock.bulkUpsertEducationDetails(any()) } returns grpcResponse

        assertEquals(
            emptyList<String>(),
            bulkEducationAdapter.upsertEducationDetails(inputs).flatMap { it.errors })
        verify {
            bulkMemberServiceMock.bulkUpsertEducationDetails(
                withArg {
                    val educationInput = it.inputsList.first()
                    assertAll(
                        { assertEquals(1, it.inputsList.size) },
                        { assertEquals("1", educationInput.requestId) },
                        { assertEquals(2, educationInput.educationDetails.memberId) },
                        {
                            assertEquals(
                                "Harvard University",
                                educationInput.educationDetails.educationDetailsList
                                    .first()
                                    .institutionName)
                        })
                })
        }
    }

    @Test
    fun `should return empty list when upsert education details data with empty input`() {
        assertEquals(emptyList<String>(), bulkEducationAdapter.upsertEducationDetails(emptyList()))

        verify(exactly = 0) { bulkMemberServiceMock.bulkUpsertEducationDetails(any()) }
    }

    @Test
    fun `should return error list when bulk upsert education details data`() {
        val inputs =
            listOf(
                CreationInput(
                    requestId = "1",
                    memberId = 2,
                    data =
                        UpsertEducationDetailsInput.newBuilder()
                            .setEducationDetails(
                                MemberEducationDetails.newBuilder().setMemberId(1).build())
                            .build()))

        val errors = listOf("member id 1 failed to upsert")
        val grpcResponse =
            BulkUpsertEducationDetailsResponse.newBuilder()
                .addAllResults(
                    listOf(
                        BulkUpsertEmergencyContactResult.newBuilder()
                            .setSuccess(false)
                            .addAllErrors(errors)
                            .build()))
                .build()
        every { bulkMemberServiceMock.bulkUpsertEducationDetails(any()) } returns grpcResponse

        assertEquals(
            errors, bulkEducationAdapter.upsertEducationDetails(inputs).flatMap { it.errors })
    }

    @Test
    fun `should return error list when bulk upsert education details data throws exception`() {
        val inputs =
            listOf(
                CreationInput(
                    requestId = "1",
                    memberId = 2,
                    data =
                        UpsertEducationDetailsInput.newBuilder()
                            .setEducationDetails(
                                MemberEducationDetails.newBuilder().setMemberId(1).build())
                            .build()))
        every { bulkMemberServiceMock.bulkUpsertEducationDetails(any()) } throws
            IllegalArgumentException()

        assertEquals(
            listOf(
                "Upsert education details failed due to an internal error: unknown exception occurred"),
            bulkEducationAdapter.upsertEducationDetails(inputs).flatMap { it.errors })
    }

    private fun EmployeeData.toGrpc() =
        ValidationInput.newBuilder()
            .setRequestId(this.identification.validationId)
            .putAllProperties(this.data)
            .build()
}
