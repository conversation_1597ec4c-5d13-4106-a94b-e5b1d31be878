package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractFilters
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.testing.faker
import com.multiplier.contract.schema.contract.ContractOuterClass
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkValidationHelperTest {

    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkValidationHelper

    @Test
    fun validateDuplicateEmployeeIds() {
        val employeeId = "abc"

        val employeeData =
            listOf(
                EmployeeData(
                    identification = EmployeeIdentification(employeeId = employeeId, rowNumber = 2),
                    data = mapOf("employeeId" to employeeId)),
                EmployeeData(
                    identification = EmployeeIdentification(employeeId = employeeId, rowNumber = 3),
                    data = mapOf("employeeId" to employeeId)))

        val validationResult = underTest.validateDuplicateEmployeeIds(employeeData)

        val expected =
            listOf(
                GrpcValidationResult(
                    success = false,
                    errors =
                        listOf(
                            "this employee id [$employeeId] is used multiple times in this sheet"),
                    validationId = employeeData[0].identification.validationId,
                    input = null,
                ),
                GrpcValidationResult(
                    success = false,
                    errors =
                        listOf(
                            "this employee id [$employeeId] is used multiple times in this sheet"),
                    validationId = employeeData[1].identification.validationId,
                    input = null,
                ))

        Assertions.assertThat(validationResult).isEqualTo(expected)
    }

    @Test
    fun validateExistingEmployeeIds() {
        val employeeId = faker.random.randomString()
        val companyId = faker.random.nextLong()

        every {
            contractServiceAdapter.getContractsBy(ContractFilters(companyIds = listOf(companyId)))
        } returns listOf(ContractOuterClass.Contract.newBuilder().setEmployeeId(employeeId).build())

        val employeeData =
            listOf(
                EmployeeData(
                    identification = EmployeeIdentification(employeeId = employeeId, rowNumber = 2),
                    data = mapOf("employeeId" to employeeId)))

        val validationResult = underTest.validateExistingEmployeeIds(employeeData, companyId)

        val expected =
            listOf(
                GrpcValidationResult(
                    success = false,
                    errors = listOf("this employee id [$employeeId] is already taken"),
                    validationId = employeeData[0].identification.validationId,
                    input = null,
                ))

        Assertions.assertThat(validationResult).isEqualTo(expected)
    }
}
