package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.service.reminders.OnboardingCtaReminderService
import com.multiplier.contract.onboarding.types.NotificationType
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class OnboardingRemindersMutatorTest {

    private val onboardingCtaReminderService = mockk<OnboardingCtaReminderService>()
    private val underTest =
        OnboardingRemindersMutator(
            onboardingReminders = mockk(),
            employeeIdAndWorkEmailReminders = mockk(),
            onboardingCtaReminderService = onboardingCtaReminderService)

    @Test
    fun `sendOpsEmailCtaReminder should call service without editable template params`() {
        // Given
        val contractId = 123L
        val templateName = NotificationType.OpsMemberSignContractReminderCta

        every {
            onboardingCtaReminderService.sendReminderForTemplate(contractId, templateName)
        } returns Unit

        // When
        val result =
            underTest.sendOpsEmailCtaReminder(contractId = contractId, templateName = templateName)

        // Then
        assertThat(result.success).isTrue()
        assertThat(result.message).isEqualTo("Onboarding CTA reminders - sent successfully")

        verify { onboardingCtaReminderService.sendReminderForTemplate(contractId, templateName) }
    }

    @Test
    fun `sendEditableOpsEmailCtaReminder should call service with editable template params`() {
        // Given
        val contractId = 123L
        val templateName = NotificationType.OpsMemberSignContractReminderCta
        val editableTemplateParams =
            mapOf("cutoffDate" to "15th January 2030", "cutoffMonthName" to "February")

        every {
            onboardingCtaReminderService.sendReminderForTemplate(
                contractId, templateName, editableTemplateParams)
        } returns Unit

        // When
        val result =
            underTest.sendEditableOpsEmailCtaReminder(
                contractId = contractId,
                templateName = templateName,
                editableTemplateParams = editableTemplateParams)

        // Then
        assertThat(result.success).isTrue()
        assertThat(result.message).isEqualTo("Onboarding CTA reminders - sent successfully")

        verify {
            onboardingCtaReminderService.sendReminderForTemplate(
                contractId, templateName, editableTemplateParams)
        }
    }

    @Test
    fun `sendEditableOpsEmailCtaReminder should work without editable template params`() {
        // Given
        val contractId = 123L
        val templateName = NotificationType.OpsMemberSignContractReminderCta

        every {
            onboardingCtaReminderService.sendReminderForTemplate(contractId, templateName, null)
        } returns Unit

        // When
        val result =
            underTest.sendEditableOpsEmailCtaReminder(
                contractId = contractId, templateName = templateName, editableTemplateParams = null)

        // Then
        assertThat(result.success).isTrue()
        assertThat(result.message).isEqualTo("Onboarding CTA reminders - sent successfully")

        verify {
            onboardingCtaReminderService.sendReminderForTemplate(contractId, templateName, null)
        }
    }
}
