package com.multiplier.contract.onboarding.domain.model

import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class OnboardingTaskKtTest {

    @Test
    fun `should convert onboarding task to property map`() {
        val task =
            OnboardingTask(
                name = OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS,
                completed = false,
                pendingOn = Person(fullName = "Harry Potter"),
            )

        val actual = task.convertToMapWithNameAsText()

        assertThat(actual)
            .isEqualTo(
                mapOf(
                    "name" to OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS.text,
                    "completed" to false,
                    "pendingOn" to "Harry Potter",
                ))
    }
}
