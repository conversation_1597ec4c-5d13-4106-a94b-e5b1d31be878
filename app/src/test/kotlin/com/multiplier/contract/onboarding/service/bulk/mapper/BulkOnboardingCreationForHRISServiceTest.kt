package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractType
import com.multiplier.contract.schema.contract.ContractOuterClass.DirectEmployeeOnboardingFlowType
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkOnboardingCreationForHRISServiceTest {

    @MockK lateinit var contractServiceAdapter: ContractServiceAdapter

    @InjectMockKs lateinit var underTest: BulkOnboardingBuilderForHRISService

    @BeforeEach
    fun setUp() {
        every { contractServiceAdapter.getNonDeletedNonEndedContracts(any()) } returns
            listOf(
                Contract.newBuilder()
                    .setId(1)
                    .setCompanyId(11L)
                    .setLegalEntityId(111)
                    .setType(ContractType.HR_MEMBER)
                    .build(),
                Contract.newBuilder()
                    .setId(2)
                    .setCompanyId(22L)
                    .setLegalEntityId(222)
                    .setType(ContractType.HR_MEMBER)
                    .build(),
                Contract.newBuilder()
                    .setId(3)
                    .setCompanyId(33L)
                    .setLegalEntityId(333)
                    .setType(ContractType.HR_MEMBER)
                    .build(),
                Contract.newBuilder()
                    .setId(4)
                    .setCompanyId(44L)
                    .setLegalEntityId(444)
                    .setType(ContractType.HR_MEMBER)
                    .build(),
                Contract.newBuilder()
                    .setId(5)
                    .setCompanyId(11L)
                    .setType(ContractType.EMPLOYEE)
                    .build(),
            )
        every { contractServiceAdapter.getDirectEmployeeOnboardingFlowTypes(any()) } returns
            mapOf(
                1L to DirectEmployeeOnboardingFlowType.GP_ONLY,
                2L to DirectEmployeeOnboardingFlowType.HRIS_ONLY,
                3L to DirectEmployeeOnboardingFlowType.GP_ONLY,
                4L to DirectEmployeeOnboardingFlowType.NONE,
            )
    }

    @Test
    fun `should return onboardings for GP only flow`() {
        val results = underTest.buildOnboardingsForHrisProfileData(listOf(1))

        assertThat(results.filter { it.contractId == 1L }).hasSize(2)
        assertThat(results)
            .contains(
                Onboarding(
                    contractId = 1,
                    experience = "company",
                    status = OnboardingStatus.MEMBER_VERIFICATION_COMPLETED,
                    currentStep = OnboardingStep.ONBOARDING_ACTIVATION,
                    isBulkOnboarded = true),
                Onboarding(
                    contractId = 1,
                    experience = "member",
                    status = OnboardingStatus.MEMBER_VERIFICATION_COMPLETED,
                    currentStep = OnboardingStep.ONBOARDING_ACTIVATION,
                    isBulkOnboarded = true))
    }

    @Test
    fun `should return onboardings for HRIS only flow`() {
        val results = underTest.buildOnboardingsForHrisProfileData(listOf(2))

        assertThat(results.filter { it.contractId == 2L }).hasSize(1)
        assertThat(results)
            .contains(
                Onboarding(
                    contractId = 2,
                    experience = "company",
                    status = OnboardingStatus.CREATED_CUSTOM,
                    currentStep = OnboardingStep.ONBOARDING_REVIEW,
                    isBulkOnboarded = true))
    }

    @Test
    fun `should return fallback onboardings if onboarding flow not found`() {
        val results = underTest.buildOnboardingsForHrisProfileData(listOf(4))

        assertThat(results.filter { it.contractId == 4L }).hasSize(1)
        assertThat(results)
            .contains(
                Onboarding(
                    contractId = 4,
                    experience = "company",
                    status = OnboardingStatus.CREATED_CUSTOM,
                    currentStep = OnboardingStep.ONBOARDING_REVIEW,
                    isBulkOnboarded = true))
    }

    @Test
    fun `should return nothing for non-direct employee contract`() {
        val results = underTest.buildOnboardingsForHrisProfileData(listOf(4))

        assertThat(results.filter { it.contractId == 5L }).isEmpty()
    }
}
