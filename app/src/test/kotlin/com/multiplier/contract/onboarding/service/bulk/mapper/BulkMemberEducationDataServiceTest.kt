package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberEducationServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.EducationDetail
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolDegreeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolGpaSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolGradeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolYearOfPassingSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.UpsertEducationDetailsInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberEducationDataServiceTest {

    @MockK private lateinit var memberServiceAdapterMock: MemberServiceAdapter

    @MockK private lateinit var bulkEducationAdapter: BulkMemberEducationServiceAdapter

    @InjectMockKs
    private lateinit var bulkMemberEducationDataService: BulkMemberEducationDataService

    @Test
    fun `should return correct validated result when validating education details`() {
        val employees =
            listOf(
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 1), data = emptyMap()))
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                .build()

        val expected =
            listOf(
                GrpcValidationResult<UpsertEducationDetailsInput>(
                    success = true, errors = emptyList(), validationId = "1", input = null))

        every { bulkEducationAdapter.validateUpsertEducationDetails(employees, options) } returns
            expected

        assertEquals(expected, bulkMemberEducationDataService.validate(employees, options))
    }

    @Test
    fun `should return error message when upsert education details failed`() {
        val inputs =
            listOf(
                ValidInput(
                    validationId = "1",
                    input = UpsertEducationDetailsInput.newBuilder().setRequestId("1").build()))

        val expected = listOf("Not good")
        every { bulkEducationAdapter.upsertEducationDetails(any()) } returns
            listOf(CreationResult.error(requestId = "1", errors = expected))

        val memberContext = MemberContext(mapOf("1" to 1))
        assertEquals(
            expected,
            bulkMemberEducationDataService.upsertEducationDetails(
                inputs, memberContext, BulkOnboardingOptions()))
    }

    @Test
    fun `should return correct data context when get data for specs`() {
        val educationDetails =
            listOf(
                EducationDetail(
                    memberId = 1,
                    lastSchoolName = "Harvard University",
                    lastSchoolDegree = "Computer Science",
                    lastSchoolYearOfPassing = 2017,
                    lastSchoolGpa = 4.0f,
                    lastSchoolGrade = "Good"))
        every { memberServiceAdapterMock.getMemberEducationDetails(any()) } returns educationDetails

        val expected =
            MemberDataContext(
                mapOf(
                    1L to
                        EmployeeDataChunk(
                            mapOf(
                                LastSchoolNameSpec.key to "Harvard University",
                                LastSchoolDegreeSpec.key to "Computer Science",
                                LastSchoolYearOfPassingSpec.key to "2017",
                                LastSchoolGpaSpec.key to "4.0",
                                LastSchoolGradeSpec.key to "Good"))))
        val dataSpecs =
            listOf(
                LastSchoolNameSpec,
                LastSchoolDegreeSpec,
                LastSchoolYearOfPassingSpec,
                LastSchoolGpaSpec,
                LastSchoolGradeSpec)
        assertEquals(
            expected, bulkMemberEducationDataService.getDataForSpecs(dataSpecs, emptySet()))
    }
}
