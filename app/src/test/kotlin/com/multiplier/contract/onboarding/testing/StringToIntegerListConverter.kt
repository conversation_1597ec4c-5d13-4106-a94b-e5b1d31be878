package com.multiplier.contract.onboarding.testing

import org.junit.jupiter.params.converter.SimpleArgumentConverter

class StringToIntegerListConverter : SimpleArgumentConverter() {

    override fun convert(source: Any, targetType: Class<*>): Any {
        return if (source is String && List::class.java.isAssignableFrom(targetType)) {
            source.split(",").filter { it.isNotBlank() }.map { it.trim().toInt() }
        } else {
            throw IllegalArgumentException(
                "Conversion from " + source.javaClass + " to " + targetType + " not supported.")
        }
    }
}
