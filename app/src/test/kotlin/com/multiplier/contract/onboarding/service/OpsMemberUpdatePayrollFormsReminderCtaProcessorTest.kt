package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.adapter.CountryAndState
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.service.helpers.CompanyTestDataFactory.createCompany
import com.multiplier.contract.onboarding.service.helpers.ContractTestDataFactory.createContract
import com.multiplier.contract.onboarding.service.helpers.JpaOnboardingTestDataFactory.createJpaOnboarding
import com.multiplier.contract.onboarding.service.helpers.MemberTestDataFactory.createMember
import com.multiplier.contract.onboarding.service.helpers.OperationsUserTestDataFactory.createOperationsUser
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.DisplayNameGeneration
import org.junit.jupiter.api.DisplayNameGenerator
import org.junit.jupiter.api.Test

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
class OpsMemberUpdatePayrollFormsReminderCtaProcessorTest {

    private val processor = OpsMemberUpdatePayrollFormsReminderCtaProcessor()

    @Test
    fun validate_should_return_true_when_payroll_forms_required_and_not_submitted() {
        val requiredDocs =
            mapOf(
                CountryAndState(CountryCode.IND, "MH") to setOf("doc1", "doc2"),
                CountryAndState(CountryCode.IND, null) to setOf("doc3", "doc4"))

        val submittedDocs = setOf("doc1", "doc2")

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember = null,
                submittedLegalDocument = submittedDocs,
                payrollFormRequirements = requiredDocs,
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_false_when_payroll_forms_required_and_submitted() {
        val requiredDocs =
            mapOf(
                CountryAndState(CountryCode.IND, "MH") to setOf("doc3", "doc4"),
                CountryAndState(CountryCode.IND, null) to setOf("doc1", "doc2"))

        val submittedDocs = setOf("doc1", "doc2")

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember = null,
                submittedLegalDocument = submittedDocs,
                payrollFormRequirements = requiredDocs,
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_false_when_payroll_forms_required_and_submitted_for_state() {
        val requiredDocs = mapOf(CountryAndState(CountryCode.IND, "MH") to setOf("doc3", "doc4"))

        val submittedDocs = setOf("doc3", "doc4")

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember = null,
                submittedLegalDocument = submittedDocs,
                payrollFormRequirements = requiredDocs,
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_false_when_payroll_forms_not_required() {
        val requiredDocs = mapOf(CountryAndState(CountryCode.IND, null) to emptySet<String>())

        val submittedDocs = setOf("doc1", "doc2")

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember = null,
                submittedLegalDocument = submittedDocs,
                payrollFormRequirements = requiredDocs,
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_false_when_opsUser_is_not_assigned() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = null,
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_false_when_contract_not_eor() {
        val requiredDocs =
            mapOf(
                CountryAndState(CountryCode.IND, "MH") to setOf("doc1", "doc2"),
                CountryAndState(CountryCode.IND, null) to setOf("doc3", "doc4"))

        val submittedDocs = setOf("doc1", "doc2")

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember = null,
                submittedLegalDocument = submittedDocs,
                payrollFormRequirements = requiredDocs,
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(type = ContractOuterClass.ContractType.CONTRACTOR),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun fetchData_should_return_a_populated_list_of_EmailData_with_10_payroll_cutoff_for_PAK_and_JPN() {
        val member = createMember()

        val company = createCompany()

        val opsUser = createOperationsUser()

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = member,
                operationsUser = opsUser,
                company = company,
                contract = createContract(country = CountryCode.PAK),
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.fetchDataAndOverrideEditableParams(emailTemplateData)

        assertThat(result).hasSize(7)
        assertThat(result[ReminderCtaHelper.GUIDE_LINK]).isEqualTo("link")
        assertThat(result[ReminderCtaHelper.MEMBER_NAME]).isEqualTo(member.fullName)
        assertThat(result[ReminderCtaHelper.ONBOARDING_SPECIALIST])
            .isEqualTo(opsUser.firstName + " " + opsUser.lastName)
        assertThat(result[ReminderCtaHelper.CLIENT_NAME]).isEqualTo(company.displayName)
        assertThat(result[ReminderCtaHelper.MONTH_PAY_DATE]).isEqualTo("10th")
        assertThat(result[ReminderCtaHelper.IS_PRE_REGISTRATION_COUNTRY]).isEqualTo("false")
        assertThat(result[ReminderCtaHelper.MULTI_FREQUENCY_SUPPORT_ENABLED]).isEqualTo("false")
    }

    @Test
    fun fetchData_should_return_payrollCutoffDate_as_15_for_countries_other_than_PAK_and_JPN() {
        val member = createMember()

        val opsUser = createOperationsUser()

        val company = createCompany()

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = member,
                operationsUser = opsUser,
                company = company,
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.fetchDataAndOverrideEditableParams(emailTemplateData)

        Assertions.assertEquals(7, result.size)
        assertThat(result[ReminderCtaHelper.GUIDE_LINK]).isEqualTo("link")
        assertThat(result[ReminderCtaHelper.MEMBER_NAME]).isEqualTo(member.fullName)
        assertThat(result[ReminderCtaHelper.ONBOARDING_SPECIALIST])
            .isEqualTo(opsUser.firstName + " " + opsUser.lastName)
        assertThat(result[ReminderCtaHelper.CLIENT_NAME]).isEqualTo(company.displayName)
        assertThat(result[ReminderCtaHelper.MONTH_PAY_DATE]).isEqualTo("15th")
        assertThat(result[ReminderCtaHelper.IS_PRE_REGISTRATION_COUNTRY]).isEqualTo("false")
        assertThat(result[ReminderCtaHelper.MULTI_FREQUENCY_SUPPORT_ENABLED]).isEqualTo("false")
    }

    @Test
    fun validate_should_return_false_when_country_is_USA_and_multiFrequencySupportEnabled_is_true() {
        val emailTemplateData =
            createEmailTemplateData(
                country = CountryCode.USA,
                payrollFormRequirements = mapOf(CountryAndState(CountryCode.USA) to setOf("W4")),
                submittedLegalDocuments = emptySet(), // Missing W4
                multiFrequencySupportEnabled = true)

        val result = processor.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_true_when_country_is_USA_and_multiFrequencySupportEnabled_is_false() {
        val emailTemplateData =
            createEmailTemplateData(
                country = CountryCode.USA,
                payrollFormRequirements = mapOf(CountryAndState(CountryCode.USA) to setOf("W4")),
                submittedLegalDocuments = emptySet(), // Missing W4
                multiFrequencySupportEnabled = false)

        val result = processor.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_true_when_country_is_not_USA_and_multiFrequencySupportEnabled_is_true() {
        val emailTemplateData =
            createEmailTemplateData(
                country = CountryCode.SGP,
                payrollFormRequirements = mapOf(CountryAndState(CountryCode.SGP) to setOf("Form1")),
                submittedLegalDocuments = emptySet(), // Missing Form1
                multiFrequencySupportEnabled = true)

        val result = processor.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_true_when_country_is_not_USA_and_multiFrequencySupportEnabled_is_false() {
        val emailTemplateData =
            createEmailTemplateData(
                country = CountryCode.GBR,
                payrollFormRequirements = mapOf(CountryAndState(CountryCode.GBR) to setOf("Form2")),
                submittedLegalDocuments = emptySet(), // Missing Form2
                multiFrequencySupportEnabled = false)

        val result = processor.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    private fun createEmailTemplateData(
        country: CountryCode = CountryCode.SGP,
        multiFrequencySupportEnabled: Boolean = false,
        contractType: ContractOuterClass.ContractType = ContractOuterClass.ContractType.EMPLOYEE,
        hasOperationsUser: Boolean = true,
        payrollFormRequirements: Map<CountryAndState, Set<String>> = emptyMap(),
        submittedLegalDocuments: Set<String> = emptySet()
    ): EmailTemplateData {
        return EmailTemplateData(
            onboardingCompany = createJpaOnboarding(),
            onboardingMember = null,
            submittedLegalDocument = submittedLegalDocuments,
            payrollFormRequirements = payrollFormRequirements,
            member = createMember(),
            operationsUser = if (hasOperationsUser) createOperationsUser() else null,
            company = createCompany(),
            contract = createContract(type = contractType, country = country),
            guideLink = "link",
            preregistrationRequiredCountry = false,
            multiFrequencySupportEnabled = multiFrequencySupportEnabled)
    }
}
