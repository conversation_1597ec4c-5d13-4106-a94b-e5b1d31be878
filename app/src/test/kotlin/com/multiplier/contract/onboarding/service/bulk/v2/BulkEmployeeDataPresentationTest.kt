import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.GenderSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.MaritalStatusSpec
import com.multiplier.contract.onboarding.service.bulk.v2.BulkEmployeeDataPresentation
import com.multiplier.contract.onboarding.types.Gender
import com.multiplier.member.schema.Member.MaritalStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

class BulkEmployeeDataPresentationTest {

    @Nested
    inner class FormatBoolean {
        private val booleanKey = "booleanKey"

        @Test
        fun `should format boolean correctly`() {
            val employeeDataChunk1 = EmployeeDataChunk(data = mapOf(booleanKey to "true"))
            val employeeDataChunk2 = EmployeeDataChunk(data = mapOf(booleanKey to "false"))
            val formattedData =
                BulkEmployeeDataPresentation.formatData(
                    listOf(employeeDataChunk1, employeeDataChunk2))
            assertThat(formattedData[0].data[booleanKey]).isEqualTo("YES")
            assertThat(formattedData[1].data[booleanKey]).isEqualTo("NO")
        }

        @Test
        fun `should unformart boolean correctly`() {
            val employeeData1 =
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 1),
                    data = mapOf(booleanKey to "YES"))
            val employeeData2 =
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 2),
                    data = mapOf(booleanKey to "NO"))
            val unformattedData =
                BulkEmployeeDataPresentation.unformatData(listOf(employeeData1, employeeData2))
            assertThat(unformattedData[0].data[booleanKey]).isEqualTo("true")
            assertThat(unformattedData[1].data[booleanKey]).isEqualTo("false")
        }

        @ParameterizedTest
        @ValueSource(strings = ["IS_INSTALLMENT"])
        fun `should not unformat boolean of certain keys`(key: String) {
            val employeeData1 =
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 1),
                    data = mapOf(key to "Yes"))
            val employeeData2 =
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 2),
                    data = mapOf(key to "No"))
            val unformattedData =
                BulkEmployeeDataPresentation.unformatData(listOf(employeeData1, employeeData2))
            assertThat(unformattedData[0].data[key]).isEqualTo("Yes")
            assertThat(unformattedData[1].data[key]).isEqualTo("No")
        }
    }

    @Nested
    inner class FormatGender {
        @Test
        fun `formatData should format gender correctly`() {
            val employeeDataChunk1 =
                EmployeeDataChunk(data = mapOf(GenderSpec.key to Gender.UNSPECIFIED.name))
            val employeeDataChunk2 =
                EmployeeDataChunk(data = mapOf(GenderSpec.key to Gender.MALE.name))
            val formattedData =
                BulkEmployeeDataPresentation.formatData(
                    listOf(employeeDataChunk1, employeeDataChunk2))
            assertThat(formattedData[0].data[GenderSpec.key]).isEqualTo("OTHER")
            assertThat(formattedData[1].data[GenderSpec.key]).isEqualTo("MALE")
        }

        @Test
        fun `unformatData should unformat gender correctly`() {
            val employeeData1 =
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 1),
                    data = mapOf(GenderSpec.key to "OTHER"))
            val employeeData2 =
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 2),
                    data = mapOf(GenderSpec.key to "MALE"))
            val unformattedData =
                BulkEmployeeDataPresentation.unformatData(listOf(employeeData1, employeeData2))
            assertThat(unformattedData[0].data[GenderSpec.key]).isEqualTo(Gender.UNSPECIFIED.name)
            assertThat(unformattedData[1].data[GenderSpec.key]).isEqualTo(Gender.MALE.name)
        }
    }

    @Nested
    inner class FormatMaritalStatus {

        @Test
        fun `unformatData should unformat marital status correctly`() {
            val employeeData1 =
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 1),
                    data = mapOf(MaritalStatusSpec.key to "NOT_SPECIFIED"))
            val employeeData2 =
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 2),
                    data = mapOf(MaritalStatusSpec.key to "SINGLE"))
            val unformattedData =
                BulkEmployeeDataPresentation.unformatData(listOf(employeeData1, employeeData2))
            assertThat(unformattedData[0].data[MaritalStatusSpec.key])
                .isEqualTo(MaritalStatus.UNSPECIFIED.name)
            assertThat(unformattedData[1].data[MaritalStatusSpec.key])
                .isEqualTo(MaritalStatus.SINGLE.name)
        }
    }
}
