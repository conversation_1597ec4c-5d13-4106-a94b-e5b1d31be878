package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.types.BulkOnboardingContext.HRIS_PROFILE_DATA
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.BulkUpsertEmergencyContactResponse
import com.multiplier.member.schema.BulkUpsertEmergencyContactResult
import com.multiplier.member.schema.BulkValidateRequest
import com.multiplier.member.schema.MemberEmergencyContact
import com.multiplier.member.schema.UpsertEmergencyContactInput
import com.multiplier.member.schema.UpsertEmergencyContactValidationResult
import com.multiplier.member.schema.ValidateUpsertEmergencyContactResponse
import com.multiplier.member.schema.ValidationInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberEmergencyDataServiceAdapterTest {

    @MockK private lateinit var bulkMemberServiceMock: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    @InjectMockKs
    private lateinit var bulkMemberEmergencyDataServiceAdapter:
        BulkMemberEmergencyDataServiceAdapter

    @Test
    fun `should return correct upsert input when validate emergency contact`() {
        val employees =
            listOf(
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 1),
                    data = mapOf("key" to "value")))

        val grpcRequest =
            BulkValidateRequest.newBuilder()
                .addAllInputs(
                    employees.map {
                        ValidationInput.newBuilder()
                            .setRequestId(it.identification.validationId)
                            .putAllProperties(it.data)
                            .build()
                    })
                .setContext(HRIS_PROFILE_DATA.name)
                .build()

        val response =
            ValidateUpsertEmergencyContactResponse.newBuilder()
                .addAllValidationResults(
                    listOf(
                        UpsertEmergencyContactValidationResult.newBuilder()
                            .setSuccess(true)
                            .addAllErrors(emptyList())
                            .setRequestId("1")
                            .setValidatedInput(
                                UpsertEmergencyContactInput.newBuilder()
                                    .setEmergencyContact(
                                        MemberEmergencyContact.newBuilder()
                                            .setMemberId(1)
                                            .setName("Johnny")
                                            .build())
                                    .build())
                            .build()))
                .build()
        every { bulkMemberServiceMock.validateUpsertEmergencyContact(grpcRequest) } returns response

        val actualResult =
            bulkMemberEmergencyDataServiceAdapter.validateUpsertEmergencyContactDataInputs(
                employeeData = employees,
                options = BulkOnboardingOptions.newBuilder().context(HRIS_PROFILE_DATA).build())

        val expect =
            response.validationResultsList.map {
                GrpcValidationResult(
                    success = it.success,
                    errors = it.errorsList,
                    validationId = it.requestId,
                    input = it.validatedInput,
                )
            }
        assertEquals(expect, actualResult)
    }

    @Test
    fun `should return empty error list when bulk upsert emergency contact data with empty input`() {
        val inputs = emptyList<CreationInput<UpsertEmergencyContactInput>>()

        assertEquals(
            emptyList<String>(),
            bulkMemberEmergencyDataServiceAdapter.upsertEmergencyContactData(inputs))

        verify(exactly = 0) { bulkMemberServiceMock.bulkUpsertEmergencyContact(any()) }
    }

    @Test
    fun `should return empty error list when bulk upsert emergency contact data`() {
        val inputs =
            listOf(
                CreationInput(
                    requestId = "1",
                    memberId = 2,
                    data =
                        UpsertEmergencyContactInput.newBuilder()
                            .setEmergencyContact(
                                MemberEmergencyContact.newBuilder().setName("John").build())
                            .build()))

        val grpcResponse =
            BulkUpsertEmergencyContactResponse.newBuilder()
                .addAllResults(
                    listOf(BulkUpsertEmergencyContactResult.newBuilder().setSuccess(true).build()))
                .build()
        every { bulkMemberServiceMock.bulkUpsertEmergencyContact(any()) } returns grpcResponse

        assertEquals(
            emptyList<String>(),
            bulkMemberEmergencyDataServiceAdapter.upsertEmergencyContactData(inputs).flatMap {
                it.errors
            })
        verify {
            bulkMemberServiceMock.bulkUpsertEmergencyContact(
                withArg {
                    val emergencyContactInput = it.inputsList.first()
                    assertAll(
                        { assertEquals(1, it.inputsList.size) },
                        { assertEquals("1", emergencyContactInput.requestId) },
                        { assertEquals(2, emergencyContactInput.emergencyContact.memberId) },
                        { assertEquals("John", emergencyContactInput.emergencyContact.name) })
                })
        }
    }

    @Test
    fun `should return error list when bulk upsert emergency contact data`() {
        val inputs =
            listOf(
                CreationInput(
                    requestId = "1",
                    memberId = 2,
                    data =
                        UpsertEmergencyContactInput.newBuilder()
                            .setEmergencyContact(
                                MemberEmergencyContact.newBuilder().setMemberId(1).build())
                            .build()))

        val errors = listOf("member id 1 failed to upsert")
        val grpcResponse =
            BulkUpsertEmergencyContactResponse.newBuilder()
                .addAllResults(
                    listOf(
                        BulkUpsertEmergencyContactResult.newBuilder()
                            .setSuccess(false)
                            .addAllErrors(errors)
                            .build()))
                .build()
        every { bulkMemberServiceMock.bulkUpsertEmergencyContact(any()) } returns grpcResponse

        assertEquals(
            errors,
            bulkMemberEmergencyDataServiceAdapter.upsertEmergencyContactData(inputs).flatMap {
                it.errors
            })
    }

    @Test
    fun `should return error list when bulk upsert emergency contact data throws exception`() {
        val inputs =
            listOf(
                CreationInput(
                    requestId = "1",
                    memberId = 2,
                    data =
                        UpsertEmergencyContactInput.newBuilder()
                            .setEmergencyContact(
                                MemberEmergencyContact.newBuilder().setMemberId(1).build())
                            .build()))

        every { bulkMemberServiceMock.bulkUpsertEmergencyContact(any()) } throws
            IllegalArgumentException()

        assertEquals(
            listOf(
                "Upsert emergency contact failed due to an internal error: unknown exception occurred"),
            bulkMemberEmergencyDataServiceAdapter.upsertEmergencyContactData(inputs).flatMap {
                it.errors
            })
    }
}
