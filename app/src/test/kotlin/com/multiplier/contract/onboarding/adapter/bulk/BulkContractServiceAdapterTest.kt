package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.BulkContract.CreateContractResult
import com.multiplier.contract.schema.contract.BulkContract.CreateContractsResponse
import com.multiplier.contract.schema.contract.BulkContractServiceGrpc
import com.multiplier.contract.schema.contract.ContractOuterClass.CreateContractInput
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import java.util.concurrent.Executor
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkContractServiceAdapterTest {
    @MockK private lateinit var bulkService: BulkContractServiceGrpc.BulkContractServiceBlockingStub

    @InjectMockKs private lateinit var underTest: BulkContractServiceAdapter

    @MockK private lateinit var executor: Executor

    @Test
    fun validateContractCreateInputs() {
        val inputs =
            listOf(
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 3),
                    data =
                        mapOf(
                            "employeeId" to "101",
                            "contractId" to "201",
                        )),
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 4),
                    data =
                        mapOf(
                            "employeeId" to "102",
                            "contractId" to "202",
                        )))

        every { bulkService.validateCreateContractInputs(any()) } answers
            {
                BulkContract.ValidateCreateContractInputsResponse.newBuilder()
                    .addAllResults(
                        listOf(
                            BulkContract.CreateContractValidationResult.newBuilder()
                                .setRequestId(inputs[0].identification.validationId)
                                .setSuccess(true)
                                .build(),
                            BulkContract.CreateContractValidationResult.newBuilder()
                                .setRequestId(inputs[1].identification.validationId)
                                .setSuccess(false)
                                .addAllErrors(listOf("error1", "error2"))
                                .build(),
                        ))
                    .build()
            }

        val result =
            underTest.validateContractCreateInputs(
                inputs,
                BulkOnboardingOptions.newBuilder()
                    .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                    .build())

        verify(exactly = 1) {
            bulkService.validateCreateContractInputs(
                withArg {
                    assertEquals(2, it.inputsList.size)
                    assertEquals("HRIS_PROFILE_DATA", it.context)

                    assertEquals(inputs[0].identification.validationId, it.inputsList[0].requestId)
                    assertEquals(
                        inputs[0].data["employeeId"], it.inputsList[0].propertiesMap["employeeId"])
                    assertEquals(
                        inputs[0].data["contractId"], it.inputsList[0].propertiesMap["contractId"])

                    assertEquals(inputs[1].identification.validationId, it.inputsList[1].requestId)
                    assertEquals(
                        inputs[1].data["employeeId"], it.inputsList[1].propertiesMap["employeeId"])
                    assertEquals(
                        inputs[1].data["contractId"], it.inputsList[1].propertiesMap["contractId"])
                })
        }

        assertEquals(
            listOf(
                GrpcValidationResult(
                    success = true,
                    errors = emptyList(),
                    validationId = "rn_3",
                    input = CreateContractInput.getDefaultInstance()),
                GrpcValidationResult(
                    success = false,
                    errors = listOf("error1", "error2"),
                    validationId = "rn_4",
                    input = CreateContractInput.getDefaultInstance())),
            result)
    }

    @Test
    fun createContracts() {
        val inputs =
            listOf(
                CreationInput(
                    requestId = "3",
                    memberId = 101L,
                    data = CreateContractInput.newBuilder().build()),
                CreationInput(
                    requestId = "4",
                    memberId = 102L,
                    data = CreateContractInput.newBuilder().build()),
                CreationInput(
                    requestId = "5",
                    memberId = 103L,
                    data = CreateContractInput.newBuilder().build()))

        every { bulkService.createContracts(any()) } answers
            {
                CreateContractsResponse.newBuilder()
                    .addAllResults(
                        listOf(
                            CreateContractResult.newBuilder()
                                .setSuccess(true)
                                .setRequestId("3")
                                .setContractId(201L)
                                .build(),
                            CreateContractResult.newBuilder()
                                .setSuccess(true)
                                .setRequestId("4")
                                .setContractId(202L)
                                .build(),
                            CreateContractResult.newBuilder()
                                .setSuccess(false)
                                .setRequestId("5")
                                .addAllErrors(listOf("error1", "error2"))
                                .build(),
                        ))
                    .build()
            }

        val result =
            underTest.createContracts(
                inputs,
                BulkOnboardingOptions(
                    CountryCode.IND,
                    ContractType.HR_MEMBER,
                    201L,
                    201L,
                    BulkOnboardingContext.HRIS_PROFILE_DATA))

        verify(exactly = 1) {
            bulkService.createContracts(
                withArg {
                    assertEquals(3, it.inputsList.size)
                    assertEquals("HRIS_PROFILE_DATA", it.context)

                    assertEquals("3", it.inputsList[0].requestId)
                    assertEquals(101L, it.inputsList[0].memberId)

                    assertEquals("4", it.inputsList[1].requestId)
                    assertEquals(102L, it.inputsList[1].memberId)

                    assertEquals("5", it.inputsList[2].requestId)
                    assertEquals(103L, it.inputsList[2].memberId)
                })
        }

        assertEquals(
            listOf(
                CreationResult(requestId = "3", success = true, upsertedIds = listOf(201L)),
                CreationResult(requestId = "4", success = true, upsertedIds = listOf(202L)),
                CreationResult.error("5", listOf("error1", "error2"))),
            result)
    }

    @Test
    fun activateContracts() {
        val contractIds = listOf(201L, 202L)

        every { bulkService.activateContracts(any()) } answers
            {
                BulkContract.ActivateContractsResponse.newBuilder()
                    .addAllResults(
                        listOf(
                            BulkContract.ActivateContractResult.newBuilder()
                                .setSuccess(true)
                                .setContractId(201L)
                                .build(),
                            BulkContract.ActivateContractResult.newBuilder()
                                .setSuccess(false)
                                .setContractId(202L)
                                .build()))
                    .build()
            }

        val result = underTest.activateContracts(contractIds)

        verify(exactly = 1) {
            bulkService.activateContracts(
                withArg {
                    assertEquals(2, it.contractIdsList.size)
                    assertEquals(201L, it.contractIdsList[0])
                    assertEquals(202L, it.contractIdsList[1])
                })
        }
    }

    @Test
    fun sendContractToContractor() {
        val contractIds = listOf(201L, 202L, 203L)

        every { bulkService.sendContractToContractor(any()) } answers
            {
                BulkContract.SendContractToContractorResponse.newBuilder()
                    .addAllResults(
                        listOf(
                            BulkContract.SendContractToContractorResult.newBuilder()
                                .setSuccess(true)
                                .setContractId(201L)
                                .build(),
                            BulkContract.SendContractToContractorResult.newBuilder()
                                .setSuccess(true)
                                .setContractId(202L)
                                .build(),
                            BulkContract.SendContractToContractorResult.newBuilder()
                                .setSuccess(false)
                                .setContractId(203L)
                                .setErrorMessage("Contract not found")
                                .build()))
                    .build()
            }

        val result = underTest.sendContractToContractor(contractIds)

        verify(exactly = 1) {
            bulkService.sendContractToContractor(
                withArg {
                    assertEquals(3, it.contractIdsList.size)
                    assertEquals(201L, it.contractIdsList[0])
                    assertEquals(202L, it.contractIdsList[1])
                    assertEquals(203L, it.contractIdsList[2])
                })
        }

        assertEquals(1, result.size)
        assertTrue(result[0].contains("203"))
        assertTrue(result[0].contains("Contract not found"))
    }

    @Test
    fun sendContractToContractor_emptyList() {
        val result = underTest.sendContractToContractor(emptyList())

        assertEquals(emptyList<String>(), result)
        verify(exactly = 0) { bulkService.sendContractToContractor(any()) }
    }

    @Test
    fun sendContractToContractor_exception() {
        val contractIds = listOf(201L, 202L)

        every { bulkService.sendContractToContractor(any()) } throws
            RuntimeException("Service unavailable")

        val result = underTest.sendContractToContractor(contractIds)

        assertEquals(1, result.size)
        assertTrue(result[0].contains("internal error"))
        assertTrue(result[0].contains("Service unavailable"))
    }

    @Test
    fun `sendContractToContractor should return empty list when contractIds is empty`() {
        val result = underTest.sendContractToContractor(emptyList())

        assertTrue(result.isEmpty())
        verify(exactly = 0) { bulkService.sendContractToContractor(any()) }
    }

    @Test
    fun `sendContractToContractor should process contracts successfully`() {
        val contractIds = listOf(1L, 2L)

        val response =
            BulkContract.SendContractToContractorResponse.newBuilder()
                .addAllResults(
                    listOf(
                        BulkContract.SendContractToContractorResult.newBuilder()
                            .setContractId(1)
                            .setSuccess(true)
                            .build(),
                        BulkContract.SendContractToContractorResult.newBuilder()
                            .setContractId(2)
                            .setSuccess(false)
                            .setErrorMessage("Error sending contract")
                            .build()))
                .build()

        every { bulkService.sendContractToContractor(any()) } returns response

        val result = underTest.sendContractToContractor(contractIds)

        assertEquals(1, result.size)
        assertTrue(result[0].contains("Failed to send contract"))
        verify(exactly = 1) { bulkService.sendContractToContractor(any()) }
    }

    @Test
    fun `sendContractToContractor should handle exceptions`() {
        val contractIds = listOf(1L, 2L)

        every { bulkService.sendContractToContractor(any()) } throws
            RuntimeException("Service unavailable")

        val result = underTest.sendContractToContractor(contractIds)

        assertEquals(1, result.size)
        assertTrue(result[0].contains("internal error"))
        assertTrue(result[0].contains("Service unavailable"))
    }
}
