package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.repository.JpaOnboardingRepository
import com.multiplier.contract.onboarding.repository.model.JpaOnboarding
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.CountryWorkStatus
import io.mockk.*
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class EmailTemplateDataServiceFeatureFlagTest {

    private val contractServiceAdapter = mockk<ContractServiceAdapter>()
    private val memberServiceAdapter = mockk<MemberServiceAdapter>()
    private val countryServiceAdapter = mockk<CountryServiceAdapter>()
    private val companyServiceAdapter = mockk<CompanyServiceAdapter>()
    private val operationsUserServiceAdapter = mockk<OperationsUserServiceAdapter>()
    private val jpaOnboardingRepository = mockk<JpaOnboardingRepository>()
    private val featureFlagService = mockk<FeatureFlagService>()

    private val underTest =
        EmailTemplateDataService(
            contractServiceAdapter,
            memberServiceAdapter,
            countryServiceAdapter,
            companyServiceAdapter,
            operationsUserServiceAdapter,
            jpaOnboardingRepository,
            featureFlagService,
            "test-guide-link")

    private val contractId = 123L
    private val companyId = 101L
    private val memberId = 1L

    @BeforeEach
    fun setup() {
        // Mock basic dependencies
        every { contractServiceAdapter.getContractById(contractId) } returns
            createContract(CountryCode.IND.name)
        every { memberServiceAdapter.getMember(memberId) } returns mockk(relaxed = true)
        every { countryServiceAdapter.getCountryNameByCode(any()) } returns "India"
        every { companyServiceAdapter.getCompany(companyId) } returns
            mockk(relaxed = true) { every { isTest } returns false }
        every { operationsUserServiceAdapter.getOperationsUsersForContracts(any()) } returns
            mapOf(contractId to mockk(relaxed = true))
        every { jpaOnboardingRepository.findByContractIdAndExperience(contractId, any()) } returns
            Optional.of(
                mockk<JpaOnboarding>(relaxed = true) {
                    every { contractId } returns
                        <EMAIL>
                    every { experience } returns "company"
                })
    }

    @Test
    fun `should set preregistrationRequiredCountry to true when feature flag is enabled for country`() {
        // Given
        val country = CountryCode.IND.name
        every { contractServiceAdapter.getContractById(contractId) } returns createContract(country)
        every {
            featureFlagService.isFeatureOn(
                FeatureFlags.PREREGISTRATION_ENABLED_COUNTRIES_REMINDERS,
                mapOf(FeatureFlags.Params.ONBOARDING_COUNTRY to country))
        } returns true

        // When
        val result = underTest.fetchEmailTemplateData(contractId)

        // Then
        assertThat(result.preregistrationRequiredCountry).isTrue()
        verify {
            featureFlagService.isFeatureOn(
                FeatureFlags.PREREGISTRATION_ENABLED_COUNTRIES_REMINDERS,
                mapOf(FeatureFlags.Params.ONBOARDING_COUNTRY to country))
        }
    }

    @Test
    fun `should set preregistrationRequiredCountry to false when feature flag is disabled for country`() {
        // Given
        val country = CountryCode.USA.name
        every { contractServiceAdapter.getContractById(contractId) } returns createContract(country)
        every {
            featureFlagService.isFeatureOn(
                FeatureFlags.PREREGISTRATION_ENABLED_COUNTRIES_REMINDERS,
                mapOf(FeatureFlags.Params.ONBOARDING_COUNTRY to country))
        } returns false

        // When
        val result = underTest.fetchEmailTemplateData(contractId)

        // Then
        assertThat(result.preregistrationRequiredCountry).isFalse()
        verify {
            featureFlagService.isFeatureOn(
                FeatureFlags.PREREGISTRATION_ENABLED_COUNTRIES_REMINDERS,
                mapOf(FeatureFlags.Params.ONBOARDING_COUNTRY to country))
        }
    }

    @Test
    fun `should pass correct country parameter to feature flag service`() {
        // Given
        val country = CountryCode.SGP.name
        every { contractServiceAdapter.getContractById(contractId) } returns createContract(country)
        every {
            featureFlagService.isFeatureOn(
                FeatureFlags.PREREGISTRATION_ENABLED_COUNTRIES_REMINDERS,
                mapOf(FeatureFlags.Params.ONBOARDING_COUNTRY to country))
        } returns false

        // When
        underTest.fetchEmailTemplateData(contractId)

        // Then
        verify {
            featureFlagService.isFeatureOn(
                FeatureFlags.PREREGISTRATION_ENABLED_COUNTRIES_REMINDERS,
                mapOf(FeatureFlags.Params.ONBOARDING_COUNTRY to country))
        }
    }

    @Test
    fun `should work with different countries`() {
        // Test multiple countries to ensure the feature flag integration works for all
        val testCases =
            listOf(
                CountryCode.IND.name to true,
                CountryCode.USA.name to false,
                CountryCode.SGP.name to true,
                CountryCode.GBR.name to false)

        testCases.forEach { (country, expectedFlag) ->
            // Given
            every { contractServiceAdapter.getContractById(contractId) } returns
                createContract(country)
            every {
                featureFlagService.isFeatureOn(
                    FeatureFlags.PREREGISTRATION_ENABLED_COUNTRIES_REMINDERS,
                    mapOf(FeatureFlags.Params.ONBOARDING_COUNTRY to country))
            } returns expectedFlag

            // When
            val result = underTest.fetchEmailTemplateData(contractId)

            // Then
            assertThat(result.preregistrationRequiredCountry).isEqualTo(expectedFlag)
        }
    }

    private fun createContract(country: String): Contract {
        return Contract.newBuilder()
            .setId(contractId)
            .setCompanyId(companyId)
            .setMemberId(memberId)
            .setCountry(country)
            .setWorkStatus(CountryWorkStatus.RESIDENT)
            .setType(ContractOuterClass.ContractType.EMPLOYEE)
            .build()
    }
}
