package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.domain.model.ComplianceParam
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.ComplianceParams
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractComplianceParamsMessage
import com.multiplier.contract.schema.contract.ContractOuterClass.ContractMemberEmailsByContractIdsResponse
import com.multiplier.contract.schema.contract.ContractServiceGrpc
import com.multiplier.contract.schema.payable.ContractPayableServiceGrpc.ContractPayableServiceBlockingStub
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class ContractServiceAdapterImplTest {

    @MockK
    private lateinit var contractServiceStubMock: ContractServiceGrpc.ContractServiceBlockingStub
    @MockK private lateinit var contractPayableServiceStubMock: ContractPayableServiceBlockingStub

    @InjectMockKs private lateinit var contractServiceAdapter: ContractServiceAdapterImpl

    @Test
    fun getComplianceParamsByContractIds() {
        val grpcResponse =
            ContractComplianceParamsMessage.newBuilder()
                .putAllComplianceParams(
                    mapOf(
                        1L to
                            ComplianceParams.newBuilder()
                                .addAllComplianceParams(
                                    listOf(
                                        ContractOuterClass.ComplianceParam.newBuilder()
                                            .setKey("key1")
                                            .setLabel("label1")
                                            .setValue(1)
                                            .setUnit("unit1")
                                            .build(),
                                        ContractOuterClass.ComplianceParam.newBuilder()
                                            .setKey("key2")
                                            .setLabel("label2")
                                            .setValue(2)
                                            .setUnit("unit2")
                                            .build(),
                                    ))
                                .build()))
                .build()

        every { contractServiceStubMock.getContractsComplianceParams(any()) } returns grpcResponse

        val expected =
            mapOf(
                1L to
                    listOf(
                        ComplianceParam(key = "key1", label = "label1", value = 1, unit = "unit1"),
                        ComplianceParam(key = "key2", label = "label2", value = 2, unit = "unit2"),
                    ),
            )

        assertEquals(expected, contractServiceAdapter.getComplianceParamsByContractIds(setOf(1L)))
    }

    @Test
    fun getMemberEmailsByContractIds() {
        val grpcResponse =
            ContractMemberEmailsByContractIdsResponse.newBuilder()
                .putAllMemberEmailByContractId(mapOf(1L to "email"))
                .build()

        every { contractServiceStubMock.getContractMembersEmailByContractIds(any()) } returns
            grpcResponse

        val expected =
            mapOf(
                1L to "email",
            )

        assertEquals(
            expected, contractServiceAdapter.getContractMembersEmailByContractIds(setOf(1L)))
    }
}
