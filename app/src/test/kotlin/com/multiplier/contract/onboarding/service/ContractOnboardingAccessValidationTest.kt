package com.multiplier.contract.onboarding.service

import com.multiplier.common.transport.auth.MPLAuthorization
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import io.mockk.every
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.security.access.AccessDeniedException
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class ContractOnboardingAccessValidationTest {

    @MockK private lateinit var currentUser: CurrentUser

    @MockK private lateinit var contractService: ContractServiceAdapter

    @MockK private lateinit var mplAuthorization: MPLAuthorization

    private lateinit var accessValidation: ContractOnboardingAccessValidation

    @BeforeEach
    fun setUp() {
        accessValidation =
            ContractOnboardingAccessValidation(currentUser, contractService, mplAuthorization)
    }

    @Test
    fun `should throw AccessDeniedException when user experience is not allowed`() {
        every { (currentUser.context?.experience) } returns "INVALID_EXPERIENCE"

        assertThrows(AccessDeniedException::class.java) {
            accessValidation.validateByContractId(123)
        }
    }

    @Test
    fun `should throw AccessDeniedException when user experience is COMPANY and companyId does not match`() {
        every { currentUser.context?.experience } returns "company"
        every { contractService.getContractById(123) } returns
            Contract.newBuilder().setCompanyId(456).build()

        every { mplAuthorization.isCompanyUserForCompanyId(456) } returns false

        assertThrows(AccessDeniedException::class.java) {
            accessValidation.validateByContractId(123)
        }
    }

    @Test
    fun `should throw AccessDeniedException when user experience is MEMBER and memberId does not match`() {
        every { (currentUser.context?.experience) } returns "member"
        every { contractService.getContractById(123) } returns
            Contract.newBuilder().setMemberId(789).build()

        every { mplAuthorization.isMyMemberId(789) } returns false

        assertThrows(AccessDeniedException::class.java) {
            accessValidation.validateByContractId(123)
        }
    }

    @Test
    fun `should not throw any exception when user experience is COMPANY and companyId matches`() {
        every { currentUser.context?.experience } returns "company"
        every { contractService.getContractById(123) } returns
            Contract.newBuilder().setCompanyId(456).build()

        every { mplAuthorization.isCompanyUserForCompanyId(456) } returns true

        // No exception should be thrown
        accessValidation.validateByContractId(123)
    }

    @Test
    fun `should not throw any exception when user experience is MEMBER and memberId matches`() {
        every { currentUser.context?.experience } returns "member"
        every { contractService.getContractById(123) } returns
            Contract.newBuilder().setMemberId(789).build()

        every { mplAuthorization.isMyMemberId(789) } returns true

        // No exception should be thrown
        accessValidation.validateByContractId(123)
    }
}
