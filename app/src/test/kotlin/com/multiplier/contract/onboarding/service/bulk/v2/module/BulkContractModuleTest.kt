package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.adapter.bulk.BulkContractServiceAdapter
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.country.CountryState
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EndOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.JobDescriptionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.PositionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.RowIdentifierSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StartOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StateOfEmploymentSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.WorkStatusSpec
import com.multiplier.contract.onboarding.types.*
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkContractModuleTest {
    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter
    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockK private lateinit var bulkContractServiceAdapter: BulkContractServiceAdapter
    @MockK private lateinit var countryCache: CountryCache
    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkContractModule

    @Nested
    inner class GetDataSpecs {

        @Test
        fun `should return specific fields for freelancer`() {

            every { countryCache.getCountryName(any()) } returns "India"

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        null, ContractType.FREELANCER, 1, null, BulkOnboardingContext.FREELANCER),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "country",
                        type = DataSpecType.SELECT,
                        description = "Contractor's tax residency",
                        allowedValues = listOf("India")),
                    DataSpec(
                        key = "employeeId",
                        type = DataSpecType.TEXT,
                        mandatory = false,
                        label = "Contractor Id",
                        description = "Unique identifier for a contractor"),
                    DataSpec(
                        key = "position",
                        type = DataSpecType.TEXT,
                        label = "Designation",
                        description = "Job title of the contractor"),
                    DataSpec(
                        key = "startOn",
                        type = DataSpecType.DATE,
                        label = "Contract Start Date",
                        description = "Date of joining of the contractor in YYYY-MM-DD format"),
                    DataSpec(
                        key = "endOn",
                        type = DataSpecType.DATE,
                        label = "Contract End Date",
                        mandatory = false,
                        description = "Contract end date for the contractor in YYYY-MM-DD format"),
                    DataSpec(
                        key = "scope",
                        type = DataSpecType.TEXT,
                        label = "Job Description",
                        description = "Job Description of the Contractor",
                        mandatory = false))
        }

        @Test
        fun `should return specific fields for aor by filtering specific countries`() {

            every { countryCache.getCountryName(any()) } returns "India"

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "country",
                        type = DataSpecType.SELECT,
                        description = "Contractor's tax residency",
                        allowedValues = listOf("India"),
                    ),
                    DataSpec(
                        key = "employeeId",
                        label = "AOR Contractor Id",
                        type = DataSpecType.TEXT,
                        mandatory = false,
                        description = "Unique identifier for a contractor"),
                    DataSpec(
                        key = "position",
                        type = DataSpecType.TEXT,
                        label = "Designation",
                        description = "Job title of the contractor"),
                    DataSpec(
                        key = "startOn",
                        label = "Contract Start Date",
                        type = DataSpecType.DATE,
                        description = "Date of joining of the contractor in YYYY-MM-DD format"),
                    DataSpec(
                        key = "endOn",
                        label = "Contract End Date",
                        type = DataSpecType.DATE,
                        description = "Contract end date for the contractor in YYYY-MM-DD format"),
                    DataSpec(
                        key = "scope",
                        type = DataSpecType.TEXT,
                        label = "Job Description",
                        description = "Job Description of the Contractor"))
            verify(exactly = 0) { countryCache.getCountryName(CountryCode.USA) }
            verify(exactly = 0) { countryCache.getCountryName(CountryCode.SGP) }
        }

        @Test
        fun `should throw error for unsupported context`() {
            val exception =
                Assertions.assertThrows(MplBusinessException::class.java) {
                    underTest.getDataSpecs(
                        BulkOnboardingOptions(
                            null,
                            ContractType.HR_MEMBER,
                            1,
                            null,
                            BulkOnboardingContext.HRIS_PROFILE_DATA),
                        null)
                }

            Assertions.assertEquals(
                "Bulk Onboarding context value HRIS_PROFILE_DATA not supported yet",
                exception.message)
        }

        @Test
        fun `should return specific fields for eor`() {
            every { countryServiceAdapter.getCountryStates(any()) } returns
                mapOf(CountryCode.IND to listOf(CountryState(CountryCode.IND, "KA", "Karnataka")))
            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        CountryCode.IND, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR),
                    null)

            assertThat(dataSpecs)
                .contains(
                    RowIdentifierSpec,
                    ContractIdSpec,
                    WorkStatusSpec,
                    JobDescriptionSpec.copy(
                        key = "scope",
                        type = DataSpecType.TEXT,
                        label = "Job Description",
                        mandatory = false),
                    PositionSpec.copy(label = "Job Title"),
                    StartOnSpec,
                    EndOnSpec,
                    StateOfEmploymentSpec.copy(
                        allowedValues = listOf("KA"),
                    ))
        }
    }
}
