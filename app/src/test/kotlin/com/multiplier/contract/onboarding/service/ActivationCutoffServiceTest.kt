package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.Clock
import com.multiplier.contract.onboarding.domain.model.PayrollCycleDTO
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import java.time.LocalDate
import java.time.YearMonth
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class ActivationCutoffServiceTest {
    @MockK private lateinit var payrollCyclesService: PayrollCyclesService
    @MockK private lateinit var clock: Clock
    @InjectMockKs private lateinit var service: ActivationCutoffService

    private val contractIdToPayrollCycleDTOMap =
        mapOf(
            553687L to
                PayrollCycleDTO(
                    payFrequency = PayrollCycleDTO.PayFrequency.MONTHLY,
                    payrollMonth = YearMonth.of(2025, 4),
                    cutoffDate = LocalDate.of(2025, 4, 15),
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30)),
            553579L to
                PayrollCycleDTO(
                    payFrequency = PayrollCycleDTO.PayFrequency.MONTHLY,
                    payrollMonth = YearMonth.of(2025, 4),
                    cutoffDate = LocalDate.of(2025, 4, 15),
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30)))

    private val onboardingIdToPayrollCycleDTOMap =
        mapOf(
            12345L to
                PayrollCycleDTO(
                    payFrequency = PayrollCycleDTO.PayFrequency.MONTHLY,
                    payrollMonth = YearMonth.of(2025, 4),
                    cutoffDate = LocalDate.of(2025, 4, 15),
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30)),
            67890L to
                PayrollCycleDTO(
                    payFrequency = PayrollCycleDTO.PayFrequency.MONTHLY,
                    payrollMonth = YearMonth.of(2025, 4),
                    cutoffDate = LocalDate.of(2025, 4, 15),
                    startDate = LocalDate.of(2025, 4, 1),
                    endDate = LocalDate.of(2025, 4, 30)))

    @Test
    fun `should return cutoff dates for given contract IDs`() {
        // Arrange
        val contractIds = listOf(553687L, 553579L)
        val checkingDate = LocalDate.of(2025, 4, 22)

        every { clock.today() } returns checkingDate
        every {
            payrollCyclesService.getPayrollCyclesByContractId(contractIds.toSet(), checkingDate)
        } returns contractIdToPayrollCycleDTOMap

        // Act
        val result = service.getActivationCutoffDateByContractId(contractIds, checkingDate)

        // Assert
        val expected =
            mapOf(
                553687L to LocalDate.of(2025, 4, 15),
                553579L to LocalDate.of(2025, 4, 15),
            )
        assertEquals(expected, result)
    }

    @Test
    fun `should return partial map when some contract IDs have no payroll cycle`() {
        // Arrange
        val contractIds = listOf(553687L, 553579L, 553666L)
        val checkingDate = LocalDate.of(2025, 4, 22)

        every { clock.today() } returns checkingDate
        every {
            payrollCyclesService.getPayrollCyclesByContractId(contractIds.toSet(), checkingDate)
        } returns contractIdToPayrollCycleDTOMap

        // Act
        val result = service.getActivationCutoffDateByContractId(contractIds, checkingDate)

        // Assert
        val expected =
            mapOf(
                553687L to LocalDate.of(2025, 4, 15),
                553579L to LocalDate.of(2025, 4, 15),
            )
        assertEquals(expected, result)
    }

    @Test
    fun `should return cutoff dates for given onboarding IDs`() {
        // Arrange
        val onboardingIds = listOf(12345L, 67890L)
        val checkingDate = LocalDate.of(2025, 4, 22)

        every {
            payrollCyclesService.getPayrollCyclesByOnboardingId(onboardingIds.toSet(), checkingDate)
        } returns onboardingIdToPayrollCycleDTOMap

        // Act
        val result = service.getActivationCutoffDateByOnboardingId(onboardingIds, checkingDate)

        // Assert
        val expected =
            mapOf(12345L to LocalDate.of(2025, 4, 15), 67890L to LocalDate.of(2025, 4, 15))
        assertEquals(expected, result)
    }

    @Test
    fun `should return partial map when some onboarding IDs have no payroll cycle`() {
        // Arrange
        val onboardingIds = listOf(12345L, 67890L, 84532L)
        val checkingDate = LocalDate.of(2025, 4, 22)

        every {
            payrollCyclesService.getPayrollCyclesByOnboardingId(onboardingIds.toSet(), checkingDate)
        } returns onboardingIdToPayrollCycleDTOMap

        // Act
        val result = service.getActivationCutoffDateByOnboardingId(onboardingIds, checkingDate)

        // Assert
        val expected =
            mapOf(12345L to LocalDate.of(2025, 4, 15), 67890L to LocalDate.of(2025, 4, 15))
        assertEquals(expected, result)
    }
}
