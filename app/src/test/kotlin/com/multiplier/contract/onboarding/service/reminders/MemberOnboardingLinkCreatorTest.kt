package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.DocumentServiceAdapter
import com.multiplier.contract.onboarding.adapter.DocumentShare
import com.multiplier.contract.onboarding.adapter.User
import com.multiplier.contract.onboarding.adapter.UserServiceAdapter
import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.service.urlEncode
import com.multiplier.contract.onboarding.types.CountryCode
import io.mockk.every
import io.mockk.impl.annotations.MockK
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class MemberOnboardingLinkCreatorTest {

    @MockK private lateinit var documentService: DocumentServiceAdapter

    @MockK private lateinit var userServiceAdapter: UserServiceAdapter

    private lateinit var linkCreator: MemberOnboardingLinkCreator

    private fun someMember() =
        Member(
            id = 1,
            email = "<EMAIL>",
            firstName = "freddy",
            lastName = "fred",
            fullName = "freddy fred",
            userId = "1",
            persona = Persona("MEMBER"))

    private fun someContract() =
        Contract(
            id = 1,
            memberId = 2,
            companyId = 10,
            agreementId = 100,
        )

    private fun someCompany() =
        Company(
            id = 1,
            logoId = 2,
            displayName = "empty",
            countryFullName = "empty",
            countryCode = CountryCode.CXR,
            msaSigned = false,
            isTest = false,
        )

    private val baseUrl = "https://app.usemultiplier.com"

    @BeforeEach
    fun beforeEach() {
        linkCreator =
            MemberOnboardingLinkCreator(
                documentServiceAdapter = documentService,
                userServiceAdapter = userServiceAdapter,
                baseUrl = baseUrl)
    }

    @Test
    fun `should create document signing link when contract needs to be signed`() {
        val tasks =
            listOf(OnboardingTask(name = OnboardingTaskName.SIGN_CONTRACT, completed = false))
        val sessionId = "sessionId"
        mockGetSessionId(sessionId)

        val company =
            someCompany()
                .copy(
                    displayName = "Multiplier",
                    countryFullName = "Switzerland",
                )

        val logoLink = "https://logo.png"

        val link: String =
            linkCreator.createOnboardingLink(
                contract = someContract(),
                memberInvitedToSignContract = true,
                company = company,
                member = someMember(),
                companyLogoLink = logoLink,
                tasks = tasks)

        val queryParams =
            "name=${company.displayName.urlEncode()}&country=${company.countryFullName.urlEncode()}&logo=${logoLink.urlEncode()}"

        assertThat(link)
            .isEqualTo("https://app.usemultiplier.com/documents/session/$sessionId?$queryParams")
    }

    @Test
    fun `should create member dashboard link when the session id cannot be obtained`() {
        val tasks =
            listOf(OnboardingTask(name = OnboardingTaskName.SIGN_CONTRACT, completed = false))
        mockGetSessionId(null)

        val link: String =
            linkCreator.createOnboardingLink(
                contract = someContract(),
                memberInvitedToSignContract = true,
                company = someCompany(),
                member = someMember(),
                companyLogoLink = null,
                tasks = tasks)

        assertThat(link).isEqualTo("https://app.usemultiplier.com/member/dashboard")
    }

    @Test
    fun `should create platform registration link when member needs to sign up`() {
        val tasks =
            listOf(OnboardingTask(name = OnboardingTaskName.PLATFORM_ONBOARDING, completed = false))

        val companyName = "Multiplier"
        val activationKey = "UserActivationKey_0209"
        val memberName = "John Wayne"
        val memberEmail = "<EMAIL>"
        mockGetMemberActivationKey(activationKey)

        val link: String =
            linkCreator.createOnboardingLink(
                contract = someContract(),
                memberInvitedToSignContract = true,
                company = someCompany().copy(displayName = companyName),
                member = someMember().copy(email = memberEmail, fullName = memberName),
                companyLogoLink = null,
                tasks = tasks)

        assertThat(link)
            .isEqualTo(
                "$baseUrl/login/invite/member?hash=$activationKey&name=${memberName.urlEncode()}&company=${companyName.urlEncode()}&email=${memberEmail.urlEncode()}")
    }

    @Test
    fun `should create a shortened platform registration link when there is no activation key`() {
        val tasks =
            listOf(OnboardingTask(name = OnboardingTaskName.PLATFORM_ONBOARDING, completed = false))

        mockGetMemberActivationKey(null)

        val link: String =
            linkCreator.createOnboardingLink(
                contract = someContract(),
                memberInvitedToSignContract = true,
                company = someCompany(),
                member = someMember(),
                companyLogoLink = null,
                tasks = tasks)

        assertThat(link).isEqualTo("$baseUrl/member?invited=true")
    }

    @Test
    fun `should create the dashboard link in any other case`() {
        val link: String =
            linkCreator.createOnboardingLink(
                contract = someContract(),
                memberInvitedToSignContract = true,
                company = someCompany(),
                member = someMember(),
                companyLogoLink = null,
                tasks = emptyList())

        assertThat(link).isEqualTo("https://app.usemultiplier.com/member/dashboard")
    }

    private fun mockGetSessionId(sessionId: String?) {
        every { documentService.getDocumentShareOrNull(any(), any()) } returns
            if (sessionId == null) null else DocumentShare(sessionId = sessionId)
    }

    private fun mockGetMemberActivationKey(key: String?) {
        every { userServiceAdapter.getUserByEmail(any()) } returns User(activationKey = key)
    }
}
