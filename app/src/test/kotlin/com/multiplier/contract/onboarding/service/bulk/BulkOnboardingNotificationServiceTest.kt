package com.multiplier.contract.onboarding.service.bulk

import com.multiplier.contract.onboarding.service.bulk.creation.BulkCreationResult
import com.multiplier.contract.onboarding.service.bulk.mapper.ContractContext
import com.multiplier.contract.onboarding.service.bulk.mapper.MemberContext
import com.multiplier.contract.onboarding.service.bulk.validation.AggregatedEmployeeValidationResult
import com.multiplier.contract.onboarding.service.bulk.validation.BulkValidationReport
import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationError
import com.multiplier.contract.onboarding.service.bulk.validation.GenericValidationResult
import com.multiplier.contract.onboarding.service.notifications.NotificationService
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.NotificationType
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class BulkOnboardingNotificationServiceTest {

    private val notificationService = mockk<NotificationService>()

    private val underTest =
        BulkOnboardingNotificationService(
            notificationService = notificationService,
            systemNotificationEmail = "systemNotificationEmail",
            opsMemberOnboardingEmail = "memberOnboardingEmail",
            opsSalesEmail = "salesEmail",
            opsCustomerSuccessEmail = "customerSuccessEmail",
            bulkOnboardingSlackChannelEmail = "slackChannelEmail")

    @Test
    fun `should send email to ops when bulk onboarding is success`() {
        // given
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.GLOBAL_PAYROLL)
                .companyId(1L)
                .build()
        val onboardingJob =
            BulkOnboardingJob.complete(
                "test",
                LocalDateTime.now(),
                BulkValidationReport(
                    GenericValidationResult.EMPTY,
                    AggregatedEmployeeValidationResult(
                        totalEmployeeCount = 1,
                        invalidEmployeeCount = 0,
                        validEmployeeCount = 1,
                        errors = emptyList(),
                    )),
                creationResult =
                    BulkCreationResult(
                        memberContext = MemberContext(mapOf("testId" to 1)),
                        contractContext = ContractContext(mapOf("testId" to 2)),
                        errors = emptyList()))
        justRun { notificationService.send(any()) }

        // when
        underTest.sendOnboardingCompletedNotification(options, onboardingJob, "")

        // then
        verify {
            notificationService.send(
                withArg {
                    assertThat(it.to).isEqualTo("memberOnboardingEmail")
                    assertThat(it.type).isEqualTo(NotificationType.BulkOnboardingSuccessToOps)
                })
        }
    }

    @Test
    fun `should send email to slack when bulk onboarding is failed`() {
        // given
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.GLOBAL_PAYROLL)
                .companyId(1L)
                .build()
        val onboardingJob =
            BulkOnboardingJob.complete(
                "test",
                LocalDateTime.now(),
                BulkValidationReport(
                    GenericValidationResult.EMPTY,
                    AggregatedEmployeeValidationResult(
                        totalEmployeeCount = 1,
                        invalidEmployeeCount = 1,
                        validEmployeeCount = 0,
                        errors =
                            listOf(
                                EmployeeValidationError(
                                    error = "test",
                                    rowNumber = 1,
                                    columnName = "test",
                                    employeeName = "test")),
                    )))
        justRun { notificationService.send(any()) }

        // when
        underTest.sendOnboardingCompletedNotification(options, onboardingJob, "")

        // then
        verify {
            notificationService.send(
                withArg {
                    assertThat(it.to).isEqualTo("slackChannelEmail")
                    assertThat(it.type).isEqualTo(NotificationType.BulkOnboardingFailedToSlack)
                })
        }
    }
}
