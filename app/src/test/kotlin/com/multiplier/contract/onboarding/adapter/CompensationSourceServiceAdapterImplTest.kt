package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.compensationsource.*
import com.multiplier.contract.schema.contract.ContractOuterClass
import io.mockk.*
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class CompensationSourceServiceAdapterImplTest {

    @MockK lateinit var stub: CompensationSourceServiceGrpc.CompensationSourceServiceBlockingStub

    @InjectMockKs lateinit var adapter: CompensationSourceServiceAdapterImpl

    @Test
    fun `should return correct enablement map for countries`() {
        val countryCodes = listOf(CountryCode.USA, CountryCode.IND)
        val contractType = ContractType.EMPLOYEE
        val grpcContractType = ContractOuterClass.ContractType.EMPLOYEE

        val grpcResponse = compensationSourceByCountriesResponse {
            sourceByCountry +=
                listOf(
                    compensationSourceByCountry {
                        key = compensationSourceByCountryKey {
                            this.contractType = grpcContractType
                            this.country = com.multiplier.country.schema.Country.GrpcCountryCode.USA
                        }
                        value = CompensationSourceOuterClass.CompensationSource.COMPENSATION_SERVICE
                    },
                    compensationSourceByCountry {
                        key = compensationSourceByCountryKey {
                            this.contractType = grpcContractType
                            this.country = com.multiplier.country.schema.Country.GrpcCountryCode.IND
                        }
                        value = CompensationSourceOuterClass.CompensationSource.CONTRACT_SERVICE
                    })
        }

        every { stub.getCompensationSourceByCountries(any()) } returns grpcResponse

        val result = adapter.isNewCompensationSourceEnabledForCountries(contractType, countryCodes)

        assertEquals(mapOf(CountryCode.USA to true, CountryCode.IND to false), result)

        verify(exactly = 1) { stub.getCompensationSourceByCountries(any()) }
        confirmVerified(stub)
    }
}
