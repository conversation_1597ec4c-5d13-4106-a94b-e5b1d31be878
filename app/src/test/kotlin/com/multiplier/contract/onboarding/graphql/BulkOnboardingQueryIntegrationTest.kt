package com.multiplier.contract.onboarding.graphql

import com.google.type.Date
import com.multiplier.company.schema.grpc.CompanyOfferingOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.onboarding.ContractOnboardingApplication
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.CompensationSourceServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.DepartmentServiceAdapter
import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.PaymentServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkCompensationSchemaServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkOrgManagementDataServiceAdapter
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.model.Address
import com.multiplier.contract.onboarding.domain.model.AddressDetail
import com.multiplier.contract.onboarding.domain.model.CompanyManager
import com.multiplier.contract.onboarding.domain.model.ComplianceParam
import com.multiplier.contract.onboarding.domain.model.Department
import com.multiplier.contract.onboarding.domain.model.EducationDetail
import com.multiplier.contract.onboarding.domain.model.EmergencyContact
import com.multiplier.contract.onboarding.domain.model.EmployerDetail
import com.multiplier.contract.onboarding.domain.model.Member
import com.multiplier.contract.onboarding.domain.model.OrgManagementData
import com.multiplier.contract.onboarding.domain.model.Persona
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataHelper
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataHelper.Companion.defaultEmployeeDataMergeStrategy
import com.multiplier.contract.onboarding.domain.model.country.CompensationStandard
import com.multiplier.contract.onboarding.domain.model.country.CountryState
import com.multiplier.contract.onboarding.domain.model.member.ApplyTo
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataDefinition
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataType
import com.multiplier.contract.onboarding.service.bulk.excel.BulkUploadExcel
import com.multiplier.contract.onboarding.service.bulk.excel.parseXlsx
import com.multiplier.contract.onboarding.service.bulk.excel.visibleSheets
import com.multiplier.contract.onboarding.testing.GraphQLTestClient
import com.multiplier.contract.onboarding.testing.HttpConfig
import com.multiplier.contract.onboarding.testing.UserContext
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.utils.base64StringToByteArray
import com.multiplier.contract.onboarding.utils.projectTmpFolder
import com.multiplier.contract.onboarding.utils.writeFileFromBase64
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.payable.ContractPayable
import com.netflix.graphql.dgs.autoconfig.DgsAutoConfiguration
import com.ninjasquad.springmockk.MockkBean
import com.poiji.annotation.ExcelCell
import com.poiji.annotation.ExcelUnknownCells
import io.mockk.called
import io.mockk.every
import io.mockk.verify
import java.io.ByteArrayInputStream
import java.time.LocalDate
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.ActiveProfiles

/**
 * Testing scope boundaries GraphQL <|---|> External service & repo adapters
 *
 * Do not mock anything in between, use service / unit tests for this!
 */
@ActiveProfiles("context-test")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [ContractOnboardingApplication::class, DgsAutoConfiguration::class])
class BulkOnboardingQueryIntegrationTest(
    @LocalServerPort private val port: Int,
) {

    @MockkBean private lateinit var countryServiceAdapter: CountryServiceAdapter

    @MockkBean private lateinit var paymentServiceAdapter: PaymentServiceAdapter

    @MockkBean private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockkBean private lateinit var companyServiceAdapter: CompanyServiceAdapter

    @MockkBean private lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockkBean private lateinit var departmentServiceAdapter: DepartmentServiceAdapter

    @MockkBean
    private lateinit var bulkOrgManagementDataServiceAdapter: BulkOrgManagementDataServiceAdapter

    @MockkBean(relaxed = true)
    private lateinit var compensationSourceServiceAdapter: CompensationSourceServiceAdapter

    @MockkBean
    private lateinit var bulkCompensationSchemaServiceAdapter: BulkCompensationSchemaServiceAdapter

    @MockkBean private lateinit var countryCache: CountryCache

    private val client = GraphQLTestClient(port)

    private val passportNumberLegalDataRequirement =
        MemberLegalDataDefinition(
            key = "passportNo",
            type = MemberLegalDataType.TEXT,
            mandatory = false,
            fetchStage = FetchStage.CONTRACT_GENERATION,
            applyTo = ApplyTo.EXPAT_ONLY,
            domainType = DomainType.LEGAL_DATA)

    @BeforeEach
    fun beforeEach() {
        every { countryServiceAdapter.getMemberLegalDataDefinitions(any(), any()) } returns
            listOf(passportNumberLegalDataRequirement)

        every { countryServiceAdapter.getCountryStates(any()) } returns
            mapOf(CountryCode.USA to listOf(CountryState(CountryCode.USA, "CA")))

        every { countryServiceAdapter.getCompensationStandards(any()) } returns
            mapOf(
                CountryState(CountryCode.USA, "CA") to
                    CompensationStandard(
                        payrollFrequencies = listOf(PayFrequency.MONTHLY),
                        rateFrequencies = listOf(RateFrequency.MONTHLY)))

        every { companyServiceAdapter.getCompanyOfferings(any()) } returns
            mutableListOf(
                CompanyOfferingOuterClass.CompanyOffering.newBuilder()
                    .setOfferingCode(OfferingCode.GLOBAL_PAYROLL.name)
                    .build())

        every { companyServiceAdapter.getLegalEntity(any()) } returns
            CompanyOuterClass.LegalEntity.newBuilder()
                .setId(1)
                .setCompanyId(1)
                .addCapabilities(Capability.GROSS_TO_NET.name)
                .setAddress(CompanyOuterClass.Address.newBuilder().setCountry("USA").build())
                .build()

        every {
            contractServiceAdapter.getPaymentAccountRequirementsRequestForMember(any(), any())
        } returns ContractPayable.PaymentAccountRequirementsRequest.getDefaultInstance()

        every { paymentServiceAdapter.getPaymentAccountRequirementForMember(any()) } returns null

        every { departmentServiceAdapter.getDepartments(any()) } returns
            listOf(Department(323L, 1234L, "Department 1"), Department(324L, 1234L, "Department 2"))

        every { countryCache.getCountryCode(any()) } returns CountryCode.IND

        every { countryCache.getCountryName(any()) } returns "India"
    }

    @Test
    fun `should generate a bulk upload template`() {
        mockLegalEntities()
        mockEmployeeDataForGpDownload()
        val document = callDownloadTemplateQuery()

        val rows = base64StringToByteArray(document.blob).parseXlsx<BulkTemplateDataRow>()

        writeFileFromBase64("${projectTmpFolder()}/download.xlsx", document.blob)

        assertThat(rows.first().valueAtColumnZero).isEqualTo("Field")
        assertThat(rows.first().valueAtColumnOne).isEqualTo("Employee Id")
        assertThat(rows.first().valueAtColumnTwo).isEqualTo("Contract Id")
        verify { bulkCompensationSchemaServiceAdapter wasNot called }
    }

    @Test
    fun `should group data columns into sheets when download HRIS profile data template`() {
        mockLegalEntities()
        mockEmployeeDataForHrisProfileDownload()

        val document =
            callDownloadTemplateQuery(onboardingContext = BulkOnboardingContext.HRIS_PROFILE_DATA)
        val byteArray = base64StringToByteArray(document.blob)
        val inputStream = ByteArrayInputStream(byteArray)
        val workbook = XSSFWorkbook(inputStream)

        assertThat(workbook.visibleSheets().size).isEqualTo(10)
    }

    @Test
    fun `should generate HRIS profile data bulk upload template with employee data`() {
        mockLegalEntities()
        mockEmployeeDataForHrisProfileDownload()

        val document =
            callDownloadTemplateQuery(onboardingContext = BulkOnboardingContext.HRIS_PROFILE_DATA)

        val employeeData =
            EmployeeDataHelper.mergeEmployeeData(
                BulkUploadExcel.parse(base64StringToByteArray(document.blob)).employeeData,
                defaultEmployeeDataMergeStrategy)

        writeFileFromBase64("${projectTmpFolder()}/download_hris.xlsx", document.blob)

        val expectedEmployeeData1 =
            mapOf(
                "contractId" to "123",
                "employeeId" to "EMP123",
                "legalEntityId" to "888 - Entity 1",
                "startOn" to "2021-01-01",
                "firstName" to "John",
                "lastName" to "Doe",
                "email" to "<EMAIL>",
                "gender" to "MALE",
                "directManagerEmail" to "<EMAIL>",
                "isManager" to "YES",
                "department" to "323 - Department 1",
                "emergencyContactName" to "Anne",
                "emergencyContactRelationship" to "Wife",
                "emergencyContactPhoneNumber" to "2222-222",
                "lastSchoolName" to "Harvard University",
                "lastSchoolDegree" to "Computer Science",
                "lastSchoolYearOfPassing" to "2017",
                "lastSchoolGpa" to "4.0",
                "lastEmployerName" to "Google",
                "lastEmployerPosition" to "Senior Software Engineer",
                "lastEmployerStartDate" to "2020-01-01",
                "lastEmployerEndDate" to "2023-12-31",
                "dateOfBirth" to "1990-01-01",
                "nationality" to "VNM",
                "maritalStatus" to "MARRIED",
                "phoneNumber" to "+84 374 123 456",
                "religion" to "NONE",
                "nationalId" to "079*********",
                "passportNumber" to "*********",
                "noticeAfterProbation.value" to "4",
                "noticeAfterProbation.unit" to "WEEKS",
                "currentAddressLine1" to "196",
                "currentAddressLine2" to "Tam Thuan",
                "currentAddressCity" to "Da Nang",
                "currentAddressState" to "Son Tra",
                "currentAddressCountry" to "VNM",
                "currentAddressPostalCode" to "550002",
                "permanentAddressLine1" to "266",
                "permanentAddressLine2" to "An Hai Bac",
                "permanentAddressCity" to "Little Elm",
                "permanentAddressState" to "Texas",
                "permanentAddressCountry" to "USA",
                "permanentAddressPostalCode" to "550004",
            )
        val expectedEmployeeData2 =
            mapOf(
                "contractId" to "124",
                "employeeId" to "EMP124",
                "legalEntityId" to "999 - Entity 2",
                "startOn" to "2022-06-01",
                "firstName" to "Mary",
                "lastName" to "Jane",
                "email" to "<EMAIL>",
                "gender" to "OTHER",
                "directManagerEmail" to "",
                "isManager" to "NO",
                "department" to "",
                "emergencyContactName" to "Barry",
                "emergencyContactRelationship" to "Husband",
                "emergencyContactPhoneNumber" to "3333-333",
                "lastSchoolName" to "MIT University",
                "lastSchoolDegree" to "Information Technology",
                "lastSchoolYearOfPassing" to "2016",
                "lastSchoolGpa" to "3.9",
                "lastEmployerName" to "Facebook",
                "lastEmployerPosition" to "Software Engineer",
                "lastEmployerStartDate" to "2020-05-15",
                "lastEmployerEndDate" to "2023-06-15",
                "currentAddressLine1" to "266",
                "currentAddressLine2" to "An Hai Bac",
                "currentAddressCity" to "Da Nang",
                "currentAddressState" to "Son Tra",
                "currentAddressCountry" to "VNM",
                "currentAddressPostalCode" to "550006",
            )

        assertThat(employeeData[0].data).containsAllEntriesOf(expectedEmployeeData1)
        assertThat(employeeData[1].data).containsAllEntriesOf(expectedEmployeeData2)
    }

    @Test
    fun `should generate GP bulk upload template with new fields from compensation schema`() {
        mockLegalEntities()
        mockEmployeeDataForGpDownload()
        every {
            compensationSourceServiceAdapter.isNewCompensationSourceEnabledForEntityId(ENTITY_ID)
        } returns true
        every {
            bulkCompensationSchemaServiceAdapter.getDataSpecs(options = any(), moduleParams = any())
        } returns
            listOf(
                DataSpec(
                    key = "employeeId",
                    type = DataSpecType.NUMBER,
                ),
                DataSpec(
                    key = "componentName",
                    type = DataSpecType.TEXT,
                ),
                DataSpec(
                    key = "currency",
                    type = DataSpecType.TEXT,
                ))
        val document = callDownloadTemplateQuery()

        val rows =
            base64StringToByteArray(document.blob).parseXlsx<BulkTemplateDataRow>(sheetIndex = 2)

        writeFileFromBase64("${projectTmpFolder()}/download.xlsx", document.blob)

        assertThat(rows.first().valueAtColumnZero).isEqualTo("Field")
        assertThat(rows.first().valueAtColumnOne).isEqualTo("Employee Id")
        assertThat(rows.first().valueAtColumnTwo).isEqualTo("Component Name")
        assertThat(rows.first().valueAtColumnThree).isEqualTo("Currency")
    }

    private fun mockLegalEntities() {
        every { companyServiceAdapter.getCompanyLegalEntities(any()) } returns
            listOf(
                CompanyOuterClass.LegalEntity.newBuilder()
                    .setId(888)
                    .setLegalName("Entity 1")
                    .build(),
                CompanyOuterClass.LegalEntity.newBuilder()
                    .setId(999)
                    .setLegalName("Entity 2")
                    .build())
    }

    private fun mockEmployeeDataForHrisProfileDownload() {
        every { contractServiceAdapter.getContractsBy(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(123)
                    .setEmployeeId("EMP123")
                    .setLegalEntityId(888)
                    .setStartOn(Date.newBuilder().setYear(2021).setMonth(1).setDay(1).build())
                    .setMemberId(222)
                    .build(),
                ContractOuterClass.Contract.newBuilder()
                    .setId(124)
                    .setEmployeeId("EMP124")
                    .setLegalEntityId(999)
                    .setStartOn(Date.newBuilder().setYear(2022).setMonth(6).setDay(1).build())
                    .setMemberId(333)
                    .build())
        every {
            memberServiceAdapter.getMembersWithAddressesAndBankAccountsAndLegalData(any())
        } returns
            listOf(
                Member(
                    id = 222,
                    firstName = "John",
                    lastName = "Doe",
                    email = "<EMAIL>",
                    fullName = "John Doe",
                    userId = "123",
                    persona = Persona("Member"),
                    gender = Gender.MALE,
                    dateOfBirth = LocalDate.of(1990, 1, 1),
                    nationality = "VNM",
                    maritalStatus = "MARRIED",
                    phoneNumber = "+84 374 123 456",
                    religion = "NONE",
                    nationalId = "079*********",
                    passportNumber = "*********",
                ),
                Member(
                    id = 333,
                    firstName = "Mary",
                    lastName = "Jane",
                    email = "<EMAIL>",
                    fullName = "Mary Jane",
                    userId = "124",
                    persona = Persona("Member"),
                    gender = Gender.UNSPECIFIED))

        every { bulkOrgManagementDataServiceAdapter.getOrgManagementData(any(), any()) } returns
            listOf(
                OrgManagementData(
                    contractId = 123,
                    department =
                        Department(
                            id = 323,
                            companyId = COMPANY_ID,
                            name = "Department 1",
                        ),
                    directManager =
                        CompanyManager(
                            companyId = COMPANY_ID,
                            email = "<EMAIL>",
                        ),
                    isManager = true))

        every { memberServiceAdapter.getMemberEmergencyContacts(any()) } returns
            listOf(
                EmergencyContact(
                    memberId = 222,
                    name = "Anne",
                    relationship = "Wife",
                    phoneNumber = "2222-222",
                ),
                EmergencyContact(
                    memberId = 333,
                    name = "Barry",
                    relationship = "Husband",
                    phoneNumber = "3333-333",
                ))

        every { memberServiceAdapter.getMemberEducationDetails(any()) } returns
            listOf(
                EducationDetail(
                    memberId = 222,
                    lastSchoolName = "Harvard University",
                    lastSchoolDegree = "Computer Science",
                    lastSchoolYearOfPassing = 2017,
                    lastSchoolGpa = 4.0f,
                    lastSchoolGrade = "Excellent"),
                EducationDetail(
                    memberId = 333,
                    lastSchoolName = "MIT University",
                    lastSchoolDegree = "Information Technology",
                    lastSchoolYearOfPassing = 2016,
                    lastSchoolGpa = 3.9f,
                    lastSchoolGrade = "Good"),
            )

        every { memberServiceAdapter.getMemberLastEmployerDetails(any()) } returns
            listOf(
                EmployerDetail(
                    memberId = 222,
                    lastEmployerName = "Google",
                    lastEmployerPosition = "Senior Software Engineer",
                    lastEmployerStartDate = LocalDate.of(2020, 1, 1),
                    lastEmployerEndDate = LocalDate.of(2023, 12, 31),
                ),
                EmployerDetail(
                    memberId = 333,
                    lastEmployerName = "Facebook",
                    lastEmployerPosition = "Software Engineer",
                    lastEmployerStartDate = LocalDate.of(2020, 5, 15),
                    lastEmployerEndDate = LocalDate.of(2023, 6, 15),
                ),
            )

        every { contractServiceAdapter.getComplianceParamsByContractIds(any()) } returns
            mapOf(
                123L to
                    listOf(
                        ComplianceParam(
                            key = "noticeAfterProbation",
                            value = 4,
                            unit = "WEEKS",
                        )))

        every { memberServiceAdapter.getMemberAddressDetails(any()) } returns
            listOf(
                AddressDetail(
                    memberId = 222,
                    currentAddress =
                        Address(
                            street = "TCV",
                            line1 = "196",
                            line2 = "Tam Thuan",
                            city = "Da Nang",
                            state = "Son Tra",
                            province = "Quang Nam",
                            country = CountryCode.VNM,
                            zipCode = "550001",
                            postalCode = "550002",
                        ),
                    permanentAddress =
                        Address(
                            street = "DDN",
                            line1 = "266",
                            line2 = "An Hai Bac",
                            city = "Little Elm",
                            state = "Texas",
                            province = "Dallas",
                            country = CountryCode.USA,
                            zipCode = "550003",
                            postalCode = "550004",
                        )),
                AddressDetail(
                    memberId = 333,
                    currentAddress =
                        Address(
                            street = "DDN",
                            line1 = "266",
                            line2 = "An Hai Bac",
                            city = "Da Nang",
                            state = "Son Tra",
                            province = "Quang Nam",
                            country = CountryCode.VNM,
                            zipCode = "550005",
                            postalCode = "550006",
                        )))
    }

    private fun mockEmployeeDataForGpDownload() {
        every { contractServiceAdapter.getContractsBy(any()) } returns emptyList()
        every {
            memberServiceAdapter.getMembersWithAddressesAndBankAccountsAndLegalData(any())
        } returns emptyList()
    }

    private fun callDownloadTemplateQuery(
        onboardingContext: BulkOnboardingContext = BulkOnboardingContext.GLOBAL_PAYROLL
    ): DocumentReadable {
        val response =
            client.execute(
                config =
                    HttpConfig(
                        permissions = listOf("download.company.contract.onboarding-template"),
                        userContext = UserContext(companyId = 1234L),
                        experience = "company"),
                variables =
                    mapOf(
                        "options" to
                            mapOf(
                                "contractType" to "HR_MEMBER",
                                "countryCode" to "CHE",
                                "companyId" to COMPANY_ID,
                                "entityId" to ENTITY_ID,
                                "context" to onboardingContext.name),
                        "pageRequest" to
                            mapOf(
                                "pageNumber" to 0,
                                "pageSize" to 100,
                            ),
                    ),
                query = "/queries/BulkOnboardingTemplateDownload.graphql",
            )

        return response.extractValueAsObject(
            "bulkOnboardingTemplateDownload", DocumentReadable::class.java)
    }

    companion object {
        const val COMPANY_ID = 1234L
        const val ENTITY_ID = 5431L
    }
}

class BulkTemplateDataRow {

    @ExcelCell(value = 0) var valueAtColumnZero: String = ""

    @ExcelCell(value = 1) var valueAtColumnOne: String = ""

    @ExcelCell(value = 2) var valueAtColumnTwo: String = ""

    @ExcelCell(value = 3) var valueAtColumnThree: String = ""

    @ExcelUnknownCells lateinit var data: Map<String, String>
}
