package com.multiplier.contract.onboarding.usecase

import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class EmployeeDataInputKtTest {

    @Nested
    inner class MergeEmployeeData {

        @Test fun `should merge employee data by employee id, if empty then use row number`() {}

        @Test fun `should do nothing if there is only one group`() {}

        @Test
        fun `should do nothing if there is only one group after excluding not-to-merge groups`() {}
    }
}
