package com.multiplier.contract.onboarding.service

import com.multiplier.company.schema.grpc.CompanyOuterClass.CompanyUserCapability
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.contants.ContractRevokedExperience
import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import com.multiplier.contract.onboarding.domain.model.ComplianceType
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.domain.model.OnboardingTask
import com.multiplier.contract.onboarding.domain.model.Person
import com.multiplier.contract.onboarding.repository.JpaOnboardingRepository
import com.multiplier.contract.onboarding.repository.model.JpaOnboarding
import com.multiplier.contract.onboarding.testing.OnboardingTaskHelper
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.contract.ContractOuterClass.*
import com.multiplier.country.schema.Country.GrpcLegalDocumentRequirement.GrpcLegalDocumentCategory
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class OnboardingTasksServiceTest {
    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockK private lateinit var memberServiceAdapter: MemberServiceAdapter
    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter
    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter
    @MockK private lateinit var jpaOnboardingRepository: JpaOnboardingRepository
    @MockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter
    @MockK private lateinit var onboardingAudServiceAdapter: OnboardingAudServiceAdapter
    @InjectMockKs private lateinit var underTest: OnboardingTasksService

    @Test
    fun getOnboardingTasks() {
        init()

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertAll({
            assertEquals(10, res.size)
            assertOnboardingTask(res, OnboardingTaskName.SIGN_MSA, false, 603L)
            assertOnboardingTask(res, OnboardingTaskName.COMPANY_COMPLETE_ONBOARDING, true, 603L)
            assertOnboardingTask(res, OnboardingTaskName.SEND_CONTRACT, true, 603L)
            assertOnboardingTask(res, OnboardingTaskName.SIGN_CONTRACT, true, 602L)
            assertOnboardingTask(res, OnboardingTaskName.PAY_DEPOSIT, true, 601L)
            assertOnboardingTask(res, OnboardingTaskName.SIGN_CONTRACT, true, 301L)
            assertOnboardingTask(res, OnboardingTaskName.PLATFORM_ONBOARDING, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.EMPLOYEE_ID_AND_WORK_EMAIL, false, 603L)
        })
    }

    @Test
    fun `should include employee id & work email task when contract type is EOR`() {
        init()

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertAll({
            assertOnboardingTask(res, OnboardingTaskName.EMPLOYEE_ID_AND_WORK_EMAIL, false, 603L)
        })
    }

    @Test
    fun `should return a fake company signatory user when the company has no signatory`() {
        init(hasSignatory = false)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!
        val signContractTask = res[3]

        assertThat(signContractTask.pendingOn?.firstName).isEqualTo("COMPANY_SIGNATORY")
    }

    @Test
    fun `should not include send and sign contract tasks when the contract requires a visa`() {
        init(visaFlow = true)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertAll({
            assertEquals(7, res.size)
            assertOnboardingTask(res, OnboardingTaskName.SIGN_MSA, false, 603L)
            assertOnboardingTask(res, OnboardingTaskName.COMPANY_COMPLETE_ONBOARDING, true, 603L)
            assertOnboardingTask(res, OnboardingTaskName.PAY_DEPOSIT, true, 601L)
            assertOnboardingTask(res, OnboardingTaskName.PLATFORM_ONBOARDING, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.EMPLOYEE_ID_AND_WORK_EMAIL, false, 603L)
        })
    }

    @Test
    fun `should not include deposit, send and sign contract tasks when the member is HR Member`() {
        init(contractType = ContractType.HR_MEMBER)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertAll({
            assertEquals(6, res.size)
            assertOnboardingTask(res, OnboardingTaskName.SIGN_MSA, false, 603L)
            assertOnboardingTask(res, OnboardingTaskName.COMPANY_COMPLETE_ONBOARDING, true, 603L)
            assertOnboardingTask(res, OnboardingTaskName.PLATFORM_ONBOARDING, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.EMPLOYEE_ID_AND_WORK_EMAIL, false, 603L)
        })
    }

    @Test
    fun `should not include msa, deposit and payroll tasks when the member is Freelancer`() {
        init(contractType = ContractType.FREELANCER)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertAll({
            assertEquals(6, res.size)
            assertOnboardingTask(res, OnboardingTaskName.COMPANY_COMPLETE_ONBOARDING, true, 603L)
            assertOnboardingTask(res, OnboardingTaskName.SEND_CONTRACT, true, 603L)
            assertOnboardingTask(res, OnboardingTaskName.SIGN_CONTRACT, true, 602L)
            assertOnboardingTask(res, OnboardingTaskName.SIGN_CONTRACT, true, 301L)
            assertOnboardingTask(res, OnboardingTaskName.PLATFORM_ONBOARDING, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS, false, 301L)
        })
    }

    @Test
    fun `should not include send and sign contract tasks when the contract is on an EOR partner`() {
        init(onboardingEnabled = false)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertAll({
            assertEquals(7, res.size)
            assertOnboardingTask(res, OnboardingTaskName.SIGN_MSA, false, 603L)
            assertOnboardingTask(res, OnboardingTaskName.COMPANY_COMPLETE_ONBOARDING, true, 603L)
            assertOnboardingTask(res, OnboardingTaskName.PAY_DEPOSIT, true, 601L)
            assertOnboardingTask(res, OnboardingTaskName.PLATFORM_ONBOARDING, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.EMPLOYEE_ID_AND_WORK_EMAIL, false, 603L)
        })
    }

    @Test
    fun `should not include send and sign contract tasks when the contract have a custom template`() {
        init(
            contractType = ContractType.FREELANCER,
            contractAgreementType =
                com.multiplier.contract.onboarding.domain.model.ContractAgreementType
                    .CUSTOM_TEMPLATE)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertAll({
            assertEquals(3, res.size)
            assertOnboardingTask(res, OnboardingTaskName.COMPANY_COMPLETE_ONBOARDING, true, 603L)
            assertOnboardingTask(res, OnboardingTaskName.PLATFORM_ONBOARDING, false, 301L)
            assertOnboardingTask(res, OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS, false, 301L)
        })
    }

    @Test
    fun `should return empty member tasks when contract type is HR_MEMBER and onboarding flow is global payroll only`() {
        init(
            contractType = ContractType.HR_MEMBER,
            onboardingFlowType = DirectEmployeeOnboardingFlowType.GP_ONLY)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertThat(res.filter { it.pendingOn?.persona == Person.Persona.MEMBER }).isEmpty()
    }

    @Test
    fun `should include EMPLOYEE_ID_AND_WORK_EMAIL task when contract type is HR_MEMBER`() {
        init(workEmail = "work_email", employeeId = "E001", contractType = ContractType.HR_MEMBER)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertThat(res.filter { it.name == OnboardingTaskName.EMPLOYEE_ID_AND_WORK_EMAIL })
            .isNotEmpty()
    }

    @Test
    fun `should not include member tasks when it is SOR HRIS only flow`() {
        init(
            contractType = ContractType.HR_MEMBER,
            onboardingFlowType = DirectEmployeeOnboardingFlowType.HRIS_ONLY)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertAll({
            assertNoOnboardingTask(res, OnboardingTaskName.PLATFORM_ONBOARDING)
            assertNoOnboardingTask(res, OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS)
            assertNoOnboardingTask(res, OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS)
        })
    }

    @Test
    fun `should not include member tasks when it is SOR GP only flow`() {
        init(
            contractType = ContractType.HR_MEMBER,
            onboardingFlowType = DirectEmployeeOnboardingFlowType.GP_ONLY)

        val res = underTest.getOnboardingTasksByOnboardingId(setOf(1L))[1L]!!

        assertAll({
            assertNoOnboardingTask(res, OnboardingTaskName.PLATFORM_ONBOARDING)
            assertNoOnboardingTask(res, OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS)
            assertNoOnboardingTask(res, OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS)
        })
    }

    private fun assertOnboardingTask(
        onboardingTaskDTOs: List<OnboardingTask>,
        name: OnboardingTaskName,
        completed: Boolean,
        pendingOnId: Long?
    ) {
        assertThat(
                onboardingTaskDTOs.filter {
                    it.name == name && it.completed == completed && it.pendingOn?.id == pendingOnId
                })
            .isNotEmpty()
    }

    private fun assertNoOnboardingTask(
        onboardingTaskDTOs: List<OnboardingTask>,
        name: OnboardingTaskName
    ) {
        assertThat(onboardingTaskDTOs.filter { it.name == name }).isEmpty()
    }

    companion object {
        const val CONTRACT_ID = 101L
    }

    private fun init(
        hasSignatory: Boolean = true,
        hasBillingContract: Boolean = true,
        visaFlow: Boolean = false,
        onboardingEnabled: Boolean = true,
        contractType: ContractType = ContractType.EMPLOYEE,
        contractAgreementType:
            com.multiplier.contract.onboarding.domain.model.ContractAgreementType =
            com.multiplier.contract.onboarding.domain.model.ContractAgreementType
                .MULTIPLIER_TEMPLATE,
        workEmail: String = "",
        employeeId: String = "",
        onboardingFlowType: DirectEmployeeOnboardingFlowType =
            DirectEmployeeOnboardingFlowType.NONE,
    ) {
        val companyOnboarding =
            JpaOnboarding(
                id = 1L,
                contractId = CONTRACT_ID,
                experience = "company",
                status = ContractOnboardingStatus.ACTIVE)
        every { jpaOnboardingRepository.findAllById(setOf(1L)) } returns listOf(companyOnboarding)

        every { contractServiceAdapter.getNonDeletedNonEndedContracts(setOf(CONTRACT_ID)) } returns
            listOf(
                Contract.newBuilder()
                    .setId(CONTRACT_ID)
                    .setCompanyId(201)
                    .setMemberId(301)
                    .setCreatedBy(1603)
                    .setCountry("IND")
                    .setWorkStatus(
                        if (visaFlow) CountryWorkStatus.REQUIRES_VISA
                        else CountryWorkStatus.RESIDENT)
                    .setType(contractType)
                    .setEmployeeId(employeeId)
                    .setWorkEmail(workEmail)
                    .build())

        every { contractServiceAdapter.getDepositPaidContractIds(setOf(CONTRACT_ID)) } returns
            setOf(CONTRACT_ID)

        every { memberServiceAdapter.getMembers(setOf(301L)) } returns
            listOf(OnboardingTaskHelper.buildMember(301L, 1301L, "member"))

        every { companyServiceAdapter.getMSASignedCompanyIds(setOf(201L)) } returns emptySet()

        every {
            companyServiceAdapter.getCompanyUsersBy(GetCompanyUsersFilter(companyIds = setOf(201L)))
        } returns
            listOfNotNull(
                if (hasBillingContract)
                    OnboardingTaskHelper.buildCompanyUser(
                        601L, 201L, "billing_contact", setOf(CompanyUserCapability.BILLING_CONTACT))
                else null,
                if (hasSignatory)
                    OnboardingTaskHelper.buildCompanyUser(
                        602L, 201L, "signatory", setOf(CompanyUserCapability.SIGNATORY))
                else null,
                OnboardingTaskHelper.buildCompanyUser(603L, 201L, "contract_creator", setOf()))

        val companyOnboardingDTO =
            Onboarding(
                companyOnboarding.id,
                companyOnboarding.contractId,
                companyOnboarding.experience,
                companyOnboarding.status.toDomain(),
                companyOnboarding.revokedBy)
        every {
            onboardingServiceAdapter.getAllOnboardingsByContractIds(setOf(CONTRACT_ID), "company")
        } returns mapOf(Pair(CONTRACT_ID, companyOnboardingDTO))

        val memberOnboarding =
            Onboarding(
                2L, CONTRACT_ID, "member", OnboardingStatus.INVITED, ContractRevokedExperience.NONE)
        every {
            onboardingServiceAdapter.getAllOnboardingsByContractIds(setOf(CONTRACT_ID), "member")
        } returns mapOf(Pair(CONTRACT_ID, memberOnboarding))

        every {
            onboardingAudServiceAdapter.getAllContractIdsOfOnboardingAuditsWithExperienceAndStatus(
                setOf(CONTRACT_ID), "company", ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SIGNED)
        } answers { callOriginal() }

        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(setOf(301L)) } returns
            mapOf(Pair(301L, setOf("document_1", "document_2")))

        every {
            countryServiceAdapter.getRequiredDocumentKeysGroupByCountryCode(
                countryCodes = setOf(CountryAndState(CountryCode.IND)),
                category = GrpcLegalDocumentCategory.PAYROLL)
        } returns mapOf(CountryAndState(CountryCode.IND) to setOf("document_3"))

        every { countryServiceAdapter.isOnboardingEnabled(any()) } returns onboardingEnabled

        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns
            listOf(
                com.multiplier.contract.onboarding.domain.model.Compliance(
                    contractId = CONTRACT_ID,
                    agreementType = contractAgreementType,
                    type = ComplianceType.MULTIPLIER,
                    agreementId = 999,
                ))

        every { contractServiceAdapter.getDirectEmployeeOnboardingFlowTypes(any()) } returns
            mapOf(CONTRACT_ID to onboardingFlowType)
    }
}
