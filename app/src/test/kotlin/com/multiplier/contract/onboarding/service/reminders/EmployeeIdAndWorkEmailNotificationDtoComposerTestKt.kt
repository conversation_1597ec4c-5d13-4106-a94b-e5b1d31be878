package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.company.schema.grpc.CompanyOuterClass.*
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.model.Persona
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.core.schema.common.Common
import com.multiplier.member.schema.CountryCode
import io.mockk.every
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
internal class EmployeeIdAndWorkEmailNotificationDtoComposerTestKt {

    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK private lateinit var companyServiceAdapter: CompanyServiceAdapter

    @MockK private lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockK private lateinit var documentServiceAdapter: DocumentServiceAdapter

    private lateinit var underTest: EmployeeIdAndWorkEmailNotificationDtoComposer

    private val defaultCompany =
        com.multiplier.contract.onboarding.domain.model.Company(
            id = 11,
            displayName = "Multiplier",
            logoId = 99,
            countryFullName = "Vietnam",
            countryCode = com.multiplier.contract.onboarding.types.CountryCode.VNM,
            msaSigned = false,
            isTest = false,
        )

    @BeforeEach
    fun beforeEach() {
        underTest =
            EmployeeIdAndWorkEmailNotificationDtoComposer(
                contractServiceAdapter = contractServiceAdapter,
                companyServiceAdapter = companyServiceAdapter,
                memberServiceAdapter = memberServiceAdapter,
                documentServiceAdapter = documentServiceAdapter,
                baseUrl = "https://app.usemultiplier.com",
            )
    }

    private fun initMocks() {
        every { contractServiceAdapter.getNonDeletedNonEndedContracts(setOf(1L)) }
            .returns(
                listOf(
                    Contract.newBuilder()
                        .setId(1L)
                        .setCompanyId(11L)
                        .setMemberId(21L)
                        .setCountry(CountryCode.VNM.name)
                        .setCreatedBy(31L)
                        .setCreatedBy(31L)
                        .build()))

        every { companyServiceAdapter.getCompanies(setOf(11L)) } returns (listOf(defaultCompany))

        every { memberServiceAdapter.getMembers(any()) } returns
            (listOf(
                com.multiplier.contract.onboarding.domain.model.Member(
                    id = 21L,
                    fullName = "Test Member1",
                    firstName = "Test",
                    lastName = "Member1",
                    email = "<EMAIL>",
                    userId = "21",
                    persona = Persona("MEMBER"),
                )))

        every { documentServiceAdapter.getDocuments(listOf(91L)) } returns
            (listOf(DocumentResponse(id = 91L, viewUrl = "http://image.com/logo11")))

        every { documentServiceAdapter.getCompanyLogoLinks(listOf(99)) } returns
            mapOf(
                defaultCompany.logoId to
                    DocumentResponse(id = 99, viewUrl = "http://multiplierlogo.png"))

        every { companyServiceAdapter.getCompanyUsersBy(any()) }
            .returns(
                listOf(
                    createCompanyUser(
                        id = 41L,
                        userId = 31L,
                        companyId = 11L,
                        firstName = "Contract creator1",
                        email = "<EMAIL>"),
                    createCompanyUser(
                        id = 42L,
                        userId = 32L,
                        companyId = 11L,
                        firstName = "Signatory1",
                        email = "<EMAIL>",
                        capabilities = listOf(CompanyUserCapability.SIGNATORY)),
                    createCompanyUser(
                        id = 43L,
                        userId = 33L,
                        companyId = 11L,
                        firstName = "Billing contact1",
                        email = "<EMAIL>",
                        capabilities = listOf(CompanyUserCapability.BILLING_CONTACT)),
                    createCompanyUser(
                        id = 44L,
                        userId = null,
                        companyId = 11L,
                        firstName = null,
                        email = "<EMAIL>",
                        roles = listOf(CompanyUserRole.PRIMARY_ADMIN),
                    )))

        every { contractServiceAdapter.getDepositPaidContractIds(setOf(1L)) } returns emptySet()

        // missing mocks
        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns emptyMap()
        every { contractServiceAdapter.getComplianceByContractIds(any()) } returns emptyList()
    }

    @Test
    fun `compose DTO when all data is present`() {
        // Given
        initMocks()
        val contract =
            Contract.newBuilder()
                .setId(1L)
                .setCompanyId(11L)
                .setMemberId(200L)
                .setEmployeeId("EMP123")
                .setWorkEmail("<EMAIL>")
                .build()
        underTest.init(listOf(contract))

        // When
        val result = underTest.composeEmployeeIdAndWorkEmailReminderEmailDto(contract)

        // Then
        assertEquals("Reminder to complete filling employee ID and work email", result.subject)
        assertEquals(listOf("<EMAIL>"), result.companyUserEmails)
    }

    @Test
    fun `compose DTO throw error no emails found`() {
        initMocks()
        val contract =
            Contract.newBuilder()
                .setId(1L)
                .setCompanyId(11L)
                .setMemberId(200L)
                .setEmployeeId("EMP123")
                .setWorkEmail("<EMAIL>")
                .build()
        every { companyServiceAdapter.getCompanyUsersBy(any()) }.returns(emptyList())
        underTest.init(listOf(contract))

        Assertions.assertThrows(IllegalStateException::class.java) {
            underTest.composeEmployeeIdAndWorkEmailReminderEmailDto(contract)
        }
    }

    private fun createCompanyUser(
        id: Long,
        userId: Long?,
        companyId: Long,
        firstName: String?,
        email: String,
        capabilities: List<CompanyUserCapability> = emptyList(),
        roles: List<CompanyUserRole> = emptyList()
    ) =
        CompanyUser.newBuilder()
            .setId(id)
            .setUserId(userId?.toString() ?: "11111")
            .setCompanyId(companyId)
            .setFirstName(firstName ?: "")
            .addAllCompanyUserCapability(emptyList())
            .addAllCompanyUserCapability(capabilities)
            .addAllRoles(roles)
            .setIsAdmin(roles.any { it.name.contains("ADMIN") })
            .addAllEmails(
                listOf(Common.EmailAddress.newBuilder().setType("primary").setEmail(email).build()))
            .build()
}
