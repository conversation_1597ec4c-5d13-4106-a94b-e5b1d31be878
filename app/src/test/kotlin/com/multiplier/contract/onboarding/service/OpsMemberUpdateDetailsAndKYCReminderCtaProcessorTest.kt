package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.service.helpers.CompanyTestDataFactory.createCompany
import com.multiplier.contract.onboarding.service.helpers.ContractTestDataFactory.createContract
import com.multiplier.contract.onboarding.service.helpers.JpaOnboardingTestDataFactory.createJpaOnboarding
import com.multiplier.contract.onboarding.service.helpers.MemberTestDataFactory.createMember
import com.multiplier.contract.onboarding.service.helpers.OperationsUserTestDataFactory.createOperationsUser
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.DisplayNameGeneration
import org.junit.jupiter.api.DisplayNameGenerator
import org.junit.jupiter.api.Test

@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
class OpsMemberUpdateDetailsAndKYCReminderCtaProcessorTest {

    private val processor = OpsMemberUpdateDetailsAndKYCReminderCtaProcessor()

    @Test
    fun validate_should_return_true_when_onboardingMember_status_are_correct() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember =
                    createJpaOnboarding(
                        experience = "member",
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_true_when_onboardingCompany_status_are_correct() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_false_when_onboardingCompany_and_onboardingMember_status_is_incorrect() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember =
                    createJpaOnboarding(
                        experience = "member",
                        currentStep = OnboardingStep.ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.MEMBER_VERIFICATION_IN_PROGRESS),
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_false_when_opsUser_is_not_assigned() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember =
                    createJpaOnboarding(
                        experience = "member",
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = null,
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_false_when_contract_is_not_eor() {
        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_SIGNING,
                        status = ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT),
                onboardingMember = null,
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = createMember(),
                operationsUser = createOperationsUser(),
                company = createCompany(),
                contract = createContract(),
                guideLink = "link",
                preregistrationRequiredCountry = false)
        val result = processor.validate(emailTemplateData)

        assertThat(result).isFalse()
    }

    @Test
    fun fetchData_should_return_a_populated_list_of_EmailData_with_10_payroll_cutoff_for_PAK_and_JPN() {
        val opsUser = createOperationsUser()

        val contract = createContract(country = CountryCode.PAK)

        val member = createMember()

        val company = createCompany()

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember =
                    createJpaOnboarding(
                        experience = "member",
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = member,
                operationsUser = opsUser,
                company = company,
                contract = contract,
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.fetchDataAndOverrideEditableParams(emailTemplateData)

        assertThat(result).hasSize(7)
        assertThat(result[ReminderCtaHelper.GUIDE_LINK]).isEqualTo("link")
        assertThat(result[ReminderCtaHelper.MEMBER_NAME]).isEqualTo(member.fullName)
        assertThat(result[ReminderCtaHelper.ONBOARDING_SPECIALIST])
            .isEqualTo(opsUser.firstName + " " + opsUser.lastName)
        assertThat(result[ReminderCtaHelper.CLIENT_NAME]).isEqualTo(company.displayName)
        assertThat(result[ReminderCtaHelper.MONTH_PAY_DATE]).isEqualTo("10th")
        assertThat(result[ReminderCtaHelper.IS_PRE_REGISTRATION_COUNTRY]).isEqualTo("false")
        assertThat(result[ReminderCtaHelper.MULTI_FREQUENCY_SUPPORT_ENABLED]).isEqualTo("false")
    }

    @Test
    fun fetchData_should_return_payrollCutoffDate_as_15_for_countries_other_than_PAK_and_JPN() {
        val contract = createContract()

        val member = createMember()

        val opsUser = createOperationsUser()

        val company = createCompany()

        val emailTemplateData =
            EmailTemplateData(
                onboardingCompany =
                    createJpaOnboarding(
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                onboardingMember =
                    createJpaOnboarding(
                        experience = "member",
                        currentStep = OnboardingStep.ONBOARDING_MEMBER,
                        status = ContractOnboardingStatus.MEMBER_INVITED),
                submittedLegalDocument = setOf(),
                payrollFormRequirements = mapOf(),
                member = member,
                operationsUser = opsUser,
                company = company,
                contract = contract,
                guideLink = "link",
                preregistrationRequiredCountry = false)

        val result = processor.fetchDataAndOverrideEditableParams(emailTemplateData)

        Assertions.assertEquals(7, result.size)
        assertThat(result[ReminderCtaHelper.GUIDE_LINK]).isEqualTo("link")
        assertThat(result[ReminderCtaHelper.MEMBER_NAME]).isEqualTo(member.fullName)
        assertThat(result[ReminderCtaHelper.ONBOARDING_SPECIALIST])
            .isEqualTo(opsUser.firstName + " " + opsUser.lastName)
        assertThat(result[ReminderCtaHelper.CLIENT_NAME]).isEqualTo(company.displayName)
        assertThat(result[ReminderCtaHelper.MONTH_PAY_DATE]).isEqualTo("15th")
        assertThat(result[ReminderCtaHelper.IS_PRE_REGISTRATION_COUNTRY]).isEqualTo("false")
        assertThat(result[ReminderCtaHelper.MULTI_FREQUENCY_SUPPORT_ENABLED]).isEqualTo("false")
    }

    @Test
    fun validate_should_return_false_when_country_is_USA_and_multiFrequencySupportEnabled_is_true() {
        val emailTemplateData =
            createEmailTemplateData(country = CountryCode.USA, multiFrequencySupportEnabled = true)

        val result = processor.validate(emailTemplateData)
        assertThat(result).isFalse()
    }

    @Test
    fun validate_should_return_true_when_country_is_USA_and_multiFrequencySupportEnabled_is_false() {
        val emailTemplateData =
            createEmailTemplateData(country = CountryCode.USA, multiFrequencySupportEnabled = false)

        val result = processor.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_true_when_country_is_not_USA_and_multiFrequencySupportEnabled_is_true() {
        val emailTemplateData =
            createEmailTemplateData(country = CountryCode.SGP, multiFrequencySupportEnabled = true)

        val result = processor.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    @Test
    fun validate_should_return_true_when_country_is_not_USA_and_multiFrequencySupportEnabled_is_false() {
        val emailTemplateData =
            createEmailTemplateData(country = CountryCode.GBR, multiFrequencySupportEnabled = false)

        val result = processor.validate(emailTemplateData)
        assertThat(result).isTrue()
    }

    private fun createEmailTemplateData(
        country: CountryCode = CountryCode.SGP,
        multiFrequencySupportEnabled: Boolean = false,
        contractType: ContractOuterClass.ContractType = ContractOuterClass.ContractType.EMPLOYEE,
        hasOperationsUser: Boolean = true,
        onboardingStatus: ContractOnboardingStatus = ContractOnboardingStatus.MEMBER_INVITED
    ): EmailTemplateData {
        return EmailTemplateData(
            onboardingCompany =
                createJpaOnboarding(
                    status = onboardingStatus, currentStep = OnboardingStep.ONBOARDING_MEMBER),
            onboardingMember =
                createJpaOnboarding(
                    experience = "member",
                    currentStep = OnboardingStep.ONBOARDING_MEMBER,
                    status = ContractOnboardingStatus.MEMBER_INVITED),
            submittedLegalDocument = setOf(),
            payrollFormRequirements = mapOf(),
            member = createMember(),
            operationsUser = if (hasOperationsUser) createOperationsUser() else null,
            company = createCompany(),
            contract = createContract(type = contractType, country = country),
            guideLink = "link",
            preregistrationRequiredCountry = false,
            multiFrequencySupportEnabled = multiFrequencySupportEnabled)
    }
}
