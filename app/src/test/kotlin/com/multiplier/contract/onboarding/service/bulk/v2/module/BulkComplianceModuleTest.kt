package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkComplianceServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.schema.contract.BulkContract.UpdateComplianceInput
import com.multiplier.country.schema.Country
import com.multiplier.country.schema.Country.GrpcComplianceParamDefinition
import com.multiplier.country.schema.Country.GrpcComplianceRequirementDefinition
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkComplianceModuleTest {
    @MockK private lateinit var bulkComplianceServiceAdapter: BulkComplianceServiceAdapter

    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkComplianceModule

    @Nested
    inner class GetDataSpecs {

        @Test
        fun `should return specific fields for aor`() {

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "noticeAfterProbation.value",
                        type = DataSpecType.NUMBER,
                        description = "Notice period for the contractor after probation",
                        mandatory = false),
                    DataSpec(
                        key = "noticeAfterProbation.unit",
                        type = DataSpecType.SELECT,
                        description = "Unit of Notice period",
                        allowedValues = listOf("DAYS"),
                        mandatory = false),
                )
        }

        @Test
        fun `should return filtered fields for eor based on response from country service`() {
            val paramDefinition =
                GrpcComplianceParamDefinition.newBuilder()
                    .setParam(
                        Country.GrpcComplianceParam.newBuilder()
                            .setComplianceParamPeriodLimit(
                                Country.GrpcComplianceParam.GrpcComplianceParamPeriodLimit
                                    .newBuilder()
                                    .setKey("probationPolicy")
                                    .setUnit(Country.GrpcComplianceParamPeriodUnit.MONTHS)
                                    .build())
                            .build())
                    .setEnabled(true)
                    .setRequired(true)
                    .build()

            val complianceDefinition =
                GrpcComplianceRequirementDefinition.newBuilder()
                    .addParamDefinitions(paramDefinition)
                    .build()

            every {
                countryServiceAdapter.getComplianceDefinitionForCountry(
                    any(), ContractType.EMPLOYEE)
            } returns complianceDefinition

            val dataSpecs =
                underTest.getDataSpecs(
                    BulkOnboardingOptions(
                        CountryCode.IND, ContractType.EMPLOYEE, 1, null, BulkOnboardingContext.EOR),
                    null)

            assertThat(dataSpecs)
                .contains(
                    DataSpec(
                        key = "rowIdentifier",
                        type = DataSpecType.TEXT,
                        label = "Row Identifier",
                        description =
                            "Unique row identifier for employee data across upload sheets",
                        source = "COMPLIANCE_SCHEMA_DATA_SPEC",
                        mandatory = true),
                    DataSpec(
                        key = "probationPolicy.value",
                        type = DataSpecType.NUMBER,
                        description = null,
                        source = "COMPLIANCE_SCHEMA_DATA_SPEC",
                        mandatory = false),
                    DataSpec(
                        key = "probationPolicy.unit",
                        type = DataSpecType.SELECT,
                        description = null,
                        source = "COMPLIANCE_SCHEMA_DATA_SPEC",
                        allowedValues = listOf("MONTHS"),
                        mandatory = false),
                )
        }
    }

    @Nested
    inner class Validate {

        @Test
        fun `should call bulkComplianceServiceAdapter for aor`() {

            val input =
                listOf(
                    EmployeeData(
                        identification = EmployeeIdentification(rowNumber = 1),
                        data = mapOf("contractId" to "123", "email" to "<EMAIL>")))
            val options =
                BulkOnboardingOptions(
                    null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR)

            every {
                bulkComplianceServiceAdapter.validateUpdateComplianceInputs(input, options)
            } returns emptyList()

            underTest.validate(input, options, null)

            verify { bulkComplianceServiceAdapter.validateUpdateComplianceInputs(input, options) }
        }

        @Test
        fun `should call bulkComplianceServiceAdapter for aor without empty data`() {

            val input =
                listOf(
                    EmployeeData(
                        identification = EmployeeIdentification(rowNumber = 1),
                        data =
                            mapOf(
                                "contractId" to "123",
                                "email" to "<EMAIL>",
                                "noticePeriodAfterProbation.value" to "")))
            val options =
                BulkOnboardingOptions(
                    null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR)

            val slot = slot<List<EmployeeData>>() // Capture argument

            every {
                bulkComplianceServiceAdapter.validateUpdateComplianceInputs(capture(slot), options)
            } returns emptyList()

            underTest.validate(input, options, null)

            verify { bulkComplianceServiceAdapter.validateUpdateComplianceInputs(any(), options) }
            val capturedInput = slot.captured
            assertThat(capturedInput).hasSize(1)
            assertThat(capturedInput.first().data).doesNotContainValue("")
        }
    }

    @Nested
    inner class CreateData {

        @Test
        fun `should call bulkCompensationServiceAdapter for aor upsert`() {

            val validationResult =
                listOf(
                    GrpcValidationResult(
                        validationId = "rn_1",
                        success = true,
                        input = UpdateComplianceInput.newBuilder().setContractId(1).build(),
                        errors = emptyList(),
                        additionalInputProperties = mapOf("module1AddedKey" to "value1")))
            val options =
                BulkOnboardingOptions(
                    null, ContractType.CONTRACTOR, 1, null, BulkOnboardingContext.AOR)

            every { bulkComplianceServiceAdapter.updateCompliances(any(), options) } returns
                emptyList()

            val creationResult =
                BulkCreationResultV2(
                    requestIdToMemberId = mapOf("test2" to 2),
                    requestIdToContractId = mapOf("rn_1" to 1))

            underTest.create(validationResult, creationResult, options)

            verify {
                bulkComplianceServiceAdapter.updateCompliances(
                    withArg { assertThat(it).hasSize(1) },
                    withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.AOR) })
            }
        }
    }
}
