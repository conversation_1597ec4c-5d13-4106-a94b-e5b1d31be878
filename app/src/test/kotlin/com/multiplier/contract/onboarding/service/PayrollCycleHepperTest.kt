package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.PayrollCycle
import java.time.LocalDate
import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class PayrollCycleHepperTest {

    @Nested
    inner class GetPayrollCycleConfig {

        @Test
        fun returns_correct_payroll_cycle_for_monthly_payday() {
            val payrollCycleDTO =
                PayrollCycleHepper.getPayrollCycleConfig(
                    startDate = LocalDate.parse("2023-05-20"),
                    payDays = setOf(PayrollCycle.PayDay.Monthly))!!

            assertAll({
                assertEquals(PayrollCycle.PayFrequency.MONTHLY, payrollCycleDTO.payFrequency)
                assertEquals(LocalDate.parse("2023-06-15"), payrollCycleDTO.cutoffDate)
            })
        }

        @Test
        fun for_December_returns_correct_payroll_cycle_monthly_payday() {
            val payrollCycleDTO =
                PayrollCycleHepper.getPayrollCycleConfig(
                    startDate = LocalDate.parse("2023-12-20"),
                    payDays = setOf(PayrollCycle.PayDay.Monthly))!!

            assertAll({
                assertEquals(PayrollCycle.PayFrequency.MONTHLY, payrollCycleDTO.payFrequency)
                assertEquals(LocalDate.parse("2024-01-15"), payrollCycleDTO.cutoffDate)
            })
        }

        @Test
        fun returns_correct_payroll_cycle_for_semi_10_25_payday() {
            val payrollCycleDTO =
                PayrollCycleHepper.getPayrollCycleConfig(
                    startDate = LocalDate.parse("2023-05-20"),
                    payDays =
                        setOf(
                            PayrollCycle.PayDay.SemiMonthly10, PayrollCycle.PayDay.SemiMonthly25))!!

            assertAll({
                assertEquals(PayrollCycle.PayFrequency.SEMIMONTHLY, payrollCycleDTO.payFrequency)
                assertEquals(LocalDate.parse("2023-05-31"), payrollCycleDTO.cutoffDate)
            })
        }

        @Test
        fun returns_correct_payroll_cycle_for_semi_14_28_payday() {
            val payrollCycleDTO =
                PayrollCycleHepper.getPayrollCycleConfig(
                    startDate = LocalDate.parse("2023-05-20"),
                    payDays =
                        setOf(
                            PayrollCycle.PayDay.SemiMonthly14, PayrollCycle.PayDay.SemiMonthly28))!!

            assertAll({
                assertEquals(PayrollCycle.PayFrequency.SEMIMONTHLY, payrollCycleDTO.payFrequency)
                assertEquals(LocalDate.parse("2023-05-15"), payrollCycleDTO.cutoffDate)
            })
        }

        @Test
        fun returns_correct_payroll_cycle_for_semi_15_30_payday() {
            val payrollCycleDTO =
                PayrollCycleHepper.getPayrollCycleConfig(
                    startDate = LocalDate.parse("2023-05-20"),
                    payDays =
                        setOf(
                            PayrollCycle.PayDay.SemiMonthly15, PayrollCycle.PayDay.SemiMonthly30))!!

            assertAll({
                assertEquals(PayrollCycle.PayFrequency.SEMIMONTHLY, payrollCycleDTO.payFrequency)
                assertEquals(LocalDate.parse("2023-05-15"), payrollCycleDTO.cutoffDate)
            })
        }
    }
}
