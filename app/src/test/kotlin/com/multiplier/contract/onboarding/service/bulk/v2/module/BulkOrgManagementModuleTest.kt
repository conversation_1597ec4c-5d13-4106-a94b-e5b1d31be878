package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.DepartmentServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkOrgManagementDataServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.CompanyManager
import com.multiplier.contract.onboarding.domain.model.Department
import com.multiplier.contract.onboarding.domain.model.OrgManagementData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService.Companion.DepartmentSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService.Companion.DirectManagerEmailSpec
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.orgmanagement.schema.BulkAPI
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkOrgManagementModuleTest {

    @MockK private lateinit var departmentServiceAdapter: DepartmentServiceAdapter

    @MockK
    private lateinit var bulkOrgManagementDataServiceAdapter: BulkOrgManagementDataServiceAdapter

    @InjectMockKs private lateinit var bulkOrgManagementModule: BulkOrgManagementModule

    @Test
    fun getDataSpecs() {
        val onboardingOptions = BulkOnboardingOptions().apply { companyId = 101L }

        every { departmentServiceAdapter.getDepartments(onboardingOptions.companyId) } returns
            listOf(
                Department(id = 1, companyId = 101L, name = "Department 1"),
                Department(id = 2, companyId = 101L, name = "Department 2"),
            )

        val expectedDepartments = listOf("1 - Department 1", "2 - Department 2")
        val expectedDataSpecs =
            listOf(
                DirectManagerEmailSpec,
                DepartmentSpec.copy(mandatory = true, allowedValues = expectedDepartments))

        val dataSpecs = bulkOrgManagementModule.getDataSpecs(onboardingOptions, null)

        assertEquals(expectedDataSpecs, dataSpecs)
    }

    @Test
    fun validate() {
        val options = BulkOnboardingOptions()
        val expectedResults = listOf<GrpcValidationResult<BulkAPI.UpsertOrgManagementDataInput>>()

        val employeeData =
            listOf(
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 1),
                    data =
                        mapOf(
                            DirectManagerEmailSpec.key to "<EMAIL>",
                            DepartmentSpec.key to "1 - Department 1")),
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 2),
                    data =
                        mapOf(
                            DirectManagerEmailSpec.key to "<EMAIL>",
                        )),
            )

        every {
            bulkOrgManagementDataServiceAdapter.validateUpsertOrgManagementDataInputs(any(), any())
        } returns expectedResults

        val results = bulkOrgManagementModule.validate(employeeData, options, null)

        assertEquals(expectedResults, results)
        verify {
            bulkOrgManagementDataServiceAdapter.validateUpsertOrgManagementDataInputs(
                withArg {
                    assertEquals(it.size, 2)
                    assertEquals(it[0].identification.rowNumber, 1)
                    assertEquals(
                        it[0].data,
                        mapOf(
                            DirectManagerEmailSpec.key to "<EMAIL>",
                            DepartmentSpec.key to "1"))
                    assertEquals(it[1].identification.rowNumber, 2)
                    assertEquals(
                        it[1].data,
                        mapOf(
                            DirectManagerEmailSpec.key to "<EMAIL>",
                            DepartmentSpec.key to ""))
                },
                options)
        }
    }

    @Test
    fun create_should_handle_errors() {
        val validationResults =
            listOf(
                GrpcValidationResult(
                    validationId = "1",
                    input = BulkAPI.UpsertOrgManagementDataInput.newBuilder().build(),
                    success = true,
                    errors = emptyList()),
                GrpcValidationResult(
                    validationId = "2",
                    input = BulkAPI.UpsertOrgManagementDataInput.newBuilder().build(),
                    success = true,
                    errors = emptyList()))
        val bulkCreationResult = BulkCreationResultV2(requestIdToContractId = mapOf("1" to 101L))
        val options = BulkOnboardingOptions()
        val errors =
            listOf(
                "some error",
                "Upsert org management data cancelled due to an internal error: reference key is missing")

        every { bulkOrgManagementDataServiceAdapter.upsertOrgManagementData(any(), any()) } returns
            listOf(CreationResult.error("1", "some error"))

        val result = bulkOrgManagementModule.create(validationResults, bulkCreationResult, options)

        assertEquals(errors, result.errors.flatMap { it.errors })
        verify {
            bulkOrgManagementDataServiceAdapter.upsertOrgManagementData(
                withArg {
                    assertEquals(it.size, 1)
                    assertEquals(it[0].requestId, "1")
                    assertEquals(it[0].contractId, 101L)
                },
                options)
        }
    }

    @Nested
    inner class GetDataForSpecs {

        @Test
        fun `should return data for contract`() {
            val contractId = 123L
            val expectedData =
                mapOf(
                    DirectManagerEmailSpec.key to "<EMAIL>",
                    DepartmentSpec.key to "1 - Department 1")
            every { bulkOrgManagementDataServiceAdapter.getOrgManagementData(any(), any()) } returns
                listOf(
                    OrgManagementData(
                        contractId = contractId,
                        department = Department(id = 1, companyId = 1, name = "Department 1"),
                        directManager = CompanyManager(companyId = 1, email = "<EMAIL>")))

            val specs = listOf(DepartmentSpec, DirectManagerEmailSpec)
            val options = BulkOnboardingOptions.newBuilder().companyId(1).build()
            val data =
                bulkOrgManagementModule.getDataForSpecs(specs, options, mapOf(contractId to 1))

            assertThat(data.contractIdToEmployeeDataChunk)
                .containsExactlyEntriesOf(mapOf(contractId to EmployeeDataChunk(expectedData)))
        }
    }
}
