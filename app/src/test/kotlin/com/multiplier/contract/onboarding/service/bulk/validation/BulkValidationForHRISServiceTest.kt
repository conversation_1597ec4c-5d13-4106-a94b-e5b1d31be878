package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEducationDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEmergencyDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEmploymentDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberLegalDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkValidationForHRISServiceTest {

    @MockK private lateinit var bulkMemberDataService: BulkMemberDataService

    @MockK private lateinit var bulkContractDataService: BulkContractDataService

    @MockK private lateinit var bulkMemberLegalDataService: BulkMemberLegalDataService

    @MockK private lateinit var bulkComplianceDataService: BulkComplianceDataService

    @MockK private lateinit var bulkOrgManagementDataService: BulkOrgManagementDataService

    @MockK private lateinit var bulkMemberEmergencyDataService: BulkMemberEmergencyDataService

    @MockK private lateinit var bulkMemberEducationDataService: BulkMemberEducationDataService

    @MockK private lateinit var bulkMemberEmploymentDataService: BulkMemberEmploymentDataService

    @MockK private lateinit var bulkMemberAddressDataService: BulkMemberAddressDataService

    @MockK private lateinit var featureFlagService: FeatureFlagService

    @InjectMockKs private lateinit var service: BulkValidationForHRISService

    @BeforeEach
    fun beforeEach() {
        every { bulkMemberDataService.validate(any(), any()) } returns emptyList()
        every { bulkContractDataService.validate(any(), any()) } returns emptyList()
        every { bulkMemberLegalDataService.validate(any(), any()) } returns emptyList()
        every { bulkComplianceDataService.validate(any(), any()) } returns emptyList()
        every { bulkOrgManagementDataService.validate(any(), any()) } returns emptyList()
        every { bulkMemberEmergencyDataService.validate(any(), any()) } returns emptyList()
        every { bulkMemberEducationDataService.validate(any(), any()) } returns emptyList()
        every { bulkMemberEmploymentDataService.validate(any(), any()) } returns emptyList()
        every { bulkMemberAddressDataService.validate(any(), any()) } returns emptyList()
    }

    private val employeeData =
        listOf(
            EmployeeData(
                identification = EmployeeIdentification(employeeId = null, rowNumber = 2),
                data = mapOf("firstName" to "Test")))

    @Test
    fun `it adds error result from contract validation`() {
        every { bulkContractDataService.validate(any(), any()) } returns mockGrpcValidationResult()

        val options = BulkOnboardingOptions.newBuilder().build()
        val validationResult = service.validateEmployeeData(employeeData, options)

        val expected =
            EmployeeValidationResults(
                members = emptyList(),
                contracts = mockGrpcValidationResult(),
                compensations = emptyList(),
                legalData = emptyList(),
                bankData = emptyList(),
                compliances = emptyList(),
                orgManagementData = emptyList(),
                emergencyData = emptyList(),
                educationData = emptyList(),
                previousEmployerData = emptyList(),
                addressData = emptyList(),
            )

        assertThat(validationResult).isEqualTo(expected)
    }

    @Test
    fun `should return correct validated emergency contact result`() {
        every { bulkMemberEmergencyDataService.validate(any(), any()) } returns
            mockGrpcValidationResult()

        val options = BulkOnboardingOptions.newBuilder().build()
        val validationResult = service.validateEmployeeData(employeeData, options)

        val expected =
            EmployeeValidationResults(
                members = emptyList(),
                contracts = emptyList(),
                compensations = emptyList(),
                legalData = emptyList(),
                bankData = emptyList(),
                compliances = emptyList(),
                orgManagementData = emptyList(),
                emergencyData = mockGrpcValidationResult(),
                educationData = emptyList(),
                previousEmployerData = emptyList(),
                addressData = emptyList(),
            )

        assertThat(validationResult).isEqualTo(expected)
    }

    @Test
    fun `should return correct validated education details result`() {
        every { bulkMemberEducationDataService.validate(any(), any()) } returns
            mockGrpcValidationResult()

        val options = BulkOnboardingOptions.newBuilder().build()
        val validationResult = service.validateEmployeeData(employeeData, options)

        val expected =
            EmployeeValidationResults(
                members = emptyList(),
                contracts = emptyList(),
                compensations = emptyList(),
                legalData = emptyList(),
                bankData = emptyList(),
                compliances = emptyList(),
                orgManagementData = emptyList(),
                emergencyData = emptyList(),
                educationData = mockGrpcValidationResult(),
                previousEmployerData = emptyList(),
                addressData = emptyList(),
            )

        assertThat(validationResult).isEqualTo(expected)
    }

    @Test
    fun `should return correct validated previous employer details result`() {
        every { bulkMemberEmploymentDataService.validate(any(), any()) } returns
            mockGrpcValidationResult()

        val options = BulkOnboardingOptions.newBuilder().build()
        val validationResult = service.validateEmployeeData(employeeData, options)

        val expected =
            EmployeeValidationResults(
                members = emptyList(),
                contracts = emptyList(),
                compensations = emptyList(),
                legalData = emptyList(),
                bankData = emptyList(),
                compliances = emptyList(),
                orgManagementData = emptyList(),
                emergencyData = emptyList(),
                educationData = emptyList(),
                previousEmployerData = mockGrpcValidationResult(),
                addressData = emptyList(),
            )

        assertThat(validationResult).isEqualTo(expected)
    }

    @Test
    fun `should return correct validated addresses result`() {
        every { bulkMemberAddressDataService.validate(any(), any()) } returns
            mockGrpcValidationResult()

        val options = BulkOnboardingOptions.newBuilder().build()
        val validationResult = service.validateEmployeeData(employeeData, options)

        val expected =
            EmployeeValidationResults(
                members = emptyList(),
                contracts = emptyList(),
                compensations = emptyList(),
                legalData = emptyList(),
                bankData = emptyList(),
                emergencyData = emptyList(),
                educationData = emptyList(),
                previousEmployerData = emptyList(),
                compliances = emptyList(),
                orgManagementData = emptyList(),
                addressData = mockGrpcValidationResult(),
            )

        assertThat(validationResult).isEqualTo(expected)
    }

    private fun <T> mockGrpcValidationResult() =
        listOf(
            GrpcValidationResult<T>(
                success = false,
                errors = listOf("employee ID is missing"),
                validationId = "2",
                input = null))
}
