package com.multiplier.contract.onboarding.service.helpers

import com.multiplier.contract.onboarding.domain.model.Company
import com.multiplier.contract.onboarding.types.CountryCode

object CompanyTestDataFactory {

    fun createCompany(
        id: Long = 1234,
        logoId: Long = 2,
        displayName: String = "empty",
        countryFullName: String = "empty",
        countryCode: CountryCode = CountryCode.CXR,
        msaSigned: Boolean = false,
        isTest: Boolean = false,
    ): Company {
        return Company(
            id = id,
            logoId = logoId,
            displayName = displayName,
            countryFullName = countryFullName,
            countryCode = countryCode,
            msaSigned = msaSigned,
            isTest = isTest)
    }
}
