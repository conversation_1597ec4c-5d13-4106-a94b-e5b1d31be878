package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberLegalDataServiceAdapter
import com.multiplier.contract.onboarding.domain.model.member.ApplyTo
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataDefinition
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataType
import com.multiplier.contract.onboarding.types.*
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberLegalDataServiceTest {

    @MockK private lateinit var countryServiceAdapter: CountryServiceAdapter
    @MockK private lateinit var memberLegalDataServiceAdapter: BulkMemberLegalDataServiceAdapter

    @InjectMockKs private lateinit var underTest: BulkMemberLegalDataService

    @Test
    fun `should return legal definitions of CONTRACT_GENERATION stage for EOR`() {
        every {
            countryServiceAdapter.getMemberLegalDataDefinitions(any(), ContractType.EMPLOYEE)
        } returns
            listOf(
                MemberLegalDataDefinition(
                    key = "nationalId",
                    type = MemberLegalDataType.TEXT,
                    mandatory = false,
                    fetchStage = FetchStage.CONTRACT_GENERATION,
                    applyTo = ApplyTo.LOCAL_ONLY,
                    domainType = DomainType.LEGAL_DATA),
                MemberLegalDataDefinition(
                    key = "passportNumber",
                    type = MemberLegalDataType.TEXT,
                    mandatory = false,
                    fetchStage = FetchStage.MEMBER_LEGAL_DATA_CAPTURE,
                    applyTo = ApplyTo.ALL,
                    domainType = DomainType.LEGAL_DATA))

        val results =
            underTest.getDataSpecs(
                BulkOnboardingOptions.newBuilder()
                    .countryCode(CountryCode.ESP)
                    .contractType(ContractType.EMPLOYEE)
                    .context(BulkOnboardingContext.EOR)
                    .build())

        assertThat(results).hasSize(1)
        assertThat(results[0].key).isEqualTo("nationalId")
    }
}
