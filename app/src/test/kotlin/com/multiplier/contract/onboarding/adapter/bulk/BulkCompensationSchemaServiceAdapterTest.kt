package com.multiplier.contract.onboarding.adapter.bulk

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.google.protobuf.any
import com.multiplier.compensation.grpc.schema.BulkUploadServiceGrpc
import com.multiplier.contract.onboarding.adapter.bulk.BulkCompensationSchemaServiceAdapterImpl.Companion.COMPENSATION_SETUP_USE_CASE
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractIdSpec
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.grpc.common.bulkupload.v1.*
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkCompensationSchemaServiceAdapterTest {

    @MockK
    private lateinit var compensationBulkUploadServiceStub:
        BulkUploadServiceGrpc.BulkUploadServiceBlockingStub

    @InjectMockKs private lateinit var underTest: BulkCompensationSchemaServiceAdapterImpl

    @Test
    fun `getDataForSpecs should call rpc method with correct param`() {
        // Given
        coEvery { compensationBulkUploadServiceStub.getFieldData(any()) } returns
            BulkDataResponse.newBuilder()
                .addRows(
                    RowData.newBuilder()
                        .setKeys(platformKeys { contractId = 1L })
                        .putData("key1", "value1"))
                .addRows(
                    RowData.newBuilder()
                        .setKeys(platformKeys { contractId = 2L })
                        .putData("key2", "value2"))
                .build()

        val contractIds = listOf(1L, 2L)
        val companyId = 3L
        val entityId = 4L
        val options =
            BulkOnboardingOptions.newBuilder().companyId(companyId).entityId(entityId).build()

        // Actions
        val result = underTest.getFieldData(options, contractIds)

        // Assertions
        assertThat(result.size).isEqualTo(2)
        assertThat(result[0].data.size).isEqualTo(2)
        assertThat(result[0].data[ContractIdSpec.key]).isEqualTo("1")
        assertThat(result[0].data["key1"]).isEqualTo("value1")
        assertThat(result[1].data.size).isEqualTo(2)
        assertThat(result[1].data[ContractIdSpec.key]).isEqualTo("2")
        assertThat(result[1].data["key2"]).isEqualTo("value2")

        coVerify {
            compensationBulkUploadServiceStub.getFieldData(
                match {
                    it.useCase == COMPENSATION_SETUP_USE_CASE &&
                        it.contractIdsList == contractIds &&
                        it.companyIdsList == listOf(companyId) &&
                        it.entityIdsList == listOf(entityId)
                })
        }
    }

    @Test
    fun `should set jsonCustomParams only for EMPLOYEE contract type`() {
        val options =
            BulkOnboardingOptions(
                CountryCode.IND, ContractType.EMPLOYEE, 1L, 10L, BulkOnboardingContext.EOR)

        val moduleParams = ModuleParams(jsonCustomParams = mapOf("extra" to "data"))

        val expectedJson =
            jacksonObjectMapper()
                .writeValueAsString(
                    mapOf("extra" to "data", "OFFERING_TYPE" to "EOR", "COUNTRY_CODE" to "IND"))

        val grpcResponse = fieldRequirementsResponse {
            fieldRequirements += fieldRequirement {
                key = "some-key"
                type = FieldType.FIELD_TYPE_TEXT
                mandatory = true
            }
        }

        every { compensationBulkUploadServiceStub.getFieldRequirements(any()) } answers
            { call ->
                val request = call.invocation.args[0] as FieldRequirementsRequest
                assertThat(expectedJson).isEqualTo(request.jsonCustomParams)
                grpcResponse
            }

        val result = underTest.getDataSpecs(options, moduleParams)

        assertThat(result.size).isEqualTo(1)
        assertThat(result[0].key).isEqualTo("some-key")

        verify(exactly = 1) { compensationBulkUploadServiceStub.getFieldRequirements(any()) }
    }

    @Test
    fun `should not set jsonCustomParams only for EMPLOYEE contract type`() {
        val options =
            BulkOnboardingOptions(
                CountryCode.IND,
                ContractType.HR_MEMBER,
                1L,
                10L,
                BulkOnboardingContext.GLOBAL_PAYROLL)

        val moduleParams = ModuleParams(jsonCustomParams = mapOf("extra" to "data"))

        val grpcResponse = fieldRequirementsResponse {
            fieldRequirements += fieldRequirement {
                key = "some-key"
                type = FieldType.FIELD_TYPE_TEXT
                mandatory = true
            }
        }

        every { compensationBulkUploadServiceStub.getFieldRequirements(any()) } answers
            { call ->
                val request = call.invocation.args[0] as FieldRequirementsRequest
                assertThat(request.jsonCustomParams).isEmpty()
                grpcResponse
            }

        val result = underTest.getDataSpecs(options, moduleParams)

        assertThat(result.size).isEqualTo(1)
        assertThat(result[0].key).isEqualTo("some-key")

        verify(exactly = 1) { compensationBulkUploadServiceStub.getFieldRequirements(any()) }
    }
}
