package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.bulk.BulkContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import com.multiplier.growthbook.sdk.model.GBFeatureSource
import io.mockk.Runs
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkContractActivationModuleTest {

    @MockK private lateinit var bulkContractServiceAdapter: BulkContractServiceAdapter

    @MockK private lateinit var featureFlagService: FeatureFlagService

    @InjectMockKs private lateinit var underTest: BulkContractActivationModule

    @Test
    fun `should activate contracts successfully`() {

        val contractIds = listOf(101L, 102L)
        val options =
            BulkOnboardingOptions.newBuilder().context(BulkOnboardingContext.GLOBAL_PAYROLL).build()

        val bulkCreationResult =
            BulkCreationResultV2(
                newContractIds = contractIds,
                requestIdToContractId = mapOf("req1" to 101L, "req2" to 102L))

        every { featureFlagService.feature(eq(FeatureFlags.CONTRACT_TO_PAYROLL_LINKING)) } returns
            GBFeatureResult(
                value = true,
                on = true,
                off = false,
                source = GBFeatureSource.force,
                experiment = null,
                experimentResult = null)

        every { bulkContractServiceAdapter.activateContracts(contractIds) } just Runs

        // When
        val result = underTest.create(emptyList(), bulkCreationResult, options)

        // Then
        verify { bulkContractServiceAdapter.activateContracts(contractIds) }
        assertEquals(bulkCreationResult, result)
    }

    @Test
    fun `should handle activation errors`() {
        // Given
        val contractIds = listOf(101L, 102L)
        val options =
            BulkOnboardingOptions.newBuilder().context(BulkOnboardingContext.GLOBAL_PAYROLL).build()

        val bulkCreationResult =
            BulkCreationResultV2(
                newContractIds = contractIds,
                requestIdToContractId = mapOf("req1" to 101L, "req2" to 102L))

        every { featureFlagService.feature(eq(FeatureFlags.CONTRACT_TO_PAYROLL_LINKING)) } returns
            GBFeatureResult(
                value = true,
                on = true,
                off = false,
                source = GBFeatureSource.force,
                experiment = null,
                experimentResult = null)

        every { bulkContractServiceAdapter.activateContracts(contractIds) } throws
            RuntimeException("Activation failed")

        // When
        val result = underTest.create(emptyList(), bulkCreationResult, options)

        // Then
        verify { bulkContractServiceAdapter.activateContracts(contractIds) }

        // Verify that error results were added
        assertEquals(2, result.errors.size)
        assertEquals("req1", result.errors[0].requestId)
        assertEquals("req2", result.errors[1].requestId)
        assertEquals(true, result.errors[0].errors.first().contains("Activation failed"))
    }

    @Test
    fun `should return identifier`() {
        assertEquals(BulkContractActivationModule.MODULE_NAME, underTest.identifier())
    }

    @Test
    fun `validate should return empty list`() {
        // Given
        val employeeData =
            listOf(
                EmployeeData(
                    identification = EmployeeIdentification(rowNumber = 1),
                    data = mapOf("contractId" to "123")))
        val options = BulkOnboardingOptions.newBuilder().build()

        // When
        val result = underTest.validate(employeeData, options, null)

        // Then
        assertEquals(emptyList<GrpcValidationResult<*>>(), result)
    }
}
