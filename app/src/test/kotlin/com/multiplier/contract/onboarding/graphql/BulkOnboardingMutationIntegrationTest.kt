package com.multiplier.contract.onboarding.graphql

import com.multiplier.company.schema.grpc.CompanyOfferingOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.contract.onboarding.ContractOnboardingApplication
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.adapter.bulk.*
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.config.objectMapper
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.Department
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingNotificationService
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.service.jsonAt
import com.multiplier.contract.onboarding.testing.*
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.utils.getFileFromResources
import com.multiplier.contract.onboarding.utils.projectTmpFolder
import com.multiplier.contract.onboarding.utils.writeFileFromBase64
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.member.schema.MemberCreateInput
import com.multiplier.member.schema.UpdateMemberLegalDataInput
import com.multiplier.member.schema.UpsertAddressInput
import com.multiplier.member.schema.UpsertBankDetailsInput
import com.multiplier.member.schema.UpsertEducationDetailsInput
import com.multiplier.member.schema.UpsertEmergencyContactInput
import com.multiplier.member.schema.UpsertPreviousEmployerDetailsInput
import com.multiplier.orgmanagement.schema.BulkAPI
import com.netflix.graphql.dgs.autoconfig.DgsAutoConfiguration
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import io.mockk.justRun
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.http.ResponseEntity
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.ContextConfiguration

/**
 * Testing scope boundaries GraphQL <|---|> External service & repo adapters
 *
 * Do not mock anything in between, use service / unit tests for this!
 */
@ActiveProfiles("integration-test")
@ContextConfiguration(initializers = [PostgreSQLContainerInitializer::class])
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [ContractOnboardingApplication::class, DgsAutoConfiguration::class])
class BulkOnboardingMutationIntegrationTest(
    @LocalServerPort private val port: Int,
) {

    @MockkBean private lateinit var bulkMemberServiceAdapter: BulkMemberServiceAdapter

    @MockkBean private lateinit var bulkContractServiceAdapter: BulkContractServiceAdapter

    @MockkBean private lateinit var bulkCompensationServiceAdapter: BulkCompensationServiceAdapter

    @MockkBean
    private lateinit var bulkMemberEmergencyDataServiceAdapter:
        BulkMemberEmergencyDataServiceAdapter

    @MockkBean
    private lateinit var bulkMemberEducationServiceAdapter: BulkMemberEducationServiceAdapter

    @MockkBean
    private lateinit var bulkMemberEmploymentServiceAdapter: BulkMemberEmploymentServiceAdapter

    @MockkBean private lateinit var bulkMemberAddressServiceAdapter: BulkMemberAddressServiceAdapter

    @MockkBean
    private lateinit var bulkMemberLegalDataServiceAdapter: BulkMemberLegalDataServiceAdapter

    @MockkBean
    private lateinit var bulkOrgManagementDataServiceAdapter: BulkOrgManagementDataServiceAdapter

    @MockkBean private lateinit var countryServiceAdapter: CountryServiceAdapter

    @MockkBean private lateinit var paymentServiceAdapter: PaymentServiceAdapter

    @MockkBean private lateinit var memberBankDataAdapter: BulkMemberBankDataAdapter

    @MockkBean private lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockkBean private lateinit var notificationService: BulkOnboardingNotificationService

    @MockkBean private lateinit var companyServiceAdapter: CompanyServiceAdapter

    @MockkBean private lateinit var bulkComplianceServiceAdapter: BulkComplianceServiceAdapter

    @MockkBean private lateinit var bulkTimeoffServiceAdapter: BulkTimeoffServiceAdapter

    @MockkBean private lateinit var departmentServiceAdapter: DepartmentServiceAdapter

    @MockkBean
    private lateinit var bulkCompensationSchemaServiceAdapter: BulkCompensationSchemaServiceAdapter

    @MockkBean(relaxed = true)
    private lateinit var compensationSourceServiceAdapter: CompensationSourceServiceAdapter

    @MockkBean private lateinit var countryCache: CountryCache

    @Autowired private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter

    private val client = HttpClient(port)

    @BeforeEach
    fun beforeEach() {
        every { countryServiceAdapter.getMemberLegalDataDefinitions(any(), any()) } returns
            emptyList()

        every { paymentServiceAdapter.getPaymentAccountRequirementForMember(any()) } returns null

        every { contractServiceAdapter.getContractsBy(any()) } returns emptyList()

        every { companyServiceAdapter.getCompanyOfferings(any()) } returns
            mutableListOf(
                CompanyOfferingOuterClass.CompanyOffering.newBuilder()
                    .setOfferingCode(OfferingCode.GLOBAL_PAYROLL.name)
                    .build())
        every { companyServiceAdapter.getLegalEntity(any()) } returns
            LegalEntity.newBuilder()
                .setCompanyId(1000)
                .addCapabilities(Capability.GROSS_TO_NET.name)
                .build()

        every { departmentServiceAdapter.getDepartments(any()) } returns
            listOf(Department(323, 1000, "Department 1"), Department(324, 1234, "Department 2"))

        justRun { notificationService.sendOnboardingCompletedNotification(any(), any(), any()) }

        every { countryCache.getCountryCode(any()) } returns CountryCode.IND

        every { countryCache.getCountryName(any()) } returns "India"
    }

    @ParameterizedTest
    @ValueSource(
        strings =
            [
                "/queries/BulkOnboardingValidate.graphql",
                "/queries/BulkOnboardingTrigger.graphql",
            ])
    fun `should not allow without permissions`(query: String) {
        val response =
            callMutation(
                query = query,
                bulkFile = "/bulk/global-payroll/bulk-upload-no-employee-id.xlsx",
                permissions = emptyList(),
            )

        val errorType =
            objectMapper.jsonAt<String>("/errors/0/extensions/errorType", response.body!!)

        assertThat(errorType).isEqualTo("PERMISSION_DENIED")
    }

    @ParameterizedTest
    @ValueSource(
        strings =
            [
                "/queries/BulkOnboardingValidate.graphql",
                "/queries/BulkOnboardingTrigger.graphql",
            ])
    fun `should not allow a company user to onboard to another company`(query: String) {
        setupValidationMocks()
        setupCreationMocks()

        val userCompany = 2000L

        val response =
            callMutation(
                query = query,
                bulkFile = "/bulk/global-payroll/bulk-upload-1-member.xlsx",
                permissions = listOf("create.company.contract"),
                userContext = UserContext(companyId = userCompany))

        val errorType =
            objectMapper.jsonAt<String>("/errors/0/extensions/errorType", response.body!!)

        assertThat(errorType).isEqualTo("PERMISSION_DENIED")
    }

    @Test
    fun `should allow an ops user to onboard to any company`() {
        setupValidationMocks()
        setupCreationMocks()

        val response =
            callMutation(
                query = "/queries/BulkOnboardingTrigger.graphql",
                bulkFile = "/bulk/global-payroll/bulk-upload-1-member.xlsx",
                permissions = listOf("create.operations.contract"),
                userContext = UserContext(companyId = null))

        val validation =
            objectMapper.jsonAt<BulkOnboardingJob>("/data/bulkOnboardingTrigger", response.body!!)

        assertThat(validation).isNotNull()
    }

    @Test
    fun `should not allow an ops user to onboard to company with unmatched legal entity`() {
        setupValidationMocks()
        setupCreationMocks()
        val response =
            callMutation(
                companyId = 2000,
                entityId = 1000,
                query = "/queries/BulkOnboardingTrigger.graphql",
                bulkFile = "/bulk/global-payroll/bulk-upload-1-member.xlsx",
                permissions = listOf("create.operations.contract"),
                userContext = UserContext(companyId = null))

        println(response.body)
        val errorMessage = objectMapper.jsonAt<String>("/errors/0/message", response.body!!)

        assertThat(errorMessage)
            .contains("Legal entity with id 1000 doesn't belong to company with id 2000")
    }

    @Test
    fun `should generate validation report when input is invalid`() {

        val memberErrors = listOf("email is invalid", "first name invalid")
        val contractErrors = listOf("start date is invalid")

        val testConfig =
            BulkValidationTestConfig(
                memberValidation = ValidationMock(success = false, errors = memberErrors),
                contractValidation = ValidationMock(success = false, errors = contractErrors))

        setupValidationMocks(testConfig)

        val validationResult =
            callBulkValidateMutation(
                query = "/queries/BulkOnboardingValidate.graphql",
                bulkFile = "/bulk/global-payroll/bulk-upload-1-member.xlsx",
            )

        validationResult.writeReportToTempDir()

        assertThat(validationResult.validEmployeeCount).isEqualTo(0)
        assertThat(validationResult.errorEmployeeCount).isEqualTo(1)
        assertThat(validationResult.totalEmployeeCount).isEqualTo(1)
        assertThat(validationResult.validationResultFile).isNotNull
        assertThat(validationResult.validationResultFile.name).isNotNull
        assertThat(validationResult.validationResultFile.extension).isNotNull
        assertThat(validationResult.validationResultFile.contentType).isNotNull
        assertThat(validationResult.validationResultFile.blob).isNotNull
    }

    @Test
    fun `should not generate a report file when validation is valid`() {
        setupValidationMocks()

        val validationResult =
            callBulkValidateMutation(
                query = "/queries/BulkOnboardingValidate.graphql",
                bulkFile = "/bulk/global-payroll/bulk-upload-1-member.xlsx",
            )

        assertThat(validationResult.validationResultFile).isNull()
    }

    @Test
    fun `should return failure when upload sheet is empty`() {
        val validationResult =
            callBulkValidateMutation(
                query = "/queries/BulkOnboardingValidate.graphql",
                bulkFile = "/bulk/excel/dummy-bulk-upload-empty.xlsx",
            )

        assertThat(validationResult.totalEmployeeCount).isEqualTo(0)
        assertThat(validationResult.validEmployeeCount).isEqualTo(0)
        assertThat(validationResult.errorEmployeeCount).isEqualTo(0)
        assertThat(validationResult.errors.map { it.message })
            .hasSameElementsAs(listOf("No employee data found"))
    }

    @Test
    fun `onboard valid gp members`() {
        val companyId = 1000L
        val entityId = 1000L
        val memberId = faker.random.nextLong()
        val contractId = faker.random.nextLong()

        setupValidationMocks()
        setupCreationMocks(memberId = memberId, contractId = contractId)

        val result =
            callBulkOnboardMutation(
                companyId = companyId,
                entityId = entityId,
                onboardingContext = BulkOnboardingContext.GLOBAL_PAYROLL)

        val expectedMemberValidationData =
            mapOf(
                "firstName" to "Robert",
                "lastName" to "Oppenheimer",
                "email" to "<EMAIL>",
                "gender" to "UNSPECIFIED",
            )

        val expectedContractValidation =
            mapOf(
                "workStatus" to "RESIDENT",
                "term" to "PERMANENT",
                "startOn" to "2024-01-01",
                "position" to "SWE",
                "salary" to "10000.00",
                "employeeId" to "1",
                "firstName" to "Robert",
                "lastName" to "Oppenheimer",
                "email" to "<EMAIL>")

        verify {
            bulkMemberServiceAdapter.validateMemberCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().identification.employeeId).isEqualTo("1")
                    assertThat(it.first().identification.rowNumber).isEqualTo(3)
                    assertThat(it.first().data).containsAllEntriesOf(expectedMemberValidationData)
                },
                any())
        }

        verify {
            bulkMemberServiceAdapter.createMembers(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.GLOBAL_PAYROLL) })
        }

        verify {
            bulkContractServiceAdapter.validateContractCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().data).containsAllEntriesOf(expectedContractValidation)
                },
                any())
        }

        verify {
            bulkContractServiceAdapter.createContracts(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                },
                any())
        }

        verify {
            bulkCompensationServiceAdapter.validateCompensationCreateInputs(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.GLOBAL_PAYROLL) })
        }

        verify {
            bulkCompensationServiceAdapter.createCompensations(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().contractId).isEqualTo(contractId)
                },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.GLOBAL_PAYROLL) })
        }

        verify {
            bulkMemberLegalDataServiceAdapter.validateUpdateLegalDataInputs(
                withArg { assertThat(it).hasSize(1) }, any())
        }

        verify {
            bulkMemberLegalDataServiceAdapter.updateLegalData(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                },
                any())
        }

        verify { memberBankDataAdapter.validate(withArg { assertThat(it).hasSize(1) }, any()) }

        verify {
            memberBankDataAdapter.upsertBankData(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                })
        }

        verify {
            notificationService.sendOnboardingCompletedNotification(
                any(),
                withArg { assertThat(it.status).isEqualTo(BulkOnboardingJobStatus.SUCCESS) },
                any())
        }

        val memberOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contractIds = listOf(contractId), experience = "member")

        val companyOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contractIds = listOf(contractId), experience = "company")

        assertThat(companyOnboardings).hasSize(1)
        assertThat(companyOnboardings.values.first()).matches {
            it.status == OnboardingStatus.MEMBER_VERIFICATION_COMPLETED &&
                it.currentStep == OnboardingStep.ONBOARDING_ACTIVATION
        }

        assertThat(memberOnboardings).hasSize(1)
        assertThat(memberOnboardings.values.first()).matches {
            it.status == OnboardingStatus.MEMBER_VERIFICATION_COMPLETED &&
                it.currentStep == OnboardingStep.ONBOARDING_ACTIVATION
        }

        assertThat(result.status).isEqualTo(BulkOnboardingJobStatus.SUCCESS)
        assertThat(result.onboardingResult.onboardedEmployeeCount).isEqualTo(1)
    }

    @Test
    fun `onboard valid gp members with new compensation schema`() {
        val companyId = 1000L
        val entityId = 2000L
        val memberId = faker.random.nextLong()
        val contractId = faker.random.nextLong()

        every {
            compensationSourceServiceAdapter.isNewCompensationSourceEnabledForEntityId(entityId)
        } returns true
        setupValidationMocks()
        setupCreationMocks(memberId = memberId, contractId = contractId)

        val result =
            callBulkOnboardMutation(
                companyId = companyId,
                entityId = entityId,
                onboardingContext = BulkOnboardingContext.GLOBAL_PAYROLL,
                inputFile = "/bulk/global-payroll/bulk-upload-1-member-new-compensation.xlsx")

        val expectedMemberValidationData =
            mapOf(
                "firstName" to "Test",
                "lastName" to "Last",
                "email" to "<EMAIL>",
                "gender" to "MALE",
            )

        val expectedContractValidation =
            mapOf(
                "startOn" to "2030-10-16",
                "position" to "Tester",
                "employeeId" to "SGPE001",
                "firstName" to "Test",
                "lastName" to "Last",
                "email" to "<EMAIL>")

        verify {
            bulkMemberServiceAdapter.validateMemberCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().identification.employeeId).isEqualTo("SGPE001")
                    assertThat(it.first().identification.rowNumber).isEqualTo(3)
                    assertThat(it.first().data).containsAllEntriesOf(expectedMemberValidationData)
                },
                any())
        }

        verify {
            bulkMemberServiceAdapter.createMembers(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.GLOBAL_PAYROLL) })
        }

        verify {
            bulkContractServiceAdapter.validateContractCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().data).containsAllEntriesOf(expectedContractValidation)
                },
                any())
        }

        verify {
            bulkContractServiceAdapter.createContracts(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                },
                any())
        }

        verify {
            bulkCompensationSchemaServiceAdapter.validateCompensationCreateInputs(
                withArg { assertThat(it).hasSize(2) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.GLOBAL_PAYROLL) },
                any())
        }

        verify {
            bulkCompensationSchemaServiceAdapter.createCompensations(
                withArg { assertThat(it).hasSize(2) })
        }

        verify {
            bulkMemberLegalDataServiceAdapter.validateUpdateLegalDataInputs(
                withArg { assertThat(it).hasSize(1) }, any())
        }

        verify {
            bulkMemberLegalDataServiceAdapter.updateLegalData(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                },
                any())
        }

        verify { memberBankDataAdapter.validate(withArg { assertThat(it).hasSize(1) }, any()) }

        verify {
            memberBankDataAdapter.upsertBankData(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                })
        }

        verify {
            notificationService.sendOnboardingCompletedNotification(
                any(),
                withArg { assertThat(it.status).isEqualTo(BulkOnboardingJobStatus.SUCCESS) },
                any())
        }

        val memberOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contractIds = listOf(contractId), experience = "member")

        val companyOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contractIds = listOf(contractId), experience = "company")

        assertThat(companyOnboardings).hasSize(1)
        assertThat(companyOnboardings.values.first()).matches {
            it.status == OnboardingStatus.MEMBER_VERIFICATION_COMPLETED &&
                it.currentStep == OnboardingStep.ONBOARDING_ACTIVATION
        }

        assertThat(memberOnboardings).hasSize(1)
        assertThat(memberOnboardings.values.first()).matches {
            it.status == OnboardingStatus.MEMBER_VERIFICATION_COMPLETED &&
                it.currentStep == OnboardingStep.ONBOARDING_ACTIVATION
        }

        assertThat(result.status).isEqualTo(BulkOnboardingJobStatus.SUCCESS)
        assertThat(result.onboardingResult.onboardedEmployeeCount).isEqualTo(1)
    }

    @Test
    fun `onboard valid hris members`() {
        val companyId = 1000L
        val entityId = 2000L
        val memberId = faker.random.nextLong()
        val contractId = faker.random.nextLong()

        setupValidationMocks()
        setupCreationMocks(memberId = memberId, contractId = contractId)
        every { contractServiceAdapter.getNonDeletedNonEndedContracts(setOf(contractId)) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setType(ContractOuterClass.ContractType.HR_MEMBER)
                    .setCompanyId(companyId)
                    .setLegalEntityId(entityId)
                    .build())
        every { contractServiceAdapter.getNonDeletedNonEndedContracts(emptySet()) } returns
            emptyList()
        every { contractServiceAdapter.getDirectEmployeeOnboardingFlowTypes(any()) } answers
            {
                firstArg<Collection<Long>>().associateWith {
                    ContractOuterClass.DirectEmployeeOnboardingFlowType.HRIS_ONLY
                }
            }
        every { companyServiceAdapter.getCompanyOfferingsByCompanyIds(setOf(companyId)) } returns
            emptyMap()
        every { companyServiceAdapter.getLegalEntitiesByIds(setOf(entityId)) } returns
            listOf(
                CompanyOuterClass.LegalEntity.newBuilder()
                    .setId(entityId)
                    .addCapabilities(Capability.GROSS_TO_NET.name)
                    .build())

        val result =
            callBulkOnboardMutation(
                companyId = companyId,
                entityId = entityId,
                onboardingContext = BulkOnboardingContext.HRIS_PROFILE_DATA)

        val expectedMemberValidationData =
            mapOf(
                "firstName" to "Robert",
                "lastName" to "Oppenheimer",
                "email" to "<EMAIL>",
                "gender" to "UNSPECIFIED",
                "dateOfBirth" to "1999-11-11",
                "nationality" to "Singapore",
                "maritalStatus" to "SINGLE",
                "phoneNumber" to "+65 12334343478",
                "currentAddressLine1" to "Marina Bay",
                "currentAddressCity" to "Singapore",
                "currentAddressCountry" to "Singapore",
                "currentAddressPostalCode" to "12345",
            )

        val expectedContractValidation =
            mapOf(
                "startOn" to "2024-04-01",
                "position" to "Bug architect",
                "employeeId" to "1",
                "contractId" to "",
                "email" to "<EMAIL>",
                "companyId" to companyId.toString(),
                "legalEntityId" to "1593435")

        verify {
            bulkMemberServiceAdapter.validateMemberCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().identification.employeeId).isEqualTo("1")
                    assertThat(it.first().identification.rowNumber).isEqualTo(3)
                    assertThat(it.first().data).containsAllEntriesOf(expectedMemberValidationData)
                },
                any())
        }

        verify {
            bulkMemberServiceAdapter.createMembers(withArg { assertThat(it).hasSize(1) }, any())
        }

        verify {
            bulkContractServiceAdapter.validateContractCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().data).containsAllEntriesOf(expectedContractValidation)
                },
                any())
        }

        verify {
            bulkContractServiceAdapter.createContracts(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                },
                any())
        }

        verify {
            bulkMemberLegalDataServiceAdapter.validateUpdateLegalDataInputs(
                withArg { assertThat(it).hasSize(1) }, any())
        }

        verify {
            bulkMemberLegalDataServiceAdapter.updateLegalData(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                },
                any())
        }

        val expectedOrgManagementValidation =
            mapOf(
                "directManagerEmail" to "<EMAIL>",
                "department" to "123",
                "isManager" to "true",
                "contractId" to "",
                "email" to "<EMAIL>")

        verify {
            bulkOrgManagementDataServiceAdapter.validateUpsertOrgManagementDataInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().data)
                        .containsAllEntriesOf(expectedOrgManagementValidation)
                },
                any())
        }

        verify {
            bulkOrgManagementDataServiceAdapter.upsertOrgManagementData(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().contractId).isEqualTo(contractId)
                    assertThat(it.first().data.email).isEqualTo("<EMAIL>")
                },
                any())
        }

        verify {
            bulkMemberEmergencyDataServiceAdapter.validateUpsertEmergencyContactDataInputs(
                withArg { assertThat(it).hasSize(1) }, any())
        }

        verify {
            bulkMemberEmergencyDataServiceAdapter.upsertEmergencyContactData(
                withArg { assertThat(it).hasSize(1) })
        }

        verify {
            bulkMemberEducationServiceAdapter.validateUpsertEducationDetails(
                withArg { assertThat(it).hasSize(1) }, any())
        }

        verify {
            bulkMemberEducationServiceAdapter.upsertEducationDetails(
                withArg { assertThat(it).hasSize(1) })
        }

        verify {
            bulkMemberEmploymentServiceAdapter.validateUpsertPreviousEmployerDetails(
                withArg { assertThat(it).hasSize(1) }, any())
        }

        verify {
            bulkMemberEmploymentServiceAdapter.upsertPreviousEmployerDetails(
                withArg { assertThat(it).hasSize(1) })
        }

        verify {
            bulkMemberAddressServiceAdapter.validateUpsertAddresses(
                withArg { assertThat(it).hasSize(1) }, any())
        }

        verify {
            bulkMemberAddressServiceAdapter.upsertAddresses(withArg { assertThat(it).hasSize(1) })
        }

        verify {
            notificationService.sendOnboardingCompletedNotification(
                any(),
                withArg { assertThat(it.status).isEqualTo(BulkOnboardingJobStatus.SUCCESS) },
                any())
        }

        val memberOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contractIds = listOf(contractId), experience = "member")

        val companyOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contractIds = listOf(contractId), experience = "company")

        assertThat(companyOnboardings).hasSize(1)
        assertThat(companyOnboardings.values.first()).matches {
            it.status == OnboardingStatus.CREATED_CUSTOM &&
                it.currentStep == OnboardingStep.ONBOARDING_REVIEW
        }

        assertThat(memberOnboardings).hasSize(0)

        assertThat(result.status).isEqualTo(BulkOnboardingJobStatus.SUCCESS)
        assertThat(result.onboardingResult.onboardedEmployeeCount).isEqualTo(1)
    }

    @Test
    fun `onboard valid aor members`() {
        val companyId = 1000L
        val entityId = 1000L
        val memberId = faker.random.nextLong()
        val contractId = faker.random.nextLong()

        setupValidationMocks()
        setupCreationMocks(memberId = memberId, contractId = contractId)

        val result =
            callBulkOnboardMutation(
                companyId = companyId,
                entityId = entityId,
                onboardingContext = BulkOnboardingContext.AOR,
                contractType = ContractType.CONTRACTOR)

        val expectedMemberValidationData =
            mapOf(
                "firstName" to "Robert",
                "lastName" to "Oppenheimer",
                "email" to "<EMAIL>",
                "gender" to "UNSPECIFIED",
            )

        val expectedContractValidation =
            mapOf(
                "workStatus" to "RESIDENT",
                "term" to "FIXED",
                "startOn" to "2024-01-01",
                "position" to "SWE",
                "salary" to "10000.00",
                "employeeId" to "1",
                "contractId" to "",
                "firstName" to "Robert",
                "lastName" to "Oppenheimer",
                "email" to "<EMAIL>",
                "type" to "CONTRACTOR",
                "country" to "CHE",
                "companyId" to companyId.toString())

        verify {
            bulkMemberServiceAdapter.validateMemberCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().identification.employeeId).isEqualTo("1")
                    assertThat(it.first().identification.rowNumber).isEqualTo(3)
                    assertThat(it.first().data).containsAllEntriesOf(expectedMemberValidationData)
                },
                any())
        }

        verify {
            bulkMemberServiceAdapter.createMembers(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.AOR) })
        }

        verify {
            bulkContractServiceAdapter.validateContractCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().data).containsAllEntriesOf(expectedContractValidation)
                },
                any())
        }

        verify {
            bulkContractServiceAdapter.createContracts(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                },
                any())
        }

        verify {
            bulkCompensationServiceAdapter.validateCompensationCreateInputs(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.AOR) })
        }

        verify {
            bulkCompensationServiceAdapter.createCompensations(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().contractId).isEqualTo(contractId)
                },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.AOR) })
        }

        verify {
            bulkComplianceServiceAdapter.validateUpdateComplianceInputs(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.AOR) })
        }

        verify {
            bulkComplianceServiceAdapter.updateCompliances(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.AOR) })
        }

        verify {
            bulkContractServiceAdapter.triggerPostOnboardingActions(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.AOR) })
        }

        verify {
            notificationService.sendOnboardingCompletedNotification(
                any(),
                withArg { assertThat(it.status).isEqualTo(BulkOnboardingJobStatus.SUCCESS) },
                any())
        }

        val memberOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contractIds = listOf(contractId), experience = "member")

        val companyOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contractIds = listOf(contractId), experience = "company")

        assertThat(companyOnboardings).hasSize(1)
        assertThat(companyOnboardings.values.first()).matches {
            it.status == OnboardingStatus.CREATED &&
                it.currentStep == OnboardingStep.ONBOARDING_REVIEW
        }

        assertThat(memberOnboardings).hasSize(0)

        assertThat(result.status).isEqualTo(BulkOnboardingJobStatus.SUCCESS)
        assertThat(result.onboardingResult.onboardedEmployeeCount).isEqualTo(1)
    }

    @Test
    fun `onboard valid eor members`() {
        val companyId = 1000L
        val memberId = faker.random.nextLong()
        val contractId = faker.random.nextLong()

        setupValidationMocks()
        setupCreationMocks(memberId = memberId, contractId = contractId)
        every { contractServiceAdapter.getNonDeletedNonEndedContracts(any()) } returns
            listOf(
                ContractOuterClass.Contract.newBuilder()
                    .setId(contractId)
                    .setType(ContractOuterClass.ContractType.EMPLOYEE)
                    .setCompanyId(companyId)
                    .build())
        every { contractServiceAdapter.getNonDeletedNonEndedContracts(emptySet()) } returns
            emptyList()
        every { companyServiceAdapter.getCompanyOfferingsByCompanyIds(setOf(companyId)) } returns
            emptyMap()
        every { countryServiceAdapter.getCountryStates(any()) } returns emptyMap()

        callBulkOnboardMutation(
            companyId = companyId, onboardingContext = BulkOnboardingContext.EOR)

        val expectedMemberValidationData =
            mapOf(
                "firstName" to "Reven",
                "lastName" to "TikTok",
                "email" to "<EMAIL>",
                "gender" to "MALE",
                "nationality" to "",
                "workStatus" to "RESIDENT",
                "nationalId" to "14151222",
            )

        val expectedContractValidation =
            mapOf(
                "startOn" to "2024-06-29",
                "position" to "Tiktoker",
                "email" to "<EMAIL>",
            )

        verify(exactly = 1) {
            bulkMemberServiceAdapter.validateMemberCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().identification.firstName).isEqualTo("Reven")
                    assertThat(it.first().identification.rowNumber).isEqualTo(3)
                    assertThat(it.first().data).containsAllEntriesOf(expectedMemberValidationData)
                },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
        }

        verify(exactly = 1) {
            bulkMemberServiceAdapter.createMembers(withArg { assertThat(it).hasSize(1) }, any())
        }

        verify(exactly = 1) {
            bulkContractServiceAdapter.validateContractCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().identification.employeeId).isNullOrEmpty()
                    assertThat(it.first().data).containsAllEntriesOf(expectedContractValidation)
                },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
        }

        verify(exactly = 1) {
            bulkContractServiceAdapter.createContracts(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
        }

        verify(exactly = 1) {
            bulkMemberLegalDataServiceAdapter.validateUpdateLegalDataInputs(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
        }

        verify(exactly = 1) {
            bulkMemberLegalDataServiceAdapter.updateLegalData(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().memberId).isEqualTo(memberId)
                },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
        }

        val expectedComplianceData =
            mapOf(
                "probationPolicy.value" to "3",
                "noticeAfterProbation.value" to "3",
                "nonCompete.value" to "3",
                "nonSolicit.value" to "3",
            )

        verify(exactly = 1) {
            bulkComplianceServiceAdapter.validateUpdateComplianceInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().data).containsAllEntriesOf(expectedComplianceData)
                },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
        }

        verify(exactly = 1) {
            bulkComplianceServiceAdapter.updateCompliances(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
        }

        val expectedCompensationData =
            mapOf(
                "currency" to "SGD",
                "basePay" to "500.0",
                "rateFrequency" to "ANNUALLY",
                "payrollFrequency" to "MONTHLY",
                "internetAllowance.amountType" to "FIXED_AMOUNT",
                "internetAllowance.amount" to "100.0",
                "internetAllowance.frequency" to "MONTHLY",
                "internetAllowance.payOn" to "2024-8",
            )

        verify(exactly = 1) {
            bulkCompensationServiceAdapter.validateCompensationCreateInputs(
                withArg {
                    assertThat(it).hasSize(1)
                    assertThat(it.first().data).containsAllEntriesOf(expectedCompensationData)
                },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
        }

        verify(exactly = 1) {
            bulkCompensationServiceAdapter.createCompensations(
                withArg { assertThat(it).hasSize(1) },
                withArg { assertThat(it.context).isEqualTo(BulkOnboardingContext.EOR) })
        }
    }

    @Test
    fun `should return failure when onboarding fails`() {
        val memberId = faker.random.nextLong()
        val contractId = faker.random.nextLong()

        setupValidationMocks()
        setupCreationMocks(memberId = memberId, contractId = contractId, hasCreationError = true)

        val result =
            callBulkOnboardMutation(onboardingContext = BulkOnboardingContext.GLOBAL_PAYROLL)

        assertThat(result.status).isEqualTo(BulkOnboardingJobStatus.FAILED)
        verify {
            notificationService.sendOnboardingCompletedNotification(
                any(),
                withArg { assertThat(it.status).isEqualTo(BulkOnboardingJobStatus.FAILED) },
                any())
        }
    }

    @Test
    fun `should return failure when failed to parse Xlxs file`() {
        val validationResult =
            callBulkValidateMutation(
                query = "/queries/BulkOnboardingValidate.graphql",
                bulkFile = "/bulk/global-payroll/invalid-excel.xlsx",
            )

        assertThat(validationResult.totalEmployeeCount).isEqualTo(0)
        assertThat(validationResult.validEmployeeCount).isEqualTo(0)
        assertThat(validationResult.errorEmployeeCount).isEqualTo(0)
        assertThat(validationResult.errors).isNotEmpty
        assertThat(validationResult.errors.map { it.message })
            .hasSameElementsAs(listOf("No employee data found"))
    }

    private fun callBulkValidateMutation(
        companyId: Long = 1000L,
        entityId: Long = 1000L,
        query: String,
        bulkFile: String,
        permissions: List<String> = listOf("create.operations.contract")
    ): BulkOnboardingValidationResult {
        val response =
            callMutation(
                companyId,
                entityId,
                query,
                bulkFile,
                permissions,
                onboardingContext = BulkOnboardingContext.GLOBAL_PAYROLL)

        return objectMapper.jsonAt<BulkOnboardingValidationResult>(
            "/data/bulkOnboardingValidate", response.body!!)
    }

    private fun BulkOnboardingValidationResult.writeReportToTempDir() {
        val path = "${projectTmpFolder()}/bulk-upload-error-report.xlsx"
        writeFileFromBase64(
            path,
            this.validationResultFile.blob,
        )
    }

    private fun callBulkOnboardMutation(
        companyId: Long = 1000L,
        entityId: Long = 1000L,
        permissions: List<String> = listOf("create.operations.contract"),
        userContext: UserContext? = null,
        onboardingContext: BulkOnboardingContext = BulkOnboardingContext.GLOBAL_PAYROLL,
        contractType: ContractType = ContractType.HR_MEMBER,
        inputFile: String = "/bulk/global-payroll/bulk-upload-1-member.xlsx"
    ): BulkOnboardingJob {
        val bulkFile =
            when (onboardingContext) {
                BulkOnboardingContext.HRIS_PROFILE_DATA ->
                    "/bulk/hris-profile/bulk-upload-1-member.xlsx"
                BulkOnboardingContext.EOR -> "/bulk/eor/eor-upload-1-member.xlsx"
                else -> inputFile
            }
        val response =
            callMutation(
                companyId = companyId,
                entityId = entityId,
                query = "/queries/BulkOnboardingTrigger.graphql",
                bulkFile = bulkFile,
                permissions = permissions,
                userContext = userContext,
                onboardingContext = onboardingContext,
                contractType = contractType,
            )

        return objectMapper.jsonAt<BulkOnboardingJob>(
            "/data/bulkOnboardingTrigger", response.body!!)
    }

    private fun callMutation(
        companyId: Long = 1000L,
        entityId: Long = 1000L,
        query: String,
        bulkFile: String,
        permissions: List<String> = emptyList(),
        userContext: UserContext? = null,
        onboardingContext: BulkOnboardingContext = BulkOnboardingContext.GLOBAL_PAYROLL,
        contractType: ContractType = ContractType.HR_MEMBER,
    ): ResponseEntity<String> {
        return client.graphQLFileUpload<String>(
            variables =
                mapOf(
                    "options" to
                        mapOf<String, Any>(
                            "contractType" to contractType.name,
                            "countryCode" to "CHE",
                            "companyId" to companyId,
                            "entityId" to entityId,
                            "context" to onboardingContext.name)),
            query = query,
            file = getFileFromResources(bulkFile),
            httpConfig = HttpConfig(permissions = permissions, userContext = userContext),
            fileParamName = "employeeDataFile")
    }

    private fun setupValidationMocks(
        config: BulkValidationTestConfig = BulkValidationTestConfig()
    ) {
        mockMemberValidation(
            success = config.memberValidation.success, errors = config.memberValidation.errors)

        mockContractValidation(
            success = config.contractValidation.success, errors = config.contractValidation.errors)

        mockCompensationValidation(
            success = config.compensationValidation.success,
            errors = config.compensationValidation.errors)

        mockCompensationSchemaValidation(
            success = config.compensationValidation.success,
            errors = config.compensationValidation.errors)

        mockLegalDataValidation(
            success = config.legalDataValidation.success,
            errors = config.legalDataValidation.errors)

        mockBankDataValidation(
            success = config.bankDataValidation.success, errors = config.bankDataValidation.errors)

        mockOrgManagementDataValidation(
            success = config.orgManagementDataValidation.success,
            errors = config.orgManagementDataValidation.errors)

        mockMemberEmergencyContactValidation(
            success = config.emergencyDataValidation.success,
            errors = config.emergencyDataValidation.errors)

        mockMemberEducationDetailsValidation(
            success = config.emergencyDataValidation.success,
            errors = config.emergencyDataValidation.errors)

        mockMemberPreviousEmployerDetailsValidation(
            success = config.emergencyDataValidation.success,
            errors = config.emergencyDataValidation.errors)

        mockMemberAddressesValidation(
            success = config.emergencyDataValidation.success,
            errors = config.emergencyDataValidation.errors)

        mockComplianceValidation(
            success = config.complianceDataValidation.success,
            errors = config.complianceDataValidation.errors)
    }

    fun setupCreationMocks(
        memberId: Long = faker.random.nextLong(),
        contractId: Long = faker.random.nextLong(),
        hasCreationError: Boolean = false
    ) {
        every { bulkMemberServiceAdapter.createMembers(any(), any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs
                    .associate { it.requestId to memberId }
                    .map {
                        CreationResult(
                            requestId = it.key, success = true, upsertedIds = listOf(memberId))
                    }
            }

        every { bulkContractServiceAdapter.createContracts(any(), any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs
                    .associate { it.requestId to contractId }
                    .map {
                        CreationResult(
                            requestId = it.key, success = true, upsertedIds = listOf(contractId))
                    }
            }

        every { bulkCompensationServiceAdapter.createCompensations(any(), any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                if (hasCreationError) {
                    inputs.map {
                        CreationResult.error(it.requestId, "Failed to create compensation")
                    }
                } else inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { bulkCompensationSchemaServiceAdapter.createCompensations(any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                if (hasCreationError) {
                    inputs.map {
                        CreationResult.error(it.requestId, "Failed to create compensation")
                    }
                } else inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { bulkMemberLegalDataServiceAdapter.updateLegalData(any(), any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { memberBankDataAdapter.upsertBankData(any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { bulkComplianceServiceAdapter.updateCompliances(any(), any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { bulkOrgManagementDataServiceAdapter.upsertOrgManagementData(any(), any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { bulkMemberEmergencyDataServiceAdapter.upsertEmergencyContactData(any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { bulkMemberEducationServiceAdapter.upsertEducationDetails(any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { bulkMemberEmploymentServiceAdapter.upsertPreviousEmployerDetails(any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { bulkMemberAddressServiceAdapter.upsertAddresses(any()) } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs.map { CreationResult(it.requestId, success = true) }
            }
        every {
            bulkTimeoffServiceAdapter.createDefaultTimeoffEntitlementForContracts(any())
        } answers
            {
                val inputs = firstArg<List<CreationInput<*>>>()
                inputs.map { CreationResult(it.requestId, success = true) }
            }
        every { bulkContractServiceAdapter.triggerPostOnboardingActions(any(), any()) } returns
            emptyList()
    }

    private fun mockBankDataValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every { memberBankDataAdapter.validate(any(), any()) } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input = UpsertBankDetailsInput.newBuilder().build(),
                    )
                }
            }
    }

    private fun mockLegalDataValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every {
            bulkMemberLegalDataServiceAdapter.validateUpdateLegalDataInputs(any(), any())
        } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        input = UpdateMemberLegalDataInput.newBuilder().build(),
                        success = success,
                        errors = errors,
                    )
                }
            }
    }

    private fun mockCompensationValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every {
            bulkCompensationServiceAdapter.validateCompensationCreateInputs(any(), any())
        } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input = CompensationOuterClass.CreateCompensationInput.newBuilder().build(),
                    )
                }
            }
    }

    private fun mockCompensationSchemaValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every {
            bulkCompensationSchemaServiceAdapter.validateCompensationCreateInputs(
                any(), any(), any())
        } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input = emptyMap(),
                    )
                }
            }
    }

    private fun mockContractValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every { bulkContractServiceAdapter.validateContractCreateInputs(any(), any()) } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input = ContractOuterClass.CreateContractInput.newBuilder().build(),
                    )
                }
            }
    }

    private fun mockMemberValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every { bulkMemberServiceAdapter.validateMemberCreateInputs(any(), any()) } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input =
                            MemberCreateInput.newBuilder()
                                .setRequestId(it.identification.validationId)
                                .build(),
                    )
                }
            }
    }

    private fun mockOrgManagementDataValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every {
            bulkOrgManagementDataServiceAdapter.validateUpsertOrgManagementDataInputs(any(), any())
        } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input =
                            BulkAPI.UpsertOrgManagementDataInput.newBuilder()
                                .setIsManager(it.data["isManager"].toBoolean())
                                .setEmail(it.data["email"] as String)
                                .build(),
                    )
                }
            }
    }

    private fun mockMemberEmergencyContactValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every {
            bulkMemberEmergencyDataServiceAdapter.validateUpsertEmergencyContactDataInputs(
                any(), any())
        } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input =
                            UpsertEmergencyContactInput.newBuilder()
                                .setRequestId(it.identification.validationId)
                                .build(),
                    )
                }
            }
    }

    private fun mockMemberEducationDetailsValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every {
            bulkMemberEducationServiceAdapter.validateUpsertEducationDetails(any(), any())
        } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input =
                            UpsertEducationDetailsInput.newBuilder()
                                .setRequestId(it.identification.validationId)
                                .build(),
                    )
                }
            }
    }

    private fun mockMemberPreviousEmployerDetailsValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every {
            bulkMemberEmploymentServiceAdapter.validateUpsertPreviousEmployerDetails(any(), any())
        } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input =
                            UpsertPreviousEmployerDetailsInput.newBuilder()
                                .setRequestId(it.identification.validationId)
                                .build(),
                    )
                }
            }
    }

    private fun mockMemberAddressesValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every { bulkMemberAddressServiceAdapter.validateUpsertAddresses(any(), any()) } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input =
                            UpsertAddressInput.newBuilder()
                                .setRequestId(it.identification.validationId)
                                .build(),
                    )
                }
            }
    }

    private fun mockComplianceValidation(
        success: Boolean,
        errors: List<String>? = null,
    ) {
        every { bulkComplianceServiceAdapter.validateUpdateComplianceInputs(any(), any()) } answers
            {
                val inputs = firstArg<List<EmployeeData>>()
                inputs.map {
                    validationResult(
                        validationId = it.identification.validationId,
                        success = success,
                        errors = errors,
                        input =
                            BulkContract.UpdateComplianceInput.newBuilder()
                                .setRequestId(it.identification.validationId)
                                .build(),
                    )
                }
            }
    }

    private fun <T> validationResult(
        validationId: String,
        input: T,
        success: Boolean = true,
        errors: List<String>? = null,
    ): GrpcValidationResult<T> =
        GrpcValidationResult(
            success = success, errors = errors, validationId = validationId, input = input)
}

data class ValidationMock(
    val success: Boolean,
    val errors: List<String>? = null,
)

data class BulkValidationTestConfig(
    val memberValidation: ValidationMock = ValidationMock(success = true),
    val contractValidation: ValidationMock = ValidationMock(success = true),
    val compensationValidation: ValidationMock = ValidationMock(success = true),
    val legalDataValidation: ValidationMock = ValidationMock(success = true),
    val bankDataValidation: ValidationMock = ValidationMock(success = true),
    val orgManagementDataValidation: ValidationMock = ValidationMock(success = true),
    val emergencyDataValidation: ValidationMock = ValidationMock(success = true),
    val complianceDataValidation: ValidationMock = ValidationMock(success = true),
)
