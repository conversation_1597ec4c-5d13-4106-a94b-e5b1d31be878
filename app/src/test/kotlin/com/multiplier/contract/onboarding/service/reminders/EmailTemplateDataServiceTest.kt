package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.common.exception.MplBusinessException
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryAndState
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.OperationsUserServiceAdapter
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.repository.JpaOnboardingRepository
import com.multiplier.contract.onboarding.repository.model.JpaOnboarding
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import com.multiplier.contract.schema.compensation.CompensationOuterClass.CompensationPayComponent
import com.multiplier.contract.schema.compensation.CompensationOuterClass.PayFrequency
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.CountryWorkStatus
import io.mockk.*
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.*
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
@DisplayNameGeneration(DisplayNameGenerator.ReplaceUnderscores::class)
class EmailTemplateDataServiceTest {

    @MockK lateinit var contractServiceAdapter: ContractServiceAdapter

    @MockK lateinit var memberServiceAdapter: MemberServiceAdapter

    @MockK lateinit var countryServiceAdapter: CountryServiceAdapter

    @MockK lateinit var companyServiceAdapter: CompanyServiceAdapter

    @MockK lateinit var operationsUserServiceAdapter: OperationsUserServiceAdapter

    @MockK lateinit var jpaOnboardingRepository: JpaOnboardingRepository

    @MockK lateinit var featureFlagService: FeatureFlagService

    @InjectMockKs lateinit var underTest: EmailTemplateDataService

    private val onboardingGuideLink = "link"

    @BeforeEach
    fun setup() {
        // Default mock setup for feature flag service
        every { featureFlagService.isFeatureOn(any(), any()) } returns false

        // Specifically mock the new multi-frequency support feature flag
        every {
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_MULTI_FREQUENCY_SUPPORT_REMINDER_CTA, any())
        } returns false

        // Default mock for compensation service
        every { contractServiceAdapter.getCurrentCompensationByContractIds(any()) } returns null

        underTest =
            EmailTemplateDataService(
                contractServiceAdapter,
                memberServiceAdapter,
                countryServiceAdapter,
                companyServiceAdapter,
                operationsUserServiceAdapter,
                jpaOnboardingRepository,
                featureFlagService,
                onboardingGuideLink)
    }

    @Test
    fun should_fail_when_onboarding_not_found() {
        val contract =
            Contract.newBuilder()
                .setId(123L)
                .setCompanyId(101L)
                .setMemberId(1L)
                .setCountry(CountryCode.IND.name)
                .setWorkStatus(CountryWorkStatus.RESIDENT)
                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                .build()

        every { contractServiceAdapter.getContractById(any()) } returns (contract)

        every { jpaOnboardingRepository.findByContractIdAndExperience(any(), any()) } returns
            Optional.empty()

        every { companyServiceAdapter.getCompany(any()) } returns
            mockk(relaxed = true) { every { isTest } returns false }

        every { memberServiceAdapter.getMember(any()) } returns mockk(relaxed = true)

        every { operationsUserServiceAdapter.getOperationsUsersForContracts(any()) } returns
            mockk(relaxed = true)

        val memberDocs = mapOf(1L to setOf("DocKey1", "DocKey2"), 2L to setOf("DocKey3"))

        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns memberDocs
        val exception =
            Assertions.assertThrows(MplBusinessException::class.java) {
                underTest.fetchEmailTemplateData(contract.id)
            }

        Assertions.assertEquals(
            "Onboarding data for company not found for contract ID 123", exception.message)
    }

    @Test
    fun should_fail_when_any_prefetchExternal_call_fails() {
        val contract =
            Contract.newBuilder()
                .setId(123L)
                .setCompanyId(101L)
                .setMemberId(1L)
                .setCountry(CountryCode.IND.name)
                .setWorkStatus(CountryWorkStatus.RESIDENT)
                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                .build()

        every { contractServiceAdapter.getContractById(any()) } returns (contract)

        every { companyServiceAdapter.getCompany(any()) } throws
            IllegalStateException("failure in company svc")

        every { memberServiceAdapter.getMember(any()) } returns mockk(relaxed = true)

        every { operationsUserServiceAdapter.getOperationsUsersForContracts(any()) } returns
            mockk(relaxed = true)

        every {
            countryServiceAdapter.getRequiredDocumentKeysGroupByCountryCode(any(), any())
        } returns
            mapOf(
                CountryAndState(CountryCode.USA, "MH") to setOf("doc1", "doc2"),
                CountryAndState(CountryCode.USA, "CA") to setOf("doc3", "doc4"))

        val memberDocs = mapOf(1L to setOf("DocKey1", "DocKey2"), 2L to setOf("DocKey3"))

        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns memberDocs
        val exception =
            Assertions.assertThrows(MplBusinessException::class.java) {
                underTest.fetchEmailTemplateData(contract.id)
            }

        Assertions.assertEquals("Company data not found", exception.message)
    }

    // ========================================
    // PAY FREQUENCY TESTS
    // ========================================

    @Test
    fun fetchEmailTemplateData_should_include_payFrequency_when_compensation_available() {
        // Given
        val contract =
            Contract.newBuilder()
                .setId(123L)
                .setCompanyId(101L)
                .setMemberId(1L)
                .setCountry(CountryCode.IND.name)
                .setWorkStatus(CountryWorkStatus.RESIDENT)
                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                .build()

        every { contractServiceAdapter.getContractById(any()) } returns contract
        every { jpaOnboardingRepository.findByContractIdAndExperience(any(), any()) } returns
            Optional.of(
                mockk<JpaOnboarding>(relaxed = true) {
                    every { contractId } returns 123L
                    every { experience } returns "company"
                })
        every { companyServiceAdapter.getCompany(any()) } returns
            mockk(relaxed = true) { every { isTest } returns false }
        every { memberServiceAdapter.getMember(any()) } returns mockk(relaxed = true)
        every { operationsUserServiceAdapter.getOperationsUsersForContracts(any()) } returns
            mapOf(123L to mockk(relaxed = true))
        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns emptyMap()
        every { countryServiceAdapter.getCountryNameByCode(any()) } returns "India"

        // Create compensation with MONTHLY payFrequency
        val compensation =
            mockk<Compensation> {
                every { basePay } returns
                    mockk<CompensationPayComponent> {
                        every { payFrequency } returns PayFrequency.PAY_FREQUENCY_MONTHLY
                    }
            }
        every { contractServiceAdapter.getCurrentCompensationByContractIds(123L) } returns
            compensation

        // When
        val result = underTest.fetchEmailTemplateData(123L)

        // Then
        assertThat(result).isNotNull()
        assertThat(result.payFrequency).isEqualTo("MONTHLY")
        verify { contractServiceAdapter.getCurrentCompensationByContractIds(123L) }
    }

    @Test
    fun fetchEmailTemplateData_should_have_null_payFrequency_when_compensation_not_available() {
        // Given
        val contract =
            Contract.newBuilder()
                .setId(456L)
                .setCompanyId(102L)
                .setMemberId(2L)
                .setCountry(CountryCode.USA.name)
                .setWorkStatus(CountryWorkStatus.RESIDENT)
                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                .build()

        every { contractServiceAdapter.getContractById(any()) } returns contract
        every { jpaOnboardingRepository.findByContractIdAndExperience(any(), any()) } returns
            Optional.of(
                mockk<JpaOnboarding>(relaxed = true) {
                    every { contractId } returns 456L
                    every { experience } returns "company"
                })
        every { companyServiceAdapter.getCompany(any()) } returns
            mockk(relaxed = true) { every { isTest } returns false }
        every { memberServiceAdapter.getMember(any()) } returns mockk(relaxed = true)
        every { operationsUserServiceAdapter.getOperationsUsersForContracts(any()) } returns
            mapOf(456L to mockk(relaxed = true))
        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns emptyMap()
        every { countryServiceAdapter.getCountryNameByCode(any()) } returns "United States"

        // No compensation available (using default mock from setup which returns null)
        every { contractServiceAdapter.getCurrentCompensationByContractIds(456L) } returns null

        // When
        val result = underTest.fetchEmailTemplateData(456L)

        // Then
        assertThat(result).isNotNull()
        assertThat(result.payFrequency).isNull()
        verify { contractServiceAdapter.getCurrentCompensationByContractIds(456L) }
    }

    @Test
    fun fetchEmailTemplateData_should_handle_weekly_pay_frequency() {
        // Given
        val contract =
            Contract.newBuilder()
                .setId(789L)
                .setCompanyId(103L)
                .setMemberId(3L)
                .setCountry(CountryCode.SGP.name)
                .setWorkStatus(CountryWorkStatus.RESIDENT)
                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                .build()

        every { contractServiceAdapter.getContractById(any()) } returns contract
        every { jpaOnboardingRepository.findByContractIdAndExperience(any(), any()) } returns
            Optional.of(
                mockk<JpaOnboarding>(relaxed = true) {
                    every { contractId } returns 789L
                    every { experience } returns "company"
                })
        every { companyServiceAdapter.getCompany(any()) } returns
            mockk(relaxed = true) { every { isTest } returns false }
        every { memberServiceAdapter.getMember(any()) } returns mockk(relaxed = true)
        every { operationsUserServiceAdapter.getOperationsUsersForContracts(any()) } returns
            mapOf(789L to mockk(relaxed = true))
        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns emptyMap()
        every { countryServiceAdapter.getCountryNameByCode(any()) } returns "Singapore"

        // Create compensation with WEEKLY payFrequency
        val compensation =
            mockk<Compensation> {
                every { basePay } returns
                    mockk<CompensationPayComponent> {
                        every { payFrequency } returns PayFrequency.PAY_FREQUENCY_WEEKLY
                    }
            }
        every { contractServiceAdapter.getCurrentCompensationByContractIds(789L) } returns
            compensation

        // When
        val result = underTest.fetchEmailTemplateData(789L)

        // Then
        assertThat(result).isNotNull()
        assertThat(result.payFrequency).isEqualTo("WEEKLY")
        verify { contractServiceAdapter.getCurrentCompensationByContractIds(789L) }
    }

    @Test
    fun fetchEmailTemplateData_should_handle_compensation_with_null_basePay() {
        // Given
        val contract =
            Contract.newBuilder()
                .setId(999L)
                .setCompanyId(104L)
                .setMemberId(4L)
                .setCountry(CountryCode.GBR.name)
                .setWorkStatus(CountryWorkStatus.RESIDENT)
                .setType(ContractOuterClass.ContractType.EMPLOYEE)
                .build()

        every { contractServiceAdapter.getContractById(any()) } returns contract
        every { jpaOnboardingRepository.findByContractIdAndExperience(any(), any()) } returns
            Optional.of(
                mockk<JpaOnboarding>(relaxed = true) {
                    every { contractId } returns 999L
                    every { experience } returns "company"
                })
        every { companyServiceAdapter.getCompany(any()) } returns
            mockk(relaxed = true) { every { isTest } returns false }
        every { memberServiceAdapter.getMember(any()) } returns mockk(relaxed = true)
        every { operationsUserServiceAdapter.getOperationsUsersForContracts(any()) } returns
            mapOf(999L to mockk(relaxed = true))
        every { memberServiceAdapter.getLegalDocumentsKeyByMemberIds(any()) } returns emptyMap()
        every { countryServiceAdapter.getCountryNameByCode(any()) } returns "United Kingdom"

        // Create compensation without basePay
        val compensation = mockk<Compensation> { every { basePay } returns null }
        every { contractServiceAdapter.getCurrentCompensationByContractIds(999L) } returns
            compensation

        // When
        val result = underTest.fetchEmailTemplateData(999L)

        // Then
        assertThat(result).isNotNull()
        assertThat(result.payFrequency).isNull()
        verify { contractServiceAdapter.getCurrentCompensationByContractIds(999L) }
    }
}
