package com.multiplier.contract.onboarding.graphql.extensions

import com.multiplier.contract.onboarding.types.Urgency
import java.time.LocalDate
import org.junit.jupiter.api.Assertions.assertAll
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ActivationCutoffDataExtensionTest {
    @Nested
    inner class ToActivationCutoffData {

        @Test
        fun returns_correct_data() {
            val cutoffDate = LocalDate.parse("2023-05-10")
            val checkingDate = LocalDate.parse("2023-05-01")
            val activationCutoffData = cutoffDate.toActivationCutoffData(checkingDate)

            assertAll({
                assertEquals(cutoffDate, activationCutoffData.date)
                assertEquals(Urgency.URGENT, activationCutoffData.urgency)
            })
        }

        @Test
        fun returns_default_for_null() {
            val cutoffDate = null
            val checkingDate = LocalDate.parse("2023-05-01")
            val activationCutoffData = cutoffDate.toActivationCutoffData(checkingDate)

            assertAll({
                assertEquals(null, activationCutoffData.date)
                assertEquals(Urgency.OTHER, activationCutoffData.urgency)
            })
        }
    }

    @Test
    fun getUrgency() {
        val checkingDate = LocalDate.parse("2023-05-01")
        val otherDay = checkingDate.plusDays(11)
        val delayedDay = checkingDate.minusDays(1)
        val urgentDay = checkingDate.plusDays(10)

        assertAll({
            assertEquals(Urgency.OTHER, getUrgency(checkingDate, otherDay))
            assertEquals(Urgency.DELAYED, getUrgency(checkingDate, delayedDay))
            assertEquals(Urgency.URGENT, getUrgency(checkingDate, checkingDate))
            assertEquals(Urgency.URGENT, getUrgency(checkingDate, urgentDay))
        })
    }
}
