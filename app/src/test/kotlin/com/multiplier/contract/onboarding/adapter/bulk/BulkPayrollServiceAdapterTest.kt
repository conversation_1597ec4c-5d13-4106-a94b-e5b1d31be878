package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.types.CountryCode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class BulkPayrollServiceAdapterTest {

    @Nested
    inner class GetAllowanceStructure {

        @ParameterizedTest
        @CsvSource("SGP", "MYS", "PHL", "IDN", "THA", "VNM", "USA", "GBR", "KOR")
        fun `can get configured allowances from CSV file`(countryCode: CountryCode) {
            val allowances = BulkPayrollServiceAdapter().getAllowanceStructure(countryCode)

            assertThat(allowances).isNotEmpty()
        }

        @Test
        fun `return default allowances if no allowances are configured`() {
            val allowances = BulkPayrollServiceAdapter().getAllowanceStructure(CountryCode.ZMB)

            assertThat(allowances.map { it.name })
                .hasSameElementsAs(listOf("internetAllowance", "phoneAllowance", "otherAllowance"))
        }
    }
}
