package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.CONTRACTOR_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmployeeIdSpec
import com.multiplier.contract.onboarding.service.bulk.v2.module.*
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.schema.contract.ContractOuterClass.CreateContractInput
import com.multiplier.core.schema.contract.Contract.ContractTerm
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class FreelancerBulkOnboardingUseCaseTest {

    @MockK private lateinit var bulkContractModule: BulkContractModule
    @MockK private lateinit var bulkMemberModule: BulkMemberModule
    @MockK private lateinit var bulkContractOnboardingModule: BulkContractOnboardingModule
    @MockK private lateinit var bulkCompensationModule: BulkCompensationModuleV2
    @MockK private lateinit var countryCache: CountryCache

    @InjectMockKs private lateinit var underTest: FreelancerBulkOnboardingUseCase

    private val companyId = 1L
    private val options =
        BulkOnboardingOptions.newBuilder()
            .context(BulkOnboardingContext.FREELANCER)
            .companyId(companyId)
            .entityId(null)
            .build()

    @BeforeEach
    fun setUp() {
        every { bulkContractModule.getDataSpecs(any(), any()) } returns listOf(EmployeeIdSpec)
        every { bulkMemberModule.getDataSpecs(any(), any()) } returns listOf()
        every { bulkContractOnboardingModule.getDataSpecs(any(), any()) } returns listOf()
        every { bulkCompensationModule.getDataSpecs(any(), any()) } returns listOf()
        every { countryCache.getCountryCode(any()) } returns CountryCode.IND
    }

    @Test
    fun `should verify that group is added for FREELANCER`() {
        val dataSpecs = underTest.getDataSpecs(options, null)
        assertTrue(dataSpecs.all { it.group == CONTRACTOR_DETAILS_GROUP })
    }

    @Test
    fun `should remove contractId and add type, term to input data before validating in order to enrich input`() {
        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification = EmployeeIdentification(rowNumber = 1),
                            data =
                                mapOf(
                                    "contractId" to "123",
                                    "email" to "<EMAIL>",
                                    "country" to "INDIA"))))
        setUpEmptyValidation()

        underTest.validate(input, options)
        verify {
            bulkContractModule.validate(
                withArg { argument ->
                    assertThat(argument).allMatch { employeeData ->
                        !employeeData.data.containsKey("contractId")
                        employeeData.data["term"].equals(ContractTerm.FIXED.name)
                        employeeData.data["type"].equals(ContractType.FREELANCER.name)
                        employeeData.data["country"].equals("IND")
                    }
                },
                any(),
                any())
        }
    }

    @Test
    fun `should take country code if not found in cache`() {
        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification = EmployeeIdentification(rowNumber = 1),
                            data =
                                mapOf(
                                    "contractId" to "123",
                                    "email" to "<EMAIL>",
                                    "country" to "India"))))
        setUpEmptyValidation()

        every { countryCache.getCountryCode(any()) } returns null

        underTest.validate(input, options)
        verify {
            bulkContractModule.validate(
                withArg { argument ->
                    assertThat(argument).allMatch { employeeData ->
                        !employeeData.data.containsKey("contractId")
                        employeeData.data["term"].equals(ContractTerm.FIXED.name)
                        employeeData.data["type"].equals(ContractType.CONTRACTOR.name)
                        employeeData.data["country"].equals("India")
                    }
                },
                any(),
                any())
        }
    }

    @Test
    fun `should add group to validate response`() {
        val input =
            EmployeeDataInput(
                employeeData =
                    listOf(
                        EmployeeData(
                            identification = EmployeeIdentification(rowNumber = 1),
                            data = mapOf("contractId" to "123", "email" to "<EMAIL>"))))
        setUpResponseInValidate()

        val result = underTest.validate(input, options)
        assertThat(result.validationResults).allSatisfy { _, validationResults ->
            assertThat(validationResults).allMatch { it.groupName == CONTRACTOR_DETAILS_GROUP }
        }
    }

    private fun setUpEmptyValidation() {
        every { bulkMemberModule.identifier() } returns BulkMemberModule.MODULE_NAME
        every { bulkContractModule.identifier() } returns BulkContractModule.MODULE_NAME
        every { bulkContractOnboardingModule.identifier() } returns
            BulkContractOnboardingModule.MODULE_NAME
        every { bulkCompensationModule.identifier() } returns BulkCompensationModuleV2.MODULE_NAME

        every { bulkContractModule.validate(any(), any(), any()) } answers
            {
                println(invocation.args[0])
                emptyList()
            }
        every { bulkMemberModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkContractOnboardingModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkCompensationModule.validate(any(), any(), any()) } returns emptyList()
    }

    private fun setUpResponseInValidate() {
        every { bulkMemberModule.identifier() } returns BulkMemberModule.MODULE_NAME
        every { bulkContractModule.identifier() } returns BulkContractModule.MODULE_NAME
        every { bulkContractOnboardingModule.identifier() } returns
            BulkContractOnboardingModule.MODULE_NAME
        every { bulkCompensationModule.identifier() } returns BulkCompensationModuleV2.MODULE_NAME

        every { bulkContractModule.validate(any(), any(), any()) } answers
            {
                println(invocation.args[0])
                listOf(
                    GrpcValidationResult(
                        success = true,
                        errors = emptyList(),
                        validationId = "rn_3",
                        input = CreateContractInput.getDefaultInstance()),
                    GrpcValidationResult(
                        success = false,
                        errors = listOf("error1", "error2"),
                        validationId = "rn_4",
                        input = CreateContractInput.getDefaultInstance()))
            }
        every { bulkMemberModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkContractOnboardingModule.validate(any(), any(), any()) } returns emptyList()
        every { bulkCompensationModule.validate(any(), any(), any()) } returns emptyList()
    }
}
