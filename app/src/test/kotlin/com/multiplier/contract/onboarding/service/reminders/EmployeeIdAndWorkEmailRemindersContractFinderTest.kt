package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.OnboardingServiceAdapter
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.grpc.common.toDate
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension

@ExtendWith(SpringExtension::class)
class EmployeeIdAndWorkEmailRemindersContractFinderTest {

    @MockK private lateinit var contractServiceAdapter: ContractServiceAdapter
    @MockK private lateinit var featureFlagService: FeatureFlagService
    @MockK private lateinit var onboardingServiceAdapter: OnboardingServiceAdapter

    @InjectMockKs private lateinit var contractFinder: EmployeeIdAndWorkEmailRemindersContractFinder

    @Nested
    inner class FindContractForEmployeeIdAndWorkEmailReminderOnContractActivation {

        @BeforeEach
        fun beforeEach() {
            every {
                featureFlagService.isFeatureOn(FeatureFlags.EMPLOYEE_ID_WORK_EMAIL_REMINDERS, any())
            } returns true
        }

        @Test
        fun `should not return contract which already has employee id and work email filled`() {
            every { contractServiceAdapter.getContractById(any()) } returns
                Contract.newBuilder()
                    .setId(1)
                    .setEmployeeId("123")
                    .setWorkEmail("<EMAIL>")
                    .build()

            val result =
                contractFinder.findContractForEmployeeIdAndWorkEmailReminderOnContractActivation(1L)

            assertTrue(result.isEmpty())
        }

        @Test
        fun `should not return contract which has start date in the future`() {
            every { contractServiceAdapter.getContractById(any()) } returns
                Contract.newBuilder()
                    .setId(1)
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setStartOn(LocalDate.now().plusDays(1).toDate())
                    .build()

            val result =
                contractFinder.findContractForEmployeeIdAndWorkEmailReminderOnContractActivation(1L)

            assertTrue(result.isEmpty())
        }

        @Test
        fun `should not return contract which doesn't have reminders enabled via feature flag`() {
            every { contractServiceAdapter.getContractById(any()) } returns
                Contract.newBuilder()
                    .setId(1)
                    .setEmployeeId("")
                    .setWorkEmail("<EMAIL>")
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setStartOn(LocalDate.now().toDate())
                    .build()
            every {
                featureFlagService.isFeatureOn(FeatureFlags.EMPLOYEE_ID_WORK_EMAIL_REMINDERS, any())
            } returns false

            val result =
                contractFinder.findContractForEmployeeIdAndWorkEmailReminderOnContractActivation(1L)

            assertTrue(result.isEmpty())
        }

        @Test
        fun `should return list of contracts where employee id is missing`() {
            every { contractServiceAdapter.getContractById(any()) } returns
                Contract.newBuilder()
                    .setId(1)
                    .setEmployeeId("")
                    .setWorkEmail("<EMAIL>")
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setStartOn(LocalDate.now().toDate())
                    .build()
            val result =
                contractFinder.findContractForEmployeeIdAndWorkEmailReminderOnContractActivation(1L)

            assertThat(result.size).isEqualTo(1)
            assertTrue(
                result.all { it.employeeId.isEmpty() || it.workEmail.isEmpty() },
                "All returned contracts should have missing employee IDs or emails")
        }

        @Test
        fun `should return list of contracts where work email is missing`() {
            every { contractServiceAdapter.getContractById(any()) } returns
                Contract.newBuilder()
                    .setId(1)
                    .setEmployeeId("123")
                    .setWorkEmail("")
                    .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                    .setStartOn(LocalDate.now().toDate())
                    .build()
            val result =
                contractFinder.findContractForEmployeeIdAndWorkEmailReminderOnContractActivation(1L)

            assertThat(result.size).isEqualTo(1)
            assertTrue(
                result.all { it.employeeId.isEmpty() || it.workEmail.isEmpty() },
                "All returned contracts should have missing employee IDs or emails")
        }
    }

    @Nested
    inner class FindContractsForEmployeeIDAndWorkEmailReminder {

        @BeforeEach
        fun beforeEach() {
            every { contractServiceAdapter.getContractsBy(any()) } returns
                listOf(
                    Contract.newBuilder()
                        .setId(1)
                        .setCompanyId(11)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                        .setEmployeeId("123")
                        .setWorkEmail("<EMAIL>")
                        .build(),
                    Contract.newBuilder()
                        .setId(2)
                        .setCompanyId(11)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                        .setEmployeeId("456")
                        .build(),
                    Contract.newBuilder()
                        .setId(3)
                        .setCompanyId(11)
                        .setType(ContractOuterClass.ContractType.HR_MEMBER)
                        .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                        .setWorkEmail("<EMAIL>")
                        .build(),
                    Contract.newBuilder()
                        .setId(4)
                        .setCompanyId(11)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                        .build(),
                    Contract.newBuilder()
                        .setId(5)
                        .setCompanyId(11)
                        .setType(ContractOuterClass.ContractType.HR_MEMBER)
                        .setStatus(ContractOuterClass.ContractStatus.ONBOARDING)
                        .build(),
                    Contract.newBuilder()
                        .setId(6)
                        .setCompanyId(22)
                        .setType(ContractOuterClass.ContractType.EMPLOYEE)
                        .setStatus(ContractOuterClass.ContractStatus.ACTIVE)
                        .build(),
                )

            every { onboardingServiceAdapter.getAllOnboardingsByContractIds(any(), any()) } returns
                mapOf(
                    3L to
                        Onboarding(
                            contractId = 3,
                            experience = "",
                            status = OnboardingStatus.MEMBER_VERIFICATION_COMPLETED),
                    5L to
                        Onboarding(
                            contractId = 5,
                            experience = "",
                            status = OnboardingStatus.CREATED_CUSTOM),
                )

            every {
                featureFlagService.isFeatureOn(FeatureFlags.EMPLOYEE_ID_WORK_EMAIL_REMINDERS, any())
            } returns true
        }

        @Test
        fun `should return empty list when no contracts found`() {
            every { contractServiceAdapter.getContractsBy(any()) } returns emptyList()

            val result =
                contractFinder.findContractsForEmployeeIdAndWorkEmailReminders(
                    LocalDate.now(), listOf(1L, 2L))

            assertTrue(result.isEmpty(), "Expected no contracts to be returned")
        }

        @Test
        fun `should return empty list when contracts do not meet the criteria`() {
            val contracts =
                listOf(
                    Contract.newBuilder()
                        .setId(1)
                        .setEmployeeId("123")
                        .setWorkEmail("<EMAIL>")
                        .build(),
                    Contract.newBuilder()
                        .setId(2)
                        .setEmployeeId("456")
                        .setWorkEmail("<EMAIL>")
                        .build())
            every { contractServiceAdapter.getContractsBy(any()) } returns contracts

            val result =
                contractFinder.findContractsForEmployeeIdAndWorkEmailReminders(
                    LocalDate.now(), listOf(1L, 2L))

            assertTrue(
                result.isEmpty(),
                "Expected no contracts to be returned because all have IDs and emails")
        }

        @Test
        fun `should return empty list when reminder feature flag is not enabled`() {
            every {
                featureFlagService.isFeatureOn(FeatureFlags.EMPLOYEE_ID_WORK_EMAIL_REMINDERS, any())
            } returns false

            val result =
                contractFinder.findContractsForEmployeeIdAndWorkEmailReminders(
                    LocalDate.now(), listOf(11L, 22L))

            assertTrue(result.isEmpty(), "Expected no contracts to be returned")
        }

        @Test
        fun `should only return contracts which have empty employee id or work email`() {
            val contracts =
                contractFinder.findContractsForEmployeeIdAndWorkEmailReminders(
                    LocalDate.now(), listOf(11L, 22L))

            assertThat(contracts).allSatisfy {
                assertThat(it.employeeId.isNullOrBlank() || it.workEmail.isNullOrBlank()).isTrue()
            }
        }

        @Test
        fun `should return ACTIVE contracts`() {
            val contracts =
                contractFinder.findContractsForEmployeeIdAndWorkEmailReminders(
                    LocalDate.now(), listOf(11L, 22L))

            assertThat(contracts).anyMatch { it.status == ContractOuterClass.ContractStatus.ACTIVE }
        }

        @Test
        fun `should not return non-ACTIVE EOR contracts`() {
            val contracts =
                contractFinder.findContractsForEmployeeIdAndWorkEmailReminders(
                    LocalDate.now(), listOf(11L, 22L))

            assertThat(contracts).noneMatch {
                it.type == ContractOuterClass.ContractType.EMPLOYEE &&
                    it.status != ContractOuterClass.ContractStatus.ACTIVE
            }
        }

        @Test
        fun `when contract is still in onboarding status, only return HR_MEMBER contract with onboarding status equal to MEMBER_VERIFICATION_COMPLETED`() {
            val contracts =
                contractFinder.findContractsForEmployeeIdAndWorkEmailReminders(
                    LocalDate.now(), listOf(11L, 22L))

            assertThat(contracts).anyMatch { it.id == 3L }
            assertThat(contracts).noneMatch { it.id == 5L }
        }
    }
}
