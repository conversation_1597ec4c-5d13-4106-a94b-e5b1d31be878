package com.multiplier.contract.onboarding.adapter.bulk

import com.google.type.Date
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.BulkUpsertEmergencyContactResult
import com.multiplier.member.schema.BulkUpsertPreviousEmployerDetailsResponse
import com.multiplier.member.schema.BulkValidateRequest
import com.multiplier.member.schema.MemberPreviousEmployerDetail
import com.multiplier.member.schema.MemberPreviousEmployerDetails
import com.multiplier.member.schema.UpsertPreviousEmployerDetailsInput
import com.multiplier.member.schema.UpsertPreviousEmployerDetailsValidationResult
import com.multiplier.member.schema.ValidateUpsertPreviousEmployerDetailsResponse
import com.multiplier.member.schema.ValidationInput
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.junit.jupiter.api.extension.ExtendWith

@ExtendWith(MockKExtension::class)
class BulkMemberEmploymentServiceAdapterTest {

    @MockK private lateinit var bulkMemberServiceMock: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    @InjectMockKs
    private lateinit var bulkMemberEmploymentServiceAdapter: BulkMemberEmploymentServiceAdapter

    @Test
    fun `should return correct validation result when validate previous employer details for upsert`() {
        val employeeData =
            listOf(
                EmployeeData(
                    EmployeeIdentification(rowNumber = 2), mapOf("lastEmployerName" to "Google")))
        val options =
            BulkOnboardingOptions.newBuilder()
                .context(BulkOnboardingContext.HRIS_PROFILE_DATA)
                .build()

        val grpcRequest =
            BulkValidateRequest.newBuilder()
                .setContext(options.context.name)
                .addAllInputs(employeeData.map { it.toGrpc() })
                .build()
        val grpcResponse =
            ValidateUpsertPreviousEmployerDetailsResponse.newBuilder()
                .addAllValidationResults(
                    listOf(
                        UpsertPreviousEmployerDetailsValidationResult.newBuilder()
                            .setSuccess(true)
                            .addAllErrors(emptyList())
                            .setRequestId("2")
                            .setValidatedInput(
                                UpsertPreviousEmployerDetailsInput.newBuilder()
                                    .setRequestId("2")
                                    .build())
                            .build()))
                .build()

        every { bulkMemberServiceMock.validateUpsertPreviousEmployerDetails(grpcRequest) } returns
            grpcResponse

        val expected =
            listOf(
                GrpcValidationResult(
                    success = true,
                    errors = emptyList(),
                    validationId = "2",
                    input =
                        UpsertPreviousEmployerDetailsInput.newBuilder().setRequestId("2").build()))
        assertEquals(
            expected,
            bulkMemberEmploymentServiceAdapter.validateUpsertPreviousEmployerDetails(
                employeeData, options))
    }

    @Nested
    inner class UpsertPreviousEmployerDetails {
        private val inputs =
            listOf(
                CreationInput(
                    requestId = "2",
                    memberId = 1,
                    data =
                        UpsertPreviousEmployerDetailsInput.newBuilder()
                            .setPreviousEmployerDetails(
                                MemberPreviousEmployerDetails.newBuilder()
                                    .addAllPreviousEmployerDetails(
                                        listOf(
                                            MemberPreviousEmployerDetail.newBuilder()
                                                .setEmployerName("Google")
                                                .setDesignation("Senior Software Engineer")
                                                .setStartDate(
                                                    Date.newBuilder()
                                                        .setYear(2020)
                                                        .setMonth(1)
                                                        .setDay(1))
                                                .setEndDate(
                                                    Date.newBuilder()
                                                        .setYear(2023)
                                                        .setMonth(12)
                                                        .setDay(31))
                                                .build()))
                                    .build())
                            .build()))

        @Test
        fun `should empty error list when upsert previous employer details successfully`() {
            val grpcResponse =
                BulkUpsertPreviousEmployerDetailsResponse.newBuilder()
                    .addAllResults(
                        listOf(
                            BulkUpsertEmergencyContactResult.newBuilder().setSuccess(true).build()))
                    .build()
            every { bulkMemberServiceMock.bulkUpsertPreviousEmployerDetails(any()) } returns
                grpcResponse
            assertEquals(
                emptyList<String>(),
                bulkMemberEmploymentServiceAdapter.upsertPreviousEmployerDetails(inputs).flatMap {
                    it.errors
                })
            verify {
                bulkMemberServiceMock.bulkUpsertPreviousEmployerDetails(
                    withArg {
                        val employerInput = it.inputsList.first()
                        assertAll(
                            { assertThat(it.inputsList).hasSize(1) },
                            { assertThat(employerInput.requestId).isEqualTo("2") },
                            {
                                assertThat(employerInput.previousEmployerDetails.memberId)
                                    .isEqualTo(1)
                            },
                            {
                                assertThat(
                                        employerInput.previousEmployerDetails
                                            .previousEmployerDetailsList
                                            .first()
                                            .employerName)
                                    .isEqualTo("Google")
                            },
                        )
                    })
            }
        }

        @Test
        fun `should return empty error list when upsert previous employer details with empty parameter`() {
            assertEquals(
                emptyList<String>(),
                bulkMemberEmploymentServiceAdapter.upsertPreviousEmployerDetails(emptyList()))
        }

        @Test
        fun `should return error list when upsert previous employer details`() {
            val errorMessages = listOf("Some error")
            val grpcResponse =
                BulkUpsertPreviousEmployerDetailsResponse.newBuilder()
                    .addAllResults(
                        listOf(
                            BulkUpsertEmergencyContactResult.newBuilder()
                                .setSuccess(false)
                                .addAllErrors(errorMessages)
                                .build()))
                    .build()
            every { bulkMemberServiceMock.bulkUpsertPreviousEmployerDetails(any()) } returns
                grpcResponse
            assertEquals(
                errorMessages,
                bulkMemberEmploymentServiceAdapter.upsertPreviousEmployerDetails(inputs).flatMap {
                    it.errors
                })
        }

        @Test
        fun `should return error list when upsert previous employer details with error occurs`() {
            every { bulkMemberServiceMock.bulkUpsertPreviousEmployerDetails(any()) } throws
                IllegalArgumentException()

            val expected =
                listOf(
                    "Upsert previous employer details failed due to an internal error: unknown exception occurred")
            assertEquals(
                expected,
                bulkMemberEmploymentServiceAdapter.upsertPreviousEmployerDetails(inputs).flatMap {
                    it.errors
                })
        }
    }
}

private fun EmployeeData.toGrpc(): ValidationInput =
    ValidationInput.newBuilder()
        .setRequestId(this.identification.validationId)
        .putAllProperties(this.data)
        .build()
