package com.multiplier.contract.onboarding.service.bulk

import com.multiplier.contract.onboarding.ContractOnboardingApplication
import com.multiplier.contract.onboarding.config.objectMapper
import com.multiplier.contract.onboarding.graphql.BulkTemplateDataRow
import com.multiplier.contract.onboarding.service.bulk.dataSpec.DataSpecForEORService.Companion.RowIdentifierSpec
import com.multiplier.contract.onboarding.service.bulk.excel.parseXlsx
import com.multiplier.contract.onboarding.service.jsonAt
import com.multiplier.contract.onboarding.testing.GraphQLTestClient
import com.multiplier.contract.onboarding.testing.HttpClient
import com.multiplier.contract.onboarding.testing.HttpConfig
import com.multiplier.contract.onboarding.testing.UserContext
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingJob
import com.multiplier.contract.onboarding.types.DocumentReadable
import com.multiplier.contract.onboarding.utils.base64StringToByteArray
import com.multiplier.contract.onboarding.utils.getFileFromResources
import com.multiplier.contract.onboarding.utils.projectTmpFolder
import com.multiplier.contract.onboarding.utils.writeFileFromBase64
import com.netflix.graphql.dgs.autoconfig.DgsAutoConfiguration
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.web.server.LocalServerPort
import org.springframework.test.context.ActiveProfiles

@ActiveProfiles("context-test")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes = [ContractOnboardingApplication::class, DgsAutoConfiguration::class])
@Disabled
class BulkOnboardingEORServiceTest(@LocalServerPort private val port: Int) {

    private val graphQLClient = GraphQLTestClient(port)
    private val httpClient = HttpClient(port)

    @Test
    fun `should generate EOR bulk upload template`() {
        val response =
            graphQLClient.execute(
                config =
                    HttpConfig(
                        permissions = listOf("download.company.contract.onboarding-template"),
                        userContext = UserContext(companyId = 508580)),
                variables =
                    mapOf(
                        "options" to
                            mapOf(
                                "contractType" to "EMPLOYEE",
                                "countryCode" to "SGP",
                                "companyId" to 508580,
                                "entityId" to 5431,
                                "context" to BulkOnboardingContext.EOR)),
                query = "/queries/BulkOnboardingTemplateDownload.graphql",
            )

        val document =
            response.extractValueAsObject(
                "bulkOnboardingTemplateDownload", DocumentReadable::class.java)

        val rows = base64StringToByteArray(document.blob).parseXlsx<BulkTemplateDataRow>()
        writeFileFromBase64("${projectTmpFolder()}/download_eor.xlsx", document.blob)

        Assertions.assertThat(rows.first().valueAtColumnZero).isEqualTo("Field")
        Assertions.assertThat(rows.first().valueAtColumnOne).isEqualTo(RowIdentifierSpec.label)
        Assertions.assertThat(rows.first().valueAtColumnTwo).isEqualTo("Contract Id")
    }

    @Test
    fun `should update data when upload eor template`() {
        val uploadFile = getFileFromResources("/bulk/eor/download_eor.xlsx")
        val response =
            httpClient.graphQLFileUpload<String>(
                httpConfig =
                    HttpConfig(
                        permissions = listOf("create.operations.contract"),
                        userContext = UserContext(companyId = 508580)),
                query = "/queries/BulkOnboardingTrigger.graphql",
                variables =
                    mapOf(
                        "options" to
                            mapOf<String, Any>(
                                "contractType" to "EMPLOYEE",
                                "countryCode" to "SGP",
                                "companyId" to 508580,
                                "entityId" to 5431,
                                "context" to BulkOnboardingContext.EOR)),
                file = uploadFile,
                fileParamName = "employeeDataFile")

        val validation =
            objectMapper.jsonAt<BulkOnboardingJob>("/data/bulkOnboardingTrigger", response.body!!)
        //        writeFileFromBase64(
        //            "${projectTmpFolder()}/bulk-upload-eor-report.xlsx",
        //            validation.blob,
        //        )
        Assertions.assertThat(validation).isNotNull()
    }
}
