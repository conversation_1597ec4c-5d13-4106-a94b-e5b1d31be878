package com.multiplier.integration.service

import com.google.protobuf.Struct
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.fieldmapping.grpc.schema.Profile
import com.multiplier.fieldmapping.grpc.schema.ProfileResponse
import com.multiplier.integration.adapter.api.ContractOnboardingServiceAdapter
import com.multiplier.integration.adapter.api.FieldMappingServiceAdapter
import com.multiplier.integration.adapter.api.KnitAdapter
import com.multiplier.integration.adapter.api.NewCompanyServiceAdapter
import com.multiplier.integration.adapter.api.PaymentServiceAdapter
import com.multiplier.integration.adapter.api.resources.knit.*
import com.multiplier.integration.mock.*
import com.multiplier.integration.repository.*
import com.multiplier.integration.repository.model.*
import com.multiplier.integration.types.UnmappedField
import com.multiplier.integration.utils.toLocalDateTime
import io.mockk.*
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNull
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import com.multiplier.integration.service.exception.EntityNotFoundException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.test.context.junit.jupiter.SpringExtension
import java.util.*
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

@ExtendWith(SpringExtension::class)
internal class FieldMappingServiceTest {

    @MockK
    private lateinit var companyIntegrationRepository: CompanyIntegrationRepository

    @MockK
    private lateinit var knitAdapter: KnitAdapter

    @MockK
    private lateinit var receivedEventRepository: ReceivedEventRepository

    @MockK
    private lateinit var newCompanyServiceAdapter: NewCompanyServiceAdapter

    @MockK
    private lateinit var contractOnboardingServiceAdapter: ContractOnboardingServiceAdapter

    @MockK
    private lateinit var legalEntityMappingRepository: LegalEntityMappingRepository

    @MockK
    private lateinit var externalPlatformValuesRepository: ExternalPlatformValuesRepository

    @MockK
    private lateinit var paymentServiceAdapter: PaymentServiceAdapter

    @MockK
    private lateinit var receivedEventsArchiveRepository: ReceivedEventsArchiveRepository

    @MockK
    private lateinit var fieldMappingServiceAdapter: FieldMappingServiceAdapter

    @InjectMockKs
    private lateinit var fieldMappingService: FieldMappingService

    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this)
    }

    @Test
    fun `getIntegrationLegalEntityMappings successfully`() {
        val integrationId = 1L
        val mockEntityId = 2L
        val companyId = 100L
        val newEntityId = 4L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
            every { entityId } returns newEntityId
        }
        val legalEntityMapping2 = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
            every { entityId } returns mockEntityId
        }
        val mockLegalEntities = CompanyOuterClass.GetLegalEntitiesResponse.newBuilder()
            .addAllEntities(
                listOf(
                    LegalEntity.newBuilder()
                        .setId(mockEntityId)
                        .setCompanyId(100)
                        .setAddress(
                            CompanyOuterClass.Address.newBuilder()
                                .setCountry("USA")
                                .build()
                        )
                        .setCurrencyCode("USD")
                        .setStatus(LegalEntity.LegalEntityStatus.ACTIVE)
                        .build(),
                    LegalEntity.newBuilder()
                        .setId(newEntityId)
                        .setCompanyId(100)
                        .setAddress(
                            CompanyOuterClass.Address.newBuilder()
                                .setCountry("VNM")
                                .build()
                        )
                        .setCurrencyCode("USD")
                        .setStatus(LegalEntity.LegalEntityStatus.ACTIVE)
                        .build()
                )
            )
            .build()

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } returns listOf(
            legalEntityMapping,
            legalEntityMapping2
        )
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every { legalEntityMappingRepository.saveAll(any<List<JpaLegalEntityMapping>>()) } returns listOf(mockk<JpaLegalEntityMapping>())

        val resp = fieldMappingService.getIntegrationLegalEntityMappings(integrationId)

        assertEquals(2, resp.size)
        assertEquals(mockEntityId, resp[1].legalEntity.id)
        assertEquals(LegalMappingStatus.UNMAPPED.name, resp[1].entityMappingStatus.name)
    }

    @Test
    fun `getIntegrationLegalEntityMappings successfully with unmap entity`() {
        val integrationId = 1L
        val mockEntityId = 2L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntities = getMockLegalEntity(entityId = mockEntityId, companyId)

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntities.entitiesList
        every { legalEntityMappingRepository.saveAll(any<List<JpaLegalEntityMapping>>()) } returns listOf(mockk<JpaLegalEntityMapping>())

        fieldMappingService.getIntegrationLegalEntityMappings(integrationId)

        verify(exactly = 1) { legalEntityMappingRepository.saveAll(any<List<JpaLegalEntityMapping>>()) }
        verify(exactly = 2) { legalEntityMappingRepository.findByIntegrationId(integrationId) }
    }

    @Test
    fun `saveIntegrationEntityMappingStatus successfully with fully mapped`() {
        val entityMappingId = 1L
        val mockIntegrationId = 2L
        val mockEntityId = 3L
        val mockEntityCountry = "USA"
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.FULLY_MAPPED
            every { integrationId } returns mockIntegrationId
            every { entityId } returns mockEntityId
            every { entityCountry } returns mockEntityCountry
        }
        val companyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { enabled } returns true
            every { incomingSyncEnabled } returns true
        }
        val receivedEvent = mockk<JpaReceivedEvent>(relaxed = true)

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping
        every { companyIntegrationRepository.findById(mockIntegrationId) } returns Optional.of(companyIntegration)
        every {
            receivedEventRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                companyIntegration.accountToken,
                mockEntityCountry,
                isEntityEnabled = false,
                processed = true
            )
        } returns listOf(receivedEvent)
        every { receivedEventRepository.saveAll(any<List<JpaReceivedEvent>>()) } returns listOf(receivedEvent)
        every {
            receivedEventsArchiveRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                any(),
                any(),
                any(),
                any()
            )
        } returns listOf()
        val resp = fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId)

        assertEquals(true, resp.success)
        verify(exactly = 1) { legalEntityMappingRepository.save(any()) }
    }

    @Test
    fun `saveIntegrationEntityMappingStatus successfully with fully mapped - include archived events`() {
        val entityMappingId = 1L
        val mockIntegrationId = 2L
        val mockEntityId = 3L
        val mockEntityCountry = "USA"
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.FULLY_MAPPED
            every { integrationId } returns mockIntegrationId
            every { entityId } returns mockEntityId
            every { entityCountry } returns mockEntityCountry
        }
        val companyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { enabled } returns true
            every { incomingSyncEnabled } returns true
        }
        val receivedEvent = mockk<JpaReceivedEvent>(relaxed = true)

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping
        every { companyIntegrationRepository.findById(mockIntegrationId) } returns Optional.of(companyIntegration)
        every {
            receivedEventRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                companyIntegration.accountToken,
                mockEntityCountry,
                isEntityEnabled = false,
                processed = true
            )
        } returns listOf(receivedEvent)
        every { receivedEventRepository.saveAll(any<List<JpaReceivedEvent>>()) } returns listOf(receivedEvent)
        every {
            receivedEventsArchiveRepository.findByIntegrationIdAndEntityCountryAndIsEntityEnabledAndProcessed(
                any(),
                any(),
                any(),
                any()
            )
        } returns listOf(
            JpaReceivedEventArchive(
                eventId = "event-id",
                syncId = "sync-id",
                integrationId = "integrationId",
                eventType = null,
                syncDataType = null,
                errors = null,
                identifiervalue = null,
                receivedTime = null,
                data = null,
                confirmedByUser = null,
                processed = null,
                isEntityEnabled = false,
                entityId = null,
                entityCountry = null
            )
        )
        justRun { receivedEventsArchiveRepository.deleteAll(any()) }

        val resp = fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId)

        assertEquals(true, resp.success)
        verify(exactly = 1) { legalEntityMappingRepository.save(any()) }
    }

    @Test
    fun `saveIntegrationEntityMappingStatus failed for not fully mapped`() {
        val entityMappingId = 1L
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping

        val resp = fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId)

        assertEquals(false, resp.success)
        verify(exactly = 0) { legalEntityMappingRepository.save(any()) }
    }

    @Test
    fun `saveIntegrationEntityMappingStatus successfully for disable sync`() {
        val entityMappingId = 1L
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        }

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } returns legalEntityMapping

        val resp = fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId, enableDataSync = false)

        assertEquals(true, resp.success)
        verify(exactly = 1) { legalEntityMappingRepository.save(any()) }
    }
    @Test
    fun `handleFieldMappingsOnDisconnection successfully`() {
        val mockIntegrationId = 1L
        val legalEntityMapping = mockk<JpaLegalEntityMapping>(relaxed = true) {
            every { integrationId } returns mockIntegrationId
        }
        val externalPlatformValues = mockk<JpaExternalPlatformValues>(relaxed = true) {
            every { integrationId } returns mockIntegrationId
        }

        every { legalEntityMappingRepository.findByIntegrationId(mockIntegrationId) } returns listOf(legalEntityMapping)
        every { legalEntityMappingRepository.saveAll(listOf(legalEntityMapping)) } returns listOf(legalEntityMapping)
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns listOf(
            externalPlatformValues
        )
        every { externalPlatformValuesRepository.saveAll(listOf(externalPlatformValues)) } returns listOf(
            externalPlatformValues
        )

        fieldMappingService.handleFieldMappingsOnDisconnection(mockIntegrationId)

        verify(exactly = 1) { legalEntityMappingRepository.saveAll(listOf(legalEntityMapping)) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(listOf(externalPlatformValues)) }
    }

    @Test
    fun `getExternalEnumValues successfully`() {
        val mockIntegrationId = 1L
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true) {
            every { fieldId } returns "gender"
            every { updatedOn } returns "2024-08-04T18:52:21.145".toLocalDateTime()
        }
        val externalFields = listOf(
            FieldData(
                fieldId = "gender",
                fieldFromApp = "gender",
                mappedKey = "gender",
                label = "Gender",
                dataType = "ENUM"
            ),
            FieldData(
                fieldId = "maritalStatus",
                fieldFromApp = "maritalStatus",
                mappedKey = "maritalStatus",
                label = "Marital Status",
                dataType = "ENUM"
            ),
        )
        val genderFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(
                fields = listOf(
                    FieldValues(
                        label = "FEMALE"
                    )
                )
            )
        )
        val maritalStatusFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(
                fields = listOf(
                    FieldValues(
                        label = "SINGLE"
                    )
                )
            )
        )
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Hibob"
        }

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns listOf(
            externalValues
        )
        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "hibob",
                "maritalStatus"
            )
        } returns maritalStatusFieldValuesResp
        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "hibob",
                "gender"
            )
        } returns genderFieldValuesResp
        every { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) } returns listOf(mockk<JpaExternalPlatformValues>())

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues without enum in external fields `() {
        val mockIntegrationId = 1L
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true)
        val externalFields = listOf(
            FieldData(
                fieldId = "gender",
                fieldFromApp = "gender",
                mappedKey = "gender",
                label = "Gender",
                dataType = "STRING"
            )
        )
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
        }
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns listOf(
            externalValues
        )

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 1) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 0) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues need to update but failed`() {
        val mockIntegrationId = 1L
        val externalValues = mockk<JpaExternalPlatformValues>(relaxed = true) {
            every { fieldId } returns "gender"
            every { updatedOn } returns "2024-08-04T18:52:21.145".toLocalDateTime()
        }
        val externalFields = listOf(
            FieldData(
                fieldId = "gender",
                fieldFromApp = "gender",
                mappedKey = "gender",
                label = "Gender",
                dataType = "ENUM"
            )
        )
        val genderFieldValuesResp = GetFieldValuesResponse(
            success = false
        )
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Hibob"
        }

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns listOf(
            externalValues
        )
        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "hibob",
                "gender"
            )
        } returns genderFieldValuesResp

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 1) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 0) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues with department field fallback when departments list returns empty`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val departmentFieldId = "department"

        // Mock company integration
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Paychex"
        }

        // Mock external fields with a department field
        val externalFields = listOf(
            FieldData(
                fieldId = departmentFieldId,
                fieldFromApp = "department",
                mappedKey = "department",
                label = "Department",
                dataType = "ENUM"
            )
        )

        // Mock empty field values response
        val emptyFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(fields = emptyList())
        )

        // Mock departments list response
        val departmentsListResp = DepartmentsListResponse(
            success = true,
            data = DepartmentsData(
                departments = listOf(
                    Department(id = "dept1", name = "Engineering", companyId = "company1"),
                    Department(id = "dept2", name = "Sales", companyId = "company2")
                )
            )
        )

        // Mock repository responses
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns emptyList()

        // Capture the argument passed to saveAll
        val savedValuesSlot = slot<List<JpaExternalPlatformValues>>()
        every { externalPlatformValuesRepository.saveAll(capture(savedValuesSlot)) } returns listOf(mockk<JpaExternalPlatformValues>())

        // Mock Knit adapter responses
        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "paychex",
                departmentFieldId
            )
        } returns emptyFieldValuesResp

        coEvery {
            knitAdapter.getDepartmentsList(
                mockCompanyId,
                mockPlatformId,
                "paychex"
            )
        } returns departmentsListResp

        // Call the method
        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        // Verify the results
        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }

        // Verify the captured saved values
        val savedValues = savedValuesSlot.captured.first()
        assertEquals(departmentFieldId, savedValues.fieldId)
        assertEquals("department", savedValues.mappedKey)

        // Verify the values contain both departments
        val values = savedValues.values
        assertNotNull(values)
        assertTrue(values.any { it.contains("Engineering") })
        assertTrue(values.any { it.contains("Sales") })
    }

    @Test
    fun `getExternalEnumValues with department field fallback when departments list fails`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val departmentFieldId = "department"
        
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Paychex"
        }

        val externalFields = listOf(
            FieldData(
                fieldId = departmentFieldId,
                fieldFromApp = "department",
                mappedKey = "department",
                label = "Department",
                dataType = "ENUM"
            )
        )

        val emptyFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(fields = emptyList())
        )

        val failedDepartmentsListResp = DepartmentsListResponse(
            success = false,
            error = ErrorResponse(msg = "Failed to fetch departments")
        )

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns emptyList()
        val savedValuesSlot = slot<List<JpaExternalPlatformValues>>()
        every { externalPlatformValuesRepository.saveAll(capture(savedValuesSlot)) } returns listOf(mockk<JpaExternalPlatformValues>())

        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "paychex",
                departmentFieldId
            )
        } returns emptyFieldValuesResp

        coEvery {
            knitAdapter.getDepartmentsList(
                mockCompanyId,
                mockPlatformId,
                "paychex"
            )
        } returns failedDepartmentsListResp

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues with department field fallback when departments list throws exception`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val departmentFieldId = "department"
        
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Paychex"
        }

        val externalFields = listOf(
            FieldData(
                fieldId = departmentFieldId,
                fieldFromApp = "department",
                mappedKey = "department",
                label = "Department",
                dataType = "ENUM"
            )
        )

        val emptyFieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(fields = emptyList())
        )

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns emptyList()
        val savedValuesSlot = slot<List<JpaExternalPlatformValues>>()
        every { externalPlatformValuesRepository.saveAll(capture(savedValuesSlot)) } returns listOf(mockk<JpaExternalPlatformValues>())

        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "paychex",
                departmentFieldId
            )
        } returns emptyFieldValuesResp

        coEvery {
            knitAdapter.getDepartmentsList(
                mockCompanyId,
                mockPlatformId,
                "paychex"
            )
        } throws RuntimeException("Network error")

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }

    @Test
    fun `getExternalEnumValues with department field fallback when field values has data`() {
        val mockIntegrationId = 1L
        val mockCompanyId = 2L
        val mockPlatformId = 3L
        val departmentFieldId = "department"
        
        val mockCompanyIntegration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns mockIntegrationId
            every { companyId } returns mockCompanyId
            every { platform.id } returns mockPlatformId
            every { platform.name } returns "Paychex"
        }

        val externalFields = listOf(
            FieldData(
                fieldId = departmentFieldId,
                fieldFromApp = "department",
                mappedKey = "department",
                label = "Department",
                dataType = "ENUM"
            )
        )

        val fieldValuesResp = GetFieldValuesResponse(
            success = true,
            data = Field(
                fields = listOf(
                    FieldValues(id = "dept1", label = "Engineering"),
                    FieldValues(id = "dept2", label = "Sales")
                )
            )
        )

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) } returns emptyList()
        val savedValuesSlot = slot<List<JpaExternalPlatformValues>>()
        every { externalPlatformValuesRepository.saveAll(capture(savedValuesSlot)) } returns listOf(mockk<JpaExternalPlatformValues>())

        coEvery {
            knitAdapter.getFieldValues(
                mockCompanyId,
                mockPlatformId,
                "paychex",
                departmentFieldId
            )
        } returns fieldValuesResp

        fieldMappingService.getExternalEnumValues(mockCompanyIntegration, externalFields)

        verify(exactly = 2) { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(mockIntegrationId) }
        verify(exactly = 1) { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
        verify(exactly = 0) { knitAdapter.getDepartmentsList(mockCompanyId, mockPlatformId, "paychex") }
        
        val savedValues = savedValuesSlot.captured.first()
        assertEquals(departmentFieldId, savedValues.fieldId)
        assertEquals("department", savedValues.mappedKey)
        
        val values = savedValues.values
        assertNotNull(values)
        assertTrue(values.any { it.contains("Engineering") })
        assertTrue(values.any { it.contains("Sales") })
    }

    @Test
    fun `getIntegrationFieldsMappingProfile returns correct output for fully mapped profile`() {
        val entityId = 1L
        val integrationId = 2L
        val companyId = 3L
        val legalEntity = mockk<CompanyOuterClass.LegalEntity>(relaxed = true) {
            every { id } returns entityId
            every { legalName } returns "Test Entity"
            every { address } returns CompanyOuterClass.Address.newBuilder().setCountry("USA").build()
        }
        val profileResponse = mockk<ProfileResponse>(relaxed = true) {
            every { profile } returns mockk(relaxed = true)
            every { profile.id } returns UUID.randomUUID().toString()
            every { profile.isActive } returns true
            every { profile.rulesList } returns listOf(
                mockk {
                    every { isRequired } returns true
                    every { sourceField } returns "field"
                }
            )
        }
        val integration = mockk<JpaCompanyIntegration>(relaxed = true) {
            every { id } returns integrationId
            every { platform.name } returns "TestPlatform"
            every { platform.id } returns 1L
        }
        val profile = mockk<Profile>(relaxed = true) {
            every { isActive } returns true
            every { rulesList } returns listOf(
                mockk {
                    every { isRequired } returns true
                    every { sourceField } returns "field"
                }
            )
            every { id } returns UUID.randomUUID().toString()
            every { configMap } returns Struct.getDefaultInstance()
        }
        // Use a real list with real objects for knitFields.data?.default
        val knitFields = mockk<GetAllFieldsResponse>(relaxed = true)
        val fieldData = FieldData(
            fieldId = "field",
            fieldFromApp = "field",
            mappedKey = "field",
            label = "Field",
            dataType = "STRING"
        )
        every { knitFields.data } returns mockk(relaxed = true) {
            every { default } returns listOf(fieldData)
        }

        every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(integration)
        every { newCompanyServiceAdapter.getLegalEntities(any()) } returns listOf(legalEntity)
        every { fieldMappingServiceAdapter.listProfiles(any()) } returns mockk {
            every { profilesList } returns listOf(profile)
        }
        every { fieldMappingServiceAdapter.createProfile(any()) } returns profileResponse
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId) } returns Optional.of(
            mockk(relaxed = true)
        )
        every { legalEntity.status } returns CompanyOuterClass.LegalEntity.LegalEntityStatus.ACTIVE
        every { knitAdapter.getAllFields(any(), any(), any()) } returns knitFields
        every { legalEntityMappingRepository.save(any()) } returns mockk(relaxed = true)
        // Mock dependencies for getKnitFieldsV2 method instead of mocking the method itself
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()

        val result = fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)
        assertNotNull(result)
        assertEquals(integrationId, result.integrationId)
    }

    @Test
    fun `formatUnmappedFields should handle custom fields correctly`() {
        val updatedExternalPlatformValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "field1",
                integrationId = 1L,
                mappedKey = "key1",
                values = listOf("""{"id":"val1","label":"Value 1"}""")
            )
        )
        val thirdPartyFields = listOf(
            UnmappedField.newBuilder()
                .key("customFields.fields.field1")
                .label("Custom Field 1")
                .fieldId("field1")
                .type("STRING")
                .isCustomField(true)
                .isMappedByThirdParty(false)
                .build()
        )
        val integration = mockk<JpaCompanyIntegration> {
            every { platform.isSpecialEnum } returns true
        }

        val result = fieldMappingService.formatUnmappedFields(updatedExternalPlatformValues, thirdPartyFields, integration)

        assertEquals(1, result.size)
        assertEquals("customFields.fields.field1", result[0].key)
        assertEquals(1, result[0].children.size)
    }

    @Test
    fun `getExternalEnumValues should return cached values when no enum fields`() {
        val integration = mockk<JpaCompanyIntegration> {
            every { id } returns 1L
        }
        val cachedValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "cached1",
                integrationId = 1L,
                mappedKey = "key1",
                values = listOf("value1")
            )
        )
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(1L) } returns cachedValues

        val result = fieldMappingService.getExternalEnumValues(integration, emptyList())

        assertEquals(cachedValues, result)
    }

    @Test
    fun `getExternalEnumValues should handle null external fields`() {
        val integration = mockk<JpaCompanyIntegration> {
            every { id } returns 1L
        }
        val cachedValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "cached1",
                integrationId = 1L,
                mappedKey = "key1",
                values = listOf("value1")
            )
        )
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(1L) } returns cachedValues

        val result = fieldMappingService.getExternalEnumValues(integration, null)

        assertEquals(cachedValues, result)
    }

    @Test
    fun `getFixedEnumValues should handle gender field`() {
        val externalPlatformValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "profile.gender",
                integrationId = 1L,
                mappedKey = "gender",
                values = listOf("MALE", "FEMALE")
            )
        )
        val thirdPartyFields = listOf(
            UnmappedField.newBuilder()
                .key("profile.gender")
                .fieldId("profile.gender")
                .type("ENUM")
                .build()
        )

        val result = fieldMappingService.getFixedEnumValues(externalPlatformValues, 1L, thirdPartyFields)

        assertTrue(result.isNotEmpty())
        val genderField = result.find { it.fieldId == "profile.gender" }
        assertNotNull(genderField)
    }

    @Test
    fun `getFixedEnumValues should handle marital status field`() {
        val externalPlatformValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "profile.maritalStatus",
                integrationId = 1L,
                mappedKey = "maritalStatus",
                values = listOf("SINGLE", "MARRIED")
            )
        )
        val thirdPartyFields = listOf(
            UnmappedField.newBuilder()
                .key("profile.maritalStatus")
                .fieldId("profile.maritalStatus")
                .type("ENUM")
                .build()
        )

        val result = fieldMappingService.getFixedEnumValues(externalPlatformValues, 1L, thirdPartyFields)

        assertTrue(result.isNotEmpty())
        val maritalStatusField = result.find { it.fieldId == "profile.maritalStatus" }
        assertNotNull(maritalStatusField)
    }

    @Test
    fun `getFixedEnumValues should add missing enum values`() {
        val externalPlatformValues = emptyList<JpaExternalPlatformValues>()
        val integrationId = 1L
        val thirdPartyFields = listOf(
            UnmappedField.newBuilder()
                .key("profile.gender")
                .fieldId("gender_field")
                .build()
        )

        val result = fieldMappingService.getFixedEnumValues(externalPlatformValues, integrationId, thirdPartyFields)

        assertTrue(result.any { it.mappedKey == "profile.gender" })
        assertTrue(result.any { it.mappedKey == "profile.maritalStatus" })
    }

    @Test
    fun `handleFieldMappingsOnDisconnection should handle exceptions gracefully`() {
        val integrationId = 1L

        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } throws RuntimeException("Database error")

        // Should not throw exception
        fieldMappingService.handleFieldMappingsOnDisconnection(integrationId)
    }

    @Test
    fun `saveIntegrationEntityMappingStatus should return success false when not fully mapped`() {
        val entityMappingId = 1L
        val legalEntityMapping = mockk<JpaLegalEntityMapping> {
            every { status } returns LegalMappingStatus.PARTIALLY_MAPPED
            every { isEnabled = any() } just Runs
        }

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)

        val result = fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId, true)

        assertFalse(result.success)
        assertEquals("Need to map all fields to enable sync for this legal entity", result.message)
    }

    @Test
    fun `formatKnitFields should handle null external fields`() {
        val result = fieldMappingService.formatKnitFields(null)

        assertTrue(result.isEmpty())
    }

    @Test
    fun `formatKnitFields should handle empty external fields`() {
        val result = fieldMappingService.formatKnitFields(emptyList())

        assertTrue(result.isEmpty())
    }

    @Test
    fun `formatKnitFields should format fields correctly`() {
        val externalFields = listOf(
            FieldData(
                fieldId = "field1",
                label = "Field 1",
                dataType = "STRING",
                mappedKey = "mapped1",
                isCustomField = true
            ),
            FieldData(
                fieldId = "field2",
                label = "Field 2",
                dataType = "ENUM",
                mappedKey = null,
                isCustomField = false
            )
        )

        val result = fieldMappingService.formatKnitFields(externalFields)

        assertEquals(2, result.size)
        // The first field has mappedKey so it should use the formatted key
        assertEquals("customFields.fields.mapped1", result[0].key)
        assertEquals("Field 1", result[0].label)
        assertTrue(result[0].isMappedByThirdParty)
        // The second field has no mappedKey so key should be null
        assertNull(result[1].key)
        assertEquals("Field 2", result[1].label)
        assertFalse(result[1].isMappedByThirdParty)
    }

    @Test
    fun `getExternalEnumValues should use cached values when fresh`() {
        val integration = mockk<JpaCompanyIntegration> {
            every { id } returns 1L
        }
        val cachedValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "field1",
                integrationId = 1L,
                mappedKey = "key1",
                values = listOf("value1")
            ).apply {
                updatedOn = java.time.LocalDateTime.now().minusHours(1) // Fresh cache
            }
        )
        val externalFields = listOf(
            FieldData(
                fieldId = "field1",
                dataType = "ENUM",
                mappedKey = "key1"
            )
        )

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(1L) } returns cachedValues

        val result = fieldMappingService.getExternalEnumValues(integration, externalFields)

        assertEquals(cachedValues, result)
        // Should not call external API when cache is fresh
        coVerify(exactly = 0) { knitAdapter.getFieldValues(any(), any(), any(), any()) }
    }

    @Test
    fun `getKnitFields should handle empty platform data`() {
        val integrationId = 1L
        val companyId = 100L
        val integration = mockk<JpaCompanyIntegration> {
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
        }

        every { knitAdapter.getAllFields(companyId, 1L, any()) } returns mockk {
            every { data } returns null
        }

        val result = fieldMappingService.getKnitFields(integrationId, companyId, integration)

        assertTrue(result.isEmpty())
    }

    @Test
    fun `getKnitFields should handle platform data with custom fields`() {
        val integrationId = 1L
        val companyId = 100L
        val integration = mockk<JpaCompanyIntegration> {
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
        }
        val defaultFields = listOf(
            FieldData(fieldId = "default1", label = "Default 1")
        )
        val mappedFields = listOf(
            FieldData(fieldId = "mapped1", label = "Mapped 1")
        )
        val unmappedFields = listOf(
            FieldData(fieldId = "unmapped1", label = "Unmapped 1")
        )

        every { knitAdapter.getAllFields(companyId, 1L, any()) } returns mockk {
            every { data } returns FieldDataList(
                default = defaultFields,
                mapped = mappedFields,
                unmapped = unmappedFields
            )
        }

        val result = fieldMappingService.getKnitFields(integrationId, companyId, integration)

        assertEquals(3, result.size)
        assertTrue(result.any { it.fieldId == "default1" })
        assertTrue(result.any { it.fieldId == "mapped1" && it.isCustomField == true })
        assertTrue(result.any { it.fieldId == "unmapped1" && it.isCustomField == true })
    }

    @Test
    fun `saveIntegrationEntityMappingStatus should handle entity mapping not found`() {
        val entityMappingId = 1L

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.empty()

        assertThrows<EntityNotFoundException> {
            fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId, true)
        }
    }

    @Test
    fun `updateAndSaveEntityMappingsOnDisconnection should handle empty mappings`() {
        val integrationId = 1L

        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } returns emptyList()

        // Should not throw exception
        assertDoesNotThrow {
            fieldMappingService.handleFieldMappingsOnDisconnection(integrationId)
        }
    }

    @Test
    fun `updateAndSaveEnumValuesOnDisconnection should handle empty values`() {
        val integrationId = 1L

        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } returns emptyList()
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()

        // Should not throw exception
        assertDoesNotThrow {
            fieldMappingService.handleFieldMappingsOnDisconnection(integrationId)
        }
    }

    @Test
    fun `getIntegrationLegalEntityMappings should handle integration not found`() {
        val integrationId = 1L

        every { companyIntegrationRepository.findById(integrationId) } returns Optional.empty()

        assertThrows<EntityNotFoundException> {
            fieldMappingService.getIntegrationLegalEntityMappings(integrationId)
        }
    }

    @Test
    fun `getIntegrationLegalEntityMappings should handle company service error`() {
        val integrationId = 1L
        val integration = mockk<JpaCompanyIntegration> {
            every { companyId } returns 100L
        }

        every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(integration)
        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } returns emptyList()
        every { newCompanyServiceAdapter.getLegalEntities(100L) } throws RuntimeException("Service error")

        assertThrows<RuntimeException> {
            fieldMappingService.getIntegrationLegalEntityMappings(integrationId)
        }
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should handle integration not found`() {
        val entityId = 1L
        val integrationId = 1L

        every { companyIntegrationRepository.findById(integrationId) } returns Optional.empty()

        assertThrows<EntityNotFoundException> {
            fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)
        }
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should handle legal entity not found`() {
        val entityId = 1L
        val integrationId = 1L
        val companyId = 100L
        val integration = mockk<JpaCompanyIntegration> {
            every { <EMAIL> } returns companyId
            every { platform.name } returns "TestPlatform"
        }

        every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(integration)
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns emptyList()

        assertThrows<EntityNotFoundException> {
            fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)
        }
    }

    @Test
    fun `formatExternalKey should handle custom field with mapped key`() {
        val fieldData = FieldData(
            fieldId = "field1",
            mappedKey = "mapped1",
            isCustomField = true
        )

        // This tests the private formatExternalKey method indirectly through formatKnitFields
        val result = fieldMappingService.formatKnitFields(listOf(fieldData))

        assertEquals(1, result.size)
        assertEquals("customFields.fields.mapped1", result[0].key)
    }

    @Test
    fun `formatExternalKey should handle non-custom field`() {
        val fieldData = FieldData(
            fieldId = "field1",
            mappedKey = "mapped1",
            isCustomField = false
        )

        val result = fieldMappingService.formatKnitFields(listOf(fieldData))

        assertEquals(1, result.size)
        assertEquals("mapped1", result[0].key)
    }

    @Test
    fun `getFixedEnumValues should handle empty external platform values and third party fields`() {
        val result = fieldMappingService.getFixedEnumValues(emptyList(), 1L, emptyList())

        // Should still add default gender and marital status values
        assertTrue(result.any { it.mappedKey == "profile.gender" })
        assertTrue(result.any { it.mappedKey == "profile.maritalStatus" })
    }

    @Test
    fun `getOrCreateProfile should handle contractor mapping`() {
        val companyId = 100L
        val integrationId = 1L
        val integration = mockk<JpaCompanyIntegration> {
            every { id } returns integrationId
            every { <EMAIL> } returns companyId
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
        }

        // Mock empty profiles list to trigger profile creation
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns emptyList()
        }
        coEvery { knitAdapter.getAllFields(any(), any(), any()) } returns mockk {
            every { data } returns null
        }
        every { contractOnboardingServiceAdapter.getFieldRequirements(any()) } returns emptyList()
        every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(integration)

        // Mock profile creation for contractor
        val mockProfile = mockk<Profile> {
            every { id } returns UUID.randomUUID().toString()
            every { isActive } returns true
            every { rulesList } returns emptyList()
        }
        every { fieldMappingServiceAdapter.createProfile(any()) } returns mockk {
            every { profile } returns mockProfile
        }
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()

        // Mock contract onboarding service for contractor data specs
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()

        // Should not throw exception when both entityId and legalEntity are null
        assertDoesNotThrow {
            val result = fieldMappingService.getIntegrationFieldsMappingContractorProfile(integrationId)
            assertNotNull(result)
            assertEquals(integrationId, result.integrationId)
        }

        // Verify contractor profile creation was called
        verify { fieldMappingServiceAdapter.createProfile(any()) }
    }

    @Test
    fun `getOrCreateProfile should not update legal entity mapping when entityId is null`() {
        val companyId = 100L
        val integrationId = 1L
        val integration = mockk<JpaCompanyIntegration> {
            every { id } returns integrationId
            every { <EMAIL> } returns companyId
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
        }

        every { knitAdapter.getAllFields(any(), any(), any()) } returns mockk {
            every { data } returns null
        }
        every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(integration)

        // Mock existing contractor profile
        val mockProfile = mockk<Profile> {
            every { id } returns "contractor-profile-id"
            every { isActive } returns true
            every { rulesList } returns emptyList()
            every { configMap.fieldsMap } returns mapOf(
                "contractType" to mockk { every { stringValue } returns "CONTRACT_TYPE_FREELANCER" },
                "integrationId" to mockk { every { stringValue } returns integrationId.toString() }
            )
        }

        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockProfile)
        }

        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()

        // Should not call legal entity mapping repository when entityId is null
        assertDoesNotThrow {
            fieldMappingService.getIntegrationFieldsMappingContractorProfile(integrationId)
        }

        // Verify legal entity mapping repository was not called
        verify(exactly = 0) { legalEntityMappingRepository.findByIntegrationIdAndEntityId(any(), any()) }
    }

    @Test
    fun `getOnboardDataSpecs should handle null legalEntity for contractor mapping`() {
        val companyId = 100L
        val integrationId = 1L

        every { contractOnboardingServiceAdapter.getFieldRequirements(any()) } returns emptyList()
        every { companyIntegrationRepository.findById(integrationId) } returns Optional.of(mockk {
            every { <EMAIL> } returns companyId
            every { id } returns integrationId
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
        })
        every { knitAdapter.getAllFields(any(), any(), any()) } returns mockk {
            every { data } returns null
        }
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns emptyList()
        }
        every { fieldMappingServiceAdapter.createProfile(any()) } returns mockk {
            every { profile } returns mockk(relaxed = true)
        }
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()


        // Should handle null legalEntity gracefully
        assertDoesNotThrow {
            val result = fieldMappingService.getIntegrationFieldsMappingContractorProfile(integrationId)
            assertNotNull(result)
        }

        // Verify contractor onboarding request was made with entityId = 0
        verify {
            contractOnboardingServiceAdapter.getFieldRequirements(
                match { request ->
                    request.companyId == companyId &&
                    request.useCase == "FREELANCER_MEMBER_ONBOARDING"
                }
            )
        }
    }

    @Test
    fun `getExternalEnumValues should handle adapter exceptions gracefully`() {
        val integrationId = 1L
        val integration = mockk<JpaCompanyIntegration> {
            every { id } returns integrationId
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
            every { <EMAIL> } returns 100L
        }
        val externalFields = listOf(
            FieldData(fieldId = "enum_field", dataType = "ENUM", mappedKey = "mapped_enum")
        )

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()
        coEvery { knitAdapter.getFieldValues(any(), any(), any(), any()) } throws RuntimeException("API Error")

        // Should throw exception when adapter fails and no cached values exist
        assertThrows<RuntimeException> {
            fieldMappingService.getExternalEnumValues(integration, externalFields)
        }
    }

    @Test
    fun `formatUnmappedFields should handle malformed field data`() {
        val integration = mockk<JpaCompanyIntegration> {
            every { platform.name } returns "TestPlatform"
            every { platform.isSpecialEnum } returns false
            every { platform.id } returns 1L
            every { id } returns 1L
            every { companyId } returns 100L
        }
        val externalPlatformValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "malformed_field",
                integrationId = 1L,
                mappedKey = "mapped",
                values = emptyList() // Empty values
            )
        )
        val thirdPartyFields = listOf(
            UnmappedField.newBuilder()
                .key("") // Empty key
                .fieldId("malformed_field")
                .isMappedByThirdParty(true)
                .isCustomField(true)
                .type("ENUM")
                .build()
        )

        val result = fieldMappingService.formatUnmappedFields(
            externalPlatformValues,
            thirdPartyFields,
            integration
        )

        assertNotNull(result)
        assertTrue(result.isNotEmpty())
    }

    @Test
    fun `getKnitFields should handle null platform data gracefully`() {
        val integrationId = 1L
        val companyId = 100L
        val integration = mockk<JpaCompanyIntegration> {
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
        }

        every { knitAdapter.getAllFields(companyId, 1L, any()) } returns mockk {
            every { data } returns null
        }

        val result = fieldMappingService.getKnitFields(integrationId, companyId, integration)

        assertTrue(result.isEmpty())
    }

    @Test
    fun `getKnitFields should handle adapter timeout exception`() {
        val integrationId = 1L
        val companyId = 100L
        val integration = mockk<JpaCompanyIntegration> {
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
        }

        every { knitAdapter.getAllFields(companyId, 1L, any()) } throws RuntimeException("Timeout")

        assertThrows<RuntimeException> {
            fieldMappingService.getKnitFields(integrationId, companyId, integration)
        }
    }

    @Test
    fun `handleFieldMappingsOnDisconnection should handle repository exceptions`() {
        val integrationId = 1L

        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } throws RuntimeException("DB Error")
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()

        // Should not throw exception even when repository fails
        assertDoesNotThrow {
            fieldMappingService.handleFieldMappingsOnDisconnection(integrationId)
        }
    }

    @Test
    fun `getExternalEnumValues should handle department field fallback correctly`() {
        val integrationId = 1L
        val integration = mockk<JpaCompanyIntegration> {
            every { id } returns integrationId
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
            every { <EMAIL> } returns 100L
        }
        val departmentField = FieldData(
            fieldId = "employee.department",
            dataType = "ENUM",
            mappedKey = "department"
        )

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()

        // Mock empty response from field values endpoint
        coEvery { knitAdapter.getFieldValues(any(), any(), any(), any()) } returns GetFieldValuesResponse(
            success = true,
            data = Field(fields = emptyList()),
            responseCode = 200
        )

        // Mock successful departments list response
        coEvery { knitAdapter.getDepartmentsList(any(), any(), any()) } returns DepartmentsListResponse(
            success = true,
            data = DepartmentsData(
                departments = listOf(
                    Department(id = "dept1", name = "Engineering", companyId = "company1"),
                    Department(id = "dept2", name = "Marketing", companyId = "company2")
                )
            ),
            responseCode = 200
        )
        val savedValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "employee.department",
                integrationId = integrationId,
                mappedKey = "department",
                values = listOf("""{"id":"dept1","label":"Engineering"}""", """{"id":"dept2","label":"Marketing"}"""),
            ).apply {
                updatedOn = java.time.LocalDateTime.now().minusHours(1) // Fresh cache
            }
        )
        every { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) } returns savedValues
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns savedValues andThen savedValues

        val result = fieldMappingService.getExternalEnumValues(integration, listOf(departmentField))

        assertNotNull(result)
        assertTrue(result.isNotEmpty())
        val departmentValues = result.find { it.fieldId == "employee.department" }
        assertNotNull(departmentValues)
        assertEquals(2, departmentValues!!.values?.size)
        // Values are stored as JSON strings, so check if they contain the department names
        assertTrue(departmentValues.values?.any { it.contains("Engineering") } == true)
        assertTrue(departmentValues.values?.any { it.contains("Marketing") } == true)
    }

    @Test
    fun `getExternalEnumValues should handle cached values with expiration`() {
        val integrationId = 1L
        val integration = mockk<JpaCompanyIntegration> {
            every { id } returns integrationId
            every { platform.id } returns 1L
            every { platform.name } returns "TestPlatform"
            every { <EMAIL> } returns 100L
        }
        val enumField = FieldData(fieldId = "status", dataType = "ENUM", mappedKey = "status")

        // Mock expired cached value (older than 1 day)
        val expiredCachedValue = JpaExternalPlatformValues(
            fieldId = "status",
            integrationId = integrationId,
            mappedKey = "status",
            values = listOf("OLD_VALUE")
        ).apply {
            updatedOn = java.time.LocalDateTime.now().minusDays(2) // Expired cache
        }

        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns listOf(expiredCachedValue)
        coEvery { knitAdapter.getFieldValues(any(), any(), any(), any()) } returns GetFieldValuesResponse(
            success = true,
            data = Field(fields = listOf(
                FieldValues(id = "ACTIVE", label = "Active"),
                FieldValues(id = "INACTIVE", label = "Inactive")
            )),
            responseCode = 200
        )
        every { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) } returns emptyList()

        val result = fieldMappingService.getExternalEnumValues(integration, listOf(enumField))

        assertNotNull(result)
        assertTrue(result!!.isNotEmpty())
        // Should fetch fresh values, not use expired cache
        coVerify { knitAdapter.getFieldValues(any(), any(), any(), any()) }
    }

    @Test
    fun `formatUnmappedFields should handle nested field structures`() {
        val integration = mockk<JpaCompanyIntegration> {
            every { platform.name } returns "TestPlatform"
            every { platform.isSpecialEnum } returns false
            every { platform.id } returns 1L
            every { id } returns 1L
            every { companyId } returns 100L
        }
        val externalPlatformValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "address.country",
                integrationId = 1L,
                mappedKey = "country",
                values = listOf(
                    """{"id":"US","label":"United States"}""",
                    """{"id":"CA","label":"Canada"}""",
                    """{"id":"UK","label":"United Kingdom"}"""
                )
            )
        )
        val nestedField = UnmappedField.newBuilder()
            .key("address.country")
            .fieldId("address.country")
            .type("ENUM")
            .isMappedByThirdParty(false)
            .isCustomField(false)
            .label("Country")
            .build()

        val result = fieldMappingService.formatUnmappedFields(
            externalPlatformValues,
            listOf(nestedField),
            integration
        )

        assertNotNull(result)
        assertTrue(result.isNotEmpty())
        val countryField = result.find { it.fieldId == "address.country" }
        assertNotNull(countryField)
        assertEquals("Country", countryField!!.label)
    }

    @Test
    fun `getKnitFieldsV2 should include Hibob bank account fields for Hibob platform`() {
        val integration = mockk<JpaCompanyIntegration> {
            every { id } returns 1L
            every { companyId } returns 100L
            every { platform.id } returns 1L
            every { platform.name } returns "Hibob"
        }

        every { knitAdapter.getAllFields(any(), any(), any()) } returns mockk {
            every { data } returns FieldDataList(
                default = listOf(FieldData(fieldId = "name", label = "Name")),
                mapped = emptyList(),
                unmapped = emptyList()
            )
        }
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(1L) } returns emptyList()

        val result = fieldMappingService.getKnitFieldsV2(integration)

        assertNotNull(result)
        assertTrue(result.isNotEmpty())

        // Should include Hibob-specific bank account fields
        val bankAccountField = result.find { it.key == "bankAccountsTable.bankAccountType" }
        assertNotNull(bankAccountField)
        assertEquals("Bank Table - Bank Account Type", bankAccountField!!.label)
        assertTrue(bankAccountField.children.isNotEmpty())
        assertTrue(bankAccountField.children.any { it.key == "Checking" })
        assertTrue(bankAccountField.children.any { it.key == "Savings" })
    }

    @Test
    fun `getFixedEnumValues should handle empty enum values gracefully`() {
        val externalPlatformValues = listOf(
            JpaExternalPlatformValues(
                fieldId = "empty_enum",
                integrationId = 1L,
                mappedKey = "empty",
                values = emptyList()
            )
        )
        val thirdPartyFields = listOf(
            UnmappedField.newBuilder()
                .key("empty_enum")
                .fieldId("empty_enum")
                .type("ENUM")
                .build()
        )

        val result = fieldMappingService.getFixedEnumValues(externalPlatformValues, 1L, thirdPartyFields)

        assertNotNull(result)
        val emptyEnumField = result.find { it.fieldId == "empty_enum" }
        assertNotNull(emptyEnumField)
        assertTrue(emptyEnumField!!.values?.isEmpty() == true)
    }

    @Test
    fun `formatKnitFields should handle fields with special characters in mappedKey`() {
        val externalFields = listOf(
            FieldData(
                fieldId = "special-field",
                label = "Special Field",
                dataType = "STRING",
                mappedKey = "special.field-with_chars",
                isCustomField = true
            )
        )

        val result = fieldMappingService.formatKnitFields(externalFields)

        assertEquals(1, result.size)
        assertEquals("customFields.fields.special.field-with_chars", result[0].key)
        assertEquals("Special Field", result[0].label)
        assertTrue(result[0].isMappedByThirdParty)
    }

    @Test
    fun `saveIntegrationEntityMappingStatus should handle concurrent modifications`() {
        val entityMappingId = 1L
        val legalEntityMapping = mockk<JpaLegalEntityMapping> {
            every { status } returns LegalMappingStatus.FULLY_MAPPED
            every { isEnabled = any() } just Runs
        }

        every { legalEntityMappingRepository.findById(entityMappingId) } returns Optional.of(legalEntityMapping)
        every { legalEntityMappingRepository.save(any()) } throws RuntimeException("Concurrent modification")

        assertThrows<RuntimeException> {
            fieldMappingService.saveIntegrationEntityMappingStatus(entityMappingId, true)
        }
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should handle very large field lists`() {
        val entityId = 1L
        val integrationId = 1L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntity = getMockLegalEntity(entityId, companyId)
        val profileId = UUID.randomUUID().toString()

        // Create a large number of fields to test performance
        val largeFieldList = (1..100).map { i -> // Reduced from 1000 to 100 for test performance
            FieldData(
                fieldId = "field_$i",
                label = "Field $i",
                dataType = "STRING",
                mappedKey = "mapped_$i",
                isCustomField = true
            )
        }

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntity.entitiesList
        every { knitAdapter.getAllFields(any(), any(), any()) } returns mockk {
            every { data } returns FieldDataList(
                default = largeFieldList,
                mapped = emptyList(),
                unmapped = emptyList()
            )
        }
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns listOf(mockk {
                every { id } returns profileId
                every { isActive } returns true
                every { rulesList } returns emptyList()
                every { configMap.fieldsMap } returns mapOf(
                    "entityId" to mockk { every { stringValue } returns entityId.toString() },
                    "integrationId" to mockk { every { stringValue } returns integrationId.toString() }
                )
            })
        }
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId) } returns Optional.of(mockk(relaxed = true) {
            every { status } returns LegalMappingStatus.UNMAPPED
        })

        val result = fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)

        assertNotNull(result)
        assertEquals(integrationId, result.integrationId)
        assertEquals(companyId, result.companyId)
        // Should handle large field lists without performance issues
        assertTrue(result.sourceFields.size >= 100)
    }

    @Test
    fun `formatUnmappedFields should handle null and empty labels`() {
        val integration = mockk<JpaCompanyIntegration> {
            every { platform.name } returns "TestPlatform"
        }
        val externalPlatformValues = emptyList<JpaExternalPlatformValues>()
        val thirdPartyFields = listOf(
            UnmappedField.newBuilder()
                .key("field1")
                .fieldId("field1")
                .type("STRING")
                .isMappedByThirdParty(false)
                .isCustomField(false)
                .build(),
            UnmappedField.newBuilder()
                .key("field2")
                .fieldId("field2")
                .type("STRING")
                .isCustomField(false)
                .isMappedByThirdParty(false)
                .label(null)
                .build()
        )

        val result = fieldMappingService.formatUnmappedFields(
            externalPlatformValues,
            thirdPartyFields,
            integration
        )

        assertNotNull(result)
        assertEquals(2, result.size)
        // Should handle empty/null labels gracefully
        assertTrue(result.all { it.label.isNotEmpty() })
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should handle profile creation failure gracefully`() {
        val entityId = 1L
        val integrationId = 1L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntity = getMockLegalEntity(entityId, companyId)

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntity.entitiesList
        every { knitAdapter.getAllFields(any(), any(), any()) } returns mockk {
            every { data } returns null
        }
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns emptyList()
        }
        // Profile creation fails
        every { fieldMappingServiceAdapter.createProfile(any()) } throws RuntimeException("Service unavailable")
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId) } returns Optional.empty()
        every { legalEntityMappingRepository.save(any()) } returns mockk(relaxed = true)

        // Should handle profile creation failure gracefully
        val result = fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)

        assertNotNull(result)
        // Should return empty result when profile creation fails
        assertEquals(null, result.integrationId)
    }

    @Test
    fun `getIntegrationFieldsMappingProfile should handle mixed field types correctly`() {
        val entityId = 1L
        val integrationId = 1L
        val companyId = 100L
        val mockCompanyIntegration = getMockCompanyIntegration()
        val mockLegalEntity = getMockLegalEntity(entityId, companyId)

        val mixedFields = listOf(
            FieldData(fieldId = "string_field", dataType = "STRING", mappedKey = "string_mapped"),
            FieldData(fieldId = "enum_field", dataType = "ENUM", mappedKey = "enum_mapped"),
            FieldData(fieldId = "number_field", dataType = "NUMBER", mappedKey = "number_mapped"),
            FieldData(fieldId = "boolean_field", dataType = "BOOLEAN", mappedKey = "boolean_mapped"),
            FieldData(fieldId = "date_field", dataType = "DATE", mappedKey = "date_mapped")
        )

        every { companyIntegrationRepository.findById(integrationId) } returns mockCompanyIntegration
        every { newCompanyServiceAdapter.getLegalEntities(companyId) } returns mockLegalEntity.entitiesList
        every { knitAdapter.getAllFields(any(), any(), any()) } returns mockk {
            every { data } returns null
        }
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns emptyList()
        every { fieldMappingServiceAdapter.listProfiles(companyId) } returns mockk {
            every { profilesList } returns emptyList()
        }
        every { fieldMappingServiceAdapter.createProfile(any()) } returns mockk {
            every { profile.id } returns UUID.randomUUID().toString()
            every { profile.rulesList } returns emptyList()
            every { profile.isActive } returns true
        }
        every { contractOnboardingServiceAdapter.getFieldRequirements(any()) } returns emptyList()
        every { contractOnboardingServiceAdapter.getBulkOnboardDataSpecs(any()) } returns emptyList()
        every { legalEntityMappingRepository.findByIntegrationIdAndEntityId(integrationId, entityId) } returns Optional.of(
            mockk<JpaLegalEntityMapping>(relaxed = true) {
                every { status } returns LegalMappingStatus.UNMAPPED
            }
        )
        every { legalEntityMappingRepository.save(any()) } returns mockk(relaxed = true)

        val result = fieldMappingService.getIntegrationFieldsMappingProfile(entityId, integrationId)

        assertNotNull(result)
        assertEquals(integrationId, result.integrationId)
        assertEquals(companyId, result.companyId)
        // Should handle all different field types
        assertTrue(result.sourceFields.size >= 5)
    }

    @Test
    fun `handleFieldMappingsOnDisconnection should clean up all related data`() {
        val integrationId = 1L
        val legalEntityMappings = listOf(
            mockk<JpaLegalEntityMapping> {
                every { status = any() } just Runs
                every { isEnabled = any() } just Runs
            }
        )
        val externalPlatformValues = listOf(
            mockk<JpaExternalPlatformValues> {
                every { isDeleted = any() } just Runs
            }
        )

        every { legalEntityMappingRepository.findByIntegrationId(integrationId) } returns legalEntityMappings
        every { legalEntityMappingRepository.saveAll(any<List<JpaLegalEntityMapping>>()) } returns emptyList()
        every { externalPlatformValuesRepository.findByIntegrationIdAndIsDeleted(integrationId) } returns externalPlatformValues
        every { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) } returns emptyList()

        fieldMappingService.handleFieldMappingsOnDisconnection(integrationId)

        // Verify all mappings are updated to unmapped status
        verify { legalEntityMappingRepository.saveAll(any<List<JpaLegalEntityMapping>>()) }
        verify { externalPlatformValuesRepository.saveAll(any<List<JpaExternalPlatformValues>>()) }
    }
}
