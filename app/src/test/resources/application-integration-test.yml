logging:
  level:
    org:
      hibernate:
        SQL: DEBUG
        type:
          descriptor:
            sql: ERROR
      apache:
        kafka: OFF
grpc:
  client:
    authority-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    core-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    contract-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    member-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    country-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    company-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    pay-se:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    payroll-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    org-management-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    timeoff-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    compensation-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
    pigeon-service:
      address: dns:///localhost:9091
      negotiationType: PLAINTEXT
  server:
    port: ${random.int[40000,63000]} # assign a random port
    security:
      enabled: false

docgen:
  url: http://localhost:1111

user-service:
  url: http://localhost:2222
ops:
  sender-email: Multiplier <<EMAIL>>
  onboarding-specialist-email-test: <EMAIL>

mpl:
  graphql:
    error-resolver:
      enabled: false
    scalar:
      enabled: false
  grpc:
    coroutine:
      enabled: false

growthbook:
  base-url: http://localhost:3333
  env-key: key

kafka:
  bootstrap-servers: localhost:9092
  producer:
    enabled: false
  consumer:
    enabled: false
  internal:
    enabled: false
  unicast:
    enabled: false

platform:
  base-url: http://localhost:4001
  kafka:
    auto-startup: false
    bootstrap-servers: localhost:9092

pigeon:
  client:
    kafka:
      bootstrap-servers: localhost:9092
