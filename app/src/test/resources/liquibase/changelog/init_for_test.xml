<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="tuanthieu (generated)" id="1682561923239-1">
        <sql dbms="postgresql, h2">
            CREATE SCHEMA IF NOT EXISTS public;
            CREATE SEQUENCE public.hibernate_sequence;
        </sql>

        <sql dbms="postgresql, h2" endDelimiter=";">
            CREATE SCHEMA IF NOT EXISTS contract
        </sql>

        <sql dbms="h2">
            CREATE TYPE "JSONB" AS json
        </sql>
    </changeSet>
    <changeSet author="tuanthieu (generated)" id="1682561923239-2">
        <createTable tableName="onboarding" schemaName="contract">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="onboarding_pkey"/>
            </column>
            <column name="contract_id" type="BIGINT"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="current_step" type="VARCHAR(255)"/>
            <column name="experience" type="VARCHAR(255)"/>
            <column name="payload" type="JSONB"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column defaultValueBoolean="false" name="before_self_serve" type="BOOLEAN"/>
            <column defaultValue="NONE" name="revoked_by" type="VARCHAR(255)"/>
            <column name="is_bulk_onboarded" type="BOOLEAN" defaultValue="NULL">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="tuanthieu (generated)" id="1682561923239-3">
        <createTable tableName="onboarding_aud" schemaName="contract">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="onboarding_aud_pkey"/>
            </column>
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="onboarding_aud_pkey"/>
            </column>
            <column name="revtype" type="SMALLINT"/>
            <column name="contract_id" type="BIGINT"/>
            <column name="created_by" type="BIGINT"/>
            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="current_step" type="VARCHAR(255)"/>
            <column name="experience" type="VARCHAR(255)"/>
            <column name="payload" type="JSONB"/>
            <column name="status" type="VARCHAR(255)"/>
            <column name="updated_by" type="BIGINT"/>
            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column defaultValueBoolean="false" name="before_self_serve" type="BOOLEAN"/>
            <column defaultValue="NONE" name="revoked_by" type="VARCHAR(255)"/>
            <column name="is_bulk_onboarded" type="BOOLEAN" defaultValue="NULL">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="tuanthieu (generated)" id="1682561923239-4">
        <createTable tableName="revinfo" schemaName="public">
            <column name="rev" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="revinfo_pkey"/>
            </column>
            <column name="revtstmp" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="tuanthieu (generated)" id="1682561923239-5">
        <createIndex indexName="onboarding_contract_id_idx" tableName="onboarding" schemaName="contract">
            <column name="contract_id"/>
        </createIndex>
    </changeSet>
    <changeSet author="tuanthieu (generated)" id="1682561923239-6">
        <createIndex indexName="onboarding_aud_updated_on_idx" tableName="onboarding_aud" schemaName="contract">
            <column name="contract_id"/>
            <column name="status"/>
            <column name="updated_on"/>
        </createIndex>
    </changeSet>
    <changeSet author="tuanthieu (generated)" id="1682561923239-7">
        <addForeignKeyConstraint baseColumnNames="rev" baseTableName="onboarding_aud" baseTableSchemaName="contract" constraintName="fk1akdldehmbwqipbib4ii4qm8p" deferrable="false" initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="rev" referencedTableName="revinfo" referencedTableSchemaName="public" validate="true"/>
    </changeSet>
</databaseChangeLog>
