<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="20250702120000-1" author="kartik.agarwal">
        <validCheckSum>ANY</validCheckSum>

        <preConditions onFail="MARK_RAN">
            <tableExists schemaName="contract" tableName="onboarding_reminder" />
            <not>
                <columnExists schemaName="contract" tableName="onboarding_reminder" columnName="editable_template_params" />
            </not>
        </preConditions>

        <comment>Add editable_template_params JSON column to onboarding_reminder table for storing key-value pairs</comment>

        <addColumn schemaName="contract" tableName="onboarding_reminder">
            <column name="editable_template_params" type="jsonb">
                <constraints nullable="true" />
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
