<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="20250213065145-1" author="Sourabha Gokavi">
        <validCheckSum>ANY</validCheckSum>
            
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists schemaName="contract" tableName="onboarding_reminder" />
            </not>
        </preConditions>

        <comment>Create onboarding_reminder table</comment>

        <createTable schemaName="contract" tableName="onboarding_reminder">

            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" primaryKeyName="pk_onboarding_reminder_id" />
            </column>

            <column name="contract_id" type="BIGINT">
                <constraints nullable="false" />
            </column>

            <column name="template_name" type="VARCHAR(255)">
                <constraints nullable="false" />
            </column>

            <column name="count" type="INT">
                <constraints nullable="false" />
            </column>

            <column name="updated_on" type="TIMESTAMP WITHOUT TIME ZONE" />

            <column name="updated_by" type="BIGINT" />

            <column name="created_on" type="TIMESTAMP WITHOUT TIME ZONE" />

            <column name="created_by" type="BIGINT" />

        </createTable>
    </changeSet>

    <changeSet id="20250213065145-2" author="Sourabha Gokavi">
        <validCheckSum>ANY</validCheckSum>
        <comment>Add unique constraint on contract_id and template_name</comment>
        <addUniqueConstraint schemaName="contract"
                             tableName="onboarding_reminder"
                             columnNames="contract_id,template_name"
                             constraintName="uq_onboarding_reminder_contract_template" />
    </changeSet>

</databaseChangeLog>
