spring:
  main:
    banner-mode: OFF
    lazy-initialization: true
  liquibase:
    change-log: classpath:liquibase/master.xml
    enabled: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ${PG_COREDB_URL}
    username: ${PG_COREDB_USER}
    password: ${PG_COREDB_PASSWORD}
    hikari:
      poolName: Hikari
      auto-commit: false
  jpa:
    show-sql: false
    open-in-view: true
    properties:
      hibernate.jdbc.time_zone: UTC
  data:
    jpa:
      repositories:
        bootstrap-mode: lazy
  devtools:
    restart:
      enabled: true
      poll-interval: 2s
      quiet-period: 1s
    livereload:
      enabled: true
      port: 35729

logging:
  level:
    root: info

server:
  port: 8080

merge-dev:
  enabled: true

external:
  core-service:
    host: ${CORE_SERVICE_ADDRESS}
    port: 8081

grpc:
  server:
    port: 9090
    security:
      enabled: false

pigeon:
  client:
    kafka:
      bootstrap-servers: localhost:9092

ops:
  recipient-email: <EMAIL>
  sender-email: Multiplier <<EMAIL>>
  test-recipient-email: <EMAIL>
  customer-success-email: <EMAIL>
  payroll-updates-email: <EMAIL>
  member-onboarding-email: <EMAIL>
  insurance-email: <EMAIL>
  billing-email: <EMAIL>
  support-email: <EMAIL>
onboarding:
  guide-link: https://multiplier-public-assets.s3.ap-southeast-1.amazonaws.com/onboarding/member_onboarding_workflow.pdf
  cc-reminder-emails: <EMAIL>
growthbook:
  refresh-frequency-ms: 15000