spring:
  main:
    banner-mode: OFF
  liquibase:
    change-log: classpath:liquibase/master.xml
    enabled: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ***************************************
    username: coredb
    password: password
    hikari:
      poolName: Hikari
      auto-commit: false
  jpa:
    show-sql: false
    open-in-view: true
    properties:
      hibernate:
        jdbc.time_zone: UTC
        dialect: org.hibernate.dialect.PostgreSQLDialect
        default_schema: contract
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

server:
  port: 8094

grpc:
  server:
    port: 9094
    security:
      enabled: false
      certificate-chain: classpath:certificates/server.crt
      private-key: classpath:certificates/server.key

ops:
  recipient-email: <EMAIL>
  sender-email: Multiplier <<EMAIL>>
  test-recipient-email: <EMAIL>
  customer-success-email: <EMAIL>
  payroll-updates-email: <EMAIL>
  member-onboarding-email: <EMAIL>
  insurance-email: <EMAIL>
  billing-email: <EMAIL>
  support-email: <EMAIL>
onboarding:
  guide-link: https://multiplier-public-assets.s3.ap-southeast-1.amazonaws.com/onboarding/member_onboarding_workflow.pdf
  cc-reminder-emails: <EMAIL>

growthbook:
  refresh-frequency-ms: 15000
