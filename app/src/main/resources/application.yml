server:
  max-http-request-header-size: 1MB

spring:
  application:
    name: contract-onboarding-service
  main:
    banner-mode: OFF
  liquibase:
    change-log: classpath:liquibase/master.xml
    enabled: true
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ${SPRING_DATASOURCE_URL}
    username: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    hikari:
      poolName: Hikari
      auto-commit: false
      max-lifetime: 1800000
      idle-timeout: 300000
      connection-timeout: 30000
      minimum-idle: 2
      maximum-pool-size: 10
      keepalive-time: 120000
  jpa:
    show-sql: false
    open-in-view: true
    properties:
      hibernate:
        jdbc.time_zone: UTC
        dialect: org.hibernate.dialect.PostgreSQLDialect
        default_schema: contract
        jdbc.batch_size: 100
        order_inserts: true
        order_updates: true
        id:
          db_structure_naming_strategy: legacy
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB

feign:
  hystrix:
    enabled: false

grpc:
  client:
    authority-service:
      address: ${GRPC_CLIENT_AUTHORITYSERVICE_ADDRESS}
      enableKeepAlive: true
      keepAliveWithoutCalls: true
      negotiationType: TLS
    core-service:
      address: ${GRPC_CLIENT_CORESERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      max-inbound-message-size: 52428800 # 50 * 1024 * 1024
    contract-service:
      address: ${GRPC_CLIENT_CONTRACTSERVICE_ADDRESS}
      negotiationType: TLS
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      max-inbound-message-size: 52428800 # 50 * 1024 * 1024
    member-service:
      address: ${GRPC_CLIENT_MEMBERSERVICE_ADDRESS}
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      negotiationType: TLS
    country-service:
      address: ${GRPC_CLIENT_COUNTRYSERVICE_ADDRESS}
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      negotiationType: TLS
    company-service:
      address: ${GRPC_CLIENT_COMPANYSERVICE_ADDRESS}
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      negotiationType: TLS
    pay-se:
      address: ${GRPC_CLIENT_PAYSE_ADDRESS}
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      negotiationType: TLS
    payroll-service:
      address: ${GRPC_CLIENT_PAYROLLSERVICE_ADDRESS}
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      negotiationType: TLS
    org-management-service:
      address: ${GRPC_CLIENT_ORGMANAGEMENTSERVICE_ADDRESS}
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      negotiationType: TLS
    timeoff-service:
      address: ${GRPC_CLIENT_TIMEOFFSERVICE_ADDRESS}
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      negotiationType: TLS
    compensation-service:
      address: ${GRPC_CLIENT_COMPENSATIONSERVICE_ADDRESS}
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      negotiationType: TLS
    pigeon-service:
      address: ${GRPC_CLIENT_PIGEONSERVICE_ADDRESS}
      enableKeepAlive: false
      keepAliveWithoutCalls: false
      negotiationType: TLS
  server:
    port: 9090
    max-inbound-metadata-size: 2097152
    security:
      enabled: true
      certificate-chain: classpath:certificates/server.local.crt
      private-key: classpath:certificates/server.local.key

platform:
  base-url: ${PLATFORM_FRONTEND_BASEURL}
  notification:
    slack:
      channel:
        bulk-onboarding: <EMAIL>
  kafka:
    auto-startup: true
    group-id: contract-onboarding
    bootstrap-servers: ${PLATFORM_CONTRACT_ONBOARDING_KAFKA_BOOTSTRAPSERVERS}

kafka:
  bootstrap-servers: ${PLATFORM_CONTRACT_ONBOARDING_KAFKA_BOOTSTRAPSERVERS}
  group-id: contract-onboarding

user-service:
  url: ${PLATFORM_USERSERVICE_BASEURL}
  username: <EMAIL>
  password: aws-secret

pigeon:
  client:
    kafka:
      bootstrap-servers: ${PLATFORM_PIGEON_KAFKA_BOOTSTRAPSERVERS}

ops:
  recipient-email: <EMAIL>
  sender-email: Multiplier <<EMAIL>>
  test-recipient-email: <EMAIL>
  customer-success-email: <EMAIL>
  payroll-updates-email: <EMAIL>
  member-onboarding-email: <EMAIL>
  insurance-email: <EMAIL>
  billing-email: <EMAIL>
  support-email: <EMAIL>
  sales-email: <EMAIL>
  onboarding-specialist-email-test: <EMAIL>
onboarding:
  guide-link: https://multiplier-public-assets.s3.ap-southeast-1.amazonaws.com/onboarding/member_onboarding_workflow.pdf
  cc-reminder-emails: <EMAIL>, <EMAIL>, <EMAIL>
growthbook:
  base-url: ${GROWTHBOOK_BASEURL}
  env-key: ${GROWTHBOOK_ENVKEY}
  refresh-frequency-ms: 15000

docgen:
  url: ${PLATFORM_DOCGEN_BASEURL}

mpl:
  graphql:
    scalar:
      enabled: false
  grpc:
    coroutine:
      enabled: false
