package com.multiplier.contract.onboarding.scheduler

import com.multiplier.contract.onboarding.config.SchedulerConfig
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.reminders.OnboardingReminders
import java.time.LocalDateTime
import java.util.*
import mu.KotlinLogging
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class OnboardingReminderScheduler(
    private val onboardingReminders: OnboardingReminders,
    private val featureFlagService: FeatureFlagService,
) {

    @Scheduled(cron = "0 0 8 * * *", zone = "Asia/Singapore") // At 8am - Everyday
    @SchedulerLock(
        name = "OnboardingServiceContractOnboardingReminders",
        lockAtLeastFor = SchedulerConfig.LOCK_AT_LEAST_FOR,
        lockAtMostFor = SchedulerConfig.LOCK_AT_MOST_FOR)
    fun sendOnboardingReminders() {
        if (featureFlagService.feature(FeatureFlags.SCHEDULER_ENABLED).off) {
            log.info("The onboarding reminder scheduler is toggled off.")
            return
        }

        val lockId = UUID.randomUUID()
        log.info(
            "[{}] scheduled job (lock id:- {}) ran at {} - START",
            "ContractOnboardingReminders",
            lockId,
            LocalDateTime.now())

        onboardingReminders.sendOnboardingRemindersBeforePayrollCutoffDate(null)
        onboardingReminders.sendOnboardingRemindersBeforeContractStartDate(null)

        log.info(
            "[{}] scheduled job (lock id:- {}) ran at {} - Done",
            "ContractOnboardingReminders",
            lockId,
            LocalDateTime.now())
    }
}
