package com.multiplier.contract.onboarding.service.bulk

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.creation.*
import com.multiplier.contract.onboarding.service.bulk.data.*
import com.multiplier.contract.onboarding.service.bulk.dataSpec.*
import com.multiplier.contract.onboarding.service.bulk.validation.*
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import org.springframework.stereotype.Service

interface BulkOnboardingServiceFactory {

    fun bulkDataSpecsService(): BulkDataSpecsServiceInterface
    fun bulkDataService(): BulkDataServiceInterface
    fun bulkEnrichmentService(): BulkEnrichmentServiceInterface
    fun bulkValidationService(): BulkValidationServiceInterface
    fun bulkCreationService(): BulkCreationServiceInterface
}

@Service
class HrisProfileBulkOnboardingServiceFactory(
    private val bulkDataSpecsService: DataSpecForHRISService,
    private val bulkDataService: DataForHrisProfileService,
    private val bulkEnrichmentService: BulkEnrichmentForHRISService,
    private val bulkValidationService: BulkValidationForHRISService,
    private val bulkCreationService: BulkCreationForHRISService
) : BulkOnboardingServiceFactory {

    override fun bulkDataSpecsService() = bulkDataSpecsService

    override fun bulkDataService() = bulkDataService

    override fun bulkEnrichmentService() = bulkEnrichmentService

    override fun bulkValidationService() = bulkValidationService

    override fun bulkCreationService() = bulkCreationService
}

@Service
class EorBulkOnboardingServiceFactory(
    private val bulkDataSpecsService: DataSpecForEORService,
    private val bulkDataService: DataForEORService,
    private val bulkEnrichmentForEorService: BulkEnrichmentForEorService,
    private val bulkValidationForEORService: BulkValidationForEORService,
    private val bulkCreationService: BulkCreationForEORService,
) : BulkOnboardingServiceFactory {
    override fun bulkDataSpecsService() = bulkDataSpecsService

    override fun bulkDataService() = bulkDataService

    override fun bulkEnrichmentService() = bulkEnrichmentForEorService

    override fun bulkValidationService() = bulkValidationForEORService

    override fun bulkCreationService() = bulkCreationService
}

@Service
class AorBulkOnboardingServiceFactory(
    private val bulkDataSpecsService: DataSpecForAORService,
    private val bulkDataService: DataForAORService,
    private val bulkEnrichmentService: BulkEnrichmentForAORService,
    private val bulkValidationService: BulkValidationForAORService,
    private val bulkCreationService: BulkCreationForAORService
) : BulkOnboardingServiceFactory {

    override fun bulkDataSpecsService() = bulkDataSpecsService

    override fun bulkDataService() = bulkDataService

    override fun bulkEnrichmentService() = bulkEnrichmentService

    override fun bulkValidationService() = bulkValidationService

    override fun bulkCreationService() = bulkCreationService
}

@Service
class FreelancerBulkOnboardingServiceFactory(
    private val bulkDataSpecsService: DataSpecForFreelancerService,
    private val bulkDataService: DataForFreelancerService,
    private val bulkEnrichmentService: BulkEnrichmentForFreelancerService,
    private val bulkValidationService: BulkValidationForFreelancerService,
    private val bulkCreationService: BulkCreationForFreelancerService,
) : BulkOnboardingServiceFactory {

    override fun bulkDataSpecsService() = bulkDataSpecsService

    override fun bulkDataService() = bulkDataService

    override fun bulkEnrichmentService() = bulkEnrichmentService

    override fun bulkValidationService() = bulkValidationService

    override fun bulkCreationService() = bulkCreationService
}

@Service
class BulkOnboardingServiceFactoryCreator(
    private val hrisProfileBulkOnboardingServiceFactory: HrisProfileBulkOnboardingServiceFactory,
    private val eorBulkOnboardingServiceFactory: EorBulkOnboardingServiceFactory,
    private val aorBulkOnboardingServiceFactory: AorBulkOnboardingServiceFactory,
    private val freelancerBulkOnboardingServiceFactory: FreelancerBulkOnboardingServiceFactory
) {

    fun getServiceFactory(context: BulkOnboardingContext): BulkOnboardingServiceFactory {
        return when (context) {
            BulkOnboardingContext.HRIS_PROFILE_DATA -> hrisProfileBulkOnboardingServiceFactory
            BulkOnboardingContext.EOR -> eorBulkOnboardingServiceFactory
            BulkOnboardingContext.AOR -> aorBulkOnboardingServiceFactory
            BulkOnboardingContext.FREELANCER -> freelancerBulkOnboardingServiceFactory
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Unsupported context: $context", context = mapOf("context" to context))
        }
    }
}
