package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.bulk.BulkContractServiceAdapter
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import org.springframework.stereotype.Service

@Service
class BulkPostOnboardingService(
    private val bulkContractServiceAdapter: BulkContractServiceAdapter
) {

    fun triggerPostOnboardingActions(
        contractIds: List<Long>,
        options: BulkOnboardingOptions
    ): List<String> {
        when (options.context) {
            BulkOnboardingContext.HRIS_PROFILE_DATA,
            BulkOnboardingContext.EOR,
            BulkOnboardingContext.AOR,
            BulkOnboardingContext.FREELANCER -> {
                return bulkContractServiceAdapter.triggerPostOnboardingActions(contractIds, options)
            }
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Trigger post onboarding actions for ${options.context} not supported yet",
                    context = mapOf("context" to options.context))
        }
    }
}
