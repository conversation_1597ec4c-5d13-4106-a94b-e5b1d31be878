package com.multiplier.contract.onboarding.service.bulk.data

import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.PageRequest
import org.springframework.stereotype.Service

@Service
class DataForFreelancerService : BulkDataServiceInterface {

    override fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?,
    ): List<EmployeeDataChunk> {
        // no data retrieval support for global payroll yet
        return emptyList()
    }
}
