package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.BulkContract.ValidateCreateContractInputsRequest
import com.multiplier.contract.schema.contract.BulkContractServiceGrpc
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.country.schema.Country
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkContractServiceAdapter {

    @GrpcClient("contract-service")
    private lateinit var bulkService: BulkContractServiceGrpc.BulkContractServiceBlockingStub

    fun validateContractCreateInputs(
        inputs: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<ContractOuterClass.CreateContractInput>> {
        val requestBuilder =
            ValidateCreateContractInputsRequest.newBuilder()
                .addAllInputs(inputs.map { it.toGrpc() })
                .setContext(options.context.name)
        options.countryCode?.let {
            requestBuilder.setCountry(Country.GrpcCountryCode.valueOf(it.name))
        }
        options.companyId?.let { requestBuilder.setCompanyId(it) }
        options.entityId?.let { requestBuilder.setLegalEntityId(it) }
        val result = bulkService.validateCreateContractInputs(requestBuilder.build())

        return result.resultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
                additionalInputProperties = it.additionalInputPropertiesMap)
        }
    }

    fun activateContracts(contractIds: List<Long>) {
        if (contractIds.isEmpty()) {
            return
        }
        try {
            val request =
                BulkContract.ActivateContractsRequest.newBuilder()
                    .addAllContractIds(contractIds)
                    .build()

            val response = bulkService.activateContracts(request)

            response.resultsList.forEach {
                if (it.success) {
                    log.info { "Successfully activated contract: ${it.contractId}" }
                } else {
                    log.warn {
                        "Failed to activate contract: ${it.contractId}, errors: ${it.errorMessage}"
                    }
                }
            }
        } catch (e: Exception) {
            log.error(e) { "Failed to activate contracts: $contractIds" }
            throw RuntimeException(
                "Contract activation failed due to an internal error: ${e.message}", e)
        }
    }

    fun sendContractToContractor(contractIds: List<Long>): List<String> {
        if (contractIds.isEmpty()) {
            return emptyList()
        }

        try {
            log.info { "Processing batch of ${contractIds.size} contracts: $contractIds" }
            val request =
                BulkContract.SendContractToContractorRequest.newBuilder()
                    .addAllContractIds(contractIds)
                    .build()

            val response = bulkService.sendContractToContractor(request)

            response.resultsList
                .filter { it.success }
                .map { it.contractId }
                .takeIf { it.isNotEmpty() }
                ?.let { log.info { "Successfully sent contracts to contractor: $it" } }

            return response.resultsList
                .filter { !it.success }
                .map {
                    "Failed to send contract to contractor for contract id: ${it.contractId}, error: ${it.errorMessage}"
                }
        } catch (e: Exception) {
            log.error(e) { "Failed to send batch of contracts to contractor: $contractIds" }
            return listOf(
                "Failed to send batch of contracts to contractor, internal error: ${e.message}")
        }
    }

    fun createContracts(
        inputs: List<CreationInput<ContractOuterClass.CreateContractInput>>,
        options: BulkOnboardingOptions
    ): List<CreationResult> {
        if (inputs.isEmpty()) {
            return emptyList()
        }

        return try {
            val response =
                bulkService.createContracts(
                    BulkContract.CreateContractsRequest.newBuilder()
                        .addAllInputs(
                            inputs.map {
                                it.data
                                    .toBuilder()
                                    .setRequestId(it.requestId)
                                    .setMemberId(
                                        requireNotNull(it.memberId) {
                                            "Member ID for contract upsert must not be null"
                                        })
                                    .build()
                            })
                        .setContext(options.context.name)
                        .build())
            response.resultsList.map {
                CreationResult(
                    requestId = it.requestId,
                    success = it.success,
                    upsertedIds = listOf(it.contractId).filter { id -> id > 0 },
                    errors = it.errorsList,
                )
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to create contracts for bulk onboarding" }
            inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Upsert contract failed due to an internal error: unknown exception occurred")
            }
        }
    }

    fun triggerPostOnboardingActions(
        contractIds: List<Long>,
        options: BulkOnboardingOptions
    ): List<String> {
        if (contractIds.isEmpty()) {
            log.info { "No contracts to trigger post onboarding actions" }
            return emptyList()
        }

        log.info { "Triggering post onboarding actions for contracts: $contractIds" }
        return try {
            val response =
                bulkService.triggerPostOnboardingActions(
                    BulkContract.TriggerPostOnboardingActionsRequest.newBuilder()
                        .addAllContractIds(contractIds)
                        .setContext(options.context.name)
                        .build())
            response.resultsList
                .filter { it.actionTriggered && it.success }
                .map { it.contractId }
                .takeIf { it.isNotEmpty() }
                ?.let { log.info { "Triggered post onboarding actions for contracts: $it" } }
            response.resultsList
                .filter { !it.actionTriggered }
                .map { it.contractId }
                .takeIf { it.isNotEmpty() }
                ?.let { log.info { "No post onboarding actions triggered for contracts: $it" } }
            response.resultsList
                .filter { it.actionTriggered && !it.success }
                .map {
                    "Failed to trigger post onboarding actions for contract id: ${it.contractId}, errors: ${it.errorsList.joinToString()}"
                }
        } catch (e: Exception) {
            log.warn(e) { "Failed to trigger post onboarding actions" }
            listOf("Fail to trigger post onboarding actions, internal error: ${e.message}")
        }
    }
}

private fun EmployeeData.toGrpc(): BulkContract.CreateContractValidationInput? {
    return BulkContract.CreateContractValidationInput.newBuilder()
        .setRequestId(this.identification.validationId)
        .putAllProperties(this.data)
        .build()
}
