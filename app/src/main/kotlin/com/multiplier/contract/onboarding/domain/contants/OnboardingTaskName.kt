package com.multiplier.contract.onboarding.domain.contants

enum class OnboardingTaskName(val text: String) {
    SIGN_MSA("Sign MSA"),
    COMPANY_COMPLETE_ONBOARDING("Complete onboarding"),
    SEND_CONTRACT("Send contract"),
    SIGN_CONTRACT("Sign contract"),
    PAY_DEPOSIT("Pay deposit"),
    PLATFORM_ONBOARDING("Platform onboarding"),
    KYC_DETAILS_AND_DOCUMENTS("KYC details & documents"),
    PAYROLL_AND_COMPLIANCE_FORMS("Mandatory payroll & compliance forms"),
    EMPLOYEE_ID_AND_WORK_EMAIL("Add work email and employee ID")
}
