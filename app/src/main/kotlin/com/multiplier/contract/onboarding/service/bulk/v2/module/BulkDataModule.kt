package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.ContractDataContext
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.PageRequest
import com.multiplier.contract.onboarding.usecase.ModuleParams

data class DataForContract(val contractIdToEmployeeDataChunk: Map<Long, EmployeeDataChunk>) {

    companion object {
        val EMPTY = DataForContract(emptyMap())
    }
}

interface BulkDataModule {

    fun identifier(): String

    fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> = emptyList()

    fun getContractDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?,
    ): ContractDataContext = ContractDataContext.EMPTY

    fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        contractIdToMemberId: Map<Long, Long>
    ): DataForContract = DataForContract.EMPTY

    fun getFieldData(
        options: BulkOnboardingOptions,
        contractIds: List<Long>
    ): List<EmployeeDataChunk> = emptyList()

    fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>>

    fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2
}
