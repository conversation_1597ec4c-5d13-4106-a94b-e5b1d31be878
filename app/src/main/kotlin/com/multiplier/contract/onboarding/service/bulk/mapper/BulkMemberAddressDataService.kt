package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberAddressServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.AddressDetail
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData.AddressCitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData.AddressCountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData.AddressLine1Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData.AddressLine2Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData.AddressPostalCodeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData.AddressProvinceSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData.AddressStateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData.AddressZipCodeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressCitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressCountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressLine1Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressLine2Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressPostalCodeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressStateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressCitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressCountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressLine1Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressLine2Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressPostalCodeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PermanentAddressStateSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.UpsertAddressInput
import org.springframework.stereotype.Service

@Service
class BulkMemberAddressDataService(
    private val memberServiceAdapter: MemberServiceAdapter,
    private val bulkMemberAddressServiceAdapter: BulkMemberAddressServiceAdapter,
) {

    object AddressLegalData {
        val AddressLine1Spec =
            DataSpec(key = "address.line1", type = DataSpecType.TEXT, mandatory = false)
        val AddressLine2Spec =
            DataSpec(key = "address.line2", type = DataSpecType.TEXT, mandatory = false)
        val AddressStateSpec =
            DataSpec(key = "address.state", type = DataSpecType.TEXT, mandatory = false)
        val AddressCitySpec =
            DataSpec(key = "address.city", type = DataSpecType.TEXT, mandatory = false)
        val AddressProvinceSpec =
            DataSpec(key = "address.province", type = DataSpecType.TEXT, mandatory = false)
        val AddressCountrySpec =
            DataSpec(key = "address.country", type = DataSpecType.TEXT, mandatory = false)
        val AddressPostalCodeSpec =
            DataSpec(key = "address.postalCode", type = DataSpecType.TEXT, mandatory = false)
        val AddressZipCodeSpec =
            DataSpec(key = "address.zipcode", type = DataSpecType.TEXT, mandatory = false)
    }

    fun validate(
        dataForMemberValidation: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<UpsertAddressInput>> =
        bulkMemberAddressServiceAdapter.validateUpsertAddresses(dataForMemberValidation, options)

    fun upsertAddresses(
        inputs: List<ValidInput<UpsertAddressInput>>,
        memberContext: MemberContext,
        options: BulkOnboardingOptions
    ): List<String> {
        val upsertAddressInputs =
            inputs
                .filterNot { it.input == UpsertAddressInput.getDefaultInstance() }
                .map {
                    CreationInput(
                        requestId = it.validationId,
                        memberId = memberContext.getMemberId(it.validationId),
                        data = it.input)
                }

        val results =
            bulkUpsert(
                "Upsert address details",
                upsertAddressInputs,
                options,
                CreationInput.refMemberId,
            ) { addressInputs, _ ->
                bulkMemberAddressServiceAdapter.upsertAddresses(addressInputs)
            }

        return results.flatMap { it.errors }
    }

    fun getDataForSpecs(dataSpecs: List<DataSpec>, memberIds: Set<Long>): MemberDataContext {
        val memberAddress = memberServiceAdapter.getMemberAddressDetails(memberIds)
        val dataChunk = memberAddress.associate { it.memberId to it.toEmployeeDataChunk(dataSpecs) }
        return MemberDataContext(dataChunk)
    }
}

private fun AddressDetail.toEmployeeDataChunk(dataSpecs: List<DataSpec>): EmployeeDataChunk {
    val data =
        dataSpecs
            .associate {
                it.key to
                    when (it.key) {
                        AddressLine1Spec.key,
                        CurrentAddressLine1Spec.key -> this.currentAddress.line1
                        AddressLine2Spec.key,
                        CurrentAddressLine2Spec.key -> this.currentAddress.line2
                        AddressCitySpec.key,
                        CurrentAddressCitySpec.key -> this.currentAddress.city
                        AddressStateSpec.key,
                        CurrentAddressStateSpec.key -> this.currentAddress.state
                        AddressCountrySpec.key,
                        CurrentAddressCountrySpec.key -> this.currentAddress.country.name
                        AddressPostalCodeSpec.key,
                        CurrentAddressPostalCodeSpec.key -> this.currentAddress.postalCode
                        AddressProvinceSpec.key -> this.currentAddress.province
                        AddressZipCodeSpec.key -> this.currentAddress.zipCode
                        PermanentAddressLine1Spec.key -> this.permanentAddress?.line1
                        PermanentAddressLine2Spec.key -> this.permanentAddress?.line2
                        PermanentAddressCitySpec.key -> this.permanentAddress?.city
                        PermanentAddressStateSpec.key -> this.permanentAddress?.state
                        PermanentAddressCountrySpec.key -> this.permanentAddress?.country?.name
                        PermanentAddressPostalCodeSpec.key -> this.permanentAddress?.postalCode
                        else -> null
                    }
            }
            .filterValues { it != null }
            .mapValues { requireNotNull(it.value) }
    return EmployeeDataChunk(data)
}
