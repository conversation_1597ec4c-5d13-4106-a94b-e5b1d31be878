package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkComplianceServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.ComplianceParam
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ComplianceParamPeriodUnit
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.schema.contract.BulkContract
import org.springframework.stereotype.Service

@Service
class BulkComplianceDataService(
    private val complianceServiceAdapter: BulkComplianceServiceAdapter,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val countryServiceAdapter: CountryServiceAdapter,
) {

    companion object {

        private val COMPLIANCE_TIME_UNITS =
            ComplianceParamPeriodUnit.values()
                .filter { it != ComplianceParamPeriodUnit.NONE }
                .map { it.name }

        val NoticeAfterProbationValueSpec =
            DataSpec(
                key = "noticeAfterProbation.value", type = DataSpecType.NUMBER, mandatory = false)
        val NoticeAfterProbationUnitSpec =
            DataSpec(
                key = "noticeAfterProbation.unit",
                type = DataSpecType.SELECT,
                allowedValues = COMPLIANCE_TIME_UNITS)
        val ProbationValueSpec =
            DataSpec(key = "probationPolicy.value", type = DataSpecType.NUMBER, mandatory = false)
        val ProbationUnitSpec =
            DataSpec(
                key = "probationPolicy.unit",
                type = DataSpecType.SELECT,
                allowedValues = COMPLIANCE_TIME_UNITS)
        val NonCompeteValueSpec =
            DataSpec(key = "nonCompete.value", type = DataSpecType.NUMBER, mandatory = false)
        val NonCompeteUnitSpec =
            DataSpec(
                key = "nonCompete.unit",
                type = DataSpecType.SELECT,
                allowedValues = COMPLIANCE_TIME_UNITS)
        val NonSolicitValueSpec =
            DataSpec(key = "nonSolicit.value", type = DataSpecType.NUMBER, mandatory = false)
        val NonSolicitUnitSpec =
            DataSpec(
                key = "nonSolicit.unit",
                type = DataSpecType.SELECT,
                allowedValues = COMPLIANCE_TIME_UNITS)
    }

    fun getDataSpecs(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.HRIS_PROFILE_DATA,
            BulkOnboardingContext.AOR ->
                listOf(NoticeAfterProbationValueSpec, NoticeAfterProbationUnitSpec)
            BulkOnboardingContext.FREELANCER -> emptyList()
            BulkOnboardingContext.EOR -> dataSpecsForEOR(onboardingOptions)
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Org management data specs query for ${onboardingOptions.context} not supported yet",
                    context = mapOf("context" to onboardingOptions.context))
        }
    }

    fun getDataForSpecs(dataSpecs: List<DataSpec>, contractIds: Set<Long>): ContractDataContext {
        val complianceParams = contractServiceAdapter.getComplianceParamsByContractIds(contractIds)
        val memberIdToComplianceData =
            complianceParams.mapValues { it.value.toEmployeeDataChunk(dataSpecs) }

        // TODO transform unit
        return ContractDataContext(memberIdToComplianceData)
    }

    private fun List<ComplianceParam>.toEmployeeDataChunk(
        dataSpecs: List<DataSpec>,
    ): EmployeeDataChunk {
        val keyToComplianceParam = this.associateBy { it.key }

        val data =
            dataSpecs
                .associate { spec ->
                    spec.key to
                        when (spec.key) {
                            NoticeAfterProbationValueSpec.key,
                            ProbationValueSpec.key,
                            NonCompeteValueSpec.key,
                            NonSolicitValueSpec.key ->
                                getComplianceParamValue(keyToComplianceParam, spec)
                            NoticeAfterProbationUnitSpec.key,
                            ProbationUnitSpec.key,
                            NonCompeteUnitSpec.key,
                            NonSolicitUnitSpec.key ->
                                getComplianceParamUnit(keyToComplianceParam, spec)
                            else -> null
                        }
                }
                .filterValues { !it.isNullOrBlank() }
                .mapValues { requireNotNull(it.value) }

        return EmployeeDataChunk(data)
    }

    private fun getComplianceParamValue(
        keyToComplianceParam: Map<String, ComplianceParam>,
        spec: DataSpec
    ): String? {
        return keyToComplianceParam[spec.complianceKey()]?.value?.toString()
    }

    private fun getComplianceParamUnit(
        keyToComplianceParam: Map<String, ComplianceParam>,
        spec: DataSpec
    ): String? {
        return keyToComplianceParam[spec.complianceKey()]?.unit
    }

    private fun DataSpec.complianceKey() = key.substringBeforeLast(".")

    private fun DataSpec.compliancePostfix() = key.substringAfterLast(".")

    private fun dataSpecsForEOR(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        val complianceDefinitionForCountry =
            countryServiceAdapter.getComplianceDefinitionForCountry(
                onboardingOptions.countryCode, ContractType.EMPLOYEE)

        val paramDefinitionsList =
            complianceDefinitionForCountry?.paramDefinitionsList?.filter { it.enabled }?.toList()

        return listOf(
                ProbationValueSpec,
                ProbationUnitSpec,
                NoticeAfterProbationValueSpec,
                NoticeAfterProbationUnitSpec,
                NonCompeteValueSpec,
                NonCompeteUnitSpec,
                NonSolicitValueSpec,
                NonSolicitUnitSpec)
            .mapNotNull { dataSpec ->
                val complianceKey = dataSpec.complianceKey()
                val complianceDefinition =
                    paramDefinitionsList?.firstOrNull {
                        it.param.complianceParamPeriodLimit.key == complianceKey
                    }

                if (complianceDefinition == null) {
                    null
                } else {
                    when (dataSpec.compliancePostfix()) {
                        "value" -> dataSpec.copy(mandatory = complianceDefinition.required)
                        "unit" ->
                            dataSpec.copy(
                                mandatory = complianceDefinition.required,
                                allowedValues =
                                    listOf(
                                        complianceDefinition.param.complianceParamPeriodLimit.unit
                                            .name))
                        else -> null
                    }
                }
            }
    }

    fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<BulkContract.UpdateComplianceInput>> {
        return complianceServiceAdapter.validateUpdateComplianceInputs(employeeData, options)
    }

    fun updateContractCompliance(
        inputs: List<ValidInput<BulkContract.UpdateComplianceInput>>,
        options: BulkOnboardingOptions,
        contractContext: ContractContext
    ): List<String> {
        val updateContractComplianceInputs =
            inputs
                .filterNot { it.input == BulkContract.UpdateComplianceInput.getDefaultInstance() }
                .map {
                    CreationInput(
                        requestId = it.validationId,
                        contractId = contractContext.getContractId(it.validationId),
                        data = it.input)
                }

        val results =
            bulkUpsert(
                "Update compliance",
                updateContractComplianceInputs,
                options,
                CreationInput.refContractId) { complianceInputs, onboardingOptions ->
                    complianceServiceAdapter.updateCompliances(complianceInputs, onboardingOptions)
                }

        return results.flatMap { it.errors }
    }
}

data class CompensationDataContext(val contractIdToCompensationMap: Map<Long, EmployeeDataChunk>)
