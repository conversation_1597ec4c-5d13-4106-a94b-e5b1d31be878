package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberEducationServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.EducationDetail
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolDegreeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolGpaSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolGradeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastSchoolYearOfPassingSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.UpsertEducationDetailsInput
import org.springframework.stereotype.Service

@Service
class BulkMemberEducationDataService(
    private val bulkEducationAdapter: BulkMemberEducationServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
) {

    fun validate(
        dataForMemberValidation: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): List<GrpcValidationResult<UpsertEducationDetailsInput>> =
        bulkEducationAdapter.validateUpsertEducationDetails(dataForMemberValidation, options)

    fun upsertEducationDetails(
        inputs: List<ValidInput<UpsertEducationDetailsInput>>,
        memberContext: MemberContext,
        options: BulkOnboardingOptions,
    ): List<String> {
        val upsertEducationInputs =
            inputs
                .filterNot { it.input == UpsertEducationDetailsInput.getDefaultInstance() }
                .map {
                    CreationInput(
                        requestId = it.validationId,
                        memberId = memberContext.getMemberId(it.validationId),
                        data = it.input)
                }

        val results =
            bulkUpsert(
                "Upsert education details",
                upsertEducationInputs,
                options,
                CreationInput.refMemberId) { educationInputs, _ ->
                    bulkEducationAdapter.upsertEducationDetails(educationInputs)
                }

        return results.flatMap { it.errors }
    }

    fun getDataForSpecs(dataSpecs: List<DataSpec>, memberIds: Set<Long>): MemberDataContext {
        val result = memberServiceAdapter.getMemberEducationDetails(memberIds)
        val memberIdToEducationData =
            result.associate { it.memberId to it.toMemberDataChunk(dataSpecs) }
        return MemberDataContext(memberIdToEducationData)
    }

    private fun EducationDetail.toMemberDataChunk(dataSpecs: List<DataSpec>): EmployeeDataChunk {
        val data =
            dataSpecs
                .associate {
                    it.key to
                        when (it.key) {
                            LastSchoolNameSpec.key -> this.lastSchoolName
                            LastSchoolDegreeSpec.key -> this.lastSchoolDegree
                            LastSchoolYearOfPassingSpec.key ->
                                this.lastSchoolYearOfPassing.toString()
                            LastSchoolGpaSpec.key ->
                                when (val gpa = this.lastSchoolGpa) {
                                    0F -> "" // by default, if gpa has no value, it is 0.0F
                                    else -> gpa.toString()
                                }
                            LastSchoolGradeSpec.key -> this.lastSchoolGrade
                            else -> null
                        }
                }
                .filterValues { it != null }
                .mapValues { requireNotNull(it.value) }
        return EmployeeDataChunk(data)
    }
}
