package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractFilters
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulkupload.ValidationIdsOfContract
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.Capability
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.member.schema.MemberCreateInput
import com.multiplier.member.schema.UpdateMemberLegalDataInput
import com.multiplier.member.schema.UpsertAddressInput
import com.multiplier.member.schema.UpsertBankDetailsInput
import com.multiplier.member.schema.UpsertEducationDetailsInput
import com.multiplier.member.schema.UpsertEmergencyContactInput
import com.multiplier.member.schema.UpsertPreviousEmployerDetailsInput
import com.multiplier.orgmanagement.schema.BulkAPI.UpsertOrgManagementDataInput
import org.springframework.stereotype.Service

data class ValidationResults(
    val employeeValidationResults: EmployeeValidationResults,
    val validationReport: BulkValidationReport
)

data class EmployeeValidationResults(
    val members: List<GrpcValidationResult<MemberCreateInput>>,
    val contracts: List<GrpcValidationResult<ContractOuterClass.CreateContractInput>>,
    val compensations: List<GrpcValidationResult<CompensationOuterClass.CreateCompensationInput>> =
        emptyList(),
    val newCompensations: List<GrpcValidationResult<Map<String, String>>> = emptyList(),
    val legalData: List<GrpcValidationResult<UpdateMemberLegalDataInput>> = emptyList(),
    val bankData: List<GrpcValidationResult<UpsertBankDetailsInput>> = emptyList(),
    val compliances: List<GrpcValidationResult<BulkContract.UpdateComplianceInput>> = emptyList(),
    val orgManagementData: List<GrpcValidationResult<UpsertOrgManagementDataInput>> = emptyList(),
    val emergencyData: List<GrpcValidationResult<UpsertEmergencyContactInput>> = emptyList(),
    val educationData: List<GrpcValidationResult<UpsertEducationDetailsInput>> = emptyList(),
    val previousEmployerData: List<GrpcValidationResult<UpsertPreviousEmployerDetailsInput>> =
        emptyList(),
    val addressData: List<GrpcValidationResult<UpsertAddressInput>> = emptyList(),
    val validationIdsOfContracts: List<ValidationIdsOfContract> = emptyList(),
) {
    companion object {
        val EMPTY =
            EmployeeValidationResults(
                members = emptyList(),
                contracts = emptyList(),
                compensations = emptyList(),
                newCompensations = emptyList(),
                legalData = emptyList(),
                bankData = emptyList(),
                compliances = emptyList(),
                orgManagementData = emptyList(),
                emergencyData = emptyList(),
                educationData = emptyList(),
                previousEmployerData = emptyList(),
                addressData = emptyList(),
            )
    }

    fun getAllValidationResults(): List<GrpcValidationResult<*>> =
        members +
            contracts +
            compensations +
            newCompensations +
            legalData +
            bankData +
            compliances +
            orgManagementData +
            emergencyData +
            educationData +
            previousEmployerData +
            addressData
}

data class GenericValidationResult(
    val errors: List<GenericValidationError>,
    val warnings: List<GenericValidationWarning> = emptyList(),
) {
    companion object {
        val EMPTY = GenericValidationResult(emptyList(), emptyList())
    }
}

@Service
class BulkValidationHelper(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val companyServiceAdapter: CompanyServiceAdapter
) {
    fun genericValidate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): GenericValidationResult {
        val noEmployeesError = validateNoEmployees(employeeData)
        validateCompanyLegalEntityOrThrow(options)

        return GenericValidationResult(errors = listOfNotNull(noEmployeesError))
    }

    fun validateCompanyLegalEntityOrThrow(options: BulkOnboardingOptions) {
        if (options.context != BulkOnboardingContext.GLOBAL_PAYROLL) return

        val legalEntity =
            companyServiceAdapter.getLegalEntity(options.entityId)
                ?: error("Legal entity with id ${options.entityId} not found")
        if (legalEntity.companyId != options.companyId) {
            error(
                "Legal entity with id ${options.entityId} doesn't belong to company with id ${options.companyId}")
        }
        if (legalEntity.capabilitiesList.none {
            it == Capability.GROSS_TO_NET.name || it == Capability.SALARY_DISBURSEMENT.name
        }) {
            error(
                "Legal entity with id ${options.entityId} doesn't have capability to process global payroll")
        }
    }

    private fun validateNoEmployees(employeeData: List<EmployeeData>): GenericValidationError? {
        return if (employeeData.isEmpty()) {
            GenericValidationError(error = "No employee data found")
        } else {
            null
        }
    }

    fun validateDuplicateEmployeeIds(
        employeeData: List<EmployeeData>
    ): List<GrpcValidationResult<ContractOuterClass.CreateContractInput>> {
        val nonUnique =
            employeeData
                .groupBy { it.identification.employeeId }
                .filter { it.value.size > 1 }
                .flatMap { it.value }
        return nonUnique.map {
            GrpcValidationResult(
                success = false,
                errors =
                    listOf(
                        "this employee id [${it.identification.employeeId}] is used multiple times in this sheet"),
                validationId = it.identification.validationId,
                input = null,
            )
        }
    }

    fun validateExistingEmployeeIds(
        employeeData: List<EmployeeData>,
        companyId: Long,
    ): List<GrpcValidationResult<ContractOuterClass.CreateContractInput>> {
        val existingEmployeeIds =
            contractServiceAdapter
                .getContractsBy(ContractFilters(companyIds = listOf(companyId)))
                .filter { !it.employeeId.isNullOrEmpty() }
                .map { it.employeeId }

        return employeeData
            .filter { existingEmployeeIds.contains(it.identification.employeeId) }
            .map {
                GrpcValidationResult(
                    success = false,
                    errors =
                        listOf(
                            "this employee id [${it.identification.employeeId}] is already taken"),
                    validationId = it.identification.validationId,
                    input = null,
                )
            }
    }
}
