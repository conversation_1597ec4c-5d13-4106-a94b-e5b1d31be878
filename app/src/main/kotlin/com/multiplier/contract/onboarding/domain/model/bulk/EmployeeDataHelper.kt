package com.multiplier.contract.onboarding.domain.model.bulk

class EmployeeDataHelper {
    companion object {
        /**
         * Merge employee data by a key selector. All data which yields the same key will be merged
         * into one.
         */
        fun mergeEmployeeData(
            employeeData: List<EmployeeData>,
            keySelector: (EmployeeData) -> String,
        ): List<EmployeeData> {
            return employeeData
                .groupBy { keySelector(it) }
                .map { (_, dataList) ->
                    dataList.reduce { acc, employeeData -> mergeEmployeeData(acc, employeeData) }
                }
        }

        /**
         * For each item in the primary list, find the corresponding item in the enrichment list and
         * merge data of the enrichment item into the primary item.
         */
        fun enrichEmployeeData(
            employeeData: List<EmployeeData>,
            enrichment: List<EmployeeData>,
            keySelector: (EmployeeData) -> String,
        ): List<EmployeeData> {
            return employeeData.map { primary ->
                val enrichmentItem =
                    enrichment.firstOrNull { keySelector(it) == keySelector(primary) }
                if (enrichmentItem != null) {
                    mergeEmployeeData(primary, enrichmentItem)
                } else primary
            }
        }

        private fun mergeEmployeeData(acc: EmployeeData, employeeData: EmployeeData) =
            acc.copy(
                identification =
                    EmployeeIdentification(
                        employeeId =
                            getNotEmpty(
                                acc.identification.employeeId,
                                employeeData.identification.employeeId),
                        contractId = acc.identification.contractId
                                ?: employeeData.identification.contractId,
                        firstName =
                            getNotEmpty(
                                acc.identification.firstName,
                                employeeData.identification.firstName),
                        lastName =
                            getNotEmpty(
                                acc.identification.lastName, employeeData.identification.lastName),
                        email =
                            getNotEmpty(
                                acc.identification.email, employeeData.identification.email),
                        rowNumber =
                            acc.identification.rowNumber, // take row number from the first record
                        rowIdentifier = acc.identification.rowIdentifier),
                data =
                    (acc.data.keys + employeeData.data.keys).associateWith { key ->
                        getNotEmpty(acc.data[key], employeeData.data[key]).orEmpty()
                    })

        private fun getNotEmpty(vararg values: String?): String? =
            values.firstOrNull { !it.isNullOrBlank() }

        val defaultEmployeeDataMergeStrategy: (EmployeeData) -> String = {
            it.identification.employeeId.orEmpty().ifEmpty {
                it.identification.contractId?.toString().orEmpty().ifEmpty {
                    it.identification.rowNumber.toString()
                }
            }
        }
    }
}
