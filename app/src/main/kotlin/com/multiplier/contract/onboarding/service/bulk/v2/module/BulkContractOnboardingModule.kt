package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.Compliance
import com.multiplier.contract.onboarding.domain.model.ContractAgreementType
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.service.reminders.OnboardingNotificationDtoComposer.Companion.CONTRACT_ID
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.country.schema.Country
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkContractOnboardingModule(
    private val onboardingServiceAdapter: OnboardingServiceAdapter,
    private val contractBenefitAdapter: ContractBenefitAdapter,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val featureFlagService: FeatureFlagService,
    private val countryServiceAdapter: CountryServiceAdapter
) : BulkDataModule {

    private val log = KotlinLogging.logger {}

    companion object {
        const val MODULE_NAME = "CONTRACT_ONBOARDING_MODULE"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        // no validation
        return emptyList()
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        return try {
            onboardingServiceAdapter.upsertOnboardings(
                getCompanyOnboardings(bulkCreationResult, options) +
                    getMemberOnboardings(bulkCreationResult, options))
            log.info {
                "Created onboarding entities for contract: ${bulkCreationResult.newContractIds}"
            }
            bulkCreationResult
        } catch (e: Exception) {
            log.warn(e) {
                "Failed to create onboarding entities for contract ids: ${bulkCreationResult.newContractIds}"
            }
            bulkCreationResult.addErrorsFrom(
                bulkCreationResult.newContractIds
                    .mapNotNull { bulkCreationResult.getRequestIdForContract(it) }
                    .map {
                        CreationResult.error(
                            it,
                            "Create onboarding entity failed due to an internal error: unknown exception occurred")
                    })
        }
    }

    fun getCompanyOnboardings(
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): List<Onboarding> {
        return when (options.context) {
            BulkOnboardingContext.GLOBAL_PAYROLL ->
                buildGlobalPayrollCompanyOnboardings(bulkCreationResult)
            BulkOnboardingContext.FREELANCER ->
                buildFreelancerCompanyOnboardings(bulkCreationResult, options)
            BulkOnboardingContext.AOR -> {
                buildAorCompanyOnboardings(bulkCreationResult, options)
            }
            BulkOnboardingContext.EOR -> {
                buildEorCompanyOnboardings(bulkCreationResult, options)
            }
            else -> {
                emptyList()
            }
        }
    }

    private fun buildGlobalPayrollCompanyOnboardings(
        bulkCreationResult: BulkCreationResultV2
    ): List<Onboarding> {
        return bulkCreationResult.newContractIds.map {
            Onboarding(
                contractId = it,
                experience = "company",
                status = OnboardingStatus.MEMBER_VERIFICATION_COMPLETED,
                currentStep = OnboardingStep.ONBOARDING_ACTIVATION,
                isBulkOnboarded = true)
        }
    }

    private fun buildFreelancerCompanyOnboardings(
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): List<Onboarding> {
        val contractMap = fetchContractsById(bulkCreationResult.newContractIds)

        return bulkCreationResult.newContractIds.map { contractId ->
            val contract =
                contractMap[contractId]
                    ?: throw ErrorCodes.CONTRACT_NOT_FOUND.toBusinessException(
                        "No contract found with id $contractId",
                        context = mapOf(CONTRACT_ID to contractId))
            val countryCode = extractCountryCode(contract, contractId)

            val currentStep = determineFreelancerOnboardingStep(countryCode)

            Onboarding(
                contractId = contractId,
                experience = "company",
                status = OnboardingStatus.DRAFT,
                currentStep = currentStep,
                isBulkOnboarded = true)
        }
    }

    private fun buildAorCompanyOnboardings(
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): List<Onboarding> {
        return bulkCreationResult.newContractIds.map {
            Onboarding(
                contractId = it,
                experience = "company",
                status = OnboardingStatus.CREATED,
                currentStep = OnboardingStep.ONBOARDING_REVIEW,
                isBulkOnboarded = true)
        }
    }

    private fun buildEorCompanyOnboardings(
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): List<Onboarding> {

        val contracts =
            contractServiceAdapter.getContractsBy(
                ContractFilters(contractIds = bulkCreationResult.newContractIds))

        return getOnboardingsForConfirmationStep(contracts)
    }

    fun getOnboardingsForConfirmationStep(
        contracts: List<ContractOuterClass.Contract>
    ): List<Onboarding> {
        return contracts.map { contract ->
            if (isVisaOnlineFlowToBeEnabled(contract) ||
                !isEmployeeWithMultiplierCountryContract(contract)) {
                Onboarding(
                    contractId = contract.id,
                    experience = "company",
                    status = OnboardingStatus.CONTRACT_PREPARING_CONFIRMATION,
                    currentStep = OnboardingStep.ONBOARDING_CONTRACT_PREPARING_CONFIRMATION,
                    isBulkOnboarded = true)
            } else {
                Onboarding(
                    contractId = contract.id,
                    experience = "company",
                    status = OnboardingStatus.CREATED,
                    currentStep = OnboardingStep.ONBOARDING_REVIEW,
                    isBulkOnboarded = true)
            }
        }
    }

    private fun isVisaOnlineFlowToBeEnabled(
        contract: ContractOuterClass.Contract,
    ): Boolean {
        return !isResident(contract.workStatus) &&
            isEOR(contract.type) &&
            isVisaWorkFlowEnabled(contract.country, contract.companyId)
    }

    fun isEmployeeWithMultiplierCountryContract(contract: ContractOuterClass.Contract): Boolean {
        val complianceByContractId =
            contractServiceAdapter.getComplianceByContractIds(setOf(contract.id)).associateBy {
                it.contractId
            }

        return isEOR(contract.type) &&
            isOnboardingEnabledForCountry(contract.country) &&
            (isResident(contract.workStatus) ||
                (isVisaOnlineFlowToBeEnabled(contract) &&
                    isMultiplierContractSelected(complianceByContractId[contract.id])))
    }

    private fun isOnboardingEnabledForCountry(countryCode: String) =
        countryServiceAdapter.isOnboardingEnabled(
            Country.CountryCodeState.newBuilder()
                .setCountryCode(Country.GrpcCountryCode.valueOf(countryCode))
                .setStateCode("")
                .build())

    private fun isResident(workStatus: ContractOuterClass.CountryWorkStatus?): Boolean =
        ContractOuterClass.CountryWorkStatus.RESIDENT == workStatus

    fun isMultiplierContractSelected(compliance: Compliance?): Boolean =
        compliance != null && compliance.agreementType === ContractAgreementType.MULTIPLIER_TEMPLATE

    private fun isEOR(contractType: ContractOuterClass.ContractType?): Boolean =
        ContractOuterClass.ContractType.EMPLOYEE == contractType

    private fun isVisaWorkFlowEnabled(country: String, companyId: Long): Boolean {
        return featureFlagService.isFeatureOn(
            FeatureFlags.ONBOARDING_VISA_WORKFLOW,
            mapOf(
                FeatureFlags.Params.COMPANY to companyId,
                FeatureFlags.Params.ONBOARDING_COUNTRY to country,
            ))
    }

    private fun fetchContractsById(
        contractIds: List<Long>
    ): Map<Long, ContractOuterClass.Contract> {
        val contracts =
            contractServiceAdapter.getContractsBy(ContractFilters(contractIds = contractIds))
        return contracts.associateBy { it.id }
    }

    private fun extractCountryCode(
        contract: ContractOuterClass.Contract,
        contractId: Long
    ): CountryCode {
        val country = contract.country

        return enumValues<CountryCode>().find { it.name == country.uppercase() }
            ?: throw ErrorCodes.INVALID_COUNTRY.toBusinessException(
                "Invalid country code '$country' for contract ID: $contractId")
    }

    private fun determineFreelancerOnboardingStep(country: CountryCode): OnboardingStep {
        return if (countryHasBenefitStepForFreelancer(country)) {
            OnboardingStep.DEFINITION_BENEFITS_DETAILS
        } else {
            OnboardingStep.DEFINITION_COMPENSATION_DETAILS
        }
    }

    fun countryHasBenefitStepForFreelancer(country: CountryCode): Boolean {
        return !contractBenefitAdapter.getBenefitRestrictedCountryListFR().contains(country)
    }

    fun getMemberOnboardings(
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): List<Onboarding> {
        return when (options.context) {
            BulkOnboardingContext.GLOBAL_PAYROLL -> {
                bulkCreationResult.newContractIds.map {
                    Onboarding(
                        contractId = it,
                        experience = "member",
                        status = OnboardingStatus.MEMBER_VERIFICATION_COMPLETED,
                        currentStep = OnboardingStep.ONBOARDING_ACTIVATION,
                        isBulkOnboarded = true)
                }
            }
            else -> {
                emptyList()
            }
        }
    }
}
