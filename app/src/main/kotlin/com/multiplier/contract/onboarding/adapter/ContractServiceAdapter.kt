package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.domain.model.ComplianceType
import com.multiplier.contract.onboarding.service.toGrpcDate
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.PageRequest
import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import com.multiplier.contract.schema.compensation.CompensationOuterClass.IdsMessage
import com.multiplier.contract.schema.compensation.CompensationServiceGrpc.CompensationServiceBlockingStub
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.*
import com.multiplier.contract.schema.contract.ContractServiceGrpc.ContractServiceBlockingStub
import com.multiplier.contract.schema.payable.CompanyPayable
import com.multiplier.contract.schema.payable.ContractPayable
import com.multiplier.contract.schema.payable.ContractPayable.PaymentAccountRequirementsRequest
import com.multiplier.contract.schema.payable.ContractPayable.PersonalPaymentAccountRequirementsRequestParam
import com.multiplier.contract.schema.payable.ContractPayableServiceGrpc.ContractPayableServiceBlockingStub
import com.multiplier.contract.schema.payable.Invoice.InvoiceStatus
import com.multiplier.country.schema.Country
import com.multiplier.grpc.common.v1.PaginationRequest
import java.time.LocalDateTime
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

data class DateRange(
    val from: LocalDateTime,
    val to: LocalDateTime,
)

data class ContractFilters(
    val type: ContractType? = null,
    val types: List<ContractType> = emptyList(),
    val status: ContractStatus? = null,
    val statuses: List<ContractStatus> = emptyList(),
    val startDateRange: DateRange? = null,
    val companyIds: Collection<Long> = emptyList(),
    val countryCode: String? = null,
    val contractIds: List<Long> = emptyList(),
    val legalEntityId: Long? = null,
    val pageRequest: PageRequest? = null,
)

interface ContractServiceAdapter {

    fun getNonDeletedNonEndedContract(contractId: Long): Contract?

    fun getNonDeletedNonEndedContracts(contractIds: Set<Long>): List<Contract>

    fun getDepositPaidContractIds(contractIds: Set<Long>): Set<Long>

    fun getContractsBy(filters: ContractFilters): List<Contract>

    fun getContractById(contractId: Long): Contract

    fun getComplianceByContractIds(
        contractIds: Set<Long>
    ): List<com.multiplier.contract.onboarding.domain.model.Compliance>

    fun getPaymentAccountRequirementsRequestForMember(
        countryCode: CountryCode,
        contractType: com.multiplier.contract.onboarding.types.ContractType
    ): PaymentAccountRequirementsRequest

    fun getComplianceParamsByContractIds(
        contractIds: Set<Long>
    ): Map<Long, List<com.multiplier.contract.onboarding.domain.model.ComplianceParam>>

    fun getContractMembersEmailByContractIds(contractIds: Set<Long>): Map<Long, String>

    fun getMemberIdsByContractIds(contractIdList: Set<Long>): Map<Long, Long>

    fun getDirectEmployeeOnboardingFlowTypes(
        contractIds: Collection<Long>
    ): Map<Long, DirectEmployeeOnboardingFlowType>

    fun getCurrentCompensationByContractIds(contractId: Long): Compensation?
}

@Service
class ContractServiceAdapterImpl : ContractServiceAdapter {

    @GrpcClient("contract-service") private lateinit var blockingStub: ContractServiceBlockingStub

    @GrpcClient("contract-service")
    private lateinit var contractPayableServiceBlockingStub: ContractPayableServiceBlockingStub

    @GrpcClient("contract-service")
    private lateinit var compensationServiceBlockingStub: CompensationServiceBlockingStub

    override fun getNonDeletedNonEndedContract(contractId: Long): Contract? {
        return getNonDeletedNonEndedContracts(setOf(contractId)).firstOrNull()
    }

    override fun getNonDeletedNonEndedContracts(contractIds: Set<Long>): List<Contract> {
        if (contractIds.isEmpty()) return emptyList()

        val request =
            GetNonDeletedNonEndedContractsByIdsRequest.newBuilder().addAllIds(contractIds).build()

        return blockingStub.getNonDeletedNonEndedContractsByIds(request).contractsList
    }

    override fun getDepositPaidContractIds(contractIds: Set<Long>): Set<Long> {
        if (contractIds.isEmpty()) return emptySet()

        val request =
            ContractOuterClass.ContractFilters.newBuilder()
                .addAllContractIds(contractIds)
                .setDepositPayableFilters(
                    CompanyPayable.DepositPayableFilters.newBuilder()
                        .addAllDepositInvoiceStatuses(listOf(InvoiceStatus.PAID))
                        .build())
                .build()

        return blockingStub.getContractIdsMatchingFilters(request).idsList.toSet()
    }

    override fun getContractsBy(filters: ContractFilters): List<Contract> {
        return blockingStub.getContracts(filters.toGrpc()).contractsList
    }

    override fun getContractById(contractId: Long): Contract {
        return blockingStub.getContractById(
            GetContractByIdRequest.newBuilder().setId(contractId).build())
    }

    override fun getComplianceByContractIds(
        contractIds: Set<Long>
    ): List<com.multiplier.contract.onboarding.domain.model.Compliance> {
        if (contractIds.isEmpty()) return emptyList()

        return blockingStub
            .getListOfCompliance(ContractIds.newBuilder().addAllContractIds(contractIds).build())
            .complianceList
            .map {
                com.multiplier.contract.onboarding.domain.model.Compliance(
                    contractId = it.contractId,
                    agreementId = it.agreementId,
                    type = ComplianceType.valueOf(it.type.name),
                    agreementType =
                        com.multiplier.contract.onboarding.domain.model.ContractAgreementType
                            .valueOf(it.contractAgreementType.name))
            }
    }

    override fun getPaymentAccountRequirementsRequestForMember(
        countryCode: CountryCode,
        contractType: com.multiplier.contract.onboarding.types.ContractType
    ): PaymentAccountRequirementsRequest {
        return contractPayableServiceBlockingStub
            .getPersonalPaymentAccountRequirementsRequests(
                ContractPayable.GetPersonalPaymentAccountRequirementsRequestsMessage.newBuilder()
                    .addParams(
                        PersonalPaymentAccountRequirementsRequestParam.newBuilder()
                            .setCountry(Country.GrpcCountryCode.valueOf(countryCode.name))
                            .setContractType(ContractType.valueOf(contractType.name))
                            .build())
                    .build())
            .paymentAccountRequestsForMemberList
            .first()
            .paymentAccountRequirementsRequest
    }

    override fun getComplianceParamsByContractIds(
        contractIds: Set<Long>
    ): Map<Long, List<com.multiplier.contract.onboarding.domain.model.ComplianceParam>> {
        if (contractIds.isEmpty()) return emptyMap()

        return blockingStub
            .getContractsComplianceParams(
                GetContractsByIdsRequest.newBuilder().addAllId(contractIds).build())
            .complianceParamsMap
            .mapValues { it.value.complianceParamsList.map(ComplianceParam::toDomain) }
    }

    override fun getContractMembersEmailByContractIds(contractIds: Set<Long>): Map<Long, String> {
        if (contractIds.isEmpty()) return emptyMap()

        val request =
            GetContractMemberEmailsByContractIdsRequest.newBuilder()
                .addAllContractId(contractIds)
                .build()
        return blockingStub.getContractMembersEmailByContractIds(request).memberEmailByContractIdMap
    }

    override fun getMemberIdsByContractIds(contractIdList: Set<Long>): Map<Long, Long> {
        val request =
            GetMemberIdsByContractIdsRequest.newBuilder().addAllContractIds(contractIdList).build()
        return blockingStub.getMemberIdsByContractIds(request).contractIdToMemberIdMap
    }

    override fun getDirectEmployeeOnboardingFlowTypes(
        contractIds: Collection<Long>
    ): Map<Long, DirectEmployeeOnboardingFlowType> {
        val request = ContractIds.newBuilder().addAllContractIds(contractIds).build()
        return blockingStub.getDirectEmployeeOnboardingFlowTypes(request).contractIdToFlowTypesMap
    }

    override fun getCurrentCompensationByContractIds(contractId: Long): Compensation? {
        val request = IdsMessage.newBuilder().addIds(contractId).build()
        val response = compensationServiceBlockingStub.getCurrentCompensationByContractIds(request)
        return response.compensationsMap[contractId]
    }
}

private fun ContractFilters.toGrpc(): ContractOuterClass.ContractFilters {
    val builder =
        ContractOuterClass.ContractFilters.newBuilder()
            .addAllContractIds(this.contractIds)
            .addAllCompanyIds(this.companyIds)
            .addAllContractTypes(this.types)
            .addAllContractStatuses(this.statuses)
    this.type?.let { builder.setContractType(this.type) }
    this.status?.let { builder.setContractStatus(this.status) }
    this.startDateRange?.let { builder.setStartDateRange(it.toGrpc()) }
    this.countryCode?.let { builder.setCountryCode(this.countryCode) }
    this.legalEntityId?.let { builder.setLegalEntityId(this.legalEntityId) }
    this.pageRequest?.let { builder.setPaginationRequest(it.toGrpc()) }

    return builder.build()
}

private fun DateRange.toGrpc() =
    ContractOuterClass.DateRange.newBuilder()
        .setStartDate(this.from.toGrpcDate())
        .setEndDate(this.to.toGrpcDate())
        .build()

private fun ComplianceParam.toDomain() =
    com.multiplier.contract.onboarding.domain.model.ComplianceParam(
        key = key, value = value, unit = unit, label = label)

private fun PageRequest.toGrpc() =
    PaginationRequest.newBuilder().setPageSize(pageSize).setPageNumber(pageNumber).build()
