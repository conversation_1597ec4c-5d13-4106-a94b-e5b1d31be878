package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.PayrollCycle
import java.time.LocalDate
import java.time.Month
import java.time.YearMonth

/**
 * This helper class provides a list of methods to create payroll cycle objects based on payroll
 * cycle configuration described here:
 * https://www.notion.so/usemultiplier/General-958da009904f49de8a6613778a5f1d90?pvs=4
 */
object PayrollCycleHepper {

    fun getPayrollCycleConfig(
        startDate: LocalDate,
        payDays: Set<PayrollCycle.PayDay>
    ): PayrollCycle? {
        val smallestPayrollCycleConfig =
            getSmallestPayrollCycleConfig(YearMonth.of(startDate.year, startDate.month), payDays)
                ?.let { if (startDate > it.cutoffDate) getNextCycle(it) else it }
                ?: return null

        return getActivePayrollCycle(startDate, smallestPayrollCycleConfig)
    }

    fun getNextMonthCycle(payrollCycleDTO: PayrollCycle): PayrollCycle {
        val nextMonth = payrollCycleDTO.payrollMonth.plusMonths(1)

        return when (payrollCycleDTO.payDay) {
            PayrollCycle.PayDay.Monthly -> getMonthlyPayrollCycleForPayrollMonth(nextMonth)
            PayrollCycle.PayDay.SemiMonthly10 ->
                getSemiMonthly10thPayrollCycleForPayrollMonth(nextMonth)
            PayrollCycle.PayDay.SemiMonthly25 ->
                getSemiMonthly25thPayrollCycleForPayrollMonth(nextMonth)
            PayrollCycle.PayDay.SemiMonthly14 ->
                getSemiMonthly14thPayrollCycleForPayrollMonth(nextMonth)
            PayrollCycle.PayDay.SemiMonthly28 ->
                getSemiMonthly28thPayrollCycleForPayrollMonth(nextMonth)
            PayrollCycle.PayDay.SemiMonthly15 ->
                getSemiMonthly15thPayrollCycleForPayrollMonth(nextMonth)
            PayrollCycle.PayDay.SemiMonthly30 ->
                getSemiMonthly30thPayrollCycleForPayrollMonth(nextMonth)
        }
    }

    private fun getActivePayrollCycle(
        startDate: LocalDate,
        payrollCycleDTO: PayrollCycle
    ): PayrollCycle {
        if (startDate > payrollCycleDTO.contractStartTo)
            return getActivePayrollCycle(startDate, getNextCycle(payrollCycleDTO))

        return payrollCycleDTO
    }

    private fun getSmallestPayrollCycleConfig(
        payrollMonth: YearMonth,
        payDays: Set<PayrollCycle.PayDay>
    ): PayrollCycle? {
        val payDay = payDays.firstOrNull() ?: return null

        return when (payDay) {
            PayrollCycle.PayDay.Monthly -> getMonthlyPayrollCycleForPayrollMonth(payrollMonth)
            PayrollCycle.PayDay.SemiMonthly10,
            PayrollCycle.PayDay.SemiMonthly25 ->
                getSemiMonthly10thPayrollCycleForPayrollMonth(payrollMonth)
            PayrollCycle.PayDay.SemiMonthly14,
            PayrollCycle.PayDay.SemiMonthly28 ->
                getSemiMonthly14thPayrollCycleForPayrollMonth(payrollMonth)
            PayrollCycle.PayDay.SemiMonthly15,
            PayrollCycle.PayDay.SemiMonthly30 ->
                getSemiMonthly15thPayrollCycleForPayrollMonth(payrollMonth)
        }
    }

    fun getNextCycle(payrollCycleDTO: PayrollCycle): PayrollCycle {
        val nextMonth = payrollCycleDTO.payrollMonth.plusMonths(1)

        return when (payrollCycleDTO.payDay) {
            PayrollCycle.PayDay.Monthly -> getMonthlyPayrollCycleForPayrollMonth(nextMonth)
            PayrollCycle.PayDay.SemiMonthly10 ->
                getSemiMonthly25thPayrollCycleForPayrollMonth(payrollCycleDTO.payrollMonth)
            PayrollCycle.PayDay.SemiMonthly25 ->
                getSemiMonthly10thPayrollCycleForPayrollMonth(nextMonth)
            PayrollCycle.PayDay.SemiMonthly14 ->
                getSemiMonthly28thPayrollCycleForPayrollMonth(payrollCycleDTO.payrollMonth)
            PayrollCycle.PayDay.SemiMonthly28 ->
                getSemiMonthly14thPayrollCycleForPayrollMonth(nextMonth)
            PayrollCycle.PayDay.SemiMonthly15 ->
                getSemiMonthly30thPayrollCycleForPayrollMonth(payrollCycleDTO.payrollMonth)
            PayrollCycle.PayDay.SemiMonthly30 ->
                getSemiMonthly15thPayrollCycleForPayrollMonth(nextMonth)
        }
    }

    private fun getMonthlyPayrollCycleForPayrollMonth(payrollMonth: YearMonth): PayrollCycle {
        val cutoffDayOfMonth = if (payrollMonth.monthValue == 12) 5 else 15

        return PayrollCycle(
            payFrequency = PayrollCycle.PayFrequency.MONTHLY,
            payrollMonth = payrollMonth,
            payDay = PayrollCycle.PayDay.Monthly,
            payDate = payrollMonth.atDay(25),
            cutoffDate = payrollMonth.atDay(cutoffDayOfMonth),
            contractStartFrom = payrollMonth.minusMonths(1).atDay(21),
            contractStartTo = payrollMonth.atDay(20),
        )
    }

    private fun getSemiMonthly10thPayrollCycleForPayrollMonth(
        payrollMonth: YearMonth
    ): PayrollCycle {
        return PayrollCycle(
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payrollMonth = payrollMonth,
            payDay = PayrollCycle.PayDay.SemiMonthly10,
            payDate = payrollMonth.atDay(10),
            cutoffDate = payrollMonth.minusMonths(1).atEndOfMonth(),
            contractStartFrom = payrollMonth.minusMonths(1).atDay(16),
            contractStartTo = payrollMonth.minusMonths(1).atEndOfMonth(),
        )
    }

    private fun getSemiMonthly25thPayrollCycleForPayrollMonth(
        payrollMonth: YearMonth
    ): PayrollCycle {
        return PayrollCycle(
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payrollMonth = payrollMonth,
            payDay = PayrollCycle.PayDay.SemiMonthly25,
            payDate = payrollMonth.atDay(25),
            cutoffDate = payrollMonth.atDay(15),
            contractStartFrom = payrollMonth.atDay(1),
            contractStartTo = payrollMonth.atDay(15),
        )
    }

    private fun getSemiMonthly14thPayrollCycleForPayrollMonth(
        payrollMonth: YearMonth
    ): PayrollCycle {
        return PayrollCycle(
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payrollMonth = payrollMonth,
            payDay = PayrollCycle.PayDay.SemiMonthly14,
            payDate = payrollMonth.atDay(14),
            cutoffDate = payrollMonth.minusMonths(1).atEndOfMonth(),
            contractStartFrom = payrollMonth.atDay(1),
            contractStartTo = payrollMonth.atDay(15),
        )
    }

    private fun getSemiMonthly28thPayrollCycleForPayrollMonth(
        payrollMonth: YearMonth
    ): PayrollCycle {
        return PayrollCycle(
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payrollMonth = payrollMonth,
            payDay = PayrollCycle.PayDay.SemiMonthly28,
            payDate = payrollMonth.atDay(28),
            cutoffDate = payrollMonth.atDay(15),
            contractStartFrom = payrollMonth.atDay(16),
            contractStartTo = payrollMonth.atEndOfMonth(),
        )
    }

    private fun getSemiMonthly15thPayrollCycleForPayrollMonth(
        payrollMonth: YearMonth
    ): PayrollCycle {
        return PayrollCycle(
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payrollMonth = payrollMonth,
            payDay = PayrollCycle.PayDay.SemiMonthly15,
            payDate = payrollMonth.atDay(15),
            cutoffDate = payrollMonth.minusMonths(1).atEndOfMonth(),
            contractStartFrom = payrollMonth.atDay(1),
            contractStartTo = payrollMonth.atDay(15),
        )
    }

    private fun getSemiMonthly30thPayrollCycleForPayrollMonth(
        payrollMonth: YearMonth
    ): PayrollCycle {
        return PayrollCycle(
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payrollMonth = payrollMonth,
            payDay = PayrollCycle.PayDay.SemiMonthly30,
            payDate =
                if (payrollMonth.month == Month.FEBRUARY) payrollMonth.atDay(28)
                else payrollMonth.atDay(30),
            cutoffDate = payrollMonth.atDay(15),
            contractStartFrom = payrollMonth.atDay(16),
            contractStartTo = payrollMonth.atEndOfMonth(),
        )
    }
}
