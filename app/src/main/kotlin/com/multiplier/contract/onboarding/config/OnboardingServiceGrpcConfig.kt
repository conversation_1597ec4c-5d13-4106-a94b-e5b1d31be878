package com.multiplier.contract.onboarding.config

import com.multiplier.contract.schema.compensation.CompensationServiceGrpc.CompensationServiceBlockingStub
import com.multiplier.contract.schema.contract.ContractServiceGrpc.ContractServiceBlockingStub
import com.multiplier.core.schema.company.CompanyServiceGrpc
import com.multiplier.member.schema.MemberServiceGrpc
import net.devh.boot.grpc.client.autoconfigure.*
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@ImportAutoConfiguration(
    GrpcClientAutoConfiguration::class,
    GrpcClientMetricAutoConfiguration::class,
    GrpcClientHealthAutoConfiguration::class,
    GrpcClientSecurityAutoConfiguration::class)
class OnboardingServiceGrpcConfig {

    @GrpcClient("contract-service")
    private lateinit var contractServiceBlockingStub: ContractServiceBlockingStub

    @GrpcClient("contract-service")
    private lateinit var compensationServiceBlockingStub: CompensationServiceBlockingStub

    @GrpcClient("core-service")
    private lateinit var onboardingServiceCompanyBlockingStub:
        CompanyServiceGrpc.CompanyServiceBlockingStub

    @GrpcClient("member-service")
    private lateinit var memberServiceBlockingStub: MemberServiceGrpc.MemberServiceBlockingStub

    @Bean
    fun contractServiceBlockingStub(): ContractServiceBlockingStub {
        return contractServiceBlockingStub
    }

    @Bean
    fun compensationServiceBlockingStub(): CompensationServiceBlockingStub =
        compensationServiceBlockingStub

    @Bean
    fun onboardingServiceCompanyBlockingStub(): CompanyServiceGrpc.CompanyServiceBlockingStub =
        onboardingServiceCompanyBlockingStub

    @Bean
    fun memberServiceBlockingStub(): MemberServiceGrpc.MemberServiceBlockingStub =
        memberServiceBlockingStub
}
