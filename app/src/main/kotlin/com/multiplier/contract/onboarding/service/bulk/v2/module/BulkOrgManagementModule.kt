package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.DepartmentServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkOrgManagementDataServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService.Companion.DepartmentSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService.Companion.DirectManagerEmailSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.toEmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.orgmanagement.schema.BulkAPI.UpsertOrgManagementDataInput
import org.springframework.stereotype.Service

@Service
class BulkOrgManagementModule(
    private val departmentServiceAdapter: DepartmentServiceAdapter,
    private val bulkOrgManagementDataServiceAdapter: BulkOrgManagementDataServiceAdapter
) : BulkDataModule {
    companion object {
        const val MODULE_NAME = "ORG_MANAGEMENT_MODULE"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        val departments =
            departmentServiceAdapter.getDepartments(onboardingOptions.companyId).map {
                "${it.id} - ${it.name}"
            }

        return listOf(
            DirectManagerEmailSpec,
            DepartmentSpec.copy(mandatory = true, allowedValues = departments))
    }

    override fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        contractIdToMemberId: Map<Long, Long>
    ): DataForContract {
        val orgManagementDataByContractId =
            bulkOrgManagementDataServiceAdapter
                .getOrgManagementData(contractIds = contractIdToMemberId.keys, options = options)
                .associateBy { it.contractId }

        return DataForContract(
            contractIdToEmployeeDataChunk =
                contractIdToMemberId.mapValues { (contractId, _) ->
                    orgManagementDataByContractId[contractId]?.toEmployeeDataChunk(dataSpecs)
                        ?: EmployeeDataChunk.EMPTY
                })
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        return bulkOrgManagementDataServiceAdapter.validateUpsertOrgManagementDataInputs(
            employeeData.map {
                it.copy(data = it.data + mapOf(DepartmentSpec.key to it.getDepartmentId()))
            },
            options)
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        val upsertOrgManagementDataInputs =
            validationResults.map {
                CreationInput(
                    requestId = it.validationId,
                    contractId = bulkCreationResult.getContractId(it.validationId),
                    data = it.input as UpsertOrgManagementDataInput)
            }

        return bulkUpsert(
            "Upsert org management data",
            upsertOrgManagementDataInputs,
            options,
            bulkCreationResult,
            CreationInput.refContractId,
        ) { inputs, onboardingOptions ->
            bulkOrgManagementDataServiceAdapter.upsertOrgManagementData(inputs, onboardingOptions)
        }
    }

    private fun EmployeeData.getDepartmentId(): String {
        return this.data[DepartmentSpec.key].orEmpty().split("-").first().trim()
    }
}
