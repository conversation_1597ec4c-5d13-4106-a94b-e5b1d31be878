package com.multiplier.contract.onboarding.featureflag

import com.multiplier.common.featureflag.FeatureFlagService
import com.multiplier.growthbook.sdk.model.GBFeatureResult
import org.springframework.stereotype.Component

object FeatureFlags {
    const val ONBOARDING_NOTIFICATION = "contract.onboarding.notification"
    const val SCHEDULER_ENABLED = "contract-onboarding-service-reminder-scheduler"
    const val EMPLOYEE_ID_WORK_EMAIL_REMINDERS =
        "contract.onboarding.employee-id-work-email-reminder"
    const val USE_PAYROLL_SERVICE_TO_GET_PAYROLL_CYCLES =
        "use-payroll-service-to-get-payroll-cycles"
    const val ONBOARDING_VISA_WORKFLOW = "contract.onboarding.visa-workflow"
    const val ONBOARDING_BULK_EOR = "contract.onboarding.bulk.eor"
    const val CONTRACT_TO_PAYROLL_LINKING = "contract-to-payroll-linking"
    const val DESCRIPTION_OF_WORK = "description-of-work"
    const val CUSTOM_BANK_FIELDS = "bulk-upload-custom-bank-fields"
    const val PREREGISTRATION_ENABLED_COUNTRIES_REMINDERS =
        "preregistration-enabled-countries-reminders"
    const val ENABLE_MULTI_FREQUENCY_SUPPORT_REMINDER_CTA =
        "enable-multi-frequency-support-reminder-cta"
    const val ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING =
        "enable-getCompanyUserByUserIdV2-contract-onboarding"

    object Params {
        const val COMPANY = "company"
        const val ONBOARDING_COUNTRY = "onboardingCountry"
        const val ONBOARDING_CONTRACT_TYPE = "onboardingContractType"
        const val CONTRACT_ID = "contractId"
        const val IS_BULK_ONBOARDED = "isBulkOnboarded"
        const val ENTITY_ID = "entityId"
    }
}

typealias PlatformFeatureFlagService = FeatureFlagService

@Component
class FeatureFlagService(
    val platformFeatureFlagService: PlatformFeatureFlagService,
) {
    fun feature(
        id: String,
        attributes: Map<String, Any>? = null,
    ): GBFeatureResult {
        return platformFeatureFlagService.feature(id, attributes.orEmpty())
    }

    fun isFeatureOn(
        id: String,
        attributes: Map<String, Any>? = null,
    ): Boolean = feature(id, attributes).on
}
