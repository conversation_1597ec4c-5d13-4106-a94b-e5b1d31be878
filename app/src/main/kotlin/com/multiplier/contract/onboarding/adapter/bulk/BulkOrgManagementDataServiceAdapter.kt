package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.CompanyManager
import com.multiplier.contract.onboarding.domain.model.Department
import com.multiplier.contract.onboarding.domain.model.OrgManagementData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.orgmanagement.schema.BulkAPI
import com.multiplier.orgmanagement.schema.BulkAPIServiceGrpc
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkOrgManagementDataServiceAdapter {

    @GrpcClient("org-management-service")
    private lateinit var bulkApiStub: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    fun getOrgManagementData(
        contractIds: Collection<Long>,
        options: BulkOnboardingOptions
    ): List<OrgManagementData> {
        val result =
            bulkApiStub.getOrgManagementData(
                BulkAPI.GetOrgManagementDataRequest.newBuilder()
                    .setCompanyId(options.companyId)
                    .addAllContractIds(contractIds)
                    .setContext(options.context.name)
                    .build())

        return result.dataList.map { it.toDomain(options.companyId) }
    }

    fun validateUpsertOrgManagementDataInputs(
        inputs: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<BulkAPI.UpsertOrgManagementDataInput>> {
        if (inputs.isEmpty()) return emptyList()

        val result =
            bulkApiStub.validateUpsertOrgManagementDataInputs(
                BulkAPI.BulkValidationRequest.newBuilder()
                    .addAllInputs(
                        inputs.map {
                            BulkAPI.GrpcValidationInput.newBuilder()
                                .setRequestId(it.identification.validationId)
                                .putAllProperties(it.data)
                                .build()
                        })
                    .setCompanyId(options.companyId)
                    .setContext(options.context.name)
                    .build())

        return result.validationResultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
            )
        }
    }

    fun upsertOrgManagementData(
        inputs: List<CreationInput<BulkAPI.UpsertOrgManagementDataInput>>,
        options: BulkOnboardingOptions
    ): List<CreationResult> {
        try {
            if (inputs.isEmpty()) return emptyList()

            val response =
                bulkApiStub.upsertOrgManagementData(
                    BulkAPI.BulkUpsertOrgManagementDataRequest.newBuilder()
                        .addAllInputs(
                            inputs.map {
                                it.data
                                    .toBuilder()
                                    .setRequestId(it.requestId)
                                    .setContractId(
                                        requireNotNull(it.contractId) {
                                            "Contract ID for org management data upsert must not be null"
                                        })
                                    .build()
                            })
                        .setCompanyId(options.companyId)
                        .setContext(options.context.name)
                        .build())

            return response.resultsList.map {
                CreationResult(
                    requestId = it.requestId, success = it.success, errors = it.errorsList)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to upsertOrgManagementData" }
            return inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Upsert org management failed due to an internal error: unknown exception occurred")
            }
        }
    }
}

private fun BulkAPI.OrgManagementData.toDomain(companyId: Long) =
    OrgManagementData(
        contractId = this.contractId,
        department =
            if (this.hasDepartment())
                Department(
                    id = this.department.id, companyId = companyId, name = this.department.name)
            else null,
        isManager = this.isManager,
        directManager =
            if (this.hasDirectManager())
                CompanyManager(
                    companyId = companyId,
                    email = this.directManager.email,
                )
            else null)
