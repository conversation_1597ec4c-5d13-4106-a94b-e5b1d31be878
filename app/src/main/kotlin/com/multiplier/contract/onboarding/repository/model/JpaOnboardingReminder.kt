package com.multiplier.contract.onboarding.repository.model

import com.multiplier.contract.onboarding.types.NotificationType
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import java.time.LocalDateTime
import org.hibernate.annotations.Type
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener

@Entity
@Table(name = "onboarding_reminder", schema = "contract")
@EntityListeners(AuditingEntityListener::class)
class JpaOnboardingReminder(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY) var id: Long? = 0,
    val contractId: Long,
    @Enumerated(EnumType.STRING) val templateName: NotificationType,
    var count: Int = 1,
    @Type(JsonType::class)
    @Column(name = "editable_template_params", columnDefinition = "jsonb")
    var editableTemplateParams: Map<String, String>? = null,
    @CreatedBy var createdBy: Long? = null,
    @CreatedDate var createdOn: LocalDateTime? = null,
    @LastModifiedBy var updatedBy: Long? = null,
    @LastModifiedDate var updatedOn: LocalDateTime? = null,
) {
    fun incrementCount() {
        this.count += 1
    }
}
