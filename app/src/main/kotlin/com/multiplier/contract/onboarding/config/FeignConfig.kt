package com.multiplier.contract.onboarding.config

import feign.codec.Encoder
import org.springframework.boot.autoconfigure.http.HttpMessageConverters
import org.springframework.cloud.openfeign.support.SpringEncoder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter

@Configuration
class FeignConfig {

    @Bean
    fun feignEncoder(): Encoder {
        val jacksonConverter = MappingJackson2HttpMessageConverter(objectMapper)
        val httpMessageConverters = HttpMessageConverters(jacksonConverter)
        return SpringEncoder { httpMessageConverters }
    }
}
