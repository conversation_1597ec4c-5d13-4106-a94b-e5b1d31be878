package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.springframework.stereotype.Component

@Component
class USAOpsMemberUpdatePayrollFormsReminderCtaProcessor : BaseReminderCtaTemplateProcessor() {

    override fun notificationType() = NotificationType.USAOpsMemberUpdatePayrollFormsReminderCta

    override fun validate(preFetchedData: EmailTemplateData): Boolean {
        // Only validate when multi-frequency support is enabled and country is USA
        if (!preFetchedData.multiFrequencySupportEnabled ||
            preFetchedData.contract.country != "USA" ||
            preFetchedData.contract.type != ContractOuterClass.ContractType.EMPLOYEE ||
            preFetchedData.operationsUser == null) {
            return false
        }

        return isPayrollFormsRequired(preFetchedData) && !isPayrollFormsSubmitted(preFetchedData)
    }

    private fun isPayrollFormsRequired(
        prefetchedData: EmailTemplateData,
    ): Boolean {
        return !prefetchedData.payrollFormRequirements[prefetchedData.contract.countryAndState]
            .isNullOrEmpty()
    }

    private fun isPayrollFormsSubmitted(
        preFetchedData: EmailTemplateData,
    ): Boolean {
        val requiredDocuments =
            preFetchedData.payrollFormRequirements[preFetchedData.contract.countryAndState]

        if (requiredDocuments.isNullOrEmpty()) return true

        return preFetchedData.submittedLegalDocument.containsAll(requiredDocuments)
    }

    override fun editableParams(preFetchedData: EmailTemplateData): List<EditableParam> {
        // USA implementation only includes CutoffParam
        return listOf(CutoffParam())
    }
}
