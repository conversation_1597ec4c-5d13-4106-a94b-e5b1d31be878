package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.CompanyIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractTypeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.CountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmploymentTermSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EndOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.MemberIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StateOfEmploymentSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.GenderSpec
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractTerm
import com.multiplier.contract.onboarding.types.ContractType
import java.util.*
import org.springframework.stereotype.Service

@Service
class BulkEnrichmentForEorService(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val countryServiceAdapter: CountryServiceAdapter
) : BulkEnrichmentServiceInterface {

    override fun enrichEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<EmployeeData> {
        enrichMemberIdForEmployeeData(employeeData, options)
        enrichUnitForComplianceData(employeeData)

        return employeeData
    }

    private fun enrichMemberIdForEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ) {
        val contractIdList =
            employeeData
                .mapNotNull { it[BulkContractDataService.ContractIdSpec].toLongOrNull() }
                .toSet()
        val contractToContractIdMap =
            contractServiceAdapter.getNonDeletedNonEndedContracts(contractIdList).associateBy {
                it.id
            }
        val countryStates =
            countryServiceAdapter
                .getCountryStates(setOf(options.countryCode))[options.countryCode]
                .orEmpty()
        employeeData.forEach {
            it.data as MutableMap
            it.data[CountrySpec.key] = options.countryCode.name
            it.data[CompanyIdSpec.key] = options.companyId.toString()
            it.data[ContractTypeSpec.key] = ContractType.EMPLOYEE.name
            it.data[GenderSpec.key] = it.getGender()
            it.data[EmploymentTermSpec.key] = getEmploymentTerm(it.data)
            if (countryStates.isNotEmpty()) {
                it.data[StateOfEmploymentSpec.key] = it.getCountryStateCode(countryStates)
            }
        }
        employeeData
            .filter { it[BulkContractDataService.ContractIdSpec].isNotBlank() }
            .forEach {
                val contractId = it[BulkContractDataService.ContractIdSpec].toLong()
                val contractData = contractToContractIdMap[contractId]
                if (Objects.nonNull(contractData)) {
                    it.data as MutableMap
                    it.data[MemberIdSpec.key] = contractData?.memberId.toString()
                    it.data[CompanyIdSpec.key] = contractData?.companyId.toString()
                }
            }
    }

    private fun enrichUnitForComplianceData(employeeData: List<EmployeeData>) {
        employeeData.forEach {
            assignUnitIfExists(
                it.data as MutableMap<String, String>,
                BulkComplianceDataService.NoticeAfterProbationValueSpec,
                BulkComplianceDataService.NoticeAfterProbationUnitSpec)
            assignUnitIfExists(
                it.data,
                BulkComplianceDataService.ProbationValueSpec,
                BulkComplianceDataService.ProbationUnitSpec)
            assignUnitIfExists(
                it.data,
                BulkComplianceDataService.NonCompeteValueSpec,
                BulkComplianceDataService.NonCompeteUnitSpec)
            assignUnitIfExists(
                it.data,
                BulkComplianceDataService.NonSolicitValueSpec,
                BulkComplianceDataService.NonSolicitUnitSpec)
        }
    }

    private fun assignUnitIfExists(
        data: MutableMap<String, String>,
        complianceValueSpec: DataSpec,
        complianceUnitSpec: DataSpec
    ) {
        if (data.containsKey(complianceValueSpec.key)) {
            if (data[complianceValueSpec.key]?.isEmpty() == true) {
                data.remove(complianceValueSpec.key)
                data.remove(complianceUnitSpec.key)
            }
        }
    }
}

fun getEmploymentTerm(input: Map<String, String>): String {
    val inputEndOn = input[EndOnSpec.key]
    return if (inputEndOn.isNullOrBlank()) ContractTerm.PERMANENT.name else ContractTerm.FIXED.name
}
