package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.BulkUpsertEmergencyContactInput
import com.multiplier.member.schema.BulkValidateRequest
import com.multiplier.member.schema.UpsertEmergencyContactInput
import com.multiplier.member.schema.ValidationInput
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkMemberEmergencyDataServiceAdapter {

    @GrpcClient("member-service")
    private lateinit var bulkMemberService: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    fun validateUpsertEmergencyContactDataInputs(
        employeeData: Collection<EmployeeData>,
        options: BulkOnboardingOptions,
    ): List<GrpcValidationResult<UpsertEmergencyContactInput>> {
        val results =
            bulkMemberService.validateUpsertEmergencyContact(
                BulkValidateRequest.newBuilder()
                    .addAllInputs(employeeData.map { it.toGrpc() })
                    .setContext(options.context.name)
                    .build())

        return results.validationResultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
            )
        }
    }

    fun upsertEmergencyContactData(
        inputs: List<CreationInput<UpsertEmergencyContactInput>>
    ): List<CreationResult> {
        if (inputs.isEmpty()) return emptyList()

        val request =
            BulkUpsertEmergencyContactInput.newBuilder()
                .addAllInputs(
                    inputs.map {
                        it.data
                            .toBuilder()
                            .setRequestId(it.requestId)
                            .setEmergencyContact(
                                it.data.emergencyContact
                                    .toBuilder()
                                    .setMemberId(
                                        requireNotNull(it.memberId) {
                                            "Member ID for emergency contact upsert must not be null"
                                        })
                                    .build())
                            .build()
                    })
                .build()
        try {
            val response = bulkMemberService.bulkUpsertEmergencyContact(request)
            return response.resultsList.map {
                CreationResult(
                    requestId = it.requestId, success = it.success, errors = it.errorsList)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to bulk upsert emergency contact data" }
            return inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Upsert emergency contact failed due to an internal error: unknown exception occurred")
            }
        }
    }

    private fun EmployeeData.toGrpc(): ValidationInput =
        ValidationInput.newBuilder()
            .setRequestId(this.identification.validationId)
            .putAllProperties(this.data)
            .build()
}
