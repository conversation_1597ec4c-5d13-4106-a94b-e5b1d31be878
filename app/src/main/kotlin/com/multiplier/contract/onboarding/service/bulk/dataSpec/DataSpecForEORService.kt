package com.multiplier.contract.onboarding.service.bulk.dataSpec

import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import org.springframework.stereotype.Service

@Service
class DataSpecForEORService(
    private val bulkContractDataService: BulkContractDataService,
    private val bulkMemberDataService: BulkMemberDataService,
    private val bulkMemberLegalDataService: BulkMemberLegalDataService,
    private val bulkComplianceDataService: BulkComplianceDataService,
    private val bulkTimeoffDataService: BulkTimeoffDataService,
    private val bulkInsuranceDataService: BulkInsuranceDataService,
    private val bulkCompensationDataService: BulkCompensationDataService,
) : BulkDataSpecsServiceInterface {

    companion object {
        val RowIdentifierSpec =
            DataSpec(
                key = "rowIdentifier",
                type = DataSpecType.TEXT,
                description = "Unique row identifier for employee data across upload sheets",
                label = "Row Identifier")
    }

    override fun getDataSpecs(options: BulkOnboardingOptions): List<DataSpec> {
        return (listOf(RowIdentifierSpec) +
            bulkContractDataService.getDataSpecs(options) +
            bulkMemberDataService.getDataSpecs(options) +
            bulkMemberLegalDataService.getDataSpecs(options) +
            bulkComplianceDataService.getDataSpecs(options) +
            bulkCompensationDataService.getDataSpecs(options) +
            bulkInsuranceDataService.getDataSpecs(options) +
            bulkTimeoffDataService.getDataSpecs(options))
    }
}
