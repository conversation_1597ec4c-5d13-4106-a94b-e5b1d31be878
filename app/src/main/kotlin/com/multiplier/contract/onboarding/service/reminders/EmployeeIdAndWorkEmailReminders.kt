package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.service.notifications.NotificationService
import com.multiplier.contract.schema.contract.ContractOuterClass
import java.text.MessageFormat
import java.time.LocalDate
import java.util.*
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class EmployeeIdAndWorkEmailReminders(
    private val contractFinder: EmployeeIdAndWorkEmailRemindersContractFinder,
    private val dtoComposer: EmployeeIdAndWorkEmailNotificationDtoComposer,
    private val notificationService: NotificationService,
    @Value("\${ops.support-email}") private val systemNotificationEmail: String,
) {
    private val log = KotlinLogging.logger {}

    fun sendEmployeeIdAndWorkEmailTaskReminders(
        simulatedSendingDate: LocalDate?,
        companyIds: List<Long> = emptyList()
    ) {
        val sendingDate = Optional.ofNullable(simulatedSendingDate).orElse(LocalDate.now())
        val contractList =
            contractFinder.findContractsForEmployeeIdAndWorkEmailReminders(sendingDate, companyIds)
        if (contractList.isEmpty()) {
            log.info { "No contracts found for sending employeeId and work email reminders" }
            return
        }

        log.info { "Send employeeId and work email reminders to ${contractList.map { it.id }}" }
        dtoComposer.init(contractList)
        for (contract in contractList) {
            sendEmployeeIdAndWorkEmailRemindersToCompany(contract)
        }
    }

    fun sendEmployeeIdAndWorkEmailReminderToCompanyOnContractActivation(contractId: Long) {
        val contractList =
            contractFinder.findContractForEmployeeIdAndWorkEmailReminderOnContractActivation(
                contractId)
        if (contractList.isEmpty()) {
            log.info { "No contracts found for sending employeeId and work email reminders" }
            return
        }

        log.info {
            "Send employeeId and work email reminders on activation to ${contractList.map { it.id }}"
        }
        dtoComposer.init(contractList)
        for (contract in contractList) {
            sendEmployeeIdAndWorkEmailRemindersToCompany(contract)
        }
    }

    private fun sendEmployeeIdAndWorkEmailRemindersToCompany(
        contract: ContractOuterClass.Contract,
    ) {
        try {
            val dto = dtoComposer.composeEmployeeIdAndWorkEmailReminderEmailDto(contract)

            notificationService.send(
                Notification(
                    subject = dto.subject,
                    from = MessageFormat.format("Multiplier <{0}>", systemNotificationEmail),
                    to = dto.companyUserEmails.joinToString(";"),
                    cc = emptyList(),
                    type = dto.notificationType,
                    data = dto.toPropertyMap(),
                ))
        } catch (ex: Exception) {
            log.warn(
                "Fail to send onboarding notification to company admin for contract id = {}",
                contract.id,
                ex)
        }
    }
}
