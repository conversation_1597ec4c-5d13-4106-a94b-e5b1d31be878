package com.multiplier.contract.onboarding.domain.model.member

import com.multiplier.contract.onboarding.types.DomainType
import com.multiplier.contract.onboarding.types.FetchStage

enum class MemberLegalDataType {
    TEXT,
    SELECT,
    DATE,
    BOOLEAN,
}

enum class ApplyTo {
    UNRECOGNIZED,
    ALL,
    LOCAL_ONLY,
    EXPAT_ONLY,
}

data class MemberLegalDataDefinition(
    val key: String,
    val type: MemberLegalDataType,
    val mandatory: Boolean,
    val description: String? = null,
    val allowedValues: List<String> = emptyList(),
    val fetchStage: FetchStage,
    val applyTo: ApplyTo,
    val domainType: DomainType,
)
