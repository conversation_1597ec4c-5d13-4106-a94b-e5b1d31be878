package com.multiplier.contract.onboarding.service.bulkupload

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.CompensationSourceServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.Company
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.EMPLOYMENT_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.CompanyIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmployeeIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.LegalEntityIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.RowIdentifierSpec
import com.multiplier.contract.onboarding.service.bulk.v2.*
import com.multiplier.contract.onboarding.service.bulkupload.ValidationResultHelper.Companion.mergeValidationResults
import com.multiplier.contract.onboarding.service.bulkupload.ValidationResultHelper.Companion.restoreValidationRequestIds
import com.multiplier.contract.onboarding.service.bulkupload.ValidationResultHelper.Companion.unmergeToBulkValidationResults
import com.multiplier.contract.onboarding.service.filterNonNullValues
import com.multiplier.contract.onboarding.service.mapOfNotNull
import com.multiplier.contract.onboarding.service.toWords
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.PageRequest
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.grpc.common.bulkupload.v1.PlatformKeys
import mu.KotlinLogging
import org.springframework.stereotype.Service

data class FieldRequirementsRequest(
    val useCase: String,
    val entityId: Long? = null,
    val companyId: Long? = null,
    val countryCode: String? = null,
    val jsonCustomParams: Map<String, String> = emptyMap()
)

enum class FieldType {
    TEXT,
    NUMBER,
    BOOLEAN,
    DATE,
    MONTH_YEAR,
    SELECT
}

data class FieldRequirement(
    val key: String,
    val label: String = key.toWords(),
    val type: FieldType = FieldType.TEXT,
    val description: String? = null,
    val mandatory: Boolean = true,
    val allowedValues: Collection<String> = emptyList(),
    val defaultValue: String? = null,
    val group: String? = null,
)

data class FieldDataRequest(
    val useCase: String,
    val entityIds: List<Long>,
    val companyIds: List<Long>,
    val countryCodes: List<String>,
    val pageRequest: PageRequest?,
)

data class RowData(
    val data: Map<String, String>,
    val key: PlatformKeys,
    val group: String? = null,
)

data class BulkValidationRequest(
    val useCase: String,
    val inputs: List<ValidationInput>,
    val jsonCustomParams: Map<String, String> = emptyMap()
)

data class ValidationInput(
    val id: String,
    val companyId: Long,
    val countryCode: String? = null,
    val entityId: Long? = null,
    val contractId: Long? = null,
    val data: Map<String, String>,
    val group: String? = null,
)

data class ValidationResult(
    val inputIds: List<String>,
    val success: Boolean,
    val validatedData: Map<String, String> = emptyMap(),
    val errors: List<ValidationError> = emptyList(),
    val module: String = "",
    val group: String = "",
)

data class ValidationError(
    val key: String,
    val value: String,
    val errors: List<String>,
)

data class CompanyEntity(
    val companyId: Long,
    val entityId: Long,
    val countryCode: String,
)

data class BulkUpsertRequest(
    val jobId: Long,
    val useCase: String,
    val inputs: List<UpsertInput>,
)

data class UpsertInput(
    val id: String,
    val data: Map<String, String>,
    val group: String? = null,
)

data class UpsertResult(
    val inputId: String,
    val success: Boolean,
    val upsertedData: Map<String, String> = emptyMap(),
    val errors: List<String> = emptyList(),
    val group: String? = null,
)

@Service
class BulkUploadService(
    private val globalPayrollBulkOnboardingUseCaseV3: GlobalPayrollBulkOnboardingUseCaseV2,
    private val freelancerBulkOnboardingUseCase: FreelancerBulkOnboardingUseCase,
    private val aorBulkOnboardingUseCase: AorBulkOnboardingUseCase,
    private val eorBulkOnboardingUseCaseV2: EorBulkOnboardingUseCaseV2,
    private val companyServiceAdapter: CompanyServiceAdapter,
    private val compensationSourceServiceAdapter: CompensationSourceServiceAdapter,
    private val featureFlagService: FeatureFlagService,
) {
    companion object {
        const val GLOBAL_PAYROLL_MEMBER_ONBOARDING = "GLOBAL_PAYROLL_MEMBER_ONBOARDING"
        const val FREELANCER_MEMBER_ONBOARDING = "FREELANCER_MEMBER_ONBOARDING"
        const val AOR_MEMBER_ONBOARDING = "AOR_MEMBER_ONBOARDING"
        const val EOR_MEMBER_ONBOARDING = "EOR_MEMBER_ONBOARDING"
    }

    private val log = KotlinLogging.logger {}

    fun getFieldRequirements(request: FieldRequirementsRequest): List<FieldRequirement> {
        val (context, fieldRequirementData) =
            when (request.useCase) {
                GLOBAL_PAYROLL_MEMBER_ONBOARDING ->
                    Pair(
                        BulkOnboardingContext.GLOBAL_PAYROLL,
                        validateAndExtractEntityDataOrThrow(request).let {
                            FieldRequirementData(
                                it.companyId,
                                it.entityId,
                                ContractType.HR_MEMBER,
                                CountryCode.valueOf(it.countryCode))
                        })
                FREELANCER_MEMBER_ONBOARDING ->
                    Pair(
                        BulkOnboardingContext.FREELANCER,
                        validateAndExtractCompanyDataOrThrow(request).let {
                            FieldRequirementData(it.id, null, ContractType.FREELANCER, null)
                        })
                AOR_MEMBER_ONBOARDING ->
                    Pair(
                        BulkOnboardingContext.AOR,
                        validateAndExtractCompanyDataOrThrow(request).let {
                            FieldRequirementData(it.id, null, ContractType.CONTRACTOR, null)
                        })
                EOR_MEMBER_ONBOARDING -> {
                    val contractType = ContractType.EMPLOYEE
                    val eorEnabled = isEorOnboardingEnabled(request.companyId)
                    if (!eorEnabled) {
                        // return if EOR not enabled
                        return emptyList()
                    }
                    val countryCode =
                        validateIfCountrySupportedForContractType(request.countryCode, contractType)
                    Pair(
                        BulkOnboardingContext.EOR,
                        validateAndExtractCompanyDataOrThrow(request).let {
                            FieldRequirementData(
                                it.id, null, contractType, countryCode, request.jsonCustomParams)
                        })
                }
                else -> return emptyList()
            }

        return getDataSpecsForOnboarding(fieldRequirementData, context)
    }

    private fun isEorOnboardingEnabled(companyId: Long?): Boolean {
        val params = mutableMapOf<String, Any>()

        companyId?.let { params[FeatureFlags.Params.COMPANY] = it }

        return featureFlagService.isFeatureOn(FeatureFlags.ONBOARDING_BULK_EOR, params)
    }

    private fun validateIfCountrySupportedForContractType(
        inputCountryCode: String?,
        contractType: ContractType
    ): CountryCode {
        val countryCode =
            try {
                CountryCode.valueOf(
                    inputCountryCode
                        ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                            "Country code is required for EOR"))
            } catch (e: IllegalArgumentException) {
                throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                    "Invalid country code: $inputCountryCode")
            }

        val isCompEnabled =
            compensationSourceServiceAdapter
                .isNewCompensationSourceEnabledForCountries(contractType, listOf(countryCode))
                .getOrDefault(countryCode, false)

        if (!isCompEnabled) {
            throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                "New compensation service not enabled for country ${countryCode.name} for type ${contractType.name}")
        }

        return countryCode
    }

    private fun getDataSpecsForOnboarding(
        fieldRequirementData: FieldRequirementData,
        context: BulkOnboardingContext
    ): List<FieldRequirement> {
        val useCase =
            when (context) {
                BulkOnboardingContext.GLOBAL_PAYROLL -> globalPayrollBulkOnboardingUseCaseV3
                BulkOnboardingContext.FREELANCER -> freelancerBulkOnboardingUseCase
                BulkOnboardingContext.AOR -> aorBulkOnboardingUseCase
                BulkOnboardingContext.EOR -> eorBulkOnboardingUseCaseV2
                else ->
                    throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                        "Unsupported context: $context", context = mapOf("context" to context))
            }
        val options =
            BulkOnboardingOptions(
                fieldRequirementData.countryCode,
                fieldRequirementData.contractType,
                fieldRequirementData.companyId,
                fieldRequirementData.entityId,
                context)

        val moduleParams = buildModuleParams(options, fieldRequirementData.jsonCustomParams)
        return useCase
            .getDataSpecs(options, moduleParams)
            .filterNot { it.key == ContractIdSpec.key }
            .map { it.toFieldRequirement() }
    }

    private data class FieldRequirementData(
        val companyId: Long,
        val entityId: Long?,
        val contractType: ContractType,
        val countryCode: CountryCode?,
        val jsonCustomParams: Map<String, String> = emptyMap()
    )

    private fun buildModuleParams(
        bulkOnboardingOptions: BulkOnboardingOptions,
        jsonCustomParams: Map<String, String>
    ): ModuleParams {
        val workPlaceEntityId =
            if (bulkOnboardingOptions.contractType == ContractType.EMPLOYEE) {
                companyServiceAdapter
                    .getCompanyPrimaryLegalEntity(bulkOnboardingOptions.companyId)
                    ?.id
            } else {
                bulkOnboardingOptions.entityId
            }

        return ModuleParams(
            jsonCustomParams = jsonCustomParams, workPlaceEntityId = workPlaceEntityId)
    }

    fun getFieldData(request: FieldDataRequest): List<RowData> {
        return if (request.useCase == GLOBAL_PAYROLL_MEMBER_ONBOARDING) {
            val bulkOnboardingOptions =
                BulkOnboardingOptions.newBuilder()
                    .companyId(request.companyIds.first())
                    .entityId(request.entityIds.first())
                    .contractType(ContractType.HR_MEMBER)
                    .context(BulkOnboardingContext.GLOBAL_PAYROLL)
                    .countryCode(CountryCode.valueOf(request.countryCodes.first()))
                    .build()
            val dataSpecs =
                globalPayrollBulkOnboardingUseCaseV3.getDataSpecs(bulkOnboardingOptions, null)
            return globalPayrollBulkOnboardingUseCaseV3
                .getFieldData(dataSpecs, bulkOnboardingOptions, request.pageRequest)
                .flatMap { it.toRowData(dataSpecs) }
        } else {
            emptyList()
        }
    }

    fun validate(request: BulkValidationRequest): List<ValidationResult> {
        logValidationRequestInfo(request)

        val (options, useCase) =
            when (request.useCase) {
                GLOBAL_PAYROLL_MEMBER_ONBOARDING -> {
                    val entity = validateAndExtractEntityDataOrThrow(request.inputs)
                    log.info { "Entity data: $entity" }
                    BulkOnboardingOptions(
                        CountryCode.valueOf(entity.countryCode),
                        ContractType.HR_MEMBER,
                        entity.companyId,
                        entity.entityId,
                        BulkOnboardingContext.GLOBAL_PAYROLL) to
                        globalPayrollBulkOnboardingUseCaseV3
                }
                FREELANCER_MEMBER_ONBOARDING -> {
                    val entity = validateAndExtractCompanyDataOrThrow(request.inputs)
                    log.info { "Entity data: $entity" }
                    BulkOnboardingOptions(
                        null,
                        ContractType.FREELANCER,
                        entity.id,
                        null,
                        BulkOnboardingContext.FREELANCER) to freelancerBulkOnboardingUseCase
                }
                AOR_MEMBER_ONBOARDING -> {
                    val entity = validateAndExtractCompanyDataOrThrow(request.inputs)
                    log.info { "Entity data: $entity" }
                    BulkOnboardingOptions(
                        null,
                        ContractType.CONTRACTOR,
                        entity.id,
                        null,
                        BulkOnboardingContext.AOR) to aorBulkOnboardingUseCase
                }
                EOR_MEMBER_ONBOARDING -> {
                    val entity = validateAndExtractCompanyDataOrThrow(request.inputs)
                    val contractType = ContractType.EMPLOYEE
                    val countryCode =
                        validateIfCountrySupportedForContractType(
                            request.inputs.validateAndGetCountryCode(), contractType)

                    log.info { "Entity data: $entity" }
                    BulkOnboardingOptions(
                        countryCode, contractType, entity.id, null, BulkOnboardingContext.EOR) to
                        eorBulkOnboardingUseCaseV2
                }
                else -> return emptyList()
            }

        val employeeDataInput = prepareEmployeeDataInput(request)
        val validationResults = useCase.validate(employeeDataInput, options)
        logValidationResultsInfo(validationResults)

        return processValidationResults(validationResults, employeeDataInput.employeeData, options)
    }

    private fun prepareEmployeeDataInput(request: BulkValidationRequest): EmployeeDataInput {
        val employeeDataInput =
            EmployeeDataInput(
                employeeData = request.inputs.map { it.toEmployeeData() },
                source = "BULK_UPLOAD",
                customParams = request.jsonCustomParams)
        return employeeDataInput.copy(
            employeeData =
                BulkEmployeeDataPresentation.unformatData(employeeDataInput.employeeData))
    }

    private fun processValidationResults(
        validationResults: BulkValidationResultV2,
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<ValidationResult> {
        val mergedResults =
            mergeValidationResults(validationResults, employeeData, options).map {
                it.copy(
                    validatedData =
                        if (it.success) it.validatedData + options.toMap() else it.validatedData)
            }
        return restoreValidationRequestIds(mergedResults, employeeData)
    }

    fun upsert(request: BulkUpsertRequest): List<UpsertResult> {
        log.info { "Upserting ${request.inputs.size} inputs for use case ${request.useCase}" }

        val (options, useCase) =
            when (request.useCase) {
                GLOBAL_PAYROLL_MEMBER_ONBOARDING -> {
                    val entity = extractEntityDataOrThrow(request.inputs)
                    log.info { "Entity data: $entity" }
                    BulkOnboardingOptions(
                        CountryCode.valueOf(entity.countryCode),
                        ContractType.HR_MEMBER,
                        entity.companyId,
                        entity.entityId,
                        BulkOnboardingContext.GLOBAL_PAYROLL) to
                        globalPayrollBulkOnboardingUseCaseV3
                }
                FREELANCER_MEMBER_ONBOARDING -> {
                    val entity = extractCompanyDataOrThrow(request.inputs)
                    log.info { "Entity data: $entity" }
                    BulkOnboardingOptions(
                        null,
                        ContractType.FREELANCER,
                        entity.id,
                        null,
                        BulkOnboardingContext.FREELANCER) to freelancerBulkOnboardingUseCase
                }
                AOR_MEMBER_ONBOARDING -> {
                    val entity = extractCompanyDataOrThrow(request.inputs)
                    log.info { "Entity data: $entity" }
                    BulkOnboardingOptions(
                        null,
                        ContractType.CONTRACTOR,
                        entity.id,
                        null,
                        BulkOnboardingContext.AOR) to aorBulkOnboardingUseCase
                }
                EOR_MEMBER_ONBOARDING -> {
                    val entity = extractCompanyDataOrThrow(request.inputs)
                    log.info { "Entity data: $entity" }
                    BulkOnboardingOptions(
                        null, ContractType.EMPLOYEE, entity.id, null, BulkOnboardingContext.EOR) to
                        eorBulkOnboardingUseCaseV2
                }
                else -> return emptyList()
            }
        val validationResults = unmergeToBulkValidationResults(request.inputs)
        logValidationResultsInfo(validationResults)
        val creationResult = useCase.create(validationResults, options)

        return request.inputs.map {
            val contractId = creationResult.getContractId(it.id)
            if (contractId != null)
                UpsertResult(
                    it.id,
                    success = true,
                    upsertedData = mapOf("contractId" to contractId.toString()),
                )
            else
                UpsertResult(
                    it.id,
                    success = false,
                )
        }
    }

    private fun extractEntityDataOrThrow(upsertInputs: List<UpsertInput>): CompanyEntity {
        require(upsertInputs.isNotEmpty()) { "Upsert inputs must not be empty" }

        val companyId =
            upsertInputs
                .mapNotNull { it.data["companyId"]?.toLongOrNull() }
                .distinct()
                .singleOrNull()
                ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                    "Exactly one company ID must be provided in upsert inputs")

        val entityId =
            upsertInputs
                .mapNotNull { it.data["entityId"]?.toLongOrNull() }
                .distinct()
                .singleOrNull()
                ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                    "Exactly one entity ID must be provided in upsert inputs")

        return findEntityOrThrow(entityId, companyId)
    }

    private fun extractCompanyDataOrThrow(upsertInputs: List<UpsertInput>): Company {
        require(upsertInputs.isNotEmpty()) { "Upsert inputs must not be empty" }

        val companyId =
            upsertInputs
                .mapNotNull { it.data["companyId"]?.toLongOrNull() }
                .distinct()
                .singleOrNull()
                ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                    "Exactly one company ID must be provided in upsert inputs")

        return companyServiceAdapter.getCompany(companyId)
    }

    private fun findEntityOrThrow(entityId: Long, companyId: Long): CompanyEntity {
        val entity =
            companyServiceAdapter.getLegalEntity(entityId)
                ?: throw ErrorCodes.LEGAL_ENTITY_NOT_FOUND.toBusinessException(
                    "Entity with ID $entityId not found")
        require(entity.companyId == companyId) {
            throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                "Entity with ID $entityId is not associated with company with ID $companyId")
        }
        val countryCode = entity.address.country
        require(!countryCode.isNullOrBlank()) {
            throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                "Country code is not provided for entity with ID $entityId")
        }

        return CompanyEntity(companyId, entityId, countryCode)
    }

    private fun validateAndExtractEntityDataOrThrow(
        validationInputs: List<ValidationInput>
    ): CompanyEntity {
        require(validationInputs.isNotEmpty()) {
            throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                "Validation inputs must not be empty")
        }

        val companyId =
            validationInputs.map { it.companyId }.distinct().singleOrNull()
                ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                    "Exactly one company ID must be provided in validation inputs")

        val entityId =
            validationInputs.mapNotNull { it.entityId }.distinct().singleOrNull()
                ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                    "Exactly one entity ID must be provided in validation inputs")

        return findEntityOrThrow(entityId, companyId)
    }

    private fun validateAndExtractCompanyDataOrThrow(
        validationInputs: List<ValidationInput>
    ): Company {
        require(validationInputs.isNotEmpty()) {
            throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                "Validation inputs must not be empty")
        }

        val companyId =
            validationInputs.map { it.companyId }.distinct().singleOrNull()
                ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                    "Exactly one company ID must be provided in validation inputs")

        return companyServiceAdapter.getCompany(companyId)
    }

    private fun validateAndExtractEntityDataOrThrow(
        request: FieldRequirementsRequest
    ): CompanyEntity {
        val companyId =
            request.companyId
                ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                    "Company ID is not provided")
        val entityId =
            request.entityId
                ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                    "Legal Entity ID is not provided")
        return findEntityOrThrow(entityId, companyId)
    }

    private fun validateAndExtractCompanyDataOrThrow(request: FieldRequirementsRequest): Company {
        request.companyId
            ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException("Company ID is not provided")

        return companyServiceAdapter.getCompany(request.companyId)
    }

    private fun BulkOnboardingOptions.toMap(): Map<String, String> {
        val result =
            mutableMapOf(
                "companyId" to companyId.toString(),
                "contractType" to contractType.name,
                "context" to context.name,
            )

        when (context) {
            BulkOnboardingContext.EOR -> {
                result["countryCode"] = countryCode.name
            }
            BulkOnboardingContext.FREELANCER,
            BulkOnboardingContext.AOR -> {
                // not setting country in case if freelancer since it is passed from the individual
                // rows
            }
            else -> {
                result["countryCode"] = countryCode.name
                result["entityId"] = entityId.toString()
            }
        }

        return result
    }

    private fun logValidationRequestInfo(request: BulkValidationRequest) {
        log.info { "Validating ${request.inputs.size} inputs for use case ${request.useCase}" }
        val groups = request.inputs.groupBy { it.group.orEmpty() }.map { it.key to it.value.size }
        log.info {
            "Input size per group: ${groups.joinToString { (group, count) -> "$group: $count" }}"
        }
    }

    private fun logValidationResultsInfo(validationResults: BulkValidationResultV2) {
        val groups =
            validationResults.validationResults.map {
                Triple(it.key, it.value.successCount(), it.value.failureCount())
            }
        log.info {
            "Validation results: ${groups.joinToString { (group, success, failure) -> "$group: success: $success, failure: $failure" }}"
        }
    }
}

private fun DataSpec.toFieldRequirement() =
    FieldRequirement(
        key = this.keyWithPrefix(),
        label = this.label,
        type = FieldType.valueOf(this.type.name),
        description = this.description,
        mandatory = this.mandatory,
        allowedValues = this.allowedValues,
        group = this.group)

fun ValidationInput.toEmployeeData() =
    EmployeeData(
        identification =
            EmployeeIdentification(
                employeeId = this.employeeId(),
                contractId = this.contractId,
                rowIdentifier = this.id,
                rowIdentifierFromSheet = this.sheetRowIdentifier(),
                rowNumber = 0,
            ),
        data = this.data + this.keysToDataMap(),
        group = this.group.orEmpty().ifEmpty { EMPLOYMENT_DATA_GROUP })

private fun ValidationInput.keysToDataMap() =
    mapOfNotNull(
        ContractIdSpec.key to this.contractId?.toString(),
        CompanyIdSpec.key to this.companyId.toString(),
        LegalEntityIdSpec.key to this.entityId?.toString(),
    )

fun List<ValidationInput>.validateAndGetCountryCode(): String {
    val distinctCountryCodes = this.mapNotNull { it.countryCode }.toSet()

    if (distinctCountryCodes.size != 1)
        throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
            "Single Country code is required for EOR got: $distinctCountryCodes")

    return distinctCountryCodes.first()
}

fun ValidationInput.employeeId() = this.data[EmployeeIdSpec.key]

fun ValidationInput.sheetRowIdentifier() = this.data[RowIdentifierSpec.key]

private fun List<GrpcValidationResult<out Any>>.successCount() = this.count { it.success }

private fun List<GrpcValidationResult<out Any>>.failureCount() = this.count { !it.success }

private fun EmployeeDataChunk.toRowData(dataSpecs: List<DataSpec>): List<RowData> {
    val groupToDataSpecs = dataSpecs.groupBy { it.group }
    return groupToDataSpecs.mapNotNull { (group, specs) ->
        val keys = PlatformKeys.newBuilder()
        this.data[ContractIdSpec.key]?.toLongOrNull()?.let { keys.contractId = it }
        val data =
            specs
                .associate { it.keyWithPrefix() to this.data[it.keyWithPrefix()] }
                .filterNonNullValues()
        if (isEmptyEmployeeData(data)) null
        else RowData(data = data, key = keys.build(), group = group)
    }
}

/**
 * Besides the employee ID and contract ID, the employee data should have other data, otherwise it
 * is considered as empty.
 */
private fun isEmptyEmployeeData(data: Map<String, String>): Boolean {
    return data.isEmpty() ||
        data.keys
            .filterNot { it == EmployeeIdSpec.key }
            .filterNot { it == ContractIdSpec.key }
            .isEmpty()
}
