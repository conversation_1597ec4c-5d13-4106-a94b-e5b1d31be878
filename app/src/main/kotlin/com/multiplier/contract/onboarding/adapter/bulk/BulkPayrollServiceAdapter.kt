package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.types.CountryCode
import org.apache.commons.io.IOUtils
import org.springframework.stereotype.Service

data class Allowance(val name: String, val label: String, val mandatory: Boolean = false)

@Service
class BulkPayrollServiceAdapter {

    companion object {

        private val defaultAllowances =
            listOf(
                Allowance("internetAllowance", "Internet Allowance"),
                Allowance("phoneAllowance", "Mobile and Phone Allowance"),
                Allowance("otherAllowance", "Other Allowance"),
            )

        private val countryCodeToAllowances: Map<CountryCode, List<Allowance>> by lazy {
            getAllowanceStructureConfig()
        }

        private fun getAllowanceStructureConfig(): Map<CountryCode, List<Allowance>> {
            val countryCodeToAllowances = mutableMapOf<CountryCode, List<Allowance>>()
            val lines =
                IOUtils.readLines(
                        this::class
                            .java
                            .classLoader
                            .getResourceAsStream("bulk-onboarding/allowance-structure.csv"),
                        "UTF-8")
                    .filterNot { it.isNullOrBlank() }

            for (line in lines) {
                val (countryCode, allowance) = parseAllowance(line)
                countryCodeToAllowances[countryCode] =
                    countryCodeToAllowances.getOrDefault(countryCode, listOf()) + listOf(allowance)
            }

            return countryCodeToAllowances
        }

        private fun parseAllowance(line: String): Pair<CountryCode, Allowance> {
            val values = line.split(",").map { it.trim() }
            val countryCode = CountryCode.valueOf(values[0])
            val allowanceKey = values[1]
            val allowanceName = values[2]
            val mandatory = values[3].toBoolean()

            val allowance = Allowance(allowanceKey, allowanceName, mandatory)
            return countryCode to allowance
        }
    }

    fun getAllowanceStructure(countryCode: CountryCode): List<Allowance> {
        return countryCodeToAllowances[countryCode] ?: defaultAllowances
    }
}
