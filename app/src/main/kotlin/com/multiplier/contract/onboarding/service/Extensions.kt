package com.multiplier.contract.onboarding.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.readValue
import com.google.protobuf.Message
import com.google.protobuf.MessageOrBuilder
import com.google.protobuf.Timestamp
import com.google.protobuf.util.JsonFormat
import com.google.type.Date
import com.multiplier.contract.onboarding.adapter.CountryAndState
import com.multiplier.contract.onboarding.config.objectMapper
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

fun Any.convertToMap(overrides: Map<String, Any>? = null): Map<String, Any> {
    val original = objectMapper.convertValue<Map<String, Any>>(this)

    if (overrides == null) {
        return original
    }

    return original.toMutableMap().apply { putAll(overrides) }
}

fun YearMonth?.toFormattedDateOrEmpty(): String =
    if (this == null) "" else this.format(DateTimeFormatter.ofPattern("MMM, yyyy"))

fun LocalDate?.toFormattedDateOrEmpty(): String =
    if (this == null) "" else this.format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))

fun String.urlEncode(): String = URLEncoder.encode(this, StandardCharsets.UTF_8)

fun Timestamp.toLocalDateTime(): LocalDateTime? =
    if (this == Timestamp.getDefaultInstance()) null
    else LocalDateTime.ofEpochSecond(this.seconds, this.nanos, ZoneOffset.UTC)

fun Timestamp.toLocalDate(): LocalDate? = this.toLocalDateTime()?.toLocalDate()

fun LocalDateTime.toTimeStamp(): Timestamp =
    this.toInstant(ZoneOffset.UTC).let {
        Timestamp.newBuilder().setSeconds(it.epochSecond).setNanos(it.nano).build()
    }

fun LocalDate.toGrpcDate(): Date =
    Date.newBuilder().setYear(this.year).setMonth(this.monthValue).setDay(this.dayOfMonth).build()

fun LocalDateTime.toGrpcDate(): Date =
    Date.newBuilder().setYear(this.year).setMonth(this.monthValue).setDay(this.dayOfMonth).build()

fun Date.toLocalDate(): LocalDate = LocalDate.of(this.year, this.month, this.day)

/**
 * Handle camel case, snake case, kebab case, dot notation string to words Camel case example:
 * "helloWorld" -> "Hello World" Dot notation example: "hello.world" -> "Hello World" Combination
 * example: "hello.programmingWorld" -> "Hello Programming World"
 */
fun String.toWords(): String {
    val words = this.splitToWords()
    return words.joinToString(" ") { word -> word.lowercase().replaceFirstChar { it.titlecase() } }
}

fun String.toCamelCase(): String {
    val words = this.splitToWords()
    return words
        .joinToString("") { word -> word.lowercase().replaceFirstChar { it.titlecase() } }
        .replaceFirstChar { it.lowercase() }
}

fun String.toSnakeCase(): String {
    val words = this.splitToWords()
    return words.joinToString("_") { it.lowercase() }
}

fun String.splitToWords(): List<String> {
    return if (this.any { it.isLowerCase() }) {
        // If the string contains lowercase letters, split by camel case and delimiters
        this.split(Regex("(?<!^)(?=[A-Z])|[\\s_.-]")).filter { it.isNotEmpty() }.map { it.trim() }
    } else {
        // If the string does not contain lowercase letters, split only by delimiters
        this.split(Regex("[\\s_.-]")).filter { it.isNotEmpty() }.map { it.trim() }
    }
}

inline fun <reified T> ObjectMapper.jsonAt(path: String, json: String) =
    this.readValue<T>(this.readTree(json).at(path).toString())

val Contract.countryAndState
    get() =
        CountryAndState(
            country = CountryCode.valueOf(this.country),
            state = this.countryStateCode.ifBlank { null })

/**
 * Converts a proto message to a map of string keys and string values. Let's take
 * `CreateContractInput` as an example: CreateContractInput { companyId: 123, country: "SGP", type:
 * "HR_MEMBER", startOn: { year: 2023, month: 1, day: 1 } } will be converted to: { "companyId":
 * "123", "country": "SGP", "type": "HR_MEMBER", "startOn.year": "2023", "startOn.month": "1",
 * "startOn.day": "1" }
 */
fun protoMessageToMap(protoMessage: Any?): Map<String, String> {
    if (protoMessage == null || protoMessage !is MessageOrBuilder) {
        return emptyMap()
    }
    val jsonString = JsonFormat.printer().print(protoMessage)
    val objectMapper = ObjectMapper().registerModule(KotlinModule.Builder().build())

    val mapObject = objectMapper.readValue<Map<String, Any>>(jsonString)
    return mapObject.toStringMap("->")
}

private fun <K, V> Map<K, V>.toStringMap(delimiter: String): Map<String, String> {
    // find maps and non maps
    val (nestedMaps, nonMaps) = this.entries.partition { it.value is Map<*, *> }
    // find list from non maps
    val (lists, direct) = nonMaps.partition { it.value is List<*> }
    // add direct keys
    val directMap = direct.associate { it.key.toString() to it.value.toString() }

    // process maps and add them
    val nestedMap = mutableMapOf<String, String>()
    nestedMaps.forEach {
        nestedMap.putAll(
            (it.value as Map<*, *>).toStringMap(delimiter).mapKeys { entry ->
                "${it.key}${delimiter}${entry.key}"
            })
    }

    // process lists and add them with index
    lists.forEach { (key, value) ->
        if (value is List<*>) {
            value.forEachIndexed { index, item ->
                if (item is Map<*, *>) {
                    nestedMap.putAll(
                        item.toStringMap(delimiter).mapKeys { entry ->
                            "$key->[$index]${delimiter}${entry.key}"
                        })
                } else {
                    nestedMap["$key${delimiter}[$index]"] = item?.toString() ?: "null"
                }
            }
        }
    }

    return directMap + nestedMap
}

fun Map<String, String>.toProtoMessage(messageBuilder: Message.Builder): MessageOrBuilder {
    val jsonString = objectMapper.writeValueAsString(this.toNestedMap())
    JsonFormat.parser().merge(jsonString, messageBuilder)
    return messageBuilder.build()
}

private fun Map<String, String>.toNestedMap(): Map<String, Any> {
    val nestedMap = mutableMapOf<String, Any>()
    val listRegex = Regex("(.+)\\[(-?\\d+)]")

    for ((key, value) in this) {
        // Splits but keeps `->[0]` intact
        val keys = key.split("->(?=[^\\[]|$)".toRegex())
        var currentMap = nestedMap

        for ((index, part) in keys.withIndex()) {
            val listMatch = listRegex.matchEntire(part)

            // if an index like ->[0] is present we will create a list else it will create a map
            if (listMatch != null) {
                val (listKey, listIndex) = listMatch.destructured
                val cleanedListKey = listKey.removeSuffix("->")
                val idx = listIndex.toInt()

                val list =
                    currentMap.getOrPut(cleanedListKey) { mutableListOf<Any>() } as MutableList<Any>

                // Ensure list has enough elements
                while (list.size <= idx) list.add(mutableListOf<Any>())

                // If it's the last key, store the value in the list as there are just values in the
                // list else store it as map
                // else there are keys inside the array and we can change list to map and set map
                // values in the next iteration
                if (index == keys.size - 1) {
                    list[idx] = value
                } else {
                    // Ensure the next level is a map
                    if (list[idx] !is MutableMap<*, *>) {
                        list[idx] = mutableMapOf<String, Any>()
                    }
                    currentMap = list[idx] as MutableMap<String, Any>
                }
            } else {
                if (index == keys.size - 1) {
                    currentMap[part] = value
                } else {
                    if (currentMap[part] !is MutableMap<*, *>) {
                        currentMap[part] = mutableMapOf<String, Any>()
                    }
                    currentMap = currentMap[part] as MutableMap<String, Any>
                }
            }
        }
    }
    return nestedMap
}

fun <K, V : Any> mapOfNotNull(vararg pairs: Pair<K, V?>): Map<K, V> {
    return pairs.toMap().filterNonNullValues()
}

fun <K, V : Any> Map<K, V?>.filterNonNullValues(): Map<K, V> {
    return this.filterValues { it != null }.mapValues { it.value!! }
}
