package com.multiplier.contract.onboarding.service.bulk.creation

import com.multiplier.contract.onboarding.service.bulk.mapper.ContractContext
import com.multiplier.contract.onboarding.service.bulk.mapper.MemberContext
import com.multiplier.contract.onboarding.service.bulkupload.ValidationIdsOfContract

data class BulkCreationResult(
    val memberContext: MemberContext,
    val contractContext: ContractContext,
    val errors: List<String>,
    private val validationIdsOfContracts: List<ValidationIdsOfContract> = emptyList(),
) {

    val success: Boolean
        get() =
            memberContext.requestIdToMemberId.isNotEmpty() &&
                memberContext.requestIdToMemberId.size ==
                    contractContext.requestIdToContractId.size &&
                errors.isEmpty()

    fun getContractId(requestId: String): Long? =
        contractContext.getContractId(requestId)
            ?: contractContext.getContractId(
                findExistingRequestIdOfSameContractByRequestId(requestId))

    private fun findExistingRequestIdOfSameContractByRequestId(requestId: String): String? {
        val requestIdsOfSameContract =
            validationIdsOfContracts.find { it.ids.contains(requestId) }?.ids
        return requestIdsOfSameContract?.find { contractContext.containsRequestId(it) }
    }
}
