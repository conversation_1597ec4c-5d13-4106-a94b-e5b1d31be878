package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.OnboardingServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.contants.OnboardingExperience
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationResults
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.CreateContractInput
import com.multiplier.country.schema.Country.GrpcCountryCode
import com.multiplier.member.schema.Member
import com.multiplier.member.schema.Member.MemberNationalogy
import com.multiplier.member.schema.MemberCreateInput
import mu.KotlinLogging
import org.springframework.stereotype.Service

data class OnboardingStatusAndStep(val status: OnboardingStatus, val step: OnboardingStep)

@Service
class BulkOnboardingDataService(
    private val bulkOnboardingBuilderForHRISService: BulkOnboardingBuilderForHRISService,
    private val onboardingServiceAdapter: OnboardingServiceAdapter
) {

    private val log = KotlinLogging.logger {}

    fun createOnboardingEntities(
        contractIds: List<Long>,
        options: BulkOnboardingOptions,
        contractContext: ContractContext = ContractContext.EMPTY,
        validationResults: EmployeeValidationResults = EmployeeValidationResults.EMPTY,
    ): List<String> {
        val contractIdToOnboardingStatusAndStep = lazy {
            getOnboardingStatusAndStepByContractId(validationResults, contractContext)
        }
        val onboardings =
            when (options.context) {
                BulkOnboardingContext.HRIS_PROFILE_DATA ->
                    bulkOnboardingBuilderForHRISService.buildOnboardingsForHrisProfileData(
                        contractIds)
                BulkOnboardingContext.EOR ->
                    buildOnboardingsForEOR(contractIds, contractIdToOnboardingStatusAndStep.value)
                BulkOnboardingContext.AOR -> buildOnboardingsForAOR(contractIds)
                BulkOnboardingContext.FREELANCER -> buildOnboardingsForFreelancer(contractIds)
                else ->
                    throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                        "Bulk onboarding entity creation for ${options.context} not supported yet",
                        context = mapOf("context" to options.context))
            }

        return try {
            onboardingServiceAdapter.upsertOnboardings(onboardings)
            log.info { "Created onboarding entities for contract: $contractIds" }
            emptyList()
        } catch (e: Exception) {
            log.warn(e) { "Failed to create onboarding entities for contract ids: $contractIds" }
            listOf("Failed to create onboarding entities, unknown exception occurred")
        }
    }

    fun updateOnboardingForDraftContracts(
        contractIdList: List<Long>,
        contractContext: ContractContext,
        validationResults: EmployeeValidationResults,
    ): List<String> {
        if (contractIdList.isEmpty()) {
            return emptyList()
        }
        return try {
            val onboardingByContractIds =
                onboardingServiceAdapter.getAllOnboardingsByContractIdsAndStatus(
                    contractIdList.toSet(),
                    ContractOnboardingStatus.DRAFT,
                    OnboardingExperience.COMPANY.value)
            if (onboardingByContractIds.isNotEmpty()) {
                val contractIdToOnboardingStatusAndStep =
                    getOnboardingStatusAndStepByContractId(validationResults, contractContext)
                val toUpdate =
                    onboardingByContractIds.map { (contractId, onboarding) ->
                        onboarding.copy(
                            status =
                                contractIdToOnboardingStatusAndStep.getValue(contractId).status,
                            currentStep =
                                contractIdToOnboardingStatusAndStep.getValue(contractId).step,
                        )
                    }
                onboardingServiceAdapter.upsertOnboardings(toUpdate)
            }
            emptyList()
        } catch (ex: Exception) {
            log.warn { "Failed to update onboarding entities for contract ids: $contractIdList" }
            listOf("Failed to update onboarding entities, unknown exception occurred")
        }
    }

    private fun buildOnboardingsForEOR(
        contractIds: List<Long>,
        contractIdToOnboardingStatusAndStep: Map<Long, OnboardingStatusAndStep>,
    ): List<Onboarding> {
        return contractIds.map {
            Onboarding(
                contractId = it,
                experience = OnboardingExperience.COMPANY.value,
                status = contractIdToOnboardingStatusAndStep.getValue(it).status,
                currentStep = contractIdToOnboardingStatusAndStep.getValue(it).step,
                isBulkOnboarded = true)
        }
    }

    private fun buildOnboardingsForAOR(contractIds: List<Long>): List<Onboarding> {
        return contractIds.map {
            Onboarding(
                contractId = it,
                experience = "company",
                status = OnboardingStatus.CREATED,
                currentStep = OnboardingStep.ONBOARDING_REVIEW,
                isBulkOnboarded = true)
        }
    }

    private fun buildOnboardingsForFreelancer(contractIds: List<Long>): List<Onboarding> {
        return contractIds.map {
            Onboarding(
                contractId = it,
                experience = "company",
                status = OnboardingStatus.DRAFT,
                currentStep = OnboardingStep.DEFINITION_BENEFITS_DETAILS,
                isBulkOnboarded = true)
        }
    }

    private fun getOnboardingStatusAndStepByContractId(
        validationResults: EmployeeValidationResults,
        contractContext: ContractContext,
    ): Map<Long, OnboardingStatusAndStep> {
        val validationIdToContractResults =
            validationResults.contracts.associateBy { it.validationId }
        val validationIdToMemberResult = validationResults.members.associateBy { it.validationId }
        return contractContext.requestIdToContractId.keys.associate { validationId ->
            contractContext.getContractId(validationId)!! to
                buildOnboardingStatusAndStep(
                    validationIdToContractResults[validationId],
                    validationIdToMemberResult[validationId])
        }
    }

    private fun buildOnboardingStatusAndStep(
        contractValidationResult: GrpcValidationResult<CreateContractInput>?,
        memberValidationResult: GrpcValidationResult<MemberCreateInput>?,
    ): OnboardingStatusAndStep {
        return if (isRequiredContractPreparingConfirmationStep(
            contractValidationResult, memberValidationResult)) {
            OnboardingStatusAndStep(
                step = OnboardingStep.ONBOARDING_CONTRACT_PREPARING_CONFIRMATION,
                status = OnboardingStatus.CONTRACT_PREPARING_CONFIRMATION)
        } else {
            OnboardingStatusAndStep(
                step = OnboardingStep.ONBOARDING_REVIEW,
                status = OnboardingStatus.CREATED,
            )
        }
    }

    private fun isRequiredContractPreparingConfirmationStep(
        contractValidationResult: GrpcValidationResult<CreateContractInput>?,
        memberValidationResult: GrpcValidationResult<MemberCreateInput>?,
    ): Boolean {
        return isResident(contractValidationResult?.input?.workStatus) &&
            isEOR(contractValidationResult?.input?.type) &&
            !matchNationality(
                memberValidationResult?.input?.nationality,
                contractValidationResult?.input?.country)
    }

    private fun isResident(workStatus: ContractOuterClass.CountryWorkStatus?): Boolean =
        ContractOuterClass.CountryWorkStatus.RESIDENT == workStatus

    private fun isEOR(contractType: ContractOuterClass.ContractType?): Boolean =
        ContractOuterClass.ContractType.EMPLOYEE == contractType

    private fun matchNationality(
        memberNationality: MemberNationalogy?,
        contractCountry: GrpcCountryCode?,
    ): Boolean {
        if (memberNationality == null || contractCountry == null) return false
        return memberNationality.type == Member.Nationalogy.CITIZEN &&
            contractCountry.name.equals(memberNationality.country.name, true)
    }
}
