package com.multiplier.contract.onboarding.domain.model

import com.multiplier.contract.onboarding.types.CurrencyCode

data class PaymentAccountRequirement(
    val accountType: String,
    val transferType: String,
    val requirementType: String,
    val sourceCurrency: CurrencyCode,
    val targetCurrency: CurrencyCode,
    val requiredDataFields: List<PaymentAccountDataField>,
)

data class PaymentAccountDataField(
    val key: String,
    val label: String,
    val type: String,
    val mandatory: Boolean,
    val allowedValues: List<String> = emptyList()
)
