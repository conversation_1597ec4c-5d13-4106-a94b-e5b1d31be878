package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.CompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.OnboardingServiceAdapter
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.isAfter
import com.multiplier.contract.onboarding.domain.model.ComplianceType
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.domain.model.PayrollCycle
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.PayrollCyclesService
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.CountryWorkStatus
import java.time.LocalDate
import org.springframework.stereotype.Component

@Component
class ReminderContractFinderHelper(
    private val onboardingServiceAdapter: OnboardingServiceAdapter,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val payrollCyclesService: PayrollCyclesService,
    private val compensationServiceAdapter: CompensationServiceAdapter,
    private val featureFlagService: FeatureFlagService,
) {
    fun parkContractsIntoPayrollCycles(
        contracts: List<Contract>,
        sendingDate: LocalDate,
    ): List<PayrollCycleContracts> {
        return payrollCyclesService
            .getPayrollCyclesForContracts(sendingDate, contracts)
            .entries
            .groupBy { it.value }
            .map { entry ->
                val payrollCycle = entry.key
                val contractIds = entry.value.map { it.key }

                PayrollCycleContracts(
                    payFrequency =
                        PayrollCycle.PayFrequency.valueOf(payrollCycle.payFrequency.name),
                    payrollMonth = payrollCycle.payrollMonth,
                    cutoffDate = payrollCycle.cutoffDate,
                    contractIds = contractIds,
                    contractStartFrom = payrollCycle.startDate,
                    contractStartTo = payrollCycle.endDate,
                    // do not worry, below values only used for old code
                    payDay = payrollCycle.cutoffDate.dayOfMonth,
                    payDate = payrollCycle.cutoffDate,
                )
            }
    }

    fun filterEligibleContracts(contracts: Collection<Contract>): List<Contract> {
        val contractById = contracts.associateBy { it.id }

        val companyExpOnboardingByContractId =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(contractById.keys, "company")
        val complianceByContractId =
            contractServiceAdapter.getComplianceByContractIds(contractById.keys).associateBy {
                it.contractId
            }

        return contracts.filter { contract ->
            val onboarding = companyExpOnboardingByContractId[contract.id]
            onboarding != null &&
                onboarding.status !== OnboardingStatus.REVOKED &&
                isOnboardingNotificationFeatureFlagOn(contract.companyId) &&
                hasPassedCompensationDefinitionSteps(onboarding) &&
                isMultiplierContractUnderMultiplierEntity(
                    contract, complianceByContractId[contract.id])
        }
    }

    fun filterMonthlyPayrollContracts(contracts: List<Contract>): List<Contract> {
        if (contracts.isEmpty()) {
            return emptyList()
        }

        val compensationByContractId =
            compensationServiceAdapter.getCurrentCompensationByContractIds(
                contracts.map { it.id }.toSet())

        return contracts.filter { hasMonthlyPayroll(compensationByContractId[it.id]) }
    }

    private fun hasMonthlyPayroll(compensation: Compensation?): Boolean {
        return compensation?.postProbationBasePay?.payFrequency ===
            CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY
    }

    private fun hasPassedCompensationDefinitionSteps(onboarding: Onboarding): Boolean {
        return onboarding.status.isAfter(OnboardingStatus.DRAFT) &&
            onboarding.currentStep.isAfter(OnboardingStep.DEFINITION_COMPENSATION_DETAILS)
    }

    private fun isMultiplierContractUnderMultiplierEntity(
        contract: Contract,
        compliance: com.multiplier.contract.onboarding.domain.model.Compliance?
    ): Boolean {
        return contract.workStatus === CountryWorkStatus.RESIDENT &&
            compliance != null &&
            compliance.type === ComplianceType.MULTIPLIER &&
            compliance.agreementType ===
                com.multiplier.contract.onboarding.domain.model.ContractAgreementType
                    .MULTIPLIER_TEMPLATE
    }

    private fun isOnboardingNotificationFeatureFlagOn(companyId: Long): Boolean {
        return featureFlagService
            .feature(
                FeatureFlags.ONBOARDING_NOTIFICATION,
                mapOf(FeatureFlags.Params.COMPANY to companyId))
            .on
    }
}
