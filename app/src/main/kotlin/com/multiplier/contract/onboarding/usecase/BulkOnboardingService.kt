package com.multiplier.contract.onboarding.usecase

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.CompensationSourceServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingJob
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingNotificationService
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingServiceFactoryCreator
import com.multiplier.contract.onboarding.service.bulk.creation.BulkCreationResult
import com.multiplier.contract.onboarding.service.bulk.excel.BulkUploadExcel
import com.multiplier.contract.onboarding.service.bulk.validation.*
import com.multiplier.contract.onboarding.types.*
import java.io.InputStream
import java.time.LocalDateTime
import java.util.*
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.stereotype.Service

@Service
class BulkOnboardingService(
    private val bulkOnboardingServiceFactoryCreator: BulkOnboardingServiceFactoryCreator,
    private val bulkValidationHelper: BulkValidationHelper,
    private val notificationService: BulkOnboardingNotificationService,
    private val compensationSourceServiceAdapter: CompensationSourceServiceAdapter
) : BulkOnboardingServiceInterface {

    private val log = KotlinLogging.logger {}

    override fun generateBulkOnboardingTemplate(
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?
    ): DocumentReadable {
        logOnboardingContext(options)

        // Throws error in case compensation is enabled on compensation service if this is the case
        // client needs to use V2 endpoint
        validateIfCountrySupportedForContractType(options)

        val dataSpecs = getDataSpec(options)
        log.info {
            "Generation bulk onboarding template with ${dataSpecs.size} data specs + pagination: $pageRequest"
        }
        val dataChunks =
            bulkOnboardingServiceFactory(options)
                .bulkDataService()
                .getDataForSpecs(dataSpecs, options, pageRequest)

        return BulkUploadExcel.generate(options, dataSpecs, dataChunks)
    }

    override fun getDataSpec(options: BulkOnboardingOptions): List<DataSpec> =
        bulkOnboardingServiceFactory(options).bulkDataSpecsService().getDataSpecs(options)

    override fun validate(
        employeeDataFile: InputStream,
        options: BulkOnboardingOptions,
        source: String,
    ): BulkValidationReport {
        // Throws error in case compensation is enabled on compensation service if this is the case
        // client needs to use V2 endpoint
        validateIfCountrySupportedForContractType(options)
        val employeeDataInput = parseEmployeeDataFile(employeeDataFile).copy(source = source)
        return validate(employeeDataInput, options).validationReport
    }

    override fun validate(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): ValidationResults {
        logOnboardingContext(options)

        val mergedEmployeeData =
            bulkOnboardingServiceFactory(options)
                .bulkValidationService()
                .mergeEmployeeData(employeeDataInput.employeeData)
        val enrichedEmployeeData =
            bulkOnboardingServiceFactory(options)
                .bulkEnrichmentService()
                .enrichEmployeeData(mergedEmployeeData, options)
        val validationService = bulkOnboardingServiceFactory(options).bulkValidationService()
        validationService.validateCompanyOrThrow(options)
        validationService.validateCompanyLegalEntityOrThrow(options)

        val genericValidationResult =
            bulkValidationHelper.genericValidate(enrichedEmployeeData, options)
        val employeeValidationResults =
            validationService.validateEmployeeData(enrichedEmployeeData, options)
        val aggregatedEmployeeValidationResults =
            aggregate(employeeValidationResults, enrichedEmployeeData)
        if (aggregatedEmployeeValidationResults.errors.isNotEmpty()) {
            log.warn {
                "Validation errors found: ${aggregatedEmployeeValidationResults.errors.size} errors"
            }
        }

        return ValidationResults(
            employeeValidationResults = employeeValidationResults,
            validationReport =
                createValidationReport(
                    genericValidationResult,
                    aggregatedEmployeeValidationResults,
                    employeeDataInput.templateData?.reportTemplate))
    }

    override fun onboard(
        employeeDataFile: InputStream,
        options: BulkOnboardingOptions
    ): BulkOnboardingJob {
        // Throws error in case compensation is enabled on compensation service if this is the case
        // client needs to use V2 endpoint
        validateIfCountrySupportedForContractType(options)
        val employeeDataInput = parseEmployeeDataFile(employeeDataFile)
        return onboard(employeeDataInput, options)
    }

    private fun validateIfCountrySupportedForContractType(
        options: BulkOnboardingOptions,
    ) {
        if (options.contractType != ContractType.EMPLOYEE) {
            return
        }

        val isCompEnabled =
            compensationSourceServiceAdapter
                .isNewCompensationSourceEnabledForCountries(
                    options.contractType, listOf(options.countryCode))
                .getOrDefault(options.countryCode, false)

        if (isCompEnabled) {
            throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                "New compensation service enabled for country ${options.countryCode.name} for type ${options.contractType.name}")
        }
    }

    override fun onboard(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): BulkOnboardingJob {
        logOnboardingContext(options)

        val jobId = UUID.randomUUID().toString()
        val startTime = LocalDateTime.now()
        withLoggingContext(
            "bulk-onboarding-job-id" to jobId,
            "bulk-onboarding-service-version" to "v1",
        ) {
            log.info("Bulk onboarding job [$jobId] started")

            val validationResults = validate(employeeDataInput, options)
            val bulkCreationResult = create(validationResults, options)
            val onboardingJob =
                BulkOnboardingJob.complete(
                    jobId, startTime, validationResults.validationReport, bulkCreationResult)
            log.info {
                "Bulk onboarding job [${jobId}] completed with status: ${onboardingJob.status}, duration: ${onboardingJob.durationInSeconds} seconds"
            }

            notificationService.sendOnboardingCompletedNotification(
                options, onboardingJob, employeeDataInput.source)

            return onboardingJob
        }
    }

    private fun create(
        validationResults: ValidationResults,
        options: BulkOnboardingOptions
    ): BulkCreationResult? {
        if (validationResults.validationReport.hasErrors()) {
            return null
        }

        log.info {
            "Creating employee data, size = ${validationResults.employeeValidationResults.members.size}"
        }

        return bulkOnboardingServiceFactory(options)
            .bulkCreationService()
            .create(validationResults.employeeValidationResults, options)
            .also { log.info { "Creation completed" } }
    }

    private fun bulkOnboardingServiceFactory(options: BulkOnboardingOptions) =
        bulkOnboardingServiceFactoryCreator.getServiceFactory(options.context)

    private fun logOnboardingContext(options: BulkOnboardingOptions) {
        log.info { "Onboarding context: $options" }
    }
}

fun parseEmployeeDataFile(employeeDataFile: InputStream): EmployeeDataInput {
    val log = KotlinLogging.logger {}
    val inputByteArray = employeeDataFile.readBytes()
    return try {
        parseEmployeeDataFile(inputByteArray)
    } catch (e: Exception) {
        log.error(e) { "Failed to parse bulk onboarding template" }
        EmployeeDataInput.EMPTY
    }
}

fun parseEmployeeDataFile(inputByteArray: ByteArray): EmployeeDataInput {
    return BulkUploadExcel.parse(inputByteArray)
}
