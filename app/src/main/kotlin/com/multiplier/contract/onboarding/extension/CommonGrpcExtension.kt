package com.multiplier.contract.onboarding.extension

import com.multiplier.contract.onboarding.domain.model.PayrollCycleDTO
import com.multiplier.payroll.schema.Payroll
import com.multiplier.schema.common.Common
import java.time.LocalDate
import java.time.YearMonth

fun Payroll.PayrollCycle.toDTO(): PayrollCycleDTO {
    return PayrollCycleDTO(
        payFrequency = PayrollCycleDTO.PayFrequency.valueOf(this.frequency),
        payrollMonth = this.payrollMonth.toYearMonth(),
        cutoffDate = this.cutOffTo.toLocalDate(),
        startDate = this.startDate.toLocalDate(),
        endDate = this.endDate.toLocalDate(),
    )
}

fun Common.Date.toLocalDate(): LocalDate = LocalDate.of(this.year, this.month, this.day)

fun Common.YearMonth.toYearMonth(): YearMonth = YearMonth.of(this.year, this.month)
