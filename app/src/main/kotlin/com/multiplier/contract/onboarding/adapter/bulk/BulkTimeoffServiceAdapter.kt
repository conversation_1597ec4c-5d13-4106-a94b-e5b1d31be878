package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.timeoff.schema.TimeOffServiceGrpc.TimeOffServiceBlockingStub
import com.multiplier.timeoff.schema.grpcContractIds
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface BulkTimeoffServiceAdapter {

    fun createDefaultTimeoffEntitlementForContracts(
        inputs: List<CreationInput<Long>>,
    ): List<CreationResult>
}

@Service
class BulkTimeoffServiceAdapterImpl : BulkTimeoffServiceAdapter {

    @GrpcClient("timeoff-service") private lateinit var blockingStub: TimeOffServiceBlockingStub

    private val log = KotlinLogging.logger {}

    override fun createDefaultTimeoffEntitlementForContracts(
        inputs: List<CreationInput<Long>>,
    ): List<CreationResult> {
        if (inputs.isEmpty()) {
            log.info { "No contracts to create default timeoff entitlement for" }
            return emptyList()
        }

        try {
            blockingStub.setEntitlementsToDefaultRequirements(
                grpcContractIds {
                    this.ids.addAll(
                        inputs.map {
                            requireNotNull(it.contractId) {
                                "Contract ID for timeoff entitlement create must not be null"
                            }
                        })
                })
            return inputs.map { CreationResult(requestId = it.requestId, success = true) }
        } catch (e: Exception) {
            log.warn(e) { "Failed to create default timeoff entitlement" }
            return inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Create default timeoff entitlement failed due to an internal error: unknown exception occurred")
            }
        }
    }
}
