package com.multiplier.contract.onboarding.domain.model

import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import com.multiplier.contract.onboarding.service.convertToMap

data class OnboardingTask(
    val name: OnboardingTaskName,
    val completed: <PERSON><PERSON><PERSON>,
    val pendingOn: Person? = null,
)

fun OnboardingTask.convertToMapWithNameAsText() =
    this.convertToMap(
        overrides =
            mapOf("name" to this.name.text, "pendingOn" to (this.pendingOn?.fullName ?: "")))
