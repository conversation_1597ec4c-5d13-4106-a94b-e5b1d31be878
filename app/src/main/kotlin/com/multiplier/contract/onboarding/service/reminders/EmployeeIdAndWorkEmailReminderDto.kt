package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.service.convertToMap
import com.multiplier.contract.onboarding.types.NotificationType

data class EmployeeIdAndWorkEmailReminderDto(
    val notificationType: NotificationType,
    val subject: String,
    val companyUserEmails: List<String>,
    val companyLogoLink: String,
    val companyName: String,
    val companyCountry: String,
    val employeeFullNames: String,
    val contractOnboardingsLink: String,
) {
    fun toPropertyMap(): Map<String, Any> =
        convertToMap(
            mapOf(
                "companyLogoLink" to companyLogoLink,
                "companyName" to companyName,
                "companyCountry" to companyCountry,
                "viewProfileLink" to contractOnboardingsLink,
                "employeeFullName" to employeeFullNames,
            ))
}
