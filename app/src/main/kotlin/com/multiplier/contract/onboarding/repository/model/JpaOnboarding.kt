package com.multiplier.contract.onboarding.repository.model

import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.contants.ContractRevokedExperience
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import jakarta.persistence.*
import java.time.LocalDateTime
import org.hibernate.envers.Audited
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener

@Entity
@Audited
@Table(name = "onboarding", schema = "contract")
@EntityListeners(AuditingEntityListener::class)
@SequenceGenerator(
    name = "onboarding_seq",
    schema = "contract",
    sequenceName = "onboarding_id_seq",
    initialValue = 1,
    allocationSize = 1,
)
class JpaOnboarding(
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "onboarding_seq")
    var id: Long = 0,
    @Column(name = "contract_id") var contractId: Long,
    var experience: String,
    @Enumerated(EnumType.STRING) var status: ContractOnboardingStatus,
    @Enumerated(EnumType.STRING)
    var revokedBy: ContractRevokedExperience = ContractRevokedExperience.NONE,
    @Enumerated(EnumType.STRING) var currentStep: OnboardingStep? = null,
    @CreatedBy var createdBy: Long? = null,
    @CreatedDate var createdOn: LocalDateTime? = null,
    @LastModifiedBy var updatedBy: Long? = null,
    @LastModifiedDate var updatedOn: LocalDateTime? = null,
    var isBulkOnboarded: Boolean? = false,
)
