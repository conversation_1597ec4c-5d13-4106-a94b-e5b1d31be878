package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.service.bulk.creation.BulkCreationResult
import com.multiplier.contract.onboarding.service.bulk.mapper.ContractContext
import com.multiplier.contract.onboarding.service.bulk.mapper.MemberContext
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkContractModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberLegalDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkOrgManagementModule
import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationResults
import com.multiplier.contract.onboarding.service.bulkupload.ValidationIdsOfContract
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.member.schema.MemberCreateInput
import com.multiplier.member.schema.UpdateMemberLegalDataInput
import com.multiplier.member.schema.UpsertBankDetailsInput
import com.multiplier.orgmanagement.schema.BulkAPI

data class BulkValidationResultV2(
    val validationResults: Map<String, List<GrpcValidationResult<out Any>>>,
    val validationIdsOfContracts: List<ValidationIdsOfContract> = emptyList(),
) {
    companion object {
        val EMPTY = BulkValidationResultV2(emptyMap())
    }
}

data class CreationInput<T>(
    val requestId: String,
    val memberId: Long? = null,
    val contractId: Long? = null,
    val data: T
) {
    companion object {
        val refSelf: (CreationInput<*>) -> Any? = { it }
        val refMemberId: (CreationInput<*>) -> Any? = { it.memberId }
        val refContractId: (CreationInput<*>) -> Any? = { it.contractId }
    }
}

data class CreationResult(
    val requestId: String,
    val success: Boolean,
    val upsertedIds: List<Long> = emptyList(),
    val errors: List<String> = emptyList(),
    val inputMemberId: Long? = null,
    val inputContractId: Long? = null,
) {
    companion object {
        fun error(requestId: String, errors: List<String>) =
            CreationResult(requestId = requestId, success = false, errors = errors)

        fun error(requestId: String, error: String) =
            CreationResult(requestId = requestId, success = false, errors = listOf(error))
    }
}

data class CreationError(
    val requestId: String,
    val errors: MutableList<String> = mutableListOf(),
) {
    fun addErrors(errors: List<String>) {
        this.errors.addAll(errors)
    }
}

data class BulkCreationResultV2(
    private val requestIdToMemberId: Map<String, Long> = emptyMap(),
    private val requestIdToContractId: Map<String, Long> = emptyMap(),
    val validationIdsOfContracts: List<ValidationIdsOfContract> = emptyList(),
    val newContractIds: List<Long> = emptyList(),
) {
    companion object {
        val EMPTY = BulkCreationResultV2()
    }

    val errors: MutableList<CreationError> = mutableListOf()

    fun contractIds() = requestIdToContractId.values
    fun getMemberId(requestId: String): Long? =
        requestIdToMemberId[requestId]
            ?: requestIdToMemberId[findExistingRequestIdOfSameContractByRequestId(requestId)]
    fun getContractId(requestId: String): Long? =
        requestIdToContractId[requestId]
            ?: requestIdToContractId[findExistingRequestIdOfSameContractByRequestId(requestId)]
    fun getRequestIdForContract(contractId: Long): String? =
        requestIdToContractId.entries.find { it.value == contractId }?.key

    fun addErrorsFrom(creationResults: List<CreationResult>): BulkCreationResultV2 {
        creationResults
            .filter { it.errors.isNotEmpty() }
            .forEach {
                val existingError = errors.find { error -> error.requestId == it.requestId }
                if (existingError == null) {
                    errors.add(
                        CreationError(requestId = it.requestId, errors = it.errors.toMutableList()))
                } else {
                    existingError.addErrors(it.errors)
                }
            }

        return this
    }

    fun toV1(): BulkCreationResult {
        return BulkCreationResult(
            memberContext = MemberContext(requestIdToMemberId),
            contractContext = ContractContext(requestIdToContractId),
            errors = errors.flatMap { it.errors },
            validationIdsOfContracts = this.validationIdsOfContracts)
    }

    private fun findExistingRequestIdOfSameContractByRequestId(requestId: String): String? {
        val requestIdsOfSameContract =
            validationIdsOfContracts.find { it.ids.contains(requestId) }?.ids
        return requestIdsOfSameContract?.find { requestIdToContractId.containsKey(it) }
    }
}

fun BulkValidationResultV2.toV1(): EmployeeValidationResults {
    // limitation: support conversion from v2 to v1 for GLOBAL_PAYROLL context only
    return if (this == BulkValidationResultV2.EMPTY) EmployeeValidationResults.EMPTY
    else
        EmployeeValidationResults(
            members = validationResults.getMemberValidationResults(),
            contracts = validationResults.getContractValidationResults(),
            compensations = validationResults.getCompensationValidationResults(),
            newCompensations = validationResults.getNewCompensationValidationResults(),
            legalData = validationResults.getLegalDataValidationResults(),
            bankData = validationResults.getBankDataValidationResults(),
            orgManagementData = validationResults.getOrgManagementDataValidationResults(),
            validationIdsOfContracts = this.validationIdsOfContracts,
        )
}

private fun Map<String, List<GrpcValidationResult<out Any>>>.getMemberValidationResults():
    List<GrpcValidationResult<MemberCreateInput>> {
    return getValue(BulkMemberModule.MODULE_NAME).map {
        it as GrpcValidationResult<MemberCreateInput>
    }
}

private fun Map<String, List<GrpcValidationResult<out Any>>>.getContractValidationResults():
    List<GrpcValidationResult<ContractOuterClass.CreateContractInput>> {
    return getValue(BulkContractModule.MODULE_NAME).map {
        it as GrpcValidationResult<ContractOuterClass.CreateContractInput>
    }
}

private fun Map<String, List<GrpcValidationResult<out Any>>>.getCompensationValidationResults():
    List<GrpcValidationResult<CompensationOuterClass.CreateCompensationInput>> {
    return this[BulkCompensationModule.MODULE_NAME]
        ?.map { it as GrpcValidationResult<CompensationOuterClass.CreateCompensationInput> }
        .orEmpty()
}

private fun Map<String, List<GrpcValidationResult<out Any>>>.getNewCompensationValidationResults():
    List<GrpcValidationResult<Map<String, String>>> {
    return this[BulkCompensationModuleV2.MODULE_NAME]
        ?.map { it as GrpcValidationResult<Map<String, String>> }
        .orEmpty()
}

private fun Map<String, List<GrpcValidationResult<out Any>>>.getLegalDataValidationResults():
    List<GrpcValidationResult<UpdateMemberLegalDataInput>> {
    return this[BulkMemberLegalDataModule.MODULE_NAME]
        ?.map { it as GrpcValidationResult<UpdateMemberLegalDataInput> }
        .orEmpty()
}

private fun Map<String, List<GrpcValidationResult<out Any>>>.getBankDataValidationResults():
    List<GrpcValidationResult<UpsertBankDetailsInput>> {
    return this[BulkMemberBankDataModule.MODULE_NAME]
        ?.map { it as GrpcValidationResult<UpsertBankDetailsInput> }
        .orEmpty()
}

private fun Map<String, List<GrpcValidationResult<out Any>>>
    .getOrgManagementDataValidationResults():
    List<GrpcValidationResult<BulkAPI.UpsertOrgManagementDataInput>> {
    return this[BulkOrgManagementModule.MODULE_NAME]
        ?.map { it as GrpcValidationResult<BulkAPI.UpsertOrgManagementDataInput> }
        .orEmpty()
}

fun EmployeeValidationResults.toV2(): BulkValidationResultV2 {
    return BulkValidationResultV2(
        mapOf(
            BulkMemberModule.MODULE_NAME to members,
            BulkContractModule.MODULE_NAME to contracts,
            BulkCompensationModule.MODULE_NAME to compensations,
            BulkCompensationModuleV2.MODULE_NAME to newCompensations,
            BulkMemberLegalDataModule.MODULE_NAME to legalData,
            BulkMemberBankDataModule.MODULE_NAME to bankData,
            BulkOrgManagementModule.MODULE_NAME to orgManagementData,
        ),
        validationIdsOfContracts = this.validationIdsOfContracts)
}
