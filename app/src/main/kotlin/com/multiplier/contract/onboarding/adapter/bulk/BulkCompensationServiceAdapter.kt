package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationServiceGrpc
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.BulkContract.CreateCompensationValidationInput
import com.multiplier.contract.schema.contract.BulkContractServiceGrpc.BulkContractServiceBlockingStub
import com.multiplier.country.schema.Country
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkCompensationServiceAdapter {

    @GrpcClient("contract-service")
    private lateinit var blockingStub: CompensationServiceGrpc.CompensationServiceBlockingStub

    @GrpcClient("contract-service")
    private lateinit var blockingBulkApiStub: BulkContractServiceBlockingStub

    fun createCompensations(
        inputs: List<CreationInput<CompensationOuterClass.CreateCompensationInput>>,
        options: BulkOnboardingOptions
    ): List<CreationResult> {
        if (inputs.isEmpty()) return emptyList()

        try {
            val results = blockingStub.createCompensations(inputs.toGrpc(options)).resultsList
            return results.map {
                CreationResult(
                    requestId = it.requestId, success = it.success, errors = it.errorsList)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to create compensations for bulk onboarding" }
            return inputs.map {
                CreationResult.error(
                    it.requestId, "Upsert compensation failed due to unknown exception")
            }
        }
    }

    fun validateCompensationCreateInputs(
        inputs: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<CompensationOuterClass.CreateCompensationInput>> {
        val results =
            blockingBulkApiStub.validateCreateCompensationInputs(
                BulkContract.ValidateCreateCompensationInputsRequest.newBuilder()
                    .addAllInputs(inputs.map { it.toGrpc() })
                    .setContext(options.context.name)
                    .build())

        return results.resultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
            )
        }
    }
}

private fun EmployeeData.toGrpc(): CreateCompensationValidationInput =
    CreateCompensationValidationInput.newBuilder()
        .setRequestId(this.identification.validationId)
        .putAllProperties(this.data)
        .build()

private fun List<CreationInput<CompensationOuterClass.CreateCompensationInput>>.toGrpc(
    options: BulkOnboardingOptions
): CompensationOuterClass.CreateCompensationsRequest {

    return CompensationOuterClass.CreateCompensationsRequest.newBuilder()
        .addAllCompensationInputs(
            this.map {
                it.data
                    .toBuilder()
                    .setRequestId(it.requestId)
                    .setContractId(
                        requireNotNull(it.contractId) {
                            "Contract ID for compensation upsert must not be null"
                        })
                    .build()
            })
        .setContext(options.context.name)
        .apply {
            options.countryCode?.let { countryCode ->
                setCountryCode(Country.GrpcCountryCode.valueOf(countryCode.name))
            }
        }
        .build()
}
