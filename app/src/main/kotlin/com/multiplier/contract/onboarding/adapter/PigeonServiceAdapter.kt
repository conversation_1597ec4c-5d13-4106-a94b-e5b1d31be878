package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.pigeonservice.schema.grpc.EmailTemplateServiceGrpc.EmailTemplateServiceBlockingStub
import com.multiplier.pigeonservice.schema.grpc.GetEmailTemplateRequest
import com.multiplier.pigeonservice.schema.grpc.GetEmailTemplateResponse
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface PigeonServiceAdapter {
    fun getEmailTemplateDetails(
        notificationType: NotificationType,
        parameters: Map<String, String>
    ): GetEmailTemplateResponse
}

@Service
class DefaultPigeonServiceAdapter : PigeonServiceAdapter {

    @GrpcClient("pigeon-service")
    private lateinit var pigeonServiceStub: EmailTemplateServiceBlockingStub

    override fun getEmailTemplateDetails(
        notificationType: NotificationType,
        parameters: Map<String, String>
    ): GetEmailTemplateResponse {
        val request =
            GetEmailTemplateRequest.newBuilder()
                .setTemplateType(notificationType.name)
                .putAllParameters(parameters)
                .build()

        return pigeonServiceStub.getEmailTemplate(request)
    }
}
