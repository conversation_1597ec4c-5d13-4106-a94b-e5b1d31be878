package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.service.extensions.isFreelancerOrContractor
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.NotificationType
import org.springframework.stereotype.Component

@Component
class OpsMemberUpdateDetailsContractorReminderCtaProcessor : BaseReminderCtaTemplateProcessor() {

    private val MEMBER_ONBOARDING_IN_PROGRESS_STATUSES =
        setOf(
            ContractOnboardingStatus.MEMBER_INVITED,
            ContractOnboardingStatus.MEMBER_STARTED,
            ContractOnboardingStatus.MEMBER_DATA_ADDED)

    override fun notificationType() = NotificationType.OpsMemberUpdateDetailsContractorReminderCta

    override fun validate(preFetchedData: EmailTemplateData): Boolean {
        if (preFetchedData.operationsUser == null ||
            !preFetchedData.contract.isFreelancerOrContractor()) {
            return false
        }

        val onboardingCompany = preFetchedData.onboardingCompany
        val onboardingMember = preFetchedData.onboardingMember

        if (onboardingCompany.status in setOf(ContractOnboardingStatus.MEMBER_INVITED)) {
            return true
        }

        if (onboardingMember != null &&
            onboardingMember.status in MEMBER_ONBOARDING_IN_PROGRESS_STATUSES) {
            return true
        }
        return false
    }
}
