package com.multiplier.contract.onboarding.service.bulk.creation

import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.service.bulk.mapper.ContractContext
import com.multiplier.contract.schema.contract.ContractOuterClass

class BulkCreationHelper {

    companion object {

        private fun getExistingContractIds(
            upsertContractInputs: List<ValidInput<ContractOuterClass.CreateContractInput>>
        ) =
            upsertContractInputs
                .mapNotNull { if (it.input.hasContractId()) it.input.contractId else null }
                .toSet()

        fun getNewlyCreatedContractIds(
            contractContext: ContractContext,
            upsertContractInputs: List<ValidInput<ContractOuterClass.CreateContractInput>>
        ) = contractContext.contractIds.toList() - getExistingContractIds(upsertContractInputs)
    }
}
