package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.PigeonServiceAdapter
import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.repository.model.JpaOnboardingReminder
import com.multiplier.contract.onboarding.service.OnboardingReminderService
import com.multiplier.contract.onboarding.service.ReminderCtaTemplateProcessor
import com.multiplier.contract.onboarding.service.notifications.NotificationService
import com.multiplier.contract.onboarding.types.EmailTemplateDataResponse
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.onboarding.types.Reminder
import com.multiplier.contract.onboarding.types.TemplateProperties
import com.multiplier.pigeonservice.schema.grpc.GetEmailTemplateResponse
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class OnboardingCtaReminderService(
    processors: List<ReminderCtaTemplateProcessor>,
    private val onboardingReminderService: OnboardingReminderService,
    private val notificationService: NotificationService,
    private val pigeonServiceAdapter: PigeonServiceAdapter,
    private val emailTemplateDataService: EmailTemplateDataService,
    @Value("\${onboarding.cc-reminder-emails}") private val ccEmails: String
) {
    companion object {
        private const val EMAIL_DELIMITER = ","
    }

    private val log = KotlinLogging.logger {}

    private val notificationTemplateProcessors:
        Map<NotificationType, ReminderCtaTemplateProcessor> =
        processors.associateBy { it.notificationType() }

    /**
     * Fetches all valid CTA templates with their data for a given contract. Only returns templates
     * that pass validation for the contract's current state.
     *
     * @param contractId The contract ID to fetch templates for
     * @return List of valid email template data responses
     */
    fun fetchAllCtaTemplatesWithData(contractId: Long): List<EmailTemplateDataResponse> {
        val preFetchedData = emailTemplateDataService.fetchEmailTemplateData(contractId)

        return notificationTemplateProcessors
            .filter { (_, processor) -> processor.validate(preFetchedData) }
            .map { (notificationType, processor) ->
                buildEmailTemplateDataResponse(
                    contractId, notificationType, processor, preFetchedData)
            }
    }

    private fun buildEmailTemplateDataResponse(
        contractId: Long,
        notificationType: NotificationType,
        processor: ReminderCtaTemplateProcessor,
        preFetchedData: EmailTemplateData
    ): EmailTemplateDataResponse {
        // Find existing reminder first to get saved editable parameters
        val existingJpaReminder = findExistingReminder(contractId, notificationType)
        val savedEditableParams = existingJpaReminder?.editableTemplateParams

        // Fetch email data with saved editable parameters so template shows saved values
        val emailData =
            processor.fetchDataAndOverrideEditableParams(preFetchedData, savedEditableParams)
        val templateData = pigeonServiceAdapter.getEmailTemplateDetails(notificationType, emailData)

        // Get editable parameters for this processor and contract state
        val processorEditableParams = processor.editableParams(preFetchedData)
        val templateIsEditable =
            preFetchedData.multiFrequencySupportEnabled && processorEditableParams.isNotEmpty()

        return EmailTemplateDataResponse().apply {
            reminder = existingJpaReminder?.let { mapJpaOnboardingReminderToReminder(it) }
            subject = getSubjectForEmail(contractId, templateData.subject)
            bodyHtml = templateData.templateBody
            templateName = notificationType

            // Add templateProperties with isEditable and editableParams
            templateProperties =
                TemplateProperties().apply {
                    isEditable = templateIsEditable
                    editableParams =
                        if (templateIsEditable) {
                            processorEditableParams.map { param -> param.key }
                        } else {
                            emptyList()
                        }
                }
        }
    }

    private fun findExistingReminder(
        contractId: Long,
        notificationType: NotificationType
    ): JpaOnboardingReminder? {
        return onboardingReminderService
            .findByContractIdAndTemplateName(contractId, notificationType)
            .orElse(null)
    }

    /**
     * Sends a reminder email for a specific template to a contract member. Validates editable
     * parameters, transforms them, merges with email data, and saves to database.
     *
     * @param contractId The contract ID to send the reminder for
     * @param notificationType The type of notification template to use
     * @param editableTemplateParams Optional editable parameters to customize the template
     * @throws ErrorCodes.UNSUPPORTED_INPUT if template is invalid or parameters are invalid
     * @throws ErrorCodes.UNSUPPORTED_OPERATION if template validation fails
     * @throws ErrorCodes.NOTIFICATION_SENDING_FAILED if email sending fails
     */
    fun sendReminderForTemplate(
        contractId: Long,
        notificationType: NotificationType,
        editableTemplateParams: Map<String, String>? = null
    ) {
        val preFetchedData = emailTemplateDataService.fetchEmailTemplateData(contractId)
        val processor = getValidatedProcessor(notificationType, preFetchedData)

        val transformedEditableParams =
            processEditableParams(processor, preFetchedData, editableTemplateParams)
        val finalEmailData =
            buildFinalEmailData(processor, preFetchedData, transformedEditableParams)

        sendNotificationSafely(preFetchedData, finalEmailData, notificationType)
        saveReminderToDatabase(preFetchedData, notificationType, transformedEditableParams)
    }

    private fun getValidatedProcessor(
        notificationType: NotificationType,
        preFetchedData: EmailTemplateData
    ): ReminderCtaTemplateProcessor {
        val processor =
            notificationTemplateProcessors[notificationType]
                ?: throw ErrorCodes.UNSUPPORTED_INPUT.toBusinessException(
                    "Invalid template for reminder cta $notificationType")

        if (!processor.validate(preFetchedData)) {
            throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                "Validation failed for reminder template $notificationType")
        }

        return processor
    }

    private fun processEditableParams(
        processor: ReminderCtaTemplateProcessor,
        preFetchedData: EmailTemplateData,
        editableTemplateParams: Map<String, String>?
    ): Map<String, String>? {
        return editableTemplateParams?.let {
            validateEditableTemplateParams(processor, preFetchedData, it)
            transformEditableTemplateParams(processor, preFetchedData, it)
        }
    }

    private fun buildFinalEmailData(
        processor: ReminderCtaTemplateProcessor,
        preFetchedData: EmailTemplateData,
        transformedEditableParams: Map<String, String>?
    ): Map<String, String> {
        // fetchData() already handles merging editable parameters with template data
        return processor.fetchDataAndOverrideEditableParams(
            preFetchedData, transformedEditableParams)
    }

    private fun sendNotificationSafely(
        preFetchedData: EmailTemplateData,
        finalEmailData: Map<String, String>,
        notificationType: NotificationType
    ) {
        try {
            sendReminderToMemberForTemplate(preFetchedData, finalEmailData, notificationType)
        } catch (e: Exception) {
            log.error(e) { "Failed to send reminder: ${e.message}" }
            throw ErrorCodes.NOTIFICATION_SENDING_FAILED.toBusinessException(
                "Failed to send reminder: ${e.message}")
        }
    }

    private fun saveReminderToDatabase(
        preFetchedData: EmailTemplateData,
        notificationType: NotificationType,
        transformedEditableParams: Map<String, String>?
    ) {
        onboardingReminderService.createOrUpdateReminder(
            preFetchedData.contract, notificationType, transformedEditableParams)
    }

    private fun validateEditableTemplateParams(
        processor: ReminderCtaTemplateProcessor,
        preFetchedData: EmailTemplateData,
        editableTemplateParams: Map<String, String>
    ) {
        val editableParams = processor.editableParams(preFetchedData)

        validateRequiredAndProvidedParams(editableParams, editableTemplateParams, processor)
        validateNoUnexpectedParams(editableParams, editableTemplateParams, processor)
    }

    private fun validateRequiredAndProvidedParams(
        editableParams: List<com.multiplier.contract.onboarding.service.EditableParam>,
        editableTemplateParams: Map<String, String>,
        processor: ReminderCtaTemplateProcessor
    ) {
        editableParams.forEach { param ->
            val value = editableTemplateParams[param.key]
            if (!param.validate(value)) {
                val errorMessage = buildValidationErrorMessage(param, value, processor)
                throw ErrorCodes.UNSUPPORTED_INPUT.toBusinessException(errorMessage)
            }
        }
    }

    private fun validateNoUnexpectedParams(
        editableParams: List<com.multiplier.contract.onboarding.service.EditableParam>,
        editableTemplateParams: Map<String, String>,
        processor: ReminderCtaTemplateProcessor
    ) {
        val validParamKeys = editableParams.map { it.key }.toSet()
        editableTemplateParams.keys.forEach { key ->
            if (key !in validParamKeys) {
                throw ErrorCodes.UNSUPPORTED_INPUT.toBusinessException(
                    "Invalid editable parameter '$key' for template ${processor.notificationType()}")
            }
        }
    }

    private fun buildValidationErrorMessage(
        param: com.multiplier.contract.onboarding.service.EditableParam,
        value: String?,
        processor: ReminderCtaTemplateProcessor
    ): String {
        return if (value.isNullOrBlank() && param.required) {
            "Required parameter '${param.key}' is missing for template ${processor.notificationType()}"
        } else {
            "Invalid value '$value' for parameter '${param.key}' in template ${processor.notificationType()}"
        }
    }

    private fun sendReminderToMemberForTemplate(
        preFetchedData: EmailTemplateData,
        emailData: Map<String, String>,
        notificationType: NotificationType
    ) {
        validateOperationsUserExists(preFetchedData)

        val templateData = pigeonServiceAdapter.getEmailTemplateDetails(notificationType, emailData)
        val notification =
            buildNotification(preFetchedData, templateData, notificationType, emailData)

        log.info {
            "Sending reminder to member from ops user for notification type ${notificationType.name}: "
        }
        notificationService.send(notification)
    }

    private fun validateOperationsUserExists(preFetchedData: EmailTemplateData) {
        if (preFetchedData.operationsUser == null) {
            throw ErrorCodes.ONBOARDING_SPECIALIST_NOT_FOUND.toBusinessException(
                "Operations User is not set for contract ${preFetchedData.contract.id}")
        }
    }

    private fun buildNotification(
        preFetchedData: EmailTemplateData,
        templateData: GetEmailTemplateResponse,
        notificationType: NotificationType,
        emailData: Map<String, String>
    ): Notification {
        return Notification(
            subject = getSubjectForEmail(preFetchedData.contract.id, templateData.subject),
            from = preFetchedData.operationsUser!!.email,
            to = preFetchedData.member.email,
            cc = getCcEmailList(),
            type = notificationType,
            data = emailData)
    }

    private fun mapJpaOnboardingReminderToReminder(
        onboardingReminder: JpaOnboardingReminder
    ): Reminder {
        return Reminder(
            onboardingReminder.templateName.name,
            onboardingReminder.count,
            onboardingReminder.updatedOn,
            onboardingReminder.editableTemplateParams)
    }

    private fun getCcEmailList(): List<String> {
        return ccEmails.split(EMAIL_DELIMITER).map { it.trim() }.filter { it.isNotBlank() }
    }

    private fun getSubjectForEmail(contractId: Long, templateSubject: String): String {
        return "[$contractId] $templateSubject"
    }

    /**
     * Transforms editable template parameters using their specific transform functions. Each
     * parameter type can define its own transformation logic (e.g., MonthYearParam normalizes
     * case).
     *
     * @param processor The template processor containing the editable parameters
     * @param preFetchedData The pre-fetched email template data for context-aware parameter
     *   selection
     * @param editableTemplateParams The raw parameter values to transform
     * @return Map of transformed parameter values
     */
    private fun transformEditableTemplateParams(
        processor: ReminderCtaTemplateProcessor,
        preFetchedData: EmailTemplateData,
        editableTemplateParams: Map<String, String>
    ): Map<String, String> {
        val editableParams = processor.editableParams(preFetchedData)
        val paramLookup = editableParams.associateBy { it.key }

        val transformedParams =
            editableTemplateParams.mapValues { (key, value) ->
                paramLookup[key]?.transform(value) ?: value
            }

        log.info {
            "Transforming editable template params for ${processor.notificationType()}: " +
                "original=$editableTemplateParams, transformed=$transformedParams"
        }

        return transformedParams
    }
}
