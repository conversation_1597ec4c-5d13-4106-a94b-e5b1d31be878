package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.CountrySpec
import com.multiplier.contract.onboarding.service.bulk.v2.module.*
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractTerm
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.onboarding.usecase.ModuleParams
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class FreelancerBulkOnboardingUseCase(
    private val bulkContractModule: BulkContractModule,
    private val bulkMemberModule: BulkMemberModule,
    private val bulkContractOnboardingModule: BulkContractOnboardingModule,
    private val bulkCompensationModule: BulkCompensationModuleV2,
    private val countryCache: CountryCache
) : BulkOnboardingUseCase() {
    private val log = KotlinLogging.logger {}

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        fieldRequirementData: ModuleParams?
    ): List<DataSpec> {
        return super.getDataSpecs(onboardingOptions, fieldRequirementData).map {
            it.copy(group = EmployeeData.CONTRACTOR_DETAILS_GROUP)
        }
    }

    override fun getBulkOnboardingModules(): List<BulkDataModule> {
        return listOf(
            bulkContractModule,
            bulkMemberModule,
            bulkContractOnboardingModule,
            bulkCompensationModule)
    }

    override fun validate(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): BulkValidationResultV2 {
        val enrichedEmployeeData = enrichEmployeeData(employeeDataInput)
        val bulkValidationResults = super.validate(enrichedEmployeeData, options)
        return addGroupToValidationResults(bulkValidationResults)
    }

    private fun addGroupToValidationResults(bulkValidationResult: BulkValidationResultV2) =
        bulkValidationResult.copy(
            validationResults =
                bulkValidationResult.validationResults.mapValues {
                    it.value.map { validationResult ->
                        validationResult.copy(groupName = EmployeeData.CONTRACTOR_DETAILS_GROUP)
                    }
                })

    private fun enrichEmployeeData(employeeDataInput: EmployeeDataInput) =
        employeeDataInput.copy(
            employeeData =
                employeeDataInput.employeeData.map {
                    val countryName = it.data[CountrySpec.key]
                    val countryCode = countryName?.let { countryCache.getCountryCode(it) }?.name

                    it.copy(
                        data =
                            it.data
                                .minus("contractId") // Disables upsert capability
                                // these values need to be added since they are required in the
                                // validate endpoint
                                .plus("type" to ContractType.FREELANCER.name)
                                .plus("term" to ContractTerm.FIXED.name)
                                .plus(CountrySpec.key to (countryCode ?: countryName ?: "")))
                })
}
