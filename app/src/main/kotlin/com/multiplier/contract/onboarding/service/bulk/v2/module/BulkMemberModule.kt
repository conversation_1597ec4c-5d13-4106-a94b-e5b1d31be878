package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmailSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.FirstNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.GenderSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.toDataSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.toEmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.service.bulk.v2.logBulkCreationResults
import com.multiplier.contract.onboarding.service.filterNonNullValues
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.member.schema.MemberCreateInput
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkMemberModule(
    private val memberServiceAdapter: MemberServiceAdapter,
    private val bulkMemberServiceAdapter: BulkMemberServiceAdapter,
    private val countryCache: CountryCache,
    private val countryServiceAdapter: CountryServiceAdapter,
) : BulkDataModule {

    private val log = KotlinLogging.logger {}

    companion object {
        const val MODULE_NAME = "MEMBER_MODULE"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.AOR,
            BulkOnboardingContext.FREELANCER ->
                listOf(
                    FirstNameSpec.copy(description = "First name of the contractor"),
                    LastNameSpec.copy(description = "Last name of the contractor"),
                    EmailSpec.copy(description = "Email address of the contractor"),
                    GenderSpec.copy(description = "Gender of the contractor"))
            BulkOnboardingContext.EOR -> {
                val defaultSpecs = listOf(FirstNameSpec, LastNameSpec, EmailSpec, GenderSpec)
                // fetch member data and add here
                return try {
                    val adapterSpecs =
                        countryServiceAdapter
                            .getMemberLegalDataDefinitions(
                                onboardingOptions.countryCode, onboardingOptions.contractType)
                            .filter {
                                it.fetchStage == FetchStage.CONTRACT_GENERATION &&
                                    it.domainType == DomainType.MEMBER_DATA
                            }
                            .distinctBy { it.key }
                            .map { it.toDataSpec() }

                    defaultSpecs + adapterSpecs
                } catch (e: Exception) {
                    log.warn(e) {
                        "Failed to get member legal data spec for options: $onboardingOptions"
                    }
                    defaultSpecs
                }
            }
            else -> listOf(FirstNameSpec, LastNameSpec, EmailSpec, GenderSpec)
        }
    }

    override fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        contractIdToMemberId: Map<Long, Long>
    ): DataForContract {
        val members =
            memberServiceAdapter
                .getMembersWithAddressesAndBankAccountsAndLegalData(
                    contractIdToMemberId.values.toSet())
                .associateBy { it.id }

        val contractIdToMemberData =
            contractIdToMemberId
                .mapValues { members[it.value]?.toEmployeeDataChunk(dataSpecs) }
                .filterNonNullValues()

        return DataForContract(contractIdToMemberData)
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        return bulkMemberServiceAdapter.validateMemberCreateInputs(employeeData, options)
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {

        val upsertMemberInputs =
            validationResults.map {
                CreationInput(requestId = it.validationId, data = it.input as MemberCreateInput)
            }
        val results =
            bulkUpsert("Upsert member", upsertMemberInputs, options, CreationInput.refSelf) {
                inputs,
                onboardingOptions ->
                bulkMemberServiceAdapter.createMembers(inputs, onboardingOptions)
            }
        logBulkCreationResults(results, bulkCreationResult, "Upsert member")
        return bulkCreationResult
            .copy(
                requestIdToMemberId =
                    results
                        .filter { it.success }
                        .associateBy({ it.requestId }, { it.upsertedIds.first() }))
            .addErrorsFrom(results)
    }
}
