package com.multiplier.contract.onboarding.repository

import jakarta.persistence.*
import org.hibernate.envers.RevisionEntity
import org.hibernate.envers.RevisionNumber
import org.hibernate.envers.RevisionTimestamp

@RevisionEntity
@Entity
@SequenceGenerator(
    name = "hibernate_sequence",
    schema = "public",
    sequenceName = "hibernate_sequence",
    allocationSize = 1,
)
@Table(name = "revinfo", schema = "public")
class RevInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "hibernate_sequence")
    @RevisionNumber
    var rev: Long = 0,
    @RevisionTimestamp var revtstmp: Long = 0
)
