package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.country.CountryState
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StateOfEmploymentSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.Gender

fun interface BulkEnrichmentServiceInterface {
    fun enrichEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<EmployeeData>
}

fun EmployeeData.getGender(): String {
    return when (val genderName = data[BulkMemberDataService.GenderSpec.key].orEmpty()) {
        "OTHER" -> Gender.UNSPECIFIED.name
        else -> genderName
    }
}

fun EmployeeData.getCountryStateCode(countryStates: List<CountryState>): String {
    val stateName = data[StateOfEmploymentSpec.key].orEmpty()

    return countryStates.find { it.name == stateName }?.code ?: stateName
}
