package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.MemberIdSpec
import com.multiplier.contract.onboarding.service.bulk.v2.BulkEmployeeDataPresentation.Companion.addHelpText
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkContractModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkContractOnboardingModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberModule
import com.multiplier.contract.onboarding.service.bulkupload.ValidationResultHelper
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.PageRequest
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.onboarding.usecase.ModuleParams
import mu.KotlinLogging

/**
 * This v2 version of the BulkOnboardingUseCase provides a more structured way to define a bulk
 * onboarding use case, yet it is still flexible enough to allow for custom implementations. In this
 * version, the use case is split into modules that can be added to the use case. Each module is
 * responsible for a specific part of the onboarding process, such as contracts, members, or
 * compensation. The use case orchestrates the modules and provides a way to validate and create the
 * data.
 */
abstract class BulkOnboardingUseCase {
    private val log = KotlinLogging.logger {}

    abstract fun getBulkOnboardingModules(): List<BulkDataModule>

    open fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        return getBulkOnboardingModules()
            .flatMap { it.getDataSpecs(onboardingOptions, moduleParams) }
            .map { addHelpText(it) }
    }

    fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?
    ): List<EmployeeDataChunk> {
        val contractData =
            getBulkOnboardingModules()
                .asSequence()
                .map { it.getContractDataForSpecs(dataSpecs + MemberIdSpec, options, pageRequest) }
                .filterNot { it.isEmpty() }
                .firstOrNull()
                ?: return emptyList()

        val contractIdToMemberId =
            contractData.contractIdToContractData.mapValues {
                it.value.data.getValue(MemberIdSpec.key).toLong()
            }
        val moduleData =
            getBulkOnboardingModules().associate {
                it.identifier() to it.getDataForSpecs(dataSpecs, options, contractIdToMemberId)
            }

        return contractData.contractIds.map { contractId ->
            val data = mutableMapOf<String, String>()
            data.putAll(contractData.contractIdToContractData[contractId]?.data.orEmpty())
            moduleData.values
                .mapNotNull { it.contractIdToEmployeeDataChunk[contractId] }
                .forEach { data.putAll(it.data) }

            EmployeeDataChunk(data)
        }
    }

    open fun getFieldData(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?
    ): List<EmployeeDataChunk> = emptyList()

    open fun validate(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): BulkValidationResultV2 {
        if (employeeDataInput.employeeData.isEmpty()) {
            log.info { "Skip validation for empty employee data" }
            return BulkValidationResultV2.EMPTY
        }

        log.info { "Start validation for ${employeeDataInput.employeeData.size} employee data" }
        val validationResults = mutableMapOf<String, List<GrpcValidationResult<out Any>>>()
        val moduleParams = ModuleParams(employeeDataInput.customParams, options.entityId)
        // allow overriding module params.
        val overriddenModuleParams = overrideModuleParams(moduleParams, options)
        getBulkOnboardingModules().sortByValidationOrder().fold(employeeDataInput.employeeData) {
            employeeData,
            module ->
            val employeeDataForModule =
                filterEmployeeDataForModule(employeeData, module.identifier())
            val moduleValidationResults =
                if (employeeDataForModule.isEmpty()) {
                    log.info {
                        "Validation for module ${module.identifier()} failed due to empty employee data"
                    }
                    employeeData.map {
                        GrpcValidationResult(
                            success = false,
                            errors =
                                listOf(
                                    "System error: No data found validating ${module.identifier()}"),
                            validationId = it.identification.validationId,
                            input = null,
                        )
                    }
                } else {
                    module.validate(employeeDataForModule, options, overriddenModuleParams)
                }

            validationResults[module.identifier()] = moduleValidationResults
            enrichEmployeeData(employeeData, moduleValidationResults)
        }
        log.info { "Data validation completed" }

        return BulkValidationResultV2(
            validationResults,
            ValidationResultHelper.associateValidationInputsByContract(
                employeeDataInput.employeeData))
    }

    open fun filterEmployeeDataForModule(
        employeeData: List<EmployeeData>,
        moduleIdentifier: String
    ) = employeeData

    open fun overrideModuleParams(moduleParams: ModuleParams?, options: BulkOnboardingOptions) =
        moduleParams

    open fun create(
        bulkValidationResult: BulkValidationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        log.info { "Start creation for ${bulkValidationResult.validationResults.size} modules" }
        return getBulkOnboardingModules()
            .sortByCreationOrder()
            .fold(
                BulkCreationResultV2(
                    validationIdsOfContracts = bulkValidationResult.validationIdsOfContracts)) {
                    bulkCreationResult,
                    module ->
                    module.create(
                        bulkValidationResult.validationResults[module.identifier()].orEmpty(),
                        bulkCreationResult,
                        options)
                }
            .also { log.info { "Data creation completed" } }
    }

    companion object {

        private fun enrichEmployeeData(
            employeeData: List<EmployeeData>,
            moduleValidationResults: List<GrpcValidationResult<out Any>>
        ): List<EmployeeData> {
            val additionalInputPropertiesByRequestId =
                moduleValidationResults
                    .filter { it.additionalInputProperties.isNotEmpty() }
                    .associateBy({ it.validationId }, { it.additionalInputProperties })

            return if (additionalInputPropertiesByRequestId.isNotEmpty()) {
                employeeData.map {
                    if (additionalInputPropertiesByRequestId.containsKey(
                        it.identification.validationId)) {
                        it.copy(
                            data =
                                it.data +
                                    additionalInputPropertiesByRequestId.getValue(
                                        it.identification.validationId))
                    } else {
                        it
                    }
                }
            } else employeeData
        }
    }
}

fun List<BulkDataModule>.sortByValidationOrder(): List<BulkDataModule> {
    return sortedBy {
        when (it.identifier()) {
            BulkContractModule.MODULE_NAME -> 1
            BulkMemberModule.MODULE_NAME -> 2
            else -> 3
        }
    }
}

fun List<BulkDataModule>.sortByCreationOrder(): List<BulkDataModule> {
    return sortedBy {
        when (it.identifier()) {
            BulkMemberModule.MODULE_NAME -> 1
            BulkContractModule.MODULE_NAME -> 2
            BulkContractOnboardingModule.MODULE_NAME -> 3
            else -> 3
        }
    }
}
