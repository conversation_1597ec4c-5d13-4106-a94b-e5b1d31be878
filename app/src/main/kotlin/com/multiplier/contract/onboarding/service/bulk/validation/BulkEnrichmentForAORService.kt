package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractTerm
import com.multiplier.contract.onboarding.types.ContractType
import org.springframework.stereotype.Service

@Service
class BulkEnrichmentForAORService : BulkEnrichmentServiceInterface {

    override fun enrichEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<EmployeeData> {
        return employeeData.map { enrichEmployeeData(it, options) }
    }

    private fun enrichEmployeeData(
        employeeData: EmployeeData,
        options: BulkOnboardingOptions
    ): EmployeeData {
        val extraData =
            mapOf(
                "type" to ContractType.CONTRACTOR.name,
                "term" to ContractTerm.FIXED.name,
                "country" to options.countryCode.name,
                "companyId" to options.companyId.toString(),
                "gender" to employeeData.getGender())

        return employeeData.copy(data = employeeData.data + extraData)
    }
}
