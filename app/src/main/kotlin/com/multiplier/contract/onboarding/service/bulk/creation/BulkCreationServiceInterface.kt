package com.multiplier.contract.onboarding.service.bulk.creation

import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationResults
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions

interface BulkCreationServiceInterface {

    fun create(
        validationResults: EmployeeValidationResults,
        options: BulkOnboardingOptions
    ): BulkCreationResult
}
