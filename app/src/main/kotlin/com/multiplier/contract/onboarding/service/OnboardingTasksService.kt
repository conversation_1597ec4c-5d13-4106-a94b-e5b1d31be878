package com.multiplier.contract.onboarding.service

import com.multiplier.company.schema.grpc.CompanyOuterClass.CompanyUser
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import com.multiplier.contract.onboarding.domain.model.Member
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.domain.model.OnboardingTask
import com.multiplier.contract.onboarding.domain.model.Person
import com.multiplier.contract.onboarding.repository.JpaOnboardingRepository
import com.multiplier.contract.onboarding.service.extensions.isFreelancer
import com.multiplier.contract.onboarding.service.extensions.isHRMember
import com.multiplier.contract.onboarding.service.extensions.isVisaFlow
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.contract.schema.contract.ContractOuterClass.DirectEmployeeOnboardingFlowType
import com.multiplier.country.schema.Country
import java.util.concurrent.CompletableFuture
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service

@Service
class OnboardingTasksService(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
    private val companyServiceAdapter: CompanyServiceAdapter,
    private val countryServiceAdapter: CountryServiceAdapter,
    private val jpaOnboardingRepository: JpaOnboardingRepository,
    private val onboardingServiceAdapter: OnboardingServiceAdapter,
    private val onboardingAudServiceAdapter: OnboardingAudServiceAdapter,
) {
    private val log = LoggerFactory.getLogger(this.javaClass)

    companion object {
        private const val COMPANY_EXP = "company"
        private const val MEMBER_EXP = "member"
    }

    fun getOnboardingTasksByOnboardingId(
        onboardingIds: Set<Long>
    ): Map<Long, List<OnboardingTask>> {
        val onboardings = jpaOnboardingRepository.findAllById(onboardingIds)
        val contracts = fetchContracts(onboardings.map { it.contractId }.toSet())

        if (contracts.isEmpty()) return emptyMap()

        val contractIds = contracts.keys
        val companyIds = contracts.values.map { it.companyId }.toSet()
        val memberIds = contracts.values.map { it.memberId }.toSet()

        val companyExpOnboardingByContractId =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(contractIds, COMPANY_EXP)
        val memberExpOnboardingByContractId =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(contractIds, MEMBER_EXP)
        val contractsHaveBeenSignedByEmployee =
            onboardingAudServiceAdapter.getAllContractIdsOfOnboardingAuditsWithExperienceAndStatus(
                contractIds, COMPANY_EXP, ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SIGNED)

        val cfGetDepositPaidContractIds =
            CompletableFuture.supplyAsync {
                contractServiceAdapter.getDepositPaidContractIds(contractIds)
            }
        val cfGetMembers =
            CompletableFuture.supplyAsync {
                memberServiceAdapter.getMembers(memberIds).associateBy { it.id }
            }
        val cfGetMSASignedCompanyIds =
            CompletableFuture.supplyAsync {
                companyServiceAdapter.getMSASignedCompanyIds(companyIds)
            }
        val cfGetCompanyUsers =
            CompletableFuture.supplyAsync {
                companyServiceAdapter
                    .getCompanyUsersBy(GetCompanyUsersFilter(companyIds = companyIds))
                    .groupBy { it.companyId }
            }

        val cfGetLegalDocumentsKeyByMemberIds =
            CompletableFuture.supplyAsync {
                memberServiceAdapter.getLegalDocumentsKeyByMemberIds(memberIds)
            }
        val cfGetRequiredPayrollDocumentsGroupByCountryCode =
            CompletableFuture.supplyAsync {
                countryServiceAdapter.getRequiredDocumentKeysGroupByCountryCode(
                    contracts.values.map { it.countryAndState }.toSet(),
                    Country.GrpcLegalDocumentRequirement.GrpcLegalDocumentCategory.PAYROLL)
            }
        val cfGetFreelancerContractIdsWithCustomTemplate =
            CompletableFuture.supplyAsync {
                contractServiceAdapter
                    .getComplianceByContractIds(
                        contracts.values.filter { it.isFreelancer() }.map { it.id }.toSet())
                    .filter { it.agreementType.isCustomTemplate() }
                    .map { it.contractId }
                    .toSet()
            }
        val cfGetOnboardingFlowTypeByContractId =
            CompletableFuture.supplyAsync {
                contractServiceAdapter.getDirectEmployeeOnboardingFlowTypes(contracts.keys)
            }

        CompletableFuture.allOf(
                cfGetDepositPaidContractIds,
                cfGetMembers,
                cfGetMSASignedCompanyIds,
                cfGetCompanyUsers,
                cfGetLegalDocumentsKeyByMemberIds,
                cfGetRequiredPayrollDocumentsGroupByCountryCode,
                cfGetFreelancerContractIdsWithCustomTemplate,
                cfGetOnboardingFlowTypeByContractId)
            .get()

        val members = cfGetMembers.join()
        val companyUsersByCompanyId = cfGetCompanyUsers.join()
        val legalDocumentsByMemberId = cfGetLegalDocumentsKeyByMemberIds.join()
        val requiredPayrollDocumentKeysByCountry =
            cfGetRequiredPayrollDocumentsGroupByCountryCode.join()
        val msaSignedCompanyIds = cfGetMSASignedCompanyIds.join()
        val depositPaidContractIds = cfGetDepositPaidContractIds.join()
        val freelancerContractIdsWithCustomTemplate =
            cfGetFreelancerContractIdsWithCustomTemplate.join()
        val onboardingFlowTypeByContractId = cfGetOnboardingFlowTypeByContractId.join()

        return onboardings
            .filter { contracts.containsKey(it.contractId) }
            .associate {
                it.id to
                    getOnboardingTaskDTOs(
                        requireNotNull(contracts[it.contractId]) {
                            "contract with id ${it.contractId} not found"
                        },
                        members,
                        companyExpOnboardingByContractId,
                        memberExpOnboardingByContractId,
                        companyUsersByCompanyId,
                        legalDocumentsByMemberId,
                        requiredPayrollDocumentKeysByCountry,
                        msaSignedCompanyIds,
                        depositPaidContractIds,
                        contractsHaveBeenSignedByEmployee,
                        freelancerContractIdsWithCustomTemplate,
                        onboardingFlowTypeByContractId)
            }
    }

    private fun getOnboardingTaskDTOs(
        contract: Contract,
        members: Map<Long, Member>,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>,
        legalDocumentsByMemberId: Map<Long, Set<String>>,
        requiredPayrollDocumentKeysByCountry: Map<CountryAndState, Set<String>>,
        msaSignedCompanyIds: Set<Long>,
        depositPaidContractIds: Set<Long>,
        contractsHaveBeenSignedByEmployee: Set<Long>,
        freelancerContractIdsWithCustomTemplate: Set<Long>,
        onboardingFlowTypeByContractId: Map<Long, DirectEmployeeOnboardingFlowType>,
    ) =
        companyTasks(
            contract,
            companyExpOnboardingByContractId,
            memberExpOnboardingByContractId,
            companyUsersByCompanyId,
            msaSignedCompanyIds,
            depositPaidContractIds,
            freelancerContractIdsWithCustomTemplate) +
            memberTasks(
                contract,
                members,
                companyExpOnboardingByContractId,
                memberExpOnboardingByContractId,
                legalDocumentsByMemberId,
                requiredPayrollDocumentKeysByCountry,
                contractsHaveBeenSignedByEmployee,
                freelancerContractIdsWithCustomTemplate,
                onboardingFlowTypeByContractId)

    private fun companyTasks(
        contract: Contract,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>,
        msaSignedCompanyIds: Set<Long>,
        depositPaidContractIds: Set<Long>,
        freelancerContractIdsWithCustomTemplate: Set<Long>
    ): List<OnboardingTask> =
        listOfNotNull(
            createCompanySignMsaTask(contract, msaSignedCompanyIds, companyUsersByCompanyId),
            createCompanyCompleteOnboardingTask(
                contract, companyExpOnboardingByContractId, companyUsersByCompanyId),
            createCompanySendContractTask(
                contract,
                companyExpOnboardingByContractId,
                memberExpOnboardingByContractId,
                companyUsersByCompanyId,
                freelancerContractIdsWithCustomTemplate),
            createCompanySignContractTask(
                contract,
                companyExpOnboardingByContractId,
                memberExpOnboardingByContractId,
                companyUsersByCompanyId,
                freelancerContractIdsWithCustomTemplate),
            createCompanyPayDeposit(
                contract,
                companyExpOnboardingByContractId,
                memberExpOnboardingByContractId,
                depositPaidContractIds,
                companyUsersByCompanyId),
            createCompanyAddEmployeeIdAndWorkEmailTask(contract, companyUsersByCompanyId))

    private fun memberTasks(
        contract: Contract,
        members: Map<Long, Member>,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        legalDocumentsByMemberId: Map<Long, Set<String>>,
        requiredPayrollDocumentKeysByCountry: Map<CountryAndState, Set<String>>,
        contractsHaveBeenSignedByEmployee: Set<Long>,
        freelancerContractIdsWithCustomTemplate: Set<Long>,
        onboardingFlowTypeByContractId: Map<Long, DirectEmployeeOnboardingFlowType>
    ): List<OnboardingTask> =
        if (hasNoMemberOnboardingFlow(onboardingFlowTypeByContractId.getValue(contract.id)))
            emptyList()
        else
            listOfNotNull(
                createMemberSignContractTask(
                    contract,
                    companyExpOnboardingByContractId,
                    contractsHaveBeenSignedByEmployee,
                    memberExpOnboardingByContractId,
                    members,
                    freelancerContractIdsWithCustomTemplate),
                createMemberPlatformOnboardingTask(
                    contract,
                    companyExpOnboardingByContractId,
                    memberExpOnboardingByContractId,
                    members),
                createMemberKycDetailsTask(
                    contract,
                    companyExpOnboardingByContractId,
                    memberExpOnboardingByContractId,
                    members),
                createMemberPayrollAndComplianceTask(
                    contract,
                    companyExpOnboardingByContractId,
                    memberExpOnboardingByContractId,
                    legalDocumentsByMemberId,
                    requiredPayrollDocumentKeysByCountry,
                    members))

    private fun hasNoMemberOnboardingFlow(
        onboardingFlowType: DirectEmployeeOnboardingFlowType
    ): Boolean {
        return onboardingFlowType == DirectEmployeeOnboardingFlowType.GP_ONLY ||
            onboardingFlowType == DirectEmployeeOnboardingFlowType.HRIS_ONLY
    }

    private fun createCompanySignMsaTask(
        contract: Contract,
        msaSignedCompanyIds: Set<Long>,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>
    ): OnboardingTask? {
        if (contract.isFreelancer()) return null
        if (msaSignedCompanyIds.contains(contract.companyId)) return null
        return OnboardingTask(
            name = OnboardingTaskName.SIGN_MSA,
            completed = false,
            pendingOn = getContractCreator(contract, companyUsersByCompanyId))
    }

    private fun createCompanyCompleteOnboardingTask(
        contract: Contract,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>
    ): OnboardingTask {
        return OnboardingTask(
            name = OnboardingTaskName.COMPANY_COMPLETE_ONBOARDING,
            completed =
                companyExpOnboardingByContractId[contract.id]?.status != OnboardingStatus.DRAFT,
            pendingOn = getContractCreator(contract, companyUsersByCompanyId))
    }

    private fun createCompanySendContractTask(
        contract: Contract,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>,
        freelancerContractIdsWithCustomTemplate: Set<Long>
    ): OnboardingTask? {
        if (contract.isOfflineContract() ||
            freelancerContractIdsWithCustomTemplate.contains(contract.id))
            return null
        return OnboardingTask(
            name = OnboardingTaskName.SEND_CONTRACT,
            completed =
                isOnboardingPassedThrough(
                    contract,
                    COMPANY_EXP,
                    ContractOnboardingStatus.REVOKED,
                    companyExpOnboardingByContractId,
                    memberExpOnboardingByContractId),
            pendingOn = getContractCreator(contract, companyUsersByCompanyId))
    }

    private fun createCompanySignContractTask(
        contract: Contract,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>,
        freelancerContractIdsWithCustomTemplate: Set<Long>
    ): OnboardingTask? {
        if (contract.isOfflineContract() ||
            freelancerContractIdsWithCustomTemplate.contains(contract.id))
            return null
        return OnboardingTask(
            name = OnboardingTaskName.SIGN_CONTRACT,
            completed =
                isOnboardingPassedThrough(
                    contract,
                    COMPANY_EXP,
                    ContractOnboardingStatus.SIGNATURE_EMPLOYER_SENT,
                    companyExpOnboardingByContractId,
                    memberExpOnboardingByContractId),
            pendingOn = getSignatoryUser(contract.companyId, companyUsersByCompanyId))
    }

    private fun createCompanyPayDeposit(
        contract: Contract,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        depositPaidContractIds: Set<Long>,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>
    ): OnboardingTask? {
        if (contract.isFreelancer() || contract.isHRMember()) return null
        if (contract.isTest) return null

        return OnboardingTask(
            name = OnboardingTaskName.PAY_DEPOSIT,
            completed =
                isOnboardingPassedThrough(
                    contract,
                    COMPANY_EXP,
                    ContractOnboardingStatus.CREATED,
                    companyExpOnboardingByContractId,
                    memberExpOnboardingByContractId) &&
                    isDepositPaid(contract, depositPaidContractIds),
            pendingOn = getBillingContact(contract.companyId, companyUsersByCompanyId))
    }

    private fun createCompanyAddEmployeeIdAndWorkEmailTask(
        contract: Contract,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>
    ): OnboardingTask? {
        if (contract.type == ContractOuterClass.ContractType.FREELANCER ||
            contract.type == ContractOuterClass.ContractType.CONTRACTOR) {
            return null
        }

        return OnboardingTask(
            name = OnboardingTaskName.EMPLOYEE_ID_AND_WORK_EMAIL,
            completed =
                StringUtils.isNotEmpty(contract.employeeId) &&
                    StringUtils.isNotEmpty(contract.workEmail),
            pendingOn = getContractCreator(contract, companyUsersByCompanyId))
    }

    private fun createMemberSignContractTask(
        contract: Contract,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        contractsHaveBeenSignedByEmployee: Set<Long>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        members: Map<Long, Member>,
        freelancerContractIdsWithCustomTemplate: Set<Long>,
    ): OnboardingTask? {
        if (contract.isOfflineContract() ||
            freelancerContractIdsWithCustomTemplate.contains(contract.id))
            return null
        return OnboardingTask(
            name = OnboardingTaskName.SIGN_CONTRACT,
            completed =
                isOnboardingPassedThrough(
                    contract,
                    COMPANY_EXP,
                    ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT,
                    companyExpOnboardingByContractId,
                    memberExpOnboardingByContractId) ||
                    hadBeenSignedAndNotReadyToSignAgain(
                        contract,
                        contractsHaveBeenSignedByEmployee,
                        companyExpOnboardingByContractId),
            pendingOn = getContractMember(contract, members))
    }

    private fun createMemberPlatformOnboardingTask(
        contract: Contract,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        members: Map<Long, Member>
    ): OnboardingTask {
        return OnboardingTask(
            name = OnboardingTaskName.PLATFORM_ONBOARDING,
            completed =
                isOnboardingPassedThrough(
                    contract,
                    MEMBER_EXP,
                    ContractOnboardingStatus.MEMBER_INVITED,
                    companyExpOnboardingByContractId,
                    memberExpOnboardingByContractId),
            pendingOn = getContractMember(contract, members))
    }

    private fun createMemberKycDetailsTask(
        contract: Contract,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        members: Map<Long, Member>
    ): OnboardingTask {
        return OnboardingTask(
            name = OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS,
            completed =
                isOnboardingPassedThrough(
                    contract,
                    MEMBER_EXP,
                    ContractOnboardingStatus.MEMBER_DATA_ADDED,
                    companyExpOnboardingByContractId,
                    memberExpOnboardingByContractId),
            pendingOn = getContractMember(contract, members))
    }

    private fun createMemberPayrollAndComplianceTask(
        contract: Contract,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
        legalDocumentsByMemberId: Map<Long, Set<String>>,
        requiredPayrollDocumentKeysByCountry: Map<CountryAndState, Set<String>>,
        members: Map<Long, Member>,
    ): OnboardingTask? {
        if (contract.isFreelancer()) return null
        return if (isPayrollFormsRequired(contract, requiredPayrollDocumentKeysByCountry)) {
            OnboardingTask(
                name = OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS,
                completed =
                    isOnboardingPassedThrough(
                        contract,
                        MEMBER_EXP,
                        ContractOnboardingStatus.MEMBER_DATA_ADDED,
                        companyExpOnboardingByContractId,
                        memberExpOnboardingByContractId) &&
                        isPayrollFormsSubmitted(
                            contract,
                            legalDocumentsByMemberId,
                            requiredPayrollDocumentKeysByCountry),
                pendingOn = getContractMember(contract, members))
        } else null
    }

    private fun getSignatoryUser(
        companyId: Long,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>
    ): Person? {
        if (!companyUsersByCompanyId.containsKey(companyId)) {
            log.warn("Unable to find signatory for company id = {}", companyId)
            return null
        }

        return getCompanyUsers(companyId, companyUsersByCompanyId)
            .firstOrNull { it.isSignatory }
            ?.run(this::mapCompanyUserToPersonDTO)
            ?: Person(firstName = "COMPANY_SIGNATORY")
    }

    private fun getCompanyUsers(
        companyId: Long,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>
    ) =
        requireNotNull(companyUsersByCompanyId[companyId]) {
            "company user for company ${companyId} not found"
        }

    private fun getBillingContact(
        companyId: Long,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>
    ): Person? {
        if (!companyUsersByCompanyId.containsKey(companyId)) {
            log.warn("Unable to find billing contact for company id = {}", companyId)
            return null
        }

        if (getCompanyUsers(companyId, companyUsersByCompanyId).count { it.isBillingContact } > 1)
            return Person(firstName = "BILLING_ADMIN")

        return getCompanyUsers(companyId, companyUsersByCompanyId)
            .firstOrNull { it.isBillingContact }
            ?.run(this::mapCompanyUserToPersonDTO)
    }

    private fun getContractCreator(
        contract: Contract,
        companyUsersByCompanyId: Map<Long, List<CompanyUser>>
    ): Person? {
        return companyUsersByCompanyId[contract.companyId]
            ?.firstOrNull { it.userId == contract.createdBy.toString() }
            ?.run(this::mapCompanyUserToPersonDTO)
    }

    private fun getContractMember(contract: Contract, members: Map<Long, Member>): Person? {
        return members[contract.memberId]?.run(this::mapMemberToPersonDTO)
    }

    private fun isOnboardingPassedThrough(
        contract: Contract,
        experience: String,
        status: ContractOnboardingStatus,
        companyExpOnboardingByContractId: Map<Long, Onboarding>,
        memberExpOnboardingByContractId: Map<Long, Onboarding>,
    ): Boolean {
        val onboarding: Onboarding? =
            (if (experience == COMPANY_EXP) companyExpOnboardingByContractId[contract.id]
            else memberExpOnboardingByContractId[contract.id])

        return onboarding?.status != null && onboarding.status.ordinal > status.ordinal
    }

    private fun hadBeenSignedAndNotReadyToSignAgain(
        contract: Contract,
        contractsHaveBeenSignedByEmployee: Set<Long>,
        companyExpOnboardingByContractId: Map<Long, Onboarding>
    ): Boolean {
        return contractsHaveBeenSignedByEmployee.contains(contract.id) &&
            requireNotNull(companyExpOnboardingByContractId[contract.id]) {
                    "company exp onboarding entity for contract ${contract.id} not found"
                }
                .status !== OnboardingStatus.SIGNATURE_EMPLOYEE_SENT
    }

    private fun isDepositPaid(contract: Contract, depositPaidContractIds: Set<Long>): Boolean {
        return depositPaidContractIds.contains(contract.id)
    }

    private fun isPayrollFormsRequired(
        contract: Contract,
        requiredPayrollDocumentKeysByCountry: Map<CountryAndState, Set<String>>
    ): Boolean {
        return !requiredPayrollDocumentKeysByCountry[contract.countryAndState].isNullOrEmpty()
    }

    private fun isPayrollFormsSubmitted(
        contract: Contract,
        legalDocumentsByMemberId: Map<Long, Set<String>>,
        requiredPayrollDocumentKeysByCountry: Map<CountryAndState, Set<String>>
    ): Boolean {
        if (!legalDocumentsByMemberId.containsKey(contract.memberId)) return false

        val requiredPayrollDocuments =
            requireNotNull(requiredPayrollDocumentKeysByCountry[contract.countryAndState]) {
                "payroll document keys for country ${contract.countryAndState} not found"
            }
        return requireNotNull(legalDocumentsByMemberId[contract.memberId]) {
                "legal documents for member ${contract.memberId} not found"
            }
            .containsAll(requiredPayrollDocuments)
    }

    private fun fetchContracts(contractIds: Set<Long>): Map<Long, Contract> {
        return contractServiceAdapter.getNonDeletedNonEndedContracts(contractIds).associateBy {
            it.id
        }
    }

    private fun mapMemberToPersonDTO(member: Member): Person {
        return Person(
            id = member.id,
            persona = Person.Persona.valueOf(member.persona.name),
            firstName = member.firstName,
            lastName = member.lastName,
            userId = member.userId)
    }

    private fun mapCompanyUserToPersonDTO(companyUser: CompanyUser): Person {
        return Person(
            id = companyUser.id,
            persona = Person.Persona.valueOf(companyUser.persona.name),
            firstName = companyUser.firstName,
            lastName = companyUser.lastName,
            userId = companyUser.userId)
    }

    private fun Contract.isOfflineContract() =
        this.isVisaFlow() ||
            this.isHRMember() ||
            !isOnboardingEnabled(this.country, this.countryStateCode)

    private fun isOnboardingEnabled(countryCode: String, stateCode: String? = null) =
        countryServiceAdapter.isOnboardingEnabled(
            Country.CountryCodeState.newBuilder()
                .setCountryCode(Country.GrpcCountryCode.valueOf(countryCode))
                .setStateCode(stateCode)
                .build())
}
