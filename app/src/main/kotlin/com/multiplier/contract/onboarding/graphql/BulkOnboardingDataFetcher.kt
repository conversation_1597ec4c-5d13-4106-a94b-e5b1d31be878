package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.DocumentReadable
import com.multiplier.contract.onboarding.types.PageRequest
import com.multiplier.contract.onboarding.usecase.BulkOnboardingServiceFactory
import com.multiplier.transaction.graphql.graphApi
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsQuery
import com.netflix.graphql.dgs.InputArgument
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class BulkOnboardingDataFetcher(
    private val bulkOnboardingServiceFactory: BulkOnboardingServiceFactory
) {

    @DgsQuery
    @PreAuthorize(
        """
        ((@me.allowed('download.company.contract.onboarding-template') && @me.isCompanyUserForCompanyId(#options.companyId))
        || @me.allowed('download.operations.contract.onboarding-template'))
        """)
    fun bulkOnboardingTemplateDownload(
        @InputArgument options: BulkOnboardingOptions,
        @InputArgument pageRequest: PageRequest?,
    ): DocumentReadable = graphApi {
        bulkOnboardingServiceFactory
            .getBulkOnboardingService(options.context)
            .generateBulkOnboardingTemplate(options.applyDefaultContextIfMissing(), pageRequest)
    }
}

fun BulkOnboardingOptions.applyDefaultContextIfMissing(): BulkOnboardingOptions {
    if (context == null) this.context = BulkOnboardingContext.GLOBAL_PAYROLL

    return this
}
