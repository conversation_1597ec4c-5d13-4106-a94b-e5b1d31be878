package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.BulkUpsertEducationDetailsInput
import com.multiplier.member.schema.BulkValidateRequest
import com.multiplier.member.schema.UpsertEducationDetailsInput
import com.multiplier.member.schema.ValidationInput
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkMemberEducationServiceAdapter {

    @GrpcClient("member-service")
    private lateinit var bulkMemberService: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    fun validateUpsertEducationDetails(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): List<GrpcValidationResult<UpsertEducationDetailsInput>> {
        val request =
            BulkValidateRequest.newBuilder()
                .setContext(options.context.name)
                .addAllInputs(employeeData.map { it.toGrpc() })
                .build()

        return bulkMemberService.validateUpsertEducationDetails(request).validationResultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
            )
        }
    }

    fun upsertEducationDetails(
        inputs: List<CreationInput<UpsertEducationDetailsInput>>
    ): List<CreationResult> {
        if (inputs.isEmpty()) return emptyList()

        val request =
            BulkUpsertEducationDetailsInput.newBuilder()
                .addAllInputs(
                    inputs.map {
                        it.data
                            .toBuilder()
                            .setRequestId(it.requestId)
                            .setEducationDetails(
                                it.data.educationDetails
                                    .toBuilder()
                                    .setMemberId(
                                        requireNotNull(it.memberId) {
                                            "Member ID for education details upsert must not be null"
                                        })
                                    .build())
                            .build()
                    })
                .build()
        try {
            val response = bulkMemberService.bulkUpsertEducationDetails(request)
            return response.resultsList.map {
                CreationResult(
                    requestId = it.requestId, success = it.success, errors = it.errorsList)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to bulk upsert education details data" }
            return inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Upsert education details failed due to an internal error: unknown exception occurred")
            }
        }
    }

    private fun EmployeeData.toGrpc() =
        ValidationInput.newBuilder()
            .setRequestId(this.identification.validationId)
            .putAllProperties(this.data)
            .build()
}
