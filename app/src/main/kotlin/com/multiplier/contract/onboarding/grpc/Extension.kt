package com.multiplier.contract.onboarding.grpc

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.readValue
import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeIdentification
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.schema.BulkOnboardDataSpec
import com.multiplier.contract.onboarding.schema.BulkOnboardInput
import com.multiplier.contract.onboarding.schema.BulkOnboardOption
import com.multiplier.contract.onboarding.service.bulkupload.*
import com.multiplier.contract.onboarding.service.bulkupload.RowData
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.PageRequest
import com.multiplier.grpc.common.bulkupload.v1.*
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirement
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldType
import com.multiplier.grpc.common.contract.v2.Contract.ContractType
import com.multiplier.grpc.common.country.v2.Country.CountryCode
import com.multiplier.grpc.common.v1.PaginationRequest
import mu.KotlinLogging

private val log = KotlinLogging.logger {}

fun UpsertBulkRequest.toDomain() =
    BulkUpsertRequest(
        jobId = this.jobId, useCase = this.useCase, inputs = this.inputsList.map { it.toDomain() })

fun UpsertRequest.toDomain() =
    UpsertInput(id = this.inputId, data = this.dataMap, group = this.group)

fun UpsertResult.toGrpc() =
    UpsertResponse.newBuilder()
        .setInputId(this.inputId)
        .setSuccess(this.success)
        .putAllUpsertedData(this.upsertedData)
        .addAllErrors(this.errors)
        .setGroup(this.group.orEmpty())
        .build()

fun ValidationResult.toGrpc() =
    ValidateUpsertInputResponse.newBuilder()
        .addAllInputIds(this.inputIds)
        .setSuccess(this.success)
        .putAllValidatedInputData(this.validatedData)
        .addAllMessages(
            this.errors.map {
                ValidationMessage.newBuilder()
                    .setKey(it.key)
                    .setValue(it.value)
                    .addAllErrors(it.errors)
                    .build()
            })
        .setGroup(this.group.orEmpty())
        .build()

fun ValidateUpsertInputBulkRequest.toDomain() =
    BulkValidationRequest(
        useCase = this.useCase,
        inputs = this.inputsList.map { it.toDomain() },
        jsonCustomParams =
            if (!this.jsonCustomParams.isNullOrBlank()) this.jsonCustomParams.parseJsonToMap()
            else emptyMap())

private fun String.parseJsonToMap(): Map<String, String> {
    try {
        val objectMapper = ObjectMapper().registerModule(KotlinModule.Builder().build())
        return objectMapper.readValue<Map<String, String>>(this)
    } catch (e: Exception) {
        log.error(e) { "unable to parse key value json to map: $this" }
        throw ErrorCodes.UNSUPPORTED_INPUT.toBusinessException("unable to parse key value")
    }
}

fun ValidateUpsertInputRequest.toDomain() =
    ValidationInput(
        id = this.inputId,
        companyId = this.keys.companyId,
        countryCode = this.keys.countryCode,
        entityId = if (this.keys.hasEntityId()) this.keys.entityId else null,
        contractId = if (this.keys.hasContractId()) this.keys.contractId else null,
        data = this.dataMap,
        group = this.group)

fun com.multiplier.contract.onboarding.service.bulkupload.FieldRequirement.toGrpc() =
    FieldRequirement.newBuilder()
        .setKey(this.key)
        .setLabel(this.label)
        .setType(FieldType.valueOf("FIELD_TYPE_${this.type.name}"))
        .setDescription(this.description.orEmpty())
        .setMandatory(this.mandatory)
        .addAllAllowedValues(this.allowedValues)
        .setDefaultValue(this.defaultValue.orEmpty())
        .setGroup(this.group.orEmpty())
        .build()

fun FieldRequirementsRequest.toDomain() =
    com.multiplier.contract.onboarding.service.bulkupload.FieldRequirementsRequest(
        useCase = this.useCase,
        entityId = if (this.hasEntityId()) this.entityId else null,
        companyId = if (this.hasCompanyId()) this.companyId else null,
        countryCode = if (this.hasCountryCode()) this.countryCode else null,
        jsonCustomParams =
            if (!this.jsonCustomParams.isNullOrBlank()) this.jsonCustomParams.parseJsonToMap()
            else emptyMap())

fun BulkOnboardInput.toDomain(): EmployeeData =
    EmployeeData(
        identification =
            EmployeeIdentification(
                employeeId = this.propertiesMap["employeeId"],
                contractId = this.propertiesMap["contractId"]?.toLongOrNull(),
                firstName = this.propertiesMap["firstName"],
                lastName = this.propertiesMap["lastName"],
                email = this.propertiesMap["email"],
                rowNumber = this.requestId),
        data = this.propertiesMap,
        group = this.group)

fun BulkOnboardOption.toDomain() =
    BulkOnboardingOptions(
        this.countryCode?.toDomain(),
        this.contractType.toDomain(),
        this.companyId,
        this.entityId,
        BulkOnboardingContext.valueOf(this.context))

fun CountryCode.toDomain(): com.multiplier.contract.onboarding.types.CountryCode? =
    try {
        com.multiplier.contract.onboarding.types.CountryCode.valueOf(
            this.name.replace("COUNTRY_CODE_", ""))
    } catch (_: IllegalArgumentException) {
        null
    }

fun ContractType.toDomain(): com.multiplier.contract.onboarding.types.ContractType =
    com.multiplier.contract.onboarding.types.ContractType.valueOf(
        this.name.replace("CONTRACT_TYPE_", ""))

fun DataSpec.toGrpc() =
    BulkOnboardDataSpec.newBuilder()
        .setKey(this.keyWithPrefix())
        .setLabel(this.label)
        .setRequired(this.mandatory)
        .addAllValues(this.allowedValues)
        .setGroup(this.group.orEmpty())
        .build()

fun BulkDataRequest.toDomain() =
    FieldDataRequest(
        useCase = this.useCase,
        entityIds = this.entityIdsList,
        companyIds = this.companyIdsList,
        countryCodes = this.countryCodesList,
        pageRequest = if (this.hasPaginationRequest()) this.paginationRequest.toDomain() else null)

fun RowData.toGrpcRowData(): com.multiplier.grpc.common.bulkupload.v1.RowData {
    val builder =
        com.multiplier.grpc.common.bulkupload.v1.RowData.newBuilder()
            .putAllData(this.data)
            .setKeys(this.key)
    this.group?.let { builder.setGroup(it) }
    return builder.build()
}

fun PaginationRequest.toDomain(): PageRequest =
    PageRequest.newBuilder().pageSize(pageSize).pageNumber(pageNumber).build()
