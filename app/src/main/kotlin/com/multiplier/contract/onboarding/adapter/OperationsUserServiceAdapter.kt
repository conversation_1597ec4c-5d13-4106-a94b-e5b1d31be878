package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.domain.model.OperationsUser
import com.multiplier.core.schema.multiplier.OperationsUserOuterClass
import com.multiplier.core.schema.multiplier.OperationsUserOuterClass.ContractIds
import com.multiplier.core.schema.multiplier.OperationsUserServiceGrpc
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

@Service
class OperationsUserServiceAdapter {

    @GrpcClient("core-service")
    private lateinit var blockingStub: OperationsUserServiceGrpc.OperationsUserServiceBlockingStub

    fun getOperationsUsersByUserId(userId: Long): OperationsUser? =
        blockingStub
            .getOperationsUsersByUserIds(
                OperationsUserOuterClass.GetOperationsUsersByUserIdsRequest.newBuilder()
                    .addUserIds(userId)
                    .build())
            .usersList
            .firstOrNull()
            ?.toDomain()

    fun getSalesUsersForCompanies(companyIds: List<Long>): Map<Long, OperationsUser> =
        blockingStub
            .getSalesUsersForCompanies(
                OperationsUserOuterClass.CompanyIds.newBuilder()
                    .addAllCompanyIds(companyIds)
                    .build())
            .companyIdToOperationsUserMap
            .map { it.key to it.value.toDomain() }
            .toMap()

    fun getCsmUsersForCompanies(companyIds: List<Long>): Map<Long, OperationsUser> =
        blockingStub
            .getCsmUsersForCompanies(
                OperationsUserOuterClass.CompanyIds.newBuilder()
                    .addAllCompanyIds(companyIds)
                    .build())
            .companyIdToOperationsUserMap
            .map { it.key to it.value.toDomain() }
            .toMap()

    fun getOperationsUsersForContracts(contractIds: List<Long>): Map<Long, OperationsUser> =
        blockingStub
            .getOperationsUsersForContracts(
                ContractIds.newBuilder().addAllContractIds(contractIds).build())
            .contractIdToOperationsUserMap
            .map { it.key to it.value.toDomain() }
            .toMap()
}

private fun OperationsUserOuterClass.OperationsUser.toDomain() =
    OperationsUser(
        id = this.id,
        email =
            this.emailsList
                .firstOrNull { listOf("PRIMARY", "DEFAULT").contains(it.type.uppercase()) }
                ?.email
                ?: "",
        firstName = this.firstName,
        lastName = this.lastName,
    )
