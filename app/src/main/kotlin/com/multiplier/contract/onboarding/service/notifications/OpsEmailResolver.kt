package com.multiplier.contract.onboarding.service.notifications

import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class OpsEmailResolver(
    @Value("\${ops.sender-email}") private val senderEmail: String,
    @Value("\${ops.onboarding-specialist-email-test}")
    private val onboardingSpecialistEmailTest: String,
) {
    fun getSenderEmail(): String {
        return senderEmail
    }

    fun getOnboardingSpecialistTestEmail(): String {
        return onboardingSpecialistEmailTest
    }
}
