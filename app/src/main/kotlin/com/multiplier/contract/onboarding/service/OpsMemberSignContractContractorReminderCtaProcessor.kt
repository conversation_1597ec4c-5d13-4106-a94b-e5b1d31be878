package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.service.extensions.isFreelancerOrContractor
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.NotificationType
import org.springframework.stereotype.Component

@Component
class OpsMemberSignContractContractorReminderCtaProcessor : BaseReminderCtaTemplateProcessor() {
    override fun notificationType() = NotificationType.OpsMemberSignContractContractorReminderCta

    override fun validate(preFetchedData: EmailTemplateData): Bo<PERSON><PERSON> {
        if (preFetchedData.operationsUser == null ||
            !preFetchedData.contract.isFreelancerOrContractor()) {
            return false
        }

        val onboardingCompany = preFetchedData.onboardingCompany

        return onboardingCompany.status == ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT &&
            onboardingCompany.currentStep == OnboardingStep.ONBOARDING_SIGNING
    }
}
