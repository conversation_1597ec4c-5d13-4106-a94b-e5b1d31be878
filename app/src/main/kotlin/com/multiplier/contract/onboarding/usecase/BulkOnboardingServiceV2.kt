package com.multiplier.contract.onboarding.usecase

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingJob
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingNotificationService
import com.multiplier.contract.onboarding.service.bulk.creation.BulkCreationResult
import com.multiplier.contract.onboarding.service.bulk.excel.BulkUploadExcel
import com.multiplier.contract.onboarding.service.bulk.v2.*
import com.multiplier.contract.onboarding.service.bulk.validation.*
import com.multiplier.contract.onboarding.types.*
import java.io.InputStream
import java.time.LocalDateTime
import java.util.*
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.stereotype.Service

@Service
class BulkOnboardingServiceV2(
    globalPayrollBulkOnboardingUseCase: GlobalPayrollBulkOnboardingUseCase,
    private val globalPayrollBulkOnboardingUseCaseV2: GlobalPayrollBulkOnboardingUseCaseV2,
    private val bulkValidationHelper: BulkValidationHelper,
    private val notificationService: BulkOnboardingNotificationService
) : BulkOnboardingServiceInterface {

    private val log = KotlinLogging.logger {}

    companion object {
        private const val GLOBAL_PAYROLL = "GLOBAL_PAYROLL"
        private const val GLOBAL_PAYROLL_V2 = "GLOBAL_PAYROLL_V2"
    }

    private val useCaseMap =
        mapOf(
            "GLOBAL_PAYROLL" to globalPayrollBulkOnboardingUseCase,
            "GLOBAL_PAYROLL_V2" to globalPayrollBulkOnboardingUseCaseV2,
        )

    private fun getUseCase(options: BulkOnboardingOptions): BulkOnboardingUseCase {
        if (options.context != BulkOnboardingContext.GLOBAL_PAYROLL) {
            throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                "Context ${options.context} not supported")
        }

        return if (globalPayrollBulkOnboardingUseCaseV2.isNewCompensationServiceIntegrationEnabled(
            options.entityId))
            useCaseMap.getValue(GLOBAL_PAYROLL_V2)
        else useCaseMap.getValue(GLOBAL_PAYROLL)
    }

    override fun generateBulkOnboardingTemplate(
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?
    ): DocumentReadable {
        logOnboardingContext(options)

        val dataSpecs = getDataSpec(options)
        log.info {
            "Generation bulk onboarding template with ${dataSpecs.size} data specs + pagination: $pageRequest"
        }
        val dataChunks = getUseCase(options).getDataForSpecs(dataSpecs, options, pageRequest)
        val formattedDataChunks = BulkEmployeeDataPresentation.formatData(dataChunks)

        return BulkUploadExcel.generate(options, dataSpecs, formattedDataChunks)
    }

    override fun getDataSpec(options: BulkOnboardingOptions): List<DataSpec> =
        getUseCase(options).getDataSpecs(options, buildModuleParams(options))

    override fun validate(
        employeeDataFile: InputStream,
        options: BulkOnboardingOptions,
        source: String,
    ): BulkValidationReport {
        val employeeDataInput = parseEmployeeDataFile(employeeDataFile).copy(source = source)
        return validate(employeeDataInput, options).validationReport
    }

    override fun validate(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): ValidationResults {
        logOnboardingContext(options)

        val unformattedEmployeeDataInput =
            employeeDataInput.copy(
                employeeData =
                    BulkEmployeeDataPresentation.unformatData(employeeDataInput.employeeData))
        val genericValidationResult =
            bulkValidationHelper.genericValidate(unformattedEmployeeDataInput.employeeData, options)
        val employeeValidationResults =
            getUseCase(options).validate(unformattedEmployeeDataInput, options).toV1()
        val aggregatedEmployeeValidationResults =
            aggregate(employeeValidationResults, unformattedEmployeeDataInput.employeeData)
        if (aggregatedEmployeeValidationResults.errors.isNotEmpty()) {
            log.warn { "Validation errors: ${aggregatedEmployeeValidationResults.errors}" }
        }

        return ValidationResults(
            employeeValidationResults = employeeValidationResults,
            validationReport =
                createValidationReport(
                    genericValidationResult,
                    aggregatedEmployeeValidationResults,
                    unformattedEmployeeDataInput.templateData?.reportTemplate))
    }

    override fun onboard(
        employeeDataFile: InputStream,
        options: BulkOnboardingOptions
    ): BulkOnboardingJob {
        val employeeDataInput = parseEmployeeDataFile(employeeDataFile)
        return onboard(employeeDataInput, options)
    }

    override fun onboard(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): BulkOnboardingJob {
        logOnboardingContext(options)

        val jobId = UUID.randomUUID().toString()
        withLoggingContext(
            "bulk-onboarding-job-id" to jobId,
            "bulk-onboarding-service-version" to "v2",
        ) {
            val startTime = LocalDateTime.now()
            log.info("Bulk onboarding job [$jobId] started")

            val validationResults = validate(employeeDataInput, options)
            val bulkCreationResult = create(validationResults, options)
            val onboardingJob =
                BulkOnboardingJob.complete(
                    jobId, startTime, validationResults.validationReport, bulkCreationResult)
            log.info {
                "Bulk onboarding job [${jobId}] completed with status: ${onboardingJob.status}," +
                    " duration: ${onboardingJob.durationInSeconds} seconds," +
                    " upserted contracts: ${onboardingJob.upsertedContractIds}"
            }

            notificationService.sendOnboardingCompletedNotification(
                options, onboardingJob, employeeDataInput.source)

            return onboardingJob
        }
    }

    private fun buildModuleParams(
        bulkOnboardingOptions: BulkOnboardingOptions,
    ): ModuleParams {
        // sending bulkOnboardingOptions.entityId as this is specific to GP incase called for non GP
        // anyway getUseCase(options) will fail
        return ModuleParams(workPlaceEntityId = bulkOnboardingOptions.entityId)
    }

    private fun create(
        validationResults: ValidationResults,
        options: BulkOnboardingOptions
    ): BulkCreationResult? {
        if (validationResults.validationReport.hasErrors()) {
            return null
        }

        log.info {
            "Creating employee data, size = ${validationResults.employeeValidationResults.members.size}"
        }
        val result =
            getUseCase(options)
                .create(validationResults.employeeValidationResults.toV2(), options)
                .toV1()
        log.info { "Creation completed" }

        return result
    }

    private fun logOnboardingContext(options: BulkOnboardingOptions) {
        log.info {
            "Onboarding context: countryCode=${options.countryCode}, contractType=${options.contractType}, companyId=${options.companyId}, entityId=${options.entityId}, context=${options.context}"
        }
    }
}
