package com.multiplier.contract.onboarding.service.bulk.creation

import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEducationDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEmergencyDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEmploymentDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberLegalDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOnboardingDataService
import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationResults
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkCreationForHRISService(
    private val bulkMemberDataService: BulkMemberDataService,
    private val bulkContractDataService: BulkContractDataService,
    private val bulkOnboardingDataService: BulkOnboardingDataService,
    private val bulkMemberLegalDataService: BulkMemberLegalDataService,
    private val bulkComplianceDataService: BulkComplianceDataService,
    private val orgManagementDataService: BulkOrgManagementDataService,
    private val bulkMemberEmergencyDataService: BulkMemberEmergencyDataService,
    private val bulkMemberEducationDataService: BulkMemberEducationDataService,
    private val bulkMemberEmploymentDataService: BulkMemberEmploymentDataService,
    private val bulkMemberAddressDataService: BulkMemberAddressDataService,
    private val bulkTimeoffDataService: BulkTimeoffDataService,
    private val bulkPostOnboardingService: BulkPostOnboardingService
) : BulkCreationServiceInterface {
    private val log = KotlinLogging.logger {}

    override fun create(
        validationResults: EmployeeValidationResults,
        options: BulkOnboardingOptions
    ): BulkCreationResult {
        val memberContext =
            bulkMemberDataService.createMembers(
                inputs = validationResults.members.mapNotNull { it.toValidInputOrNull() },
                options = options)

        val upsertContractInputs =
            validationResults.contracts.mapNotNull { it.toValidInputOrNull() }
        val contractContext =
            bulkContractDataService.createContracts(
                inputs = upsertContractInputs, memberContext = memberContext, options = options)

        val newlyCreatedContractIds =
            BulkCreationHelper.getNewlyCreatedContractIds(contractContext, upsertContractInputs)
        val onboardingErrors =
            bulkOnboardingDataService.createOnboardingEntities(
                newlyCreatedContractIds, options = options)

        val legalDataErrors =
            bulkMemberLegalDataService.createLegalData(
                inputs = validationResults.legalData.mapNotNull { it.toValidInputOrNull() },
                memberContext = memberContext,
                options = options)

        val complianceErrors =
            bulkComplianceDataService.updateContractCompliance(
                inputs = validationResults.compliances.mapNotNull { it.toValidInputOrNull() },
                options = options,
                contractContext = contractContext)
        val upsertOrgManagementDataErrors =
            orgManagementDataService.upsertOrgManagementData(
                inputs = validationResults.orgManagementData.mapNotNull { it.toValidInputOrNull() },
                contractContext = contractContext,
                options = options)

        val emergencyContactErrors =
            bulkMemberEmergencyDataService.upsertEmergencyContacts(
                inputs = validationResults.emergencyData.mapNotNull { it.toValidInputOrNull() },
                memberContext = memberContext,
                options = options)

        val educationDetailErrors =
            bulkMemberEducationDataService.upsertEducationDetails(
                inputs = validationResults.educationData.mapNotNull { it.toValidInputOrNull() },
                memberContext = memberContext,
                options = options)

        val previousEmployerDetailErrors =
            bulkMemberEmploymentDataService.upsertPreviousEmployerDetails(
                inputs =
                    validationResults.previousEmployerData.mapNotNull { it.toValidInputOrNull() },
                memberContext = memberContext,
                options = options)

        val addressErrors =
            bulkMemberAddressDataService.upsertAddresses(
                inputs = validationResults.addressData.mapNotNull { it.toValidInputOrNull() },
                memberContext = memberContext,
                options = options)

        val timeoffErrors =
            bulkTimeoffDataService.createDefaultTimeoffEntitlementForContracts(
                contractContext = contractContext, options = options)

        val creationErrors =
            onboardingErrors +
                legalDataErrors +
                complianceErrors +
                upsertOrgManagementDataErrors +
                emergencyContactErrors +
                educationDetailErrors +
                previousEmployerDetailErrors +
                addressErrors +
                timeoffErrors
        val postOnboardingErrors =
            if (creationErrors.isEmpty()) {
                bulkPostOnboardingService.triggerPostOnboardingActions(
                    newlyCreatedContractIds, options = options)
            } else {
                log.warn { "Post onboarding actions are skipped due to bulk creation errors" }
                emptyList()
            }

        log.info { "Bulk employee data creation completed" }
        return BulkCreationResult(
            memberContext = memberContext,
            contractContext = contractContext,
            errors = creationErrors + postOnboardingErrors)
    }
}
