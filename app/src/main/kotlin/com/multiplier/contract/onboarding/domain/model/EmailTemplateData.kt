package com.multiplier.contract.onboarding.domain.model

import com.multiplier.contract.onboarding.adapter.CountryAndState
import com.multiplier.contract.onboarding.repository.model.JpaOnboarding
import com.multiplier.contract.schema.contract.ContractOuterClass

data class EmailTemplateData(
    val company: Company,
    val contract: ContractOuterClass.Contract,
    val onboardingCompany: JpaOnboarding,
    val onboardingMember: JpaOnboarding?,
    val member: Member,
    val operationsUser: OperationsUser?,
    val submittedLegalDocument: Set<String>,
    val payrollFormRequirements: Map<CountryAndState, Set<String>>,
    val guideLink: String,
    val preregistrationRequiredCountry: Boolean,
    val payFrequency: String? = null,
    val multiFrequencySupportEnabled: Boolean = false,
)

val EmailTemplateData.memberName: String
    get() = member.fullName

val EmailTemplateData.clientName: String
    get() = company.displayName

val EmailTemplateData.onboardingSpecialistFullName: String
    get() = operationsUser?.let { "${it.firstName} ${it.lastName}" } ?: ""
