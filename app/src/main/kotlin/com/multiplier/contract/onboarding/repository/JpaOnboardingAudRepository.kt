package com.multiplier.contract.onboarding.repository

import com.multiplier.contract.onboarding.repository.model.JpaOnboardingAud
import com.multiplier.contract.onboarding.repository.model.JpaOnboardingAudId
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository

@Repository
interface JpaOnboardingAudRepository : CrudRepository<JpaOnboardingAud, JpaOnboardingAudId> {
    @Query(
        nativeQuery = true,
        value =
            "SELECT DISTINCT contract_id FROM contract.onboarding_aud " +
                "WHERE status = :#{#status.name()} AND experience = :experience AND contract_id IN (:contractIds) ")
    fun findAllContractIdByContractIdInAndExperienceAndStatus(
        contractIds: Set<Long>,
        experience: String,
        status: ContractOnboardingStatus,
    ): Set<Long>
}
