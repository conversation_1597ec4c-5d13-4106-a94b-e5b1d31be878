package com.multiplier.contract.onboarding.service.reminders

import com.google.common.annotations.VisibleForTesting
import com.multiplier.contract.onboarding.service.notifications.NotificationService
import java.text.MessageFormat
import java.time.LocalDate
import java.time.YearMonth
import java.util.*
import mu.KotlinLogging
import mu.withLoggingContext
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class OnboardingReminders(
    private val payrollCutoffDateReminderContractFinder: PayrollCutoffDateReminderContractFinder,
    private val startDateReminderContractFinder: StartDateReminderContractFinder,
    private val dtoComposer: OnboardingNotificationDtoComposer,
    private val notificationService: NotificationService,
    @Value("\${ops.support-email}") private val systemNotificationEmail: String,
) {

    fun sendOnboardingRemindersBeforePayrollCutoffDate(
        simulatedSendingDate: LocalDate?,
        companyIds: List<Long> = emptyList()
    ) {
        val sendingDate = Optional.ofNullable(simulatedSendingDate).orElse(LocalDate.now())
        val payrollCycleContractsList =
            payrollCutoffDateReminderContractFinder.findBy(sendingDate, companyIds.toSet())
        if (payrollCycleContractsList.isEmpty()) {
            log.info { "No contracts found for payroll cutoff date reminder" }
            return
        }
        log.info { "Contracts found for payroll cutoff date reminder: $payrollCycleContractsList" }
        val mergedPayrollContractsList =
            mergePayrollCyclesIfCutoffDateAndPayrollMonthAreSame(payrollCycleContractsList)
        dtoComposer.init(mergedPayrollContractsList)
        for (payrollCycleContracts in mergedPayrollContractsList) {
            sendOnboardingReminders(payrollCycleContracts, sendingDate)
        }
    }

    fun sendOnboardingRemindersBeforeContractStartDate(
        simulatedSendingDate: LocalDate?,
        companyIds: List<Long> = emptyList()
    ) {
        val sendingDate = Optional.ofNullable(simulatedSendingDate).orElse(LocalDate.now())
        val payrollCycleContractsList =
            startDateReminderContractFinder.findBy(sendingDate, companyIds.toSet()).map {
                it.toContractsForCutoffDate()
            }
        if (payrollCycleContractsList.isEmpty()) {
            log.info { "No contracts found for start date reminder" }
            return
        }
        log.info { "Contracts found for start date reminder: $payrollCycleContractsList" }
        dtoComposer.init(payrollCycleContractsList)
        for (payrollCycleContracts in payrollCycleContractsList) {
            sendOnboardingReminders(payrollCycleContracts, sendingDate)
        }
    }

    private fun sendOnboardingReminders(
        payrollCycleContracts: ContractsForCutoffDate,
        sendingDate: LocalDate
    ) {
        try {
            sendOnboardingRemindersToCompanies(payrollCycleContracts, sendingDate)
        } catch (ex: Exception) {
            log.error("Failed to send payroll cutoff onboarding reminder to companies", ex)
        }
        try {
            sendOnboardingRemindersToMembers(payrollCycleContracts, sendingDate)
        } catch (ex: Exception) {
            log.error("Failed to send payroll cutoff onboarding reminder to members", ex)
        }
    }

    private fun sendOnboardingRemindersToCompanies(
        payrollCycleContracts: ContractsForCutoffDate,
        sendingDate: LocalDate
    ) {
        val companyIds =
            payrollCycleContracts.contractIds
                .mapNotNull { id -> dtoComposer.contracts[id] }
                .map { it.companyId }
                .toSet()

        for (companyId in companyIds) {
            try {
                val dto =
                    dtoComposer.composeCompanyEmailDto(
                        payrollCycleContracts, companyId, sendingDate)
                if (dto == null || dto.onboardingCount == 0) {
                    continue
                }
                withLoggingContext(
                    "notificationType" to dto.notificationType.name,
                    "companyId" to companyId.toString(),
                    "contractId" to payrollCycleContracts.contractIds.joinToString(),
                    "cutoffDate" to payrollCycleContracts.cutoffDate.toString(),
                ) {
                    log.info { "Sending onboarding notification to company" }
                }
                sendReminderToCompany(dto)
            } catch (ex: Exception) {
                log.warn("Fail to send onboarding notification to company id = {}", companyId, ex)
            }
        }
    }

    private fun sendReminderToCompany(dto: CompanyOnboardingNotificationEmailDto) {
        val notification =
            Notification(
                subject = dto.subject,
                from = MessageFormat.format("Multiplier <{0}>", systemNotificationEmail),
                to = dto.companyUserEmails.joinToString(";"),
                cc =
                    (listOf(dto.saleUserEmail, dto.csmUserEmail) + dto.onboardingOpsUserEmails)
                        .filter { it.isNotBlank() },
                type = dto.notificationType,
                data = dto.toPropertyMap(),
            )

        notificationService.send(notification)
        addDelay(5)
    }

    private fun sendOnboardingRemindersToMembers(
        payrollCycleContracts: ContractsForCutoffDate,
        sendingDate: LocalDate
    ) {
        for (contractId in payrollCycleContracts.contractIds) {
            try {
                val dto =
                    dtoComposer.composeMemberEmailDto(
                        payrollCycleContracts, contractId, sendingDate)
                if (!dto.memberInvitedToSignContract || dto.pendingStepCount == 0) {
                    continue
                }
                withLoggingContext(
                    "notificationType" to dto.notificationType.name,
                    "contractId" to contractId.toString(),
                    "cutoffDate" to payrollCycleContracts.cutoffDate.toString(),
                ) {
                    log.info { "Sending onboarding notification to member" }
                }
                sendReminderToMember(dto)
            } catch (ex: Exception) {
                log.warn(
                    "Fail to send onboarding notification to member of contract id = {}",
                    contractId,
                    ex)
            }
        }
    }

    private fun sendReminderToMember(dto: MemberOnboardingNotificationEmailDto) {
        val notification =
            Notification(
                subject = dto.subject,
                from = MessageFormat.format("Multiplier <{0}>", systemNotificationEmail),
                to = dto.memberEmail,
                cc =
                    (listOf(dto.saleUserEmail, dto.csmUserEmail) + dto.onboardingOpsUserEmail)
                        .filter { it.isNotBlank() },
                type = dto.notificationType,
                data = dto.toPropertyMap(),
            )

        notificationService.send(notification)
        addDelay(5)
    }

    data class ContractsForCutoffDate(
        val cutoffDate: LocalDate,
        val payrollMonth: YearMonth,
        val contractIds: List<Long> = emptyList(),
    )

    private fun PayrollCycleContracts.toContractsForCutoffDate() =
        ContractsForCutoffDate(
            cutoffDate = this.cutoffDate,
            payrollMonth = this.payrollMonth,
            contractIds = this.contractIds,
        )

    @VisibleForTesting
    fun mergePayrollCyclesIfCutoffDateAndPayrollMonthAreSame(
        payrollCycleContractsList: List<PayrollCycleContracts>
    ): List<ContractsForCutoffDate> {
        val mergedIndexes = HashSet<Int>()
        val mergedPayrollCycleContractsList = mutableListOf<ContractsForCutoffDate>()
        for (first in payrollCycleContractsList.indices) {
            if (mergedIndexes.contains(first)) {
                continue
            }
            val firstPayrollCycle = payrollCycleContractsList[first].toContractsForCutoffDate()
            var mergedPayrollCycle: ContractsForCutoffDate = firstPayrollCycle
            for (second in first + 1 until payrollCycleContractsList.size) {
                if (mergedIndexes.contains(second)) {
                    continue
                }
                val secondPayrollCycle: PayrollCycleContracts = payrollCycleContractsList[second]
                if (firstPayrollCycle.cutoffDate.isEqual(secondPayrollCycle.cutoffDate) &&
                    firstPayrollCycle.payrollMonth == secondPayrollCycle.payrollMonth) {
                    mergedPayrollCycle =
                        ContractsForCutoffDate(
                            cutoffDate = mergedPayrollCycle.cutoffDate,
                            payrollMonth = mergedPayrollCycle.payrollMonth,
                            contractIds =
                                mergedPayrollCycle.contractIds + secondPayrollCycle.contractIds)
                    mergedIndexes.add(first)
                    mergedIndexes.add(second)
                }
            }
            if (mergedPayrollCycle.contractIds.size > firstPayrollCycle.contractIds.size) {
                mergedPayrollCycleContractsList.add(mergedPayrollCycle)
            } else {
                mergedPayrollCycleContractsList.add(firstPayrollCycle)
            }
        }
        return mergedPayrollCycleContractsList
    }

    // todo we can use a ratelimiter instead
    private fun addDelay(noOfSec: Long) {
        try {
            Thread.sleep(noOfSec * 1000)
        } catch (ex: InterruptedException) {
            log.warn("Email sending delay is interrupted", ex)
            Thread.currentThread().interrupt() // Restore interrupted state.
        }
    }
}
