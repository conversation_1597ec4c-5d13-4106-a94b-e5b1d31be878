package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.excel.BulkUploadExcel
import com.multiplier.contract.onboarding.utils.asBase64String
import com.multiplier.contract.onboarding.utils.writeTemporaryFile
import java.io.File
import java.io.InputStream
import java.util.*

data class EmployeeValidationError(
    val error: String,
    val rowNumber: Int,
    val columnName: String,
    val employeeName: String,
    val groupName: String? = null,
)

data class GenericValidationError(val error: String, val rowNumber: Int? = null)

data class GenericValidationWarning(val warning: String, val rowNumber: Int? = null)

data class AggregatedEmployeeValidationResult(
    val totalEmployeeCount: Int,
    val invalidEmployeeCount: Int,
    val validEmployeeCount: Int,
    val errors: List<EmployeeValidationError>,
) {
    companion object {

        val EMPTY =
            AggregatedEmployeeValidationResult(
                totalEmployeeCount = 0,
                invalidEmployeeCount = 0,
                validEmployeeCount = 0,
                errors = emptyList(),
            )
    }
}

fun aggregate(
    employeeValidationResults: EmployeeValidationResults,
    employeeData: List<EmployeeData>
): AggregatedEmployeeValidationResult {
    if (employeeValidationResults == EmployeeValidationResults.EMPTY) {
        return AggregatedEmployeeValidationResult.EMPTY
    }

    val allValidationResults = employeeValidationResults.getAllValidationResults()
    return aggregatedEmployeeValidationResult(allValidationResults, employeeData)
}

private fun aggregatedEmployeeValidationResult(
    allValidationResults: List<GrpcValidationResult<*>>,
    employeeData: List<EmployeeData>
): AggregatedEmployeeValidationResult {
    val validationIdToResults = allValidationResults.groupBy { it.validationId }
    val validCount =
        validationIdToResults.filter { it.value.all { validation -> validation.success } }.size

    val invalid = allValidationResults.filter { !it.success }

    val validationIdToEmployeeData = employeeData.associateBy { it.identification.validationId }
    return AggregatedEmployeeValidationResult(
        totalEmployeeCount = validationIdToResults.size,
        validEmployeeCount = validCount,
        invalidEmployeeCount = validationIdToResults.size - validCount,
        errors =
            invalid.flatMap { validation ->
                val inputData = validationIdToEmployeeData[validation.validationId]

                validation.errors?.map {
                    EmployeeValidationError(
                        error = it,
                        rowNumber = inputData?.identification?.rowNumber ?: -1,
                        columnName = "", // todo not sure how to get this
                        employeeName = inputData?.identification?.fullName ?: "",
                        groupName = validation.groupName)
                }
                    ?: emptyList()
            })
}

data class EmployeeValidationReportFile(
    val id: Long? = null,
    val name: String? = null,
    val extension: String? = null,
    val contentType: String? = null,
    val blob: String? = null,
)

data class BulkValidationReport(
    val genericValidationResult: GenericValidationResult,
    val employeeValidationResult: AggregatedEmployeeValidationResult,
    val errorReportFile: EmployeeValidationReportFile? = null,
) {

    fun hasErrors(): Boolean {
        return genericValidationResult.errors.isNotEmpty() ||
            employeeValidationResult.errors.isNotEmpty()
    }

    fun getAllErrors(): List<String> {
        return genericValidationResult.errors.map { it.error } +
            employeeValidationResult.errors.map { it.error }
    }
}

fun createValidationReport(
    genericValidationResult: GenericValidationResult,
    aggregatedResults: AggregatedEmployeeValidationResult,
    reportTemplate: ByteArray? = null
): BulkValidationReport {
    val errorReport =
        if (aggregatedResults.errors.isEmpty() || reportTemplate == null) {
            null
        } else {
            createErrorReport(reportTemplate.inputStream(), aggregatedResults)
        }

    return BulkValidationReport(
        genericValidationResult = genericValidationResult,
        employeeValidationResult = aggregatedResults,
        errorReportFile = errorReport)
}

private fun createErrorReport(
    employeeDataFile: InputStream,
    aggregatedResults: AggregatedEmployeeValidationResult
): EmployeeValidationReportFile {
    val (path, cleanup) =
        writeTemporaryFile("${UUID.randomUUID()}.xlsx", employeeDataFile.readBytes())

    val errorReportBlob =
        BulkUploadExcel.addValidationErrorsToInputSheet(
            inputSheet = File(path), validationErrors = aggregatedResults.errors)

    cleanup()
    return EmployeeValidationReportFile(
        id = System.currentTimeMillis(),
        name = "bulk_onboarding_validation_report",
        extension = "xlsx",
        contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        blob = errorReportBlob.asBase64String())
}
