package com.multiplier.contract.onboarding.adapter.bulk

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.multiplier.compensation.grpc.schema.BulkUploadServiceGrpc
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.CountrySpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2.Companion.COMPENSATION_SERVICE_KEYS.CONTRACT_ID
import com.multiplier.contract.onboarding.service.bulk.v2.module.CompensationInputsForContract
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.grpc.common.bulkupload.v1.BulkDataRequest
import com.multiplier.grpc.common.bulkupload.v1.FieldRequirement
import com.multiplier.grpc.common.bulkupload.v1.FieldType
import com.multiplier.grpc.common.bulkupload.v1.PlatformKeys
import com.multiplier.grpc.common.bulkupload.v1.fieldRequirementsRequest
import com.multiplier.grpc.common.bulkupload.v1.platformKeys
import com.multiplier.grpc.common.bulkupload.v1.upsertBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.upsertRequest
import com.multiplier.grpc.common.bulkupload.v1.validateUpsertInputBulkRequest
import com.multiplier.grpc.common.bulkupload.v1.validateUpsertInputRequest
import kotlin.random.Random
import kotlin.random.nextULong
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface BulkCompensationSchemaServiceAdapter {
    fun getDataSpecs(options: BulkOnboardingOptions, moduleParams: ModuleParams?): List<DataSpec>

    fun getFieldData(
        options: BulkOnboardingOptions,
        contractIds: List<Long>
    ): List<EmployeeDataChunk>

    fun validateCompensationCreateInputs(
        inputs: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<Map<String, String>>>

    fun createCompensations(
        upsertInputs: List<CreationInput<CompensationInputsForContract>>,
    ): List<CreationResult>
}

@Service
class BulkCompensationSchemaServiceAdapterImpl : BulkCompensationSchemaServiceAdapter {
    private val log = KotlinLogging.logger {}

    @GrpcClient("compensation-service")
    private lateinit var compensationBulkUploadServiceStub:
        BulkUploadServiceGrpc.BulkUploadServiceBlockingStub

    companion object {
        const val COMPENSATION_SETUP_USE_CASE = "COMPENSATION_SETUP"

        const val COUNTRY_CODE = "COUNTRY_CODE"

        const val OFFERING_TYPE = "OFFERING_TYPE"
        const val EOR_OFFERING_TYPE = "EOR"
    }

    override fun getDataSpecs(
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        val request = fieldRequirementsRequest {
            companyId = options.companyId
            moduleParams?.workPlaceEntityId?.let { entityId = it }
            useCase = COMPENSATION_SETUP_USE_CASE

            if (moduleParams != null) {
                buildCustomJsonParamsIfNeeded(
                        options.contractType, moduleParams.jsonCustomParams, options.countryCode)
                    ?.let { jsonCustomParams = it }
            }
        }
        log.info { "Getting compensation data specs for bulk onboarding: $request" }

        return compensationBulkUploadServiceStub
            .getFieldRequirements(request)
            .fieldRequirementsList
            .map { it.toDataSpec() }
    }

    private fun buildCustomJsonParamsIfNeeded(
        contractType: ContractType,
        jsonCustomParams: Map<String, String>,
        countryCode: CountryCode?
    ): String? {
        // set for EOR only for now
        if (contractType != ContractType.EMPLOYEE) return null

        val objectMapper = jacksonObjectMapper()
        val updatedMap =
            jsonCustomParams.toMutableMap().apply {
                put(OFFERING_TYPE, EOR_OFFERING_TYPE)
                countryCode?.let { put(COUNTRY_CODE, it.name) }
            }

        return objectMapper.writeValueAsString(updatedMap)
    }

    override fun getFieldData(
        options: BulkOnboardingOptions,
        contractIds: List<Long>
    ): List<EmployeeDataChunk> {
        if (contractIds.isEmpty()) {
            return emptyList()
        }

        val request =
            BulkDataRequest.newBuilder()
                .setUseCase(COMPENSATION_SETUP_USE_CASE)
                .addAllContractIds(contractIds)
                .addCompanyIds(options.companyId)
                .addEntityIds(options.entityId)
                .build()
        return compensationBulkUploadServiceStub.getFieldData(request).rowsList.map {
            EmployeeDataChunk(
                it.dataMap + mapOf(ContractIdSpec.key to it.keys.contractId.toString()))
        }
    }

    override fun validateCompensationCreateInputs(
        inputs: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<Map<String, String>>> =
        try {
            val request = validateUpsertInputBulkRequest {
                this.inputs.addAll(
                    inputs.map {
                        validateUpsertInputRequest {
                            inputId = it.identification.validationId
                            data.putAll(it.data)
                            keys = buildPlatformKeys(it, options, moduleParams)
                        }
                    })
                useCase = COMPENSATION_SETUP_USE_CASE

                if (moduleParams != null) {
                    buildCustomJsonParamsIfNeeded(
                            options.contractType,
                            moduleParams.jsonCustomParams,
                            options.countryCode)
                        ?.let { jsonCustomParams = it }
                }
            }
            log.info { "Validating compensations for bulk onboarding" }

            val results = compensationBulkUploadServiceStub.bulkValidateUpsertInput(request)

            results.resultsList.map {
                GrpcValidationResult(
                    success = it.success,
                    errors = it.messagesList.flatMap { message -> message.errorsList },
                    validationId = it.inputId,
                    input = it.validatedInputDataMap)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to validate compensations for bulk onboarding" }
            inputs.map {
                GrpcValidationResult(
                    validationId = it.identification.validationId,
                    success = false,
                    errors = listOf("Compensation validation failed due to internal error"),
                    input = null)
            }
        }

    override fun createCompensations(
        upsertInputs: List<CreationInput<CompensationInputsForContract>>,
    ): List<CreationResult> =
        try {
            val request = upsertBulkRequest {
                this.inputs.addAll(
                    upsertInputs.flatMap {
                        it.data.compensationData.map { compensationDataMap ->
                            upsertRequest {
                                inputId = it.requestId.toIndividualUpsertRequestId()
                                data.putAll(
                                    compensationDataMap +
                                        mapOf(CONTRACT_ID to it.contractId.toString()))
                            }
                        }
                    })
                useCase = COMPENSATION_SETUP_USE_CASE
            }
            val response = compensationBulkUploadServiceStub.bulkUpsert(request)
            response.resultsList
                .groupBy { it.inputId.extractCommonRequestId() }
                .map { (requestId, results) ->
                    CreationResult(
                        requestId = requestId,
                        success = results.all { it.success },
                        errors = results.flatMap { it.errorsList })
                }
        } catch (e: Exception) {
            log.warn(e) { "Failed to create compensations for bulk onboarding" }
            upsertInputs.map {
                CreationResult.error(
                    it.requestId,
                    "Upsert compensation failed due to an internal error: unknown exception occurred")
            }
        }

    private fun buildPlatformKeys(
        employeeData: EmployeeData,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): PlatformKeys {
        val contractId = employeeData.identification.contractId
        val companyId = options.companyId
        val entityId = moduleParams?.workPlaceEntityId
        val countryCode = employeeData.data[CountrySpec.key]
        return platformKeys {
            contractId?.let { this.contractId = it }
            companyId?.let { this.companyId = it }
            entityId?.let { this.entityId = it }
            countryCode?.let { this.countryCode = it }
        }
    }
}

private fun String.toIndividualUpsertRequestId() = "${this}->${Random.nextULong()}"

private fun String.extractCommonRequestId() = this.split("->").first()

private fun FieldRequirement.toDataSpec() =
    DataSpec(
        key = this.key,
        type = this.type.toDomain(),
        label = this.label,
        mandatory = this.mandatory,
        description = this.description,
        allowedValues = this.allowedValuesList,
    )

private fun FieldType.toDomain(): DataSpecType {
    return if (this == FieldType.FIELD_TYPE_UNSPECIFIED || this == FieldType.UNRECOGNIZED) {
        DataSpecType.TEXT
    } else {
        DataSpecType.valueOf(this.name.removePrefix("FIELD_TYPE_"))
    }
}
