package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.DepartmentServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkOrgManagementDataServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.OrgManagementData
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.excel.BulkUploadExcel
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService.Companion.DepartmentSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService.Companion.DirectManagerEmailSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService.Companion.IsManagerSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.orgmanagement.schema.BulkAPI
import org.springframework.stereotype.Service

data class OrgManagementDataContext(
    val contractIdToOrgManagementData: Map<Long, EmployeeDataChunk>
)

@Service
class BulkOrgManagementDataService(
    private val departmentServiceAdapter: DepartmentServiceAdapter,
    private val bulkOrgManagementDataServiceAdapter: BulkOrgManagementDataServiceAdapter,
) {

    companion object {

        val DirectManagerEmailSpec =
            DataSpec(
                key = "directManagerEmail",
                type = DataSpecType.TEXT,
                mandatory = false,
                description = "Email address of employee's reporting manager")
        val DepartmentSpec =
            DataSpec(
                key = "department",
                type = DataSpecType.SELECT,
                mandatory = false,
                allowedValues = emptyList(),
                description = "Department of the employee")
        val IsManagerSpec =
            DataSpec(key = "isManager", type = DataSpecType.BOOLEAN, mandatory = false)
    }

    fun getDataSpecs(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.HRIS_PROFILE_DATA ->
                dataSpecsForHrisProfile(onboardingOptions.companyId)
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Org management data specs query for ${onboardingOptions.context} not supported yet",
                    context = mapOf("context" to onboardingOptions.context))
        }
    }

    fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): List<GrpcValidationResult<BulkAPI.UpsertOrgManagementDataInput>> {
        return bulkOrgManagementDataServiceAdapter.validateUpsertOrgManagementDataInputs(
            employeeData, options)
    }

    fun upsertOrgManagementData(
        inputs: List<ValidInput<BulkAPI.UpsertOrgManagementDataInput>>,
        contractContext: ContractContext,
        options: BulkOnboardingOptions,
    ): List<String> {
        val upsertOrgManagementDataInputs =
            inputs.map {
                CreationInput(
                    requestId = it.validationId,
                    contractId = contractContext.getContractId(it.validationId),
                    data = it.input)
            }

        val results =
            bulkUpsert(
                "Upsert org management data",
                upsertOrgManagementDataInputs,
                options,
                CreationInput.refContractId) { orgManagementInputs, onboardingOptions ->
                    bulkOrgManagementDataServiceAdapter.upsertOrgManagementData(
                        orgManagementInputs, onboardingOptions)
                }

        return results.flatMap { it.errors }
    }

    private fun dataSpecsForHrisProfile(companyId: Long): List<DataSpec> {
        val departments =
            departmentServiceAdapter.getDepartments(companyId).map { "${it.id} - ${it.name}" }

        return listOf(
            DirectManagerEmailSpec, DepartmentSpec.copy(allowedValues = departments), IsManagerSpec)
    }

    fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        contractIds: Set<Long>,
        options: BulkOnboardingOptions
    ): OrgManagementDataContext {
        if (options.context != BulkOnboardingContext.HRIS_PROFILE_DATA) {
            return OrgManagementDataContext(emptyMap())
        }

        val orgManagementDataByContractId =
            bulkOrgManagementDataServiceAdapter
                .getOrgManagementData(contractIds, options)
                .associateBy { it.contractId }

        return OrgManagementDataContext(
            contractIds.associateWith { contractId ->
                orgManagementDataByContractId[contractId]?.toEmployeeDataChunk(dataSpecs)
                    ?: EmployeeDataChunk(
                        mapOf(
                            IsManagerSpec.key to BulkUploadExcel.BooleanValues.NO.name,
                        ))
            })
    }
}

fun OrgManagementData.toEmployeeDataChunk(dataSpecs: List<DataSpec>): EmployeeDataChunk {
    val data =
        dataSpecs
            .mapNotNull { spec ->
                val value =
                    when (spec.key) {
                        IsManagerSpec.key ->
                            if (this.isManager) BulkUploadExcel.BooleanValues.YES.name
                            else BulkUploadExcel.BooleanValues.NO.name
                        DepartmentSpec.key ->
                            this.department?.let { department ->
                                "${department.id} - ${department.name}"
                            }
                        DirectManagerEmailSpec.key -> this.directManager?.email
                        else -> null
                    }
                value?.let { spec.key to it }
            }
            .toMap()

    return EmployeeDataChunk(data)
}
