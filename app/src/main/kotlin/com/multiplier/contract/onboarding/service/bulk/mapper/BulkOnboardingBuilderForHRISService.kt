package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.DirectEmployeeOnboardingFlowType
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkOnboardingBuilderForHRISService(
    val contractServiceAdapter: ContractServiceAdapter,
) {

    private val log = KotlinLogging.logger {}

    fun buildOnboardingsForHrisProfileData(contractIds: List<Long>): List<Onboarding> {
        val contracts = contractServiceAdapter.getNonDeletedNonEndedContracts(contractIds.toSet())
        val directEmployeeContracts =
            contracts.filter { it.type == ContractOuterClass.ContractType.HR_MEMBER }
        val nonDirectEmployeeContracts =
            contracts.filter { it.type != ContractOuterClass.ContractType.HR_MEMBER }
        if (nonDirectEmployeeContracts.isNotEmpty()) {
            log.error {
                "Following contract types are not supported for HRIS profile data onboarding: ${nonDirectEmployeeContracts.map { it.type.name }.distinct()}"
            }
        }
        val contractIdToOnboardingFlowType =
            contractServiceAdapter.getDirectEmployeeOnboardingFlowTypes(
                directEmployeeContracts.map { it.id })

        return directEmployeeContracts
            .map { it.id }
            .map { buildOnboardings(it, contractIdToOnboardingFlowType.getValue(it)) }
            .flatten()
    }

    private fun buildOnboardings(
        contractId: Long,
        onboardingFlowType: DirectEmployeeOnboardingFlowType
    ): List<Onboarding> {
        return when (onboardingFlowType) {
            DirectEmployeeOnboardingFlowType.GP_ONLY -> buildOnboardingsForGpOnly(contractId)
            DirectEmployeeOnboardingFlowType.HRIS_ONLY -> buildOnboardingsForHrisOnly(contractId)
            else -> {
                "No onboarding flow found for the given contract: $contractId, expected one of [GP_ONLY, HRIS_ONLY]"
                buildFallbackOnboardingsForDirectEmployee(contractId)
            }
        }
    }

    private fun buildOnboardingsForGpOnly(contractId: Long): List<Onboarding> {
        return listOf(
            Onboarding(
                contractId = contractId,
                experience = "company",
                status = OnboardingStatus.MEMBER_VERIFICATION_COMPLETED,
                currentStep = OnboardingStep.ONBOARDING_ACTIVATION,
                isBulkOnboarded = true),
            Onboarding(
                contractId = contractId,
                experience = "member",
                status = OnboardingStatus.MEMBER_VERIFICATION_COMPLETED,
                currentStep = OnboardingStep.ONBOARDING_ACTIVATION,
                isBulkOnboarded = true))
    }

    private fun buildOnboardingsForHrisOnly(contractId: Long): List<Onboarding> {
        return listOf(
            Onboarding(
                contractId = contractId,
                experience = "company",
                status = OnboardingStatus.CREATED_CUSTOM,
                currentStep = OnboardingStep.ONBOARDING_REVIEW,
                isBulkOnboarded = true))
    }

    private fun buildFallbackOnboardingsForDirectEmployee(contractId: Long): List<Onboarding> {
        return listOf(
            Onboarding(
                contractId = contractId,
                experience = "company",
                status = OnboardingStatus.CREATED_CUSTOM,
                currentStep = OnboardingStep.ONBOARDING_REVIEW,
                isBulkOnboarded = true))
    }
}
