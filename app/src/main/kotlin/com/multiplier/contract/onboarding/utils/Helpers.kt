package com.multiplier.contract.onboarding.utils

import java.io.File
import java.io.FileOutputStream
import java.net.URL
import java.nio.file.Files
import java.nio.file.Paths
import org.bouncycastle.util.encoders.Base64

fun getResourceAsText(path: String): String? = getResource(path)?.readText()

fun getResource(path: String): URL? = object {}.javaClass.getResource(path)

fun getFileFromResources(path: String): File = File(getResource(path)?.toURI())

fun writeFileFromBase64(path: String, base64: String) {
    val out = FileOutputStream(path)
    out.write(Base64.decode(base64))
    out.close()
}

fun writeFile(path: String, bytes: ByteArray) {
    FileOutputStream(path).use { it.write(bytes) }
}

fun base64StringToByteArray(base64: String): ByteArray =
    java.util.Base64.getDecoder().decode(base64)

fun ByteArray.asBase64String(): String = java.util.Base64.getEncoder().encodeToString(this)

fun projectTmpFolder(): String {
    val path = "${System.getProperty("user.dir")}/tmp/"
    Files.createDirectories(Paths.get(path))
    return path
}

fun writeTemporaryFile(fileName: String, bytes: ByteArray): Pair<String, () -> Unit> {
    val path = "/tmp/$fileName"
    writeFile(path, bytes)
    return Pair(path) { Files.deleteIfExists(Paths.get(path)) }
}
