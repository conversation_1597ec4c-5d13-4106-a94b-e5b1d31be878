package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractFilters
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.CountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmployeeIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EndOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.JobDescriptionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.PositionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.RowIdentifierSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ScopeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StartOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StateOfEmploymentSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.WorkStatusSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.ContractDataContext
import com.multiplier.contract.onboarding.service.bulk.mapper.limitContractSize
import com.multiplier.contract.onboarding.service.bulk.mapper.toEmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.service.bulk.v2.logBulkCreationResults
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.PageRequest
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.CreateContractInput
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkContractModule(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val bulkContractServiceAdapter: BulkContractServiceAdapter,
    private val companyServiceAdapter: CompanyServiceAdapter,
    private val countryServiceAdapter: CountryServiceAdapter,
    private val countryCache: CountryCache
) : BulkDataModule {

    companion object {
        const val MODULE_NAME = "CONTRACT_MODULE"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.FREELANCER ->
                listOf(
                    CountrySpec.copy(
                        type = DataSpecType.SELECT,
                        allowedValues = getCountryListOptions(onboardingOptions),
                        description = "Contractor's tax residency"),
                    EmployeeIdSpec.copy(
                        label = "Contractor Id",
                        mandatory = false,
                        description = "Unique identifier for a contractor"),
                    PositionSpec.copy(
                        label = "Designation", description = "Job title of the contractor"),
                    StartOnSpec.copy(
                        label = "Contract Start Date",
                        description = "Date of joining of the contractor in YYYY-MM-DD format"),
                    EndOnSpec.copy(
                        label = "Contract End Date",
                        description = "Contract end date for the contractor in YYYY-MM-DD format"),
                    ScopeSpec.copy(
                        label = "Job Description",
                        description = "Job Description of the Contractor",
                        mandatory = false))
            BulkOnboardingContext.GLOBAL_PAYROLL ->
                listOf(
                    EmployeeIdSpec,
                    ContractIdSpec,
                    PositionSpec.copy(label = "Designation"),
                    StartOnSpec)
            BulkOnboardingContext.AOR ->
                listOf(
                    CountrySpec.copy(
                        type = DataSpecType.SELECT,
                        allowedValues = getCountryListOptions(onboardingOptions),
                        description = "Contractor's tax residency"),
                    EmployeeIdSpec.copy(
                        label = "AOR Contractor Id",
                        mandatory = false,
                        description = "Unique identifier for a contractor"),
                    PositionSpec.copy(
                        label = "Designation", description = "Job title of the contractor"),
                    StartOnSpec.copy(
                        label = "Contract Start Date",
                        description = "Date of joining of the contractor in YYYY-MM-DD format"),
                    EndOnSpec.copy(
                        label = "Contract End Date",
                        description = "Contract end date for the contractor in YYYY-MM-DD format",
                        mandatory = true),
                    ScopeSpec.copy(
                        label = "Job Description",
                        description = "Job Description of the Contractor"))
            BulkOnboardingContext.EOR ->
                listOfNotNull(
                    RowIdentifierSpec,
                    ContractIdSpec,
                    this.getCountryStateDataSpec(onboardingOptions.countryCode),
                    WorkStatusSpec,
                    PositionSpec.copy(label = "Job Title"),
                    JobDescriptionSpec,
                    StartOnSpec,
                    EndOnSpec)
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Bulk Onboarding context value ${onboardingOptions.context} not supported yet",
                    context = mapOf("context" to onboardingOptions.context))
        }
    }

    private fun getCountryStateDataSpec(countryCode: CountryCode): DataSpec? {
        val countryStates = getCountryStates(countryCode)

        return if (countryStates.isNotEmpty())
            StateOfEmploymentSpec.copy(allowedValues = countryStates)
        else null
    }

    private fun getCountryStates(countryCode: CountryCode): List<String> {
        val states =
            countryServiceAdapter
                .getCountryStates(setOf(countryCode))
                .filterKeys { it == countryCode }
                .values
                .firstOrNull()

        return states?.map { it.code }?.sorted() ?: emptyList()
    }

    override fun getContractDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?,
    ): ContractDataContext {
        val contracts =
            contractServiceAdapter.getContractsBy(
                ContractFilters(
                    companyIds = listOf(options.companyId),
                    legalEntityId = options.entityId,
                    types = listOf(ContractOuterClass.ContractType.HR_MEMBER),
                    statuses =
                        listOf(
                            ContractOuterClass.ContractStatus.ONBOARDING,
                            ContractOuterClass.ContractStatus.ACTIVE),
                    pageRequest = pageRequest.limitContractSize()))

        log.info { "Extracting data from ${contracts.size} contracts" }
        val legalEntities = companyServiceAdapter.getCompanyLegalEntities(options.companyId)

        return ContractDataContext(
            contracts.associate { it.id to it.toEmployeeDataChunk(dataSpecs, legalEntities) })
    }

    private fun getCountryListOptions(
        onboardingOptions: BulkOnboardingOptions
    ): Collection<String> {
        return enumValues<CountryCode>()
            .filterNot {
                it in getCountriesToFilter(onboardingOptions)
            } // Filter out unwanted countries
            .mapNotNull { countryCache.getCountryName(it) }
            .distinct()
    }

    private fun getCountriesToFilter(
        onboardingOptions: BulkOnboardingOptions
    ): Collection<CountryCode> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.AOR -> listOf(CountryCode.USA, CountryCode.SGP)
            else -> return emptyList()
        }
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        return bulkContractServiceAdapter.validateContractCreateInputs(employeeData, options)
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        val upsertContractInputs =
            validationResults.map {
                CreationInput(
                    requestId = it.validationId,
                    memberId = bulkCreationResult.getMemberId(it.validationId),
                    data = it.input as CreateContractInput)
            }
        val results =
            bulkUpsert(
                "Upsert contact",
                upsertContractInputs,
                options,
                CreationInput.refMemberId,
            ) { inputs, onboardingOptions ->
                bulkContractServiceAdapter.createContracts(inputs, onboardingOptions)
            }
        logBulkCreationResults(results, bulkCreationResult, "Upsert contract")

        val existingContractIds =
            upsertContractInputs.map { it.data.contractId }.filter { it > 0 }.toSet()
        val newContractIds = results.map { it.upsertedIds.first() }.toSet() - existingContractIds
        return bulkCreationResult
            .copy(
                requestIdToContractId =
                    results
                        .filter { it.success }
                        .associate { it.requestId to it.upsertedIds.first() },
                newContractIds = newContractIds.toList())
            .addErrorsFrom(results)
    }
}
