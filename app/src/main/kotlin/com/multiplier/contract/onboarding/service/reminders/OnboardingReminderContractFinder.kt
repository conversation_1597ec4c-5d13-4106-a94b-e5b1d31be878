package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.CompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.OnboardingServiceAdapter
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.isAfter
import com.multiplier.contract.onboarding.domain.model.ComplianceType
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.toLocalDate
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.*
import java.time.LocalDate
import java.time.LocalTime
import java.time.YearMonth
import java.util.*
import lombok.RequiredArgsConstructor
import lombok.extern.slf4j.Slf4j
import mu.KotlinLogging
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Deprecated("Would be removed after release of use-payroll-service-to-get-payroll-cycles")
@Component
@RequiredArgsConstructor
@Slf4j
class OnboardingReminderContractFinder(
    private val payrollCycleContractService: InternalPayrollCycleContractService,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val onboardingServiceAdapter: OnboardingServiceAdapter,
    private val compensationServiceAdapter: CompensationServiceAdapter,
    private val featureFlagService: FeatureFlagService,
) {

    fun findContractsForPayrollCutoffDateReminder(
        sendingDate: LocalDate,
        companyIds: List<Long>,
    ): List<PayrollCycleContracts> {
        val cutoffDatesForNotification =
            listOf(
                sendingDate,
                sendingDate.plusDays(1),
                sendingDate.plusDays(2),
                sendingDate.plusDays(4),
                sendingDate.plusDays(6),
                sendingDate.plusDays(8),
                sendingDate.plusDays(11),
                sendingDate.plusDays(14),
                sendingDate.plusDays(17),
                sendingDate.plusDays(20))
        val payrollCycleContractsList =
            payrollCycleContractService
                .getPayrollCycleContractsByCutoffDatesAndCompanyIds(
                    sendingDate, cutoffDatesForNotification, companyIds)
                .filter { pcc -> pcc.contractIds.isNotEmpty() }

        val eligiblePayrollCycleContractsList =
            filterEligibleContractsForPayrollCycles(payrollCycleContractsList)

        if (eligiblePayrollCycleContractsList.isEmpty()) {
            return emptyList()
        }

        log.info(
            "Found {} contracts for {} payroll cycles",
            eligiblePayrollCycleContractsList.flatMap { it.contractIds }.size,
            eligiblePayrollCycleContractsList.size)
        return eligiblePayrollCycleContractsList
    }

    fun findContractsForStartDateReminder(
        sendingDate: LocalDate,
        companyIds: List<Long>
    ): List<PayrollCycleContracts> {
        val contracts: List<Contract> =
            findEligibleContractsForStartDateReminder(sendingDate, companyIds)
        if (contracts.isEmpty()) {
            return emptyList()
        }
        log.info(
            "Before filtering, found {} monthly payroll contracts which will start in 3 or 7 days from {}",
            contracts.size,
            sendingDate)
        val payrollCycleContractsList: List<PayrollCycleContracts> =
            parkContractsIntoPayrollCycles(contracts, sendingDate)
        if (payrollCycleContractsList.isEmpty()) {
            return emptyList()
        }
        val mergedPayrollCycleContractList: List<PayrollCycleContracts> =
            mergePayrollCycleWhoseCutoffDateHasPassed(sendingDate, payrollCycleContractsList)

        log.info(
            "Found {} contracts which will start in 3 or 7 days from {}, parked in {} payroll cycles, ",
            mergedPayrollCycleContractList.flatMap { it.contractIds }.size,
            sendingDate,
            mergedPayrollCycleContractList.size)

        return mergedPayrollCycleContractList
    }

    private fun findEligibleContractsForStartDateReminder(
        sendingDate: LocalDate,
        companyIds: List<Long>
    ): List<Contract> {
        val contracts: List<Contract> =
            getContractsStartInFutureDates(
                listOf(sendingDate.plusDays(3), sendingDate.plusDays(7)), companyIds)
        val eligibleContracts: List<Contract> = filterEligibleContracts(contracts)
        return if (eligibleContracts.isEmpty()) {
            emptyList()
        } else filterMonthlyPayrollContracts(eligibleContracts)
    }

    private fun parkContractsIntoPayrollCycles(
        contracts: List<Contract>,
        sendingDate: LocalDate
    ): List<PayrollCycleContracts> {

        val fromMonth =
            contracts.mapNotNull { it.startOn }.minOfOrNull { YearMonth.from(it.toLocalDate()) }
                ?: YearMonth.from(contracts[0].startOn.toLocalDate())

        val toMonth =
            (contracts.mapNotNull { it.startOn }.maxOfOrNull { YearMonth.from(it.toLocalDate()) }
                    ?: YearMonth.from(contracts[0].startOn.toLocalDate()))
                .plusMonths(1)

        val payrollCycles: ArrayList<PayrollCycleContracts> = ArrayList()
        var month: YearMonth = fromMonth

        while (month.isBefore(toMonth) || month == toMonth) {
            payrollCycles.add(PayrollCycleHelper.getMonthlyPayrollCycleForMonth(month))
            month = month.plusMonths(1)
        }

        val payrollCycleContractsList: ArrayList<PayrollCycleContracts> =
            ArrayList<PayrollCycleContracts>()
        for (payrollCycle in payrollCycles) {
            val eligibleContracts: List<Contract> =
                filterContractsEligibleForStartDateReminderInPayrollCycle(
                    contracts, sendingDate, payrollCycle)

            if (eligibleContracts.isNotEmpty()) {
                payrollCycleContractsList.add(
                    PayrollCycleContracts(payrollCycle, eligibleContracts.map { it.id }))
            }
        }
        return payrollCycleContractsList
    }

    private fun filterContractsEligibleForStartDateReminderInPayrollCycle(
        contracts: List<Contract>,
        sendingDate: LocalDate,
        payrollCycle: PayrollCycleContracts
    ): List<Contract> {
        val eligibleContracts: LinkedList<Contract> = LinkedList<Contract>()

        for (contract in contracts) {
            if (contractIsEligibleForPayrollCycle(contract, payrollCycle)) {
                if (contractStartBeforeOrOnPayrollCutoffDate(payrollCycle, contract)) {
                    if (payrollCutoffReminderIsNotBeingSend(sendingDate, payrollCycle)) {
                        eligibleContracts.add(contract)
                    }
                } else if (contractIsCreatedAfterPayrollCutoffDate(payrollCycle, contract) &&
                    sendingDate.isAfter(payrollCycle.cutoffDate)) {
                    eligibleContracts.add(contract)
                }
            }
        }

        return eligibleContracts
    }

    private fun contractIsCreatedAfterPayrollCutoffDate(
        payrollCycle: PayrollCycleContracts,
        contract: Contract
    ): Boolean {
        return contract.createdOn.toLocalDate()?.isAfter(payrollCycle.cutoffDate) ?: false
    }

    private fun contractStartBeforeOrOnPayrollCutoffDate(
        payrollCycle: PayrollCycleContracts,
        contract: Contract
    ): Boolean {
        return !contract.startOn.toLocalDate().isAfter(payrollCycle.cutoffDate)
    }

    private fun contractIsEligibleForPayrollCycle(
        contract: Contract,
        payrollCycle: PayrollCycleContracts
    ): Boolean {
        return isDateInInclusiveRange(
            contract.startOn.toLocalDate(),
            payrollCycle.contractStartFrom,
            payrollCycle.contractStartTo)
    }

    private fun mergePayrollCycleWhoseCutoffDateHasPassed(
        sendingDate: LocalDate,
        payrollCycleContractsList: List<PayrollCycleContracts>
    ): List<PayrollCycleContracts> {
        val mergedPayrollCycleContractList: LinkedList<PayrollCycleContracts> =
            LinkedList<PayrollCycleContracts>()
        if (payrollCycleContractsList.size > 1) {
            var i = 0
            while (i < payrollCycleContractsList.size - 1) {
                val payrollCycleContracts: PayrollCycleContracts = payrollCycleContractsList[i]
                if (sendingDate.isAfter(payrollCycleContracts.cutoffDate)) {
                    val nextPayrollCycleContracts: PayrollCycleContracts =
                        payrollCycleContractsList[++i]

                    val mergedContractIds =
                        payrollCycleContracts.contractIds + nextPayrollCycleContracts.contractIds

                    val mergedPayrollCycleContracts =
                        PayrollCycleContracts(nextPayrollCycleContracts, mergedContractIds)
                    mergedPayrollCycleContractList.add(mergedPayrollCycleContracts)
                } else {
                    mergedPayrollCycleContractList.add(payrollCycleContracts)
                }
                i++
            }
        } else {
            val payrollCycleContracts: PayrollCycleContracts = payrollCycleContractsList[0]
            if (sendingDate.isAfter(payrollCycleContracts.cutoffDate)) {
                val nextMonthPayrollCycle =
                    PayrollCycleHelper.getMonthlyPayrollCycleForMonth(
                        payrollCycleContracts.payrollMonth.plusMonths(1))
                mergedPayrollCycleContractList.add(
                    PayrollCycleContracts(nextMonthPayrollCycle, payrollCycleContracts.contractIds))
            } else {
                mergedPayrollCycleContractList.add(payrollCycleContracts)
            }
        }
        return mergedPayrollCycleContractList
    }

    private fun filterMonthlyPayrollContracts(contracts: List<Contract>): List<Contract> {
        val compensationByContractId =
            compensationServiceAdapter.getCurrentCompensationByContractIds(
                contracts.map { it.id }.toSet())

        return contracts.filter { hasMonthlyPayroll(compensationByContractId[it.id]) }
    }

    private fun getContractsStartInFutureDates(
        startDates: List<LocalDate>,
        companyIds: List<Long>
    ): List<Contract> {
        val minStartDate =
            startDates
                .stream()
                .min { obj: LocalDate, o: LocalDate? -> obj.compareTo(o) }
                .orElse(startDates[0])
        val maxStartDate =
            startDates
                .stream()
                .max { obj: LocalDate, o: LocalDate? -> obj.compareTo(o) }
                .orElse(startDates[0])

        val contracts =
            contractServiceAdapter.getContractsBy(
                filters =
                    com.multiplier.contract.onboarding.adapter.ContractFilters(
                        type = ContractType.EMPLOYEE,
                        status = ContractStatus.ONBOARDING,
                        startDateRange =
                            com.multiplier.contract.onboarding.adapter.DateRange(
                                from = minStartDate.atStartOfDay(),
                                to = maxStartDate.atTime(LocalTime.MAX)),
                        companyIds = companyIds),
            )

        return contracts.filter {
            it.startOn != null && startDates.contains(it.startOn.toLocalDate())
        }
    }

    fun filterEligibleContractIds(contractIds: Collection<Long>): List<Long> {
        val contracts = contractServiceAdapter.getNonDeletedNonEndedContracts(contractIds.toSet())
        return filterEligibleContracts(contracts).map { it.id }
    }

    fun filterEligibleContracts(contracts: Collection<Contract>): List<Contract> {
        val contractById = contracts.associateBy { it.id }

        val companyExpOnboardingByContractId =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(contractById.keys, "company")
        val complianceByContractId =
            contractServiceAdapter.getComplianceByContractIds(contractById.keys).associateBy {
                it.contractId
            }

        return contracts.filter { contract ->
            val onboarding = companyExpOnboardingByContractId[contract.id]
            onboarding != null &&
                contractIsInOnboardingState(contract) &&
                onboarding.status !== OnboardingStatus.REVOKED &&
                isOnboardingNotificationFeatureFlagOn(contract.companyId) &&
                hasPassedCompensationDefinitionSteps(onboarding) &&
                isMultiplierContractUnderMultiplerEntity(
                    contract, complianceByContractId[contract.id])
        }
    }

    private fun payrollCutoffReminderIsNotBeingSend(
        date: LocalDate,
        payrollCycleContracts: PayrollCycleContracts
    ): Boolean {
        return !isDateInInclusiveRange(
            date, payrollCycleContracts.cutoffDate.minusDays(21), payrollCycleContracts.cutoffDate)
    }

    private fun isDateInInclusiveRange(date: LocalDate, from: LocalDate?, to: LocalDate?): Boolean {
        return !date.isBefore(from) && !date.isAfter(to)
    }

    private fun contractIsInOnboardingState(contract: Contract): Boolean {
        return contract.status === ContractStatus.ONBOARDING
    }

    private fun hasPassedCompensationDefinitionSteps(onboarding: Onboarding): Boolean {
        return onboarding.status.isAfter(OnboardingStatus.DRAFT) &&
            onboarding.currentStep.isAfter(OnboardingStep.DEFINITION_COMPENSATION_DETAILS)
    }

    private fun isMultiplierContractUnderMultiplerEntity(
        contract: Contract,
        compliance: com.multiplier.contract.onboarding.domain.model.Compliance?
    ): Boolean {
        return contract.workStatus === CountryWorkStatus.RESIDENT &&
            compliance != null &&
            compliance.type === ComplianceType.MULTIPLIER &&
            compliance.agreementType ===
                com.multiplier.contract.onboarding.domain.model.ContractAgreementType
                    .MULTIPLIER_TEMPLATE
    }

    private fun isOnboardingNotificationFeatureFlagOn(companyId: Long): Boolean {
        return featureFlagService
            .feature(
                FeatureFlags.ONBOARDING_NOTIFICATION,
                mapOf(FeatureFlags.Params.COMPANY to companyId))
            .on
    }

    private fun hasMonthlyPayroll(compensation: CompensationOuterClass.Compensation?): Boolean {
        return compensation?.postProbationBasePay != null &&
            compensation.postProbationBasePay.payFrequency ===
                CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY
    }

    private fun filterEligibleContractsForPayrollCycles(
        payrollCycleContractsList: List<PayrollCycleContracts>
    ): List<PayrollCycleContracts> {
        val contractIds = payrollCycleContractsList.flatMap { it.contractIds }

        val eligibleContractIds = filterEligibleContractIds(contractIds)
        val filteredPayrollCycleContractsList = mutableListOf<PayrollCycleContracts>()

        for (payrollCycleContracts in payrollCycleContractsList) {
            val eligibleContractIdsForCycle =
                payrollCycleContracts.contractIds.filter { eligibleContractIds.contains(it) }

            if (eligibleContractIdsForCycle.isNotEmpty()) {
                filteredPayrollCycleContractsList.add(
                    PayrollCycleContracts(payrollCycleContracts, eligibleContractIdsForCycle))
            }
        }

        return filteredPayrollCycleContractsList
    }
}
