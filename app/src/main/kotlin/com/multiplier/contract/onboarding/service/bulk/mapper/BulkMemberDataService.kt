package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService.AddressLegalData
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressCitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressCountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressLine1Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressLine2Spec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressPostalCodeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.CurrentAddressStateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.DateOfBirthSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmailSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.FirstNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.GenderSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.MaritalStatusSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.NationalitySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.PhoneNumberSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule.Companion.DYNAMIC_BANK_DETAILS_SPEC
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule.Companion.STATIC_BANK_DETAILS_SPEC
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule.Companion.StaticBankDataSpec
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.Gender
import com.multiplier.contract.onboarding.types.Relationship
import com.multiplier.member.schema.Member.MaritalStatus
import java.time.format.DateTimeFormatter
import org.springframework.stereotype.Service

data class MemberContext(val requestIdToMemberId: Map<String, Long>) {
    fun getMemberId(requestId: String): Long? = requestIdToMemberId[requestId]
}

data class MemberDataContext(val memberIdToMemberData: Map<Long, EmployeeDataChunk>) {
    fun isNotEmpty() = memberIdToMemberData.isNotEmpty()
}

@Service
class BulkMemberDataService(
    private val bulkMemberServiceAdapter: BulkMemberServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
) {

    companion object {
        val FirstNameSpec =
            DataSpec(
                key = "firstName",
                type = DataSpecType.TEXT,
                description = "First name of the employee")
        val LastNameSpec =
            DataSpec(
                key = "lastName",
                type = DataSpecType.TEXT,
                description = "Last name of the employee")
        val EmailSpec =
            DataSpec(
                key = "email",
                type = DataSpecType.TEXT,
                description = "Email address of the employee")
        val GenderSpec =
            DataSpec(
                key = "gender",
                type = DataSpecType.SELECT,
                allowedValues = Gender.values().map { mapGenderOption(it.name) },
                description = "Gender of the employee")
        val DateOfBirthSpec =
            DataSpec(key = "dateOfBirth", type = DataSpecType.DATE, mandatory = false)
        val NationalitySpec =
            DataSpec(key = "nationality", type = DataSpecType.TEXT, mandatory = false)
        val MaritalStatusSpec =
            DataSpec(
                key = "maritalStatus",
                type = DataSpecType.SELECT,
                mandatory = false,
                allowedValues =
                    listOf(
                        MaritalStatus.SINGLE.name,
                        MaritalStatus.MARRIED.name,
                        MaritalStatus.DIVORCED.name,
                        MaritalStatus.UNSPECIFIED.name))
        val PhoneNumberSpec =
            DataSpec(key = "phoneNumber", type = DataSpecType.TEXT, mandatory = false)
        val CurrentAddressLine1Spec =
            DataSpec(key = "currentAddressLine1", type = DataSpecType.TEXT, mandatory = false)
        val CurrentAddressLine2Spec =
            DataSpec(key = "currentAddressLine2", type = DataSpecType.TEXT, mandatory = false)
        val CurrentAddressCitySpec =
            DataSpec(key = "currentAddressCity", type = DataSpecType.TEXT, mandatory = false)
        val CurrentAddressStateSpec =
            DataSpec(key = "currentAddressState", type = DataSpecType.TEXT, mandatory = false)
        val CurrentAddressCountrySpec =
            DataSpec(key = "currentAddressCountry", type = DataSpecType.TEXT, mandatory = false)
        val CurrentAddressPostalCodeSpec =
            DataSpec(key = "currentAddressPostalCode", type = DataSpecType.TEXT, mandatory = false)
        val PermanentAddressLine1Spec =
            DataSpec(key = "permanentAddressLine1", type = DataSpecType.TEXT, mandatory = false)
        val PermanentAddressLine2Spec =
            DataSpec(key = "permanentAddressLine2", type = DataSpecType.TEXT, mandatory = false)
        val PermanentAddressCitySpec =
            DataSpec(key = "permanentAddressCity", type = DataSpecType.TEXT, mandatory = false)
        val PermanentAddressStateSpec =
            DataSpec(key = "permanentAddressState", type = DataSpecType.TEXT, mandatory = false)
        val PermanentAddressCountrySpec =
            DataSpec(key = "permanentAddressCountry", type = DataSpecType.TEXT, mandatory = false)
        val PermanentAddressPostalCodeSpec =
            DataSpec(
                key = "permanentAddressPostalCode", type = DataSpecType.TEXT, mandatory = false)
        val EmergencyContactNameSpec =
            DataSpec(key = "emergencyContactName", type = DataSpecType.TEXT, mandatory = false)
        val EmergencyContactRelationshipSpec =
            DataSpec(
                key = "emergencyContactRelationship",
                type = DataSpecType.SELECT,
                mandatory = false,
                allowedValues = Relationship.values().map { it.name })
        val EmergencyContactPhoneNumberSpec =
            DataSpec(
                key = "emergencyContactPhoneNumber", type = DataSpecType.TEXT, mandatory = false)
        val LastSchoolNameSpec =
            DataSpec(key = "lastSchoolName", type = DataSpecType.TEXT, mandatory = false)
        val LastSchoolDegreeSpec =
            DataSpec(key = "lastSchoolDegree", type = DataSpecType.TEXT, mandatory = false)
        val LastSchoolYearOfPassingSpec =
            DataSpec(key = "lastSchoolYearOfPassing", type = DataSpecType.TEXT, mandatory = false)
        val LastSchoolGpaSpec =
            DataSpec(key = "lastSchoolGpa", type = DataSpecType.TEXT, mandatory = false)
        val LastSchoolGradeSpec =
            DataSpec(key = "lastSchoolGrade", type = DataSpecType.TEXT, mandatory = false)
        val LastEmployerNameSpec =
            DataSpec(key = "lastEmployerName", type = DataSpecType.TEXT, mandatory = false)
        val LastEmployerPositionSpec =
            DataSpec(key = "lastEmployerPosition", type = DataSpecType.TEXT, mandatory = false)
        val LastEmployerStartDateSpec =
            DataSpec(key = "lastEmployerStartDate", type = DataSpecType.DATE, mandatory = false)
        val LastEmployerEndDateSpec =
            DataSpec(key = "lastEmployerEndDate", type = DataSpecType.DATE, mandatory = false)
    }

    fun getDataSpecs(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.HRIS_PROFILE_DATA -> memberDataSpecsForHrisProfile()
            BulkOnboardingContext.EOR -> memberDataSpecsForEOR()
            BulkOnboardingContext.AOR -> memberDataSpecsForAOR()
            BulkOnboardingContext.FREELANCER -> memberDataSpecsForFreelancer()
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Member data specs query for ${onboardingOptions.context} not supported yet",
                    context = mapOf("context" to onboardingOptions.context))
        }
    }

    private fun memberDataSpecsForAOR() =
        listOf(
            FirstNameSpec,
            LastNameSpec,
            EmailSpec,
            GenderSpec,
            PhoneNumberSpec.copy(mandatory = true),
            CurrentAddressLine1Spec.copy(key = "address.line1", mandatory = true),
            CurrentAddressLine2Spec.copy(key = "address.line2", mandatory = true),
            CurrentAddressCitySpec.copy(key = "address.city", mandatory = true),
            CurrentAddressStateSpec.copy(key = "address.state"),
            CurrentAddressCountrySpec.copy(key = "address.country", mandatory = true),
            CurrentAddressPostalCodeSpec.copy(key = "address.postalCode", mandatory = true))

    private fun memberDataSpecsForFreelancer() =
        listOf(FirstNameSpec, LastNameSpec, EmailSpec, GenderSpec)

    private fun memberDataSpecsForEOR() =
        listOf(
            CurrentAddressCountrySpec,
            CurrentAddressStateSpec,
            FirstNameSpec,
            LastNameSpec,
            EmailSpec,
            GenderSpec)

    private fun memberDataSpecsForHrisProfile() =
        listOf(
            FirstNameSpec,
            LastNameSpec,
            GenderSpec,
            EmailSpec,
            DateOfBirthSpec,
            NationalitySpec,
            MaritalStatusSpec,
            PhoneNumberSpec,
            CurrentAddressLine1Spec,
            CurrentAddressLine2Spec,
            CurrentAddressCitySpec,
            CurrentAddressStateSpec,
            CurrentAddressCountrySpec,
            CurrentAddressPostalCodeSpec,
            PermanentAddressLine1Spec,
            PermanentAddressLine2Spec,
            PermanentAddressCitySpec,
            PermanentAddressStateSpec,
            PermanentAddressCountrySpec,
            PermanentAddressPostalCodeSpec,
            EmergencyContactNameSpec,
            EmergencyContactRelationshipSpec,
            EmergencyContactPhoneNumberSpec,
            LastSchoolNameSpec,
            LastSchoolDegreeSpec,
            LastSchoolYearOfPassingSpec,
            LastSchoolGpaSpec,
            LastSchoolGradeSpec,
            LastEmployerNameSpec,
            LastEmployerPositionSpec,
            LastEmployerStartDateSpec,
            LastEmployerEndDateSpec)

    fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): List<GrpcValidationResult<com.multiplier.member.schema.MemberCreateInput>> {
        return bulkMemberServiceAdapter.validateMemberCreateInputs(employeeData, options)
    }

    fun createMembers(
        inputs: List<ValidInput<com.multiplier.member.schema.MemberCreateInput>>,
        options: BulkOnboardingOptions
    ): MemberContext {
        val upsertMemberInputs =
            inputs.map {
                CreationInput(
                    requestId = it.validationId,
                    data = it.input,
                )
            }

        val results =
            bulkUpsert("Upsert member", upsertMemberInputs, options, CreationInput.refSelf) {
                memberInputs,
                onboardingOptions ->
                bulkMemberServiceAdapter.createMembers(memberInputs, onboardingOptions)
            }

        return MemberContext(
            results.filter { it.success }.associate { it.requestId to it.upsertedIds.first() })
    }

    fun getDataForSpecs(dataSpecs: List<DataSpec>, memberIds: Set<Long>): MemberDataContext {
        val members =
            memberServiceAdapter.getMembersWithAddressesAndBankAccountsAndLegalData(memberIds)
        val memberIdToMemberData = members.associate { it.id to it.toEmployeeDataChunk(dataSpecs) }

        return MemberDataContext(memberIdToMemberData)
    }
}

fun com.multiplier.contract.onboarding.domain.model.Member.toEmployeeDataChunk(
    dataSpecs: List<DataSpec>
): EmployeeDataChunk {
    val legalDataKeys =
        dataSpecs.filter { it.source == BulkMemberLegalDataService.LEGAL_SPEC_DATA }.map { it.key }
    val data =
        dataSpecs
            .associate {
                it.key to
                    when (it.key) {
                        FirstNameSpec.key -> this.firstName
                        LastNameSpec.key -> this.lastName
                        EmailSpec.key -> this.email
                        GenderSpec.key -> this.gender?.name?.let(::mapGenderOption)
                        DateOfBirthSpec.key -> this.dateOfBirth?.format(DateTimeFormatter.ISO_DATE)
                        NationalitySpec.key -> this.nationality
                        MaritalStatusSpec.key -> this.maritalStatus
                        PhoneNumberSpec.key -> this.phoneNumber
                        BulkMemberLegalDataService.ReligionSpec.key -> this.religion
                        BulkMemberLegalDataService.NationalIdSpec.key -> this.nationalId
                        BulkMemberLegalDataService.PassportNumberSpec.key -> this.passportNumber
                        CurrentAddressLine1Spec.key,
                        AddressLegalData.AddressLine1Spec.key -> this.address?.line1
                        CurrentAddressLine2Spec.key,
                        AddressLegalData.AddressLine2Spec.key -> this.address?.line2
                        CurrentAddressCitySpec.key,
                        AddressLegalData.AddressCitySpec.key -> this.address?.city
                        CurrentAddressStateSpec.key,
                        AddressLegalData.AddressStateSpec.key -> this.address?.state
                        CurrentAddressCountrySpec.key,
                        AddressLegalData.AddressCountrySpec.key -> this.address?.country?.name
                        CurrentAddressPostalCodeSpec.key,
                        AddressLegalData.AddressPostalCodeSpec.key -> this.address?.postalCode
                        AddressLegalData.AddressZipCodeSpec.key -> this.address?.zipCode
                        in legalDataKeys -> this.legalDataProps[it.key]
                        else -> null
                    }
            }
            .filterValues { it != null }
            .mapValues { requireNotNull(it.value) }

    val staticBankData =
        if (dataSpecs.any { it.source == STATIC_BANK_DETAILS_SPEC })
            mapOf(
                    StaticBankDataSpec.AccountHolderNameSpec.keyWithPrefix() to
                        this.staticBankDetails?.bankAccountHolderName.orEmpty(),
                    StaticBankDataSpec.AccountNumberSpec.keyWithPrefix() to
                        this.staticBankDetails?.bankAccountNumber.orEmpty(),
                    StaticBankDataSpec.BankNameSpec.keyWithPrefix() to
                        this.staticBankDetails?.bankName.orEmpty(),
                    StaticBankDataSpec.BranchNameSpec.keyWithPrefix() to
                        this.staticBankDetails?.bankBranch.orEmpty(),
                    StaticBankDataSpec.SwiftCodeSpec.keyWithPrefix() to
                        this.staticBankDetails?.swiftCode.orEmpty(),
                    StaticBankDataSpec.LocalBankCodeSpec.keyWithPrefix() to
                        this.staticBankDetails?.localBankCode.orEmpty())
                .filterValues { it.isNotBlank() }
        else emptyMap()
    val dynamicBankData =
        dataSpecs
            .filter { it.source == DYNAMIC_BANK_DETAILS_SPEC }
            .associate { it.keyWithPrefix() to this.dynamicBankDetails[it.key].orEmpty() }
            .filterValues { it.isNotBlank() }

    return EmployeeDataChunk(data + staticBankData + dynamicBankData)
}

private fun mapGenderOption(genderName: String): String {
    return when (genderName) {
        Gender.UNSPECIFIED.name -> "OTHER"
        else -> genderName
    }
}
