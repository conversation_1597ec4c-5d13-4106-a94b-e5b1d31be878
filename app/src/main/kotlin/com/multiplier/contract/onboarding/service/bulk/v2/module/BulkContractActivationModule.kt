package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.bulk.BulkContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.usecase.ModuleParams
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkContractActivationModule(
    private val bulkContractServiceAdapter: BulkContractServiceAdapter,
    private val featureFlagService: FeatureFlagService,
) : BulkDataModule {
    private val log = KotlinLogging.logger {}

    companion object {
        const val MODULE_NAME = "CONTRACT_ACTIVATION_MODULE"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        return emptyList()
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        log.info { "Inside create method of BulkContractActivationModule" }

        return try {
            if (featureFlagService.feature(FeatureFlags.CONTRACT_TO_PAYROLL_LINKING).on) {
                log.info { "Feature flag CONTRACT_TO_PAYROLL_LINKING is on" }
                bulkContractServiceAdapter.activateContracts(bulkCreationResult.newContractIds)
                log.info { "Activated contracts: ${bulkCreationResult.newContractIds}" }
            } else {
                log.info {
                    "Skipping contract activation as the feature flag CONTRACT_TO_PAYROLL_LINKING is off"
                }
            }
            bulkCreationResult
        } catch (e: Exception) {
            log.warn(e) { "Failed to activate contracts: ${bulkCreationResult.newContractIds}" }
            bulkCreationResult.addErrorsFrom(
                bulkCreationResult.newContractIds
                    .mapNotNull { bulkCreationResult.getRequestIdForContract(it) }
                    .map {
                        CreationResult.error(
                            it, "Contract activation failed due to an internal error: ${e.message}")
                    })
        }
    }
}
