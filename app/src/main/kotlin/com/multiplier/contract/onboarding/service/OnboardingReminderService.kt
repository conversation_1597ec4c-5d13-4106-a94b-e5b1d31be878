package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.repository.JpaOnboardingReminderRepository
import com.multiplier.contract.onboarding.repository.model.JpaOnboardingReminder
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import jakarta.transaction.Transactional
import java.util.Optional
import org.springframework.stereotype.Service

@Service
class OnboardingReminderService(
    private val onboardingReminderRepository: JpaOnboardingReminderRepository,
) {

    @Transactional
    fun createOrUpdateReminder(
        contract: ContractOuterClass.Contract,
        templateName: NotificationType,
        editableTemplateParams: Map<String, String>? = null
    ): JpaOnboardingReminder {
        val existingReminder =
            onboardingReminderRepository.findByContractIdAndTemplateName(contract.id, templateName)

        return if (existingReminder.isPresent) {
            // Update the existing record
            val reminder = existingReminder.get()
            reminder.incrementCount()
            // Update editable template params if provided
            if (editableTemplateParams != null) {
                reminder.editableTemplateParams = editableTemplateParams
            }
            onboardingReminderRepository.save(reminder)
        } else {
            // Create a new record
            val newReminder =
                JpaOnboardingReminder(
                    contractId = contract.id,
                    templateName = templateName,
                    editableTemplateParams = editableTemplateParams)
            onboardingReminderRepository.save(newReminder)
        }
    }

    fun findByContractIdAndTemplateName(
        contractId: Long,
        templateName: NotificationType
    ): Optional<JpaOnboardingReminder> {
        return onboardingReminderRepository.findByContractIdAndTemplateName(
            contractId, templateName)
    }
}
