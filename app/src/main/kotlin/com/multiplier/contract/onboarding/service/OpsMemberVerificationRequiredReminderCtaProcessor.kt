package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.service.extensions.isFreelancerOrContractor
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.NotificationType
import org.springframework.stereotype.Component

@Component
class OpsMemberVerificationRequiredReminderCtaProcessor : BaseReminderCtaTemplateProcessor() {
    override fun notificationType() = NotificationType.OpsMemberVerificationRequiredReminderCta

    override fun validate(preFetchedData: EmailTemplateData): Bo<PERSON><PERSON> {
        if (preFetchedData.operationsUser == null ||
            !preFetchedData.contract.isFreelancerOrContractor()) {
            return false
        }

        val onboardingCompany = preFetchedData.onboardingCompany

        return onboardingCompany.status ==
            ContractOnboardingStatus.MEMBER_VERIFICATION_IN_PROGRESS &&
            onboardingCompany.currentStep == OnboardingStep.ONBOARDING_VERIFYING
    }
}
