package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.bulk.BulkContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.usecase.ModuleParams
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkPostOnboardingTriggerModule(
    private val bulkContractServiceAdapter: BulkContractServiceAdapter,
) : BulkDataModule {

    private val log = KotlinLogging.logger {}

    companion object {
        const val MODULE_NAME = "POST_ONBOARDING_TRIGGER_MODULE"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        // no validation
        return emptyList()
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        return try {
            bulkContractServiceAdapter.triggerPostOnboardingActions(
                bulkCreationResult.newContractIds, options)
            log.info {
                "Created onboarding entities for contract: ${bulkCreationResult.newContractIds}"
            }
            bulkCreationResult
        } catch (e: Exception) {
            log.warn(e) {
                "Failed to trigger post onboarding actions for contract ids: ${bulkCreationResult.newContractIds}"
            }
            bulkCreationResult.addErrorsFrom(
                bulkCreationResult.newContractIds
                    .mapNotNull { bulkCreationResult.getRequestIdForContract(it) }
                    .map {
                        CreationResult.error(
                            it,
                            "Post onboarding actions failed due to an internal error: unknown exception occurred")
                    })
        }
    }
}
