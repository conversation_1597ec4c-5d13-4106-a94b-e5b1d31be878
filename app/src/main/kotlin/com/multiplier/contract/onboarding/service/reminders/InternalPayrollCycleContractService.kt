package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.isAfter
import com.multiplier.contract.onboarding.domain.model.PayrollCycle.PayFrequency
import com.multiplier.contract.onboarding.service.reminders.PayrollCycleHelper.getMonthlyPayrollCycleForCutoffDate
import com.multiplier.contract.onboarding.service.reminders.PayrollCycleHelper.getPrevCycle
import com.multiplier.contract.onboarding.service.reminders.PayrollCycleHelper.getPrevMonthCycle
import com.multiplier.contract.onboarding.service.reminders.PayrollCycleHelper.getSemiMonthly10thPayrollCycleForCutoffDate
import com.multiplier.contract.onboarding.service.reminders.PayrollCycleHelper.getSemiMonthly14thPayrollCycleForCutoffDate
import com.multiplier.contract.onboarding.service.reminders.PayrollCycleHelper.getSemiMonthly15thPayrollCycleForCutoffDate
import com.multiplier.contract.onboarding.service.reminders.PayrollCycleHelper.getSemiMonthly25thPayrollCycleForCutoffDate
import com.multiplier.contract.onboarding.service.reminders.PayrollCycleHelper.getSemiMonthly28thPayrollCycleForCutoffDate
import com.multiplier.contract.onboarding.service.reminders.PayrollCycleHelper.getSemiMonthly30thPayrollCycleForCutoffDate
import com.multiplier.contract.onboarding.service.toLocalDate
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.DayOfWeek
import com.multiplier.contract.onboarding.types.PayFrequencyDate
import com.multiplier.contract.onboarding.types.PayFrequencyDateIdentifier
import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import java.time.LocalDate
import java.time.LocalTime
import java.util.*
import kotlin.collections.set
import lombok.RequiredArgsConstructor
import lombok.extern.slf4j.Slf4j
import mu.KotlinLogging
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

private val log = KotlinLogging.logger {}

/**
 * This is an internal class that provides a temporary implementation for
 * [com.multiplier.core.adapter.api.PayrollServiceAdapter.getPayrollCycleContractsByCutoffDatesAndCompanyIds].
 * This class should be removed once aforementioned method is implemented in payroll-service.
 */
@Deprecated("Would be removed after release of use-payroll-service-to-get-payroll-cycles")
@Component
@RequiredArgsConstructor
@Slf4j
class InternalPayrollCycleContractService(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val compensationServiceAdapter: CompensationServiceAdapter,
    private val onboardingServiceAdapter: OnboardingServiceAdapter,
    private val companyServiceAdapter: CompanyServiceAdapter,
) {

    @Transactional
    fun getPayrollCycleContractsByCutoffDatesAndCompanyIds(
        sendingDate: LocalDate,
        cutoffDates: List<LocalDate>,
        companyIds: List<Long>,
    ): List<PayrollCycleContracts> {
        val payrollCycles =
            cutoffDates.flatMap { getPayrollCyclesForCutoffDate(it) }.sortedBy { it.cutoffDate }

        if (payrollCycles.isEmpty()) {
            return emptyList()
        }

        val payrollCycleWithMinContractStartFrom =
            payrollCycles.minByOrNull { it.contractStartFrom } ?: payrollCycles[0]
        val minContractStartFromOfPrevMonthCycle =
            getPrevMonthCycle(payrollCycleWithMinContractStartFrom).contractStartFrom
        val maxContractStartTo =
            (payrollCycles.maxByOrNull { it.contractStartTo } ?: payrollCycles[0]).contractStartTo

        val contracts =
            contractServiceAdapter.getContractsBy(
                ContractFilters(
                    type = ContractOuterClass.ContractType.EMPLOYEE,
                    status = ContractOuterClass.ContractStatus.ONBOARDING,
                    startDateRange =
                        DateRange(
                            from = minContractStartFromOfPrevMonthCycle.atStartOfDay(),
                            to = maxContractStartTo.atTime(LocalTime.MAX)),
                    companyIds = companyIds,
                ))

        log.info("Found {} contracts before filtering", contracts.size)

        val contractIds = contracts.map { it.id }
        val compensationByContractId =
            compensationServiceAdapter.getCurrentCompensationByContractIds(contractIds.toSet())

        return addEligibleContractIdsToPayrollCycles(
            payrollCycles, contracts, compensationByContractId, sendingDate)
    }

    private fun addEligibleContractIdsToPayrollCycles(
        payrollCycles: List<PayrollCycleContracts>,
        contracts: List<Contract>,
        compensationByContractId: Map<Long, Compensation>,
        sendingDate: LocalDate
    ): List<PayrollCycleContracts> {
        val pickedUpContractIds = mutableSetOf<Long>()
        val payrollCycleMap = mutableMapOf<PayrollCycleContracts, List<Long>>()

        for (payrollCycle in payrollCycles) {
            val eligibleContractIds =
                contracts
                    .filter { contract ->
                        !pickedUpContractIds.contains(contract.id) &&
                            doesPayDateMatch(
                                compensationByContractId[contract.id],
                                CountryCode.valueOf(contract.country),
                                payrollCycle.payFrequency,
                                payrollCycle.payDay) &&
                            isDateInInclusiveRange(
                                contract.startOn.toLocalDate(),
                                payrollCycle.contractStartFrom,
                                payrollCycle.contractStartTo)
                    }
                    .map { it.id }

            if (eligibleContractIds.isNotEmpty()) {
                payrollCycleMap[payrollCycle] = eligibleContractIds
                pickedUpContractIds.addAll(eligibleContractIds)
            }
        }

        addEligibleContractIdsOfPrevMonthToPayrollCycles(
            payrollCycles,
            contracts,
            compensationByContractId,
            sendingDate,
            pickedUpContractIds,
            payrollCycleMap)

        return payrollCycles.mapNotNull { cycle ->
            payrollCycleMap[cycle]?.let { contractIds -> PayrollCycleContracts(cycle, contractIds) }
        }
    }

    private fun addEligibleContractIdsOfPrevMonthToPayrollCycles(
        payrollCycles: List<PayrollCycleContracts>,
        contracts: List<Contract>,
        compensationByContractId: Map<Long, Compensation>,
        sendingDate: LocalDate,
        pickedUpContractIds: MutableSet<Long>,
        payrollCycleMap: MutableMap<PayrollCycleContracts, List<Long>>
    ) {

        val remainContracts = contracts.filter { !pickedUpContractIds.contains(it.id) }

        val shouldFollowUpContracts: List<Contract> = filterShouldFollowUpContracts(remainContracts)
        for (payrollCycle in payrollCycles) {
            if (!isCurrentlyActive(sendingDate, payrollCycle)) continue
            val prevMonthCycle = getPrevMonthCycle(payrollCycle)

            val eligibleContractIds =
                shouldFollowUpContracts
                    .filter { contract ->
                        !pickedUpContractIds.contains(contract.id) &&
                            doesPayDateMatch(
                                compensationByContractId[contract.id],
                                CountryCode.valueOf(contract.country),
                                payrollCycle.payFrequency,
                                payrollCycle.payDay) &&
                            isDateInInclusiveRange(
                                contract.startOn.toLocalDate(),
                                prevMonthCycle.contractStartFrom,
                                payrollCycle.contractStartTo)
                    }
                    .map { it.id }

            if (eligibleContractIds.isNotEmpty()) {
                val existingContractIds = payrollCycleMap[payrollCycle] ?: emptyList()
                payrollCycleMap[payrollCycle] = existingContractIds + eligibleContractIds
                pickedUpContractIds.addAll(eligibleContractIds)
            }
        }
    }

    private fun isCurrentlyActive(
        sendingDate: LocalDate,
        payrollCycle: PayrollCycleContracts
    ): Boolean {
        return sendingDate.isAfter(getPrevCycle(payrollCycle).cutoffDate) &&
            !sendingDate.isAfter(payrollCycle.cutoffDate)
    }

    private fun getPayrollCyclesForCutoffDate(cutoffDate: LocalDate): List<PayrollCycleContracts> =
        listOfNotNull(
            getMonthlyPayrollCycleForCutoffDate(cutoffDate),
            getSemiMonthly15thPayrollCycleForCutoffDate(cutoffDate),
            getSemiMonthly30thPayrollCycleForCutoffDate(cutoffDate),
            getSemiMonthly10thPayrollCycleForCutoffDate(cutoffDate),
            getSemiMonthly25thPayrollCycleForCutoffDate(cutoffDate),
            getSemiMonthly14thPayrollCycleForCutoffDate(cutoffDate),
            getSemiMonthly28thPayrollCycleForCutoffDate(cutoffDate))

    private fun isDateInInclusiveRange(date: LocalDate?, from: LocalDate, to: LocalDate): Boolean =
        if (date == null) {
            false
        } else !date.isBefore(from) && !date.isAfter(to)

    private fun doesPayDateMatch(
        compensation: Compensation?,
        countryCode: CountryCode,
        payFrequency: PayFrequency?,
        payDay: Int
    ): Boolean {
        if (compensation?.postProbationBasePay == null ||
            compensation.postProbationBasePay.payFrequency == null) {
            return false
        }

        val basePayFrequency = PayFrequency.from(compensation.postProbationBasePay.payFrequency)
        val paymentFrequencyDates =
            getPayFrequencyDates(compensation, countryCode, basePayFrequency)

        return (basePayFrequency === payFrequency &&
            paymentFrequencyDates.any { it.dateOfMonth == payDay })
    }

    private fun filterShouldFollowUpContracts(contracts: List<Contract>): List<Contract> {
        val contractIds = contracts.map { it.id }

        val depositPaidContractIds = fetchDepositPaidContractIds(contractIds)

        val depositUnpaidContracts = contracts.filter { !depositPaidContractIds.contains(it.id) }

        val msaSignedContractIds = fetchMSASignedContractIds(depositUnpaidContracts)
        val msaSignedAndEmployeeSignedContractIds =
            fetchEmployeeSignedContracts(msaSignedContractIds)

        val shouldFollowUpContractId =
            (depositPaidContractIds + msaSignedAndEmployeeSignedContractIds).toSet()

        return contracts.filter { shouldFollowUpContractId.contains(it.id) }
    }

    private fun fetchDepositPaidContractIds(contractIds: List<Long>): Set<Long> {
        return contractServiceAdapter.getDepositPaidContractIds(contractIds.toSet())
    }

    private fun fetchMSASignedContractIds(contracts: List<Contract>): List<Long> {
        val companyIds = contracts.map { it.companyId }

        val msaSignedCompanyIds = companyServiceAdapter.getMSASignedCompanyIds(companyIds.toSet())

        return contracts.filter { msaSignedCompanyIds.contains(it.companyId) }.map { it.id }
    }

    private fun fetchEmployeeSignedContracts(contractIds: Collection<Long>): List<Long> {
        return onboardingServiceAdapter
            .getAllOnboardingsByContractIds(contractIds.toSet(), COMPANY_EXP)
            .entries
            .filter { it.value.status.isAfter(OnboardingStatus.SIGNATURE_EMPLOYEE_SIGNED) }
            .map { it.key }
    }

    companion object {
        private val MONTHLY_PAY_DATE_DEFAULT: PayFrequencyDate =
            PayFrequencyDate.newBuilder().dateOfMonth(25).build()
        private val FIRST_PAYOUT_DATE_DEFAULT: PayFrequencyDate =
            PayFrequencyDate.newBuilder().dateOfMonth(15).build()
        private val SECOND_PAYOUT_DATE_DEFAULT: PayFrequencyDate =
            PayFrequencyDate.newBuilder().dateOfMonth(30).build()
        private val MONTHLY_PAY_DATE_CONFIG: Map<CountryCode, List<PayFrequencyDate>> =
            java.util.Map.ofEntries()
        private val SEMI_MONTHLY_PAY_DATE_CONFIG: Map<CountryCode, List<PayFrequencyDate>> =
            java.util.Map.ofEntries(
                java.util.Map.entry(
                    CountryCode.PHL,
                    java.util.List.of(
                        PayFrequencyDate.newBuilder().dateOfMonth(10).build(),
                        PayFrequencyDate.newBuilder().dateOfMonth(25).build())),
                java.util.Map.entry(
                    CountryCode.USA,
                    java.util.List.of(
                        PayFrequencyDate.newBuilder().dateOfMonth(14).build(),
                        PayFrequencyDate.newBuilder().dateOfMonth(28).build())))
        private const val COMPANY_EXP = "company"

        private fun getPayFrequencyDates(
            compensation: Compensation,
            countryCode: CountryCode,
            basePayFrequency: PayFrequency?
        ): List<PayFrequencyDate> {
            val paymentFrequencyDates = compensation.postProbationBasePay.paymentFrequencyDatesList

            if (paymentFrequencyDates.isNotEmpty()) {
                return paymentFrequencyDates.map {
                    PayFrequencyDate.newBuilder()
                        .dateOfMonth(it.dateOfMonth)
                        .dayOfWeek(it.dayOfWeekValue.toDayOfWeek())
                        .identifier(PayFrequencyDateIdentifier.valueOf(it.identifier))
                        .build()
                }
            }

            return when (basePayFrequency) {
                PayFrequency.MONTHLY -> MONTHLY_PAY_DATE_CONFIG[countryCode]
                        ?: listOf(MONTHLY_PAY_DATE_DEFAULT)
                else -> SEMI_MONTHLY_PAY_DATE_CONFIG[countryCode]
                        ?: listOf(FIRST_PAYOUT_DATE_DEFAULT, SECOND_PAYOUT_DATE_DEFAULT)
            }
        }
    }
}

private fun Int.toDayOfWeek(): DayOfWeek =
    when (this) {
        0 -> DayOfWeek.SUNDAY
        1 -> DayOfWeek.MONDAY
        2 -> DayOfWeek.TUESDAY
        3 -> DayOfWeek.WEDNESDAY
        4 -> DayOfWeek.THURSDAY
        5 -> DayOfWeek.FRIDAY
        6 -> DayOfWeek.SATURDAY
        else -> DayOfWeek.MONDAY
    }
