package com.multiplier.contract.onboarding.config

import com.multiplier.common.DateScalar
import com.multiplier.common.DateTimeScalar
import com.multiplier.common.IdScalar
import com.multiplier.common.TimeScalar
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsRuntimeWiring
import graphql.scalars.id.UUIDScalar
import graphql.scalars.`object`.JsonScalar
import graphql.schema.GraphQLScalarType
import graphql.schema.idl.RuntimeWiring

@DgsComponent
class RuntimeScalarWiring {

    private val dateTimeScalar =
        GraphQLScalarType.newScalar()
            .name("DateTime")
            .description("DateTime Type")
            .coercing(DateTimeScalar())
            .build()

    private val dateScalar =
        GraphQLScalarType.newScalar()
            .name("Date")
            .description("Date Type")
            .coercing(DateScalar())
            .build()

    private val timeScalar =
        GraphQLScalarType.newScalar()
            .name("Time")
            .description("Time Type")
            .coercing(TimeScalar())
            .build()

    private val idScalar =
        GraphQLScalarType.newScalar().name("ID").description("ID type").coercing(IdScalar()).build()

    @DgsRuntimeWiring
    fun addScalar(builder: RuntimeWiring.Builder): RuntimeWiring.Builder {
        return builder
            .scalar(dateTimeScalar)
            .scalar(dateScalar)
            .scalar(timeScalar)
            .scalar(idScalar)
            .scalar(UUIDScalar.INSTANCE)
            .scalar(JsonScalar.INSTANCE)
    }
}
