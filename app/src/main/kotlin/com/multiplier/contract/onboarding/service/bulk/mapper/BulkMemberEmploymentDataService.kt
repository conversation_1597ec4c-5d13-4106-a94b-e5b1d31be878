package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberEmploymentServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.EmployerDetail
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerEndDateSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerPositionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastEmployerStartDateSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.UpsertPreviousEmployerDetailsInput
import org.springframework.stereotype.Service

@Service
class BulkMemberEmploymentDataService(
    private val memberServiceAdapter: MemberServiceAdapter,
    private val bulkMemberEmploymentServiceAdapter: BulkMemberEmploymentServiceAdapter,
) {

    fun validate(
        dataForMemberValidation: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): List<GrpcValidationResult<UpsertPreviousEmployerDetailsInput>> =
        bulkMemberEmploymentServiceAdapter.validateUpsertPreviousEmployerDetails(
            dataForMemberValidation, options)

    fun upsertPreviousEmployerDetails(
        inputs: List<ValidInput<UpsertPreviousEmployerDetailsInput>>,
        memberContext: MemberContext,
        options: BulkOnboardingOptions,
    ): List<String> {
        val upsertEmployerInputs =
            inputs
                .filterNot { it.input == UpsertPreviousEmployerDetailsInput.getDefaultInstance() }
                .map {
                    CreationInput(
                        requestId = it.validationId,
                        memberId = memberContext.getMemberId(it.validationId),
                        data = it.input)
                }

        val results =
            bulkUpsert(
                "Upsert previous employer details",
                upsertEmployerInputs,
                options,
                CreationInput.refMemberId) { employerInputs, _ ->
                    bulkMemberEmploymentServiceAdapter.upsertPreviousEmployerDetails(employerInputs)
                }

        return results.flatMap { it.errors }
    }

    fun getDataForSpecs(dataSpecs: List<DataSpec>, memberIds: Set<Long>): MemberDataContext {
        val employmentDetails = memberServiceAdapter.getMemberLastEmployerDetails(memberIds)
        val memberIdToEmployeeDataChunk =
            employmentDetails.associate { it.memberId to it.toEmployeeDataChunk(dataSpecs) }
        return MemberDataContext(memberIdToEmployeeDataChunk)
    }
}

private fun EmployerDetail.toEmployeeDataChunk(dataSpecs: List<DataSpec>): EmployeeDataChunk {
    val data =
        dataSpecs
            .associate {
                it.key to
                    when (it.key) {
                        LastEmployerNameSpec.key -> this.lastEmployerName
                        LastEmployerPositionSpec.key -> this.lastEmployerPosition
                        LastEmployerStartDateSpec.key -> this.lastEmployerStartDate.toString()
                        LastEmployerEndDateSpec.key -> this.lastEmployerEndDate.toString()
                        else -> null
                    }
            }
            .filterValues { it != null }
            .mapValues { requireNotNull(it.value) }
    return EmployeeDataChunk(data)
}
