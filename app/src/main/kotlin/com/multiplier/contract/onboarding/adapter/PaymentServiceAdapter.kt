package com.multiplier.contract.onboarding.adapter

import com.multiplier.common.exception.toSystemException
import com.multiplier.contract.onboarding.domain.model.PaymentAccountDataField
import com.multiplier.contract.onboarding.domain.model.PaymentAccountRequirement
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.types.CurrencyCode
import com.multiplier.contract.schema.payable.ContractPayable
import com.multiplier.payse.schema.common.AccountType
import com.multiplier.payse.schema.common.PaymentDirection
import com.multiplier.payse.schema.common.PaymentPartner
import com.multiplier.payse.schema.common.TransferType
import com.multiplier.payse.schema.grpc.paymentaccount.PaymentAccountRequirementsRequest
import com.multiplier.payse.schema.grpc.paymentaccount.PaymentAccountRequirementsServiceGrpc
import io.grpc.StatusRuntimeException
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface PaymentServiceAdapter {

    fun getPaymentAccountRequirementForMember(
        paymentAccountRequirementsRequest: ContractPayable.PaymentAccountRequirementsRequest
    ): PaymentAccountRequirement?
}

@Service
class PaymentServiceAdapterImpl : PaymentServiceAdapter {

    private val log = KotlinLogging.logger {}

    @GrpcClient("pay-se")
    private lateinit var blockingStub:
        PaymentAccountRequirementsServiceGrpc.PaymentAccountRequirementsServiceBlockingStub

    override fun getPaymentAccountRequirementForMember(
        paymentAccountRequirementsRequest: ContractPayable.PaymentAccountRequirementsRequest
    ): PaymentAccountRequirement? {
        val request =
            PaymentAccountRequirementsRequest.newBuilder()
                .setAccountType(AccountType.valueOf(paymentAccountRequirementsRequest.accountType))
                .setTransferType(
                    TransferType.valueOf(paymentAccountRequirementsRequest.transferType))
                .setCountryCode(paymentAccountRequirementsRequest.countryCode)
                .setSourceCurrency(paymentAccountRequirementsRequest.sourceCurrency)
                .setTargetCurrency(paymentAccountRequirementsRequest.targetCurrency)
                .setPaymentDirection(
                    PaymentDirection.valueOf(paymentAccountRequirementsRequest.paymentDirection))
                .addAllPaymentPartners(
                    paymentAccountRequirementsRequest.paymentPartnersList.map {
                        PaymentPartner.valueOf(it)
                    })
                .build()

        return try {
            val requirementsList =
                blockingStub.getPaymentAccountRequirements(request).requirementsList
            if (requirementsList.size > 1) {
                log.warn {
                    "More than one payment account requirements for member found, request: $paymentAccountRequirementsRequest"
                }
            }
            requirementsList.firstOrNull()?.toDomain()
        } catch (e: StatusRuntimeException) {
            return when {
                e.status.description?.contains("PAYMENT_ACCOUNT_REQUIREMENTS_NOT_FOUND") == true ||
                    e.status.description?.contains("PAYMENT_REQUIREMENT_CONFIG_NOT_FOUND") ==
                        true ->
                    null.also {
                        log.info {
                            "Payment account requirements not found for member, return null. Query response: ${e.status.description}"
                        }
                    }
                else ->
                    throw ErrorCodes.PAYMENT_ACCOUNT_ERROR.toSystemException(
                            message =
                                "Unable to to get payment account requirements due to internal error",
                            cause = e,
                        )
                        .also {
                            log.warn(e) {
                                "Unable to to get payment account requirements due to internal error"
                            }
                        }
            }
        }
    }
}

private fun com.multiplier.payse.schema.grpc.paymentaccount.PaymentAccountRequirement.toDomain() =
    PaymentAccountRequirement(
        accountType = this.accountType.name,
        transferType = this.transferType.name,
        requirementType = this.paymentAccountRequirementType,
        sourceCurrency = CurrencyCode.valueOf(this.sourceCurrency),
        targetCurrency = CurrencyCode.valueOf(this.targetCurrency),
        requiredDataFields =
            this.accountRequirementsList.map {
                PaymentAccountDataField(
                    it.key,
                    it.label,
                    it.type,
                    it.isMandatory,
                    it.valuesAllowedList.mapNotNull { item -> item.value })
            })
