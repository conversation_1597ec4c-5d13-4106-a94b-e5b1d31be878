package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.schema.contract.BulkContract
import com.multiplier.contract.schema.contract.BulkContractServiceGrpc
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkComplianceServiceAdapter {

    @GrpcClient("contract-service")
    private lateinit var bulkService: BulkContractServiceGrpc.BulkContractServiceBlockingStub

    fun validateUpdateComplianceInputs(
        inputs: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<BulkContract.UpdateComplianceInput>> {
        val response =
            bulkService.validateUpdateComplianceInputs(
                BulkContract.ValidateUpdateComplianceInputsRequest.newBuilder()
                    .addAllInputs(inputs.map { it.toGrpc() })
                    .setContext(options.context.name)
                    .build())
        return response.resultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
            )
        }
    }

    fun updateCompliances(
        inputs: List<CreationInput<BulkContract.UpdateComplianceInput>>,
        options: BulkOnboardingOptions
    ): List<CreationResult> {
        if (inputs.isEmpty()) return emptyList()

        try {
            val results = bulkService.updateCompliances(inputs.toGrpc(options)).resultsList
            if (results.any { it.success }) {
                val requestIdToContractId = inputs.associate { it.requestId to it.contractId }
                log.info {
                    "Compliance updated for contracts: ${results.mapNotNull { requestIdToContractId[it.requestId] }}"
                }
            }
            return results.map {
                CreationResult(
                    requestId = it.requestId, success = it.success, errors = it.errorsList)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to update compliance for bulk onboarding" }
            return inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Update compliance failed due to an internal error: unknown exception occurred")
            }
        }
    }
}

private fun EmployeeData.toGrpc(): BulkContract.UpdateComplianceValidationInput? {
    return BulkContract.UpdateComplianceValidationInput.newBuilder()
        .setRequestId(this.identification.validationId)
        .putAllProperties(this.data)
        .build()
}

private fun List<CreationInput<BulkContract.UpdateComplianceInput>>.toGrpc(
    options: BulkOnboardingOptions
): BulkContract.UpdateCompliancesRequest {

    return BulkContract.UpdateCompliancesRequest.newBuilder()
        .addAllInputs(
            this.map {
                it.data
                    .toBuilder()
                    .setRequestId(it.requestId)
                    .setContractId(
                        requireNotNull(it.contractId) {
                            "Contract ID for compliance update must not be null"
                        })
                    .build()
            })
        .setContext(options.context.name)
        .build()
}
