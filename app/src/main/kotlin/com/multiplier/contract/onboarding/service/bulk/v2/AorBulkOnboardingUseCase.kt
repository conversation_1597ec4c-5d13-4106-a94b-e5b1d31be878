package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.cache.CountryCache
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.CountrySpec
import com.multiplier.contract.onboarding.service.bulk.v2.module.*
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractTerm
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.onboarding.usecase.ModuleParams
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class AorBulkOnboardingUseCase(
    private val bulkContractModule: BulkContractModule,
    private val bulkMemberModule: BulkMemberModule,
    private val bulkContractOnboardingModule: BulkContractOnboardingModule,
    private val bulkComplianceModule: BulkComplianceModule,
    private val bulkCompensationModule: BulkCompensationModuleV2,
    private val bulkPostOnboardingTriggerModule: BulkPostOnboardingTriggerModule,
    private val countryCache: CountryCache
) : BulkOnboardingUseCase() {
    private val log = KotlinLogging.logger {}

    private val allowedCustomParams =
        setOf(
            "complianceConsent.aor.workingPracticesMatchAorAgreement",
            "complianceConsent.aor.workerCharacterizationSupported")

    private val aorKeyToHelpText =
        mapOf(
            "phoneNumber" to "Phone number of the contractor",
            "address.line1" to "Street address of the contractor",
            "address.city" to "City of residence of the contractor",
            "address.state" to "State/province of residence of the contractor",
            "address.country" to "Country of residence of the contractor",
        )

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        return super.getDataSpecs(onboardingOptions, moduleParams).map {
            it.copy(group = EmployeeData.AOR_DETAILS_GROUP, description = addHelpText(it))
        }
    }

    fun addHelpText(dataSpec: DataSpec): String? {
        return aorKeyToHelpText[dataSpec.keyWithPrefix()] ?: dataSpec.description
    }

    override fun getBulkOnboardingModules(): List<BulkDataModule> {
        return listOf(
            bulkContractModule,
            bulkMemberModule,
            bulkComplianceModule,
            bulkCompensationModule,
            bulkContractOnboardingModule,
            bulkPostOnboardingTriggerModule,
        )
    }

    override fun validate(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): BulkValidationResultV2 {
        val enrichedEmployeeData = enrichEmployeeData(employeeDataInput)
        val fixedEmployeeDataInput = disableUpsertCapability(enrichedEmployeeData)
        val bulkValidationResults = super.validate(fixedEmployeeDataInput, options)
        return addGroupToValidationResults(bulkValidationResults)
    }

    private fun addGroupToValidationResults(bulkValidationResult: BulkValidationResultV2) =
        bulkValidationResult.copy(
            validationResults =
                bulkValidationResult.validationResults.mapValues {
                    it.value.map { validationResult ->
                        validationResult.copy(groupName = EmployeeData.AOR_DETAILS_GROUP)
                    }
                })

    private fun disableUpsertCapability(employeeDataInput: EmployeeDataInput) =
        employeeDataInput.copy(
            employeeData =
                employeeDataInput.employeeData.map {
                    // by removing contractId, input data will be treated as data for new contract
                    it.copy(data = it.data - "contractId")
                })

    private fun enrichEmployeeData(employeeDataInput: EmployeeDataInput): EmployeeDataInput {
        // we allow only some specific custom params to be validated and saved.
        // This is just a filtering layer to make sure only necessary params are being forwarded.
        val filteredCustomParams =
            employeeDataInput.customParams
                .filterKeys { it in allowedCustomParams }
                .filterValues { it.isNotBlank() || it != null }

        return employeeDataInput.copy(
            employeeData =
                employeeDataInput.employeeData.map { it ->
                    val countryName = it.data[CountrySpec.key]
                    val countryCode = countryName?.let { countryCache.getCountryCode(it) }?.name

                    val addressCountryName = it.data["address.country"]
                    val addressCountryCode =
                        addressCountryName?.let { countryCache.getCountryCode(it) }?.name

                    // these values need to be added since they are required in the validate
                    // endpoint
                    it.copy(
                        data =
                            it.data
                                .plus("type" to ContractType.CONTRACTOR.name)
                                .plus("term" to ContractTerm.FIXED.name)
                                // replacing country name with country code
                                .plus(CountrySpec.key to (countryCode ?: countryName ?: ""))
                                .plus(
                                    "address.country" to
                                        (addressCountryCode ?: addressCountryName ?: ""))
                                .plus(filteredCustomParams))
                })
    }
}
