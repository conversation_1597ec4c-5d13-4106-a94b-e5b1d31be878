package com.multiplier.contract.onboarding.service.notifications

import com.multiplier.contract.onboarding.config.objectMapper
import com.multiplier.contract.onboarding.service.reminders.Notification
import com.multiplier.pigeonservice.PigeonNotificationClient
import com.multiplier.pigeonservice.dto.PigeonEmailNotificationDTO
import com.multiplier.pigeonservice.schema.kafka.EmailNotificationBody
import org.springframework.stereotype.Service

@Service
class NotificationService(
    private val pigeonNotificationClient: PigeonNotificationClient,
) {

    fun send(notification: Notification) {
        val dto =
            PigeonEmailNotificationDTO(
                emailNotificationBody =
                    EmailNotificationBody.newBuilder()
                        .setTemplateType(notification.type.name)
                        .setSubject(notification.subject)
                        .setFrom(notification.from)
                        .setTo(notification.to)
                        .addAllCc(notification.cc)
                        .putAllContentVariables(notification.data.convertValuesToString())
                        .build(),
                metadata = emptyMap(),
                attachments = emptyList(),
            )

        pigeonNotificationClient.send(dto)
    }
}

private fun Map<String, Any>.convertValuesToString(): Map<String, String> =
    this.entries.associate { it.key to it.value.toJsonIfObject() }

private fun Any.toJsonIfObject(): String =
    when (this) {
        is String -> this
        else -> objectMapper.writeValueAsString(this)
    }
