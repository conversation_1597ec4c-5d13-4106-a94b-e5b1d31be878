package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.common.exception.toBusinessException
import com.multiplier.company.schema.grpc.CompanyOuterClass.LegalEntity
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.adapter.ContractFilters
import com.multiplier.contract.onboarding.adapter.bulk.BulkContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.contants.OnboardingExperience
import com.multiplier.contract.onboarding.domain.isAfter
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.CountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmployeeIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EndOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.JobDescriptionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.LegalEntityIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.MemberIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.PositionSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StartOnSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StateOfEmploymentSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.WorkEmailSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.WorkStatusSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.service.toLocalDate
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

data class ContractContext(val requestIdToContractId: Map<String, Long>) {
    fun getContractId(id: String?): Long? = requestIdToContractId[id]
    fun containsRequestId(id: String) = requestIdToContractId.containsKey(id)

    val contractIds
        get() = requestIdToContractId.values

    companion object {
        val EMPTY = ContractContext(emptyMap())
    }
}

data class ContractDataContext(val contractIdToContractData: Map<Long, EmployeeDataChunk>) {

    companion object {
        val EMPTY = ContractDataContext(emptyMap())
    }

    val contractIds
        get() = contractIdToContractData.keys

    fun isEmpty() = contractIdToContractData.isEmpty()
}

// temporarily set max contract count to 500, will follow up with proper pagination in FND-2423
const val MAX_CONTRACTS_FOR_TEMPLATE_GENERATION = 500

@Service
class BulkContractDataService(
    private val bulkContractServiceAdapter: BulkContractServiceAdapter,
    private val contractServiceAdapter: ContractServiceAdapter,
    private val companyServiceAdapter: CompanyServiceAdapter,
    private val countryServiceAdapter: CountryServiceAdapter,
    private val onboardingServiceAdapter: OnboardingServiceAdapter,
) {

    companion object {
        val RowIdentifierSpec =
            DataSpec(
                key = "rowIdentifier",
                type = DataSpecType.TEXT,
                description = "Unique row identifier for employee data across upload sheets",
                label = "Row Identifier")
        val CompanyIdSpec = DataSpec(key = "companyId", type = DataSpecType.NUMBER)
        val ContractTypeSpec =
            DataSpec(
                key = "type",
                type = DataSpecType.SELECT,
                allowedValues = ContractType.values().map { it.name })
        val EmployeeIdSpec =
            DataSpec(
                key = "employeeId",
                type = DataSpecType.TEXT,
                description = "Unique identifier for an employee")
        val PositionSpec =
            DataSpec(
                key = "position",
                type = DataSpecType.TEXT,
                description = "Job title of the employee")
        val JobDescriptionSpec =
            DataSpec(
                key = "scope",
                type = DataSpecType.TEXT,
                mandatory = false,
                label = "Job Description")
        val StartOnSpec =
            DataSpec(
                key = "startOn",
                type = DataSpecType.DATE,
                description = "Date of joining of the employee in YYYY-MM-DD format")
        val ContractIdSpec =
            DataSpec(key = "contractId", type = DataSpecType.NUMBER, mandatory = false)
        val LegalEntityIdSpec = DataSpec(key = "legalEntityId", type = DataSpecType.SELECT)
        val MemberIdSpec = DataSpec(key = "memberId", type = DataSpecType.NUMBER)
        val EndOnSpec = DataSpec(key = "endOn", type = DataSpecType.DATE, mandatory = false)
        val WorkEmailSpec = DataSpec(key = "workEmail", type = DataSpecType.TEXT, mandatory = false)
        val CountrySpec = DataSpec(key = "country", type = DataSpecType.TEXT)
        val StateOfEmploymentSpec =
            DataSpec(
                key = "state", type = DataSpecType.SELECT, label = "Country State of Employment")
        val ScopeSpec = DataSpec(key = "scope", type = DataSpecType.TEXT)
        val WorkStatusSpec =
            DataSpec(
                key = "workStatus",
                type = DataSpecType.SELECT,
                mandatory = false,
                allowedValues =
                    listOf(CountryWorkStatus.RESIDENT.name, CountryWorkStatus.REQUIRES_VISA.name))
        val EmploymentTermSpec =
            DataSpec(key = "term", type = DataSpecType.TEXT, label = "Employment Term")
        val ContractStatusSpec = DataSpec(key = "status", type = DataSpecType.SELECT)
    }

    fun getDataSpecs(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.HRIS_PROFILE_DATA ->
                contractDataSpecsForHrisProfile(onboardingOptions)
            BulkOnboardingContext.EOR -> contractDataSpecsForEOR(onboardingOptions)
            BulkOnboardingContext.AOR -> contractDataSpecsForAOR(onboardingOptions)
            BulkOnboardingContext.FREELANCER -> contractDataSpecsForFreelancer(onboardingOptions)
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Contract data specs query for ${onboardingOptions.context} not supported yet",
                    context = mapOf("context" to onboardingOptions.context))
        }
    }

    private fun contractDataSpecsForEOR(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        return listOfNotNull(
            ContractIdSpec,
            CountrySpec,
            this.getCountryStateDataSpec(onboardingOptions.countryCode),
            WorkStatusSpec,
            PositionSpec,
            JobDescriptionSpec,
            StartOnSpec,
            EndOnSpec)
    }

    private fun contractDataSpecsForAOR(onboardingOptions: BulkOnboardingOptions): List<DataSpec> =
        listOf(
            EmployeeIdSpec,
            PositionSpec,
            StartOnSpec,
            EndOnSpec.copy(mandatory = true),
            ScopeSpec.copy(label = "Job Description")) +
            listOfNotNull(getCountryStateDataSpec(onboardingOptions.countryCode))

    private fun contractDataSpecsForFreelancer(
        onboardingOptions: BulkOnboardingOptions
    ): List<DataSpec> =
        listOf(
            PositionSpec,
            StartOnSpec,
            EndOnSpec,
            ScopeSpec.copy(label = "Job Description", mandatory = false)) +
            listOfNotNull(getCountryStateDataSpec(onboardingOptions.countryCode))

    private fun contractDataSpecsForHrisProfile(
        onboardingOptions: BulkOnboardingOptions
    ): List<DataSpec> {
        val legalEntities =
            companyServiceAdapter.getCompanyLegalEntities(onboardingOptions.companyId).map {
                "${it.id} - ${it.legalName}"
            }

        return listOf(
            EmployeeIdSpec,
            ContractIdSpec,
            LegalEntityIdSpec.copy(allowedValues = legalEntities),
            StartOnSpec,
            EndOnSpec,
            PositionSpec,
            WorkEmailSpec)
    }

    fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): List<GrpcValidationResult<ContractOuterClass.CreateContractInput>> {
        return bulkContractServiceAdapter.validateContractCreateInputs(employeeData, options)
    }

    fun createContracts(
        inputs: List<ValidInput<ContractOuterClass.CreateContractInput>>,
        memberContext: MemberContext,
        options: BulkOnboardingOptions,
    ): ContractContext {
        val upsertContractInputs =
            inputs.map {
                CreationInput(
                    requestId = it.validationId,
                    memberId = memberContext.getMemberId(it.validationId),
                    data = it.input)
            }

        val results =
            bulkUpsert(
                "Upsert contract", upsertContractInputs, options, CreationInput.refMemberId) {
                    contractInputs,
                    onboardingOptions ->
                    bulkContractServiceAdapter.createContracts(contractInputs, onboardingOptions)
                }

        return ContractContext(
            results.filter { it.success }.associate { it.requestId to it.upsertedIds.first() })
    }

    fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?,
    ): ContractDataContext {
        val contractList =
            when (options.context) {
                BulkOnboardingContext.HRIS_PROFILE_DATA -> contractDataForHRIS(options, pageRequest)
                BulkOnboardingContext.EOR -> contractDataForEOR(options, pageRequest)
                else -> emptyList()
            }

        if (contractList.isEmpty()) {
            return ContractDataContext(emptyMap())
        }

        log.info { "Extracting data from ${contractList.size} contracts" }
        val legalEntities = companyServiceAdapter.getCompanyLegalEntities(options.companyId)
        val contractIdToContractData =
            contractList.associate { it.id to it.toEmployeeDataChunk(dataSpecs, legalEntities) }
        return ContractDataContext(contractIdToContractData)
    }

    private fun contractDataForEOR(
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?
    ): List<Contract> {
        val contractList =
            contractServiceAdapter.getContractsBy(
                ContractFilters(
                    companyIds = listOf(options.companyId),
                    type = ContractOuterClass.ContractType.EMPLOYEE,
                    countryCode = options.countryCode.name,
                    status = ContractOuterClass.ContractStatus.ONBOARDING,
                    pageRequest = pageRequest.limitContractSize(),
                ))

        // Only return contracts with OnboardingStep from ONBOARDING_REVIEW backward
        val contractIdList = contractList.map { it.id }
        val onboardingByContractId =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contractIdList, OnboardingExperience.COMPANY.value)
        val allowedUpdateContractIds =
            onboardingByContractId.values
                .filter { !it.currentStep.isAfter(OnboardingStep.ONBOARDING_SIGNING) }
                .map { it.contractId }
                .toSet()

        return contractList.filter { it.id in allowedUpdateContractIds }
    }

    private fun contractDataForHRIS(
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?
    ): List<Contract> {
        return contractServiceAdapter.getContractsBy(
            ContractFilters(
                companyIds = listOf(options.companyId),
                types =
                    listOf(
                        ContractOuterClass.ContractType.HR_MEMBER,
                        ContractOuterClass.ContractType.EMPLOYEE),
                statuses =
                    listOf(
                        ContractOuterClass.ContractStatus.ONBOARDING,
                        ContractOuterClass.ContractStatus.ACTIVE),
                pageRequest = pageRequest.limitContractSize(),
            ))
    }

    private fun getCountryStateDataSpec(countryCode: CountryCode): DataSpec? {
        val countryStates = getCountryStates(countryCode)

        return if (countryStates.isNotEmpty())
            StateOfEmploymentSpec.copy(allowedValues = countryStates)
        else null
    }

    private fun getCountryStates(countryCode: CountryCode): List<String> {
        val states =
            countryServiceAdapter
                .getCountryStates(setOf(countryCode))
                .filterKeys { it == countryCode }
                .values
                .firstOrNull()

        return states?.map { it.name }?.sorted() ?: emptyList()
    }
}

fun Contract.toEmployeeDataChunk(
    dataSpecs: List<DataSpec>,
    legalEntities: List<LegalEntity>
): EmployeeDataChunk {
    val legalEntity = legalEntities.find { it.id == this.legalEntityId }
    val data =
        dataSpecs
            .associate { spec ->
                spec.key to
                    when (spec.key) {
                        EmployeeIdSpec.key -> this.employeeId
                        StartOnSpec.key -> this.startOn.toLocalDate().toString()
                        EndOnSpec.key ->
                            if (this.hasEndOn()) this.endOn.toLocalDate().toString() else null
                        ContractIdSpec.key -> this.id.toString()
                        LegalEntityIdSpec.key -> legalEntity?.let { "${it.id} - ${it.legalName}" }
                        MemberIdSpec.key -> this.memberId.toString()
                        PositionSpec.key -> this.position
                        JobDescriptionSpec.key -> this.scope
                        WorkEmailSpec.key -> this.workEmail
                        CountrySpec.key -> this.country
                        StateOfEmploymentSpec.key -> this.countryStateCode
                        WorkStatusSpec.key -> this.workStatus.name
                        else -> null
                    }
            }
            .filterValues { it != null }
            .mapValues { requireNotNull(it.value) }

    return EmployeeDataChunk(data)
}

fun PageRequest?.limitContractSize(): PageRequest {
    if (this == null || this.pageSize > MAX_CONTRACTS_FOR_TEMPLATE_GENERATION) {
        log.warn {
            "No pagination or Too many contracts requested for template generation, limiting to $MAX_CONTRACTS_FOR_TEMPLATE_GENERATION first contracts"
        }
        return PageRequest.newBuilder()
            .pageNumber(0)
            .pageSize(MAX_CONTRACTS_FOR_TEMPLATE_GENERATION)
            .build()
    }

    return this
}
