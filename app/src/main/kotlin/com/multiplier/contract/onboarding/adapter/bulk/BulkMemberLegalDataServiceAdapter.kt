package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.*
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkMemberLegalDataServiceAdapter {

    @GrpcClient("member-service")
    private lateinit var bulkMemberService: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    fun validateUpdateLegalDataInputs(
        employeeData: Collection<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<UpdateMemberLegalDataInput>> {

        val results =
            bulkMemberService.validateUpdateLegalDataInputs(
                ValidateUpdateLegalDataRequest.newBuilder()
                    .addAllInputs(employeeData.map { it.toGrpc() })
                    .setContext(options.context.name)
                    .build())

        return results.validationResultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
            )
        }
    }

    fun updateLegalData(
        inputs: List<CreationInput<UpdateMemberLegalDataInput>>,
        options: BulkOnboardingOptions
    ): List<CreationResult> {
        if (inputs.isEmpty()) return emptyList()

        try {
            val results =
                bulkMemberService.updateMemberLegalData(inputs.toGrpc(options)).resultsList
            return results.map {
                CreationResult(
                    requestId = it.requestId, success = it.success, errors = it.errorsList)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to update legal data for bulk onboarding" }
            return inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Update legal data failed due to an internal error: unknown exception occurred")
            }
        }
    }

    private fun List<CreationInput<UpdateMemberLegalDataInput>>.toGrpc(
        options: BulkOnboardingOptions
    ): UpdateMemberLegalDataRequest {
        return UpdateMemberLegalDataRequest.newBuilder()
            .addAllInputs(
                this.map {
                    it.data
                        .toBuilder()
                        .setRequestId(it.requestId)
                        .setMemberId(
                            requireNotNull(it.memberId) {
                                "Member ID for legal data update must not be null"
                            })
                        .build()
                })
            .setContext(options.context.name)
            .build()
    }

    private fun EmployeeData.toGrpc(): UpdateLegalDataValidationInput {

        return UpdateLegalDataValidationInput.newBuilder()
            .setRequestId(this.identification.validationId)
            .putAllProperties(this.data)
            .build()
    }
}
