package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.repository.JpaOnboardingAudRepository
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import org.springframework.stereotype.Service

interface OnboardingAudServiceAdapter {

    fun getAllContractIdsOfOnboardingAuditsWithExperienceAndStatus(
        contractIds: Set<Long>,
        experience: String,
        contractOnboardingStatus: ContractOnboardingStatus
    ): Set<Long>
}

@Service
class OnboardingAudServiceAdapterImpl(
    private val jpaOnboardingAudRepository: JpaOnboardingAudRepository
) : OnboardingAudServiceAdapter {

    override fun getAllContractIdsOfOnboardingAuditsWithExperienceAndStatus(
        contractIds: Set<Long>,
        experience: String,
        contractOnboardingStatus: ContractOnboardingStatus
    ): Set<Long> {
        return jpaOnboardingAudRepository
            .findAllContractIdByContractIdInAndExperienceAndStatus(
                contractIds, experience, contractOnboardingStatus)
            .toHashSet()
    }
}
