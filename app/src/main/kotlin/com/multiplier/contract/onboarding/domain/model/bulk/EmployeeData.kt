package com.multiplier.contract.onboarding.domain.model.bulk

data class EmployeeData(
    val identification: EmployeeIdentification,
    val data: Map<String, String>,
    val group: String = EMPLOYMENT_DATA_GROUP
) {

    companion object {
        const val EMPLOYMENT_DATA_GROUP = "EMPLOYMENT_DATA"
        const val MEMBER_DATA_GROUP = "MEMBER_DATA"
        const val COMPENSATION_DATA_GROUP = "COMPENSATION_DATA"

        const val BASIC_DETAILS_GROUP = "BASIC_DETAILS"
        const val COMPLIANCE_AND_LEAVES_DATA_GROUP = "COMPLIANCE_AND_LEAVES_DATA"
        const val EOR_COMPENSATION_DATA_GROUP = "COMPENSATION_DATA"

        const val CONTRACTOR_DETAILS_GROUP = "CONTRACTOR_DETAILS"
        const val AOR_DETAILS_GROUP = "AOR_DETAILS"
    }

    operator fun get(spec: DataSpec): String = data[spec.key] ?: ""
}

data class EmployeeDataChunk(val data: Map<String, String>) {
    companion object {
        val EMPTY = EmployeeDataChunk(emptyMap())
    }
}

fun List<EmployeeData>.contractData() =
    this.filter { it.group == EmployeeData.EMPLOYMENT_DATA_GROUP }.ifEmpty { this }

fun List<EmployeeData>.contractAndMemberData(): List<EmployeeData> {
    val memberData = this.filter { it.group == EmployeeData.MEMBER_DATA_GROUP }
    return EmployeeDataHelper.enrichEmployeeData(
        this.contractData(), memberData, EmployeeDataHelper.defaultEmployeeDataMergeStrategy)
}

fun List<EmployeeData>.memberData(): List<EmployeeData> {
    val memberData = this.filter { it.group == EmployeeData.MEMBER_DATA_GROUP }
    return if (memberData.isEmpty()) this.contractData()
    else
        EmployeeDataHelper.enrichEmployeeData(
            memberData, this.contractData(), EmployeeDataHelper.defaultEmployeeDataMergeStrategy)
}

fun List<EmployeeData>.complianceData(): List<EmployeeData> {
    return this.filter { it.group == EmployeeData.COMPLIANCE_AND_LEAVES_DATA_GROUP }
        .ifEmpty { this }
}

fun List<EmployeeData>.basicDetails(): List<EmployeeData> {
    return this.filter { it.group == EmployeeData.BASIC_DETAILS_GROUP }.ifEmpty { this }
}
