package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberLegalDataServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.mapper.toDataSpec
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.DomainType
import com.multiplier.contract.onboarding.types.FetchStage
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.member.schema.UpdateMemberLegalDataInput
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkMemberLegalDataModule(
    private val countryServiceAdapter: CountryServiceAdapter,
    private val bulkMemberLegalDataAdapter: BulkMemberLegalDataServiceAdapter
) : BulkDataModule {

    private val log = KotlinLogging.logger {}

    companion object {
        const val MODULE_NAME = "MEMBER_LEGAL_DATA_MODULE"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        return try {
            val definitions =
                countryServiceAdapter.getMemberLegalDataDefinitions(
                    onboardingOptions.countryCode, onboardingOptions.contractType)

            val filteredDefinitions =
                if (onboardingOptions.contractType == ContractType.EMPLOYEE) {
                    // add filter for EOR
                    // filtering out the MEMBER_DATA domain type since they will be taken as input
                    // in member experience
                    definitions.filter {
                        it.fetchStage == FetchStage.CONTRACT_GENERATION &&
                            it.domainType == DomainType.LEGAL_DATA
                    }
                } else {
                    definitions
                }

            filteredDefinitions.distinctBy { it.key }.map { it.toDataSpec() }
        } catch (e: Exception) {
            log.warn(e) { "Failed to get member legal data spec for options: $onboardingOptions" }
            emptyList()
        }
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        return bulkMemberLegalDataAdapter.validateUpdateLegalDataInputs(employeeData, options)
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        val upsertMemberLegalDataInputs =
            validationResults.map {
                CreationInput(
                    requestId = it.validationId,
                    memberId = bulkCreationResult.getMemberId(it.validationId),
                    data = it.input as UpdateMemberLegalDataInput)
            }

        return bulkUpsert(
            "Upsert legal data",
            upsertMemberLegalDataInputs,
            options,
            bulkCreationResult,
            CreationInput.refMemberId) { inputs, onboardingOptions ->
                bulkMemberLegalDataAdapter.updateLegalData(inputs, onboardingOptions)
            }
    }
}
