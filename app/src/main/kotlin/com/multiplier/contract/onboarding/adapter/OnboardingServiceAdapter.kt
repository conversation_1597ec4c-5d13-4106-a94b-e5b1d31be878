package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.contants.ContractRevokedExperience
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.repository.JpaOnboardingRepository
import com.multiplier.contract.onboarding.repository.model.JpaOnboarding
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.ContractOnboardingStep
import org.springframework.stereotype.Service

interface OnboardingServiceAdapter {

    fun getAllByIds(onboardingIds: Set<Long>): List<Onboarding>

    fun getAllOnboardingsByContractIds(
        contractIds: Collection<Long>,
        experience: String
    ): Map<Long, Onboarding>

    fun getAllOnboardingsByContractIdsAndStatus(
        contractIds: Collection<Long>,
        status: ContractOnboardingStatus,
        experience: String
    ): Map<Long, Onboarding>

    fun upsertOnboardings(onboardings: List<Onboarding>)
}

@Service
class OnboardingServiceAdapterImpl(
    private val jpaOnboardingRepository: JpaOnboardingRepository,
) : OnboardingServiceAdapter {
    override fun getAllByIds(onboardingIds: Set<Long>): List<Onboarding> {
        return jpaOnboardingRepository.findAllById(onboardingIds).map { it.toDomain() }
    }

    override fun getAllOnboardingsByContractIds(
        contractIds: Collection<Long>,
        experience: String
    ): Map<Long, Onboarding> {
        return jpaOnboardingRepository
            .findByContractIdInAndExperience(contractIds.toSet(), experience)
            .associate { it.contractId to it.toDomain() }
    }

    override fun getAllOnboardingsByContractIdsAndStatus(
        contractIds: Collection<Long>,
        status: ContractOnboardingStatus,
        experience: String
    ): Map<Long, Onboarding> {
        return jpaOnboardingRepository
            .findByContractIdInAndStatusAndExperience(contractIds.toSet(), status, experience)
            .associate { it.contractId to it.toDomain() }
    }

    override fun upsertOnboardings(onboardings: List<Onboarding>) {
        jpaOnboardingRepository.saveAll(onboardings.map { it.toEntity() })
    }
}

fun Onboarding.toEntity(): JpaOnboarding =
    JpaOnboarding(
        id = this.id ?: 0,
        contractId = this.contractId,
        experience = this.experience,
        status = ContractOnboardingStatus.valueOf(this.status.name),
        revokedBy = ContractRevokedExperience.NONE,
        currentStep = this.currentStep,
        isBulkOnboarded = this.isBulkOnboarded ?: false)

fun JpaOnboarding.toDomain() =
    Onboarding(
        id = this.id,
        contractId = this.contractId,
        experience = this.experience,
        status = this.status.toDomain(),
        currentStep = this.currentStep,
        revokedBy = this.revokedBy)

fun ContractOnboardingStatus.toDomain() = OnboardingStatus.valueOf(this.name)

fun ContractOnboardingStep.toDomain() = OnboardingStep.valueOf(this.name)
