package com.multiplier.contract.onboarding.repository.model

import com.multiplier.contract.onboarding.repository.AuditableBaseEntityWithId
import com.multiplier.contract.schema.onboarding.Onboarding.ContractOnboardingStatus
import com.multiplier.contract.schema.onboarding.Onboarding.ContractOnboardingStep
import jakarta.persistence.*
import org.hibernate.envers.RevisionType

@Entity
@Table(name = "onboarding_aud", schema = "contract")
@IdClass(JpaOnboardingAudId::class)
class JpaOnboardingAud(
    id: Long,
    @Id var rev: Int,
    @Column(name = "revtype") var revtype: RevisionType,
    @Column(name = "contract_id") var contractId: Long,
    var experience: String? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: ContractOnboardingStatus? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "current_step")
    var currentStep: ContractOnboardingStep? = null,
) : AuditableBaseEntityWithId(id)
