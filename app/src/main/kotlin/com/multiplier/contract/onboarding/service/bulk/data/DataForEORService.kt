package com.multiplier.contract.onboarding.service.bulk.data

import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.dataSpec.DataSpecForEORService
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.MemberIdSpec
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.PageRequest
import java.util.*
import org.springframework.stereotype.Service

@Service
class DataForEORService(
    private val contractDataService: BulkContractDataService,
    private val memberDataService: BulkMemberDataService,
    private val complianceDataService: BulkComplianceDataService,
    private val bulkCompensationDataService: BulkCompensationDataService,
    private val bulkInsuranceDataService: BulkInsuranceDataService,
    private val addressDataService: BulkMemberAddressDataService,
) : BulkDataServiceInterface {
    override fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?,
    ): List<EmployeeDataChunk> {
        val contractDataContext =
            contractDataService.getDataForSpecs(dataSpecs + MemberIdSpec, options, pageRequest)
        val contractIdList = contractDataContext.contractIds
        val contractIdToMemberIdMap =
            contractDataContext.contractIdToContractData.mapValues {
                it.value.data.getValue(MemberIdSpec.key).toLong()
            }

        val memberIdList = contractIdToMemberIdMap.values.toSet()
        val memberDataContext = memberDataService.getDataForSpecs(dataSpecs, memberIdList)
        val memberAddressDataContext = addressDataService.getDataForSpecs(dataSpecs, memberIdList)

        val complianceDataContext = complianceDataService.getDataForSpecs(dataSpecs, contractIdList)
        val compensationDataContext =
            bulkCompensationDataService.getDataForSpecs(dataSpecs, contractIdList)

        val insuranceDataContext =
            bulkInsuranceDataService.getDataForSpecs(dataSpecs, contractIdList)

        val employeeDataChunk =
            contractDataContext.contractIdToContractData.mapNotNull {
                (contractId, contractDataChunk) ->
                val memberId = contractIdToMemberIdMap.getValue(contractId)
                val memberData =
                    memberDataContext.memberIdToMemberData[memberId]?.data ?: emptyMap()
                val memberAddressData =
                    memberAddressDataContext.memberIdToMemberData[memberId]?.data ?: emptyMap()
                val complianceData =
                    complianceDataContext.contractIdToContractData[contractId]?.data ?: emptyMap()
                val compensationData =
                    compensationDataContext.contractIdToCompensationMap[contractId]?.data
                        ?: emptyMap()
                val insuranceData =
                    insuranceDataContext.contractIdToInsuranceMap[contractId]?.data ?: emptyMap()

                EmployeeDataChunk(
                    contractDataChunk.data +
                        complianceData +
                        memberData +
                        memberAddressData +
                        compensationData +
                        insuranceData)
            }

        return autoFillRowIdentifier(employeeDataChunk, dataSpecs)
    }

    private fun autoFillRowIdentifier(
        employeeDataChunk: List<EmployeeDataChunk>,
        dataSpecs: List<DataSpec>
    ): List<EmployeeDataChunk> {
        if (employeeDataChunk.isEmpty()) {
            return emptyList()
        }

        val shouldGenerateRowIdentifier =
            dataSpecs.any { it == DataSpecForEORService.RowIdentifierSpec }
        return if (shouldGenerateRowIdentifier) {
            employeeDataChunk.mapIndexed { index, employeeData ->
                employeeData.copy(
                    data = employeeData.data + mapOf("rowIdentifier" to "EMPLOYEE_${index + 1}"))
            }
        } else employeeDataChunk
    }
}
