package com.multiplier.contract.onboarding.exception

import com.multiplier.common.exception.ErrorCode

enum class ErrorCodes(override val message: String) : ErrorCode {
    ONBOARDING_SPECIALIST_NOT_FOUND("No onboarding specialist found"),
    ONBOARDING_ENTITY_NOT_FOUND("No onboarding entity found"),
    EXPERIENCE_NOT_FOUND("Experience not found"),
    MEMBER_NOT_FOUND("Member not found"),
    CONTRACT_NOT_FOUND("Contact not found"),
    COMPANY_NOT_FOUND("Company not found"),
    NOTIFICATION_TYPE_INVALID("Notification type is invalid"),
    UNSUPPORTED_OPERATION("Operation not supported"),
    UNSUPPORTED_INPUT("Input not supported"),
    VALIDATION_FAILED("Validation failed"),
    LEGAL_ENTITY_NOT_FOUND("Legal entity not found"),
    COMPANY_HAS_NO_DEPARTMENTS("Company has no departments"),
    USER_NOT_FOUND("User not found"),
    PAYMENT_ACCOUNT_ERROR("Payment account error"),
    NOTIFICATION_SENDING_FAILED("Failed to send notification"),
    INVALID_COUNTRY("Invalid country code"),
    ERROR_FINDING_COMPANY_USER("Error finding company user"),
}
