package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.DgsConstants
import com.multiplier.contract.onboarding.service.OnboardingOpsService
import com.multiplier.contract.onboarding.types.ContractOnboardingUpdateStepInput
import com.multiplier.contract.onboarding.types.TaskResponse
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument

@DgsComponent
class OnboardingMutator(
    private val onboardingOpsService: OnboardingOpsService,
) {
    @DgsMutation(field = DgsConstants.MUTATION.ContractOnboardingUpdateStep)
    fun updateContractOnboardingStep(
        @InputArgument input: ContractOnboardingUpdateStepInput
    ): TaskResponse {
        return try {
            onboardingOpsService.updateContractOnboardingStep(input)
        } catch (ex: Exception) {
            TaskResponse.newBuilder()
                .success(false)
                .message("Error occurred: ${ex.message}")
                .build()
        }
    }
}
