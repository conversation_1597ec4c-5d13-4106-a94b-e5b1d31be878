package com.multiplier.contract.onboarding.usecase

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData

data class EmployeeDataInput(
    val employeeData: List<EmployeeData>,
    val templateData: EmployeeTemplateData? = null,
    val source: String = "Unknown",
    val customParams: Map<String, String> = emptyMap(),
) {
    companion object {
        val EMPTY = EmployeeDataInput(emptyList())
    }
}

data class EmployeeTemplateData(
    val countryCode: String?,
    val templateVersion: String?,
    val reportTemplate: ByteArray?
)
