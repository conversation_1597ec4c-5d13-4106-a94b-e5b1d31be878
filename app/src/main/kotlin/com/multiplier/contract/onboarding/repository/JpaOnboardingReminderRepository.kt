package com.multiplier.contract.onboarding.repository

import com.multiplier.contract.onboarding.repository.model.JpaOnboardingReminder
import com.multiplier.contract.onboarding.types.NotificationType
import java.util.Optional
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository

@Repository
interface JpaOnboardingReminderRepository : CrudRepository<JpaOnboardingReminder, Long> {
    fun findByContractIdAndTemplateName(
        contractId: Long,
        templateName: NotificationType
    ): Optional<JpaOnboardingReminder>
}
