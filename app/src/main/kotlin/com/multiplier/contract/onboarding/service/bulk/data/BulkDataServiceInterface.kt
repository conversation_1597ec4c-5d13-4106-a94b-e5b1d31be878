package com.multiplier.contract.onboarding.service.bulk.data

import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.PageRequest

interface BulkDataServiceInterface {

    fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?,
    ): List<EmployeeDataChunk>
}
