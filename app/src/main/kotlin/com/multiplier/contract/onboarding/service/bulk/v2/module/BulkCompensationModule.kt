package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.CompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkCompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkPayrollServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.country.CompensationStandard
import com.multiplier.contract.onboarding.domain.model.country.CountryState
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.BasePaySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.CurrencySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.PayrollFrequencySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.RateFrequencySpec
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModule.Companion.GP_ALLOWANCE_SPEC
import com.multiplier.contract.onboarding.service.toCamelCase
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.PayFrequency
import com.multiplier.contract.onboarding.types.RateFrequency
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationOuterClass.CreateCompensationInput
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkCompensationModule(
    private val countryServiceAdapter: CountryServiceAdapter,
    private val payrollServiceAdapter: BulkPayrollServiceAdapter,
    private val compensationServiceAdapter: CompensationServiceAdapter,
    private val bulkCompensationServiceAdapter: BulkCompensationServiceAdapter,
) : BulkDataModule {

    private val log = KotlinLogging.logger {}

    companion object {
        const val MODULE_NAME = "COMPENSATION_MODULE"

        const val GP_ALLOWANCE_SPEC = "GP_ALLOWANCE_SPEC"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        val compensationStandards = getCompensationStandards(onboardingOptions.countryCode)
        log.info {
            "Building GP data specs for ${onboardingOptions.countryCode}," +
                " compensation standards: $compensationStandards"
        }

        return listOf(
            PayrollFrequencySpec.copy(
                allowedValues =
                    getSupportedPayrollFrequencies(onboardingOptions, compensationStandards)),
            RateFrequencySpec.copy(
                allowedValues =
                    getSupportedRateFrequencies(onboardingOptions, compensationStandards)),
            BasePaySpec) + getAllowancesDataSpecs(onboardingOptions.countryCode)
    }

    private fun getCompensationStandards(
        countryCode: CountryCode
    ): Collection<CompensationStandard> {
        val countryStates = countryServiceAdapter.getCountryStates(setOf(countryCode))[countryCode]
        val countryStateSet =
            if (countryStates.isNullOrEmpty()) setOf(CountryState(countryCode, ""))
            else countryStates.toSet()

        return countryServiceAdapter.getCompensationStandards(countryStateSet).values
    }

    private fun getSupportedRateFrequencies(
        onboardingOptions: BulkOnboardingOptions,
        compensationStandards: Collection<CompensationStandard>
    ): List<String> {
        val customRateFrequencies =
            if (onboardingOptions.context == BulkOnboardingContext.GLOBAL_PAYROLL &&
                onboardingOptions.countryCode == CountryCode.TWN)
                listOf(RateFrequency.HOURLY.name)
            else emptyList()
        val rateFrequenciesFromLabourStandard =
            compensationStandards
                .flatMap { it.rateFrequencies }
                .distinct()
                .filterNot { it == RateFrequency.HOURLY }
                .map { it.name }

        return (customRateFrequencies + rateFrequenciesFromLabourStandard).distinct()
    }

    private fun getSupportedPayrollFrequencies(
        onboardingOptions: BulkOnboardingOptions,
        compensationStandards: Collection<CompensationStandard>,
    ): List<String> {
        val customFrequencies =
            if (onboardingOptions.context == BulkOnboardingContext.GLOBAL_PAYROLL &&
                onboardingOptions.countryCode == CountryCode.CAN)
                listOf(PayFrequency.WEEKLY.name, PayFrequency.BIWEEKLY.name)
            else emptyList()
        val standardFrequencies =
            compensationStandards.flatMap { it.payrollFrequencies }.distinct().map { it.name }
        return (standardFrequencies + customFrequencies).distinct()
    }

    private fun getAllowancesDataSpecs(countryCode: CountryCode): List<DataSpec> {
        return payrollServiceAdapter.getAllowanceStructure(countryCode).map {
            DataSpec(
                key = it.name,
                type = DataSpecType.NUMBER,
                label = it.label,
                mandatory = it.mandatory,
                source = GP_ALLOWANCE_SPEC)
        }
    }

    override fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        contractIdToMemberId: Map<Long, Long>
    ): DataForContract {
        return DataForContract(
            compensationServiceAdapter
                .getCurrentCompensationByContractIds(contractIdToMemberId.keys)
                .mapValues { it.value.toEmployeeDataChunk(dataSpecs) })
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        return bulkCompensationServiceAdapter.validateCompensationCreateInputs(
            employeeData.filter { it.group != EmployeeData.COMPENSATION_DATA_GROUP }, options)
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        val upsertCompensationInputs =
            validationResults.map {
                CreationInput(
                    requestId = it.validationId,
                    contractId = bulkCreationResult.getContractId(it.validationId),
                    data = it.input as CreateCompensationInput)
            }
        return bulkUpsert(
            "Upsert compensation",
            upsertCompensationInputs,
            options,
            bulkCreationResult,
            CreationInput.refContractId,
        ) { inputs, onboardingOptions ->
            bulkCompensationServiceAdapter.createCompensations(inputs, onboardingOptions)
        }
    }
}

private fun CompensationOuterClass.Compensation.toEmployeeDataChunk(
    dataSpecs: List<DataSpec>
): EmployeeDataChunk {
    val gpAllowanceKeys = dataSpecs.filter { it.source == GP_ALLOWANCE_SPEC }.map { it.key }
    val dataMap =
        dataSpecs
            .associate {
                it.key to
                    when (it.key) {
                        CurrencySpec.key -> basePay.currency
                        BasePaySpec.key -> basePay.amount.toLong().toString()
                        RateFrequencySpec.key -> basePay.frequency.name
                        PayrollFrequencySpec.key ->
                            basePay.payFrequency.name.removePrefix("PAY_FREQUENCY_")
                        in gpAllowanceKeys ->
                            additionalPaysList
                                .find { pay -> pay.label.toCamelCase() == it.key }
                                ?.amount
                                ?.toLong()
                                ?.toString()
                                ?: ""
                        else -> ""
                    }
            }
            .filterValues { it.isNotBlank() }

    return EmployeeDataChunk(dataMap)
}
