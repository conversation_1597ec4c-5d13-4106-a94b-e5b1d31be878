package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.schema.compensationsource.*
import com.multiplier.contract.schema.compensationsource.CompensationSourceServiceGrpc.CompensationSourceServiceBlockingStub
import com.multiplier.contract.schema.contract.ContractOuterClass
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface CompensationSourceServiceAdapter {
    fun isNewCompensationSourceEnabledForEntityId(entityId: Long): Boolean
    fun isNewCompensationSourceEnabledForCountries(
        contractType: ContractType,
        countryCodes: List<CountryCode>
    ): Map<CountryCode, Boolean>
}

@Service
class CompensationSourceServiceAdapterImpl : CompensationSourceServiceAdapter {

    @GrpcClient("contract-service") private lateinit var stub: CompensationSourceServiceBlockingStub

    override fun isNewCompensationSourceEnabledForEntityId(entityId: Long): Boolean {
        val request = compensationSourceByEntityRequest {
            request.add(
                entityRequest {
                    this.entityId = entityId
                    entityType = CompensationSourceOuterClass.EntityType.COMPANY_ENTITY
                })
        }
        val response = stub.getCompensationSourceByEntityIds(request)
        return response.sourceByEntityIdMap.getOrDefault(entityId, null) ==
            CompensationSourceOuterClass.CompensationSource.COMPENSATION_SERVICE
    }

    override fun isNewCompensationSourceEnabledForCountries(
        contractType: ContractType,
        countryCodes: List<CountryCode>
    ): Map<CountryCode, Boolean> {
        val grpcContractType = ContractOuterClass.ContractType.valueOf(contractType.name)

        val request = compensationSourceByCountriesRequest {
            countryRequest +=
                countryCodes.map { countryCode ->
                    compensationSourceByCountryKey {
                        this.contractType = grpcContractType
                        this.country =
                            com.multiplier.country.schema.Country.GrpcCountryCode.valueOf(
                                countryCode.name)
                    }
                }
        }
        val response = stub.getCompensationSourceByCountries(request)

        return response.sourceByCountryList.associate { entry ->
            val grpcCountry = entry.key.country
            val isEnabled =
                entry.value == CompensationSourceOuterClass.CompensationSource.COMPENSATION_SERVICE
            CountryCode.valueOf(grpcCountry.name) to isEnabled
        }
    }
}
