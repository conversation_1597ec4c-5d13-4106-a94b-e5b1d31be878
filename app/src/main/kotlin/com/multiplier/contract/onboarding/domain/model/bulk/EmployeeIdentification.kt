package com.multiplier.contract.onboarding.domain.model.bulk

data class EmployeeIdentification(
    val employeeId: String? = null,
    val contractId: Long? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val email: String? = null,
    val rowIdentifier: String? = null,
    val rowIdentifierFromSheet: String? = null,
    val rowNumber: Int
) {

    // validation id is used to uniquely identify each employee input in bulk validation pipeline
    val validationId = generateValidationId()

    private fun generateValidationId(): String {
        return when {
            contractId.isValidId() -> "cId_${contractId}_rn_$rowNumber"
            rowIdentifier.isValidString() -> "ri_${rowIdentifier}_rn_$rowNumber"
            employeeId.isValidString() -> "eId_${employeeId}_rn_$rowNumber"
            rowNumber > 0 -> "rn_$rowNumber"
            else -> "hc_${this.hashCode()}"
        }
    }

    private fun Long?.isValidId() = this != null && this > 0

    private fun String?.isValidString() = !this.isNullOrBlank()

    val fullName: String = "$firstName $lastName"
}
