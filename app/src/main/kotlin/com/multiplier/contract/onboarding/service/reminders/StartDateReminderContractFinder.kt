package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.adapter.ContractFilters
import com.multiplier.contract.onboarding.adapter.DateRange
import com.multiplier.contract.onboarding.service.toLocalDate
import com.multiplier.contract.schema.contract.ContractOuterClass.*
import java.time.LocalDate
import java.time.LocalTime
import java.util.*
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class StartDateReminderContractFinder(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val reminderContractFinderHelper: ReminderContractFinderHelper,
) {

    fun findBy(sendingDate: LocalDate, companyIds: Set<Long>): List<PayrollCycleContracts> {
        val contracts = findEligibleContractsForStartDateReminder(sendingDate, companyIds)

        if (contracts.isEmpty()) {
            return emptyList()
        }

        log.info(
            "Before filtering, found {} monthly payroll contracts which will start in 3 or 7 days from {}",
            contracts.size,
            sendingDate)

        val contractById = contracts.associateBy { it.id }
        val payrollCycleContractsList =
            reminderContractFinderHelper
                .parkContractsIntoPayrollCycles(contracts, sendingDate)
                .map { filterContractsEligibleForStartDateReminder(contractById, sendingDate, it) }
                .filter { it.contractIds.isNotEmpty() }

        if (payrollCycleContractsList.isEmpty()) {
            return emptyList()
        }

        val mergedPayrollCycleContractList =
            mergePayrollCycleWhoseCutoffDateHasPassed(sendingDate, payrollCycleContractsList)

        log.info(
            "Found {} contracts which will start in 3 or 7 days from {}, parked in {} payroll cycles, ",
            mergedPayrollCycleContractList.flatMap { it.contractIds }.size,
            sendingDate,
            mergedPayrollCycleContractList.size)

        return mergedPayrollCycleContractList
    }

    private fun findEligibleContractsForStartDateReminder(
        sendingDate: LocalDate,
        companyIds: Set<Long>
    ): List<Contract> {
        return getContractsStartInFutureDates(
                listOf(sendingDate.plusDays(3), sendingDate.plusDays(7)), companyIds)
            .let(reminderContractFinderHelper::filterEligibleContracts)
            .let(reminderContractFinderHelper::filterMonthlyPayrollContracts)
    }

    private fun getContractsStartInFutureDates(
        startDates: List<LocalDate>,
        companyIds: Set<Long>
    ): List<Contract> {
        val minStartDate = startDates.minOf { it }
        val maxStartDate = startDates.maxOf { it }

        return contractServiceAdapter
            .getContractsBy(
                filters =
                    ContractFilters(
                        type = ContractType.EMPLOYEE,
                        status = ContractStatus.ONBOARDING,
                        startDateRange =
                            DateRange(
                                from = minStartDate.atStartOfDay(),
                                to = maxStartDate.atTime(LocalTime.MAX)),
                        companyIds = companyIds),
            )
            .filter { it.startOn != null && startDates.contains(it.startOn.toLocalDate()) }
    }

    private fun filterContractsEligibleForStartDateReminder(
        contractById: Map<Long, Contract>,
        sendingDate: LocalDate,
        payrollCycle: PayrollCycleContracts
    ): PayrollCycleContracts {
        val eligibleContractIds =
            payrollCycle.contractIds.filter { contractId ->
                val contract = contractById.getValue(contractId)

                (contractStartBeforeOrOnPayrollCutoffDate(payrollCycle, contract) &&
                    payrollCutoffReminderIsNotBeingSend(sendingDate, payrollCycle)) ||
                    (contractIsCreatedAfterPayrollCutoffDate(payrollCycle, contract) &&
                        sendingDate.isAfter(payrollCycle.cutoffDate))
            }

        return payrollCycle.copy(contractIds = eligibleContractIds)
    }

    private fun mergePayrollCycleWhoseCutoffDateHasPassed(
        sendingDate: LocalDate,
        payrollCycleContractsList: List<PayrollCycleContracts>
    ): List<PayrollCycleContracts> {
        val mergedPayrollCycleContractList: LinkedList<PayrollCycleContracts> =
            LinkedList<PayrollCycleContracts>()
        if (payrollCycleContractsList.size > 1) {
            var i = 0
            while (i < payrollCycleContractsList.size - 1) {
                val payrollCycleContracts: PayrollCycleContracts = payrollCycleContractsList[i]
                if (sendingDate.isAfter(payrollCycleContracts.cutoffDate)) {
                    val nextPayrollCycleContracts = payrollCycleContractsList[++i]

                    val mergedContractIds =
                        payrollCycleContracts.contractIds + nextPayrollCycleContracts.contractIds

                    val mergedPayrollCycleContracts =
                        PayrollCycleContracts(nextPayrollCycleContracts, mergedContractIds)
                    mergedPayrollCycleContractList.add(mergedPayrollCycleContracts)
                } else {
                    mergedPayrollCycleContractList.add(payrollCycleContracts)
                }
                i++
            }
        } else {
            val payrollCycleContracts: PayrollCycleContracts = payrollCycleContractsList[0]
            if (sendingDate.isAfter(payrollCycleContracts.cutoffDate)) {
                val nextMonthPayrollCycle =
                    PayrollCycleHelper.getMonthlyPayrollCycleForMonth(
                        payrollCycleContracts.payrollMonth.plusMonths(1))
                mergedPayrollCycleContractList.add(
                    PayrollCycleContracts(nextMonthPayrollCycle, payrollCycleContracts.contractIds))
            } else {
                mergedPayrollCycleContractList.add(payrollCycleContracts)
            }
        }
        return mergedPayrollCycleContractList
    }

    private fun contractIsCreatedAfterPayrollCutoffDate(
        payrollCycle: PayrollCycleContracts,
        contract: Contract
    ): Boolean {
        return contract.createdOn.toLocalDate()?.isAfter(payrollCycle.cutoffDate) ?: false
    }

    private fun contractStartBeforeOrOnPayrollCutoffDate(
        payrollCycle: PayrollCycleContracts,
        contract: Contract
    ): Boolean {
        return !contract.startOn.toLocalDate().isAfter(payrollCycle.cutoffDate)
    }

    private fun payrollCutoffReminderIsNotBeingSend(
        date: LocalDate,
        payrollCycleContracts: PayrollCycleContracts
    ): Boolean {
        return !isDateInInclusiveRange(
            date, payrollCycleContracts.cutoffDate.minusDays(21), payrollCycleContracts.cutoffDate)
    }

    private fun isDateInInclusiveRange(date: LocalDate, from: LocalDate?, to: LocalDate?): Boolean {
        return !date.isBefore(from) && !date.isAfter(to)
    }
}
