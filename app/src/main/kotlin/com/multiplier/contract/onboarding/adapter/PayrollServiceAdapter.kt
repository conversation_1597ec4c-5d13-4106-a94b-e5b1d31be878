package com.multiplier.contract.onboarding.adapter

import com.google.type.Date
import com.multiplier.contract.onboarding.domain.model.PayrollCycleDTO
import com.multiplier.contract.onboarding.domain.model.PayrollCycleQuery
import com.multiplier.contract.onboarding.extension.toDTO
import com.multiplier.payroll.schema.Payroll
import com.multiplier.payroll.schema.PayrollServiceGrpc
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface PayrollServiceAdapter {
    fun getPayrollCyclesForContractsByReferenceDate(
        input: Set<PayrollCycleQuery>
    ): Map<PayrollCycleQuery, PayrollCycleDTO>
}

@Service
class DefaultPayrollServiceAdapter : PayrollServiceAdapter {

    @GrpcClient("payroll-service")
    private lateinit var payrollStub: PayrollServiceGrpc.PayrollServiceBlockingStub

    override fun getPayrollCyclesForContractsByReferenceDate(
        input: Set<PayrollCycleQuery>
    ): Map<PayrollCycleQuery, PayrollCycleDTO> {
        if (input.isEmpty()) return mapOf()

        val request =
            Payroll.PayrollCyclesByReferenceDateRequest.newBuilder()
                .addAllQueries(
                    input.map {
                        Payroll.PayrollDataQuery.newBuilder()
                            .setRequestId(it.requestId)
                            .setContractId(it.contractId)
                            .setReferenceDate(
                                Date.newBuilder()
                                    .setYear(it.referenceDate.year)
                                    .setMonth(it.referenceDate.monthValue)
                                    .setDay(it.referenceDate.dayOfMonth)
                                    .build())
                            .setReturnClosestToReferenceDateUpcomingCutOff(true)
                            .build()
                    })
                .build()

        val res = payrollStub.getPayrollCyclesForContractsByReferenceDate(request)

        return res.payrollCyclesMap
            .filterValues { it != Payroll.PayrollCycle.getDefaultInstance() }
            .entries
            .associate { input.first { query -> query.requestId == it.key } to it.value.toDTO() }
    }
}
