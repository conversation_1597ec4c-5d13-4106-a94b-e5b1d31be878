package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.CompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkCompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.country.CompensationStandard
import com.multiplier.contract.onboarding.domain.model.country.CountryState
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import java.util.*
import org.apache.logging.log4j.util.Strings
import org.springframework.stereotype.Service

@Service
class BulkCompensationDataService(
    private val compensationServiceAdapter: CompensationServiceAdapter,
    private val bulkCompensationServiceAdapter: BulkCompensationServiceAdapter,
    private val countryServiceAdapter: CountryServiceAdapter
) {

    companion object {
        private val ALLOWANCE_RATE_FREQUENCY =
            listOf(
                    RateFrequency.BI_WEEKLY,
                    RateFrequency.SEMIMONTHLY,
                    RateFrequency.MONTHLY,
                    RateFrequency.QUATERLY,
                    RateFrequency.TRI_ANNUALLY,
                    RateFrequency.SEMIMONTHLY,
                    RateFrequency.ANNUALLY,
                )
                .map { it.name }

        val CurrencySpec = DataSpec(key = "currency", type = DataSpecType.TEXT)
        val PayrollFrequencySpec =
            DataSpec(
                key = "payrollFrequency",
                type = DataSpecType.SELECT,
                allowedValues =
                    listOf(
                        PayFrequency.MONTHLY.name,
                        PayFrequency.SEMIMONTHLY.name,
                        PayFrequency.BIWEEKLY.name))
        val RateFrequencySpec =
            DataSpec(
                key = "rateFrequency",
                type = DataSpecType.SELECT,
                allowedValues = listOf(RateFrequency.MONTHLY.name, RateFrequency.ANNUALLY.name))
        val BasePaySpec = DataSpec(key = "basePay", type = DataSpecType.NUMBER)
        val MobileAndPhoneTypeSpec =
            DataSpec(
                key = "mobileAndPhoneAllowance.amountType",
                type = DataSpecType.TEXT,
                label = "(Mobile Phone) Pay Type",
                mandatory = false)
        val MobileAndPhoneSpec =
            DataSpec(
                key = "mobileAndPhoneAllowance.amount",
                type = DataSpecType.NUMBER,
                label = "(Mobile Phone) Amount",
                mandatory = false)
        val MobileAndPhoneStartOnSpec =
            DataSpec(
                key = "mobileAndPhoneAllowance.payOn",
                type = DataSpecType.DATE,
                label = "(Mobile Phone) Effective From",
                mandatory = false)
        val MobileAndPhoneFrequencySpec =
            DataSpec(
                key = "mobileAndPhoneAllowance.frequency",
                type = DataSpecType.SELECT,
                label = "(Mobile Phone) Frequency",
                mandatory = false,
                allowedValues = ALLOWANCE_RATE_FREQUENCY)
        val InternetTypeSpec =
            DataSpec(
                key = "internetAllowance.amountType",
                type = DataSpecType.TEXT,
                label = "(Internet) Pay Type",
                mandatory = false)
        val InternetSpec =
            DataSpec(
                key = "internetAllowance.amount",
                type = DataSpecType.NUMBER,
                label = "(Internet) Amount",
                mandatory = false)
        val InternetStartOnSpec =
            DataSpec(
                key = "internetAllowance.payOn",
                type = DataSpecType.DATE,
                label = "(Internet) Effective From",
                mandatory = false)
        val InternetFrequencySpec =
            DataSpec(
                key = "internetAllowance.frequency",
                type = DataSpecType.SELECT,
                label = "(Internet) Frequency",
                mandatory = false,
                allowedValues = ALLOWANCE_RATE_FREQUENCY)
        val OtherAllowanceTypeSpec =
            DataSpec(
                key = "otherAllowance.amountType",
                type = DataSpecType.TEXT,
                label = "(Other) Pay Type",
                mandatory = false)
        val OtherAllowanceSpec =
            DataSpec(
                key = "otherAllowance.amount",
                type = DataSpecType.NUMBER,
                label = "(Other) Amount",
                mandatory = false)
        val OtherAllowanceStartOnSpec =
            DataSpec(
                key = "otherAllowance.payOn",
                type = DataSpecType.DATE,
                label = "(Other) Effective From",
                mandatory = false)
        val OtherAllowanceFrequencySpec =
            DataSpec(
                key = "otherAllowance.frequency",
                type = DataSpecType.SELECT,
                label = "(Other) Frequency",
                mandatory = false,
                allowedValues = ALLOWANCE_RATE_FREQUENCY)
        val JoiningBonusAmountSpec =
            DataSpec(
                key = "joiningBonus.amount",
                type = DataSpecType.NUMBER,
                label = "Joining Bonus Amount",
                mandatory = false)
        val JoiningBonusPayOnSpec =
            DataSpec(
                key = "joiningBonus.payOn",
                type = DataSpecType.DATE,
                label = "Joining Bonus Payout Date",
                mandatory = false)
        val VariableBonusFrequencySpec =
            DataSpec(
                key = "variablePerformanceBonus.frequency",
                type = DataSpecType.SELECT,
                label = "Variable Bonus Frequency",
                allowedValues = ALLOWANCE_RATE_FREQUENCY,
                mandatory = false)
        val VariableBonusPayOnSpec =
            DataSpec(
                key = "variablePerformanceBonus.payOn",
                type = DataSpecType.DATE,
                label = "Variable Bonus Payout Date",
                mandatory = false)
    }

    fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        contractIdSet: Set<Long>
    ): CompensationDataContext {

        val compensationByContractIdMap =
            compensationServiceAdapter
                .getCurrentCompensationByContractIds(contractIdSet)
                .mapValues { (_, entry) -> entry.toEmployeeDataChunk(dataSpecs) }

        return CompensationDataContext(compensationByContractIdMap)
    }

    private fun Compensation.toEmployeeDataChunk(dataSpecs: List<DataSpec>): EmployeeDataChunk {
        val dataMap =
            dataSpecs
                .associate {
                    it.key to
                        when (it.key) {
                            CurrencySpec.key -> this.basePay.currency.toString()
                            BasePaySpec.key -> this.basePay.amount.toString() // GROSS SALARY
                            RateFrequencySpec.key -> this.basePay.frequency.name
                            PayrollFrequencySpec.key -> this.basePay.payFrequency.getFrequencyName()
                            else -> getValueForCompensationSpecs(this.additionalPaysList, it)
                        }
                }
                .filterValues { it.isNotBlank() }
                .mapValues { it.value }
        return EmployeeDataChunk(dataMap)
    }

    fun getDataSpecs(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.EOR -> dataSpecForEor(onboardingOptions)
            BulkOnboardingContext.AOR -> dataSpecsForAOR(onboardingOptions)
            BulkOnboardingContext.FREELANCER -> dataSpecsForFreelancer(onboardingOptions)
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Compensation data specs query for ${onboardingOptions.context} not supported yet",
                    context = mapOf("context" to onboardingOptions.context))
        }
    }

    private fun dataSpecsForAOR(onboardingOptions: BulkOnboardingOptions) =
        listOf(
            PayrollFrequencySpec.copy(
                allowedValues = listOf(PayFrequency.MONTHLY.name, PayFrequency.SEMIMONTHLY.name)),
            RateFrequencySpec.copy(
                allowedValues =
                    listOf(
                        RateFrequency.HOURLY.name,
                        RateFrequency.DAILY.name,
                        RateFrequency.SEMIMONTHLY.name,
                        RateFrequency.MONTHLY.name)),
            CurrencySpec.copy(
                type = DataSpecType.SELECT,
                allowedValues =
                    setOf(
                        countryServiceAdapter.getMainCurrency(onboardingOptions.countryCode).name,
                        "USD",
                        "EUR")),
            BasePaySpec)

    private fun dataSpecsForFreelancer(onboardingOptions: BulkOnboardingOptions) =
        listOf(
            PayrollFrequencySpec.copy(
                allowedValues = listOf(PayFrequency.MONTHLY.name, PayFrequency.SEMIMONTHLY.name)),
            RateFrequencySpec.copy(
                allowedValues =
                    listOf(
                        RateFrequency.HOURLY.name,
                        RateFrequency.DAILY.name,
                        RateFrequency.SEMIMONTHLY.name,
                        RateFrequency.MONTHLY.name)),
            CurrencySpec.copy(
                type = DataSpecType.SELECT,
                allowedValues =
                    setOf(
                        countryServiceAdapter.getMainCurrency(onboardingOptions.countryCode).name,
                        "USD",
                        "EUR")),
            BasePaySpec)

    private fun getValueForCompensationSpecs(
        compensationList: List<CompensationOuterClass.CompensationPayComponent>,
        dataSpec: DataSpec
    ): String {
        if (compensationList.isEmpty()) {
            return Strings.EMPTY
        }

        val (compensationName, key) = dataSpec.parseSpec()
        val compensationComponent = compensationList.firstOrNull { it.name == compensationName }

        if (Objects.isNull(compensationComponent)) return Strings.EMPTY
        return when (key) {
            "amount" -> compensationComponent?.amount.toString()
            "amountType" -> compensationComponent?.amountType.toString()
            "frequency" -> compensationComponent?.frequency.toString()
            "payOn" -> compensationComponent?.payOn?.toStringFormat() ?: ""
            else -> Strings.EMPTY
        }
    }

    // internetAllowance.amount -> <"internetAllowance", "amount">
    // internetAllowance.frequency -> <"internetAllowance", "frequency">
    private fun DataSpec.parseSpec(): Pair<String, String> {
        val parts = this.key.split(".")
        require(parts.size <= 2) { "Invalid key format: ${this.key}" }
        if (parts.size == 1) {
            return Pair(parts[0], "")
        }
        return Pair(parts[0], parts[1])
    }

    // PAY_FREQUENCY_WEEKLY -> WEEKLY
    // PAY_FREQUENCY_NULL -> NULL
    // ANY_STRING -> UNRECOGNIZED
    private fun CompensationOuterClass.PayFrequency.getFrequencyName(): String {
        return when (this) {
            CompensationOuterClass.PayFrequency.UNRECOGNIZED -> this.name
            CompensationOuterClass.PayFrequency.PAY_FREQUENCY_NULL -> ""
            else -> this.name.removePrefix("PAY_FREQUENCY_")
        }
    }

    private fun dataSpecForEor(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        val compensationStandards = getCompensationStandards(onboardingOptions.countryCode)
        return listOf(
            CurrencySpec,
            BasePaySpec,
            PayrollFrequencySpec.copy(
                allowedValues = getSupportedPayrollFrequencies(compensationStandards)),
            RateFrequencySpec.copy(
                allowedValues = getSupportedRateFrequencies(compensationStandards)),
            JoiningBonusAmountSpec,
            JoiningBonusPayOnSpec,
            VariableBonusFrequencySpec,
            VariableBonusPayOnSpec,
            MobileAndPhoneTypeSpec,
            MobileAndPhoneSpec,
            MobileAndPhoneFrequencySpec,
            MobileAndPhoneStartOnSpec,
            InternetTypeSpec,
            InternetSpec,
            InternetFrequencySpec,
            InternetStartOnSpec,
            OtherAllowanceTypeSpec,
            OtherAllowanceSpec,
            OtherAllowanceFrequencySpec,
            OtherAllowanceStartOnSpec,
        )
    }

    private fun getCompensationStandards(
        countryCode: CountryCode
    ): Collection<CompensationStandard> {
        val countryStates = countryServiceAdapter.getCountryStates(setOf(countryCode))[countryCode]
        val countryStateSet =
            if (countryStates.isNullOrEmpty()) setOf(CountryState(countryCode, ""))
            else countryStates.toSet()

        return countryServiceAdapter.getCompensationStandards(countryStateSet).values
    }

    private fun getSupportedRateFrequencies(
        compensationStandards: Collection<CompensationStandard>
    ) =
        compensationStandards
            .flatMap { it.rateFrequencies }
            .distinct()
            .filterNot { it == RateFrequency.HOURLY }
            .map { it.name }

    private fun getSupportedPayrollFrequencies(
        compensationStandards: Collection<CompensationStandard>
    ) = compensationStandards.flatMap { it.payrollFrequencies }.distinct().map { it.name }

    fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<CompensationOuterClass.CreateCompensationInput>> {
        return bulkCompensationServiceAdapter.validateCompensationCreateInputs(
            employeeData, options)
    }

    fun createCompensations(
        inputs: List<ValidInput<CompensationOuterClass.CreateCompensationInput>>,
        contractContext: ContractContext,
        options: BulkOnboardingOptions
    ): List<String> {
        val upsertCompensationInputs =
            inputs.map {
                CreationInput(
                    requestId = it.validationId,
                    contractId = contractContext.getContractId(it.validationId),
                    data = it.input)
            }

        val results =
            bulkUpsert(
                "Upsert compensation",
                upsertCompensationInputs,
                options,
                CreationInput.refContractId) { compensationInputs, onboardingOptions ->
                    bulkCompensationServiceAdapter.createCompensations(
                        compensationInputs, onboardingOptions)
                }

        return results.flatMap { it.errors }
    }

    private fun CompensationOuterClass.MonthYear.toStringFormat() =
        "${this.year}-${String.format("%02d", this.month)}"
}
