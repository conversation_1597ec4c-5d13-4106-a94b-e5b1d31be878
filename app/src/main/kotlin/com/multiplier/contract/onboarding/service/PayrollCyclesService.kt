package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.model.PayrollCycleDTO
import com.multiplier.contract.onboarding.domain.model.PayrollCycleQuery
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.extensions.isFreelancer
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.schema.compensation.CompensationOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import java.time.LocalDate
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class PayrollCyclesService(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val onboardingServiceAdapter: OnboardingServiceAdapter,
    private val compensationServiceAdapter: CompensationServiceAdapter,
    private val onboardingAudServiceAdapter: OnboardingAudServiceAdapter,
    private val payrollServiceAdapter: DefaultPayrollServiceAdapter,
    private val companyServiceAdapter: CompanyServiceAdapter,
    private val featureFlagService: FeatureFlagService,
    private val payrollCycleConfigService: PayrollCycleConfigService,
) {
    companion object {
        private const val COMPANY_EXP = "company"
    }

    fun getPayrollCyclesByContractId(
        contractIds: Set<Long>,
        checkingDate: LocalDate
    ): Map<Long, PayrollCycleDTO> {
        if (contractIds.isEmpty()) {
            return mapOf()
        }

        val contracts =
            contractServiceAdapter.getNonDeletedNonEndedContracts(contractIds).filter {
                !it.isFreelancer()
            }

        if (contracts.isEmpty()) {
            log.info("no contract found for ids: $contractIds")
            return mapOf()
        }

        return getPayrollCyclesForContracts(checkingDate, contracts)
    }

    fun getPayrollCyclesForContracts(
        checkingDate: LocalDate,
        contracts: List<Contract>
    ): Map<Long, PayrollCycleDTO> {
        if (contracts.isEmpty()) {
            return mapOf()
        }

        val contractsWithFF = filterContractsEnabledToUsePayrollService(contracts)
        val payrollCyclesForContracts =
            getPayrollCyclesForContractsWithFF(checkingDate, contractsWithFF)

        val payrollCyclesForContractsWithoutFF =
            getPayrollCyclesByContractIdWithoutPayrollService(
                checkingDate, contracts.filter { it.id !in payrollCyclesForContracts.keys })

        return payrollCyclesForContracts + payrollCyclesForContractsWithoutFF
    }

    fun getPayrollCyclesByOnboardingId(
        onboardingIds: Set<Long>,
        checkingDate: LocalDate
    ): Map<Long, PayrollCycleDTO> {
        val onboardingByContractId =
            onboardingServiceAdapter.getAllByIds(onboardingIds).associateBy { it.contractId }

        if (onboardingByContractId.isEmpty()) {
            log.info("no onboarding found for ids: $onboardingIds")
            return mapOf()
        }

        return getPayrollCyclesByContractId(
                onboardingByContractId.keys,
                checkingDate,
            )
            .mapKeys { requireNotNull(onboardingByContractId.getValue(it.key).id) }
    }

    private fun getPayrollCyclesForContractsWithFF(
        checkingDate: LocalDate,
        contracts: List<Contract>
    ): Map<Long, PayrollCycleDTO> {
        if (contracts.isEmpty()) return mapOf()

        val contractIds = contracts.map { it.id }.toSet()

        val compensationByContractId =
            compensationServiceAdapter
                .getCurrentCompensationByContractIds(contractIds)
                .filterValues(CompensationOuterClass.Compensation::hasPostProbationBasePay)

        val contractById =
            contracts.filter { compensationByContractId.keys.contains(it.id) }.associateBy { it.id }

        if (contractById.isEmpty()) {
            log.info("no contract with compensation found for ids: $contractIds")
            return mapOf()
        }

        val payrollCycleContracts = getPayrollCycleContracts(checkingDate, contractById)
        val cyclesNeedFollowup =
            getCyclesNeedFollowup(payrollCycleContracts, checkingDate, contractById)
        val followupCycles = getFollowUpCycles(checkingDate, cyclesNeedFollowup, contractById)

        // we use map merge to overwrite cycles found in first run with any followup cycles found
        return (payrollCycleContracts + followupCycles).associate {
            it.contract.id to it.payrollCycle
        }
    }

    private fun getPayrollCycleContracts(
        checkingDate: LocalDate,
        contractById: Map<Long, Contract>,
    ) =
        payrollServiceAdapter
            .getPayrollCyclesForContractsByReferenceDate(
                contractById.values
                    .map {
                        val startDate = it.startOn.toLocalDate()

                        val referenceDate =
                            if (it.type == ContractOuterClass.ContractType.HR_MEMBER &&
                                startDate < checkingDate)
                                checkingDate
                            else startDate

                        PayrollCycleQuery(it.id.toString(), it.id, referenceDate)
                    }
                    .toSet())
            .map { PayrollCycleContract(it.value, contractById.getValue(it.key.contractId)) }

    private fun getCyclesNeedFollowup(
        payrollCycleContracts: List<PayrollCycleContract>,
        checkingDate: LocalDate,
        contractById: Map<Long, Contract>,
    ): List<PayrollCycleContract> {
        val cyclesWithCutoffDateWithinOneMonth =
            payrollCycleContracts.filter {
                it.payrollCycle.cutoffDate < checkingDate &&
                    it.payrollCycle.cutoffDate >= checkingDate.minusMonths(1)
            }
        val shouldFollowUpContractIds =
            filterShouldFollowUpContractIds(cyclesWithCutoffDateWithinOneMonth, contractById)

        return cyclesWithCutoffDateWithinOneMonth.filter {
            shouldFollowUpContractIds.contains(it.contract.id)
        }
    }

    data class PayrollCycleContract(val payrollCycle: PayrollCycleDTO, val contract: Contract)

    private fun getFollowUpCycles(
        checkingDate: LocalDate,
        cyclesNeedFollowup: List<PayrollCycleContract>,
        contractById: Map<Long, Contract>,
    ): List<PayrollCycleContract> {
        val cutoffDateByContract =
            cyclesNeedFollowup.associate { it.contract to it.payrollCycle.cutoffDate }

        val nextCycles = getNextCycles(checkingDate, cyclesNeedFollowup, contractById)

        return nextCycles.filter {
            it.payrollCycle.cutoffDate >= checkingDate
            // Only get cycle has cutoff date within 1 month from the start date cycle's cutoff date
            &&
                it.payrollCycle.cutoffDate.minusMonths(1) <=
                    cutoffDateByContract.getValue(it.contract)
        }
    }

    private fun getNextCycles(
        checkingDate: LocalDate,
        cyclesForFindingFollowUp: List<PayrollCycleContract>,
        contractById: Map<Long, Contract>,
    ): List<PayrollCycleContract> {
        if (cyclesForFindingFollowUp.isEmpty()) {
            return emptyList()
        }

        return payrollServiceAdapter
            .getPayrollCyclesForContractsByReferenceDate(
                cyclesForFindingFollowUp
                    .map {
                        PayrollCycleQuery(it.contract.id.toString(), it.contract.id, checkingDate)
                    }
                    .toSet())
            .map { PayrollCycleContract(it.value, contractById.getValue(it.key.contractId)) }
    }

    private fun filterShouldFollowUpContractIds(
        payrollCycleContracts: List<PayrollCycleContract>,
        contractById: Map<Long, Contract>,
    ): Set<Long> {
        if (payrollCycleContracts.isEmpty()) {
            return emptySet()
        }

        val depositPaidContractIds = filterDepositPaid(contractById)

        val msaSignedAndEmployeeSignedContractIds =
            filterMsaSignedAndEmployeeSigned(
                contractById.filterKeys { it !in depositPaidContractIds })

        return depositPaidContractIds + msaSignedAndEmployeeSignedContractIds
    }

    private fun filterDepositPaid(contractById: Map<Long, Contract>) =
        contractServiceAdapter.getDepositPaidContractIds(contractById.keys)

    private fun filterMsaSignedAndEmployeeSigned(contractById: Map<Long, Contract>): Set<Long> {
        val companyIdsSignedMSA =
            companyServiceAdapter
                .getMSASignedCompanyIds(contractById.values.map { it.companyId }.toSet())
                .ifEmpty {
                    return emptySet()
                }

        val contractsIdsSignedMSA =
            contractById.filterValues { it.companyId in companyIdsSignedMSA }.keys

        val msaSignedAndEmployeeSignedContractIds =
            onboardingAudServiceAdapter.getAllContractIdsOfOnboardingAuditsWithExperienceAndStatus(
                contractsIdsSignedMSA,
                COMPANY_EXP,
                ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SIGNED)

        return msaSignedAndEmployeeSignedContractIds
    }

    private fun getPayrollCyclesByContractIdWithoutPayrollService(
        checkingDate: LocalDate,
        contractsWithoutFF: List<Contract>
    ): Map<Long, PayrollCycleDTO> =
        payrollCycleConfigService
            .getPayrollCyclesByContractId(checkingDate, contractsWithoutFF)
            .filterValues { it != null }
            .mapValues {
                val payrollCycle = requireNotNull(it.value)

                PayrollCycleDTO(
                    payFrequency =
                        PayrollCycleDTO.PayFrequency.valueOf(payrollCycle.payFrequency.name),
                    cutoffDate = payrollCycle.cutoffDate,
                    payrollMonth = payrollCycle.payrollMonth,
                    startDate = payrollCycle.contractStartFrom,
                    endDate = payrollCycle.contractStartTo,
                )
            }

    private fun filterContractsEnabledToUsePayrollService(
        contracts: List<Contract>
    ): List<Contract> {
        return contracts
            .groupBy { it.companyId }
            .filterKeys {
                featureFlagService.isFeatureOn(
                    FeatureFlags.USE_PAYROLL_SERVICE_TO_GET_PAYROLL_CYCLES,
                    mapOf(FeatureFlags.Params.COMPANY to it))
            }
            .values
            .flatten()
    }
}
