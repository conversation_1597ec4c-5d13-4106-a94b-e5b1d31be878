package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.domain.model.OnboardingTask
import com.multiplier.contract.onboarding.domain.model.convertToMapWithNameAsText
import com.multiplier.contract.onboarding.service.convertToMap

data class PendingOnboardingEmailDto(
    val contractId: Long,
    val contractOnboardingLinkForCompany: String,
    val employeeFullName: String?,
    val pendingStepCount: Int,
    val onboardingTasks: List<OnboardingTask>,
) {
    fun toPropertyMap(): Map<String, Any> =
        this.convertToMap(
            overrides =
                mapOf(
                    "onboardingTasks" to onboardingTasks.map { it.convertToMapWithNameAsText() },
                    "pendingStepCount" to pendingStepCount.toString()),
        )
}
