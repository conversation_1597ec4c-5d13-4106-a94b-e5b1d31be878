package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.excel.BulkUploadExcel
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.GenderSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.MaritalStatusSpec
import com.multiplier.contract.onboarding.types.Gender
import com.multiplier.member.schema.Member.MaritalStatus

class BulkEmployeeDataPresentation {

    companion object {

        private const val OTHER_GENDER = "OTHER"
        private const val NOT_SPECIFIED_MARITAL_STATUS = "NOT_SPECIFIED"
        private val keyToHelpText =
            mapOf(
                "race" to "Race of the employee",
                "phoneNumber" to "Phone number of the employee",
                "address.line1" to "Street address of the employee",
                "address.city" to "City of residence of the employee",
                "address.state" to "State/province of residence of the employee",
                "address.country" to "Country of residence of the employee",
                "address.postalCode" to "Postal/zip code of the residential address",
                "nationality" to "Nationality of the employee",
                "immigrationStatus" to "Immigration status of the employee",
                "workPassType" to "Type of work pass held by employee",
                "nid" to "National identification number of the employee",
                "passportNumber" to "Passport number of the employee",
                "workPassNumber" to "Work pass number of the employee",
                "bank.accountHolderName" to "Name on employee's bank account for salary payment",
                "bank.accountNumber" to "Employee's bank account number for salary payment",
                "bank.swiftCode" to "Bank's SWIFT code for salary payment",
                "bank.bankName" to "Name of employee's bank for salary payment",
                "bank.address.country" to "Country code of employee's bank for salary payment",
            )

        fun formatData(employeeDataChunks: List<EmployeeDataChunk>): List<EmployeeDataChunk> =
            employeeDataChunks.map { formatData(it) }

        private fun formatData(employeeDataChunk: EmployeeDataChunk) =
            formatBooleanValue(formatGender(employeeDataChunk))

        private fun formatGender(employeeDataChunk: EmployeeDataChunk): EmployeeDataChunk {
            if (employeeDataChunk.data.containsKey(GenderSpec.key)) {
                when (employeeDataChunk.data.getValue(GenderSpec.key).uppercase()) {
                    Gender.UNSPECIFIED.name ->
                        return employeeDataChunk.copy(
                            data = employeeDataChunk.data + mapOf(GenderSpec.key to OTHER_GENDER))
                }
            }

            return employeeDataChunk
        }

        private fun formatBooleanValue(employeeDataChunk: EmployeeDataChunk): EmployeeDataChunk {
            return employeeDataChunk.copy(
                data =
                    employeeDataChunk.data.mapValues {
                        when (it.value.uppercase()) {
                            BulkUploadExcel.BooleanValues.YES.value.uppercase() ->
                                BulkUploadExcel.BooleanValues.YES.name
                            BulkUploadExcel.BooleanValues.NO.value.uppercase() ->
                                BulkUploadExcel.BooleanValues.NO.name
                            else -> it.value
                        }
                    })
        }

        fun unformatData(employeeData: List<EmployeeData>) = employeeData.map { unformatData(it) }

        private fun unformatData(employeeData: EmployeeData) =
            unformatBooleanValue(unformatMaritalStatus(unformatGender(employeeData)))

        private fun unformatMaritalStatus(employeeData: EmployeeData): EmployeeData {
            if (employeeData.data.containsKey(MaritalStatusSpec.key)) {
                when (employeeData.data.getValue(MaritalStatusSpec.key).uppercase()) {
                    NOT_SPECIFIED_MARITAL_STATUS ->
                        return employeeData.copy(
                            data =
                                employeeData.data +
                                    mapOf(MaritalStatusSpec.key to MaritalStatus.UNSPECIFIED.name))
                }
            }

            return employeeData
        }

        private fun unformatGender(employeeData: EmployeeData): EmployeeData {
            if (employeeData.data.containsKey(GenderSpec.key)) {
                when (employeeData.data.getValue(GenderSpec.key).uppercase()) {
                    OTHER_GENDER ->
                        return employeeData.copy(
                            data =
                                employeeData.data +
                                    mapOf(GenderSpec.key to Gender.UNSPECIFIED.name))
                }
            }

            return employeeData
        }

        // HACK: this hack is quick, otherwise it is required to fetch the list of data specs before
        // unformatting, which is an expensive operation. Trade-off: if the data specs are changed,
        // this will not work. In such case, code change and new deployment is required.
        private val keysToExcludeFromBooleanUnformat = listOf("IS_INSTALLMENT")
        private fun unformatBooleanValue(employeeData: EmployeeData): EmployeeData {
            return employeeData.copy(
                data =
                    employeeData.data.mapValues {
                        if (keysToExcludeFromBooleanUnformat.contains(it.key)) {
                            it.value
                        } else
                            when (it.value.uppercase()) {
                                BulkUploadExcel.BooleanValues.YES.name ->
                                    BulkUploadExcel.BooleanValues.YES.value
                                BulkUploadExcel.BooleanValues.NO.name ->
                                    BulkUploadExcel.BooleanValues.NO.value
                                else -> it.value
                            }
                    })
        }

        fun addHelpText(dataSpec: DataSpec): DataSpec {
            return dataSpec.copy(
                description = keyToHelpText[dataSpec.keyWithPrefix()] ?: dataSpec.description)
        }
    }
}
