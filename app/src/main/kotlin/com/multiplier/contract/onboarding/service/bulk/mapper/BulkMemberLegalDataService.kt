package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberLegalDataServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.member.ApplyTo
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataDefinition
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.service.toWords
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.FetchStage
import com.multiplier.member.schema.UpdateMemberLegalDataInput
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkMemberLegalDataService(
    private val countryServiceAdapter: CountryServiceAdapter,
    private val memberLegalDataServiceAdapter: BulkMemberLegalDataServiceAdapter
) {

    companion object {
        const val LEGAL_SPEC_DATA = "LEGAL_SPEC"

        val ReligionSpec =
            DataSpec(
                key = "religion",
                type = DataSpecType.SELECT,
                mandatory = false,
                allowedValues =
                    listOf(
                        "Islam",
                        "Protestantism",
                        "Catholicism",
                        "Hinduism",
                        "Buddhism",
                        "Confucianism",
                        "Other"))
        val NationalIdSpec =
            DataSpec(key = "nationalId", type = DataSpecType.TEXT, mandatory = false)
        val PassportNumberSpec =
            DataSpec(key = "passportNumber", type = DataSpecType.TEXT, mandatory = false)
    }

    private val log = KotlinLogging.logger {}

    fun getDataSpecs(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.EOR -> dataSpecsForEor(onboardingOptions)
            BulkOnboardingContext.HRIS_PROFILE_DATA -> dataSpecsForHrisProfile()
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Member legal data specs query for ${onboardingOptions.context} not supported yet",
                    context = mapOf("context" to onboardingOptions.context))
        }
    }

    private fun dataSpecsForEor(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        return try {
            countryServiceAdapter
                .getMemberLegalDataDefinitions(
                    onboardingOptions.countryCode, onboardingOptions.contractType)
                .filter { it.fetchStage == FetchStage.CONTRACT_GENERATION }
                .distinctBy { it.key }
                .map { it.toDataSpec() }
        } catch (e: Exception) {
            log.warn(e) { "Failed to get member legal data spec for options: $onboardingOptions" }
            listOf()
        }
    }

    private fun dataSpecsForHrisProfile() = listOf(ReligionSpec, NationalIdSpec, PassportNumberSpec)

    fun validate(
        employeeData: Collection<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<UpdateMemberLegalDataInput>> {

        return memberLegalDataServiceAdapter.validateUpdateLegalDataInputs(employeeData, options)
    }

    fun createLegalData(
        inputs: List<ValidInput<UpdateMemberLegalDataInput>>,
        memberContext: MemberContext,
        options: BulkOnboardingOptions,
    ): List<String> {
        val upsertMemberLegalDataInputs =
            inputs.map {
                CreationInput(
                    requestId = it.validationId,
                    memberId = memberContext.getMemberId(it.validationId),
                    data = it.input)
            }

        val results =
            bulkUpsert(
                "Upsert legal data",
                upsertMemberLegalDataInputs,
                options,
                CreationInput.refMemberId) { legalDataInputs, onboardingOptions ->
                    memberLegalDataServiceAdapter.updateLegalData(
                        legalDataInputs, onboardingOptions)
                }

        return results.flatMap { it.errors }
    }
}

fun MemberLegalDataDefinition.toDataSpec(): DataSpec =
    DataSpec(
        key = this.key,
        type = DataSpecType.valueOf(this.type.name),
        mandatory = if (this.applyTo != ApplyTo.ALL) false else this.mandatory,
        label = if (this.description.isNullOrBlank()) key.toWords() else this.description,
        source = BulkMemberLegalDataService.LEGAL_SPEC_DATA,
        allowedValues = this.allowedValues)
