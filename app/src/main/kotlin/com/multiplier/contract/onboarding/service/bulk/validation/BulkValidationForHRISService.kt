package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.springframework.stereotype.Service

@Service
class BulkValidationForHRISService(
    private val bulkMemberDataService: BulkMemberDataService,
    private val bulkContractDataService: BulkContractDataService,
    private val bulkMemberLegalDataService: BulkMemberLegalDataService,
    private val bulkComplianceDataService: BulkComplianceDataService,
    private val bulkOrgManagementDataService: BulkOrgManagementDataService,
    private val bulkMemberEmergencyDataService: BulkMemberEmergencyDataService,
    private val bulkMemberEducationDataService: BulkMemberEducationDataService,
    private val bulkMemberEmploymentDataService: BulkMemberEmploymentDataService,
    private val bulkMemberAddressDataService: BulkMemberAddressDataService,
) : BulkValidationServiceInterface {
    override fun validateEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): EmployeeValidationResults {
        if (employeeData.isEmpty()) {
            return EmployeeValidationResults.EMPTY
        }

        val contractValidationResults = bulkContractDataService.validate(employeeData, options)
        val newEmployeeData =
            mutateEmployeeDataWithContractData(employeeData, contractValidationResults)

        val memberValidationResults = bulkMemberDataService.validate(newEmployeeData, options)
        val legalDataValidationResults =
            bulkMemberLegalDataService.validate(newEmployeeData, options)
        val orgManagementDataValidationResults =
            bulkOrgManagementDataService.validate(newEmployeeData, options)
        val emergencyContactValidationResults =
            bulkMemberEmergencyDataService.validate(newEmployeeData, options)
        val educationDetailValidationResults =
            bulkMemberEducationDataService.validate(newEmployeeData, options)
        val employmentDetailValidationResults =
            bulkMemberEmploymentDataService.validate(newEmployeeData, options)
        val addressValidationResults =
            bulkMemberAddressDataService.validate(newEmployeeData, options)
        val complianceValidationResults =
            bulkComplianceDataService.validate(
                newEmployeeData.filterNot { it.data["country"].isNullOrBlank() }, options)

        return EmployeeValidationResults(
            members = memberValidationResults,
            contracts = contractValidationResults,
            compensations = emptyList(),
            legalData = legalDataValidationResults,
            bankData = emptyList(),
            compliances = complianceValidationResults,
            orgManagementData = orgManagementDataValidationResults,
            emergencyData = emergencyContactValidationResults,
            educationData = educationDetailValidationResults,
            previousEmployerData = employmentDetailValidationResults,
            addressData = addressValidationResults,
        )
    }

    override fun validateCompanyOrThrow(options: BulkOnboardingOptions) {
        // No validation required for HRIS
    }

    override fun validateCompanyLegalEntityOrThrow(options: BulkOnboardingOptions) {
        // No validation required for HRIS
    }

    private fun mutateEmployeeDataWithContractData(
        employeeData: List<EmployeeData>,
        contractValidationResults:
            List<GrpcValidationResult<ContractOuterClass.CreateContractInput>>
    ): List<EmployeeData> {
        val validatedContracts = contractValidationResults.filter { it.success }
        return putAdditionalContractData(employeeData, validatedContracts)
    }

    private fun putAdditionalContractData(
        employeeData: List<EmployeeData>,
        validatedContracts: List<GrpcValidationResult<ContractOuterClass.CreateContractInput>>
    ): List<EmployeeData> {
        return employeeData.map { employee ->
            val validatedContract =
                validatedContracts
                    .find { it.validationId == employee.identification.validationId }
                    ?.input

            employee.copy(
                data =
                    employee.data +
                        if (validatedContract != null)
                            mapOf(
                                "country" to validatedContract.country.name,
                                "type" to validatedContract.type.name,
                                "term" to validatedContract.term.name,
                                "memberId" to validatedContract.memberId.toString(),
                            )
                        else emptyMap())
        }
    }
}
