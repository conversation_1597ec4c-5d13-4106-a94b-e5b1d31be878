package com.multiplier.contract.onboarding.service.bulk

import com.multiplier.contract.onboarding.service.bulk.creation.BulkCreationResult
import com.multiplier.contract.onboarding.service.bulk.validation.BulkValidationReport
import com.multiplier.contract.onboarding.types.BulkOnboardingJobStatus
import java.time.LocalDateTime
import java.time.temporal.ChronoUnit
import mu.KotlinLogging

data class BulkOnboardingJob
private constructor(
    val id: String,
    val startTime: LocalDateTime,
    val status: BulkOnboardingJobStatus,
    val validationReport: BulkValidationReport,
    val creationResult: BulkCreationResult?,
    val totalEmployeeCount: Int,
    val onboardedEmployeeCount: Int,
    val durationInSeconds: Long,
    val upsertedContractIds: List<Long>,
) {

    companion object {
        private val log = KotlinLogging.logger {}

        fun complete(
            id: String,
            startTime: LocalDateTime,
            validationReport: BulkValidationReport,
            creationResult: BulkCreationResult? = null
        ): BulkOnboardingJob {
            val status =
                when {
                    validationReport.hasErrors() -> {
                        log.debug {
                            "Bulk onboarding job [${id}] failed, input employee data has following validation errors: ${validationReport.getAllErrors()}"
                        }
                        val errorCount = validationReport.getAllErrors().size
                        log.warn {
                            "Bulk onboarding job [${id}] failed, input employee data has $errorCount validation errors"
                        }
                        BulkOnboardingJobStatus.VALIDATION_FAILED
                    }
                    creationResult?.success != true -> {
                        log.debug {
                            "Bulk onboarding job [${id}] failed, following errors occurred during object creation: ${creationResult?.errors}"
                        }
                        val errorCount = creationResult?.errors?.size ?: 0
                        log.error {
                            "Bulk onboarding job [${id}] failed, $errorCount errors occurred during object creation"
                        }
                        BulkOnboardingJobStatus.FAILED
                    }
                    else -> BulkOnboardingJobStatus.SUCCESS
                }

            val totalEmployeeCount = validationReport.employeeValidationResult.totalEmployeeCount
            val onboardedEmployeeCount = creationResult?.contractContext?.contractIds?.size ?: 0
            val durationInSeconds = ChronoUnit.SECONDS.between(startTime, LocalDateTime.now())
            val upsertedContractIds =
                creationResult?.contractContext?.contractIds?.toList().orEmpty()

            return BulkOnboardingJob(
                id = id,
                startTime = startTime,
                status = status,
                validationReport = validationReport,
                creationResult = creationResult,
                totalEmployeeCount = totalEmployeeCount,
                onboardedEmployeeCount = onboardedEmployeeCount,
                durationInSeconds = durationInSeconds,
                upsertedContractIds = upsertedContractIds)
        }
    }
}
