package com.multiplier.contract.onboarding.adapter

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.github.benmanes.caffeine.cache.Caffeine
import com.multiplier.company.schema.grpc.CompanyServiceGrpc
import com.multiplier.contract.onboarding.config.FeignConfig
import java.net.URL
import java.time.Duration
import java.time.LocalDateTime
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam

data class DocumentShare(
    val sessionId: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class DocumentResponse(
    val id: Long,
    @JsonProperty("viewURL") val viewUrl: String,
)

data class DocumentShareRequest(
    val email: String,
    val expiration: LocalDateTime? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class DocumentShareResponse(
    val id: Long,
    val shareId: String,
    val shareURL: URL,
    val expiresOn: LocalDateTime,
)

private val log = KotlinLogging.logger {}

@Service
class DocumentServiceAdapter(
    private val docGenClient: DocGenClient,
) {

    @GrpcClient("company-service")
    private lateinit var blockingStub: CompanyServiceGrpc.CompanyServiceBlockingStub

    private val companyLogoCache =
        Caffeine.newBuilder()
            .maximumSize(10_000)
            .expireAfterWrite(Duration.ofHours(24))
            .build<Long, DocumentResponse>()

    fun getDocumentShareOrNull(contractAgreementId: Long, memberEmail: String): DocumentShare? {
        return try {
            DocumentShare(
                sessionId =
                    docGenClient
                        .shareDocument(
                            contractAgreementId, DocumentShareRequest(email = memberEmail))
                        .shareId)
        } catch (t: Throwable) {
            log.warn("Error while getting document share link. documentId=$contractAgreementId", t)
            null
        }
    }

    fun getDocuments(ids: List<Long>): List<DocumentResponse> =
        docGenClient.getDocuments(ids.toSet())

    fun getCompanyLogoLinks(logoIds: List<Long>): Map<Long, DocumentResponse> {
        return companyLogoCache.getAll(logoIds) { ids ->
            kotlin
                .runCatching { getDocuments(ids.toList()).associateBy { it.id } }
                .onFailure { ex -> log.warn(ex) { "Failed to get company logo links, ids=$ids" } }
                .getOrElse { emptyMap() }
        }
    }
}

@FeignClient(value = "docgen-api", url = "\${docgen.url}", configuration = [FeignConfig::class])
interface DocGenClient {

    @PostMapping("/documents/{id}/share")
    fun shareDocument(
        @PathVariable("id") id: Long,
        @RequestBody request: DocumentShareRequest
    ): DocumentShareResponse

    @GetMapping("/documents") fun getDocuments(@RequestParam ids: Set<Long>): List<DocumentResponse>
}
