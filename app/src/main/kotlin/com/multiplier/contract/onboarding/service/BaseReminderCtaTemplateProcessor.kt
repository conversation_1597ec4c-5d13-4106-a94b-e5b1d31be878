package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.service.ReminderCtaHelper.baseFields

/**
 * Base abstract class for reminder CTA template processors that provides common functionality for
 * fetching data and merging editable parameters.
 */
abstract class BaseReminderCtaTemplateProcessor : ReminderCtaTemplateProcessor {

    /**
     * Final implementation that handles the common pattern of fetching base data and merging with
     * editable parameters. Subclasses should implement fetchTemplateData() to provide their
     * specific template data.
     */
    final override fun fetchDataAndOverrideEditableParams(
        preFetchedData: EmailTemplateData,
        editableParams: Map<String, String>?
    ): Map<String, String> {
        // Get base fields common to all templates
        val data = baseFields(preFetchedData).toMutableMap()

        // Add template-specific data
        data += fetchTemplateData(preFetchedData)

        // Always add editable parameter keys for frontend identification
        data += generateEditableParamsKeysFromProcessor(preFetchedData)

        // Merge saved editable parameters with template data (editable params take precedence)
        if (editableParams != null) {
            data += editableParams
        }

        return data
    }

    /**
     * Subclasses can override this method to provide template-specific data. Base fields are
     * already handled by the base class. Default implementation returns empty map.
     *
     * @param preFetchedData The pre-fetched email template data
     * @return Map of template-specific data (excluding base fields)
     */
    protected open fun fetchTemplateData(preFetchedData: EmailTemplateData): Map<String, String> =
        emptyMap()

    /**
     * Generates key identifiers for editable parameters from the processor's editable params to
     * help frontend identify which HTML elements correspond to editable fields.
     *
     * For each editable parameter key, this creates a new entry "paramNameKey": "paramName" that
     * can be used in HTML id attributes.
     *
     * @param preFetchedData The pre-fetched email template data
     * @return Map of parameter key identifiers
     */
    private fun generateEditableParamsKeysFromProcessor(
        preFetchedData: EmailTemplateData
    ): Map<String, String> {
        return editableParams(preFetchedData)
            .map { it.key }
            .associateWith { paramName -> paramName }
            .mapKeys { (paramName, _) -> "${paramName}Key" }
    }
}
