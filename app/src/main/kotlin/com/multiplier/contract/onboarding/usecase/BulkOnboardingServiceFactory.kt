package com.multiplier.contract.onboarding.usecase

import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingJob
import com.multiplier.contract.onboarding.service.bulk.validation.BulkValidationReport
import com.multiplier.contract.onboarding.service.bulk.validation.ValidationResults
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.DocumentReadable
import com.multiplier.contract.onboarding.types.PageRequest
import java.io.InputStream
import org.springframework.stereotype.Service

interface BulkOnboardingServiceInterface {
    fun generateBulkOnboardingTemplate(
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?
    ): DocumentReadable
    fun getDataSpec(options: BulkOnboardingOptions): List<DataSpec>
    fun validate(
        employeeDataFile: InputStream,
        options: BulkOnboardingOptions,
        source: String,
    ): BulkValidationReport
    fun validate(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): ValidationResults
    fun onboard(employeeDataFile: InputStream, options: BulkOnboardingOptions): BulkOnboardingJob
    fun onboard(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): BulkOnboardingJob
}

@Service
class BulkOnboardingServiceFactory(
    private val bulkOnboardingService: BulkOnboardingService,
    private val bulkOnboardingServiceV2: BulkOnboardingServiceV2
) {

    fun getBulkOnboardingService(context: BulkOnboardingContext): BulkOnboardingServiceInterface {
        return when (context) {
            BulkOnboardingContext.GLOBAL_PAYROLL -> bulkOnboardingServiceV2
            else -> bulkOnboardingService
        }
    }
}
