package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.service.convertToMap
import com.multiplier.contract.onboarding.types.NotificationType

data class ContractWorkEmailOnStartDayReminderDto(
    val notificationType: NotificationType,
    val subject: String,
    val companyUserEmails: List<String>,
    val companyLogoLink: String,
    val companyName: String,
    val companyCountry: String,
    val contractOnboardingsLink: String,
    val companyUserFirstName: String,
) {
    fun toPropertyMap(): Map<String, Any> =
        convertToMap(
            mapOf(
                "viewProfileLink" to contractOnboardingsLink,
                "firstName" to companyUserFirstName,
            ))
}
