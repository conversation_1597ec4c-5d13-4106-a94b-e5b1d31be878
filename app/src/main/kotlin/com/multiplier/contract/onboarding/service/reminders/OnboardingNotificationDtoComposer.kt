package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.common.exception.toBusinessException
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import com.multiplier.contract.onboarding.domain.isAfter
import com.multiplier.contract.onboarding.domain.isOnOrAfter
import com.multiplier.contract.onboarding.domain.model.Member
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.domain.model.OnboardingTask
import com.multiplier.contract.onboarding.domain.model.Person
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.countryAndState
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.country.schema.Country
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import mu.KotlinLogging
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class OnboardingNotificationDtoComposer(
    val contractServiceAdapter: ContractServiceAdapter,
    val companyServiceAdapter: CompanyServiceAdapter,
    val memberServiceAdapter: MemberServiceAdapter,
    val countryServiceAdapter: CountryServiceAdapter,
    val documentServiceAdapter: DocumentServiceAdapter,
    val onboardingServiceAdapter: OnboardingServiceAdapter,
    val operationsUserServiceAdapter: OperationsUserServiceAdapter,
    val memberOnboardingLinkCreator: MemberOnboardingLinkCreator,
    @Value("\${platform.base-url}") val baseUrl: String,
) {

    lateinit var contracts: Map<Long, Contract>
    lateinit var companies: Map<Long, com.multiplier.contract.onboarding.domain.model.Company>
    lateinit var members: Map<Long, Member>
    lateinit var companyLogoLinks: Map<Long, String?>
    lateinit var companyUsersByCompanyId: Map<Long, List<CompanyOuterClass.CompanyUser>>
    lateinit var companyOnboardings: Map<Long, Onboarding>
    lateinit var memberOnboardings: Map<Long, Onboarding>
    lateinit var paidDeposits: Set<Long>
    lateinit var legalDocumentsByMemberId: Map<Long, Set<String>>
    lateinit var requiredPayrollDocumentKeysByCountryAndState: Map<CountryAndState, Set<String>>
    lateinit var saleUserByCompanyId:
        Map<Long, com.multiplier.contract.onboarding.domain.model.OperationsUser>
    lateinit var csmUserByCompanyId:
        Map<Long, com.multiplier.contract.onboarding.domain.model.OperationsUser>
    lateinit var onboardingOpsUserByContractId:
        Map<Long, com.multiplier.contract.onboarding.domain.model.OperationsUser>
    lateinit var complianceByContractId:
        Map<Long, com.multiplier.contract.onboarding.domain.model.Compliance>
    lateinit var memberEmailByContractId: Map<Long, String>

    fun init(payrollCycleContractsList: List<OnboardingReminders.ContractsForCutoffDate>) {
        val contractIds = payrollCycleContractsList.flatMap { it.contractIds }
        fetchContracts(contractIds)
        fetchCompanies()
        fetchCompanyLogoLinks()
        fetchCompanyUsers()
        fetchMembers()
        fetchContractOnboardingStatuses()
        fetchContractDepositPayables()
        fetchMemberLegalDocuments()
        fetchLegalDocumentRequirements()
        fetchSaleUsers()
        fetchCsmUsers()
        fetchOnboardingOpsUsers()
        fetchComplianceByContractId()
        fetchMemberEmailByContractId()
    }

    fun composeCompanyEmailDto(
        payrollCycleContracts: OnboardingReminders.ContractsForCutoffDate,
        companyId: Long,
        sendingDate: LocalDate
    ): CompanyOnboardingNotificationEmailDto? {
        val daysToCutoff =
            sendingDate.until(payrollCycleContracts.cutoffDate, ChronoUnit.DAYS).toInt()
        val notificationType: NotificationType = getNotificationTypeForCompany(daysToCutoff)

        val contractsInCompany =
            payrollCycleContracts.contractIds
                .mapNotNull { contracts[it] }
                .filter { companyId == it.companyId }

        if (contractsInCompany.isEmpty()) {
            log.warn(
                "Can't find any onboarding-pending contracts for company id = {}, payroll cutoff date = {}, month = {}",
                companyId,
                payrollCycleContracts.cutoffDate,
                payrollCycleContracts.payrollMonth)
            return null
        }

        val pendingOnboardings: List<PendingOnboardingEmailDto> =
            getPendingOnboardings(contractsInCompany)

        val onboardingContracts = pendingOnboardings.mapNotNull { contracts[it.contractId] }

        return CompanyOnboardingNotificationEmailDto(
            notificationType = notificationType,
            subject = getNotificationSubjectForCompany(notificationType, daysToCutoff),
            companyUserEmails =
                getTargetCompanyUsers(companyId, onboardingContracts).mapNotNull {
                    getCompanyUserEmail(it)
                },
            saleUserEmail = saleUserByCompanyId[companyId]?.email ?: "",
            csmUserEmail = csmUserByCompanyId[companyId]?.email ?: "",
            onboardingOpsUserEmails =
                onboardingContracts.map { onboardingOpsUserByContractId[it.id]?.email ?: "" },
            companyLogoLink = companyLogoLinks[companyId],
            companyName = companies[companyId]?.displayName ?: "",
            companyCountry = companies[companyId]?.countryFullName,
            onboardingCount = onboardingContracts.size,
            employeeFullNames = onboardingContracts.mapNotNull { members[it.memberId]?.fullName },
            payrollMonth = payrollCycleContracts.payrollMonth,
            cutoffDate = payrollCycleContracts.cutoffDate,
            daysToCutoff = daysToCutoff,
            pendingOnboardings = pendingOnboardings,
            contractOnboardingsLink = "$baseUrl/company/team?tab=ONBOARDING",
        )
    }

    private fun getCompanyUserEmail(companyUser: CompanyOuterClass.CompanyUser) =
        companyUser.emailsList
            .firstOrNull {
                it.type.equals("primary", ignoreCase = true) ||
                    it.type.equals("default", ignoreCase = true)
            }
            ?.email

    fun composeMemberEmailDto(
        payrollCycleContracts: OnboardingReminders.ContractsForCutoffDate,
        contractId: Long,
        sendingDate: LocalDate
    ): MemberOnboardingNotificationEmailDto {
        val daysToCutoff =
            sendingDate.until(payrollCycleContracts.cutoffDate, ChronoUnit.DAYS).toInt()
        val notificationType: NotificationType = getNotificationTypeForMember(daysToCutoff)
        val contract: Contract =
            contracts[contractId]
                ?: throw ErrorCodes.CONTRACT_NOT_FOUND.toBusinessException(
                    "No contract found with id $contractId",
                    context = mapOf(CONTRACT_ID to contractId))

        val member =
            members[contract.memberId]
                ?: throw ErrorCodes.MEMBER_NOT_FOUND.toBusinessException(
                    "Member not found, contractId = ${contract.id}, memberId = ${contract.memberId}",
                    context = mapOf(MEMBER_ID to contract.memberId, CONTRACT_ID to contractId))
        val company =
            companies[contract.companyId]
                ?: throw ErrorCodes.COMPANY_NOT_FOUND.toBusinessException(
                    "Company not found, contractId = ${contract.id}, companyId = ${contract.companyId}",
                    context =
                        mapOf(
                            CONTRACT_ID to contractId,
                            MEMBER_ID to contract.memberId,
                            "companyId" to contract.companyId))
        val onboardingTasks: List<OnboardingTask> = getOnboardingTasksShownToMember(contract)
        val incompleteOnboardingTasks = onboardingTasks.filter { !it.completed }

        val domainContract =
            com.multiplier.contract.onboarding.domain.model.Contract(
                id = contract.id,
                memberId = contract.memberId,
                companyId = company.id,
                agreementId = complianceByContractId[contract.id]?.agreementId)

        val memberInvitedToSignContract =
            isMemberInvitedToSignContract(contract) || isMemberInvitedToPlatform(contract)

        return MemberOnboardingNotificationEmailDto(
            notificationType = notificationType,
            subject =
                getNotificationSubjectForMember(
                    notificationType, payrollCycleContracts.cutoffDate, daysToCutoff),
            memberEmail = memberEmailByContractId[contractId] ?: "",
            saleUserEmail = saleUserByCompanyId[contract.companyId]?.email ?: "",
            csmUserEmail = csmUserByCompanyId[contract.companyId]?.email ?: "",
            onboardingOpsUserEmail = onboardingOpsUserByContractId[contractId]?.email ?: "",
            memberInvitedToSignContract = memberInvitedToSignContract,
            companyLogoLink = companyLogoLinks[contract.companyId],
            companyName = companies[contract.companyId]?.displayName ?: "",
            companyCountry = company.countryFullName,
            employeeFirstName = getMemberFirstName(contract.memberId),
            payrollMonth = payrollCycleContracts.payrollMonth,
            cutoffDate = payrollCycleContracts.cutoffDate,
            daysToCutoff =
                sendingDate.until(payrollCycleContracts.cutoffDate, ChronoUnit.DAYS).toInt(),
            pendingStepCount = incompleteOnboardingTasks.size,
            onboardingTasks = onboardingTasks,
            contractOnboardingLink =
                memberOnboardingLinkCreator.createOnboardingLink(
                    contract = domainContract,
                    memberInvitedToSignContract = memberInvitedToSignContract,
                    company = company,
                    member = member,
                    tasks = onboardingTasks,
                    companyLogoLink = companyLogoLinks[company.id],
                ),
        )
    }

    private fun getPendingOnboardings(
        onboardingContracts: List<Contract>
    ): List<PendingOnboardingEmailDto> {
        return onboardingContracts
            .map { contract ->
                val onboardingTasks = getOnboardingTasksShownToCompany(contract)
                PendingOnboardingEmailDto(
                    contractId = contract.id,
                    contractOnboardingLinkForCompany =
                        "$baseUrl/company/member-onboard/${contract.id}/onboarding/",
                    employeeFullName = members[contract.memberId]?.fullName,
                    pendingStepCount = onboardingTasks.count { !it.completed },
                    onboardingTasks = onboardingTasks,
                )
            }
            .filter { it.pendingStepCount > 0 }
    }

    private fun getOnboardingTasksShownToCompany(contract: Contract): List<OnboardingTask> =
        listOfNotNull(
                createCompanySignMsaTask(contract.companyId),
                createCompanySendContractTask(contract),
                createCompanySignContractTask(contract),
                createCompanyPayDeposit(contract),
                createMemberPlatformOnboardingTask(contract),
                createMemberKycDetailsTask(contract),
                createMemberPayrollAndComplianceTask(contract))
            .filter { !it.completed }

    private fun getOnboardingTasksShownToMember(contract: Contract): List<OnboardingTask> =
        listOfNotNull(
            createMemberSignContractTask(contract),
            createMemberPlatformOnboardingTask(contract),
            createMemberKycDetailsTask(contract),
            createMemberPayrollAndComplianceTask(contract))

    private fun createCompanySendContractTask(contract: Contract): OnboardingTask {
        return OnboardingTask(
            OnboardingTaskName.SEND_CONTRACT,
            isOnboardingPassedThrough(contract, COMPANY_EXP, ContractOnboardingStatus.CREATED) &&
                isOnboardingPassedThrough(contract, COMPANY_EXP, ContractOnboardingStatus.REVOKED),
            Person(
                fullName =
                    getCompanyUserName(
                        getContractCreator(contract),
                        companies[contract.companyId]?.displayName ?: "")))
    }

    private fun createCompanySignContractTask(contract: Contract): OnboardingTask {
        return OnboardingTask(
            OnboardingTaskName.SIGN_CONTRACT,
            isOnboardingPassedThrough(
                contract, COMPANY_EXP, ContractOnboardingStatus.SIGNATURE_EMPLOYER_SENT),
            Person(
                fullName =
                    getCompanyUserName(
                        getSignatoryUser(contract.companyId),
                        companies[contract.companyId]?.displayName ?: "")))
    }

    private fun createCompanyPayDeposit(contract: Contract): OnboardingTask? {
        return if (contract.isTest) {
            null
        } else
            OnboardingTask(
                OnboardingTaskName.PAY_DEPOSIT,
                isOnboardingPassedThrough(
                    contract, COMPANY_EXP, ContractOnboardingStatus.CREATED) &&
                    isDepositPaid(contract),
                Person(
                    fullName =
                        getCompanyUserName(
                            getBillingContact(contract.companyId),
                            companies[contract.companyId]?.displayName ?: "")))
    }

    private fun createMemberSignContractTask(contract: Contract): OnboardingTask {
        val companyOnboarding = companyOnboardings[contract.id]

        val memberHasSigned =
            companyOnboarding?.status?.isAfter(OnboardingStatus.SIGNATURE_EMPLOYEE_SENT) ?: false
        val memberHasStartedOnboarding = memberOnboardings[contract.id] != null
        val currentlyWaitingForMemberSignature =
            companyOnboarding?.status == OnboardingStatus.SIGNATURE_EMPLOYEE_SENT
        val contractHasBeenRevised = memberHasStartedOnboarding && !memberHasSigned

        // Special case:
        // 1. Contract is signed by all parties & member starts onboarding
        // 2. Company admin revises the contract but has not re-sent to the member for signature yet
        // => The task should still be considered done from the member's perspective as they might
        // not yet know about the revision.
        val shouldConsiderTaskCompleted =
            (memberHasSigned || contractHasBeenRevised) && !currentlyWaitingForMemberSignature

        return OnboardingTask(
            name = OnboardingTaskName.SIGN_CONTRACT,
            completed = shouldConsiderTaskCompleted,
            pendingOn =
                Person(
                    fullName = members[contract.memberId]?.fullName
                            ?: throw ErrorCodes.MEMBER_NOT_FOUND.toBusinessException(
                                "Member not found, memberId = ${contract.memberId}",
                                context = mapOf(MEMBER_ID to contract.memberId))))
    }

    private fun createMemberPlatformOnboardingTask(contract: Contract): OnboardingTask {
        return OnboardingTask(
            name = OnboardingTaskName.PLATFORM_ONBOARDING,
            completed =
                isOnboardingPassedThrough(
                    contract, MEMBER_EXP, ContractOnboardingStatus.MEMBER_INVITED),
            pendingOn =
                Person(
                    fullName = members[contract.memberId]?.fullName
                            ?: throw ErrorCodes.MEMBER_NOT_FOUND.toBusinessException(
                                "Member not found, memberId = ${contract.memberId}",
                                context = mapOf(MEMBER_ID to contract.memberId))))
    }

    private fun createMemberKycDetailsTask(contract: Contract): OnboardingTask {
        return OnboardingTask(
            name = OnboardingTaskName.KYC_DETAILS_AND_DOCUMENTS,
            completed =
                isOnboardingPassedThrough(
                    contract, MEMBER_EXP, ContractOnboardingStatus.MEMBER_DATA_ADDED),
            pendingOn =
                Person(
                    fullName = members[contract.memberId]?.fullName
                            ?: throw ErrorCodes.MEMBER_NOT_FOUND.toBusinessException(
                                "Member not found, memberId = ${contract.memberId}",
                                context = mapOf(MEMBER_ID to contract.memberId))))
    }

    private fun createMemberPayrollAndComplianceTask(contract: Contract): OnboardingTask? {
        return if (isPayrollFormsRequired(contract)) {
            OnboardingTask(
                name = OnboardingTaskName.PAYROLL_AND_COMPLIANCE_FORMS,
                completed =
                    isOnboardingPassedThrough(
                        contract, MEMBER_EXP, ContractOnboardingStatus.MEMBER_DATA_ADDED) &&
                        isPayrollFormsSubmitted(contract),
                pendingOn =
                    Person(
                        fullName = members[contract.memberId]?.fullName
                                ?: throw ErrorCodes.MEMBER_NOT_FOUND.toBusinessException(
                                    "Member not found, memberId = ${contract.memberId}",
                                    context = mapOf(MEMBER_ID to contract.memberId))))
        } else null
    }

    private fun createCompanySignMsaTask(companyId: Long): OnboardingTask? {
        val isMsaSigned = companies[companyId]?.msaSigned ?: false

        return if (isMsaSigned) {
            null
        } else
            OnboardingTask(
                OnboardingTaskName.SIGN_MSA,
                false,
                Person(fullName = companies[companyId]?.displayName ?: ""))
    }

    private fun isOnboardingPassedThrough(
        contract: Contract,
        experience: String,
        status: ContractOnboardingStatus
    ): Boolean {
        val onboarding: Onboarding =
            (if (experience == COMPANY_EXP) companyOnboardings[contract.id]
            else memberOnboardings[contract.id])
                ?: return false

        return onboarding.status.isAfter(status.toDomainType())
    }

    private fun ContractOnboardingStatus.toDomainType(): OnboardingStatus =
        OnboardingStatus.valueOf(this.name)

    private fun isDepositPaid(contract: Contract): Boolean = paidDeposits.contains(contract.id)

    private fun isMemberInvitedToSignContract(contract: Contract): Boolean {
        val onboarding = companyOnboardings[contract.id] ?: return false
        return onboarding.status.isOnOrAfter(OnboardingStatus.SIGNATURE_EMPLOYEE_SENT)
    }

    private fun isMemberInvitedToPlatform(contract: Contract): Boolean {
        return memberOnboardings[contract.id]?.status?.isOnOrAfter(OnboardingStatus.MEMBER_INVITED)
            ?: false
    }

    private fun isPayrollFormsRequired(contract: Contract): Boolean {
        return requiredPayrollDocumentKeysByCountryAndState[contract.countryAndState]?.isNotEmpty()
            ?: false
    }

    private fun isPayrollFormsSubmitted(contract: Contract): Boolean {
        if (!legalDocumentsByMemberId.containsKey(contract.memberId)) {
            return false
        }

        val keys = legalDocumentsByMemberId[contract.memberId] ?: emptySet()
        val required =
            requiredPayrollDocumentKeysByCountryAndState[contract.countryAndState] ?: emptyList()
        return keys.containsAll(required)
    }

    private fun fetchContracts(contractIds: Collection<Long>) {
        contracts =
            contractServiceAdapter.getNonDeletedNonEndedContracts(contractIds.toSet()).associateBy {
                it.id
            }
    }

    private fun fetchCompanies() {
        companies =
            companyServiceAdapter
                .getCompanies(contracts.values.map { it.companyId }.toSet())
                .associateBy { it.id }
    }

    private fun fetchCompanyLogoLinks() {
        val companyLogos =
            companies.values
                .map { it.logoId }
                .let { logoIds -> documentServiceAdapter.getCompanyLogoLinks(logoIds) }
        companyLogoLinks = companies.values.associate { it.id to companyLogos[it.logoId]?.viewUrl }
    }

    private fun fetchCompanyUsers() {
        companyUsersByCompanyId =
            companyServiceAdapter
                .getCompanyUsersBy(
                    GetCompanyUsersFilter(
                        companyIds = companies.values.map { it.id }.toSet(),
                        statuses = setOf(CompanyOuterClass.CompanyUserStatus.ACTIVE),
                    ))
                .groupBy { it.companyId }
    }

    private fun fetchMembers() {
        val memberIds = contracts.values.map { it.memberId }.toSet()

        members = memberServiceAdapter.getMembers(memberIds).associateBy { it.id }
    }

    private fun fetchContractOnboardingStatuses() {
        companyOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(contracts.keys, COMPANY_EXP)
        memberOnboardings =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(contracts.keys, MEMBER_EXP)
    }

    private fun fetchComplianceByContractId() {
        complianceByContractId =
            contractServiceAdapter.getComplianceByContractIds(contracts.keys).associateBy {
                it.contractId
            }
    }

    private fun fetchMemberEmailByContractId() {
        memberEmailByContractId =
            contractServiceAdapter.getContractMembersEmailByContractIds(contracts.keys)
    }

    private fun fetchContractDepositPayables() {
        paidDeposits = contractServiceAdapter.getDepositPaidContractIds(contracts.keys.toSet())
    }

    private fun fetchMemberLegalDocuments() {
        val memberIds = members.values.map { it.id }.toSet()
        legalDocumentsByMemberId = memberServiceAdapter.getLegalDocumentsKeyByMemberIds(memberIds)
    }

    private fun fetchLegalDocumentRequirements() {
        // INFO: 06-Mar-2023/max: we didn't handle the case where the legal document requirements
        // are country state-specific
        // To do so, we need an API return state-specific configs, which is not yet implemented at
        // the moment, (at the same time,
        // there are no state-specific configs on prod which are different from country configs,
        // i.e. Canada configs)

        val countryAndStates = contracts.values.map { it.countryAndState }.toSet()

        requiredPayrollDocumentKeysByCountryAndState =
            countryServiceAdapter
                .getRequiredDocumentKeysGroupByCountryCode(
                    countryAndStates,
                    Country.GrpcLegalDocumentRequirement.GrpcLegalDocumentCategory.PAYROLL)
                .map { it.key to it.value }
                .toMap()
    }

    private fun fetchSaleUsers() {
        saleUserByCompanyId =
            operationsUserServiceAdapter.getSalesUsersForCompanies(companies.values.map { it.id })
    }

    private fun fetchCsmUsers() {
        csmUserByCompanyId =
            operationsUserServiceAdapter.getCsmUsersForCompanies(companies.values.map { it.id })
    }

    private fun fetchOnboardingOpsUsers() {
        onboardingOpsUserByContractId =
            operationsUserServiceAdapter.getOperationsUsersForContracts(contracts.keys.toList())
    }

    private fun getNotificationSubjectForCompany(
        notificationType: NotificationType,
        daysToCutoff: Int
    ): String {
        return when (notificationType) {
            NotificationType.FirstContractOnboardingReminderToCompany ->
                "Action Pending: Complete Onboarding to Avoid Potential Employee Payroll Delay"
            NotificationType.SecondContractOnboardingReminderToCompany ->
                "[Action Needed] Complete Onboarding to Avoid Potential Employee Payroll Delay"
            NotificationType.ThirdContractOnboardingReminderToCompany -> {
                val daysToCutoffString =
                    if (daysToCutoff > 0) "$daysToCutoff Day(s) Left" else "Last Day"
                "[URGENT] $daysToCutoffString to Complete Onboarding. Take Action Now to Avoid Delay in Employee Payroll."
            }
            else ->
                throw ErrorCodes.NOTIFICATION_TYPE_INVALID.toBusinessException(
                    "Notification of type " + notificationType.name + " is not for company",
                    context = mapOf(NOTIFICATION_TYPE to notificationType.name))
        }
    }

    private fun getNotificationSubjectForMember(
        notificationType: NotificationType,
        cutoffDate: LocalDate,
        daysToCutoff: Int
    ): String {
        return when (notificationType) {
            NotificationType.FirstContractOnboardingReminderToMember ->
                "Complete your Onboarding to be included for Payroll"
            NotificationType.SecondContractOnboardingReminderToMember ->
                "[Action Pending] Complete Onboarding Before " +
                    cutoffDate.format(DateTimeFormatter.ofPattern("MMM dd, yyyy")) +
                    " to Avoid Delay in Your Salary"
            NotificationType.ThirdContractOnboardingReminderToMember -> {
                val daysToCutoffString =
                    if (daysToCutoff > 0) "$daysToCutoff Day(s) Left" else "Last Day"
                "[URGENT] $daysToCutoffString to Complete Onboarding & Avoid Delay in Your Salary"
            }
            else ->
                throw ErrorCodes.NOTIFICATION_TYPE_INVALID.toBusinessException(
                    "Notification of type " + notificationType.name + " is not for member",
                    context = mapOf(NOTIFICATION_TYPE to notificationType.name))
        }
    }

    private fun getNotificationTypeForCompany(daysToCutoff: Int): NotificationType {
        if (daysToCutoff > 8) {
            return NotificationType.FirstContractOnboardingReminderToCompany
        } else if (daysToCutoff > 2) {
            return NotificationType.SecondContractOnboardingReminderToCompany
        }
        return NotificationType.ThirdContractOnboardingReminderToCompany
    }

    private fun getNotificationTypeForMember(daysToCutoff: Int): NotificationType {
        if (daysToCutoff > 8) {
            return NotificationType.FirstContractOnboardingReminderToMember
        } else if (daysToCutoff > 2) {
            return NotificationType.SecondContractOnboardingReminderToMember
        }
        return NotificationType.ThirdContractOnboardingReminderToMember
    }

    private fun getContractCreator(contract: Contract): CompanyOuterClass.CompanyUser? {
        var contractCreator: CompanyOuterClass.CompanyUser? = null

        if (companyUsersByCompanyId.containsKey(contract.companyId)) {
            contractCreator =
                companyUsersByCompanyId[contract.companyId]?.firstOrNull {
                    contract.createdBy.toString() == it.userId
                }
        }

        if (contractCreator == null) {
            log.warn("Unable to find contract creator for contract id = {}", contract.id)
        }

        return contractCreator
    }

    private fun getContractCreators(
        companyId: Long,
        contracts: List<Contract>
    ): List<CompanyOuterClass.CompanyUser> {
        if (!companyUsersByCompanyId.containsKey(companyId)) {
            log.warn("Unable to find contract creators from users of company id = {}", companyId)
            return emptyList()
        }

        val creatorIds = contracts.map { it.createdBy }

        return companyUsersByCompanyId[companyId]?.filter {
            it.userId.isNotEmpty() && creatorIds.contains(it.userId.toLong())
        }
            ?: emptyList()
    }

    private fun getTargetCompanyUsers(
        companyId: Long,
        contracts: List<Contract>
    ): List<CompanyOuterClass.CompanyUser> {
        return getCompanyAdminAndBillingContact(companyId) +
            getContractCreators(companyId, contracts)
    }

    private fun getCompanyAdminAndBillingContact(
        companyId: Long
    ): List<CompanyOuterClass.CompanyUser> {
        if (!companyUsersByCompanyId.containsKey(companyId)) {
            log.warn("Unable to find admins for company id = {}", companyId)
            return emptyList()
        }

        return companyUsersByCompanyId[companyId]?.filter { isAdmin(it) || isBillingContact(it) }
            ?: emptyList()
    }

    private fun isAdmin(user: CompanyOuterClass.CompanyUser): Boolean {
        return user.rolesList?.any {
            it == CompanyOuterClass.CompanyUserRole.ADMIN ||
                it == CompanyOuterClass.CompanyUserRole.PRIMARY_ADMIN
        }
            ?: false
    }

    private fun getSignatoryUser(companyId: Long): CompanyOuterClass.CompanyUser? {
        if (!companyUsersByCompanyId.containsKey(companyId)) {
            log.warn("Unable to find signatory for company id = {}", companyId)
            return null
        }

        return companyUsersByCompanyId[companyId]?.firstOrNull { isSignatory(it) }
    }

    private fun isSignatory(cu: CompanyOuterClass.CompanyUser): Boolean {
        return cu.isSignatory ||
            hasCapability(cu, CompanyOuterClass.CompanyUserCapability.SIGNATORY)
    }

    private fun getBillingContact(companyId: Long): CompanyOuterClass.CompanyUser? {
        if (!companyUsersByCompanyId.containsKey(companyId)) {
            log.warn("Unable to find billing contact for company id = {}", companyId)
            return null
        }

        return companyUsersByCompanyId[companyId]?.firstOrNull { isBillingContact(it) }
    }

    private fun isBillingContact(cu: CompanyOuterClass.CompanyUser): Boolean {
        return java.lang.Boolean.TRUE == cu.isBillingContact ||
            hasCapability(cu, CompanyOuterClass.CompanyUserCapability.BILLING_CONTACT)
    }

    private fun hasCapability(
        cu: CompanyOuterClass.CompanyUser,
        billingContact: CompanyOuterClass.CompanyUserCapability
    ): Boolean {
        return cu.companyUserCapabilityList.isNotEmpty() &&
            cu.companyUserCapabilityList.any { it == billingContact }
    }

    private fun getCompanyUserName(
        companyUser: CompanyOuterClass.CompanyUser?,
        defaultName: String
    ): String {
        if (companyUser == null) {
            return defaultName.trim()
        }
        if (!companyUser.firstName.isNullOrEmpty() && !companyUser.lastName.isNullOrEmpty()) {
            return companyUser.firstName.trim() + " " + companyUser.lastName.trim()
        }
        if (!companyUser.firstName.isNullOrEmpty()) {
            return companyUser.firstName.trim()
        }
        return if (!companyUser.lastName.isNullOrEmpty()) {
            companyUser.lastName.trim()
        } else defaultName.trim()
    }

    private fun getMemberFirstName(memberId: Long): String {
        val member: Member = members[memberId] ?: return StringUtils.EMPTY
        if (StringUtils.isNotBlank(member.firstName)) {
            return member.firstName
        }
        return member.fullName.ifBlank { member.lastName }
    }

    companion object {
        const val COMPANY_EXP = "company"
        const val MEMBER_EXP = "member"
        const val MEMBER_ID = "memberId"
        const val CONTRACT_ID = "contractId"
        const val NOTIFICATION_TYPE = "notificationType"
    }
}
