package com.multiplier.contract.onboarding.service.bulk.data

import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.MemberIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberAddressDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEducationDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEmergencyDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberEmploymentDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.PageRequest
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class DataForHrisProfileService(
    private val memberDataService: BulkMemberDataService,
    private val contractDataService: BulkContractDataService,
    private val bulkOrgManagementDataService: BulkOrgManagementDataService,
    private val emergencyDataService: BulkMemberEmergencyDataService,
    private val educationDataService: BulkMemberEducationDataService,
    private val employmentDataService: BulkMemberEmploymentDataService,
    private val complianceDataService: BulkComplianceDataService,
    private val addressDataService: BulkMemberAddressDataService,
) : BulkDataServiceInterface {

    private val log = KotlinLogging.logger {}

    override fun getDataForSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?,
    ): List<EmployeeDataChunk> {
        val contractDataContext =
            contractDataService.getDataForSpecs(dataSpecs + MemberIdSpec, options, pageRequest)
        val contractIdToMemberId =
            contractDataContext.contractIdToContractData.mapValues {
                it.value.data.getValue(MemberIdSpec.key).toLong()
            }

        val memberIds = contractIdToMemberId.values.toSet()
        val memberDataContext = memberDataService.getDataForSpecs(dataSpecs, memberIds)

        val orgManagementDataContext =
            bulkOrgManagementDataService.getDataForSpecs(
                dataSpecs = dataSpecs,
                contractIds = contractDataContext.contractIds,
                options = options)

        val memberEmergencyContactDataContext =
            emergencyDataService.getDataForSpecs(dataSpecs, memberIds)

        val memberEducationDataContext = educationDataService.getDataForSpecs(dataSpecs, memberIds)

        val memberPreviousEmploymentDataContext =
            employmentDataService.getDataForSpecs(dataSpecs, memberIds)

        val complianceDataContext =
            complianceDataService.getDataForSpecs(dataSpecs, contractDataContext.contractIds)

        val memberAddressDataContext = addressDataService.getDataForSpecs(dataSpecs, memberIds)

        return contractDataContext.contractIdToContractData.mapNotNull {
            (contractId, contractDataChunk) ->
            val memberId = contractIdToMemberId[contractId]
            val memberDataChunk = memberDataContext.memberIdToMemberData[memberId]
            val orgManagementData =
                orgManagementDataContext.contractIdToOrgManagementData[contractId]?.data
                    ?: emptyMap()
            val memberEmergencyData =
                memberEmergencyContactDataContext.memberIdToMemberData[memberId]?.data ?: emptyMap()

            val memberEducationData =
                memberEducationDataContext.memberIdToMemberData[memberId]?.data ?: emptyMap()

            val memberPreviousEmployerData =
                memberPreviousEmploymentDataContext.memberIdToMemberData[memberId]?.data
                    ?: emptyMap()

            val complianceData =
                complianceDataContext.contractIdToContractData[contractId]?.data ?: emptyMap()

            val memberAddressData =
                memberAddressDataContext.memberIdToMemberData[memberId]?.data ?: emptyMap()
            if (memberDataChunk == null) {
                log.warn { "Member data is missing for contractId: $contractId" }
                null
            } else {
                EmployeeDataChunk(
                    contractDataChunk.data +
                        memberDataChunk.data +
                        orgManagementData +
                        memberEmergencyData +
                        memberEducationData +
                        memberPreviousEmployerData +
                        complianceData +
                        memberAddressData)
            }
        }
    }
}
