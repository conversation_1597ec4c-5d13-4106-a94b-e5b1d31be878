package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataHelper
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataHelper.Companion.defaultEmployeeDataMergeStrategy
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.springframework.stereotype.Service

@Service
class BulkValidationForEORService(
    private val bulkContractDataService: BulkContractDataService,
    private val bulkMemberDataService: BulkMemberDataService,
    private val bulkMemberLegalDataService: BulkMemberLegalDataService,
    private val bulkComplianceDataService: BulkComplianceDataService,
    private val bulkCompensationDataService: BulkCompensationDataService,
) : BulkValidationServiceInterface {

    override fun mergeEmployeeData(employeeData: List<EmployeeData>): List<EmployeeData> {
        val mergeStrategy: (EmployeeData) -> String = {
            it.identification.rowIdentifier.orEmpty().ifEmpty {
                defaultEmployeeDataMergeStrategy(it)
            }
        }
        return EmployeeDataHelper.mergeEmployeeData(employeeData, mergeStrategy)
    }

    override fun validateEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): EmployeeValidationResults {
        if (employeeData.isEmpty()) {
            return EmployeeValidationResults.EMPTY
        }

        val validatedContracts = bulkContractDataService.validate(employeeData, options)
        val newEmployeeData = mutateEmployeeDataWithContractData(employeeData, validatedContracts)
        val validatedMembers = bulkMemberDataService.validate(newEmployeeData, options)
        val validateLegalData = bulkMemberLegalDataService.validate(newEmployeeData, options)
        val validateCompliance = bulkComplianceDataService.validate(newEmployeeData, options)
        val validateCompensation = bulkCompensationDataService.validate(newEmployeeData, options)

        return EmployeeValidationResults(
            members = validatedMembers,
            contracts = validatedContracts,
            compliances = validateCompliance,
            compensations = validateCompensation,
            legalData = validateLegalData,
            bankData = emptyList(),
            orgManagementData = emptyList(),
            emergencyData = emptyList(),
            educationData = emptyList(),
            previousEmployerData = emptyList(),
            addressData = emptyList(),
        )
    }

    override fun validateCompanyOrThrow(options: BulkOnboardingOptions) {}

    override fun validateCompanyLegalEntityOrThrow(options: BulkOnboardingOptions) {}

    private fun mutateEmployeeDataWithContractData(
        employeeData: List<EmployeeData>,
        contractValidationResults:
            List<GrpcValidationResult<ContractOuterClass.CreateContractInput>>
    ): List<EmployeeData> {
        val validatedContracts = contractValidationResults.filter { it.success }
        return putAdditionalContractData(employeeData, validatedContracts)
    }

    private fun putAdditionalContractData(
        employeeData: List<EmployeeData>,
        validatedContracts: List<GrpcValidationResult<ContractOuterClass.CreateContractInput>>
    ): List<EmployeeData> {
        return employeeData.map { employee ->
            val validatedContract =
                validatedContracts
                    .find { it.validationId == employee.identification.validationId }
                    ?.input

            employee.copy(
                data =
                    employee.data +
                        if (validatedContract != null)
                            mapOf(
                                "country" to validatedContract.country.name,
                                "type" to validatedContract.type.name,
                                "term" to validatedContract.term.name,
                                "memberId" to validatedContract.memberId.toString(),
                            )
                        else emptyMap())
        }
    }
}
