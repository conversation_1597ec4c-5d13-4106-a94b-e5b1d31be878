package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.notifications.Email

data class Notification(
    val type: NotificationType,
    val subject: String,
    val from: String,
    val to: String,
    val cc: List<String>,
    val attachments: List<Email.EmailAttachment> = emptyList(),
    val data: Map<String, Any>,
)
