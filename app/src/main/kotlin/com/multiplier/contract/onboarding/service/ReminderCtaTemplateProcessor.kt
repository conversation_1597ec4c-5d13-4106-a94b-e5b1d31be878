package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.types.NotificationType

interface ReminderCtaTemplateProcessor {
    fun notificationType(): NotificationType
    fun validate(preFetchedData: EmailTemplateData): Bo<PERSON>an
    /**
     * Fetches the data required for the email template.
     *
     * @param preFetchedData The pre-fetched email template data containing contract, member, and
     *   other context
     * @param editableParams Optional editable parameters from saved reminder (for template preview
     *   with saved values)
     * @return Map of template data including any saved editable parameter values
     */
    fun fetchDataAndOverrideEditableParams(
        preFetchedData: EmailTemplateData,
        editableParams: Map<String, String>? = null
    ): Map<String, String>

    /**
     * Returns the list of editable parameters specific to this template processor. These parameters
     * can be customized per reminder instance based on the contract's current state.
     *
     * @param preFetchedData The pre-fetched email template data containing contract, member, and
     *   other context
     * @return List of editable parameters available for this template and contract state
     */
    fun editableParams(preFetchedData: EmailTemplateData): List<EditableParam> = emptyList()
}
