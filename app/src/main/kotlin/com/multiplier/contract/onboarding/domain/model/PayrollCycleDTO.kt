package com.multiplier.contract.onboarding.domain.model

import java.time.LocalDate
import java.time.YearMonth

data class PayrollCycleDTO(
    val payFrequency: PayFrequency,
    val payrollMonth: YearMonth,
    val cutoffDate: LocalDate,
    val startDate: LocalDate,
    val endDate: LocalDate,
) {
    enum class PayFrequency {
        SEMIMONTHLY,
        MONTHLY,
        BIWEEKLY,
        WEEKLY
    }

    override fun equals(other: Any?): <PERSON>olean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PayrollCycleDTO

        return this.payFrequency == other.payFrequency &&
            this.payrollMonth == other.payrollMonth &&
            this.cutoffDate == other.cutoffDate
    }

    override fun hashCode(): Int {
        return (31 * payFrequency.hashCode() + payrollMonth.hashCode()) * 31 + cutoffDate.hashCode()
    }
}
