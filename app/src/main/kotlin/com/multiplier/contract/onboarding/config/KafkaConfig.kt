package com.multiplier.contract.onboarding.config

import com.github.daniel.shuy.kafka.protobuf.serde.KafkaProtobufDeserializer
import com.google.protobuf.MessageLite
import com.google.protobuf.Parser
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventMessage
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.common.serialization.StringDeserializer
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.annotation.EnableKafka
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory
import org.springframework.kafka.core.DefaultKafkaConsumerFactory

@EnableKafka
@Configuration
class KafkaConfig {

    @Value("\${platform.kafka.group-id}") private lateinit var kafkaGroupID: String

    @Value("\${platform.kafka.bootstrap-servers}")
    private lateinit var kafkaBootstrapServers: String

    @Value("\${platform.kafka.auto-startup}") private val autoStartup: Boolean = false

    @Bean
    fun contractEventConsumerFactory():
        ConcurrentKafkaListenerContainerFactory<String, ContractEventMessage> =
        getDefaultConsumerFactory(ContractEventMessage.parser())

    private fun <T : MessageLite> getDefaultConsumerFactory(
        valueDeserializer: Parser<T>
    ): ConcurrentKafkaListenerContainerFactory<String, T> =
        ConcurrentKafkaListenerContainerFactory<String, T>().apply {
            this.setConcurrency(1)
            this.setAutoStartup(autoStartup)
            this.consumerFactory =
                DefaultKafkaConsumerFactory(
                    mapOf(
                        ConsumerConfig.GROUP_ID_CONFIG to kafkaGroupID,
                        ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafkaBootstrapServers,
                    ),
                    StringDeserializer(),
                    KafkaProtobufDeserializer(valueDeserializer),
                )
        }
}
