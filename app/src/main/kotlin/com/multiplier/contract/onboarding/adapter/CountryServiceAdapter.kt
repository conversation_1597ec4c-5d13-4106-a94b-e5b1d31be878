package com.multiplier.contract.onboarding.adapter

import com.github.benmanes.caffeine.cache.Caffeine
import com.multiplier.contract.onboarding.domain.model.country.CompensationStandard
import com.multiplier.contract.onboarding.domain.model.country.CountryState
import com.multiplier.contract.onboarding.domain.model.member.ApplyTo
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataDefinition
import com.multiplier.contract.onboarding.domain.model.member.MemberLegalDataType
import com.multiplier.contract.onboarding.types.*
import com.multiplier.country.schema.Country.*
import com.multiplier.country.schema.Country.GrpcLegalDocumentRequirement.GrpcLegalDocumentCategory
import com.multiplier.country.schema.CountryServiceGrpc.CountryServiceBlockingStub
import com.multiplier.country.schema.contract.Contract
import com.multiplier.country.schema.legaldatadefinition.LegalDataDefinition
import com.multiplier.country.schema.legaldatadefinition.LegalDataDefinitionServiceGrpc
import com.multiplier.country.schema.legaldocumentrequirement.LegalDocumentRequirement
import com.multiplier.country.schema.legaldocumentrequirement.LegalDocumentRequirementServiceGrpc.LegalDocumentRequirementServiceBlockingStub
import java.time.Duration
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

data class CountryAndState(
    val country: CountryCode,
    val state: String? = null,
)

interface CountryServiceAdapter {
    fun getRequiredDocumentKeysGroupByCountryCode(
        countryCodes: Set<CountryAndState>,
        category: GrpcLegalDocumentCategory
    ): Map<CountryAndState, Set<String>>

    fun isOnboardingEnabled(countryAndState: CountryCodeState): Boolean

    fun getCountryNamesByCodes(codes: List<CountryCode>): Map<CountryCode, String>

    fun getCountryNameByCode(code: CountryCode): String?

    fun getMemberLegalDataDefinitions(
        countryCode: CountryCode,
        contractType: ContractType
    ): List<MemberLegalDataDefinition>

    fun getCompensationStandards(
        countryStates: Set<CountryState>
    ): Map<CountryState, CompensationStandard>

    fun getCountryStates(countryCodes: Set<CountryCode>): Map<CountryCode, List<CountryState>>

    fun getMainCurrency(countryCode: CountryCode): CurrencyCode

    fun getComplianceDefinitionForCountry(
        countryCode: CountryCode,
        contractType: ContractType
    ): GrpcComplianceRequirementDefinition?
}

private val log = KotlinLogging.logger {}

@Service
class CountryServiceAdapterImpl : CountryServiceAdapter {

    @GrpcClient("country-service")
    private lateinit var countryServiceStub: CountryServiceBlockingStub

    @GrpcClient("country-service")
    private lateinit var legalDocumentRequirementsStub: LegalDocumentRequirementServiceBlockingStub

    @GrpcClient("country-service")
    private lateinit var legalDataDefinitionServiceStub:
        LegalDataDefinitionServiceGrpc.LegalDataDefinitionServiceBlockingStub

    private var countriesLastUpdatedMillis = 0L
    private val updateCountriesEveryMillis = Duration.ofHours(1).toMillis()
    private var onboardingEnabledCountries: List<CountryCodeState>? = emptyList()

    private val countryNameCache =
        Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofHours(24))
            .build<CountryCode, String>()

    private val countryStateCache =
        Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofHours(24))
            .build<CountryCode, List<CountryState>>()

    private val countryMainCurrencyCache =
        Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(Duration.ofHours(24))
            .build<CountryCode, CurrencyCode>()

    override fun getRequiredDocumentKeysGroupByCountryCode(
        countryCodes: Set<CountryAndState>,
        category: GrpcLegalDocumentCategory
    ): Map<CountryAndState, Set<String>> {
        val requirementsByCountry =
            legalDocumentRequirementsStub
                .getLegalDocumentRequirementsGroupByCountryCodeAndState(
                    LegalDocumentRequirement.Request.GroupByCountryCodeAndState.newBuilder()
                        .addAllCountryCodeAndState(
                            countryCodes.map {
                                LegalDocumentRequirement.Request.GroupByCountryCodeAndState
                                    .GrpcCountryCodeAndState
                                    .newBuilder()
                                    .setCountryCode(GrpcCountryCode.valueOf(it.country.name))
                                    .setState(it.state ?: "")
                                    .build()
                            })
                        .build())
                .groupByCountryMap

        return requirementsByCountry.entries
            .flatMap { byCountry ->
                val requirementsWithoutState =
                    CountryAndState(country = CountryCode.valueOf(byCountry.key), state = null) to
                        byCountry.value.nullStateRequirements.legalDocumentRequirementList
                            .filter { it.required && it.category == category }
                            .map { it.key }
                            .toSet()

                val requirementsByState =
                    byCountry.value.groupByStateMap.map { byState ->
                        CountryAndState(
                            country = CountryCode.valueOf(byCountry.key), state = byState.key) to
                            byState.value.legalDocumentRequirementList
                                .filter { it.required && it.category == category }
                                .map { it.key }
                                .toSet()
                    }

                requirementsByState.plusElement(requirementsWithoutState)
            }
            .toMap()
    }

    override fun isOnboardingEnabled(countryAndState: CountryCodeState): Boolean =
        getOnboardingEnabledCountries().contains(countryAndState)

    override fun getCountryNameByCode(code: CountryCode): String? =
        getCountryNamesByCodes(listOf(code))[code]

    override fun getCountryNamesByCodes(codes: List<CountryCode>): Map<CountryCode, String> {
        return countryNameCache.getAll(codes) { missingCodes -> getCountryNames(missingCodes) }
    }

    override fun getMemberLegalDataDefinitions(
        countryCode: CountryCode,
        contractType: ContractType
    ): List<MemberLegalDataDefinition> =
        legalDataDefinitionServiceStub
            .getLegalDataDefinitions(
                LegalDataDefinition.GetLegalDataDefinitionsRequest.newBuilder()
                    .addCountryCodes(GrpcCountryCode.valueOf(countryCode.name))
                    .addContractTypes(Contract.GrpcContractType.valueOf(contractType.name))
                    .setIsActive(true)
                    .build())
            .definitionsList
            .map { it.toDomain() }

    override fun getCompensationStandards(
        countryStates: Set<CountryState>
    ): Map<CountryState, CompensationStandard> {
        val request =
            GetLabourStandardsRequest.newBuilder()
                .addAllCountryCodesAndStates(
                    countryStates.map {
                        CountryCodeState.newBuilder()
                            .setCountryCode(GrpcCountryCode.valueOf(it.country.name))
                            .setStateCode(it.code)
                            .build()
                    })
                .build()

        return countryServiceStub
            .getLabourStandards(request)
            .labourStandardsList
            .associateBy(
                { CountryState(CountryCode.valueOf(it.countryCode.name), it.stateCode) },
                { it.toCompensationStandard() })
    }

    override fun getCountryStates(
        countryCodes: Set<CountryCode>
    ): Map<CountryCode, List<CountryState>> {
        return countryStateCache.getAll(countryCodes) { missingCodes ->
            getCountryStatesWithoutCache(missingCodes)
        }
    }

    override fun getMainCurrency(countryCode: CountryCode): CurrencyCode {
        return countryMainCurrencyCache[
            countryCode,
            {
                CurrencyCode.valueOf(
                    countryServiceStub
                        .getMainCurrency(
                            GetMainCurrencyRequest.newBuilder()
                                .setCountryCode(GrpcCountryCode.valueOf(countryCode.name))
                                .build())
                        .currencyCode
                        .name)
            }]
    }

    override fun getComplianceDefinitionForCountry(
        countryCode: CountryCode,
        contractType: ContractType
    ): GrpcComplianceRequirementDefinition? {

        val request =
            GetComplianceRequirementDefinitionRequest.newBuilder()
                .setCountryCode(GrpcCountryCode.valueOf(countryCode.name))
                .setContractType(Contract.GrpcContractType.valueOf(contractType.name))
                .build()
        return countryServiceStub
            .getComplianceRequirementDefinition(request)
            .complianceRequirementDefinition
    }

    private fun getCountryStatesWithoutCache(
        countryCodes: Set<CountryCode>
    ): Map<CountryCode, List<CountryState>> {
        val request =
            GetCountryStatesRequest.newBuilder()
                .addAllCountryCodes(countryCodes.map { GrpcCountryCode.valueOf(it.name) })
                .build()

        return countryServiceStub.getCountryStates(request).countryStatesList.associate {
            val countryCode = CountryCode.valueOf(it.countryCode.name)
            countryCode to
                it.countryStatesList.map { state ->
                    CountryState(countryCode, state.code, state.name)
                }
        }
    }

    private fun getCountryNames(countryCodes: Set<CountryCode>): Map<CountryCode, String> =
        countryServiceStub
            .getCountryNamesByCodes(
                GetCountryNamesByCodesRequest.newBuilder()
                    .addAllCountryCodes(countryCodes.map { GrpcCountryCode.valueOf(it.name) })
                    .build())
            .countryNamesByCodesMap
            .entries
            .associate { CountryCode.valueOf(it.key) to it.value }

    private fun getOnboardingEnabledCountries(): List<CountryCodeState> {
        if (onboardingEnabledCountries == null || shouldRefreshCountries()) {
            onboardingEnabledCountries =
                countryServiceStub
                    .getAllOnboardingCountries(
                        GetAllOnboardingCountriesRequest.newBuilder().build())
                    .countryCodeStatesList

            countriesLastUpdatedMillis = System.currentTimeMillis()
        }

        return onboardingEnabledCountries ?: emptyList()
    }

    private fun shouldRefreshCountries() =
        System.currentTimeMillis() - countriesLastUpdatedMillis > updateCountriesEveryMillis
}

private fun LabourStandard.toCompensationStandard(): CompensationStandard {
    return CompensationStandard(
        this.compensationStandard.payrollFrequenciesList.map { PayFrequency.valueOf(it.name) },
        this.compensationStandard.rateFrequenciesList.map { RateFrequency.valueOf(it.name) })
}

private fun LegalDataDefinition.GrpcLegalDataDefinition.toDomain(): MemberLegalDataDefinition {
    return MemberLegalDataDefinition(
        key = this.key,
        type =
            when (this.config.valueCase) {
                GrpcDataFieldType.ValueCase.TEXTFIELD,
                GrpcDataFieldType.ValueCase.DROPDOWNTEXTFIELD -> MemberLegalDataType.SELECT
                GrpcDataFieldType.ValueCase.DROPDOWNFIELD -> MemberLegalDataType.SELECT
                GrpcDataFieldType.ValueCase.DATEFIELD -> MemberLegalDataType.DATE
                GrpcDataFieldType.ValueCase.CHECKBOXFIELD -> MemberLegalDataType.BOOLEAN
                else -> {
                    log.warn(
                        "{} type of field name: {} not recognised, fallback to TEXT field",
                        this.config.valueCase,
                        this.key)
                    MemberLegalDataType.TEXT
                }
            },
        mandatory = this.isRequired,
        description = this.description,
        allowedValues =
            when (this.config.valueCase) {
                GrpcDataFieldType.ValueCase.DROPDOWNTEXTFIELD ->
                    this.config.dropDownTextField.valuesList
                GrpcDataFieldType.ValueCase.DROPDOWNFIELD -> this.config.dropDownField.valuesList
                else -> emptyList()
            },
        fetchStage = FetchStage.valueOf(this.fetchStage.name),
        domainType = DomainType.valueOf(this.domainType.name),
        applyTo = this.applyTo.toDomain())
}

private fun GrpcLegalDataRequirementApplicability.toDomain(): ApplyTo =
    when (this) {
        GrpcLegalDataRequirementApplicability.ALL -> ApplyTo.ALL
        GrpcLegalDataRequirementApplicability.LOCAL_ONLY -> ApplyTo.LOCAL_ONLY
        GrpcLegalDataRequirementApplicability.EXPAT_ONLY -> ApplyTo.EXPAT_ONLY
        else -> ApplyTo.UNRECOGNIZED
    }
