package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.bulk.BulkTimeoffServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import org.springframework.stereotype.Service

@Service
class BulkTimeoffDataService(private val bulkTimeoffServiceAdapter: BulkTimeoffServiceAdapter) {
    fun getDataSpecs(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.EOR -> dataSpecsForEOR()
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Leave data specs query for ${onboardingOptions.context} not supported yet")
        }
    }

    private fun dataSpecsForEOR(): List<DataSpec> {
        return emptyList()
    }

    fun createDefaultTimeoffEntitlementForContracts(
        contractContext: ContractContext,
        options: BulkOnboardingOptions
    ): List<String> {
        val createDefaultTimeoffEntitlementInputs =
            contractContext.requestIdToContractId.map {
                CreationInput(requestId = it.key, contractId = it.value, data = it.value)
            }
        val results =
            bulkUpsert(
                "Create default timeoff entitlement",
                createDefaultTimeoffEntitlementInputs,
                options,
                CreationInput.refContractId) { contractInputs, _ ->
                    bulkTimeoffServiceAdapter.createDefaultTimeoffEntitlementForContracts(
                        contractInputs)
                }

        return results.flatMap { it.errors }
    }
}
