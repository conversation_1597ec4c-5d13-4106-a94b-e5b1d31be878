package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.CompensationSourceServiceAdapter
import com.multiplier.contract.onboarding.adapter.DepartmentServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.domain.model.bulk.contractAndMemberData
import com.multiplier.contract.onboarding.domain.model.bulk.memberData
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.FirstNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.LastNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberLegalDataService
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2.Companion.COMPENSATION_SERVICE_KEYS.EMPLOYEE_FULL_NAME
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkContractActivationModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkContractModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkContractOnboardingModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberLegalDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkOrgManagementModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkTimeOffModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.compensationData
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.onboarding.usecase.ModuleParams
import mu.KotlinLogging
import org.springframework.stereotype.Service

/**
 * This is version 3 of the GlobalPayrollBulkOnboardingUseCase. The main purpose of this use case
 * remains the same as the previous versions. The main difference is that this version supports the
 * new bulk uploading interface defined by bulk-upload-service.
 */
@Service
class GlobalPayrollBulkOnboardingUseCaseV2(
    private val bulkContractModule: BulkContractModule,
    private val bulkMemberModule: BulkMemberModule,
    private val bulkContractOnboardingModule: BulkContractOnboardingModule,
    private val bulkCompensationModule: BulkCompensationModuleV2,
    private val bulkMemberLegalDataModule: BulkMemberLegalDataModule,
    private val bulkMemberBankDataModule: BulkMemberBankDataModule,
    private val bulkOrgManagementModule: BulkOrgManagementModule,
    private val bulkTimeOffModule: BulkTimeOffModule,
    private val companyServiceAdapter: CompanyServiceAdapter,
    private val departmentServiceAdapter: DepartmentServiceAdapter,
    private val compensationSourceServiceAdapter: CompensationSourceServiceAdapter,
    private val bulkContractActivationModule: BulkContractActivationModule,
) : BulkOnboardingUseCase() {
    private val log = KotlinLogging.logger {}

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        validateBulkOnboardingCapabilityOrThrow(onboardingOptions)
        return super.getDataSpecs(onboardingOptions, moduleParams).map {
            it.copy(group = groupBySource(it.source))
        }
    }

    override fun getBulkOnboardingModules(): List<BulkDataModule> {
        return listOf(
            bulkContractModule,
            bulkMemberModule,
            bulkContractOnboardingModule,
            bulkMemberLegalDataModule,
            bulkMemberBankDataModule,
            bulkCompensationModule,
            bulkOrgManagementModule,
            bulkTimeOffModule,
            bulkContractActivationModule)
    }

    override fun getFieldData(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions,
        pageRequest: PageRequest?,
    ): List<EmployeeDataChunk> {
        log.info { "Query field data for options: $options" }
        val employeeDataChunks = getDataForSpecs(dataSpecs, options, pageRequest)
        val contractIds =
            employeeDataChunks
                .mapNotNull { it.data[ContractIdSpec.key] }
                .distinct()
                .mapNotNull { it.toLongOrNull() }
        val compensationDataChunks = bulkCompensationModule.getFieldData(options, contractIds)
        return employeeDataChunks +
            enrichWithEmployeeName(compensationDataChunks, employeeDataChunks)
    }

    override fun validate(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): BulkValidationResultV2 {
        validateBulkOnboardingCapabilityOrThrow(options)
        val fixedEmployeeDataInput = disableUpsertCapability(employeeDataInput)
        val bulkValidationResults = super.validate(fixedEmployeeDataInput, options)
        return addGroupToValidationResults(bulkValidationResults)
    }

    override fun create(
        bulkValidationResult: BulkValidationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        validateBulkOnboardingCapabilityOrThrow(options)
        return super.create(bulkValidationResult, options)
    }

    private fun addGroupToValidationResults(bulkValidationResult: BulkValidationResultV2) =
        bulkValidationResult.copy(
            validationResults =
                bulkValidationResult.validationResults.mapValues {
                    it.value.map { validationResult ->
                        validationResult.copy(groupName = groupByModule(it.key))
                    }
                })

    override fun filterEmployeeDataForModule(
        employeeData: List<EmployeeData>,
        moduleIdentifier: String
    ): List<EmployeeData> =
        when (moduleIdentifier) {
            BulkMemberLegalDataModule.MODULE_NAME -> employeeData.memberData()
            BulkMemberBankDataModule.MODULE_NAME -> employeeData.memberData()
            BulkCompensationModuleV2.MODULE_NAME ->
                employeeData.compensationData(BulkOnboardingContext.GLOBAL_PAYROLL)
            else -> employeeData.contractAndMemberData()
        }

    fun isNewCompensationServiceIntegrationEnabled(entityId: Long) =
        compensationSourceServiceAdapter.isNewCompensationSourceEnabledForEntityId(entityId)

    private fun groupBySource(source: String?) =
        when (source) {
            BulkCompensationModuleV2.COMPENSATION_SCHEMA_DATA_SPEC ->
                EmployeeData.COMPENSATION_DATA_GROUP
            BulkMemberLegalDataService.LEGAL_SPEC_DATA,
            BulkMemberBankDataModule.STATIC_BANK_DETAILS_SPEC,
            BulkMemberBankDataModule.DYNAMIC_BANK_DETAILS_SPEC -> EmployeeData.MEMBER_DATA_GROUP
            else -> EmployeeData.EMPLOYMENT_DATA_GROUP
        }

    private fun groupByModule(module: String) =
        when (module) {
            BulkMemberLegalDataModule.MODULE_NAME -> EmployeeData.MEMBER_DATA_GROUP
            BulkMemberBankDataModule.MODULE_NAME -> EmployeeData.MEMBER_DATA_GROUP
            BulkCompensationModuleV2.MODULE_NAME -> EmployeeData.COMPENSATION_DATA_GROUP
            else -> EmployeeData.EMPLOYMENT_DATA_GROUP
        }

    private fun validateBulkOnboardingCapabilityOrThrow(onboardingOptions: BulkOnboardingOptions) {
        validateEntityCapabilitiesOrThrow(onboardingOptions.companyId, onboardingOptions.entityId)
        validateCompanyHasDepartmentsOrThrow(onboardingOptions.companyId)
    }

    private fun validateEntityCapabilitiesOrThrow(companyId: Long, entityId: Long) {
        val offerings = companyServiceAdapter.getCompanyOfferings(companyId).map { it.offeringCode }
        if (!offerings.contains(OfferingCode.GLOBAL_PAYROLL.name)) {
            throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                "Company with id $companyId doesn't have capability to process global payroll")
        }
        val capabilities =
            companyServiceAdapter.getLegalEntity(entityId)?.capabilitiesList.orEmpty()
        if (capabilities.none {
            it == Capability.GROSS_TO_NET.name || it == Capability.SALARY_DISBURSEMENT.name
        }) {
            throw ErrorCodes.VALIDATION_FAILED.toBusinessException(
                "Legal entity with id $entityId doesn't have capability to process global payroll")
        }
    }

    private fun validateCompanyHasDepartmentsOrThrow(companyId: Long) {
        if (departmentServiceAdapter.getDepartments(companyId).isEmpty()) {
            throw ErrorCodes.COMPANY_HAS_NO_DEPARTMENTS.toBusinessException(
                "Company with id $companyId doesn't have any departments")
        }
    }

    private fun enrichWithEmployeeName(
        compensationDataChunks: List<EmployeeDataChunk>,
        employeeDataChunks: List<EmployeeDataChunk>
    ): List<EmployeeDataChunk> {
        val employeeNameByContractId =
            employeeDataChunks
                .filter {
                    it.data.containsKey(ContractIdSpec.key) &&
                        it.data.containsKey(FirstNameSpec.key) &&
                        it.data.containsKey(LastNameSpec.key)
                }
                .associate {
                    it.data.getValue(ContractIdSpec.key) to
                        "${it.data.getValue(FirstNameSpec.key)} ${it.data.getValue(LastNameSpec.key)}"
                }
        return compensationDataChunks.map {
            val contractId = it.data.getValue(ContractIdSpec.key)
            val employeeName = employeeNameByContractId.getOrDefault(contractId, "")
            it.copy(data = it.data + mapOf(EMPLOYEE_FULL_NAME to employeeName))
        }
    }

    private fun disableUpsertCapability(employeeDataInput: EmployeeDataInput) =
        employeeDataInput.copy(
            employeeData =
                employeeDataInput.employeeData.map {
                    // by removing contractId, input data will be treated as data for new contract
                    it.copy(data = it.data - "contractId")
                })
}
