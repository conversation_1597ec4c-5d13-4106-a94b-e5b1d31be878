package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.DgsConstants
import com.multiplier.contract.onboarding.service.reminders.EmployeeIdAndWorkEmailReminders
import com.multiplier.contract.onboarding.service.reminders.OnboardingCtaReminderService
import com.multiplier.contract.onboarding.service.reminders.OnboardingReminders
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.onboarding.types.TaskResponse
import com.multiplier.transaction.graphql.graphApi
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import java.time.LocalDate
import mu.KotlinLogging
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class OnboardingRemindersMutator(
    private val onboardingReminders: OnboardingReminders,
    private val employeeIdAndWorkEmailReminders: EmployeeIdAndWorkEmailReminders,
    private val onboardingCtaReminderService: OnboardingCtaReminderService,
) {

    private val log = KotlinLogging.logger {}

    @PreAuthorize("@me.allowed('trigger.operations.onboarding.notification')")
    @DgsMutation(field = DgsConstants.MUTATION.TriggerOnboardingReminderEmailProcess)
    fun triggerOnboardingReminderEmailProcess(
        @InputArgument simulatedSendingDate: LocalDate,
        @InputArgument companyIds: List<Long>,
    ): TaskResponse = graphApi {
        log.info {
            "Sending onboarding reminders manually, sendingDate = $simulatedSendingDate, companyIds = $companyIds"
        }
        onboardingReminders.sendOnboardingRemindersBeforePayrollCutoffDate(
            simulatedSendingDate, companyIds)
        onboardingReminders.sendOnboardingRemindersBeforeContractStartDate(
            simulatedSendingDate, companyIds)

        TaskResponse(true, "Onboarding reminders sent successfully")
    }

    @PreAuthorize("@me.allowed('trigger.operations.onboarding.notification')")
    @DgsMutation(
        field = DgsConstants.MUTATION.TriggerCompleteWorkEmailAndEmployeeIdReminderEmailProcess)
    fun triggerCompleteWorkEmailAndEmployeeIdReminderEmailProcess(
        @InputArgument simulatedSendingDate: LocalDate,
        @InputArgument companyIds: List<Long>,
    ): TaskResponse = graphApi {
        log.info {
            "Sending employeeId and work email reminders manually, sendingDate = $simulatedSendingDate, companyIds = $companyIds"
        }
        employeeIdAndWorkEmailReminders.sendEmployeeIdAndWorkEmailTaskReminders(
            simulatedSendingDate, companyIds)

        TaskResponse(
            true, "Onboarding reminders - employeeId and work email task - sent successfully")
    }

    @PreAuthorize("@me.allowed('trigger.operations.onboarding-reminder.cta')")
    @DgsMutation(field = DgsConstants.MUTATION.SendOpsEmailCtaReminder)
    fun sendOpsEmailCtaReminder(
        @InputArgument contractId: Long,
        @InputArgument templateName: NotificationType,
    ): TaskResponse = graphApi {
        log.info {
            "Sending cta reminder for, contract_id = $contractId, notificationType = ${templateName.name}"
        }
        onboardingCtaReminderService.sendReminderForTemplate(contractId, templateName)

        TaskResponse(true, "Onboarding CTA reminders - sent successfully")
    }

    @PreAuthorize("@me.allowed('trigger.operations.onboarding-reminder.cta')")
    @DgsMutation(field = DgsConstants.MUTATION.SendEditableOpsEmailCtaReminder)
    fun sendEditableOpsEmailCtaReminder(
        @InputArgument contractId: Long,
        @InputArgument templateName: NotificationType,
        @InputArgument editableTemplateParams: Map<String, String>?,
    ): TaskResponse = graphApi {
        log.info {
            "Sending cta reminder for, contract_id = $contractId, notificationType = ${templateName.name}, editableTemplateParams = $editableTemplateParams"
        }
        onboardingCtaReminderService.sendReminderForTemplate(
            contractId, templateName, editableTemplateParams)

        TaskResponse(true, "Onboarding CTA reminders - sent successfully")
    }
}
