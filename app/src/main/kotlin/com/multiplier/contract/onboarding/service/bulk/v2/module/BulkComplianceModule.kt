package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkComplianceServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.NonCompeteUnitSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.NonCompeteValueSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.NonSolicitUnitSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.NonSolicitValueSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.NoticeAfterProbationUnitSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.NoticeAfterProbationValueSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.ProbationUnitSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkComplianceDataService.Companion.ProbationValueSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.RowIdentifierSpec
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ComplianceParamPeriodUnit
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.contract.schema.contract.BulkContract.UpdateComplianceInput
import org.springframework.stereotype.Service

@Service
class BulkComplianceModule(
    private val complianceServiceAdapter: BulkComplianceServiceAdapter,
    private val countryServiceAdapter: CountryServiceAdapter,
) : BulkDataModule {

    companion object {
        const val MODULE_NAME = "COMPLIANCE_MODULE"
        const val COMPLIANCE_SCHEMA_DATA_SPEC = "COMPLIANCE_SCHEMA_DATA_SPEC"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        return when (onboardingOptions.context) {
            BulkOnboardingContext.EOR -> {
                listOf(RowIdentifierSpec).plus(getEorComplianceSpecs(onboardingOptions)).map {
                    it.copy(source = COMPLIANCE_SCHEMA_DATA_SPEC)
                }
            }
            else -> {
                listOf(
                    NoticeAfterProbationValueSpec.copy(
                        description = "Notice period for the contractor after probation"),
                    NoticeAfterProbationUnitSpec.copy(
                        description = "Unit of Notice period",
                        mandatory = false,
                        allowedValues =
                            getNoticeAfterProbationUnitStrings(ComplianceParamPeriodUnit.DAYS)))
            }
        }
    }

    private fun DataSpec.complianceKey() = key.substringBeforeLast(".")

    private fun DataSpec.compliancePostfix() = key.substringAfterLast(".")

    fun getNoticeAfterProbationUnitStrings(vararg units: ComplianceParamPeriodUnit): List<String> {
        return units.map { it.name }
    }

    fun getEorComplianceSpecs(onboardingOptions: BulkOnboardingOptions): List<DataSpec> {
        val paramDefinitionsList =
            countryServiceAdapter
                .getComplianceDefinitionForCountry(
                    onboardingOptions.countryCode, ContractType.EMPLOYEE)
                ?.paramDefinitionsList
                ?.filter { it.enabled }
                .orEmpty()

        return listOf(
                ProbationValueSpec,
                ProbationUnitSpec,
                NoticeAfterProbationValueSpec,
                NoticeAfterProbationUnitSpec,
                NonCompeteValueSpec,
                NonCompeteUnitSpec,
                NonSolicitValueSpec,
                NonSolicitUnitSpec)
            .mapNotNull { spec ->
                val key = spec.complianceKey()
                val definition =
                    paramDefinitionsList.firstOrNull {
                        it.param.complianceParamPeriodLimit.key == key
                    }

                if (definition == null) {
                    null
                } else {
                    when (spec.compliancePostfix()) {
                        "value" -> spec.copy(mandatory = false)
                        "unit" ->
                            spec.copy(
                                mandatory = false,
                                allowedValues =
                                    listOf(definition.param.complianceParamPeriodLimit.unit.name))
                        else -> null
                    }
                }
            }
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        // we are removing empty data here so that incase optional "noticeAfterProbation.value" and
        // "noticeAfterProbation.unit" can be removed.
        // this is because we get response like "noticeAfterProbation": {} if empty values are sent
        // and when this empty struct is sent in upsert this would fail/
        // Therefore we will send keys with values to validate.
        val cleanedEmployeeData = employeeData.map { it.removeEmptyValues() }
        return complianceServiceAdapter.validateUpdateComplianceInputs(cleanedEmployeeData, options)
    }

    private fun EmployeeData.removeEmptyValues(): EmployeeData {
        return this.copy(data = this.data.filterValues { it.isNotBlank() })
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {

        val updateContractComplianceInputs =
            validationResults
                .filterNot { it.input == UpdateComplianceInput.getDefaultInstance() }
                .map {
                    CreationInput(
                        requestId = it.validationId,
                        contractId = bulkCreationResult.getContractId(it.validationId),
                        data = it.input as UpdateComplianceInput)
                }

        return bulkUpsert(
            "Update compliance",
            updateContractComplianceInputs,
            options,
            bulkCreationResult,
            CreationInput.refContractId) { complianceInputs, onboardingOptions ->
                complianceServiceAdapter.updateCompliances(complianceInputs, onboardingOptions)
            }
    }
}
