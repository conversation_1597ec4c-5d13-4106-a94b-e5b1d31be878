package com.multiplier.contract.onboarding.service.extensions

import com.multiplier.common.transport.user.CurrentUser

val CurrentUser.userId: Long
    get() = this.context?.id!!

val CurrentUser.partnerId: Long?
    get() = this.context?.scopes?.partnerId

val CurrentUser.isPartnerExperience: Boolean
    get() = this.context?.experience.equals("partner", ignoreCase = true)

val CurrentUser.isCompanyExperience: Boolean
    get() = this.context?.experience.equals("company", ignoreCase = true)

val CurrentUser.isMemberExperience: Boolean
    get() = this.context?.experience.equals("member", ignoreCase = true)

val CurrentUser.isOperationsExperience: Boolean
    get() = this.context?.experience.equals("operations", ignoreCase = true)
