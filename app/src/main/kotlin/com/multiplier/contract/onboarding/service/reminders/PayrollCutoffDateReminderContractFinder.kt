package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class PayrollCutoffDateReminderContractFinder(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val reminderContractFinderHelper: ReminderContractFinderHelper,
) {

    fun findBy(
        sendingDate: LocalDate,
        companyIds: Set<Long>,
    ): List<PayrollCycleContracts> {
        val cutoffDatesForNotification = getCutoffDatesForNotification(sendingDate)

        val contracts =
            getContractsForPayrollCutoffDateReminder(sendingDate, companyIds)
                .let(reminderContractFinderHelper::filterEligibleContracts)

        log.info(
            "Before filtering, found {} contracts for payroll cutoff date reminder", contracts.size)

        val payrollCycleContractsList =
            reminderContractFinderHelper
                .parkContractsIntoPayrollCycles(contracts, sendingDate)
                .filter { it.cutoffDate in cutoffDatesForNotification }

        if (payrollCycleContractsList.isEmpty()) {
            return emptyList()
        }

        log.info(
            "Found {} contracts for {} payroll cycles",
            payrollCycleContractsList.flatMap { it.contractIds }.size,
            payrollCycleContractsList.size)
        return payrollCycleContractsList
    }

    private fun getContractsForPayrollCutoffDateReminder(
        sendingDate: LocalDate,
        companyIds: Set<Long>,
    ): List<Contract> {
        return contractServiceAdapter.getContractsBy(
            ContractFilters(
                type = ContractOuterClass.ContractType.EMPLOYEE,
                status = ContractOuterClass.ContractStatus.ONBOARDING,
                startDateRange =
                    DateRange(
                        from = sendingDate.minusMonths(2).atStartOfMonth(),
                        to = sendingDate.plusMonths(1).atEndOfMonth()),
                companyIds = companyIds,
            ))
    }

    private fun getCutoffDatesForNotification(sendingDate: LocalDate): Set<LocalDate> {
        return setOf(
            sendingDate,
            sendingDate.plusDays(1),
            sendingDate.plusDays(2),
            sendingDate.plusDays(4),
            sendingDate.plusDays(6),
            sendingDate.plusDays(8),
            sendingDate.plusDays(11),
            sendingDate.plusDays(14),
            sendingDate.plusDays(17),
            sendingDate.plusDays(20))
    }
}

private fun LocalDate.atStartOfMonth(): LocalDateTime = withDayOfMonth(1).atStartOfDay()

private fun LocalDate.atEndOfMonth(): LocalDateTime =
    withDayOfMonth(lengthOfMonth()).atTime(LocalTime.MAX)
