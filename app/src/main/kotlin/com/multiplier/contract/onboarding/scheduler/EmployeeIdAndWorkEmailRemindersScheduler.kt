package com.multiplier.contract.onboarding.scheduler

import com.multiplier.contract.onboarding.config.SchedulerConfig
import com.multiplier.contract.onboarding.service.reminders.EmployeeIdAndWorkEmailReminders
import java.time.LocalDateTime
import java.util.*
import mu.KotlinLogging
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class EmployeeIdAndWorkEmailRemindersScheduler(
    private val employeeIdAndWorkEmailReminders: EmployeeIdAndWorkEmailReminders,
) {
    @Scheduled(cron = "0 0 0 * * *", zone = "Asia/Singapore") // At 0 am - Everyday
    @SchedulerLock(
        name = "OnboardingServiceEmployeeIdAndWorkEmailIncompleteReminders",
        lockAtLeastFor = SchedulerConfig.LOCK_AT_LEAST_FOR,
        lockAtMostFor = SchedulerConfig.LOCK_AT_MOST_FOR)
    fun sendEmployeeIdAndWorkEmailIncompleteReminders() {
        val lockId = UUID.randomUUID()
        log.info(
            "[{}] scheduled job (lock id:- {}) ran at {} - START",
            "EmployeeIdAndWorkEmailIncompleteReminder",
            lockId,
            LocalDateTime.now())

        employeeIdAndWorkEmailReminders.sendEmployeeIdAndWorkEmailTaskReminders(null)

        log.info(
            "[{}] scheduled job (lock id:- {}) ran at {} - Done",
            "EmployeeIdAndWorkEmailIncompleteReminder",
            lockId,
            LocalDateTime.now())
    }
}
