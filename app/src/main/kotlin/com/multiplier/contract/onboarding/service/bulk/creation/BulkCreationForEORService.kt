package com.multiplier.contract.onboarding.service.bulk.creation

import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationResults
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import java.util.concurrent.CompletableFuture
import org.springframework.stereotype.Service

@Service
class BulkCreationForEORService(
    private val bulkMemberDataService: BulkMemberDataService,
    private val bulkMemberLegalDataService: BulkMemberLegalDataService,
    private val bulkContractDataService: BulkContractDataService,
    private val bulkOnboardingDataService: BulkOnboardingDataService,
    private val bulkComplianceDataService: BulkComplianceDataService,
    private val bulkCompensationDataService: BulkCompensationDataService,
    private val bulkTimeoffDataService: BulkTimeoffDataService,
    private val bulkPostOnboardingService: BulkPostOnboardingService,
) : BulkCreationServiceInterface {

    override fun create(
        validationResults: EmployeeValidationResults,
        options: BulkOnboardingOptions
    ): BulkCreationResult {
        val memberContext =
            bulkMemberDataService.createMembers(
                inputs = validationResults.members.mapNotNull { it.toValidInputOrNull() },
                options = options)

        val legalDataErrors =
            bulkMemberLegalDataService.createLegalData(
                inputs = validationResults.legalData.mapNotNull { it.toValidInputOrNull() },
                memberContext = memberContext,
                options = options,
            )

        val upsertContractInputs =
            validationResults.contracts.mapNotNull { it.toValidInputOrNull() }
        val contractContext =
            bulkContractDataService.createContracts(
                inputs = upsertContractInputs, memberContext = memberContext, options = options)

        val newlyCreatedContractIds =
            BulkCreationHelper.getNewlyCreatedContractIds(contractContext, upsertContractInputs)
        val existingContractIds = contractContext.contractIds - newlyCreatedContractIds.toSet()
        val onboardingErrors =
            bulkOnboardingDataService.createOnboardingEntities(
                newlyCreatedContractIds, options = options, contractContext, validationResults)
        val updateOnboardingErrors =
            bulkOnboardingDataService.updateOnboardingForDraftContracts(
                existingContractIds, contractContext, validationResults)

        val complianceErrors =
            bulkComplianceDataService.updateContractCompliance(
                inputs = validationResults.compliances.mapNotNull { it.toValidInputOrNull() },
                options = options,
                contractContext = contractContext)

        val compensationErrors =
            bulkCompensationDataService.createCompensations(
                inputs = validationResults.compensations.mapNotNull { it.toValidInputOrNull() },
                contractContext = contractContext,
                options = options)

        val timeoffErrors =
            bulkTimeoffDataService.createDefaultTimeoffEntitlementForContracts(
                contractContext = contractContext, options = options)

        val creationResult =
            BulkCreationResult(
                memberContext = memberContext,
                contractContext = contractContext,
                errors =
                    onboardingErrors +
                        updateOnboardingErrors +
                        legalDataErrors +
                        complianceErrors +
                        timeoffErrors +
                        compensationErrors)

        // Generate contract for success creation
        if (creationResult.success) {
            CompletableFuture.supplyAsync {
                bulkPostOnboardingService.triggerPostOnboardingActions(
                    contractContext.contractIds.toList(), options)
            }
        }

        return creationResult
    }
}
