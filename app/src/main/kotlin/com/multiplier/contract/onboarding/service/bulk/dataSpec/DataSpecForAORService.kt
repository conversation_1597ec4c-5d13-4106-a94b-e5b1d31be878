package com.multiplier.contract.onboarding.service.bulk.dataSpec

import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import org.springframework.stereotype.Service

@Service
class DataSpecForAORService(
    private val bulkMemberDataService: BulkMemberDataService,
    private val bulkContractDataService: BulkContractDataService,
    private val bulkCompensationDataService: BulkCompensationDataService,
    private val bulkComplianceDataService: BulkComplianceDataService,
) : BulkDataSpecsServiceInterface {

    override fun getDataSpecs(options: BulkOnboardingOptions): List<DataSpec> {
        return bulkMemberDataService.getDataSpecs(options) +
            bulkContractDataService.getDataSpecs(options) +
            bulkComplianceDataService.getDataSpecs(options) +
            bulkCompensationDataService.getDataSpecs(options)
    }
}
