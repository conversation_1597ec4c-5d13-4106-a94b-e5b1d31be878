package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.service.bulk.validation.*
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.types.BulkOnboardingJob
import com.multiplier.contract.onboarding.usecase.BulkOnboardingServiceFactory
import com.multiplier.transaction.graphql.graphApi
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.multipart.MultipartFile

@DgsComponent
class BulkOnboardingDataMutator(
    private val bulkOnboardingServiceFactory: BulkOnboardingServiceFactory
) {

    @PreAuthorize(
        "( (@me.allowed('create.company.contract') && @me.isCompanyUserForCompanyId(#options.getCompanyId())) " +
            "|| @me.allowed('create.operations.contract') " +
            ")")
    @DgsMutation
    fun bulkOnboardingTrigger(
        @InputArgument employeeDataFile: MultipartFile,
        @InputArgument options: BulkOnboardingOptions
    ): BulkOnboardingJob = graphApi {
        val onboardingJob =
            bulkOnboardingServiceFactory
                .getBulkOnboardingService(options.context)
                .onboard(
                    employeeDataFile = employeeDataFile.inputStream,
                    options = options.applyDefaultContextIfMissing())

        BulkOnboardingJob.newBuilder()
            .id(onboardingJob.id)
            .startTime(onboardingJob.startTime)
            .status(onboardingJob.status)
            .validationResult(onboardingJob.validationReport.toGraph())
            .onboardingResult(
                BulkOnboardingResult.newBuilder()
                    .onboardedEmployeeCount(onboardingJob.onboardedEmployeeCount)
                    .failedEmployeeCount(
                        onboardingJob.totalEmployeeCount - onboardingJob.onboardedEmployeeCount)
                    .build())
            .build()
    }

    @PreAuthorize(
        "( (@me.allowed('create.company.contract') && @me.isCompanyUserForCompanyId(#options.getCompanyId())) " +
            "|| @me.allowed('create.operations.contract') " +
            ")")
    @DgsMutation
    fun bulkOnboardingValidate(
        @InputArgument employeeDataFile: MultipartFile,
        @InputArgument options: BulkOnboardingOptions
    ): BulkOnboardingValidationResult {
        val validationReport =
            bulkOnboardingServiceFactory
                .getBulkOnboardingService(options.context)
                .validate(
                    employeeDataFile = employeeDataFile.inputStream,
                    options = options.applyDefaultContextIfMissing(),
                    source = "GraphQL")

        return validationReport.toGraph()
    }
}

private fun BulkValidationReport.toGraph(): BulkOnboardingValidationResult {
    return BulkOnboardingValidationResult.newBuilder()
        .errors(this.genericValidationResult.errors.map { it.toGraph() })
        .warnings(this.genericValidationResult.warnings.map { it.toGraph() })
        .validEmployeeCount(this.employeeValidationResult.validEmployeeCount)
        .errorEmployeeCount(this.employeeValidationResult.invalidEmployeeCount)
        .totalEmployeeCount(this.employeeValidationResult.totalEmployeeCount)
        .validationErrors(this.employeeValidationResult.errors.map { it.toGraph() })
        .validationResultFile(this.errorReportFile?.toGraph())
        .build()
}

private fun EmployeeValidationError.toGraph(): BulkOnboardingMessage =
    BulkOnboardingMessage.newBuilder()
        .message(this.error)
        .rowNumber(this.rowNumber)
        .employeeName(this.employeeName)
        .build()

private fun GenericValidationError.toGraph(): BulkOnboardingMessage {
    return BulkOnboardingMessage.newBuilder().message(this.error).rowNumber(this.rowNumber).build()
}

private fun GenericValidationWarning.toGraph(): BulkOnboardingMessage {
    return BulkOnboardingMessage.newBuilder()
        .message(this.warning)
        .rowNumber(this.rowNumber)
        .build()
}

private fun EmployeeValidationReportFile.toGraph(): DocumentReadable {
    return DocumentReadable.newBuilder()
        .id(this.id)
        .name(this.name)
        .extension(this.extension)
        .contentType(this.contentType)
        .blob(this.blob)
        .build()
}
