package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.bulk.BulkTimeoffServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.usecase.ModuleParams
import org.springframework.stereotype.Service

@Service
class BulkTimeOffModule(private val bulkTimeOffServiceAdapter: BulkTimeoffServiceAdapter) :
    BulkDataModule {

    companion object {
        const val MODULE_NAME = "TIME_OFF_MODULE"
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        // no validation
        return emptyList()
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        val createDefaultTimeoffInputs =
            bulkCreationResult.newContractIds.map {
                CreationInput(
                    requestId =
                        requireNotNull(bulkCreationResult.getRequestIdForContract(it)) {
                            "Request ID for given contract ID: $it not found"
                        },
                    contractId = it,
                    data = it)
            }

        return bulkUpsert(
            "Create default timeoff entitlement",
            createDefaultTimeoffInputs,
            options,
            bulkCreationResult,
            CreationInput.refContractId) { inputs, _ ->
                bulkTimeOffServiceAdapter.createDefaultTimeoffEntitlementForContracts(inputs)
            }
    }
}
