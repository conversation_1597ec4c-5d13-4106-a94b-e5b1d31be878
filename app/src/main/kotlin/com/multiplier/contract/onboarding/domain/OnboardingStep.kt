package com.multiplier.contract.onboarding.domain

enum class OnboardingStep {
    START,
    DEFINITION_MEMBER_ELIGIBILITY,
    DEFINITION_MEMBER_BASIC_DETAILS,
    DEFINITION_COUNTRY_SPECIFIC_DETAILS,
    DEFINITION_COMPENSATION_DETAILS,
    DEFINITION_<PERSON><PERSON><PERSON>_DETAILS,
    DEFINITION_BENEFITS_DETAILS,
    DEFINITION_COMPLIANCE_DETAILS,
    <PERSON>FINITION_DESCRIPTION_OF_WORK,
    DEFINITION_CONTRACT,
    ONBOARDING_REVIEW,
    ONBOARDING_SIGNING,
    ONBOARDING_CONTRACT,
    ONBOARDING_CONTRACT_PREPARING_CONFIRMATION,
    VISA_ELIGIBILITY,
    ONBOARDING_CONTRACT_UPDATING,
    VISA_APPLICATION,
    ONBOARDING_MEMBER,
    ONBOARDING_MSA_PENDING,
    ONBOARDING_ACTIVATION,
    ONBOARDING_VERIFYING,
    MEMBER_WELCOME,
    MEMBER_WELCOME_VISA,
    MEMBER_VISA_ELIGIBILITY,
    MEMBER_DOCUMENTS_SUBMITTED,
    MEMBER_VISA_APPLICATION,
    MEMBER_BASIC_DETAILS,
    MEMBER_VISA_APPROVED,
    MEMBER_LEGAL_DETAILS,
    MEMBER_BANK_DETAILS,
    MEMBER_LEGAL_DOCUMENTS,
    MEMBER_BENEFIT_DETAILS,
    MEMBER_REVIEW,
    ONBOARDING_MULTIPLIER_SIGNING,
    ONBOARDED,
    ONBOARDING_CONTRACT_WET_INK_PREPARING_CONFIRMATION,
    ONBOARDING_CONTRACT_WET_INK_PREPARING,
}

fun OnboardingStep?.isAfter(other: OnboardingStep?): Boolean =
    if (this == null || other == null) false else this.ordinal > other.ordinal
