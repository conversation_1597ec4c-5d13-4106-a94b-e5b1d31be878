package com.multiplier.contract.onboarding.graphql.dataloader

import com.multiplier.contract.onboarding.DgsConstants
import com.multiplier.contract.onboarding.domain.model.mapToGraph
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.service.OnboardingTasksService
import com.multiplier.contract.onboarding.types.OnboardingTask
import com.multiplier.transaction.graphql.futureGraphApi
import com.netflix.graphql.dgs.DgsDataLoader
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionStage
import mu.KotlinLogging
import org.dataloader.MappedBatchLoader

private val log = KotlinLogging.logger {}

@DgsDataLoader(name = DgsConstants.ONBOARDINGTASK.TYPE_NAME, maxBatchSize = 500)
class OnboardingTasksDataLoader(
    private val onboardingTasksService: OnboardingTasksService,
    private val featureFlagService: FeatureFlagService,
) : MappedBatchLoader<Long, List<OnboardingTask>> {

    override fun load(onboardingIds: Set<Long>): CompletionStage<Map<Long, List<OnboardingTask>>> {
        if (onboardingIds.isEmpty()) {
            return CompletableFuture.completedFuture(emptyMap())
        }

        return futureGraphApi {
                onboardingTasksService.getOnboardingTasksByOnboardingId(onboardingIds).mapValues {
                    it.value.mapIndexed { index, onboardingTaskDTO ->
                        onboardingTaskDTO.mapToGraph(index)
                    }
                }
            }
            .exceptionally {
                log.error(it) {
                    "[OnboardingTasksDataLoader] failed to load onboarding tasks for ${onboardingIds.size} onboarding ids"
                }
                emptyMap()
            }
    }
}
