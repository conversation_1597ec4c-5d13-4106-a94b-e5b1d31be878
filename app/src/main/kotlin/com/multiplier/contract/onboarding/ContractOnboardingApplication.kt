package com.multiplier.contract.onboarding

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.context.annotation.ComponentScan

@EnableFeignClients(basePackages = ["com.multiplier"])
@SpringBootApplication
@ConfigurationPropertiesScan
@ComponentScan("com.multiplier")
class ContractOnboardingApplication

fun main(args: Array<String>) {
    runApplication<ContractOnboardingApplication>(*args)
}
