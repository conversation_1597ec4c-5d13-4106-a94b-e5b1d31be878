package com.multiplier.contract.onboarding.handler

import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventMessage
import com.multiplier.contract.kafka.contract.ContractEventMessageOuterClass.ContractEventType.CONTRACT_STATUS_UPDATE
import com.multiplier.contract.onboarding.service.reminders.EmployeeIdAndWorkEmailReminders
import com.multiplier.contract.schema.contract.ContractOuterClass
import mu.KotlinLogging
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.messaging.MessageHeaders
import org.springframework.messaging.handler.annotation.Headers
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class ContractEventListener(
    private val reminders: EmployeeIdAndWorkEmailReminders,
) {
    @KafkaListener(
        topics = ["topic.internal.v1.contract"], containerFactory = "contractEventConsumerFactory")
    fun onExternalEventFromContractTopic(
        @Payload message: ContractEventMessage,
        @Headers headers: MessageHeaders
    ) {
        if (message.eventType == CONTRACT_STATUS_UPDATE &&
            message.event.contractStatus == ContractOuterClass.ContractStatus.ACTIVE) {
            log.info("Received contract activated event. contractId: ${message.event.contractId}")

            reminders.sendEmployeeIdAndWorkEmailReminderToCompanyOnContractActivation(
                message.event.contractId)

            log.info(
                "Finished processing contract activated event. contractId: ${message.event.contractId}")
        }
    }
}
