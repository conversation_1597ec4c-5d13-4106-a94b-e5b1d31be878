package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.CreateMembersRequest
import com.multiplier.member.schema.ValidateMemberCreateInputsRequest
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkMemberServiceAdapter {

    @GrpcClient("member-service")
    private lateinit var bulkService: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    fun validateMemberCreateInputs(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<com.multiplier.member.schema.MemberCreateInput>> {
        val result =
            bulkService.validateMemberCreateInputs(
                ValidateMemberCreateInputsRequest.newBuilder()
                    .addAllInputs(employeeData.map { it.toGrpc() })
                    .setContext(options.context.name)
                    .build())

        return result.validationResultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
            )
        }
    }

    fun createMembers(
        inputs: List<CreationInput<com.multiplier.member.schema.MemberCreateInput>>,
        options: BulkOnboardingOptions
    ): List<CreationResult> {
        return try {
            val response =
                bulkService.createMembers(
                    CreateMembersRequest.newBuilder()
                        .addAllInputs(
                            inputs.map { it.data.toBuilder().setRequestId(it.requestId).build() })
                        .setContext(options.context.name)
                        .build())
            response.resultsList.map {
                CreationResult(
                    requestId = it.requestId,
                    success = it.success,
                    upsertedIds = listOf(it.memberId).filter { id -> id > 0 },
                    errors = it.errorsList)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to create members for bulk onboarding" }
            inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Upsert member failed due to an internal error: unknown exception occurred")
            }
        }
    }
}

private fun EmployeeData.toGrpc(): com.multiplier.member.schema.MemberCreateValidationInput =
    com.multiplier.member.schema.MemberCreateValidationInput.newBuilder()
        .setRequestId(this.identification.validationId)
        .putAllProperties(this.data)
        .build()
