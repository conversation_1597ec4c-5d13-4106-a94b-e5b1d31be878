package com.multiplier.contract.onboarding.service.notifications

import com.multiplier.bulk.upload.schema.kafka.BulkUploadJob
import com.multiplier.common.exception.toBusinessException
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.DocumentServiceAdapter
import com.multiplier.contract.onboarding.adapter.OperationsUserServiceAdapter
import com.multiplier.contract.onboarding.domain.model.Company
import com.multiplier.contract.onboarding.domain.model.CompanyUser
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulkupload.BulkUploadService
import com.multiplier.contract.onboarding.service.convertToMap
import com.multiplier.contract.onboarding.service.extensions.userId
import com.multiplier.contract.onboarding.service.reminders.Notification
import com.multiplier.contract.onboarding.types.NotificationType
import java.text.MessageFormat
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}
private val BULK_ONBOARDING_MODULES =
    listOf(
        BulkUploadService.GLOBAL_PAYROLL_MEMBER_ONBOARDING,
        BulkUploadService.AOR_MEMBER_ONBOARDING,
        BulkUploadService.FREELANCER_MEMBER_ONBOARDING,
        BulkUploadService.EOR_MEMBER_ONBOARDING)

@Service
class BulkOnboardingJobNotificationService(
    private val notificationService: NotificationService,
    private val companyServiceAdapter: CompanyServiceAdapter,
    private val documentServiceAdapter: DocumentServiceAdapter,
    private val operationsUserServiceAdapter: OperationsUserServiceAdapter,
    private val currentUser: CurrentUser,
    @Value("\${platform.base-url}") private val baseUrl: String,
    @Value("\${ops.support-email}") private val systemNotificationEmail: String,
) {

    fun notifyOnboardingFileUploadedSuccessfully(
        job: BulkUploadJob,
    ) {
        try {
            log.info { "Start notify onboarding file uploaded successfully for job id = ${job.id}" }
            val module = getBulkOnboardingModule(job)
            if (module == BulkUploadService.EOR_MEMBER_ONBOARDING) {
                // Skipping sending notification for EOR since we are not live on company experience
                log.info { "Skip sending file uploaded notification for module $module" }
                return
            }
            val dto = composeBulkOnboardingFileUploadedSuccessfullyDTO(job, module)

            notificationService.send(
                Notification(
                    subject = dto.subject,
                    from = MessageFormat.format("Multiplier <{0}>", systemNotificationEmail),
                    to = dto.recipientEmails.joinToString(";"),
                    cc = emptyList(),
                    type = dto.notificationType,
                    data = dto.toPropertyMap(),
                ))
        } catch (ex: Exception) {
            log.error(ex) {
                "Fail to notify onboarding file uploaded successfully for job id = ${job.id}"
            }
        }
    }

    fun notifyBulkOnboardingEmployeesAddedToTeam(job: BulkUploadJob, employeeCount: Int) {
        try {
            log.info {
                "Start notify bulk onboarding employee added to team for company id = ${job.companyId}"
            }
            val module = getBulkOnboardingModule(job)
            if (module == BulkUploadService.EOR_MEMBER_ONBOARDING) {
                // Skipping sending notification for EOR since we are not live on company experience
                log.info { "Skip sending employees added to team notification for module $module" }
                return
            }
            val dto =
                composeBulkOnboardingEmployeesAddedToTeamDTO(
                    job.id, job.companyId, job.entityId, employeeCount, module)

            notificationService.send(
                Notification(
                    subject = dto.subject,
                    from = MessageFormat.format("Multiplier <{0}>", systemNotificationEmail),
                    to = dto.recipientEmails.joinToString(";"),
                    cc = emptyList(),
                    type = dto.notificationType,
                    data = dto.toPropertyMap(),
                ))
        } catch (ex: Exception) {
            log.error(ex) {
                "Fail to notify bulk onboarding employee added to team for company id = ${job.companyId}"
            }
        }
    }

    private fun composeBulkOnboardingFileUploadedSuccessfullyDTO(
        job: BulkUploadJob,
        module: String,
    ): BulkOnboardingFileUploadedSuccessfullyDTO {
        val company = getCompany(job.companyId)
        val companyName = company.displayName
        val companyLogoLink = getCompanyLogoLink(company)
        val companyCountry = company.countryFullName
        val companyUser = getCompanyUserByUserId(currentUser.userId)
        val currentUserEmail = getUserEmail(currentUser.userId, companyUser)
        val entityName = getEntityName(job.entityId)

        return BulkOnboardingFileUploadedSuccessfullyDTO(
            recipientEmails = listOf(currentUserEmail),
            companyLogoLink = companyLogoLink.orEmpty(),
            companyName = companyName,
            entityName = entityName ?: companyName,
            companyCountry = companyCountry,
            adminName = companyUser?.firstName ?: "team",
            jobSummaryLink = getJobSummaryLink(module, job.id),
            module = module,
        )
    }

    private fun composeBulkOnboardingEmployeesAddedToTeamDTO(
        jobId: Long,
        companyId: Long,
        entityId: Long?,
        employeeCount: Int,
        module: String,
    ): BulkOnboardingEmployeesAddedToTeamDTO {
        val company = getCompany(companyId)
        val companyName = company.displayName
        val companyLogoLink = getCompanyLogoLink(company)
        val companyCountry = company.countryFullName
        val companyUser = getCompanyUserByUserId(currentUser.userId)
        val currentUserEmail = getUserEmail(currentUser.userId, companyUser)
        val entityName = getEntityName(entityId)
        val subject = getNotificationSubject(module, employeeCount)

        return BulkOnboardingEmployeesAddedToTeamDTO(
            recipientEmails = listOf(currentUserEmail),
            companyLogoLink = companyLogoLink.orEmpty(),
            companyName = companyName,
            entityName = entityName ?: companyName,
            companyCountry = companyCountry,
            adminName = companyUser?.firstName ?: "team",
            employeeCount = employeeCount,
            module = module,
            jobReviewLink = getJobReviewLink(module, jobId),
            subject = subject,
        )
    }

    private fun getNotificationSubject(module: String, count: Int): String =
        when (module) {
            BulkUploadService.AOR_MEMBER_ONBOARDING ->
                "Aor Contractor addition successful: $count contractors added!"
            BulkUploadService.FREELANCER_MEMBER_ONBOARDING ->
                "Contractor addition successful: $count contractors added!"
            else -> "Employee addition successful: $count employees added!"
        }

    private fun getJobReviewLink(module: String, jobId: Long?): String =
        when (module) {
            // For freelancer and AOR we are redirecting to team tab
            BulkUploadService.AOR_MEMBER_ONBOARDING,
            BulkUploadService.FREELANCER_MEMBER_ONBOARDING ->
                "$baseUrl/company/team?sortBy=createdOn&sortDir=desc&subtab=DEFAULT&page=1&rows=10&tab=ONBOARDING"
            else -> "$baseUrl/company/member-onboard/bulk-upload/${jobId}/review?tab=added"
        }

    private fun getJobSummaryLink(module: String, jobId: Long?): String =
        when (module) {
            BulkUploadService.AOR_MEMBER_ONBOARDING ->
                "$baseUrl/company/member-onboard/aor-contractor-bulk-upload/aor/bulk-upload/${jobId}/summary"
            BulkUploadService.FREELANCER_MEMBER_ONBOARDING ->
                "$baseUrl/company/member-onboard/aor-contractor-bulk-upload/freelancer/bulk-upload/${jobId}/summary"
            else -> "$baseUrl/company/member-onboard/bulk-upload/${jobId}/summary"
        }

    private fun getEntityName(entityId: Long?): String? =
        entityId?.let { companyServiceAdapter.getLegalEntity(entityId) }?.legalName

    private fun getCompany(companyId: Long) = companyServiceAdapter.getCompany(companyId)

    private fun getCompanyLogoLink(company: Company) =
        documentServiceAdapter.getCompanyLogoLinks(listOf(company.logoId)).firstNotNullOfOrNull {
            it.value.viewUrl
        }

    private fun getBulkOnboardingModule(job: BulkUploadJob): String {
        val moduleList = job.modulesList.toMutableList()
        val filteredModules = moduleList.filter { it in BULK_ONBOARDING_MODULES }
        if (filteredModules.size != 1) {
            log.error { "modules not present or more than 1 for onboarding = $filteredModules" }
            throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException()
        }
        return moduleList.first()
    }

    private fun getUserEmail(userId: Long, companyUser: CompanyUser?): String {
        return (companyUser?.email ?: getOperationUserEmailByUserId(userId)).orEmpty().ifEmpty {
            log.error { "Fail to get user email for userId = $userId" }
            throw ErrorCodes.USER_NOT_FOUND.toBusinessException()
        }
    }

    private fun getCompanyUserByUserId(userId: Long): CompanyUser? {
        return try {
            companyServiceAdapter.getCompanyUserByUserId(userId)
        } catch (ex: Exception) {
            return null
        }
    }

    private fun getOperationUserEmailByUserId(userId: Long) =
        operationsUserServiceAdapter.getOperationsUsersByUserId(userId)?.email
}

data class BulkOnboardingFileUploadedSuccessfullyDTO(
    val notificationType: NotificationType =
        NotificationType.BulkOnboardingFileUploadedSuccessfully,
    val recipientEmails: List<String>,
    val companyLogoLink: String,
    val companyName: String,
    val entityName: String,
    val companyCountry: String,
    val adminName: String,
    val jobSummaryLink: String,
    val module: String,
    val subject: String = "Bulk upload of onboarding details completed for $entityName",
) {
    fun toPropertyMap(): Map<String, Any> =
        convertToMap(
            mapOf(
                "companyLogoLink" to companyLogoLink,
                "companyName" to companyName,
                "entityName" to entityName,
                "companyCountry" to companyCountry,
                "adminName" to adminName,
                "jobSummaryLink" to jobSummaryLink,
                "module" to module,
            ))
}

data class BulkOnboardingEmployeesAddedToTeamDTO(
    val notificationType: NotificationType = NotificationType.BulkOnboardingEmployeeAddedToTeam,
    val recipientEmails: List<String>,
    val companyLogoLink: String,
    val companyName: String,
    val entityName: String,
    val companyCountry: String,
    val adminName: String,
    val employeeCount: Int,
    val jobReviewLink: String,
    val module: String,
    val subject: String = "Employee addition successful: $employeeCount employees added!",
) {
    fun toPropertyMap(): Map<String, Any> =
        convertToMap(
            mapOf(
                "companyLogoLink" to companyLogoLink,
                "companyName" to companyName,
                "entityName" to entityName,
                "companyCountry" to companyCountry,
                "adminName" to adminName,
                "employeeCount" to employeeCount,
                "jobReviewLink" to jobReviewLink,
                "module" to module,
            ))
}
