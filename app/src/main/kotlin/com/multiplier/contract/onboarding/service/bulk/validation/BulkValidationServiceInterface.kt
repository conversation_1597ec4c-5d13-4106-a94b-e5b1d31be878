package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataHelper
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataHelper.Companion.defaultEmployeeDataMergeStrategy
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions

interface BulkValidationServiceInterface {

    fun mergeEmployeeData(employeeData: List<EmployeeData>) =
        EmployeeDataHelper.mergeEmployeeData(employeeData, defaultEmployeeDataMergeStrategy)

    fun validateEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): EmployeeValidationResults

    fun validateCompanyOrThrow(options: BulkOnboardingOptions)

    fun validateCompanyLegalEntityOrThrow(options: BulkOnboardingOptions)
}
