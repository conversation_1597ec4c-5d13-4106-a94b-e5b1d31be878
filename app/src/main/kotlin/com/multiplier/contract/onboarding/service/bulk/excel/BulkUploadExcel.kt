package com.multiplier.contract.onboarding.service.bulk.excel

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.domain.model.bulk.*
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.COMPENSATION_DATA_GROUP
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationError
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.DocumentReadable
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.onboarding.usecase.EmployeeTemplateData
import com.poiji.annotation.ExcelCellName
import com.poiji.annotation.ExcelProperty
import com.poiji.annotation.ExcelRow
import com.poiji.annotation.ExcelUnknownCells
import com.poiji.bind.Poiji
import com.poiji.exception.PoijiExcelType
import com.poiji.option.PoijiOptions.PoijiOptionsBuilder
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.nio.file.Files
import java.nio.file.Path
import java.time.LocalDateTime
import java.util.*
import mu.KotlinLogging
import org.apache.poi.ss.usermodel.*
import org.apache.poi.ss.util.CellRangeAddressList
import org.apache.poi.xssf.usermodel.XSSFDataValidationHelper
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook

const val CHARACTER_WIDTH_MULTIPLIER = 256
const val VALIDATION_ERRORS_COL_HEADER = "Validation errors"
const val VALIDATION_ERRORS_COL_DESCRIPTION = "Please address these issues:"
const val MINIMUM_COLUMN_WIDTH = "a few words of space".length * CHARACTER_WIDTH_MULTIPLIER
const val PIXEL_TO_POINTS_RATIO = 20
const val ERROR_MESSAGE_SEPARATOR = "\n"
const val PROMPT_BOX_LENGTH_LIMIT = 255
const val EMPLOYEE_DATA_VALIDATION_ERROR_SHEET = 0
const val COMPENSATION_DATA_VALIDATION_ERROR_SHEET = 1

const val DATE_FORMAT = "yyyy-MM-dd"
const val TEXT_FORMAT = "@"

fun templateVersion(context: BulkOnboardingContext) =
    when (context) {
        BulkOnboardingContext.GLOBAL_PAYROLL -> "1.0.2"
        BulkOnboardingContext.HRIS_PROFILE_DATA -> "1.0.1"
        BulkOnboardingContext.EOR -> "1.0.1"
        BulkOnboardingContext.AOR -> "1.0.0"
        BulkOnboardingContext.FREELANCER -> "1.0.0"
        else ->
            throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                "Template version not supported for $context",
                context = mapOf("context" to context))
    }

val fieldDescriptionColumn =
    Column(
        key = "DO NOT MODIFY OR DELETE THIS ROW",
        label = "Field",
        description = "Description",
        type = DataSpecType.TEXT)

// translated header row
// description row
private const val ROW_OFFSET = 2

private val log = KotlinLogging.logger {}

data class CellStyleConfig(
    val fontName: String = "Calibri",
    val fontHeight: Short = 11,
    val isBold: Boolean = false,
    val verticalAlignment: VerticalAlignment = VerticalAlignment.CENTER,
    val horizontalAlignment: HorizontalAlignment = HorizontalAlignment.LEFT,
    val wrapText: Boolean = true
)

fun Workbook.createCustomCellStyle(config: CellStyleConfig): CellStyle {
    val font =
        this.createFont().apply {
            fontName = config.fontName
            fontHeightInPoints = config.fontHeight
            bold = config.isBold
        }

    val format = this.createDataFormat().getFormat(TEXT_FORMAT)

    return this.createCellStyle().apply {
        setFont(font)
        dataFormat = format
        verticalAlignment = config.verticalAlignment
        alignment = config.horizontalAlignment
        wrapText = config.wrapText
    }
}

object BulkUploadExcel {
    fun parse(bytes: ByteArray): EmployeeDataInput {
        val properties: BulkUploadProperties = bytes.parseXlsxProperties()
        log.info(
            "Parsing bulk onboarding template, countryCode: ${properties.countryCode}, template version: ${properties.templateVersion}")

        return EmployeeDataInput(
            employeeData = parseAllSheets(bytes),
            templateData =
                EmployeeTemplateData(
                    countryCode = properties.countryCode,
                    templateVersion = properties.templateVersion,
                    reportTemplate = bytes))
    }

    private fun parseAllSheets(bytes: ByteArray): List<EmployeeData> {
        val sheets = bytes.inputStream().use { XSSFWorkbook(it).visibleSheets() }
        return sheets.flatMap { parseSheet(bytes, it) }
    }

    private fun parseSheet(bytes: ByteArray, sheet: ExcelSheet): List<EmployeeData> {
        val rows: List<BulkUploadRowData> = bytes.parseXlsx<BulkUploadRowData>(sheet.index)
        val employeeData =
            rows
                .slice(ROW_OFFSET until rows.size)
                .filterNot { it.isEmpty() }
                .map {
                    EmployeeData(
                        identification =
                            EmployeeIdentification(
                                rowIdentifier = it.rowIdentifier,
                                employeeId = it.employeeId,
                                contractId = it.contractId,
                                firstName = it.firstName,
                                lastName = it.lastName,
                                email = it.email,
                                rowNumber = it.row),
                        data =
                            it.unknownCells +
                                mapOf(
                                    "rowIdentifier" to (it.rowIdentifier ?: ""),
                                    "employeeId" to (it.employeeId ?: ""),
                                    "contractId" to (it.contractId?.toString() ?: ""),
                                    "firstName" to (it.firstName ?: ""),
                                    "lastName" to (it.lastName ?: ""),
                                    "email" to (it.email ?: ""),
                                    "rowNumber" to it.row.toString(),
                                ),
                        group = sheet.name)
                }

        return employeeData
    }

    fun generate(
        options: BulkOnboardingOptions,
        dataSpecs: List<DataSpec>,
        dataChunks: List<EmployeeDataChunk> = emptyList()
    ): DocumentReadable {
        WorkbookFactory.create(true).use { workbook ->
            val fileSheetName = getFileAndSheetName(options)
            val sheetSpecs = ExcelPresentation.createSheetSpecs(dataSpecs, options)
            workbook.createSheets(sheetSpecs, dataChunks)
            workbook.insertCustomProperties(options)

            log.info {
                "Excel generation completed, data specs size = ${dataSpecs.size}, employee data size = ${dataChunks.size}"
            }

            return DocumentReadable.newBuilder()
                .blob(getFileAsBlob(workbook))
                .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .id(System.currentTimeMillis())
                .name(fileSheetName)
                .extension("xlsx")
                .build()
        }
    }

    data class DropDownCell(
        val sheet: Sheet,
        val columnIndex: Int,
        val column: Column,
        val allowedValues: Collection<String> = column.allowedValues
    )

    enum class BooleanValues(val value: String) {
        YES("true"),
        NO("false")
    }

    private fun Workbook.createSheets(
        sheetSpecs: List<SheetSpec>,
        dataChunks: List<EmployeeDataChunk>
    ) {
        val dropdownCells = mutableListOf<DropDownCell>()

        sheetSpecs.forEach {
            val sheet = this.createSheet(it.name)
            val keyRow = sheet.createRow(0)
            val headerRow = sheet.createRow(1)
            val descriptionRow = sheet.createRow(2)
            keyRow.height = 0 // hide key row
            headerRow.height = 40.pixels()
            val dateFormat = this.dateFormatCellStyle()

            val headerColumns = listOf(fieldDescriptionColumn) + it.columns
            val defaultCellStyle = createDefaultCellStyle()
            headerColumns.forEach { column ->
                val columnIndex = headerRow.physicalNumberOfCells
                sheet.setDefaultColumnStyle(columnIndex, defaultCellStyle)

                addKeyCell(keyRow, columnIndex, this, column)
                addHeaderCell(headerRow, columnIndex, column, this)
                addDescriptionCell(descriptionRow, columnIndex, column, this)

                if (column.type == DataSpecType.DATE) {
                    sheet.setDefaultDateFormatOnColumn(columnIndex, dateFormat)
                }
                if (column.type == DataSpecType.SELECT && column.allowedValues.isNotEmpty()) {
                    dropdownCells.add(DropDownCell(sheet, columnIndex, column))
                }
                if (column.type == DataSpecType.BOOLEAN) {
                    dropdownCells.add(
                        DropDownCell(
                            sheet, columnIndex, column, BooleanValues.values().map(Enum<*>::name)))
                }
            }

            sheet.fillEmployeeData(dataChunks, headerColumns)

            for (i in 0..it.columns.size) {
                sheet.setColumnWidth(i, MINIMUM_COLUMN_WIDTH)
            }

            sheet.createFreezePane(1, ROW_OFFSET)
        }

        // put hidden sheets to the end to reduce noise when open by Mac-Numbers
        dropdownCells.forEach { (sheet, columnIndex, column, allowedValues) ->
            sheet.createDropdownCells(columnIndex, column, allowedValues)
        }
    }

    private fun Sheet.fillEmployeeData(
        dataChunks: List<EmployeeDataChunk>,
        headerColumns: List<Column>
    ) {
        dataChunks.forEachIndexed { rowIndex, dataChunk ->
            val row = this.createRow(ROW_OFFSET + rowIndex + 1)
            headerColumns.forEachIndexed { columnIndex, column ->
                val cell = row.createCell(columnIndex)
                val value = dataChunk.data[column.keyWithPrefix()] ?: ""
                cell.setCellValue(value)
            }
        }
    }

    private fun Workbook.createHeaderCellStyle(): CellStyle {
        return createCustomCellStyle(CellStyleConfig(fontHeight = 11, isBold = true))
    }

    private fun Workbook.createDescriptionHeaderCellStyle(): CellStyle {
        return createCustomCellStyle(CellStyleConfig(fontHeight = 9, isBold = true))
    }

    private fun Workbook.createDescriptionCellStyle(): CellStyle {
        return createCustomCellStyle(CellStyleConfig(fontHeight = 9, isBold = false))
    }

    private fun Workbook.createDefaultCellStyle(): CellStyle {
        return createCustomCellStyle(CellStyleConfig(fontHeight = 11, isBold = false))
    }

    private fun Workbook.createErrorCellStyle(): CellStyle {
        return createCustomCellStyle(CellStyleConfig(fontHeight = 11, isBold = false))
    }

    private fun getFileAndSheetName(options: BulkOnboardingOptions): String {
        return listOfNotNull(
                "Bulk Onboarding Template",
                options.context,
                if (options.context == BulkOnboardingContext.HRIS_PROFILE_DATA) options.companyId
                else options.countryCode?.name,
                templateVersion(options.context))
            .joinToString(separator = " - ")
    }

    private fun addKeyCell(keyRow: Row, columnIndex: Int, workbook: Workbook, it: Column) {
        keyRow
            .createCell(columnIndex)
            .setCellValue(workbook.creationHelper.createRichTextString(it.keyWithPrefix()))
    }

    private fun addHeaderCell(headerRow: Row, columnIndex: Int, it: Column, workbook: Workbook) {
        val headerCell = headerRow.createCell(columnIndex)
        headerCell.setCellValue(it.label)
        headerCell.cellStyle = workbook.createHeaderCellStyle()
    }

    private fun addDescriptionCell(
        descriptionRow: Row,
        columnIndex: Int,
        it: Column,
        workbook: Workbook
    ) {
        val descriptionCell = descriptionRow.createCell(columnIndex)
        descriptionCell.setCellValue(it.description)
        descriptionCell.cellStyle =
            if (it.description == fieldDescriptionColumn.description) {
                workbook.createDescriptionHeaderCellStyle()
            } else {
                workbook.createDescriptionCellStyle()
            }
    }

    fun addValidationErrorsToInputSheet(
        inputSheet: File,
        validationErrors: List<EmployeeValidationError>
    ): ByteArray {
        if (validationErrors.isEmpty()) {
            return inputSheet.readBytes()
        }

        val workbook = WorkbookFactory.create(inputSheet)
        val (compensationDataErrors, employeeDataErrors) =
            validationErrors.partition { it.groupName == COMPENSATION_DATA_GROUP }
        addValidationErrorsToSheet(
            employeeDataErrors, workbook.getSheetAt(EMPLOYEE_DATA_VALIDATION_ERROR_SHEET))
        if (compensationDataErrors.isNotEmpty()) {
            addValidationErrorsToSheet(
                compensationDataErrors,
                workbook.getSheetAt(COMPENSATION_DATA_VALIDATION_ERROR_SHEET))
        }

        return workbook.flush().toByteArray()
    }

    private fun addValidationErrorsToSheet(
        errors: List<EmployeeValidationError>,
        sheet: Sheet,
    ) {
        val errorsByRow = errors.groupBy { it.rowNumber }
        val errorCellStyle = sheet.workbook.createErrorCellStyle()

        for (rowIndex in listOf(0, 1, 2) + errorsByRow.keys) {
            when (rowIndex) {
                0 -> continue
                1 -> {
                    sheet.setCellValue(
                        rowIndex,
                        0,
                        VALIDATION_ERRORS_COL_HEADER,
                        sheet.workbook.createHeaderCellStyle())
                }
                2 -> {
                    sheet.setCellValue(
                        rowIndex,
                        0,
                        VALIDATION_ERRORS_COL_DESCRIPTION,
                        sheet.workbook.createDescriptionHeaderCellStyle())
                }
                else -> {
                    setErrorCell(
                        errorsByRow = errorsByRow,
                        sheet = sheet,
                        workbook = sheet.workbook,
                        rowIndex = rowIndex,
                        errorCellStyle = errorCellStyle)
                }
            }
        }

        sheet[0]?.height = 0
        sheet.createFreezePane(2, ROW_OFFSET)
        sheet.autoSizeColumn(0)
    }
}

private fun Workbook.insertCustomProperties(options: BulkOnboardingOptions) {
    (this as XSSFWorkbook)
        .properties
        .customProperties
        .addProperty(BulkUploadProperties::countryCode.name, options.countryCode?.name)
    this.properties.customProperties.addProperty(
        BulkUploadProperties::templateVersion.name, templateVersion(options.context))
}

private fun setErrorCell(
    errorsByRow: Map<Int, List<EmployeeValidationError>>,
    sheet: Sheet,
    workbook: Workbook,
    rowIndex: Int,
    errorCellStyle: CellStyle
) {
    val errors = errorsByRow[rowIndex]
    sheet.setRichText(
        rowIndex = rowIndex,
        colIndex = 0,
        text =
            workbook.createRichText(
                errors?.joinToString(separator = ERROR_MESSAGE_SEPARATOR) { it.error } ?: ""),
        style = errorCellStyle,
    )

    sheet[rowIndex]?.heightInPoints = (errors?.size ?: 1) * sheet.defaultRowHeightInPoints
}

private fun getFileAsBlob(workbook: Workbook): String? {
    val tempFile: Path = createTempXlsxFile()
    FileOutputStream(tempFile.toFile()).use { workbook.write(it) }
    val fileData = Base64.getEncoder().encodeToString(Files.readAllBytes(tempFile))
    Files.deleteIfExists(tempFile)
    return fileData
}

private fun createTempXlsxFile(): Path {
    return Files.createTempFile(LocalDateTime.now().toString().replace(":", "-"), ".xlsx")
}

private fun Sheet.setDefaultDateFormatOnColumn(colIndex: Int, dateFormat: CellStyle) {
    (this as XSSFSheet).columnHelper.setColDefaultStyle(colIndex.toLong(), dateFormat)
}

private fun Sheet.createDropdownCells(
    columnIndex: Int,
    column: Column,
    allowedValues: Collection<String> = column.allowedValues
) {
    this.createDataValidation(columnIndex, column, allowedValues)
}

private fun Sheet.createDataValidation(
    columnIndex: Int,
    column: Column,
    allowedValues: Collection<String>,
) {
    val validationHelper = XSSFDataValidationHelper(this as XSSFSheet)
    val constraint = getDataValidationConstraint(allowedValues, validationHelper, column)
    // reduce the range to 100 to avoid "java.io.ioexception: zip bomb detected"
    val addressList = CellRangeAddressList(ROW_OFFSET + 1, 100, columnIndex, columnIndex)
    val dataValidation = validationHelper.createValidation(constraint, addressList)
    dataValidation.suppressDropDownArrow = true
    val promptText = allowedValues.joinToString(separator = "\n")
    if (promptText.length <= PROMPT_BOX_LENGTH_LIMIT) {
        dataValidation.createPromptBox("Allowed values:", promptText)
        dataValidation.showPromptBox = true
    }
    dataValidation.showErrorBox = true
    this.addValidationData(dataValidation)
}

private fun XSSFSheet.getDataValidationConstraint(
    allowedValues: Collection<String>,
    validationHelper: XSSFDataValidationHelper,
    column: Column
): DataValidationConstraint =
    if ("\"${allowedValues.joinToString(",")}\"".length <= PROMPT_BOX_LENGTH_LIMIT) {
        validationHelper.createExplicitListConstraint(allowedValues.toTypedArray())
    } else {
        val hiddenName = "hidden_${column.key}"
        val hiddenSheet = workbook.createHiddenSheet(hiddenName)
        hiddenSheet.createFormulaForAllowedValues(hiddenName, allowedValues)
        validationHelper.createFormulaListConstraint(hiddenName)
    }

private fun Sheet.createFormulaForAllowedValues(
    formulaName: String,
    allowedValues: Collection<String>
) {
    // write all allowed values in the first column (column A)
    allowedValues.forEach {
        val row = this.createRow(this.physicalNumberOfRows)
        val cell = row.createCell(0)
        cell.setCellValue(it)
    }

    // create a named cell that refers to the range of allowed value cells in the hidden sheet
    val namedCell = workbook.createName()
    namedCell.nameName = formulaName
    namedCell.refersToFormula = "${this.sheetName}!\$A$1:\$A$" + allowedValues.size
}

private fun Workbook.createHiddenSheet(hiddenSheetName: String): Sheet {
    val hiddenSheet = this.createSheet(hiddenSheetName)
    val hiddenSheetIndex = this.numberOfSheets - 1 // 0-indexed
    this.setSheetHidden(hiddenSheetIndex, true)

    return hiddenSheet
}

inline fun <reified T> ByteArray.parseXlsx(sheetIndex: Int = 0): List<T> {
    val options =
        PoijiOptionsBuilder.settings()
            .trimCellValue(true)
            .ignoreHiddenSheets(true)
            .sheetIndex(sheetIndex)
            .build()
    this.inputStream().use {
        return Poiji.fromExcel(it, PoijiExcelType.XLSX, T::class.java, options)
    }
}

private fun ByteArray.parseXlsxProperties(): BulkUploadProperties {
    this.inputStream().use {
        return Poiji.fromExcelProperties(it, PoijiExcelType.XLSX, BulkUploadProperties::class.java)
    }
}

private class BulkUploadRowData {

    @ExcelRow var row: Int = -1

    @ExcelCellName("employeeId") var employeeId: String? = null

    @ExcelCellName("contractId") var contractId: Long? = null

    @ExcelCellName("firstName") var firstName: String? = null

    @ExcelCellName("lastName") var lastName: String? = null

    @ExcelCellName("email") var email: String? = null

    @ExcelCellName("rowIdentifier") var rowIdentifier: String? = null

    @ExcelUnknownCells var unknownCells: MutableMap<String, String> = mutableMapOf()

    fun isEmpty(): Boolean {
        return (listOf(
                employeeId, contractId?.toString(), firstName, lastName, email, rowIdentifier) +
                unknownCells.values)
            .all { it.isNullOrBlank() }
    }
}

private class BulkUploadProperties {

    @ExcelProperty var countryCode: String? = null

    @ExcelProperty var templateVersion: String? = null
}

private fun Workbook.flush(): ByteArrayOutputStream {
    val outputStream = ByteArrayOutputStream()
    this.write(outputStream)
    outputStream.close()
    this.close()
    return outputStream
}

private fun Workbook.createRichText(value: String) = this.creationHelper.createRichTextString(value)

private fun Sheet.setRichText(
    rowIndex: Int,
    colIndex: Int,
    text: RichTextString,
    style: CellStyle
) {
    val cell = this[rowIndex]?.let { it[colIndex] ?: it.createCell(colIndex) }
    cell?.setCellValue(text)
    cell?.cellStyle = style
}

private fun Sheet.setCellValue(
    rowIndex: Int,
    colIndex: Int,
    value: String,
    style: CellStyle? = null,
) {
    this[rowIndex]?.setCellValue(colIndex, value, style)
}

private fun Row.setCellValue(colIndex: Int, value: String, style: CellStyle? = null) {
    val cell = this[colIndex] ?: this.createCell(colIndex)
    cell.setCellValue(value)
    style?.let { cell.cellStyle = style }
}

private fun Workbook.dateFormatCellStyle(): CellStyle {
    val createHelper: CreationHelper = this.creationHelper
    val format = createHelper.createDataFormat().getFormat(DATE_FORMAT)

    return this.createCellStyle().apply { dataFormat = format }
}

private fun Int.pixels() = (this * PIXEL_TO_POINTS_RATIO).toShort()

operator fun Sheet.get(rowIndex: Int): Row? {
    if (rowIndex <= this.lastRowNum) {
        return this.getRow(rowIndex)
    }

    log.warn {
        "Row index $rowIndex is out of bounds for sheet ${this.sheetName}, last row index is ${this.lastRowNum}"
    }
    return null
}

operator fun Row.get(cellIndex: Int): Cell? = this.getCell(cellIndex)

data class ExcelSheet(
    val index: Int,
    val name: String,
)

fun XSSFWorkbook.visibleSheets(): List<ExcelSheet> {
    val sheets = mutableListOf<ExcelSheet>()
    for (i in 0 until numberOfSheets) {
        if (!isSheetHidden(i)) {
            sheets.add(ExcelSheet(index = i, name = getSheetName(i)))
        }
    }
    return sheets
}
