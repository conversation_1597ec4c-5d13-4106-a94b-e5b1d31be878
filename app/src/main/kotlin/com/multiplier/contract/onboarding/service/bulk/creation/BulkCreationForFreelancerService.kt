package com.multiplier.contract.onboarding.service.bulk.creation

import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.service.bulk.validation.EmployeeValidationResults
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkCreationForFreelancerService(
    private val bulkMemberDataService: BulkMemberDataService,
    private val bulkContractDataService: BulkContractDataService,
    private val bulkOnboardingDataService: BulkOnboardingDataService,
    private val bulkCompensationDataService: BulkCompensationDataService,
    private val bulkPostOnboardingService: BulkPostOnboardingService
) : BulkCreationServiceInterface {
    private val log = KotlinLogging.logger {}

    override fun create(
        validationResults: EmployeeValidationResults,
        options: BulkOnboardingOptions
    ): BulkCreationResult {
        val memberContext =
            bulkMemberDataService.createMembers(
                inputs = validationResults.members.mapNotNull { it.toValidInputOrNull() },
                options = options)

        val upsertContractInputs =
            validationResults.contracts.mapNotNull { it.toValidInputOrNull() }
        val contractContext =
            bulkContractDataService.createContracts(
                inputs = upsertContractInputs, memberContext = memberContext, options = options)

        val newlyCreatedContractIds =
            BulkCreationHelper.getNewlyCreatedContractIds(contractContext, upsertContractInputs)
        val onboardingErrors =
            bulkOnboardingDataService.createOnboardingEntities(
                contractContext.contractIds.toList(), options = options)

        val compensationErrors =
            bulkCompensationDataService.createCompensations(
                inputs = validationResults.compensations.mapNotNull { it.toValidInputOrNull() },
                contractContext = contractContext,
                options = options)

        val creationErrors = onboardingErrors + compensationErrors
        val postOnboardingErrors =
            if (creationErrors.isEmpty()) {
                bulkPostOnboardingService.triggerPostOnboardingActions(
                    newlyCreatedContractIds, options = options)
            } else {
                log.warn { "Post onboarding actions are skipped due to bulk creation errors" }
                emptyList()
            }

        log.info { "Bulk employee data creation completed" }
        return BulkCreationResult(
            memberContext = memberContext,
            contractContext = contractContext,
            errors = creationErrors + postOnboardingErrors)
    }
}
