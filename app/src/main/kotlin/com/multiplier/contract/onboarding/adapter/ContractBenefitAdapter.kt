package com.multiplier.contract.onboarding.adapter

import com.github.benmanes.caffeine.cache.Caffeine
import com.google.protobuf.Empty
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.utils.EnumMapper
import com.multiplier.core.schema.grpc.benefit.Benefit
import com.multiplier.core.schema.grpc.benefit.BenefitServiceGrpc
import java.util.concurrent.TimeUnit
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface ContractBenefitAdapter {

    fun getContractBenefitByContractIds(
        contractIdList: Iterable<Long>
    ): List<Benefit.ContractIdAndBenefitMap>

    fun getBenefitRestrictedCountryListFR(): List<CountryCode>
}

@Service
class ContractBenefitAdapterImpl : ContractBenefitAdapter {

    @GrpcClient("core-service")
    private lateinit var contractBenefitBlockingStub: BenefitServiceGrpc.BenefitServiceBlockingStub

    val benefitRestrictedCountryListCache =
        Caffeine.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS)
            .maximumSize(10)
            .build<String, List<CountryCode>>()

    private val log = KotlinLogging.logger {}

    override fun getContractBenefitByContractIds(
        contractIdList: Iterable<Long>
    ): List<Benefit.ContractIdAndBenefitMap> {
        val request =
            Benefit.GetBenefitForContractsInput.newBuilder()
                .addAllContractIds(contractIdList)
                .build()

        val contractBenefitMapList =
            contractBenefitBlockingStub
                .getContractBenefitsByContractIds(request)
                .contractBenefitMapList

        return emptyList()
    }

    override fun getBenefitRestrictedCountryListFR(): List<CountryCode> {
        return benefitRestrictedCountryListCache.get("GET_BENEFIT_RESTRICTED_COUNTRY_LIST_FR_KEY") {
            contractBenefitBlockingStub
                .getBenefitRestrictedCountryListFR(Empty.getDefaultInstance())
                .countriesList
                .mapNotNull { EnumMapper.mapFromGrpcCountryCode(it) }
        }
    }
}
