package com.multiplier.contract.onboarding.repository

import com.multiplier.contract.onboarding.common.db.constant.Database
import jakarta.persistence.*
import java.time.LocalDateTime
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener

@MappedSuperclass abstract class BaseEntity()

@MappedSuperclass
@EntityListeners(AuditingEntityListener::class)
abstract class AuditableBaseEntity(
    @Column(name = Database.AuditableEntity.Column.CREATED_BY)
    @CreatedBy
    var createdBy: Long? = null,
    @Column(name = Database.AuditableEntity.Column.CREATED_ON)
    @CreatedDate
    var createdOn: LocalDateTime? = null,
    @Column(name = Database.AuditableEntity.Column.UPDATED_BY)
    @LastModifiedBy
    var updatedBy: Long? = null,
    @Column(name = Database.AuditableEntity.Column.UPDATED_ON)
    @LastModifiedDate
    var updatedOn: LocalDateTime? = null,
)

@MappedSuperclass
abstract class AuditableBaseEntityWithId(
    @GeneratedValue(strategy = GenerationType.IDENTITY) @Id open var id: Long,
) : AuditableBaseEntity()

@MappedSuperclass abstract class AuditableBaseEntityWithoutId() : AuditableBaseEntity()
