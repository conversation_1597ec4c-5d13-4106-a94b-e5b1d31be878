package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule.Companion.BANK_DATA_SPEC_PREFIX
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.*
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkMemberBankDataAdapter {

    @GrpcClient("member-service")
    private lateinit var bulkService: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    fun validate(
        inputs: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<GrpcValidationResult<UpsertBankDetailsInput>> {
        val result =
            bulkService.validateUpsertBankDetailsInputs(
                ValidateUpsertBankDetailsInputsRequest.newBuilder()
                    .addAllInputs(inputs.map { it.toGrpc() })
                    .setContext(options.context.name)
                    .setCompanyId(options.companyId)
                    .setCountry(CountryCode.valueOf(options.countryCode.name))
                    .build())

        return result.validationResultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
            )
        }
    }

    fun upsertBankData(
        inputs: List<CreationInput<UpsertBankDetailsInput>>,
    ): List<CreationResult> {
        if (inputs.isEmpty()) return emptyList()

        try {
            val results = bulkService.upsertBankDetails(inputs.toGrpc()).resultsList
            return results.map {
                CreationResult(
                    requestId = it.requestId, success = it.success, errors = it.errorsList)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to upsert bank data for bulk onboarding" }
            return inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Upsert bank data failed due to an internal error: unknown exception occurred")
            }
        }
    }
}

private fun EmployeeData.toGrpc(): UpsertBankDetailsValidationInput {
    return UpsertBankDetailsValidationInput.newBuilder()
        .setRequestId(this.identification.validationId)
        .setKeyPrefix(BANK_DATA_SPEC_PREFIX)
        .putAllProperties(this.data)
        .build()
}

private fun List<CreationInput<UpsertBankDetailsInput>>.toGrpc(): UpsertBankDetailsRequest {
    return UpsertBankDetailsRequest.newBuilder()
        .addAllInputs(
            this.map {
                it.data
                    .toBuilder()
                    .setRequestId(it.requestId)
                    .setMemberId(
                        requireNotNull(it.memberId) {
                            "Member ID for bank data upsert must not be null"
                        })
                    .build()
            })
        .build()
}
