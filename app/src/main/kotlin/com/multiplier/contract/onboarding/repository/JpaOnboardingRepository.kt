package com.multiplier.contract.onboarding.repository

import com.multiplier.contract.onboarding.repository.model.JpaOnboarding
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import java.util.Optional
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository

@Repository
interface JpaOnboardingRepository : CrudRepository<JpaOnboarding, Long> {
    fun findByContractIdInAndExperience(
        contractIds: Set<Long>,
        experience: String
    ): List<JpaOnboarding>

    fun findByContractIdInAndStatusAndExperience(
        contractIds: Set<Long>,
        status: ContractOnboardingStatus,
        experience: String,
    ): List<JpaOnboarding>

    fun findByContractIdAndExperience(
        contractId: Long?,
        experience: String?
    ): Optional<JpaOnboarding>
}
