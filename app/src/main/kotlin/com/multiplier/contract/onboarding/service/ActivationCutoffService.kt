package com.multiplier.contract.onboarding.service

import java.time.LocalDate
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class ActivationCutoffService(
    private val payrollCyclesService: PayrollCyclesService,
) {
    private val log = KotlinLogging.logger {}

    fun getActivationCutoffDateByContractId(
        contractIds: List<Long>,
        checkingDate: LocalDate
    ): Map<Long, LocalDate?> {
        log.info(
            "[getActivationCutoffDateByContractId] Getting activation cutoff data for " +
                "contractIds $contractIds and checkingDate $checkingDate")

        return payrollCyclesService
            .getPayrollCyclesByContractId(contractIds.toSet(), checkingDate)
            .mapValues { it.value.cutoffDate }
    }

    fun getActivationCutoffDateByOnboardingId(
        onboardingIds: List<Long>,
        checkingDate: LocalDate
    ): Map<Long, LocalDate?> {
        log.info(
            "[getActivationCutoffDateByOnboardingId] Getting activation cutoff data for " +
                "onboardingIds $onboardingIds and checkingDate $checkingDate")

        return payrollCyclesService
            .getPayrollCyclesByOnboardingId(onboardingIds.toSet(), checkingDate)
            .mapValues { it.value.cutoffDate }
    }
}
