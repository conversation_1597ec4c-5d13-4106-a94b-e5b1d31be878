package com.multiplier.contract.onboarding.domain.model

import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.CurrencyCode
import com.multiplier.contract.onboarding.types.Gender
import java.time.LocalDate

enum class ComplianceType {
    MULTIPLIER,
    PARTNER,
    CLIENT,
    UNRECOGNIZED
}

enum class ContractAgreementType {
    MULTIPLIER_TEMPLATE,
    UPLOADED_FINAL,
    CUSTOM_TEMPLATE;

    fun isCustomTemplate() = this == CUSTOM_TEMPLATE || this == UPLOADED_FINAL
}

data class Contract(
    val id: Long,
    val memberId: Long,
    val companyId: Long,
    val agreementId: Long? = null,
    val currency: CurrencyCode? = null,
)

data class Compliance(
    val contractId: Long,
    val agreementId: Long,
    val type: ComplianceType,
    val agreementType: ContractAgreementType,
)

data class ComplianceParam(
    val key: String,
    val label: String? = null,
    val value: Int,
    val unit: String,
)

data class Company(
    val id: Long,
    val logoId: Long,
    val displayName: String,
    val countryFullName: String,
    val countryCode: CountryCode?,
    val msaSigned: Boolean,
    val isTest: Boolean,
)

data class CompanyUser(
    val id: Long,
    val companyId: Long,
    val firstName: String,
    val lastName: String,
    val email: String,
)

data class Department(
    val id: Long,
    val companyId: Long,
    val name: String,
)

data class OperationsUser(
    val id: Long,
    val email: String,
    val firstName: String,
    val lastName: String,
)

data class Member(
    val id: Long,
    val email: String,
    val fullName: String,
    val firstName: String,
    val lastName: String,
    val userId: String,
    val persona: Persona,
    val gender: Gender? = null,
    val dateOfBirth: LocalDate? = null,
    val nationality: String? = null,
    val maritalStatus: String? = null,
    val religion: String? = null,
    val phoneNumber: String? = null,
    val nationalId: String? = null,
    val passportNumber: String? = null,
    val address: Address? = null,
    val legalDataProps: Map<String, String> = emptyMap(),
    val staticBankDetails: StaticBankDetails? = null,
    val dynamicBankDetails: Map<String, String> = emptyMap(),
)

data class EmergencyContact(
    val memberId: Long,
    val name: String,
    val relationship: String,
    val phoneNumber: String,
)

data class EducationDetail(
    val memberId: Long,
    val lastSchoolName: String,
    val lastSchoolDegree: String,
    val lastSchoolYearOfPassing: Int,
    val lastSchoolGpa: Float,
    val lastSchoolGrade: String,
)

data class EmployerDetail(
    val memberId: Long,
    val lastEmployerName: String,
    val lastEmployerPosition: String,
    val lastEmployerStartDate: LocalDate,
    val lastEmployerEndDate: LocalDate,
)

data class AddressDetail(
    val memberId: Long,
    val currentAddress: Address,
    val permanentAddress: Address? = null,
)

data class Address(
    val street: String,
    val line1: String,
    val line2: String,
    val city: String,
    val state: String,
    val province: String,
    val country: CountryCode,
    val zipCode: String,
    val postalCode: String,
)

data class Persona(
    val name: String,
)

data class StaticBankDetails(
    val bankName: String,
    val bankBranch: String,
    val bankAccountNumber: String,
    val bankAccountHolderName: String,
    val swiftCode: String,
    val localBankCode: String
)

data class CompanyManager(
    val companyId: Long,
    val email: String,
)

data class OrgManagementData(
    val contractId: Long,
    val department: Department? = null,
    val isManager: Boolean = false,
    val directManager: CompanyManager? = null,
)
