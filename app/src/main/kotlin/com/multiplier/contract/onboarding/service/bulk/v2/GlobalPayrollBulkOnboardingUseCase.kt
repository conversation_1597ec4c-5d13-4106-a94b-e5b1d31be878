package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkContractModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkContractOnboardingModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberLegalDataModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkOrgManagementModule
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkTimeOffModule
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.usecase.ModuleParams
import org.springframework.stereotype.Service

/** Use case for bulk onboarding/updating GP data of direct employees. */
@Service
class GlobalPayrollBulkOnboardingUseCase(
    private val bulkContractModule: BulkContractModule,
    private val bulkMemberModule: BulkMemberModule,
    private val bulkContractOnboardingModule: BulkContractOnboardingModule,
    private val bulkCompensationModule: BulkCompensationModule,
    private val bulkMemberLegalDataModule: BulkMemberLegalDataModule,
    private val bulkMemberBankDataModule: BulkMemberBankDataModule,
    private val bulkOrgManagementModule: BulkOrgManagementModule,
    private val bulkTimeOffModule: BulkTimeOffModule
) : BulkOnboardingUseCase() {

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ) =
        super.getDataSpecs(onboardingOptions, moduleParams).map {
            it.copy(group = groupBySource(it.source))
        }

    override fun getBulkOnboardingModules(): List<BulkDataModule> {
        return listOf(
            bulkContractModule,
            bulkMemberModule,
            bulkContractOnboardingModule,
            bulkMemberLegalDataModule,
            bulkMemberBankDataModule,
            bulkCompensationModule,
            bulkOrgManagementModule,
            bulkTimeOffModule)
    }

    override fun filterEmployeeDataForModule(
        employeeData: List<EmployeeData>,
        moduleIdentifier: String
    ): List<EmployeeData> =
        when (moduleIdentifier) {
            BulkCompensationModule.MODULE_NAME ->
                employeeData // further filtering is done in the module
            else -> employeeData.filter { it.group != EmployeeData.COMPENSATION_DATA_GROUP }
        }

    private fun groupBySource(source: String?) =
        when (source) {
            BulkCompensationModuleV2.COMPENSATION_SCHEMA_DATA_SPEC ->
                EmployeeData.COMPENSATION_DATA_GROUP
            else -> EmployeeData.EMPLOYMENT_DATA_GROUP
        }
}
