package com.multiplier.contract.onboarding.utils

import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.country.schema.Country.GrpcCountryCode
import mu.KotlinLogging
import org.apache.commons.lang3.EnumUtils

class EnumMapper {

    companion object {

        val log = KotlinLogging.logger {}

        inline fun <reified GEnum : Enum<GEnum>, reified TEnum : Enum<TEnum>> fromGrpcEnum(
            source: GEnum,
            vararg nullValues: GEnum
        ): TEnum? {
            return if (nullValues.contains(source)) {
                null
            } else {
                return if (EnumUtils.isValidEnum(TEnum::class.java, source.name)) {
                    enumValueOf<TEnum>(source.name)
                } else {
                    // Handle the case when the source enum name doesn't exist in the target enum
                    // You can provide a default value or throw a custom exception
                    log.error(
                        "Unable to map {}.{} to {}, temporarily return null. Please resolve this mapping error soon.",
                        GEnum::class.simpleName,
                        source,
                        TEnum::class.simpleName)
                    null
                }
            }
        }

        fun mapFromGrpcCountryCode(grpcCountryCode: GrpcCountryCode): CountryCode? {
            return fromGrpcEnum<GrpcCountryCode, CountryCode>(
                grpcCountryCode, GrpcCountryCode.UNRECOGNIZED)
        }
    }
}
