package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.ValidInput
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberEmergencyDataServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.EmergencyContact
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmergencyContactNameSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmergencyContactPhoneNumberSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService.Companion.EmergencyContactRelationshipSpec
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.UpsertEmergencyContactInput
import org.springframework.stereotype.Service

@Service
class BulkMemberEmergencyDataService(
    private val memberServiceAdapter: MemberServiceAdapter,
    private val bulkMemberEmergencyDataServiceAdapter: BulkMemberEmergencyDataServiceAdapter,
) {

    fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): List<GrpcValidationResult<UpsertEmergencyContactInput>> =
        bulkMemberEmergencyDataServiceAdapter.validateUpsertEmergencyContactDataInputs(
            employeeData, options)

    fun upsertEmergencyContacts(
        inputs: List<ValidInput<UpsertEmergencyContactInput>>,
        memberContext: MemberContext,
        options: BulkOnboardingOptions,
    ): List<String> {
        val upsertEmergencyContactInputs =
            inputs
                .filterNot { it.input == UpsertEmergencyContactInput.getDefaultInstance() }
                .map {
                    CreationInput(
                        requestId = it.validationId,
                        memberId = memberContext.getMemberId(it.validationId),
                        data = it.input)
                }

        val results =
            bulkUpsert(
                "Upsert emergency contacts",
                upsertEmergencyContactInputs,
                options,
                CreationInput.refMemberId) { emergencyInputs, _ ->
                    bulkMemberEmergencyDataServiceAdapter.upsertEmergencyContactData(
                        emergencyInputs)
                }

        return results.flatMap { it.errors }
    }

    fun getDataForSpecs(dataSpecs: List<DataSpec>, memberIds: Set<Long>): MemberDataContext {
        val result = memberServiceAdapter.getMemberEmergencyContacts(memberIds)
        val memberIdToEmergencyData =
            result.associate { it.memberId to it.toMemberDataChunk(dataSpecs) }
        return MemberDataContext(memberIdToEmergencyData)
    }

    private fun EmergencyContact.toMemberDataChunk(dataSpecs: List<DataSpec>): EmployeeDataChunk {
        val data =
            dataSpecs
                .associate {
                    it.key to
                        when (it.key) {
                            EmergencyContactNameSpec.key -> this.name
                            EmergencyContactRelationshipSpec.key -> this.relationship
                            EmergencyContactPhoneNumberSpec.key -> this.phoneNumber
                            else -> null
                        }
                }
                .filterValues { it != null }
                .mapValues { requireNotNull(it.value) }
        return EmployeeDataChunk(data)
    }
}
