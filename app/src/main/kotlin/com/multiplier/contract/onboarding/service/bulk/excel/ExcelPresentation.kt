package com.multiplier.contract.onboarding.service.bulk.excel

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.dataSpec.DataSpecForEORService.Companion.RowIdentifierSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmployeeIdSpec
import com.multiplier.contract.onboarding.service.toWords
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.CountryCode

enum class Alignment {
    LEFT,
    RIGHT,
}

data class Column(
    val key: String,
    val label: String,
    val description: String,
    val type: DataSpecType,
    val horizontalAlignment: Alignment = Alignment.LEFT,
    val allowedValues: Collection<String> = emptyList(),
    val prefix: String? = null,
) {
    fun keyWithPrefix(): String = if (prefix.isNullOrBlank()) key else "${prefix}.$key"
}

data class SpecGroup(val name: String, val keys: List<String>)

data class SheetSpec(val name: String, val columns: List<Column>)

object ExcelPresentation {

    fun createSheetSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions
    ): List<SheetSpec> {
        return when (options.context) {
            BulkOnboardingContext.HRIS_PROFILE_DATA -> createHrisProfileSheetSpecs(dataSpecs)
            BulkOnboardingContext.GLOBAL_PAYROLL ->
                createGlobalPayrollSheetSpecs(dataSpecs, options)
            BulkOnboardingContext.EOR -> createEORSheetSpecs(dataSpecs)
            BulkOnboardingContext.AOR -> createAorSheetSpecs(dataSpecs, options)
            BulkOnboardingContext.FREELANCER -> createFreelancerSheetSpecs(dataSpecs, options)
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Sheet specs query for ${options.context} not supported yet",
                    context = mapOf("context" to options.context))
        }
    }

    private fun createGlobalPayrollSheetSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions
    ): List<SheetSpec> {
        val dataSpecGroups = dataSpecs.groupBy { it.group.orEmpty() }
        return if (dataSpecGroups.size == 1) {
            // this is for keeping the old behavior of having a single sheet
            listOf(
                SheetSpec(
                    name = "Employee Data",
                    columns = dataSpecs.toHeaderColumns().translateGlobalPayrollLabels(options)))
        } else
            dataSpecGroups.map {
                SheetSpec(
                    name = it.key.lowercase().toWords(),
                    columns = it.value.toHeaderColumns().translateGlobalPayrollLabels(options))
            }
    }

    private fun createAorSheetSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions
    ): List<SheetSpec> {
        return listOf(
            SheetSpec(
                name = "AOR Employee Data",
                columns = dataSpecs.toHeaderColumns().translateGlobalPayrollLabels(options)))
    }

    private fun createFreelancerSheetSpecs(
        dataSpecs: List<DataSpec>,
        options: BulkOnboardingOptions
    ): List<SheetSpec> {
        return listOf(
            SheetSpec(
                name = "Freelancer Employee Data",
                columns = dataSpecs.toHeaderColumns().translateGlobalPayrollLabels(options)))
    }

    private fun createEORSheetSpecs(dataSpecs: List<DataSpec>): List<SheetSpec> {
        val legalDataSpecs =
            dataSpecs.filter { it.source == BulkMemberLegalDataService.LEGAL_SPEC_DATA }
        val countryStateSpec =
            dataSpecs.firstOrNull { it.key == BulkContractDataService.StateOfEmploymentSpec.key }
        return listOf(
            SheetSpec(
                name = "Basic Details",
                columns =
                    listOfNotNull(
                            RowIdentifierSpec,
                            ContractIdSpec,
                            BulkContractDataService.CountrySpec,
                            countryStateSpec,
                            BulkContractDataService.WorkStatusSpec,
                            BulkMemberDataService.FirstNameSpec,
                            BulkMemberDataService.LastNameSpec,
                            BulkMemberDataService.GenderSpec,
                            BulkMemberDataService.EmailSpec,
                            BulkContractDataService.PositionSpec,
                            BulkContractDataService.StartOnSpec,
                            BulkContractDataService.EndOnSpec,
                            BulkContractDataService.JobDescriptionSpec,
                        )
                        .toHeaderColumns()
                        .translateEORLabel() + legalDataSpecs.toHeaderColumns()),
            SheetSpec(
                name = "Insurance",
                columns =
                    listOf(
                            RowIdentifierSpec,
                            ContractIdSpec,
                            BulkContractDataService.CountrySpec,
                            BulkInsuranceDataService.InsuranceTypeSpec,
                            BulkInsuranceDataService.InsurancePlanSpec,
                            BulkInsuranceDataService.NumberOfDependantSpec,
                        )
                        .toHeaderColumns()),
            SheetSpec(
                name = "Compliance & Leaves",
                columns =
                    (listOf(
                            RowIdentifierSpec,
                            ContractIdSpec,
                        ) + eorComplianceSpecIfExist(dataSpecs))
                        .toHeaderColumns()
                        .translateEORLabel()),
            SheetSpec(
                name = "Compensation",
                columns =
                    listOfNotNull(
                            RowIdentifierSpec,
                            ContractIdSpec,
                            BulkCompensationDataService.CurrencySpec,
                            BulkCompensationDataService.BasePaySpec,
                            dataSpecs.firstOrNull {
                                it.key == BulkCompensationDataService.RateFrequencySpec.key
                            },
                            dataSpecs.firstOrNull {
                                it.key == BulkCompensationDataService.PayrollFrequencySpec.key
                            },
                            BulkCompensationDataService.JoiningBonusAmountSpec,
                            BulkCompensationDataService.JoiningBonusPayOnSpec,
                            BulkCompensationDataService.VariableBonusFrequencySpec,
                            BulkCompensationDataService.VariableBonusPayOnSpec,
                            BulkCompensationDataService.MobileAndPhoneTypeSpec,
                            BulkCompensationDataService.MobileAndPhoneSpec,
                            BulkCompensationDataService.MobileAndPhoneFrequencySpec,
                            BulkCompensationDataService.MobileAndPhoneStartOnSpec,
                            BulkCompensationDataService.InternetTypeSpec,
                            BulkCompensationDataService.InternetSpec,
                            BulkCompensationDataService.InternetFrequencySpec,
                            BulkCompensationDataService.InternetStartOnSpec,
                            BulkCompensationDataService.OtherAllowanceTypeSpec,
                            BulkCompensationDataService.OtherAllowanceSpec,
                            BulkCompensationDataService.OtherAllowanceFrequencySpec,
                            BulkCompensationDataService.OtherAllowanceStartOnSpec,
                        )
                        .toHeaderColumns()),
        )
    }

    private fun createHrisProfileSheetSpecs(dataSpecs: List<DataSpec>): List<SheetSpec> {
        return listOf(
                basicInfoGroup,
                personalInfoGroup,
                contactInfoGroup,
                contractGroup,
                workInfoGroup,
                identificationDetailsGroup,
                addressGroup,
                emergencyGroup,
                educationGroup,
                previousEmploymentGroup)
            .mapNotNull { it.createHrisProfileSheetSpec(dataSpecs) }
    }

    private fun SpecGroup.createHrisProfileSheetSpec(dataSpecs: List<DataSpec>): SheetSpec? {
        val specs = dataSpecs.filter { this.keys.contains(it.key) }
        return if (specs.isEmpty()) null
        else
            SheetSpec(
                name = this.name,
                columns =
                    (listOf(EmployeeIdSpec, ContractIdSpec) + specs)
                        .toHeaderColumns()
                        .translateHrisProfileLabels())
    }

    private val basicInfoGroup =
        SpecGroup(
            name = "Basic Info",
            keys =
                listOf(
                        BulkMemberDataService.FirstNameSpec,
                        BulkMemberDataService.LastNameSpec,
                    )
                    .map { it.key })

    private val personalInfoGroup =
        SpecGroup(
            name = "Personal Info",
            keys =
                listOf(
                        BulkMemberDataService.GenderSpec,
                        BulkMemberDataService.DateOfBirthSpec,
                        BulkMemberDataService.NationalitySpec,
                        BulkMemberDataService.MaritalStatusSpec,
                        BulkMemberLegalDataService.ReligionSpec)
                    .map { it.key })

    private val contactInfoGroup =
        SpecGroup(
            name = "Contact Info",
            keys =
                listOf(
                        BulkMemberDataService.EmailSpec,
                        BulkContractDataService.WorkEmailSpec,
                        BulkMemberDataService.PhoneNumberSpec)
                    .map { it.key })

    private val contractGroup =
        SpecGroup(
            name = "Contract",
            keys =
                listOf(
                        BulkComplianceDataService.NoticeAfterProbationValueSpec,
                        BulkComplianceDataService.NoticeAfterProbationUnitSpec,
                        BulkContractDataService.StartOnSpec,
                        BulkContractDataService.EndOnSpec)
                    .map { it.key })

    private val workInfoGroup =
        SpecGroup(
            name = "Work Info",
            keys =
                listOf(
                        BulkContractDataService.PositionSpec,
                        BulkOrgManagementDataService.DirectManagerEmailSpec,
                        BulkOrgManagementDataService.DepartmentSpec,
                        BulkContractDataService.LegalEntityIdSpec,
                        BulkOrgManagementDataService.IsManagerSpec)
                    .map { it.key })

    private val identificationDetailsGroup =
        SpecGroup(
            name = "Identification Details",
            keys =
                listOf(
                        BulkMemberLegalDataService.NationalIdSpec,
                        BulkMemberLegalDataService.PassportNumberSpec)
                    .map { it.key })

    private val addressGroup =
        SpecGroup(
            name = "Address",
            keys =
                listOf(
                        BulkMemberDataService.CurrentAddressLine1Spec,
                        BulkMemberDataService.CurrentAddressLine2Spec,
                        BulkMemberDataService.CurrentAddressCitySpec,
                        BulkMemberDataService.CurrentAddressStateSpec,
                        BulkMemberDataService.CurrentAddressCountrySpec,
                        BulkMemberDataService.CurrentAddressPostalCodeSpec,
                        BulkMemberDataService.PermanentAddressLine1Spec,
                        BulkMemberDataService.PermanentAddressLine2Spec,
                        BulkMemberDataService.PermanentAddressCitySpec,
                        BulkMemberDataService.PermanentAddressStateSpec,
                        BulkMemberDataService.PermanentAddressCountrySpec,
                        BulkMemberDataService.PermanentAddressPostalCodeSpec)
                    .map { it.key })

    private val emergencyGroup =
        SpecGroup(
            name = "Emergency",
            keys =
                listOf(
                        BulkMemberDataService.EmergencyContactNameSpec,
                        BulkMemberDataService.EmergencyContactRelationshipSpec,
                        BulkMemberDataService.EmergencyContactPhoneNumberSpec)
                    .map { it.key })

    private val educationGroup =
        SpecGroup(
            name = "Education",
            keys =
                listOf(
                        BulkMemberDataService.LastSchoolNameSpec,
                        BulkMemberDataService.LastSchoolDegreeSpec,
                        BulkMemberDataService.LastSchoolYearOfPassingSpec,
                        BulkMemberDataService.LastSchoolGpaSpec,
                        BulkMemberDataService.LastSchoolGradeSpec)
                    .map { it.key })

    private val previousEmploymentGroup =
        SpecGroup(
            name = "Previous Employment",
            keys =
                listOf(
                        BulkMemberDataService.LastEmployerNameSpec,
                        BulkMemberDataService.LastEmployerStartDateSpec,
                        BulkMemberDataService.LastEmployerEndDateSpec,
                        BulkMemberDataService.LastEmployerPositionSpec)
                    .map { it.key })
}

private fun eorComplianceSpecIfExist(dataSpecs: List<DataSpec>): List<DataSpec> {
    return listOf(
            BulkComplianceDataService.ProbationValueSpec,
            BulkComplianceDataService.ProbationUnitSpec,
            BulkComplianceDataService.NoticeAfterProbationValueSpec,
            BulkComplianceDataService.NoticeAfterProbationUnitSpec,
            BulkComplianceDataService.NonCompeteValueSpec,
            BulkComplianceDataService.NonCompeteUnitSpec,
            BulkComplianceDataService.NonSolicitValueSpec,
            BulkComplianceDataService.NonSolicitUnitSpec,
        )
        .mapNotNull { dataSpec -> dataSpecs.firstOrNull { it.key == dataSpec.key } }
}

private fun List<Column>.translateGlobalPayrollLabels(
    options: BulkOnboardingOptions
): List<Column> {
    return this.map {
        when (it.key) {
            BulkMemberDataService.EmailSpec.key -> it.copy(label = "Email Address")
            BulkContractDataService.StartOnSpec.key -> it.copy(label = "Start Date")
            BulkContractDataService.EndOnSpec.key -> it.copy(label = "End Date")
            BulkContractDataService.PositionSpec.key -> it.copy(label = "Designation")
            BulkCompensationDataService.RateFrequencySpec.key -> it.copy(label = "Billing Rate")
            BulkCompensationDataService.BasePaySpec.key ->
                if (options.countryCode == CountryCode.IND) it.copy(label = "Gross Salary") else it
            else -> it
        }
    }
}

private fun List<Column>.translateHrisProfileLabels(): List<Column> {
    return this.map {
        when (it.key) {
            BulkMemberDataService.EmailSpec.key -> it.copy(label = "Personal Email")
            BulkContractDataService.StartOnSpec.key -> it.copy(label = "Start Date")
            BulkContractDataService.EndOnSpec.key -> it.copy(label = "End Date")
            BulkContractDataService.PositionSpec.key -> it.copy(label = "Designation")
            BulkMemberDataService.EmergencyContactNameSpec.key -> it.copy(label = "Contact Name")
            BulkMemberDataService.EmergencyContactRelationshipSpec.key ->
                it.copy(label = "Relationship")
            BulkMemberDataService.EmergencyContactPhoneNumberSpec.key ->
                it.copy(label = "Phone Number")
            BulkMemberDataService.LastSchoolDegreeSpec.key -> it.copy(label = "Degree")
            BulkMemberDataService.LastSchoolYearOfPassingSpec.key ->
                it.copy(label = "Year of Passing")
            BulkMemberDataService.LastSchoolGpaSpec.key -> it.copy(label = "GPA")
            BulkMemberDataService.LastSchoolGradeSpec.key -> it.copy(label = "Grade")
            BulkMemberDataService.LastEmployerStartDateSpec.key -> it.copy(label = "Start Date")
            BulkMemberDataService.LastEmployerEndDateSpec.key -> it.copy(label = "End Date")
            BulkMemberDataService.LastEmployerPositionSpec.key -> it.copy(label = "Designation")
            else -> it
        }
    }
}

private fun List<Column>.translateEORLabel(): List<Column> {
    return this.map {
        when (it.key) {
            BulkComplianceDataService.ProbationValueSpec.key -> it.copy(label = "Probation")
            BulkComplianceDataService.NoticeAfterProbationValueSpec.key ->
                it.copy(label = "Notice Period")
            BulkComplianceDataService.NonCompeteValueSpec.key -> it.copy(label = "Non-Compete")
            BulkComplianceDataService.NonSolicitValueSpec.key -> it.copy(label = "Non-Solicitation")
            BulkContractDataService.PositionSpec.key -> it.copy(label = "Job Title")
            else -> it
        }
    }
}

private fun DataSpec.descriptionString() =
    (if (mandatory) "Mandatory" else "Optional") + (description?.let { " - $it" } ?: "")

private fun List<DataSpec>.toHeaderColumns() =
    this.distinctBy { it.keyWithPrefix() }
        .map {
            Column(
                key = it.key,
                label = it.label,
                description = it.descriptionString(),
                type = it.type,
                allowedValues = it.allowedValues,
                prefix = it.prefix)
        }
        .sortedBy {
            when (it.key) {
                RowIdentifierSpec.key -> 0
                EmployeeIdSpec.key -> 0
                ContractIdSpec.key -> 1
                else -> 2
            }
        }
