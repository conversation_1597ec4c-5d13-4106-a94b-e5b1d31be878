package com.multiplier.contract.onboarding.handler

import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobEventType
import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobMessage
import com.multiplier.bulk.upload.schema.kafka.BulkUploadJobStatus
import com.multiplier.contract.onboarding.service.bulkupload.BulkUploadService
import com.multiplier.contract.onboarding.service.notifications.BulkOnboardingJobNotificationService
import com.multiplier.messaging.api.consumer.registerConsumers
import com.multiplier.messaging.bulkuploadservice.BulkUploadServiceTopics
import mu.KotlinLogging
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

private val log = KotlinLogging.logger {}

@Configuration
class BulkUploadingJobEventListener(
    private val bulkOnboardingJobNotificationService: BulkOnboardingJobNotificationService,
) {
    companion object {
        private const val ONBOARDING_GROUP = "ONBOARDING"
    }

    @Bean
    fun kafkaConsumer() = registerConsumers {
        onMessage(BulkUploadServiceTopics.bulkUploadJobTopic) { message ->
            log.info {
                "Received bulkUploadJobTopic message for jobId: ${message.payload.id}, eventType: ${message.eventType}"
            }
            if (message.payload.group == ONBOARDING_GROUP) {
                when (message.eventType) {
                    BulkUploadJobEventType.BULK_UPLOAD_JOB_DATA_CREATED -> {
                        message.getAddedEmployeesCount()?.let { employeeCount ->
                            bulkOnboardingJobNotificationService
                                .notifyBulkOnboardingEmployeesAddedToTeam(
                                    message.payload, employeeCount)
                        }

                        log.info {
                            "Finished processing BULK_UPLOAD_JOB_DATA_CREATED message for jobId: ${message.payload.id}"
                        }
                    }
                    BulkUploadJobEventType.BULK_UPLOAD_JOB_UPDATED -> {
                        handleJobUpdated(message)
                    }
                    else -> {
                        // DO NOTHING
                    }
                }
            }
        }
    }

    private fun BulkUploadJobMessage.getAddedEmployeesCount(): Int? =
        payload.moduleDataCreationInfoList
            .firstOrNull {
                it.module == BulkUploadService.GLOBAL_PAYROLL_MEMBER_ONBOARDING ||
                    it.module == BulkUploadService.FREELANCER_MEMBER_ONBOARDING ||
                    it.module == BulkUploadService.AOR_MEMBER_ONBOARDING ||
                    it.module == BulkUploadService.EOR_MEMBER_ONBOARDING
            }
            ?.createdCount

    private fun handleJobUpdated(message: BulkUploadJobMessage) {
        when (message.payload.status) {
            BulkUploadJobStatus.VALIDATION_FAILED,
            BulkUploadJobStatus.VALIDATION_SUCCESS -> {
                bulkOnboardingJobNotificationService.notifyOnboardingFileUploadedSuccessfully(
                    message.payload)

                log.info {
                    "Finished processing bulk onboarding file uploaded event. jobId: ${message.payload.id}"
                }
            }
            else -> {
                // DO NOTHING
            }
        }
    }
}
