package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.adapter.ValidInput

data class GrpcValidationResult<T>(
    val success: Boolean,
    val errors: List<String>?,
    val validationId: String,
    val input: T?,
    val additionalInputProperties: Map<String, String> = emptyMap(),
    val groupName: String? = null,
) {
    fun toValidInputOrNull() =
        if (success && input != null) ValidInput(validationId = validationId, input = input)
        else null
}
