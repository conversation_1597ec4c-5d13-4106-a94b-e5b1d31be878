package com.multiplier.contract.onboarding.service

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.toDomain
import com.multiplier.contract.onboarding.domain.contants.ContractRevokedExperience
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.repository.JpaOnboardingRepository
import com.multiplier.contract.onboarding.types.ContractOnboardingUpdateStepInput
import com.multiplier.contract.onboarding.types.TaskResponse
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class OnboardingOpsService(
    private val jpaOnboardingRepository: JpaOnboardingRepository,
) {
    companion object {
        const val EXPERIENCE = "experience"
        const val REVOKED_BY = "revokedBy"
    }

    private val log = KotlinLogging.logger {}

    fun updateContractOnboardingStep(input: ContractOnboardingUpdateStepInput): TaskResponse {
        validateUpdateContractOnboardingStepInput(input)

        val jpaOnboardings =
            jpaOnboardingRepository.findByContractIdInAndExperience(
                setOf(input.contractId), input.experience)

        if (jpaOnboardings.isEmpty()) {
            return TaskResponse.newBuilder()
                .success(false)
                .message(
                    "Cannot find any onboarding entity with contractId=${input.contractId} and experience=${input.experience}")
                .build()
        }

        val jpaOnboarding = jpaOnboardings.first()
        log.info {
            "Updating onboarding entity: input=$input," +
                " current onboarding status of ${jpaOnboarding.experience} experience: step=${jpaOnboarding.currentStep}, status=${jpaOnboarding.status}"
        }

        val notes = mutableListOf<String>()
        input.currentStep?.let {
            jpaOnboarding.currentStep = it.toDomain()
            notes.add("{CurrentStep was updated to $it}")
        }
        input.status?.let {
            jpaOnboarding.status = it
            notes.add("{Status was updated to $it}")
        }
        input.revokedBy?.let {
            jpaOnboarding.revokedBy = ContractRevokedExperience.valueOf(it)
            notes.add("{RevokedBy was updated to $it}")
        }
        jpaOnboardingRepository.save(jpaOnboarding)

        return TaskResponse.newBuilder().success(true).message("Success with notes: $notes").build()
    }

    private fun validateUpdateContractOnboardingStepInput(
        input: ContractOnboardingUpdateStepInput
    ) {
        if (!listOf("member", "company").contains(input.experience)) {
            throw ErrorCodes.UNSUPPORTED_INPUT.toBusinessException(
                "Invalid experience. It should be either 'member' or 'company'. Provided experience = ${input.experience}",
                context = mapOf(EXPERIENCE to input.experience))
        }

        if (input.revokedBy != null &&
            !ContractRevokedExperience.entries.any { it.name == input.revokedBy }) {
            throw ErrorCodes.UNSUPPORTED_INPUT.toBusinessException(
                "Invalid revokedBy. It should be one of ${ContractRevokedExperience.entries.joinToString(prefix = "[", postfix = "]")}. Provided revokedBy = ${input.revokedBy}",
                context = mapOf(REVOKED_BY to input.revokedBy))
        }
    }
}
