package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.springframework.stereotype.Component

@Component
class USAOpsMemberSignContractReminderCtaProcessor : BaseReminderCtaTemplateProcessor() {
    override fun notificationType() = NotificationType.USAOpsMemberSignContractReminderCta

    override fun validate(preFetchedData: EmailTemplateData): Bo<PERSON>an {
        // Only validate when multi-frequency support is enabled and country is USA
        if (!preFetchedData.multiFrequencySupportEnabled ||
            preFetchedData.contract.country != "USA" ||
            preFetchedData.contract.type != ContractOuterClass.ContractType.EMPLOYEE ||
            preFetchedData.operationsUser == null) {
            return false
        }

        val onboardingCompany = preFetchedData.onboardingCompany

        return onboardingCompany.status == ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SENT &&
            onboardingCompany.currentStep == OnboardingStep.ONBOARDING_SIGNING
    }

    override fun editableParams(preFetchedData: EmailTemplateData): List<EditableParam> {
        // USA implementation only includes CutoffParam
        return listOf(CutoffParam())
    }
}
