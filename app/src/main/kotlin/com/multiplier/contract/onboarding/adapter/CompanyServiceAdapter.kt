package com.multiplier.contract.onboarding.adapter

import com.multiplier.common.exception.toBusinessException
import com.multiplier.company.schema.grpc.CompanyOfferingOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.company.schema.grpc.CompanyOuterClass.*
import com.multiplier.company.schema.grpc.CompanyServiceGrpc.CompanyServiceBlockingStub
import com.multiplier.contract.onboarding.domain.model.Company
import com.multiplier.contract.onboarding.domain.model.CompanyUser
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.types.CountryCode
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

data class GetCompanyUsersFilter(
    val companyIds: Set<Long> = emptySet(),
    val statuses: Set<CompanyOuterClass.CompanyUserStatus> = emptySet(),
)

data class GetCompanyUserByUserIdV2Response(
    val success: Boolean,
    val companyUser: CompanyOuterClass.CompanyUser?
)

interface CompanyServiceAdapter {

    fun getCompanyUsersBy(filter: GetCompanyUsersFilter): List<CompanyOuterClass.CompanyUser>

    fun getCompanyUserByUserId(id: Long): CompanyUser

    fun getMSASignedCompanyIds(companyIds: Set<Long>): Set<Long>

    fun getCompanies(companyIds: Set<Long>): List<Company>

    fun getCompany(id: Long): Company

    fun getLegalEntity(entityId: Long): CompanyOuterClass.LegalEntity?

    fun getLegalEntitiesByIds(entityIds: Set<Long>): List<CompanyOuterClass.LegalEntity>

    fun getCompanyLegalEntities(companyId: Long): List<CompanyOuterClass.LegalEntity>

    fun getCompanyOfferings(companyId: Long): MutableList<CompanyOfferingOuterClass.CompanyOffering>

    fun getCompanyOfferingsByCompanyIds(
        companyIds: Collection<Long>
    ): Map<Long, List<CompanyOfferingOuterClass.CompanyOffering>>

    fun getCompanyPrimaryLegalEntity(companyId: Long): LegalEntity?
}

@Service
class CompanyServiceAdapterImpl(
    private val countryServiceAdapter: CountryServiceAdapter,
    private val featureFlagService: FeatureFlagService,
) : CompanyServiceAdapter {

    @GrpcClient("company-service") private lateinit var blockingStub: CompanyServiceBlockingStub

    override fun getCompanyUsersBy(
        filter: GetCompanyUsersFilter
    ): List<CompanyOuterClass.CompanyUser> {
        val request =
            CompanyOuterClass.CompanyUsersFiltersRequest.newBuilder()
                .addAllCompanyIds(filter.companyIds)
                .addAllStatuses(filter.statuses)
                .build()

        return blockingStub.getCompanyUsersBy(request).itemsList
    }

    override fun getCompanyUserByUserId(id: Long): CompanyUser {
        val isV2Enabled =
            featureFlagService.isFeatureOn(
                FeatureFlags.ENABLE_GET_COMPANY_USER_BY_USER_ID_V2_CONTRACT_ONBOARDING)

        if (isV2Enabled) {
            return getCompanyUserByUserIdV2(id)
        }

        val request = GetCompanyUserByUserIdRequest.newBuilder().setUserId(id).build()
        return blockingStub.getCompanyUserByUserId(request).toDomainType()
    }

    private fun getCompanyUserByUserIdV2(id: Long): CompanyUser {
        val v2Request = GetCompanyUserByUserIdV2Request.newBuilder().setUserId(id).build()
        val v2Response = blockingStub.getCompanyUserByUserIdV2(v2Request)
        val response = v2Response.toDomainTypeV2()

        if (!response.success) {
            log.error { "getCompanyUserByUserIdV2 failed for userId: $id" }
            throw ErrorCodes.ERROR_FINDING_COMPANY_USER.toBusinessException(
                "Failed to get company user by user ID: $id", context = mapOf("userId" to id))
        }

        return response.companyUser!!.toDomainType()
    }

    override fun getMSASignedCompanyIds(companyIds: Set<Long>): Set<Long> {
        if (companyIds.isEmpty()) {
            return emptySet()
        }

        val request =
            CompanyOuterClass.GetMSASignedCompanyIdsRequest.newBuilder()
                .addAllIds(companyIds)
                .build()
        return blockingStub.getMSASignedCompanyIds(request).idsList.toSet()
    }

    override fun getCompanies(companyIds: Set<Long>): List<Company> {
        val companiesWithSignedMSAs = getMSASignedCompanyIds(companyIds)

        return blockingStub
            .getCompanies(GetCompaniesRequest.newBuilder().addAllIds(companyIds).build())
            .companiesList
            .map { it.toDomainType(companiesWithSignedMSAs.contains(it.id)) }
    }

    override fun getCompany(id: Long): Company {
        val request = CompanyOuterClass.GetCompanyRequest.newBuilder().setId(id).build()
        return blockingStub.getCompany(request).toDomainType()
    }

    override fun getLegalEntity(entityId: Long): CompanyOuterClass.LegalEntity? {
        val request =
            CompanyOuterClass.GetLegalEntitiesRequest.newBuilder()
                .addAllIds(listOf(entityId))
                .build()

        return blockingStub.getLegalEntities(request).entitiesList.firstOrNull()
    }

    override fun getLegalEntitiesByIds(entityIds: Set<Long>): List<CompanyOuterClass.LegalEntity> {
        val request =
            CompanyOuterClass.GetLegalEntitiesRequest.newBuilder().addAllIds(entityIds).build()

        return blockingStub.getLegalEntities(request).entitiesList
    }

    override fun getCompanyLegalEntities(companyId: Long): List<CompanyOuterClass.LegalEntity> {
        val request =
            CompanyOuterClass.GetLegalEntitiesRequest.newBuilder()
                .addAllCompanyIds(listOf(companyId))
                .build()

        return blockingStub.getLegalEntities(request).entitiesList
    }

    override fun getCompanyOfferings(
        companyId: Long
    ): MutableList<CompanyOfferingOuterClass.CompanyOffering> {
        val request =
            CompanyOfferingOuterClass.GetCompanyOfferingsRequest.newBuilder()
                .addCompanyIds(companyId)
                .build()

        return blockingStub.getCompanyOfferings(request).offeringsList.toMutableList()
    }

    override fun getCompanyOfferingsByCompanyIds(
        companyIds: Collection<Long>
    ): Map<Long, List<CompanyOfferingOuterClass.CompanyOffering>> {
        if (companyIds.isEmpty()) {
            log.warn("Skip getting company offerings, input company ID set is null or empty")
            return emptyMap()
        }

        val request =
            CompanyOfferingOuterClass.GetCompanyOfferingsRequest.newBuilder()
                .addAllCompanyIds(companyIds.toSet())
                .build()

        return blockingStub.getCompanyOfferings(request).offeringsList.groupBy { it.companyId }
    }

    override fun getCompanyPrimaryLegalEntity(companyId: Long): LegalEntity? {
        val request = GetCompanyRequest.newBuilder().setId(companyId).build()

        return blockingStub.getCompanyPrimaryLegalEntityByCompanyId(request)
    }

    private fun CompanyOuterClass.Company.toDomainType(msaSigned: Boolean = false): Company {
        val countryCode = getCountryCode(this.primaryEntity.address.country)
        return Company(
            id = this.id,
            logoId = this.companyLogoId.value,
            displayName = this.displayName,
            countryFullName = getCountryFullName(countryCode),
            countryCode = countryCode,
            msaSigned = msaSigned,
            isTest = this.isTest,
        )
    }

    private fun getCountryFullName(countryCode: CountryCode?): String {
        return countryCode?.let { countryServiceAdapter.getCountryNameByCode(it) } ?: ""
    }
}

private fun CompanyOuterClass.CompanyUser.toDomainType() =
    CompanyUser(
        id = this.id,
        companyId = this.companyId,
        firstName = this.firstName,
        lastName = this.lastName,
        email = this.primaryOrDefaultEmail ?: "",
    )

private val CompanyOuterClass.CompanyUser.primaryOrDefaultEmail: String?
    get() = if (primaryEmail != null) primaryEmail else defaultEmail

private val CompanyOuterClass.CompanyUser.primaryEmail: String?
    get() =
        emailsList
            .filterNotNull()
            .firstOrNull { it.type.equals("primary", ignoreCase = true) }
            ?.email

private val CompanyOuterClass.CompanyUser.defaultEmail: String?
    get() =
        emailsList
            .filterNotNull()
            .firstOrNull { it.type.equals("default", ignoreCase = true) }
            ?.email

private fun getCountryCode(country: String?): CountryCode? =
    if (country.isNullOrBlank()) null else CountryCode.valueOf(country)

private fun CompanyOuterClass.GetCompanyUserByUserIdV2Response.toDomainTypeV2() =
    GetCompanyUserByUserIdV2Response(
        success = this.success, companyUser = if (this.hasCompanyUser()) this.companyUser else null)
