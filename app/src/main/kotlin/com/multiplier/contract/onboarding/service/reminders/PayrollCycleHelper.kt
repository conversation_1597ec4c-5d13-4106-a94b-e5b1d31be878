package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.domain.model.PayrollCycle
import java.time.LocalDate
import java.time.Month
import java.time.YearMonth

private const val MONTHLY_DEFAULT_CUTOFF_DATE = 15

/**
 * This helper class provides a list of methods to create payroll cycle objects based on payroll
 * cycle configuration described here:
 * https://www.notion.so/usemultiplier/General-958da009904f49de8a6613778a5f1d90?pvs=4
 */
@Deprecated("Would be removed after release of use-payroll-service-to-get-payroll-cycles")
object PayrollCycleHelper {

    fun getMonthlyPayrollCycleForCutoffDate(cutoffDate: LocalDate): PayrollCycleContracts? {
        val cutoffDayOfMonth = if (cutoffDate.monthValue == 12) 5 else MONTHLY_DEFAULT_CUTOFF_DATE

        return if (cutoffDate.dayOfMonth != cutoffDayOfMonth) {
            null
        } else getMonthlyPayrollCycleForMonth(YearMonth.from(cutoffDate))
    }

    fun getMonthlyPayrollCycleForMonth(payrollMonth: YearMonth): PayrollCycleContracts {
        val cutoffDayOfMonth = if (payrollMonth.monthValue == 12) 5 else MONTHLY_DEFAULT_CUTOFF_DATE

        return PayrollCycleContracts(
            cutoffDate = payrollMonth.atDay(cutoffDayOfMonth),
            payrollMonth = payrollMonth,
            payFrequency = PayrollCycle.PayFrequency.MONTHLY,
            payDay = 25,
            payDate = payrollMonth.atDay(25),
            contractStartFrom = payrollMonth.minusMonths(1).atDay(21),
            contractStartTo = payrollMonth.atDay(20),
        )
    }

    fun getSemiMonthly15thPayrollCycleForCutoffDate(cutoffDate: LocalDate): PayrollCycleContracts? {
        val cutoffMonth = YearMonth.from(cutoffDate)
        return if (cutoffDate.isEqual(cutoffMonth.atEndOfMonth())) {
            getSemiMonthly15thPayrollCycleForMonth(cutoffMonth.plusMonths(1))
        } else null
    }

    fun getSemiMonthly15thPayrollCycleForMonth(payrollMonth: YearMonth): PayrollCycleContracts {
        return PayrollCycleContracts(
            cutoffDate = payrollMonth.minusMonths(1).atEndOfMonth(),
            payrollMonth = payrollMonth,
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payDay = 15,
            payDate = payrollMonth.atDay(15),
            contractStartFrom = payrollMonth.atDay(1),
            contractStartTo = payrollMonth.atDay(15),
        )
    }

    fun getSemiMonthly30thPayrollCycleForCutoffDate(cutoffDate: LocalDate): PayrollCycleContracts? {
        return if (cutoffDate.getDayOfMonth() != 15) {
            null
        } else getSemiMonthly30thPayrollCycleForMonth(YearMonth.from(cutoffDate))
    }

    fun getSemiMonthly30thPayrollCycleForMonth(payrollMonth: YearMonth): PayrollCycleContracts {
        return PayrollCycleContracts(
            cutoffDate = payrollMonth.atDay(15),
            payrollMonth = payrollMonth,
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payDay = 30,
            payDate =
                if (payrollMonth.month == Month.FEBRUARY) payrollMonth.atDay(28)
                else payrollMonth.atDay(30),
            contractStartFrom = payrollMonth.atDay(16),
            contractStartTo = payrollMonth.atEndOfMonth(),
        )
    }

    fun getSemiMonthly10thPayrollCycleForCutoffDate(cutoffDate: LocalDate): PayrollCycleContracts? {
        val cutoffMonth = YearMonth.from(cutoffDate)
        return if (cutoffDate.isEqual(cutoffMonth.atEndOfMonth())) {
            getSemiMonthly10thPayrollCycleForMonth(cutoffMonth.plusMonths(1))
        } else null
    }

    fun getSemiMonthly10thPayrollCycleForMonth(payrollMonth: YearMonth): PayrollCycleContracts {
        return PayrollCycleContracts(
            cutoffDate = payrollMonth.minusMonths(1).atEndOfMonth(),
            payrollMonth = payrollMonth,
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payDay = 10,
            payDate = payrollMonth.atDay(10),
            contractStartFrom = payrollMonth.minusMonths(1).atDay(16),
            contractStartTo = payrollMonth.minusMonths(1).atEndOfMonth(),
        )
    }

    fun getSemiMonthly25thPayrollCycleForCutoffDate(cutoffDate: LocalDate): PayrollCycleContracts? {
        return if (cutoffDate.getDayOfMonth() != 15) {
            null
        } else getSemiMonthly25thPayrollCycleForMonth(YearMonth.from(cutoffDate))
    }

    fun getSemiMonthly25thPayrollCycleForMonth(payrollMonth: YearMonth): PayrollCycleContracts {
        return PayrollCycleContracts(
            cutoffDate = payrollMonth.atDay(15),
            payrollMonth = payrollMonth,
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payDay = 25,
            payDate = payrollMonth.atDay(25),
            contractStartFrom = payrollMonth.atDay(1),
            contractStartTo = payrollMonth.atDay(15),
        )
    }

    fun getSemiMonthly14thPayrollCycleForCutoffDate(cutoffDate: LocalDate): PayrollCycleContracts? {
        val cutoffMonth = YearMonth.from(cutoffDate)
        return if (cutoffDate.isEqual(cutoffMonth.atEndOfMonth())) {
            getSemiMonthly14thPayrollCycleForMonth(cutoffMonth.plusMonths(1))
        } else null
    }

    fun getSemiMonthly14thPayrollCycleForMonth(payrollMonth: YearMonth): PayrollCycleContracts {
        return PayrollCycleContracts(
            cutoffDate = payrollMonth.minusMonths(1).atEndOfMonth(),
            payrollMonth = payrollMonth,
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payDay = 14,
            payDate = payrollMonth.atDay(14),
            contractStartFrom = payrollMonth.atDay(1),
            contractStartTo = payrollMonth.atDay(15),
        )
    }

    fun getSemiMonthly28thPayrollCycleForCutoffDate(cutoffDate: LocalDate): PayrollCycleContracts? {
        return if (cutoffDate.getDayOfMonth() != 15) {
            null
        } else getSemiMonthly28thPayrollCycleForMonth(YearMonth.from(cutoffDate))
    }

    fun getSemiMonthly28thPayrollCycleForMonth(payrollMonth: YearMonth): PayrollCycleContracts {
        return PayrollCycleContracts(
            cutoffDate = payrollMonth.atDay(15),
            payrollMonth = payrollMonth,
            payFrequency = PayrollCycle.PayFrequency.SEMIMONTHLY,
            payDay = 28,
            payDate = payrollMonth.atDay(28),
            contractStartFrom = payrollMonth.atDay(16),
            contractStartTo = payrollMonth.atEndOfMonth(),
        )
    }

    fun getPrevMonthCycle(payrollCycle: PayrollCycleContracts): PayrollCycleContracts {
        if (payrollCycle.payFrequency === PayrollCycle.PayFrequency.SEMIMONTHLY) {
            when (payrollCycle.payDay) {
                10 ->
                    return getSemiMonthly10thPayrollCycleForMonth(
                        payrollCycle.payrollMonth.minusMonths(1))
                25 ->
                    return getSemiMonthly25thPayrollCycleForMonth(
                        payrollCycle.payrollMonth.minusMonths(1))
                15 ->
                    return getSemiMonthly15thPayrollCycleForMonth(
                        payrollCycle.payrollMonth.minusMonths(1))
                30 ->
                    return getSemiMonthly30thPayrollCycleForMonth(
                        payrollCycle.payrollMonth.minusMonths(1))
                14 ->
                    return getSemiMonthly14thPayrollCycleForMonth(
                        payrollCycle.payrollMonth.minusMonths(1))
                28 ->
                    return getSemiMonthly28thPayrollCycleForMonth(
                        payrollCycle.payrollMonth.minusMonths(1))
            }
        }
        return getMonthlyPayrollCycleForMonth(payrollCycle.payrollMonth.minusMonths(1))
    }

    fun getPrevCycle(payrollCycle: PayrollCycleContracts): PayrollCycleContracts {
        if (payrollCycle.payFrequency === PayrollCycle.PayFrequency.SEMIMONTHLY) {
            when (payrollCycle.payDay) {
                10 ->
                    return getSemiMonthly25thPayrollCycleForMonth(
                        payrollCycle.payrollMonth.minusMonths(1))
                25 -> return getSemiMonthly10thPayrollCycleForMonth(payrollCycle.payrollMonth)
                15 ->
                    return getSemiMonthly30thPayrollCycleForMonth(
                        payrollCycle.payrollMonth.minusMonths(1))
                30 -> return getSemiMonthly15thPayrollCycleForMonth(payrollCycle.payrollMonth)
                14 ->
                    return getSemiMonthly28thPayrollCycleForMonth(
                        payrollCycle.payrollMonth.minusMonths(1))
                28 -> return getSemiMonthly14thPayrollCycleForMonth(payrollCycle.payrollMonth)
            }
        }
        return getMonthlyPayrollCycleForMonth(payrollCycle.payrollMonth.minusMonths(1))
    }
}
