package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.repository.JpaOnboardingRepository
import com.multiplier.contract.onboarding.service.countryAndState
import com.multiplier.country.schema.Country
import java.util.concurrent.CompletableFuture
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

@Service
class EmailTemplateDataService(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val memberServiceAdapter: MemberServiceAdapter,
    private val countryServiceAdapter: CountryServiceAdapter,
    private val companyServiceAdapter: CompanyServiceAdapter,
    private val operationsUserServiceAdapter: OperationsUserServiceAdapter,
    private val onboardingRepository: JpaOnboardingRepository,
    private val featureFlagService: FeatureFlagService,
    @Value("\${onboarding.guide-link}") private val onboardingGuideLink: String
) {
    private val log = KotlinLogging.logger {}

    fun fetchEmailTemplateData(contractId: Long): EmailTemplateData {
        val contract = contractServiceAdapter.getContractById(contractId)

        val cfCompany =
            CompletableFuture.supplyAsync { companyServiceAdapter.getCompany(contract.companyId) }
                .exceptionally {
                    log.error(it) {
                        "failed to fetch company with id ${contract.companyId} with error ${it.message}"
                    }
                    null
                }

        val cfMember =
            CompletableFuture.supplyAsync { memberServiceAdapter.getMember(contract.memberId) }
                .exceptionally {
                    log.error(it) {
                        "failed to fetch member with id ${contract.memberId} with error ${it.message}"
                    }
                    null
                }

        val cfContractOnboardingSpecialist =
            CompletableFuture.supplyAsync {
                    operationsUserServiceAdapter
                        .getOperationsUsersForContracts(listOf(contract.id))
                        .values
                        .firstOrNull()
                }
                .exceptionally {
                    log.error(it) {
                        "failed to fetch operationsUser for contract ${contract.id} with error ${it.message}"
                    }
                    null
                }

        val cfLegalDocumentsMap =
            CompletableFuture.supplyAsync {
                    memberServiceAdapter.getLegalDocumentsKeyByMemberIds(setOf(contract.memberId))
                }
                .exceptionally {
                    log.error(it) {
                        "failed to fetch submitted forms for member ${contract.memberId} with error ${it.message}"
                    }
                    emptyMap()
                }

        val cfPayrollFormRequirements =
            CompletableFuture.supplyAsync {
                    countryServiceAdapter.getRequiredDocumentKeysGroupByCountryCode(
                        setOf(contract.countryAndState),
                        Country.GrpcLegalDocumentRequirement.GrpcLegalDocumentCategory.PAYROLL)
                }
                .exceptionally {
                    log.error(it) {
                        "failed to fetch required payroll forms for countryAndState ${contract.countryAndState} with error ${it.message}"
                    }
                    emptyMap()
                }

        val cfCompensation =
            CompletableFuture.supplyAsync {
                    contractServiceAdapter.getCurrentCompensationByContractIds(contract.id)
                }
                .exceptionally {
                    log.error(it) {
                        "failed to fetch compensation for contract ${contract.id} with error ${it.message}"
                    }
                    null
                }

        val cfMultiFrequencySupportEnabled =
            CompletableFuture.supplyAsync {
                    featureFlagService.isFeatureOn(
                        FeatureFlags.ENABLE_MULTI_FREQUENCY_SUPPORT_REMINDER_CTA)
                }
                .exceptionally {
                    log.error(it) {
                        "failed to check multi-frequency support feature flag for contract ${contract.id} with error ${it.message}"
                    }
                    false
                }

        CompletableFuture.allOf(
                cfCompany,
                cfMember,
                cfContractOnboardingSpecialist,
                cfLegalDocumentsMap,
                cfPayrollFormRequirements,
                cfCompensation,
                cfMultiFrequencySupportEnabled)
            .join()

        val company =
            cfCompany.join()
                ?: throw ErrorCodes.COMPANY_NOT_FOUND.toBusinessException("Company data not found")
        val member =
            cfMember.join()
                ?: throw ErrorCodes.MEMBER_NOT_FOUND.toBusinessException("Member data not found")
        val contractOnboardingSpecialist = cfContractOnboardingSpecialist.join()
        val legalDocumentsMap = cfLegalDocumentsMap.join() ?: emptyMap()
        val payrollFormRequirements = cfPayrollFormRequirements.join() ?: emptyMap()
        val compensation = cfCompensation.join()
        val multiFrequencySupportEnabled = cfMultiFrequencySupportEnabled.join()

        val onboardingCompany =
            onboardingRepository.findByContractIdAndExperience(contractId, "company")
        if (onboardingCompany.isEmpty) {
            throw ErrorCodes.ONBOARDING_ENTITY_NOT_FOUND.toBusinessException(
                "Onboarding data for company not found for contract ID $contractId")
        }

        val onboardingMember =
            onboardingRepository.findByContractIdAndExperience(contractId, "member").orElse(null)

        // Check if preregistration is enabled for the contract's country
        val preregistrationRequiredCountry =
            featureFlagService.isFeatureOn(
                FeatureFlags.PREREGISTRATION_ENABLED_COUNTRIES_REMINDERS,
                mapOf(FeatureFlags.Params.ONBOARDING_COUNTRY to contract.country))

        // Extract payFrequency from compensation basePay
        val payFrequency = compensation?.basePay?.payFrequency?.name?.removePrefix("PAY_FREQUENCY_")

        return EmailTemplateData(
            company = company,
            member = member,
            operationsUser = contractOnboardingSpecialist,
            onboardingCompany = onboardingCompany.get(),
            onboardingMember = onboardingMember,
            contract = contract,
            submittedLegalDocument = legalDocumentsMap[member.id] ?: emptySet(),
            payrollFormRequirements = payrollFormRequirements,
            guideLink = onboardingGuideLink,
            preregistrationRequiredCountry = preregistrationRequiredCountry,
            payFrequency = payFrequency,
            multiFrequencySupportEnabled = multiFrequencySupportEnabled)
    }
}
