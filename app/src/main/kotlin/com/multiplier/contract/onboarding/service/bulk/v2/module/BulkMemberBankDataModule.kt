package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.PaymentServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkMemberBankDataAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.PaymentAccountDataField
import com.multiplier.contract.onboarding.domain.model.PaymentAccountRequirement
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule.Companion.BANK_DATA_SPEC_PREFIX
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkMemberBankDataModule.Companion.DYNAMIC_BANK_DETAILS_SPEC
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.member.schema.UpsertBankDetailsInput
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class BulkMemberBankDataModule(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val paymentServiceAdapter: PaymentServiceAdapter,
    private val bulkMemberBankDataAdapter: BulkMemberBankDataAdapter,
    private val featureFlagService: FeatureFlagService
) : BulkDataModule {

    private val log = KotlinLogging.logger {}

    companion object {
        const val MODULE_NAME = "MEMBER_BANK_DATA_MODULE"

        const val STATIC_BANK_DETAILS_SPEC = "STATIC_BANK_DETAILS_SPEC"
        const val DYNAMIC_BANK_DETAILS_SPEC = "DYNAMIC_BANK_DETAILS_SPEC"
        const val BANK_DATA_SPEC_PREFIX = "bank"

        object StaticBankDataSpec {
            private val BaseStaticBankDataSpec =
                DataSpec(
                    key = "base",
                    type = DataSpecType.TEXT,
                    prefix = BANK_DATA_SPEC_PREFIX,
                    source = STATIC_BANK_DETAILS_SPEC)
            val AccountHolderNameSpec =
                BaseStaticBankDataSpec.copy(
                    key = "accountHolderName",
                    label = "Bank Account Holder Name",
                    description = "Name on employee's bank account for salary payment")
            val AccountNumberSpec =
                BaseStaticBankDataSpec.copy(
                    key = "accountNumber",
                    label = "Bank Account Number",
                    description = "Employee's bank account number for salary payment")
            val BankNameSpec =
                BaseStaticBankDataSpec.copy(
                    key = "bankName",
                    label = "Bank Name",
                    description = "Name of employee's bank for salary payment")
            val BranchNameSpec =
                BaseStaticBankDataSpec.copy(key = "bankBranch", label = "Bank Branch Name")
            val SwiftCodeSpec =
                BaseStaticBankDataSpec.copy(
                    key = "swiftCode",
                    label = "SWIFT Code",
                    description = "Bank's SWIFT code for salary payment")
            val LocalBankCodeSpec =
                BaseStaticBankDataSpec.copy(key = "localBankCode", label = "Local Bank Code")
        }
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {

        if (featureFlagService.isFeatureOn(
            FeatureFlags.CUSTOM_BANK_FIELDS,
            mapOf(FeatureFlags.Params.ENTITY_ID to onboardingOptions.entityId))) {
            return listOf(
                StaticBankDataSpec.AccountHolderNameSpec,
                StaticBankDataSpec.AccountNumberSpec,
                StaticBankDataSpec.BankNameSpec,
                StaticBankDataSpec.BranchNameSpec,
                StaticBankDataSpec.SwiftCodeSpec,
                StaticBankDataSpec.LocalBankCodeSpec)
        }
        val paymentAccountRequirement: PaymentAccountRequirement? =
            getPaymentAccountRequirementForMember(
                onboardingOptions.countryCode, onboardingOptions.contractType)
        return getMemberBankDataSpecs(paymentAccountRequirement)
    }

    private fun getPaymentAccountRequirementForMember(
        countryCode: CountryCode,
        contractType: ContractType
    ): PaymentAccountRequirement? {
        val paymentAccountRequirementsRequest =
            contractServiceAdapter.getPaymentAccountRequirementsRequestForMember(
                countryCode, contractType)
        val paymentAccountRequirement: PaymentAccountRequirement? =
            paymentServiceAdapter.getPaymentAccountRequirementForMember(
                paymentAccountRequirementsRequest)
        if (paymentAccountRequirement == null) {
            log.info {
                "No payment account requirement found for contract type: $contractType in country: $countryCode"
            }
        }

        return paymentAccountRequirement
    }

    private fun getMemberBankDataSpecs(
        paymentAccountRequirement: PaymentAccountRequirement?
    ): List<DataSpec> {
        return paymentAccountRequirement?.requiredDataFields?.map { it.toDataSpec() }
            ?: listOf(
                StaticBankDataSpec.AccountHolderNameSpec,
                StaticBankDataSpec.AccountNumberSpec,
                StaticBankDataSpec.BankNameSpec,
                StaticBankDataSpec.BranchNameSpec,
                StaticBankDataSpec.SwiftCodeSpec,
                StaticBankDataSpec.LocalBankCodeSpec)
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {
        return bulkMemberBankDataAdapter.validate(employeeData, options)
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        val upsertBankDataInputs =
            validationResults.map {
                CreationInput(
                    requestId = it.validationId,
                    memberId = bulkCreationResult.getMemberId(it.validationId),
                    data = it.input as UpsertBankDetailsInput)
            }
        return bulkUpsert(
            "Upsert bank data",
            upsertBankDataInputs,
            options,
            bulkCreationResult,
            CreationInput.refMemberId) { inputs, _ ->
                bulkMemberBankDataAdapter.upsertBankData(inputs)
            }
    }
}

private fun PaymentAccountDataField.toDataSpec(): DataSpec {
    val dataSpecType =
        when (this.type) {
            "text" -> DataSpecType.TEXT
            "select" -> DataSpecType.SELECT
            else -> DataSpecType.TEXT
        }

    return DataSpec(
        key = this.key,
        label = this.label.addBankPrefix(),
        type = dataSpecType,
        mandatory = this.mandatory,
        allowedValues = this.allowedValues,
        source = DYNAMIC_BANK_DETAILS_SPEC,
        prefix = BANK_DATA_SPEC_PREFIX)
}

private fun String.addBankPrefix(): String {
    return when (this) {
        "Country" -> "Bank Country"
        else -> this
    }
}
