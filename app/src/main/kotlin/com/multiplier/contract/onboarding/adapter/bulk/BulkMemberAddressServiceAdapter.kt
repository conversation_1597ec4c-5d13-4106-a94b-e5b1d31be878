package com.multiplier.contract.onboarding.adapter.bulk

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.CreationResult
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.member.schema.BulkAPIServiceGrpc
import com.multiplier.member.schema.BulkUpsertAddressesInput
import com.multiplier.member.schema.BulkValidateRequest
import com.multiplier.member.schema.UpsertAddressInput
import com.multiplier.member.schema.ValidationInput
import mu.KotlinLogging
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class BulkMemberAddressServiceAdapter {

    @GrpcClient("member-service")
    private lateinit var bulkMemberService: BulkAPIServiceGrpc.BulkAPIServiceBlockingStub

    fun validateUpsertAddresses(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): List<GrpcValidationResult<UpsertAddressInput>> {
        val request =
            BulkValidateRequest.newBuilder()
                .setContext(options.context.name)
                .addAllInputs(employeeData.map { it.toGrpc() })
                .build()

        return bulkMemberService.validateUpsertAddresses(request).validationResultsList.map {
            GrpcValidationResult(
                success = it.success,
                errors = it.errorsList,
                validationId = it.requestId,
                input = it.validatedInput,
            )
        }
    }

    fun upsertAddresses(inputs: List<CreationInput<UpsertAddressInput>>): List<CreationResult> {
        if (inputs.isEmpty()) return emptyList()

        val request =
            BulkUpsertAddressesInput.newBuilder()
                .addAllInputs(
                    inputs.map {
                        it.data
                            .toBuilder()
                            .setRequestId(it.requestId)
                            .setAddressDetails(
                                it.data.addressDetails
                                    .toBuilder()
                                    .setMemberId(
                                        requireNotNull(it.memberId) {
                                            "Member ID for address details upsert must not be null"
                                        })
                                    .build())
                            .build()
                    })
                .build()
        try {
            val response = bulkMemberService.bulkUpsertAddresses(request)
            return response.resultsList.map {
                CreationResult(
                    requestId = it.requestId, success = it.success, errors = it.errorsList)
            }
        } catch (e: Exception) {
            log.warn(e) { "Failed to bulk upsert addresses data" }
            return inputs.map {
                CreationResult.error(
                    it.requestId,
                    "Upsert address details failed due to an internal error: unknown exception occurred")
            }
        }
    }
}

private fun EmployeeData.toGrpc(): ValidationInput =
    ValidationInput.newBuilder()
        .setRequestId(this.identification.validationId)
        .putAllProperties(this.data)
        .build()
