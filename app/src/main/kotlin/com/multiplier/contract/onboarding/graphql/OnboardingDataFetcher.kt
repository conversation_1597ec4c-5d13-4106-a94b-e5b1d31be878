package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.DgsConstants.CONTRACTONBOARDING
import com.multiplier.contract.onboarding.graphql.dataloader.ActivationCutoffDataDataLoader
import com.multiplier.contract.onboarding.graphql.dataloader.OnboardingTasksDataLoader
import com.multiplier.contract.onboarding.types.ActivationCutoffData
import com.multiplier.contract.onboarding.types.ContractOnboarding
import com.multiplier.contract.onboarding.types.OnboardingTask
import com.multiplier.transaction.graphql.graphApi
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsData
import com.netflix.graphql.dgs.DgsDataFetchingEnvironment
import com.netflix.graphql.dgs.DgsEntityFetcher
import java.time.LocalDate
import java.util.concurrent.CompletableFuture
import org.dataloader.DataLoader

@DgsComponent
class OnboardingDataFetcher {

    @DgsData(parentType = CONTRACTONBOARDING.TYPE_NAME, field = CONTRACTONBOARDING.Tasks)
    fun tasks(dfe: DgsDataFetchingEnvironment): CompletableFuture<List<OnboardingTask>> = graphApi {
        val onboarding = dfe.getSource<ContractOnboarding>()
        val dataLoader: DataLoader<Long, List<OnboardingTask>> =
            dfe.getDataLoader(OnboardingTasksDataLoader::class.java)
        dataLoader.load(onboarding.id)
    }

    @DgsData(
        parentType = CONTRACTONBOARDING.TYPE_NAME, field = CONTRACTONBOARDING.ActivationCutoffData)
    fun activationCutoffData(
        dfe: DgsDataFetchingEnvironment
    ): CompletableFuture<ActivationCutoffData?> = graphApi {
        val onboarding = dfe.getSource<ContractOnboarding>()
        val dataLoader: DataLoader<Long, ActivationCutoffData?> =
            dfe.getDataLoader(ActivationCutoffDataDataLoader::class.java)
        dataLoader.load(onboarding.id)
    }

    @DgsData(parentType = CONTRACTONBOARDING.TYPE_NAME, field = CONTRACTONBOARDING.ActivationCutoff)
    fun activationCutoff(dfe: DgsDataFetchingEnvironment): CompletableFuture<LocalDate?> =
        graphApi {
            val onboarding = dfe.getSource<ContractOnboarding>()
            val dataLoader: DataLoader<Long, ActivationCutoffData?> =
                dfe.getDataLoader(ActivationCutoffDataDataLoader::class.java)
            dataLoader.load(onboarding.id).thenApply { it?.date }
        }

    @DgsEntityFetcher(name = CONTRACTONBOARDING.TYPE_NAME)
    fun contractOnboardingDataFetcher(keys: Map<String, Any>): ContractOnboarding = graphApi {
        ContractOnboarding().apply { this.id = (keys["id"] as String).toLong() }
    }
}
