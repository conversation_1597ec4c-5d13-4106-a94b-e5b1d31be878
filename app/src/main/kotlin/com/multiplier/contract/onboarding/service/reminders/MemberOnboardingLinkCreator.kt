package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.DocumentServiceAdapter
import com.multiplier.contract.onboarding.adapter.UserServiceAdapter
import com.multiplier.contract.onboarding.domain.contants.OnboardingTaskName
import com.multiplier.contract.onboarding.domain.model.Company
import com.multiplier.contract.onboarding.domain.model.Contract
import com.multiplier.contract.onboarding.domain.model.Member
import com.multiplier.contract.onboarding.domain.model.OnboardingTask
import com.multiplier.contract.onboarding.service.urlEncode
import java.nio.charset.Charset
import org.apache.http.client.utils.URLEncodedUtils
import org.apache.http.message.BasicNameValuePair
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class MemberOnboardingLinkCreator(
    private val documentServiceAdapter: DocumentServiceAdapter,
    private val userServiceAdapter: UserServiceAdapter,
    @Value("\${platform.base-url}") private val baseUrl: String,
) {
    fun createOnboardingLink(
        contract: Contract,
        memberInvitedToSignContract: Boolean,
        company: Company,
        companyLogoLink: String?,
        member: Member,
        tasks: List<OnboardingTask>
    ): String {
        return when {
            !memberInvitedToSignContract -> dashboardLink()
            tasks.hasIncompleteTask(OnboardingTaskName.SIGN_CONTRACT) ->
                generateSignContractLink(
                    agreementId = contract.agreementId,
                    memberEmail = member.email,
                    companyName = company.displayName,
                    countryName = company.countryFullName,
                    logoLink = companyLogoLink,
                )
            tasks.hasIncompleteTask(OnboardingTaskName.PLATFORM_ONBOARDING) ->
                generatePlatformRegistrationLink(member, company.displayName)
            else -> dashboardLink()
        }
    }

    private fun generatePlatformRegistrationLink(member: Member, companyName: String): String {
        val activationKey =
            userServiceAdapter.getUserByEmail(member.email).activationKey
                ?: return "$baseUrl/member?invited=true"

        return "$baseUrl/login/invite/member?hash=$activationKey&name=${member.fullName.urlEncode()}&company=${companyName.urlEncode()}&email=${member.email.urlEncode()}"
    }

    private fun generateSignContractLink(
        agreementId: Long?,
        memberEmail: String,
        companyName: String?,
        countryName: String?,
        logoLink: String?,
    ): String {
        if (agreementId == null) {
            return dashboardLink()
        }

        val shareResponse =
            documentServiceAdapter.getDocumentShareOrNull(agreementId, memberEmail)
                ?: return dashboardLink()

        val queryParams =
            listOfNotNull(
                companyName?.let { BasicNameValuePair("name", companyName) },
                countryName?.let { BasicNameValuePair("country", countryName) },
                companyName?.let { BasicNameValuePair("logo", logoLink) },
            )

        val encodedQueryParams = URLEncodedUtils.format(queryParams, Charset.defaultCharset())

        val sessionId = shareResponse.sessionId
        return "$baseUrl/documents/session/$sessionId?$encodedQueryParams"
    }

    private fun dashboardLink() = "$baseUrl/member/dashboard"
}

private fun List<OnboardingTask>.hasIncompleteTask(name: OnboardingTaskName) =
    this.any { it.name == name && !it.completed }
