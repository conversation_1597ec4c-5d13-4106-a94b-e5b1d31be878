package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.constants.OnboardingReminders
import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.domain.model.clientName
import com.multiplier.contract.onboarding.domain.model.memberName
import com.multiplier.contract.onboarding.domain.model.onboardingSpecialistFullName
import com.multiplier.contract.onboarding.types.CountryCode

object ReminderCtaHelper {

    const val MEMBER_NAME = "memberName"
    const val ONBOARDING_SPECIALIST = "onboardingSpecialist"
    const val CLIENT_NAME = "clientName"
    const val GUIDE_LINK = "guideLink"
    const val MONTH_PAY_DATE = "monthPayDate"
    const val IS_PRE_REGISTRATION_COUNTRY = "isPreRegistrationCountry"
    const val MULTI_FREQUENCY_SUPPORT_ENABLED = "multiFrequencySupportEnabled"
    const val PAY_FREQUENCY = "payFrequency"

    fun getPayrollCutoffDate(
        preFetchedData: EmailTemplateData,
    ): String {
        return if (preFetchedData.contract.country == CountryCode.PAK.name ||
            preFetchedData.contract.country == CountryCode.JPN.name)
            OnboardingReminders.PAYROLL_CUTOFF_10TH
        else OnboardingReminders.PAYROLL_CUTOFF_15TH
    }

    fun baseFields(preFetchedData: EmailTemplateData): MutableMap<String, String> {
        return mutableMapOf(
            MEMBER_NAME to preFetchedData.memberName,
            ONBOARDING_SPECIALIST to preFetchedData.onboardingSpecialistFullName,
            CLIENT_NAME to preFetchedData.clientName)
    }

    fun guideLinkField(preFetchedData: EmailTemplateData): Pair<String, String> {
        return GUIDE_LINK to preFetchedData.guideLink
    }

    fun monthPayDateField(preFetchedData: EmailTemplateData): Pair<String, String> {
        return MONTH_PAY_DATE to getPayrollCutoffDate(preFetchedData)
    }

    /**
     * Adds additional fields to the data map including:
     * - preRegistration: "true" if preFetchedData.preregistration > 0, "false" otherwise
     * - payFrequency: value from preFetchedData.payFrequency
     * - multiFrequencySupportEnabled: "true" if feature flag is enabled, "false" otherwise
     */
    fun addMultiFrequencyFields(
        data: MutableMap<String, String>,
        preFetchedData: EmailTemplateData
    ) {
        // Add preRegistration field based on preregistration value
        data[IS_PRE_REGISTRATION_COUNTRY] = preFetchedData.preregistrationRequiredCountry.toString()

        // Add payFrequency field if available
        preFetchedData.payFrequency?.let { payFreq -> data[PAY_FREQUENCY] = payFreq }

        // Add multiFrequencySupportEnabled field based on feature flag
        data[MULTI_FREQUENCY_SUPPORT_ENABLED] =
            preFetchedData.multiFrequencySupportEnabled.toString()
    }

    /**
     * Returns the standard editable parameters for reminder CTA processors.
     *
     * @param preFetchedData The email template data containing feature flags and configuration
     * @return List of editable parameters based on feature flags and conditions
     */
    fun getMultiFrequencyEditableParams(preFetchedData: EmailTemplateData): List<EditableParam> {
        // Return empty list if multi-frequency support is disabled
        if (!preFetchedData.multiFrequencySupportEnabled) {
            return emptyList()
        }

        val params = mutableListOf<EditableParam>()

        // Always include cutoff parameter
        params.add(CutoffParam())

        // Only include month parameter when:
        // 1. preregistrationRequiredCountry is false AND
        // 2. payFrequency is not SEMIMONTHLY
        if (!preFetchedData.preregistrationRequiredCountry &&
            preFetchedData.payFrequency != "SEMIMONTHLY") {
            params.add(MonthYearParam())
        }

        return params
    }
}
