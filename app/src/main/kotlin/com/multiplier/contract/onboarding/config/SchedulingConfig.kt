package com.multiplier.contract.onboarding.config

import javax.sql.DataSource
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.scheduling.annotation.EnableScheduling

object SchedulerConfig {
    const val DEFAULT_LOCK_AT_LEAST_FOR = "PT20M"
    const val DEFAULT_LOCK_AT_MOST_FOR = "PT40M"
    const val LOCK_AT_LEAST_FOR = "PT30M" // 30 min
    const val LOCK_AT_MOST_FOR = "PT60M" // 60 min
}

@Configuration
@EnableScheduling
@EnableSchedulerLock(
    defaultLockAtMostFor = SchedulerConfig.DEFAULT_LOCK_AT_LEAST_FOR,
    defaultLockAtLeastFor = SchedulerConfig.DEFAULT_LOCK_AT_MOST_FOR)
class SchedulingConfig {

    @Bean
    fun lockProvider(dataSource: DataSource): LockProvider {
        return JdbcTemplateLockProvider(
            JdbcTemplateLockProvider.Configuration.builder()
                .withJdbcTemplate(JdbcTemplate(dataSource))
                .withTableName("cronjob.shedlock")
                .build())
    }
}
