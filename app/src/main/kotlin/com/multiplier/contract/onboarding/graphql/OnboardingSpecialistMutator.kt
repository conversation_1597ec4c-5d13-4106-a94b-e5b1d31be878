package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.DgsConstants
import com.multiplier.contract.onboarding.service.ContractOnboardingAccessValidation
import com.multiplier.contract.onboarding.service.OnboardingSpecialistService
import com.multiplier.contract.onboarding.types.SendEmailToOnboardingSpecialistResponse
import com.multiplier.transaction.graphql.graphApi
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsMutation
import com.netflix.graphql.dgs.InputArgument
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class OnboardingSpecialistMutator(
    private val onboardingSpecialistService: OnboardingSpecialistService,
    private val accessValidation: ContractOnboardingAccessValidation,
) {
    @PreAuthorize(
        "(" +
            "@me.allowed('view.company.contract.onboarding')" +
            "||" +
            "@me.allowed('view.member.contract.onboarding')" +
            ")")
    @DgsMutation(field = DgsConstants.MUTATION.SendEmailToOnboardingSpecialist)
    fun sendEmailToOnboardingSpecialist(
        @InputArgument contractId: Long,
        @InputArgument message: String
    ): SendEmailToOnboardingSpecialistResponse = graphApi {
        accessValidation.validateByContractId(contractId)

        val requester =
            onboardingSpecialistService.sendEmailToOnboardingSpecialist(contractId, message)

        SendEmailToOnboardingSpecialistResponse(
            true, "Email sent to onboarding specialist", requester)
    }
}
