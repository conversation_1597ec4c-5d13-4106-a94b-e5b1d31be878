package com.multiplier.contract.onboarding.service.bulk

import com.multiplier.contract.onboarding.service.convertToMap
import com.multiplier.contract.onboarding.service.notifications.NotificationService
import com.multiplier.contract.onboarding.service.reminders.Notification
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingJobStatus
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.transaction.spring.TraceSpan
import java.text.MessageFormat
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service

data class BulkOnboardingCompletedEmailDto(
    val subject: String,
    val to: String,
    val cc: List<String>,
    val notificationType: NotificationType,
    val onboardingContext: BulkOnboardingContext,
    val companyId: Long,
    val entityId: Long?,
    val jobId: String,
    val totalEmployeeCount: Int,
    val onboardedEmployeeCount: Int,
    val memberIds: List<Long>,
    val contractIds: List<Long>,
    val source: String = "Unknown",
) {
    fun toPropertyMap(): Map<String, Any> =
        this.convertToMap(
            overrides =
                mapOf(
                    "memberIds" to memberIds.joinToString(", "),
                    "contractIds" to contractIds.joinToString(", "),
                    "source" to source))
}

@Service
class BulkOnboardingNotificationService(
    private val notificationService: NotificationService,
    @Value("\${ops.support-email}") private val systemNotificationEmail: String,
    @Value("\${ops.member-onboarding-email}") private val opsMemberOnboardingEmail: String,
    @Value("\${ops.sales-email}") private val opsSalesEmail: String,
    @Value("\${ops.customer-success-email}") private val opsCustomerSuccessEmail: String,
    @Value("\${platform.notification.slack.channel.bulk-onboarding}")
    private val bulkOnboardingSlackChannelEmail: String
) {

    private val log = KotlinLogging.logger {}

    @TraceSpan
    fun sendOnboardingCompletedNotification(
        options: BulkOnboardingOptions,
        onboardingJob: BulkOnboardingJob,
        source: String,
    ) {
        val dto = createOnboardingCompletedEmailDto(options, onboardingJob, source)
        val notification =
            Notification(
                subject = dto.subject,
                from = MessageFormat.format("Multiplier <{0}>", systemNotificationEmail),
                to = dto.to,
                cc = dto.cc,
                type = dto.notificationType,
                data = dto.toPropertyMap(),
            )

        try {
            notificationService.send(notification)
        } catch (e: Exception) {
            log.error(e) { "Failed to send onboarding completed notification" }
        }
    }

    private fun createOnboardingCompletedEmailDto(
        options: BulkOnboardingOptions,
        onboardingJob: BulkOnboardingJob,
        source: String
    ): BulkOnboardingCompletedEmailDto {
        val subject: String
        val to: String
        val cc: List<String>
        val notificationType: NotificationType
        if (onboardingJob.status == BulkOnboardingJobStatus.SUCCESS) {
            subject = options.context.name + " - Contract bulk onboarding completed ✅"
            to = opsMemberOnboardingEmail
            cc = listOf(opsSalesEmail, opsCustomerSuccessEmail, bulkOnboardingSlackChannelEmail)
            notificationType = NotificationType.BulkOnboardingSuccessToOps
        } else {
            subject = options.context.name + " - Contract bulk onboarding ☠ FAILED ☠"
            to = bulkOnboardingSlackChannelEmail
            cc = emptyList()
            notificationType = NotificationType.BulkOnboardingFailedToSlack
        }

        return BulkOnboardingCompletedEmailDto(
            subject = subject,
            to = to,
            cc = cc,
            notificationType = notificationType,
            onboardingContext = options.context,
            companyId = options.companyId,
            entityId = options.entityId,
            jobId = onboardingJob.id,
            totalEmployeeCount = onboardingJob.totalEmployeeCount,
            onboardedEmployeeCount = onboardingJob.onboardedEmployeeCount,
            memberIds =
                onboardingJob.creationResult?.memberContext?.requestIdToMemberId?.values?.toList()
                    ?: emptyList(),
            contractIds =
                onboardingJob.creationResult
                    ?.contractContext
                    ?.requestIdToContractId
                    ?.values
                    ?.toList()
                    ?: emptyList(),
            source = source)
    }
}
