package com.multiplier.contract.onboarding.graphql.extensions

import com.multiplier.contract.onboarding.graphql.Constants
import com.multiplier.contract.onboarding.types.ActivationCutoffData
import com.multiplier.contract.onboarding.types.Urgency
import java.time.LocalDate

fun LocalDate?.toActivationCutoffData(checkingDate: LocalDate): ActivationCutoffData {
    return if (this == null)
        ActivationCutoffData.newBuilder().date(null).urgency(Urgency.OTHER).build()
    else
        ActivationCutoffData.newBuilder().date(this).urgency(getUrgency(checkingDate, this)).build()
}

fun getUrgency(checkingDate: LocalDate, cutoffDate: LocalDate): Urgency {
    if (cutoffDate < checkingDate) return Urgency.DELAYED

    if (cutoffDate > checkingDate.plusDays(Constants.ATTENTION_PERIOD_IN_DAYS)) return Urgency.OTHER

    return Urgency.URGENT
}
