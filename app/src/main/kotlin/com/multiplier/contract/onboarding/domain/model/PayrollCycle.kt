package com.multiplier.contract.onboarding.domain.model

import com.multiplier.contract.schema.compensation.CompensationOuterClass
import java.time.LocalDate
import java.time.YearMonth

data class PayrollCycle(
    val payFrequency: PayFrequency,
    val payDay: PayDay,
    val payrollMonth: YearMonth,
    val payDate: LocalDate,
    val cutoffDate: LocalDate,
    val contractStartFrom: LocalDate,
    val contractStartTo: LocalDate,
) {
    enum class PayFrequency {
        SEMIMONTHLY,
        MONTHLY,
        BIWEEKLY,
        WEEKLY;

        companion object {
            fun from(grpcPayFrequency: CompensationOuterClass.PayFrequency): PayFrequency? {
                return when (grpcPayFrequency) {
                    CompensationOuterClass.PayFrequency.PAY_FREQUENCY_MONTHLY -> MONTHLY
                    CompensationOuterClass.PayFrequency.PAY_FREQUENCY_SEMIMONTHLY -> SEMIMONTHLY
                    else -> null
                }
            }
        }
    }

    enum class PayDay {
        Monthly,
        SemiMonthly10,
        SemiMonthly25,
        SemiMonthly14,
        SemiMonthly28,
        SemiMonthly15,
        SemiMonthly30;

        companion object {
            fun from(payDay: Int, payFrequency: PayFrequency): PayDay? {
                if (payFrequency == PayFrequency.MONTHLY) return Monthly

                if (payFrequency == PayFrequency.SEMIMONTHLY)
                    when (payDay) {
                        10 -> return SemiMonthly10
                        25 -> return SemiMonthly25
                        14 -> return SemiMonthly14
                        28 -> return SemiMonthly28
                        15 -> return SemiMonthly15
                        30 -> return SemiMonthly30
                    }

                return null
            }
        }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as PayrollCycle

        if (payDay != other.payDay) return false
        if (payrollMonth != other.payrollMonth) return false

        return true
    }

    override fun hashCode(): Int {
        var result = payDay.hashCode()
        result = 31 * result + payrollMonth.hashCode()
        return result
    }
}
