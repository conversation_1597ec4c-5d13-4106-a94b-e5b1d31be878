package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.mapper.*
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import org.springframework.stereotype.Service

@Service
class BulkValidationForAORService(
    private val bulkMemberDataService: BulkMemberDataService,
    private val bulkContractDataService: BulkContractDataService,
    private val bulkCompensationDataService: BulkCompensationDataService,
    private val bulkComplianceDataService: BulkComplianceDataService,
    private val validationHelper: BulkValidationHelper,
) : BulkValidationServiceInterface {
    override fun validateEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): EmployeeValidationResults {
        if (employeeData.isEmpty()) {
            return EmployeeValidationResults.EMPTY
        }

        val alreadyExistingEmployeeIds =
            validationHelper.validateExistingEmployeeIds(employeeData, options.companyId)
        val duplicateEmployeeIds = validationHelper.validateDuplicateEmployeeIds(employeeData)
        val validatedMembers = bulkMemberDataService.validate(employeeData, options)
        val validatedContracts = bulkContractDataService.validate(employeeData, options)
        val validatedCompensations = bulkCompensationDataService.validate(employeeData, options)
        val validatedCompliances = bulkComplianceDataService.validate(employeeData, options)

        return EmployeeValidationResults(
            members = validatedMembers,
            contracts = duplicateEmployeeIds + alreadyExistingEmployeeIds + validatedContracts,
            compensations = validatedCompensations,
            legalData = emptyList(),
            bankData = emptyList(),
            compliances = validatedCompliances,
            orgManagementData = emptyList(),
            emergencyData = emptyList(),
            educationData = emptyList(),
            previousEmployerData = emptyList(),
            addressData = emptyList(),
        )
    }

    override fun validateCompanyOrThrow(options: BulkOnboardingOptions) {
        // No validation required for aor
    }

    override fun validateCompanyLegalEntityOrThrow(options: BulkOnboardingOptions) {
        // No validation required for aor
    }
}
