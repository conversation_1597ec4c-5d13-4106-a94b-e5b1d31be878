package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.domain.model.OnboardingTask
import com.multiplier.contract.onboarding.domain.model.convertToMapWithNameAsText
import com.multiplier.contract.onboarding.service.convertToMap
import com.multiplier.contract.onboarding.service.toFormattedDateOrEmpty
import com.multiplier.contract.onboarding.types.NotificationType
import java.time.LocalDate
import java.time.YearMonth

data class MemberOnboardingNotificationEmailDto(
    val notificationType: NotificationType,
    val subject: String,
    val memberEmail: String,
    val saleUserEmail: String,
    val csmUserEmail: String,
    val onboardingOpsUserEmail: String,
    val memberInvitedToSignContract: Boolean,
    val companyLogoLink: String?,
    val companyName: String,
    val companyCountry: String?,
    val employeeFirstName: String,
    val payrollMonth: YearMonth,
    val cutoffDate: LocalDate,
    val daysToCutoff: Int,
    val pendingStepCount: Int,
    val onboardingTasks: List<OnboardingTask>,
    val contractOnboardingLink: String,
) {
    fun toPropertyMap(): Map<String, Any> =
        this.convertToMap(
            overrides =
                mapOf(
                    "onboardingTasks" to onboardingTasks.map { it.convertToMapWithNameAsText() },
                    "payrollMonth" to payrollMonth.toFormattedDateOrEmpty(),
                    "cutoffDate" to cutoffDate.toFormattedDateOrEmpty(),
                ))
}
