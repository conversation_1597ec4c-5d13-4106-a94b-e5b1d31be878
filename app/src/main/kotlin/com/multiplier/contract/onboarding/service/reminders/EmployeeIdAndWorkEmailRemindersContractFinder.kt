package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.OnboardingServiceAdapter
import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.model.Onboarding
import com.multiplier.contract.onboarding.featureflag.FeatureFlagService
import com.multiplier.contract.onboarding.featureflag.FeatureFlags
import com.multiplier.contract.schema.contract.ContractOuterClass
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.grpc.common.toLocalDate
import java.time.LocalDate
import java.time.LocalTime
import lombok.RequiredArgsConstructor
import lombok.extern.slf4j.Slf4j
import org.springframework.stereotype.Component

@Component
@RequiredArgsConstructor
@Slf4j
class EmployeeIdAndWorkEmailRemindersContractFinder(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val onboardingServiceAdapter: OnboardingServiceAdapter,
    private val featureFlagService: FeatureFlagService
) {

    fun findContractsForEmployeeIdAndWorkEmailReminders(
        sendingDate: LocalDate,
        companyIds: List<Long>,
    ): List<Contract> {
        val contracts: List<Contract> =
            findEligibleContractsForEmployeeIdAndWorkEmailReminders(sendingDate, companyIds)
        if (contracts.isEmpty()) {
            return emptyList()
        }

        return contracts
    }

    fun findContractForEmployeeIdAndWorkEmailReminderOnContractActivation(
        id: Long,
    ): List<Contract> {
        val contract = contractServiceAdapter.getContractById(id)
        val shouldSendReminder =
            contract.hasEmployeeIdOrWorkEmailNotSet() &&
                contract.hasReminderEnabled() &&
                contract.hasStartDateArrived()

        return if (shouldSendReminder) listOf(contract) else emptyList()
    }

    private fun findEligibleContractsForEmployeeIdAndWorkEmailReminders(
        sendingDate: LocalDate,
        companyIds: List<Long>,
    ): List<Contract> {
        val contracts = getContractsForEmployeeIdAndWorkEmailReminders(sendingDate, companyIds)
        val onboardingByContractId =
            onboardingServiceAdapter.getAllOnboardingsByContractIds(
                contracts.map { it.id }, "company")
        return contracts.filter { isContractEligible(it, onboardingByContractId[it.id]) }
    }

    private fun isContractEligible(contract: Contract, onboarding: Onboarding?): Boolean {
        return (contract.status == ContractOuterClass.ContractStatus.ACTIVE) ||
            ((contract.status == ContractOuterClass.ContractStatus.ONBOARDING) &&
                (contract.type == ContractOuterClass.ContractType.HR_MEMBER) &&
                (onboarding?.status == OnboardingStatus.MEMBER_VERIFICATION_COMPLETED))
    }

    private fun getContractsForEmployeeIdAndWorkEmailReminders(
        onDate: LocalDate,
        companyIds: List<Long>,
    ): List<Contract> {
        val contracts =
            contractServiceAdapter.getContractsBy(
                filters =
                    com.multiplier.contract.onboarding.adapter.ContractFilters(
                        statuses =
                            listOf(
                                ContractOuterClass.ContractStatus.ONBOARDING,
                                ContractOuterClass.ContractStatus.ACTIVE),
                        startDateRange =
                            com.multiplier.contract.onboarding.adapter.DateRange(
                                from = onDate.atStartOfDay(), to = onDate.atTime(LocalTime.MAX)),
                        companyIds = companyIds),
            )

        return contracts.filter { it.hasEmployeeIdOrWorkEmailNotSet() && it.hasReminderEnabled() }
    }

    private fun Contract.hasReminderEnabled(): Boolean {
        return featureFlagService.isFeatureOn(
            FeatureFlags.EMPLOYEE_ID_WORK_EMAIL_REMINDERS,
            mapOf(
                FeatureFlags.Params.COMPANY to companyId,
                FeatureFlags.Params.ONBOARDING_CONTRACT_TYPE to type,
            ))
    }

    private fun Contract.hasEmployeeIdOrWorkEmailNotSet(): Boolean {
        return employeeId.isNullOrBlank() || workEmail.isNullOrBlank()
    }

    private fun Contract.hasStartDateArrived(): Boolean {
        return !startOn.toLocalDate().isAfter(LocalDate.now())
    }
}
