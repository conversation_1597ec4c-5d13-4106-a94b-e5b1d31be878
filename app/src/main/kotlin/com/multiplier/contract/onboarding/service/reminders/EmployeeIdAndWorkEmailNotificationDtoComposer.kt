package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.company.schema.grpc.CompanyOuterClass
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.DocumentServiceAdapter
import com.multiplier.contract.onboarding.adapter.GetCompanyUsersFilter
import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.domain.model.Member
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import mu.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

private val log = KotlinLogging.logger {}

@Component
class EmployeeIdAndWorkEmailNotificationDtoComposer(
    val contractServiceAdapter: ContractServiceAdapter,
    val companyServiceAdapter: CompanyServiceAdapter,
    val memberServiceAdapter: MemberServiceAdapter,
    val documentServiceAdapter: DocumentServiceAdapter,
    @Value("\${platform.base-url}") val baseUrl: String,
) {

    lateinit var contracts: Map<Long, ContractOuterClass.Contract>
    lateinit var companies: Map<Long, com.multiplier.contract.onboarding.domain.model.Company>
    lateinit var members: Map<Long, Member>
    lateinit var companyLogoLinks: Map<Long, String?>
    lateinit var companyUsersByCompanyId: Map<Long, List<CompanyOuterClass.CompanyUser>>

    fun init(contractsList: List<ContractOuterClass.Contract>) {
        fetchContracts(contractsList)
        fetchCompanies()
        fetchCompanyLogoLinks()
        fetchCompanyAdmins()
        fetchMembers()
    }

    fun composeEmployeeIdAndWorkEmailReminderEmailDto(
        contract: ContractOuterClass.Contract
    ): EmployeeIdAndWorkEmailReminderDto {

        return EmployeeIdAndWorkEmailReminderDto(
            notificationType = NotificationType.ContractEmployeeIdAndWorkEmailReminderToCompany,
            subject = "Reminder to complete filling employee ID and work email",
            companyUserEmails =
                companyUsersByCompanyId[contract.companyId]
                    ?.flatMap { it.emailsList }
                    ?.map { it.email }
                    ?: error("No admin emails found for contractId ${contract.id}"),
            companyLogoLink = companyLogoLinks[contract.companyId].orEmpty(),
            companyName = companies[contract.companyId]?.displayName.orEmpty(),
            companyCountry = companies[contract.companyId]?.countryFullName.orEmpty(),
            employeeFullNames = members[contract.memberId]?.fullName.orEmpty(),
            contractOnboardingsLink = "$baseUrl/company/team/${contract.id}/employment#contract")
    }

    private fun fetchContracts(contractsList: List<ContractOuterClass.Contract>) {
        contracts = contractsList.associateBy { it.id }
    }

    private fun fetchCompanies() {
        companies =
            companyServiceAdapter
                .getCompanies(contracts.values.map { it.companyId }.toSet())
                .associateBy { it.id }
    }

    private fun fetchCompanyLogoLinks() {
        val companyLogos =
            companies.values
                .map { it.logoId }
                .let { logoIds -> documentServiceAdapter.getCompanyLogoLinks(logoIds) }
        companyLogoLinks = companies.values.associate { it.id to companyLogos[it.logoId]?.viewUrl }
    }

    private fun fetchCompanyAdmins() {
        companyUsersByCompanyId =
            companyServiceAdapter
                .getCompanyUsersBy(
                    GetCompanyUsersFilter(
                        companyIds = companies.values.map { it.id }.toSet(),
                        statuses = setOf(CompanyOuterClass.CompanyUserStatus.ACTIVE),
                    ))
                .filter { it.isAdmin }
                .groupBy { it.companyId }
    }

    private fun fetchMembers() {
        val memberIds = contracts.values.map { it.memberId }.toSet()

        members = memberServiceAdapter.getMembers(memberIds).associateBy { it.id }
    }
}
