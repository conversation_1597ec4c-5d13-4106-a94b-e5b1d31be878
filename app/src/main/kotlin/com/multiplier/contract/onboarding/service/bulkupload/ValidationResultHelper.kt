package com.multiplier.contract.onboarding.service.bulkupload

import com.google.protobuf.Message
import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.AOR_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.BASIC_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.COMPENSATION_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.COMPLIANCE_AND_LEAVES_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.CONTRACTOR_DETAILS_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.EMPLOYMENT_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.EOR_COMPENSATION_DATA_GROUP
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData.Companion.MEMBER_DATA_GROUP
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.v2.BulkValidationResultV2
import com.multiplier.contract.onboarding.service.protoMessageToMap
import com.multiplier.contract.onboarding.service.toProtoMessage
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions

data class ValidationIdsOfContract(val ids: List<String>)

data class ValidationResultsForContract(
    val validationId: String,
    val data: Map<String, String>,
)

data class SerialisedValidationResult(
    val validationId: String,
    val module: String,
    val dataClass: String,
    val data: Map<String, String>,
)

private const val GLOBAL_PAYROLL_V3_DATA_GROUP_COUNT = 3
private const val FREELANCER_DATA_GROUP_COUNT = 1
private const val AOR_DATA_GROUP_COUNT = 1
private const val EOR_DATA_GROUP_COUNT = 3
private val GLOBAL_PAYROLL_V3_DATA_GROUPS =
    listOf(EMPLOYMENT_DATA_GROUP, MEMBER_DATA_GROUP, COMPENSATION_DATA_GROUP)
private val EOR_V2_DATA_GROUPS =
    listOf(BASIC_DETAILS_GROUP, COMPLIANCE_AND_LEAVES_DATA_GROUP, EOR_COMPENSATION_DATA_GROUP)
private val AOR_DATA_GROUPS = listOf(AOR_DETAILS_GROUP)
private val FREELANCER_DATA_GROUPS = listOf(CONTRACTOR_DETAILS_GROUP)

class ValidationResultHelper {
    companion object {

        fun mergeValidationResults(
            bulkValidationResults: BulkValidationResultV2,
            employeeData: List<EmployeeData>,
            bulkOnboardingOptions: BulkOnboardingOptions
        ): List<ValidationResult> {
            val validationIdsByContract = associateValidationInputsByContract(employeeData)
            val validationResults =
                bulkValidationResults.validationResults.entries.flatMap { (module, results) ->
                    results.map { result -> result.toBulkUploadValidationResults(module) }
                }
            return mergeValidationResultsByContract(
                validationResults, validationIdsByContract, bulkOnboardingOptions)
        }

        /**
         * There can be multiple validation input for one onboarding contract, such as contract
         * validation input, member validation input, bank details validation input, etc. During
         * creation process, we'll need to chain them together, so we need a way to know which
         * validation input belongs to which contract. In new onboarding, since the contract is not
         * created yet, we'll use employee id for linking. For existing contracts (update case),
         * we'll use contract id for linking.
         */
        fun associateValidationInputsByContract(
            employeeData: List<EmployeeData>
        ): List<ValidationIdsOfContract> {
            return employeeData
                .filter {
                    it.hasContractId() ||
                        it.hasEmployeeId() ||
                        it.hasSheetRowIdentifier() ||
                        it.hasRowIdentifier()
                }
                .groupBy {
                    it.getPositiveContractId()
                        ?.toString()
                        .orEmpty()
                        .ifEmpty { it.identification.employeeId.orEmpty() }
                        .ifEmpty { it.identification.rowIdentifierFromSheet.orEmpty() }
                        .ifEmpty { it.identification.rowIdentifier }
                }
                .map {
                    ValidationIdsOfContract(
                        ids = it.value.map { ed -> ed.identification.validationId })
                }
        }

        private fun mergeValidationResultsByContract(
            validationResults: List<ValidationResult>,
            validationIdsByContract: List<ValidationIdsOfContract>,
            bulkOnboardingOptions: BulkOnboardingOptions
        ): List<ValidationResult> {
            val successfulValidationResults =
                findAndMergeSuccessfulValidationResults(
                    validationIdsByContract, validationResults, bulkOnboardingOptions)
            val failedValidationResults =
                findAndMergeFailedValidationResults(
                    validationIdsByContract, validationResults, bulkOnboardingOptions)

            val allValidationIds = validationResults.flatMap { it.inputIds }.toSet()
            val mergedValidationIds =
                successfulValidationResults.flatMap { it.inputIds }.toSet() +
                    failedValidationResults.flatMap { it.inputIds }.toSet()
            val notToBeMergedValidationIds = allValidationIds - mergedValidationIds
            val standaloneResults =
                validationResults.filter { result ->
                    result.inputIds.any { it in notToBeMergedValidationIds }
                }

            return successfulValidationResults + failedValidationResults + standaloneResults
        }

        private fun findAndMergeSuccessfulValidationResults(
            validationIdsByContract: List<ValidationIdsOfContract>,
            validationResults: List<ValidationResult>,
            bulkOnboardingOptions: BulkOnboardingOptions
        ) =
            validationIdsByContract
                .map { validationIdsOfContract ->
                    validationResults.filter { result ->
                        result.inputIds.any { it in validationIdsOfContract.ids }
                    }
                }
                .filter { resultsForContract ->
                    resultsForContract.all { it.success } &&
                        resultsForContract.map { it.group }.distinct().size ==
                            getGroupSizeForContext(bulkOnboardingOptions.context) &&
                        // its not good enough to check only size so matching data group as well
                        getDataGroupForContext(bulkOnboardingOptions.context)
                            .containsAll(resultsForContract.map { it.group }.toSet())
                }
                .map { resultsForContract ->
                    ValidationResult(
                        inputIds = resultsForContract.flatMap { it.inputIds }.distinct(),
                        success = resultsForContract.all { it.success },
                        validatedData = mergeValidatedData(resultsForContract),
                        errors = resultsForContract.flatMap { it.errors },
                    )
                }

        private fun findAndMergeFailedValidationResults(
            validationIdsByContract: List<ValidationIdsOfContract>,
            validationResults: List<ValidationResult>,
            bulkOnboardingOptions: BulkOnboardingOptions
        ) =
            validationIdsByContract
                .map { validationIdsOfContract ->
                    validationResults.filter { result ->
                        result.inputIds.any { it in validationIdsOfContract.ids }
                    }
                }
                .filter { resultsForContract ->
                    resultsForContract.any { !it.success } ||
                        resultsForContract.map { it.group }.distinct().size <
                            getGroupSizeForContext(bulkOnboardingOptions.context) ||
                        // its not good enough to check only size so matching data group as well
                        (getDataGroupForContext(bulkOnboardingOptions.context) -
                                resultsForContract.map { it.group }.toSet())
                            .isNotEmpty()
                }
                .flatMap { resultsForContract ->
                    val inputIds = resultsForContract.flatMap { it.inputIds }.distinct()
                    val missingGroups =
                        getDataGroupForContext(bulkOnboardingOptions.context) -
                            resultsForContract.map { it.group }.toSet()
                    inputIds.map { inputId ->
                        val group = resultsForContract.first { it.inputIds.contains(inputId) }.group
                        val errors =
                            resultsForContract
                                .filter { it.inputIds.contains(inputId) }
                                .flatMap { it.errors }
                                .toMutableList()

                        adjustErrorsForGroup(
                            group, errors, resultsForContract, bulkOnboardingOptions)

                        if (missingGroups.isNotEmpty()) {
                            errors.add(
                                ValidationError(
                                    key = "",
                                    value = "",
                                    errors =
                                        missingGroups.map {
                                            "${groupDisplayName(it)} for same employee is missing"
                                        }))
                        }
                        if (errors.isEmpty()) {
                            errors.add(
                                ValidationError(
                                    key = "",
                                    value = "",
                                    errors =
                                        listOf(
                                            "Please resolve validation errors of the same employee in other modules")))
                        }
                        ValidationResult(
                            inputIds = listOf(inputId),
                            success = false,
                            errors = errors,
                            group = group,
                        )
                    }
                }

        private fun adjustErrorsForGroup(
            group: String,
            errors: MutableList<ValidationError>,
            resultsForContract: List<ValidationResult>,
            bulkOnboardingOptions: BulkOnboardingOptions
        ) {

            // skip this for freelancer and AOR
            // this is required for GP as there are separate sheets for member details and others
            // but Freelancer and AOR has a single sheet
            // for EOR this is not required since member details and contract details are in same
            // sheet unlike GP
            if (bulkOnboardingOptions.context == BulkOnboardingContext.AOR ||
                bulkOnboardingOptions.context == BulkOnboardingContext.FREELANCER ||
                bulkOnboardingOptions.context == BulkOnboardingContext.EOR) {
                return
            }
            val memberDetailAsLegalDataErrors =
                resultsForContract.flatMap { it.errors }.filter { it.isLegalDataError() }

            // due to the legacy of member details been classified as legal data (i.e:
            // phone number, address, etc)
            // we need to remove related member details errors from employment data
            // group and add them to member data group

            if (group == EMPLOYMENT_DATA_GROUP) {
                errors.removeAll(memberDetailAsLegalDataErrors)
            } else if (group == MEMBER_DATA_GROUP) {
                errors.addAll(memberDetailAsLegalDataErrors)
            }
        }

        private fun mergeValidatedData(
            validationResults: List<ValidationResult>
        ): Map<String, String> {
            val moduleCounts = validationResults.groupingBy { it.module }.eachCount()
            val moduleCounters = mutableMapOf<String, Int>()

            return validationResults
                .flatMap { validationResult ->
                    val module = validationResult.module
                    val prefix =
                        if (moduleCounts.getValue(module) > 1) {
                            // More than one map in this module, use module name + index
                            val index = moduleCounters.getOrDefault(module, 0)
                            moduleCounters[module] = index + 1
                            "$module->[$index]"
                        } else {
                            // Only one map in this module, use module name only
                            module
                        }

                    validationResult.validatedData.map { (key, value) -> "$prefix->$key" to value }
                }
                .toMap()
        }

        fun unmergeToBulkValidationResults(inputs: List<UpsertInput>) =
            BulkValidationResultV2(
                inputs
                    .map { it.toValidationResultsForContract() }
                    .flatMap { it.splitByModule() }
                    .groupBy { it.module }
                    .mapValues { it.value.map { result -> result.deserialise() } })

        private fun groupDisplayName(group: String) =
            when (group) {
                EMPLOYMENT_DATA_GROUP -> "Employment data"
                MEMBER_DATA_GROUP -> "Member data"
                COMPENSATION_DATA_GROUP -> "Compensation data"
                else -> group
            }

        private fun getGroupSizeForContext(bulkOnboardingContext: BulkOnboardingContext) =
            when (bulkOnboardingContext) {
                BulkOnboardingContext.GLOBAL_PAYROLL -> GLOBAL_PAYROLL_V3_DATA_GROUP_COUNT
                BulkOnboardingContext.FREELANCER -> FREELANCER_DATA_GROUP_COUNT
                BulkOnboardingContext.AOR -> AOR_DATA_GROUP_COUNT
                BulkOnboardingContext.EOR -> EOR_DATA_GROUP_COUNT
                else ->
                    throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                        "Unsupported context: $bulkOnboardingContext",
                        context = mapOf("context" to bulkOnboardingContext))
            }

        private fun getDataGroupForContext(bulkOnboardingContext: BulkOnboardingContext) =
            when (bulkOnboardingContext) {
                BulkOnboardingContext.GLOBAL_PAYROLL -> GLOBAL_PAYROLL_V3_DATA_GROUPS
                BulkOnboardingContext.FREELANCER -> FREELANCER_DATA_GROUPS
                BulkOnboardingContext.AOR -> AOR_DATA_GROUPS
                BulkOnboardingContext.EOR -> EOR_V2_DATA_GROUPS
                else ->
                    throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                        "Unsupported context: $bulkOnboardingContext",
                        context = mapOf("context" to bulkOnboardingContext))
            }

        /**
         * Validation ID in ValidationResult class is an internal id derived from the input
         * validation request id, it is used for tracking the validation request id in the
         * validation pipeline. This function is used to restore the validation request id from the
         * internal validation id. Restored validation request id is used in the validation response
         * so that consumer can track the validation request id.
         */
        fun restoreValidationRequestIds(
            validationResults: List<ValidationResult>,
            employeeData: List<EmployeeData>
        ): List<ValidationResult> {
            val validationIdToRequestId =
                employeeData.associate {
                    it.identification.validationId to it.identification.rowIdentifier
                }
            return validationResults.map {
                it.copy(
                    inputIds = restoreValidationRequestIds(it.inputIds, validationIdToRequestId))
            }
        }

        private fun restoreValidationRequestIds(
            validationIds: List<String>,
            validationIdToRequestId: Map<String, String?>
        ): List<String> {
            return validationIds.map { validationIdToRequestId[it] ?: it }
        }
    }
}

private fun ValidationError.isLegalDataError() =
    this.errors.any { error ->
        error.contains("phone number", ignoreCase = true) ||
            (error.contains("address", ignoreCase = true) &&
                !error.contains("bank", ignoreCase = true)) ||
            error.contains("date of birth", ignoreCase = true) ||
            error.contains("nationality", ignoreCase = true) ||
            error.contains("marital status", ignoreCase = true)
    }

private fun GrpcValidationResult<out Any>.toBulkUploadValidationResults(module: String) =
    ValidationResult(
        inputIds = this.validationId.split(',').map { it.trim() },
        success = this.success,
        validatedData = this.input?.let { convertGrpcValidatedInputToMap(it) }.orEmpty(),
        errors =
            this.errors
                ?.map { ValidationError(key = "", value = "", errors = listOf(it)) }
                .orEmpty(),
        module = module,
        group = this.groupName.orEmpty())

private fun convertGrpcValidatedInputToMap(input: Any): Map<String, String> {
    if (input is Map<*, *>) {
        return input.entries.associate { it.key.toString() to it.value.toString() } +
            mapOf("dataClass" to "Map")
    }

    return protoMessageToMap(input) + mapOf("dataClass" to getGrpcClassName(input))
}

private fun getMessageBuilder(dataClass: String): Message.Builder {
    val clazz = Class.forName(dataClass)
    return clazz.getMethod("newBuilder").invoke(null) as Message.Builder
}

fun getGrpcClassName(message: Any): String {
    val clazz = message::class.java
    return if (clazz.isMemberClass && java.lang.reflect.Modifier.isStatic(clazz.modifiers))
        clazz.canonicalName.replaceLast(".", "\$")
    else clazz.canonicalName
}

private fun String.replaceLast(oldValue: String, newValue: String): String {
    val lastIndex = this.lastIndexOf(oldValue)
    return if (lastIndex != -1) {
        this.substring(0, lastIndex) + newValue + this.substring(lastIndex + oldValue.length)
    } else {
        this
    }
}

private fun EmployeeData.hasContractId() =
    this.identification.contractId != null && this.identification.contractId > 0

private fun EmployeeData.hasEmployeeId() = !this.identification.employeeId.isNullOrBlank()

private fun EmployeeData.hasRowIdentifier() = !this.identification.rowIdentifier.isNullOrBlank()

private fun EmployeeData.hasSheetRowIdentifier() =
    !this.identification.rowIdentifierFromSheet.isNullOrBlank()

private fun EmployeeData.getPositiveContractId() =
    if (this.hasContractId()) this.identification.contractId else null

private fun ValidationResultsForContract.splitByModule(): List<SerialisedValidationResult> {
    return data.entries
        .filter { it.key.contains("->") }
        .groupBy { entry -> entry.key.substringBefore("->") }
        .mapValues { entry -> entry.value.associate { it.key.substringAfter("->") to it.value } }
        .flatMap { (module, data) ->
            data.toArrayOfMaps().map {
                SerialisedValidationResult(
                    validationId, module, it.getValue("dataClass"), it - "dataClass")
            }
        }
}

private fun Map<String, String>.toArrayOfMaps(): List<Map<String, String>> {
    return if (this.keys.all { it.startsWith("[") }) {
        this.entries
            .groupBy { entry -> entry.key.substringBefore("->") }
            .map { (_, entries) ->
                entries.associate { entry -> entry.key.substringAfter("->") to entry.value }
            }
    } else listOf(this)
}

private fun UpsertInput.toValidationResultsForContract() =
    ValidationResultsForContract(
        validationId = this.id,
        data = this.data,
    )

private fun SerialisedValidationResult.deserialise() =
    GrpcValidationResult(
        success = true,
        errors = emptyList(),
        validationId = validationId,
        input = if (dataClass == "Map") data else data.toProtoMessage(getMessageBuilder(dataClass)))
