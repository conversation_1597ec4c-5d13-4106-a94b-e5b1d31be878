package com.multiplier.contract.onboarding.grpc

import com.multiplier.contract.onboarding.domain.Clock
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.schema.*
import com.multiplier.contract.onboarding.schema.BulkOnboardResult as GrpcBulkOnboardResult
import com.multiplier.contract.onboarding.schema.BulkOnboardValidationResult as GrpcBulkOnboardValidationResult
import com.multiplier.contract.onboarding.service.ActivationCutoffService
import com.multiplier.contract.onboarding.service.bulk.BulkOnboardingJob
import com.multiplier.contract.onboarding.service.bulk.validation.ValidationResults
import com.multiplier.contract.onboarding.service.grpc.GrpcExceptionWrapper
import com.multiplier.contract.onboarding.usecase.BulkOnboardingServiceFactory
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.grpc.common.bulkupload.v1.*
import io.grpc.Status
import io.grpc.stub.StreamObserver
import java.time.LocalDate
import mu.KotlinLogging
import net.devh.boot.grpc.server.service.GrpcService

@GrpcService
class GrpcContractOnboardingService(
    private val bulkOnboardingServiceFactory: BulkOnboardingServiceFactory,
    private val grpcBulkUploadService: GrpcBulkUploadService,
    private val activationCutoffService: ActivationCutoffService,
    private val clock: Clock
) : ContractOnboardingServiceGrpc.ContractOnboardingServiceImplBase() {

    private val log = KotlinLogging.logger {}

    companion object {
        private const val GRPC = "gRPC"
    }

    override fun validateBulkOnboarding(
        request: BulkOnboardRequest,
        responseObserver: StreamObserver<BulkOnboardValidationResponse>
    ) {
        try {
            val employeeData = request.inputsList.map { it.toDomain() }
            val options = request.option.toDomain()
            val result =
                bulkOnboardingServiceFactory
                    .getBulkOnboardingService(options.context)
                    .validate(
                        EmployeeDataInput(employeeData = employeeData, source = GRPC), options)
            val response = buildValidationResponse(result, employeeData)

            responseObserver.onNext(response)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.warn(e) { "Failed to validate bulk onboarding with request: $request" }
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    private fun buildValidationResponse(
        validationResults: ValidationResults,
        employeeData: List<EmployeeData>,
    ): BulkOnboardValidationResponse {
        val requestIdToValidationErrors =
            validationResults.validationReport.employeeValidationResult.errors.groupBy {
                it.rowNumber
            }
        val grpcValidationResult =
            employeeData.map { employee ->
                val errors =
                    requestIdToValidationErrors[employee.identification.rowNumber]?.map { it.error }
                        ?: emptyList()
                GrpcBulkOnboardValidationResult.newBuilder()
                    .setRequestId(employee.identification.rowNumber)
                    .putAllValidatedProperties(employee.data)
                    .addAllErrors(errors)
                    .setSuccess(errors.isEmpty())
                    .build()
            }

        return BulkOnboardValidationResponse.newBuilder()
            .addAllValidationResults(grpcValidationResult)
            .build()
    }

    override fun bulkOnboard(
        request: BulkOnboardRequest,
        responseObserver: StreamObserver<BulkOnboardResponse>
    ) {
        try {
            val employeeData = request.inputsList.map { it.toDomain() }
            val options = request.option.toDomain()
            val result =
                bulkOnboardingServiceFactory
                    .getBulkOnboardingService(options.context)
                    .onboard(EmployeeDataInput(employeeData = employeeData, source = GRPC), options)
            val response = buildBulkOnboardResponse(employeeData, result)

            responseObserver.onNext(response)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.warn(e) { "Failed to bulk onboard with request: $request" }
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    private fun buildBulkOnboardResponse(
        employeeData: List<EmployeeData>,
        bulkOnboardingJob: BulkOnboardingJob
    ) =
        BulkOnboardResponse.newBuilder()
            .addAllResults(
                employeeData.mapNotNull {
                    val employeeId = it.identification.employeeId.orEmpty()
                    val contractId =
                        bulkOnboardingJob.creationResult?.getContractId(
                            it.identification.validationId)
                            ?: bulkOnboardingJob.creationResult?.getContractId(employeeId)
                    when (contractId) {
                        null -> null
                        else ->
                            GrpcBulkOnboardResult.newBuilder()
                                .setRequestId(it.identification.rowNumber)
                                .setContractId(contractId)
                                .build()
                    }
                })
            .build()

    override fun getBulkOnboardDataSpecs(
        request: BulkOnboardOption,
        responseObserver: StreamObserver<BulkOnboardDataSpecsResponse>
    ) {
        try {
            val options = request.toDomain()
            val results =
                bulkOnboardingServiceFactory
                    .getBulkOnboardingService(options.context)
                    .getDataSpec(options)
            val response =
                BulkOnboardDataSpecsResponse.newBuilder()
                    .addAllSpecs(results.map { it.toGrpc() })
                    .build()

            responseObserver.onNext(response)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.warn(e) { "Failed to get bulk onboard data specs: $request" }
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    override fun getFieldRequirements(
        request: FieldRequirementsRequest,
        responseObserver: StreamObserver<FieldRequirementsResponse>
    ) {
        grpcBulkUploadService.getFieldRequirements(request, responseObserver)
    }

    override fun getFieldData(
        request: BulkDataRequest,
        responseObserver: StreamObserver<BulkDataResponse>
    ) {
        grpcBulkUploadService.getFieldData(request, responseObserver)
    }

    override fun bulkValidateUpsertInput(
        request: ValidateUpsertInputBulkRequest,
        responseObserver: StreamObserver<ValidateUpsertInputBulkResponse>
    ) {
        grpcBulkUploadService.bulkValidateUpsertInput(request, responseObserver)
    }

    override fun bulkUpsert(
        request: UpsertBulkRequest,
        responseObserver: StreamObserver<UpsertBulkResponse>
    ) {
        grpcBulkUploadService.bulkUpsert(request, responseObserver)
    }

    override fun getActivationCutoffDataForContracts(
        request: ActivationCutoffRequest,
        responseObserver: StreamObserver<ActivationCutoffResponse>
    ) {
        try {
            val contractIds = request.contractIdsList
            val checkingDate = clock.today()
            val contractIdToCutoffDate =
                activationCutoffService.getActivationCutoffDateByContractId(
                    contractIds, checkingDate)
            val response = buildActivationCutoffResponse(contractIdToCutoffDate)

            responseObserver.onNext(response)
            responseObserver.onCompleted()
        } catch (e: Exception) {
            log.warn(e) { "Failed to get activation cutoff data for contracts: $request" }
            responseObserver.onError(GrpcExceptionWrapper(Status.INTERNAL, e))
        }
    }

    private fun buildActivationCutoffResponse(
        contractIdToCutoffDate: Map<Long, LocalDate?>
    ): ActivationCutoffResponse {
        return ActivationCutoffResponse.newBuilder()
            .apply {
                contractIdToCutoffDate
                    .map { (contractId, cutoffDate) ->
                        ContractCutoff.newBuilder()
                            .setContractId(contractId)
                            .setActivationCutoff(cutoffDate!!.toString())
                            .build()
                    }
                    .let { addAllContractsCutoffResponse(it) }
            }
            .build()
    }
}
