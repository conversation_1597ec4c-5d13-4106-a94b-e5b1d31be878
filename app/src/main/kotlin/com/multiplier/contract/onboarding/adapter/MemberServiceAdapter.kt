package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.domain.model.*
import com.multiplier.contract.onboarding.domain.model.Address
import com.multiplier.contract.onboarding.domain.model.Member
import com.multiplier.contract.onboarding.domain.model.Persona
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberLegalDataService
import com.multiplier.contract.onboarding.service.toLocalDate
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.contract.onboarding.types.Gender
import com.multiplier.member.schema.*
import com.multiplier.member.schema.Address as GrpcAddress
import com.multiplier.member.schema.Member.MaritalStatus
import com.multiplier.member.schema.MemberServiceGrpc.MemberServiceBlockingStub
import net.devh.boot.grpc.client.inject.GrpcClient
import org.apache.commons.collections4.CollectionUtils
import org.springframework.stereotype.Service

data class ValidInput<T>(val validationId: String, val input: T)

interface MemberServiceAdapter {
    fun getMember(memberId: Long): Member
    fun getMembers(memberIds: Set<Long>): List<Member>
    fun getMemberByUserId(userId: Long): Member?
    fun getLegalDocumentsKeyByMemberIds(memberIds: Set<Long>): Map<Long, Set<String>>
    fun getMemberEmergencyContacts(memberIds: Set<Long>): List<EmergencyContact>
    fun getMemberEducationDetails(memberIds: Set<Long>): List<EducationDetail>
    fun getMemberLastEmployerDetails(memberIds: Set<Long>): List<EmployerDetail>
    fun getMembersWithAddressesAndBankAccountsAndLegalData(memberIds: Set<Long>): List<Member>
    fun getMemberAddressDetails(memberIds: Set<Long>): List<AddressDetail>
}

@Service
class MemberServiceAdapterImpl : MemberServiceAdapter {

    @GrpcClient("member-service") private lateinit var memberService: MemberServiceBlockingStub

    override fun getMember(memberId: Long): Member {
        val request = MemberIdRequest.newBuilder().setMemberId(memberId).build()

        return memberService.getMember(request).toDomain()
    }

    override fun getMembers(memberIds: Set<Long>): List<Member> {
        val request = MemberIdsRequest.newBuilder().addAllMemberId(memberIds).build()

        return memberService.getMembers(request).membersList.map { it.toDomain() }
    }

    override fun getMembersWithAddressesAndBankAccountsAndLegalData(
        memberIds: Set<Long>
    ): List<Member> {
        val request = MemberIdsRequest.newBuilder().addAllMemberId(memberIds).build()

        return memberService
            .getMembersWithAddressesAndBankAccountsAndLegalData(request)
            .membersList
            .map { it.toDomain() }
    }

    override fun getMemberByUserId(userId: Long): Member? {
        val request = GetMemberByUserIdRequest.newBuilder().setUserId(userId).build()

        return memberService.getMemberByUserId(request).toDomain()
    }

    override fun getLegalDocumentsKeyByMemberIds(memberIds: Set<Long>): Map<Long, Set<String>> {
        val req =
            GetSubmittedLegalDocumentKeysByMemberIdRequest.newBuilder()
                .addAllMemberIds(memberIds)
                .build()
        return memberService
            .getSubmittedLegalDocumentKeysByMemberId(req)
            .memberIdAndDocumentKeysList
            .associate { it.memberId to it.documentKeysList.toSet() }
    }

    override fun getMemberEmergencyContacts(memberIds: Set<Long>): List<EmergencyContact> {
        val request = MemberIdsRequest.newBuilder().addAllMemberId(memberIds).build()
        return memberService.getMemberEmergencyContacts(request).emergencyContactsList.mapNotNull {
            it.toDomain()
        }
    }

    override fun getMemberEducationDetails(memberIds: Set<Long>): List<EducationDetail> {
        val request = MemberIdsRequest.newBuilder().addAllMemberId(memberIds).build()
        return memberService.getMemberEducationDetails(request).educationDetailsList.mapNotNull {
            it.toDomain()
        }
    }

    override fun getMemberLastEmployerDetails(memberIds: Set<Long>): List<EmployerDetail> {
        val request = MemberIdsRequest.newBuilder().addAllMemberId(memberIds).build()
        return memberService
            .getMemberPreviousEmployerDetails(request)
            .previousEmployerDetailsList
            .mapNotNull { it.toDomain() }
    }

    override fun getMemberAddressDetails(memberIds: Set<Long>): List<AddressDetail> {
        if (CollectionUtils.isEmpty(memberIds)) return emptyList()
        val request = MemberIdsRequest.newBuilder().addAllMemberId(memberIds).build()
        return memberService.getMemberAddressDetails(request).addressDetailsList.mapNotNull {
            it.toDomain()
        }
    }
}

private fun com.multiplier.member.schema.Member.toDomain() =
    Member(
        id = this.id,
        email = this.primaryOrDefaultEmail ?: "",
        firstName = this.firstName,
        lastName = this.lastName,
        fullName = this.fullName(),
        persona = this.persona.toDomain(),
        userId = this.userId,
        gender = this.gender.toDomain(),
        dateOfBirth = if (this.hasDateOfBirth()) this.dateOfBirth.toLocalDate() else null,
        nationality = this.nationalitiesList.firstNotNullOfOrNull { it.country.name },
        maritalStatus = this.martialStatus.toDomain(),
        phoneNumber = this.phoneNosList.firstNotNullOfOrNull { it.phoneNo },
        religion =
            this.legalDataList
                .find { it.key == BulkMemberLegalDataService.ReligionSpec.key }
                ?.value,
        nationalId =
            this.legalDataList
                .find { it.key == BulkMemberLegalDataService.NationalIdSpec.key }
                ?.value,
        passportNumber =
            this.legalDataList
                .find { it.key == BulkMemberLegalDataService.PassportNumberSpec.key }
                ?.value,
        address = this.addressesList.firstOrNull()?.toDomain(),
        legalDataProps = this.legalDataList.associate { it.key to it.value },
        staticBankDetails = this.bankAccountsList.firstOrNull()?.toDomain(),
        dynamicBankDetails =
            this.bankAccountsList
                .firstOrNull()
                ?.accountDetailsList
                ?.associate { it.key to it.value }
                .orEmpty(),
    )

private fun com.multiplier.member.schema.Persona.toDomain() = Persona(name = this.name)

private fun com.multiplier.member.schema.Gender.toDomain() =
    when (this) {
        com.multiplier.member.schema.Gender.NULL_GENDER,
        com.multiplier.member.schema.Gender.UNRECOGNIZED -> null
        else -> Gender.valueOf(this.name)
    }

private fun MaritalStatus.toDomain(): String? =
    when (this) {
        MaritalStatus.NULL,
        MaritalStatus.UNRECOGNIZED -> null
        else -> this.name
    }

private fun MemberEmergencyContact.toDomain() =
    EmergencyContact(
        memberId = this.memberId,
        name = this.name,
        relationship = this.relationship,
        phoneNumber = this.phoneNumber.phoneNo,
    )

private fun MemberEducationDetails.toDomain(): EducationDetail? =
    this.educationDetailsList
        .maxByOrNull { it.yearOfPassing }
        ?.let {
            EducationDetail(
                memberId = this.memberId,
                lastSchoolName = it.institutionName,
                lastSchoolDegree = it.degree,
                lastSchoolYearOfPassing = it.yearOfPassing,
                lastSchoolGpa = it.gpa,
                lastSchoolGrade = it.grade,
            )
        }

private fun MemberPreviousEmployerDetails.toDomain(): EmployerDetail? =
    this.previousEmployerDetailsList
        .maxByOrNull { it.endDate.toLocalDate() }
        ?.let {
            EmployerDetail(
                memberId = this.memberId,
                lastEmployerName = it.employerName,
                lastEmployerPosition = it.designation,
                lastEmployerStartDate = it.startDate.toLocalDate(),
                lastEmployerEndDate = it.endDate.toLocalDate(),
            )
        }

private fun MemberAddressDetails.toDomain() =
    AddressDetail(
        memberId = this.memberId,
        currentAddress = this.currentAddress.toDomain(),
        permanentAddress =
            when (val address = this.permanentAddress) {
                GrpcAddress.getDefaultInstance() -> null
                else -> address.toDomain()
            },
    )

private fun GrpcAddress.toDomain() =
    Address(
        street = this.street,
        line1 = this.line1,
        line2 = this.line2,
        city = this.city,
        state = this.state,
        province = this.province,
        country = CountryCode.valueOf(this.country.name),
        zipCode = this.zipcode,
        postalCode = this.postalCode,
    )

private fun BankAccount.toDomain() =
    StaticBankDetails(
        bankName = this.bankName,
        bankBranch = this.branchName,
        bankAccountHolderName = this.accountName,
        bankAccountNumber = this.accountNumber,
        swiftCode = this.swiftCode,
        localBankCode = this.localBankCode,
    )

private fun com.multiplier.member.schema.Member.fullName() =
    when {
        !firstName.isNullOrEmpty() && !lastName.isNullOrEmpty() ->
            "${firstName.trim()} ${lastName.trim()}"
        !firstName.isNullOrEmpty() -> firstName.trim()
        !lastName.isNullOrEmpty() -> lastName.trim()
        fullLegalName != null -> fullLegalName.trim()
        else -> ""
    }

private val com.multiplier.member.schema.Member.primaryOrDefaultEmail: String?
    get() = if (primaryEmail != null) primaryEmail else defaultEmail

private val com.multiplier.member.schema.Member.primaryEmail: String?
    get() =
        emailsList
            .filterNotNull()
            .firstOrNull { it.type.equals("primary", ignoreCase = true) }
            ?.email

private val com.multiplier.member.schema.Member.defaultEmail: String?
    get() =
        emailsList
            .filterNotNull()
            .firstOrNull { it.type.equals("default", ignoreCase = true) }
            ?.email
