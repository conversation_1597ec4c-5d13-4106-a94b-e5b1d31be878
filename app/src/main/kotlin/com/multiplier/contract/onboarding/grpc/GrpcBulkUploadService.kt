package com.multiplier.contract.onboarding.grpc

import com.multiplier.contract.onboarding.service.bulkupload.BulkUploadService
import com.multiplier.grpc.common.bulkupload.v1.*
import io.grpc.stub.StreamObserver
import net.devh.boot.grpc.server.service.GrpcService

@GrpcService
class GrpcBulkUploadService(
    private val bulkUploadService: BulkUploadService,
) : BulkUploadServiceGrpc.BulkUploadServiceImplBase() {

    override fun getFieldRequirements(
        request: FieldRequirementsRequest,
        responseObserver: StreamObserver<FieldRequirementsResponse>
    ) {
        val fieldRequirements = bulkUploadService.getFieldRequirements(request.toDomain())
        responseObserver.onNext(
            fieldRequirementsResponse {
                this.fieldRequirements.addAll(fieldRequirements.map { it.toGrpc() })
            })
        responseObserver.onCompleted()
    }

    override fun getFieldData(
        request: BulkDataRequest,
        responseObserver: StreamObserver<BulkDataResponse>
    ) {
        val fieldData = bulkUploadService.getFieldData(request.toDomain())
        responseObserver.onNext(
            bulkDataResponse { this.rows.addAll(fieldData.map { it.toGrpcRowData() }) })
        responseObserver.onCompleted()
    }

    override fun bulkValidateUpsertInput(
        request: ValidateUpsertInputBulkRequest,
        responseObserver: StreamObserver<ValidateUpsertInputBulkResponse>
    ) {
        val validationResults = bulkUploadService.validate(request.toDomain())
        responseObserver.onNext(
            ValidateUpsertInputBulkResponse.newBuilder()
                .addAllResults(validationResults.map { it.toGrpc() })
                .build())
        responseObserver.onCompleted()
    }

    override fun bulkUpsert(
        request: UpsertBulkRequest,
        responseObserver: StreamObserver<UpsertBulkResponse>
    ) {
        val upsertResults = bulkUploadService.upsert(request.toDomain())
        responseObserver.onNext(
            UpsertBulkResponse.newBuilder()
                .addAllResults(upsertResults.map { it.toGrpc() })
                .build())
        responseObserver.onCompleted()
    }
}
