package com.multiplier.contract.onboarding.domain.model

import com.multiplier.contract.onboarding.domain.OnboardingStatus
import com.multiplier.contract.onboarding.domain.OnboardingStep
import com.multiplier.contract.onboarding.domain.contants.ContractRevokedExperience

data class Onboarding(
    val id: Long? = null,
    val contractId: Long,
    val experience: String,
    val status: OnboardingStatus,
    val revokedBy: ContractRevokedExperience? = ContractRevokedExperience.NONE,
    val currentStep: OnboardingStep? = null,
    val isBulkOnboarded: Boolean? = false,
)
