package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.adapter.*
import com.multiplier.contract.onboarding.domain.model.PayrollCycle
import com.multiplier.contract.onboarding.service.extensions.isFreelancer
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import com.multiplier.contract.schema.contract.ContractOuterClass.Contract
import com.multiplier.member.schema.CountryCode
import com.multiplier.transaction.spring.TraceSpan
import java.time.LocalDate
import mu.KotlinLogging
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger {}

@Service
class PayrollCycleConfigService(
    private val contractServiceAdapter: ContractServiceAdapter,
    private val onboardingServiceAdapter: OnboardingServiceAdapter,
    private val compensationServiceAdapter: CompensationServiceAdapter,
    private val onboardingAudServiceAdapter: OnboardingAudServiceAdapter,
    private val companyServiceAdapter: CompanyServiceAdapter
) {
    companion object {
        private val MONTHLY_PAY_DATE_DEFAULT: Set<PayrollCycle.PayDay> =
            setOf(PayrollCycle.PayDay.Monthly)
        private val MONTHLY_PAY_DATE_CONFIG: Map<CountryCode, Set<PayrollCycle.PayDay>> = mapOf()

        private val SEMI_MONTHLY_PAY_DATE_DEFAULT: Set<PayrollCycle.PayDay> =
            setOf(PayrollCycle.PayDay.SemiMonthly15, PayrollCycle.PayDay.SemiMonthly30)
        private val SEMI_MONTHLY_PAY_DATE_CONFIG: Map<CountryCode, Set<PayrollCycle.PayDay>> =
            mapOf(
                Pair(
                    CountryCode.PHL,
                    setOf(PayrollCycle.PayDay.SemiMonthly10, PayrollCycle.PayDay.SemiMonthly25)),
                Pair(
                    CountryCode.USA,
                    setOf(PayrollCycle.PayDay.SemiMonthly14, PayrollCycle.PayDay.SemiMonthly28)))

        private const val COMPANY_EXP = "company"
        private const val MEMBER_EXP = "member"
    }

    fun getPayrollCycleConfigByOnboardingId(
        onboardingIds: Set<Long>,
        checkingDate: LocalDate
    ): Map<Long, PayrollCycle?> {
        val onboardingByContractId =
            onboardingServiceAdapter.getAllByIds(onboardingIds).associateBy { it.contractId }
        if (onboardingByContractId.isEmpty()) {
            log.info("no onboarding found for ids: $onboardingIds")
            return mapOf()
        }

        val contracts =
            contractServiceAdapter
                .getNonDeletedNonEndedContracts(onboardingByContractId.keys)
                .filter { !it.isFreelancer() }

        return getPayrollCyclesByContractId(checkingDate, contracts)
            .mapKeys { onboardingByContractId[it.key]?.id }
            .filterKeys { it != null }
            .mapKeys { it.key as Long }
    }

    @TraceSpan
    fun getPayrollCyclesByContractId(
        checkingDate: LocalDate,
        contracts: List<Contract>,
    ): Map<Long, PayrollCycle?> {
        if (contracts.isEmpty()) return mapOf()

        val contractIds = contracts.map { it.id }.toSet()

        val compensationByContractId =
            compensationServiceAdapter
                .getCurrentCompensationByContractIds(contractIds)
                .filterValues(Compensation::hasPostProbationBasePay)

        val contractById =
            contracts.filter { compensationByContractId.keys.contains(it.id) }.associateBy { it.id }

        if (contractById.isEmpty()) {
            log.info("no contract with compensation found for ids: ${contractIds}")
            return mapOf()
        }

        val payrollCycleByContractId =
            compensationByContractId.entries.associate {
                val contract = contractById[it.key]!!
                val payrollCycleDTO =
                    getPayrollCycleConfig(contract, contract.startOn.toLocalDate(), it.value)

                it.key to payrollCycleDTO
            }

        val payrollCycleOfPreviousMonthByContractId =
            getPayrollCycleOfPreviousMonth(checkingDate, payrollCycleByContractId, contractById)

        return (payrollCycleByContractId + payrollCycleOfPreviousMonthByContractId)
    }

    private fun getPayrollCycleOfPreviousMonth(
        checkingDate: LocalDate,
        resultMap: Map<Long, PayrollCycle?>,
        contractById: Map<Long, Contract>
    ): Map<Long, PayrollCycle> {
        val memoized = mutableMapOf<PayrollCycle, Boolean>()

        val contractsWithValidCutoffDateOfPrevMonth =
            resultMap
                .filter { entry ->
                    entry.value != null &&
                        entry.value!!.cutoffDate < checkingDate &&
                        memoized.computeIfAbsent(entry.value!!) { key ->
                            PayrollCycleHepper.getNextMonthCycle(key).cutoffDate >= checkingDate
                        }
                }
                .keys

        val shouldFollowUpContractIds =
            fetchShouldFollowUpContractIds(
                contractById.filter { contractsWithValidCutoffDateOfPrevMonth.contains(it.key) })

        val shiftedMap =
            shouldFollowUpContractIds.associateWith {
                shiftCutoffDateIfNeeded(resultMap[it]!!, checkingDate)
            }
        return shiftedMap
    }

    private fun fetchShouldFollowUpContractIds(contractById: Map<Long, Contract>): Set<Long> {
        val depositPaidContractIds = filterDepositPaid(contractById)

        val msaSignedAndEmployeeSignedContractIds =
            filterMsaSignedAndEmployeeSigned(
                contractById.filterKeys { it !in depositPaidContractIds })

        return depositPaidContractIds + msaSignedAndEmployeeSignedContractIds
    }

    private fun filterDepositPaid(contractById: Map<Long, Contract>) =
        contractServiceAdapter.getDepositPaidContractIds(contractById.keys)

    private fun filterMsaSignedAndEmployeeSigned(contractById: Map<Long, Contract>): Set<Long> {
        val companyIdsSignedMSA =
            companyServiceAdapter.getMSASignedCompanyIds(
                contractById.values.map { it.companyId }.toSet())

        val contractsIdsSignedMSA =
            contractById.filterValues { it.companyId in companyIdsSignedMSA }.keys

        val msaSignedAndEmployeeSignedContractIds =
            onboardingAudServiceAdapter.getAllContractIdsOfOnboardingAuditsWithExperienceAndStatus(
                contractsIdsSignedMSA,
                COMPANY_EXP,
                ContractOnboardingStatus.SIGNATURE_EMPLOYEE_SIGNED)

        return msaSignedAndEmployeeSignedContractIds
    }

    private fun getPayrollCycleConfig(
        contract: Contract,
        startOn: LocalDate,
        compensation: Compensation
    ): PayrollCycle? {
        val payFrequency =
            PayrollCycle.PayFrequency.from(compensation.postProbationBasePay.payFrequency)
                ?: return null
        val compensationPayDays =
            getPayDays(compensation, payFrequency, CountryCode.valueOf(contract.country))
                ?: return null

        return PayrollCycleHepper.getPayrollCycleConfig(startOn, compensationPayDays)
    }

    private fun getPayDays(
        compensation: Compensation,
        payFrequency: PayrollCycle.PayFrequency,
        countryCode: CountryCode
    ): Set<PayrollCycle.PayDay>? {
        val paymentFrequencyDates =
            compensation.postProbationBasePay.paymentFrequencyDatesList
                .mapNotNull { PayrollCycle.PayDay.from(it.dateOfMonth, payFrequency) }
                .toSet()
        if (paymentFrequencyDates.isNotEmpty()) return paymentFrequencyDates

        if (payFrequency === PayrollCycle.PayFrequency.SEMIMONTHLY)
            return SEMI_MONTHLY_PAY_DATE_CONFIG.getOrDefault(
                countryCode, SEMI_MONTHLY_PAY_DATE_DEFAULT)

        if (payFrequency === PayrollCycle.PayFrequency.MONTHLY)
            return MONTHLY_PAY_DATE_CONFIG.getOrDefault(countryCode, MONTHLY_PAY_DATE_DEFAULT)

        return null
    }

    private fun shiftCutoffDateIfNeeded(
        payrollCycleDTO: PayrollCycle,
        checkingDate: LocalDate
    ): PayrollCycle {
        if (payrollCycleDTO.cutoffDate >= checkingDate) return payrollCycleDTO

        if (payrollCycleDTO.payFrequency == PayrollCycle.PayFrequency.MONTHLY) {
            val nextCycle = PayrollCycleHepper.getNextCycle(payrollCycleDTO)
            if (nextCycle.cutoffDate >= checkingDate) return nextCycle

            return payrollCycleDTO
        }

        if (payrollCycleDTO.payFrequency == PayrollCycle.PayFrequency.SEMIMONTHLY) {
            val nextCycle = PayrollCycleHepper.getNextCycle(payrollCycleDTO)
            if (nextCycle.cutoffDate >= checkingDate) return nextCycle

            val next2Cycle = PayrollCycleHepper.getNextCycle(nextCycle)
            if (next2Cycle.cutoffDate >= checkingDate) return next2Cycle

            return payrollCycleDTO
        }

        return payrollCycleDTO
    }
}
