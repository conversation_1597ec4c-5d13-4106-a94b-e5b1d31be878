package com.multiplier.contract.onboarding.domain

// DANGER! The order of this enum is relevant
enum class OnboardingStatus {
    NULL_STATE,
    DRAFT,
    CREATED,
    CREATED_CUSTOM,
    REVOKED,
    INVITED,
    ORDER_FORM_SENT_TO_EMPLOYER,
    ORDER_FORM_SIGNED_BY_EMPLOYER,
    SIGNATURE_EMPLOYER_SENT,
    SIGNATURE_EMPLOYER_SIGNED,
    SIGNATURE_EMPLOYEE_SENT,
    SIGNATURE_EMPLOYEE_SIGNED,
    SIGNATURE_MULTIPLIER_SENT,
    SIGNATURE_MULTIPLIER_SIGNED,
    CONTRACT_PREPARING_CONFIRMATION,
    VISA_DOCUMENTS_STARTED,
    VISA_DOCUMENTS_SUBMITTED,
    VISA_DOCUMENTS_VERIFIED,
    VISA_INVOICE_PAID,
    CONTRACT_PREPARING,
    CONTRACT_UPDATING,
    MEMBER_INVITE_PENDING,
    MEMBER_INVITED,
    VISA_APPLICATION_PREPARING,
    VISA_APPLICATION_FILING,
    VISA_APPLICATION_APPROVED,
    MEMBER_STARTED,
    MEMBER_DATA_REVIEWED,
    MEMBER_DATA_ADDED,
    MEMBER_COMPLETED,
    MEMBER_VERIFICATION_IN_PROGRESS,
    MEMBER_VERIFICATION_COMPLETED,
    MSA_SIGNING_PENDING,
    MSA_SIGNING_IN_PROGRESS,
    VERIFICATION_IN_PROGRESS,
    RESOLUTION_IN_PROGRESS,
    VERIFICATION_DONE,
    PREPARING_CONTRACT,
    SIGNING_IN_PROGRESS,
    ORDER_FORM_SENT_TO_MULTIPLIER,
    ORDER_FORM_SIGNED_BY_MULTIPLIER,
    ICA_SENT_TO_MULTIPLIER,
    ICA_SIGNED_BY_MULTIPLIER,
    ACTIVE,
    DROPPED,
    CONTRACT_WET_INK_PREPARING
}

fun OnboardingStatus.isAfter(other: OnboardingStatus) = this.ordinal > other.ordinal

fun OnboardingStatus.isOnOrAfter(other: OnboardingStatus) = this.ordinal >= other.ordinal
