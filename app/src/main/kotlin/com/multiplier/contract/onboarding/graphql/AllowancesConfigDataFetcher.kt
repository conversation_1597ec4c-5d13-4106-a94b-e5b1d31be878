package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.DgsConstants
import com.multiplier.contract.onboarding.adapter.bulk.BulkPayrollServiceAdapter
import com.multiplier.contract.onboarding.types.Allowance
import com.multiplier.contract.onboarding.types.CountryCode
import com.multiplier.transaction.graphql.graphApi
import com.netflix.graphql.dgs.*

@DgsComponent
class AllowancesConfigDataFetcher(private val bulkPayrollService: BulkPayrollServiceAdapter) {
    @DgsQuery(field = DgsConstants.QUERY.CountryAllowancesConfig)
    fun countryAllowancesConfig(
        @InputArgument("country") countryCode: CountryCode
    ): List<Allowance> = graphApi {
        bulkPayrollService.getAllowanceStructure(countryCode).map {
            Allowance.newBuilder()
                .country(countryCode)
                .name(it.name)
                .label(it.label)
                .mandatory(it.mandatory)
                .build()
        }
    }
}
