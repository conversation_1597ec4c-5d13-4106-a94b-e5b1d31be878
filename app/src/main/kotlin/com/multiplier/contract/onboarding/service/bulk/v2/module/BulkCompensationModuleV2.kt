package com.multiplier.contract.onboarding.service.bulk.v2.module

import com.multiplier.common.exception.toBusinessException
import com.multiplier.contract.onboarding.adapter.bulk.BulkCompensationSchemaServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.BulkCompensationServiceAdapter
import com.multiplier.contract.onboarding.adapter.bulk.GrpcValidationResult
import com.multiplier.contract.onboarding.domain.model.bulk.*
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.BasePaySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.CurrencySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.PayrollFrequencySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService.Companion.RateFrequencySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractStatusSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.ContractTypeSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmployeeIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmploymentTermSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.RowIdentifierSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.StartOnSpec
import com.multiplier.contract.onboarding.service.bulk.v2.BulkCreationResultV2
import com.multiplier.contract.onboarding.service.bulk.v2.CreationInput
import com.multiplier.contract.onboarding.service.bulk.v2.bulkUpsert
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2.Companion.COMPENSATION_SERVICE_KEYS.CONTRACT_START_ON
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2.Companion.COMPENSATION_SERVICE_KEYS.CONTRACT_STATUS
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2.Companion.COMPENSATION_SERVICE_KEYS.CONTRACT_TERM
import com.multiplier.contract.onboarding.service.bulk.v2.module.BulkCompensationModuleV2.Companion.COMPENSATION_SERVICE_KEYS.CONTRACT_TYPE
import com.multiplier.contract.onboarding.types.*
import com.multiplier.contract.onboarding.usecase.ModuleParams
import com.multiplier.contract.schema.compensation.CompensationOuterClass.CreateCompensationInput
import org.springframework.stereotype.Service

data class CompensationInputsForContract(
    val compensationData: List<Map<String, String>> // each map contains data for one compensation
)

@Service
class BulkCompensationModuleV2(
    private val bulkCompensationSchemaServiceAdapter: BulkCompensationSchemaServiceAdapter,
    private val bulkCompensationServiceAdapter: BulkCompensationServiceAdapter,
) : BulkDataModule {
    companion object {
        const val MODULE_NAME = "COMPENSATION_SCHEMA_MODULE"
        const val COMPENSATION_SCHEMA_DATA_SPEC = "COMPENSATION_SCHEMA_DATA_SPEC"

        object COMPENSATION_SERVICE_KEYS {
            const val CONTRACT_ID = "CONTRACT_ID"
            const val CONTRACT_TYPE = "CONTRACT_TYPE"
            const val CONTRACT_TERM = "CONTRACT_TERM"
            const val CONTRACT_STATUS = "CONTRACT_STATUS"
            const val CONTRACT_START_ON = "CONTRACT_START_ON"
            const val EMPLOYEE_FULL_NAME = "EMPLOYEE_FULL_NAME"
        }
    }

    override fun identifier(): String {
        return MODULE_NAME
    }

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        return when (onboardingOptions.context
            ?: throw ErrorCodes.VALIDATION_FAILED.toBusinessException("Context is required")) {
            BulkOnboardingContext.AOR,
            BulkOnboardingContext.FREELANCER ->
                listOf(
                    PayrollFrequencySpec.copy(
                        description = "Expected payout frequency",
                        allowedValues =
                            listOf(PayFrequency.MONTHLY.name, PayFrequency.SEMIMONTHLY.name)),
                    RateFrequencySpec.copy(
                        description = "Billing Rate frequency of the contractor",
                        allowedValues =
                            listOf(
                                RateFrequency.HOURLY.name,
                                RateFrequency.DAILY.name,
                                RateFrequency.SEMIMONTHLY.name,
                                RateFrequency.MONTHLY.name)),
                    CurrencySpec.copy(
                        type = DataSpecType.SELECT,
                        description = "Billing currency of the contractor",
                        allowedValues = enumValues<CurrencyCode>().map { it.name }),
                    BasePaySpec.copy(
                        description = "Base Pay Rate of the contractor",
                    ))
            BulkOnboardingContext.EOR -> {
                val specs =
                    listOf(RowIdentifierSpec) +
                        bulkCompensationSchemaServiceAdapter.getDataSpecs(
                            onboardingOptions, moduleParams)

                specs
                    .filterNot { it.key == EmployeeIdSpec.key }
                    .map { it.copy(source = COMPENSATION_SCHEMA_DATA_SPEC) }
            }
            BulkOnboardingContext.GLOBAL_PAYROLL ->
                bulkCompensationSchemaServiceAdapter
                    .getDataSpecs(onboardingOptions, moduleParams)
                    .map { it.copy(source = COMPENSATION_SCHEMA_DATA_SPEC) }
            else ->
                throw ErrorCodes.UNSUPPORTED_OPERATION.toBusinessException(
                    "Bulk Onboarding context value ${onboardingOptions.context} not supported yet",
                    context = mapOf("context" to onboardingOptions.context))
        }
    }

    override fun getFieldData(
        options: BulkOnboardingOptions,
        contractIds: List<Long>
    ): List<EmployeeDataChunk> {
        return bulkCompensationSchemaServiceAdapter.getFieldData(options, contractIds)
    }

    override fun validate(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<GrpcValidationResult<out Any>> {

        return when (options.context) {
            BulkOnboardingContext.AOR,
            BulkOnboardingContext.FREELANCER -> {
                bulkCompensationServiceAdapter.validateCompensationCreateInputs(
                    employeeData, options)
            }
            else -> {
                bulkCompensationSchemaServiceAdapter.validateCompensationCreateInputs(
                    employeeData, options, moduleParams)
            }
        }
    }

    override fun create(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        if (options.context == BulkOnboardingContext.FREELANCER ||
            options.context == BulkOnboardingContext.AOR) {
            // this calls contract service currently since freelancer and aor is not yet supported
            // by compensation service
            return callContractServiceCompensationCreate(
                validationResults, bulkCreationResult, options)
        }

        val upsertCompensationInputs =
            validationResults
                .groupBy { it.validationId }
                .map { (validationId, compensationValidationResults) ->
                    CreationInput(
                        requestId = validationId,
                        contractId = bulkCreationResult.getContractId(validationId),
                        data =
                            CompensationInputsForContract(
                                compensationValidationResults.map {
                                    it.input as Map<String, String>
                                }))
                }

        return bulkUpsert(
            "Upsert compensation",
            upsertCompensationInputs,
            options,
            bulkCreationResult,
            CreationInput.refContractId,
        ) { inputs, _ ->
            bulkCompensationSchemaServiceAdapter.createCompensations(inputs)
        }
    }

    fun callContractServiceCompensationCreate(
        validationResults: List<GrpcValidationResult<out Any>>,
        bulkCreationResult: BulkCreationResultV2,
        options: BulkOnboardingOptions
    ): BulkCreationResultV2 {
        val upsertCompensationInputs =
            validationResults.map {
                CreationInput(
                    requestId = it.validationId,
                    contractId = bulkCreationResult.getContractId(it.validationId),
                    data = it.input as CreateCompensationInput)
            }

        return bulkUpsert(
            "Upsert compensation",
            upsertCompensationInputs,
            options,
            bulkCreationResult,
            CreationInput.refContractId,
        ) { inputs, _ ->
            bulkCompensationServiceAdapter.createCompensations(upsertCompensationInputs, options)
        }
    }
}

fun List<EmployeeData>.compensationData(
    bulkOnboardingContext: BulkOnboardingContext
): List<EmployeeData> {
    val compensationGroup: String
    val dataToEnrich: List<EmployeeData>
    val keySelector: (EmployeeIdentification) -> String?
    when (bulkOnboardingContext) {
        BulkOnboardingContext.EOR -> {
            compensationGroup = EmployeeData.EOR_COMPENSATION_DATA_GROUP
            dataToEnrich = this.basicDetails()
            keySelector = { it.rowIdentifierFromSheet }
        }
        else -> {
            compensationGroup = EmployeeData.COMPENSATION_DATA_GROUP
            dataToEnrich = this.contractData()
            keySelector = { it.employeeId }
        }
    }
    val compensationData = this.filter { it.group == compensationGroup }
    return compensationData.enrichCompensationDataWithEmploymentData(dataToEnrich, keySelector)
}

fun List<EmployeeData>.enrichCompensationDataWithEmploymentData(
    employmentData: List<EmployeeData>,
    keySelector: (EmployeeIdentification) -> String?
): List<EmployeeData> {
    val contractPropertiesMap = extractContractProperties(employmentData, keySelector)
    return this.map {
        val key = keySelector(it.identification)
        it.copy(data = it.data + contractPropertiesMap[key]?.toCompensationSchemaData().orEmpty())
    }
}

private data class ContractProperties(
    val type: String,
    val term: String,
    val status: String,
    val startOn: String,
) {
    fun toCompensationSchemaData(): Map<String, String> =
        mapOf(
            CONTRACT_TYPE to type,
            CONTRACT_TERM to term,
            CONTRACT_STATUS to status,
            CONTRACT_START_ON to startOn,
        )
}

private fun extractContractProperties(
    employeeData: List<EmployeeData>,
    keySelector: (EmployeeIdentification) -> String?
): Map<String, ContractProperties> =
    employeeData
        .filterNot { keySelector(it.identification).isNullOrBlank() }
        .groupBy { keySelector(it.identification)!! }
        .mapValues { it.value.map { employee -> employee.data }.reduce { acc, map -> acc + map } }
        .mapValues { (_, data) ->
            ContractProperties(
                type = data[ContractTypeSpec.key].orEmpty(),
                term = data[EmploymentTermSpec.key].orEmpty(),
                status =
                    data[ContractStatusSpec.key].orEmpty().ifEmpty {
                        ContractStatus.ONBOARDING.name
                    },
                startOn = data[StartOnSpec.key].orEmpty(),
            )
        }
