package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.service.ReminderCtaHelper.addMultiFrequencyFields
import com.multiplier.contract.onboarding.service.ReminderCtaHelper.getMultiFrequencyEditableParams
import com.multiplier.contract.onboarding.service.ReminderCtaHelper.guideLinkField
import com.multiplier.contract.onboarding.service.ReminderCtaHelper.monthPayDateField
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.springframework.stereotype.Component

@Component
class OpsMemberUpdateDetailsAndKYCReminderCtaProcessor : BaseReminderCtaTemplateProcessor() {

    private val MEMBER_ONBOARDING_IN_PROGRESS_STATUSES =
        setOf(
            ContractOnboardingStatus.MEMBER_INVITED,
            ContractOnboardingStatus.MEMBER_STARTED,
            ContractOnboardingStatus.MEMBER_DATA_ADDED)

    override fun notificationType() = NotificationType.OpsMemberUpdateDetailsAndKYCReminderCta

    override fun validate(preFetchedData: EmailTemplateData): Boolean {
        if (preFetchedData.contract.type != ContractOuterClass.ContractType.EMPLOYEE ||
            preFetchedData.operationsUser == null ||
            (preFetchedData.contract.country == "USA" &&
                preFetchedData.multiFrequencySupportEnabled)) {
            return false
        }

        val onboardingCompany = preFetchedData.onboardingCompany
        val onboardingMember = preFetchedData.onboardingMember

        if (onboardingCompany.status in setOf(ContractOnboardingStatus.MEMBER_INVITED)) {
            return true
        }

        if (onboardingMember != null &&
            onboardingMember.status in MEMBER_ONBOARDING_IN_PROGRESS_STATUSES) {
            return true
        }
        return false
    }

    override fun fetchTemplateData(preFetchedData: EmailTemplateData): Map<String, String> {
        val data = mutableMapOf<String, String>()
        data += guideLinkField(preFetchedData)
        data += monthPayDateField(preFetchedData)
        addMultiFrequencyFields(data, preFetchedData)
        return data
    }

    override fun editableParams(preFetchedData: EmailTemplateData): List<EditableParam> {
        return getMultiFrequencyEditableParams(preFetchedData)
    }
}
