package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.onboarding.domain.model.Department
import com.multiplier.orgmanagement.schema.DepartmentServiceGrpc.DepartmentServiceBlockingStub
import com.multiplier.orgmanagement.schema.grpcDepartmentFilter
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface DepartmentServiceAdapter {
    fun getDepartments(companyId: Long): List<Department>
}

@Service
class DepartmentServiceAdapterImpl : DepartmentServiceAdapter {

    @GrpcClient("org-management-service")
    private lateinit var blockingStub: DepartmentServiceBlockingStub

    override fun getDepartments(companyId: Long): List<Department> {
        val request = grpcDepartmentFilter { this.companyId = companyId }
        return blockingStub.getDepartmentsByFilter(request).departmentsList.map {
            Department(it.id, companyId, it.name)
        }
    }
}
