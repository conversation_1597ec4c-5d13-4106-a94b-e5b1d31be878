package com.multiplier.contract.onboarding.service.extensions

import com.multiplier.contract.schema.contract.ContractOuterClass

fun ContractOuterClass.Contract.isFreelancer() =
    this.type == ContractOuterClass.ContractType.FREELANCER

fun ContractOuterClass.Contract.isFreelancerOrContractor() =
    this.type == ContractOuterClass.ContractType.FREELANCER ||
        this.type == ContractOuterClass.ContractType.CONTRACTOR

fun ContractOuterClass.Contract.isHRMember() =
    this.type == ContractOuterClass.ContractType.HR_MEMBER

fun ContractOuterClass.Contract.isVisaFlow() =
    this.workStatus == ContractOuterClass.CountryWorkStatus.REQUIRES_VISA
