package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.service.ReminderCtaHelper.addMultiFrequencyFields
import com.multiplier.contract.onboarding.service.ReminderCtaHelper.getMultiFrequencyEditableParams
import com.multiplier.contract.onboarding.service.ReminderCtaHelper.guideLinkField
import com.multiplier.contract.onboarding.service.ReminderCtaHelper.monthPayDateField
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.springframework.stereotype.Component

@Component
class OpsMemberUpdatePayrollFormsReminderCtaProcessor : BaseReminderCtaTemplateProcessor() {

    override fun notificationType() = NotificationType.OpsMemberUpdatePayrollFormsReminderCta

    override fun validate(preFetchedData: EmailTemplateData): Boolean {
        if (preFetchedData.contract.type != ContractOuterClass.ContractType.EMPLOYEE ||
            preFetchedData.operationsUser == null ||
            (preFetchedData.contract.country == "USA" &&
                preFetchedData.multiFrequencySupportEnabled)) {
            return false
        }

        return isPayrollFormsRequired(preFetchedData) && !isPayrollFormsSubmitted(preFetchedData)
    }

    private fun isPayrollFormsRequired(
        prefetchedData: EmailTemplateData,
    ): Boolean {
        return !prefetchedData.payrollFormRequirements[prefetchedData.contract.countryAndState]
            .isNullOrEmpty()
    }

    private fun isPayrollFormsSubmitted(
        preFetchedData: EmailTemplateData,
    ): Boolean {
        val requiredDocuments =
            preFetchedData.payrollFormRequirements[preFetchedData.contract.countryAndState]

        if (requiredDocuments.isNullOrEmpty()) return true

        return preFetchedData.submittedLegalDocument.containsAll(requiredDocuments)
    }

    override fun fetchTemplateData(preFetchedData: EmailTemplateData): Map<String, String> {
        val data = mutableMapOf<String, String>()
        data += guideLinkField(preFetchedData)
        data += monthPayDateField(preFetchedData)
        addMultiFrequencyFields(data, preFetchedData)
        return data
    }

    override fun editableParams(preFetchedData: EmailTemplateData): List<EditableParam> {
        return getMultiFrequencyEditableParams(preFetchedData)
    }
}
