package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.excel.BulkUploadExcel
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkOrgManagementDataService
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.springframework.stereotype.Service

@Service
class BulkEnrichmentForHRISService(private val contractServiceAdapter: ContractServiceAdapter) :
    BulkEnrichmentServiceInterface {

    override fun enrichEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions
    ): List<EmployeeData> {
        val contracts =
            contractServiceAdapter
                .getNonDeletedNonEndedContracts(
                    employeeData.mapNotNull { it.identification.contractId }.toSet())
                .associateBy { it.id }

        return employeeData.map {
            enrichEmployeeData(it, options, contracts[it.identification.contractId])
        }
    }

    private fun enrichEmployeeData(
        employeeData: EmployeeData,
        options: BulkOnboardingOptions,
        contract: ContractOuterClass.Contract?
    ): EmployeeData {
        val extraData =
            mapOf(
                "companyId" to options.companyId.toString(),
                "legalEntityId" to getLegalEntityId(employeeData),
                "department" to getDepartmentId(employeeData),
                "isManager" to getIsManager(employeeData),
                "gender" to employeeData.getGender(),
                "memberId" to contract?.memberId?.toString().orEmpty(),
                "state" to contract?.countryStateCode.orEmpty())

        return employeeData.copy(data = employeeData.data + extraData)
    }

    private fun getLegalEntityId(employeeData: EmployeeData): String {
        return employeeData.data[BulkContractDataService.LegalEntityIdSpec.key]
            .orEmpty()
            .split("-")
            .first()
            .trim()
    }

    private fun getDepartmentId(employeeData: EmployeeData): String {
        return employeeData.data[BulkOrgManagementDataService.DepartmentSpec.key]
            .orEmpty()
            .split("-")
            .first()
            .trim()
    }

    private fun getIsManager(employeeData: EmployeeData): String {
        return if (employeeData.data["isManager"].orEmpty().uppercase() ==
            BulkUploadExcel.BooleanValues.YES.name)
            BulkUploadExcel.BooleanValues.YES.value
        else BulkUploadExcel.BooleanValues.NO.value
    }
}
