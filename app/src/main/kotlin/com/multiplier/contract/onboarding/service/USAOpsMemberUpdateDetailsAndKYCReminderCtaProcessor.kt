package com.multiplier.contract.onboarding.service

import com.multiplier.contract.onboarding.domain.model.EmailTemplateData
import com.multiplier.contract.onboarding.types.ContractOnboardingStatus
import com.multiplier.contract.onboarding.types.NotificationType
import com.multiplier.contract.schema.contract.ContractOuterClass
import org.springframework.stereotype.Component

@Component
class USAOpsMemberUpdateDetailsAndKYCReminderCtaProcessor : BaseReminderCtaTemplateProcessor() {

    private val MEMBER_ONBOARDING_IN_PROGRESS_STATUSES =
        setOf(
            ContractOnboardingStatus.MEMBER_INVITED,
            ContractOnboardingStatus.MEMBER_STARTED,
            ContractOnboardingStatus.MEMBER_DATA_ADDED)

    override fun notificationType() = NotificationType.USAOpsMemberUpdateDetailsAndKYCReminderCta

    override fun validate(preFetchedData: EmailTemplateData): Boolean {
        // Only validate when multi-frequency support is enabled and country is USA
        if (!preFetchedData.multiFrequencySupportEnabled ||
            preFetchedData.contract.country != "USA" ||
            preFetchedData.contract.type != ContractOuterClass.ContractType.EMPLOYEE ||
            preFetchedData.operationsUser == null) {
            return false
        }

        val onboardingCompany = preFetchedData.onboardingCompany
        val onboardingMember = preFetchedData.onboardingMember

        return onboardingCompany.status in setOf(ContractOnboardingStatus.MEMBER_INVITED) ||
            (onboardingMember != null &&
                onboardingMember.status in MEMBER_ONBOARDING_IN_PROGRESS_STATUSES)
    }

    override fun editableParams(preFetchedData: EmailTemplateData): List<EditableParam> {
        // USA implementation only includes CutoffParam
        return listOf(CutoffParam())
    }
}
