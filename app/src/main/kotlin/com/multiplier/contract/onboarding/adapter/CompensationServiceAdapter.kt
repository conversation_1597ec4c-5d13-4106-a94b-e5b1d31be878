package com.multiplier.contract.onboarding.adapter

import com.multiplier.contract.schema.compensation.CompensationOuterClass.Compensation
import com.multiplier.contract.schema.compensation.CompensationOuterClass.IdsMessage
import com.multiplier.contract.schema.compensation.CompensationServiceGrpc.CompensationServiceBlockingStub
import net.devh.boot.grpc.client.inject.GrpcClient
import org.springframework.stereotype.Service

interface CompensationServiceAdapter {

    fun getCurrentCompensation(contractId: Long): Compensation?

    fun getCurrentCompensationByContractIds(contractIds: Set<Long>): Map<Long, Compensation>
}

@Service
class CompensationServiceAdapterImpl : CompensationServiceAdapter {

    @GrpcClient("contract-service")
    private lateinit var blockingStub: CompensationServiceBlockingStub

    override fun getCurrentCompensation(contractId: Long): Compensation? {
        return getCurrentCompensationByContractIds(setOf(contractId)).getOrDefault(contractId, null)
    }

    override fun getCurrentCompensationByContractIds(
        contractIds: Set<Long>
    ): Map<Long, Compensation> {
        if (contractIds.isEmpty()) {
            return mapOf()
        }

        val request = IdsMessage.newBuilder().addAllIds(contractIds).build()

        return blockingStub.getCurrentCompensationByContractIds(request).compensationsMap
    }
}
