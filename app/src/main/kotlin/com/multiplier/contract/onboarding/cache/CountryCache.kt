package com.multiplier.contract.onboarding.cache

import com.multiplier.contract.onboarding.adapter.CountryServiceAdapter
import com.multiplier.contract.onboarding.types.CountryCode
import java.util.concurrent.ConcurrentHashMap
import javax.annotation.PostConstruct
import javax.annotation.PreDestroy
import mu.KotlinLogging
import org.springframework.stereotype.Component

@Component
class CountryCache(private val countryService: CountryServiceAdapter) {

    private val countryCodeToNameCache = ConcurrentHashMap<CountryCode, String>()
    private val countryNameToCodeCache = ConcurrentHashMap<String, CountryCode>()
    private val log = KotlinLogging.logger {}

    @PostConstruct
    fun populateCache() {
        val countryCodes = CountryCode.values().toList()
        try {
            val countryMappings = countryService.getCountryNamesByCodes(countryCodes)
            if (countryMappings.isEmpty()) {
                log.warn("Country service returned empty country names = $countryMappings")
                throw IllegalStateException(
                    "Failed to populate country cache - received empty mappings")
            }
            countryMappings.forEach { (code, name) ->
                countryCodeToNameCache[code] = name
                countryNameToCodeCache[name.lowercase()] = code
            }
        } catch (e: Exception) {
            // not throwing error here since we don't want the service startup to fail in case some
            // issue with country service
            log.error(e) { "Failed to populate country cache" }
        }
    }

    fun getCountryName(code: CountryCode): String? {
        val fetchedName = countryCodeToNameCache[code]
        if (!fetchedName.isNullOrBlank()) return fetchedName

        val fetchedMap = countryService.getCountryNamesByCodes(listOf(code))
        val name = fetchedMap[code]

        return if (!name.isNullOrBlank()) {
            countryCodeToNameCache[code] = name
            countryNameToCodeCache[name.lowercase()] = code
            name
        } else {
            null
        }
    }

    fun getCountryCode(name: String): CountryCode? = countryNameToCodeCache[name.lowercase()]

    @PreDestroy
    fun clearCache() {
        countryCodeToNameCache.clear()
        countryNameToCodeCache.clear()
    }
}
