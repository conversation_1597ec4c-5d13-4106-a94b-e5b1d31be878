package com.multiplier.contract.onboarding.service.bulk.mapper

import com.multiplier.contract.onboarding.adapter.ContractBenefitAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpec
import com.multiplier.contract.onboarding.domain.model.bulk.DataSpecType
import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeDataChunk
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import org.springframework.stereotype.Service

@Service
class BulkInsuranceDataService(
    private val contractBenefitAdapter: ContractBenefitAdapter,
) {

    companion object {
        val InsuranceTypeSpec =
            DataSpec(
                key = "insuranceType",
                type = DataSpecType.TEXT,
                label = "Insurance Type",
                mandatory = false)
        val InsurancePlanSpec =
            DataSpec(
                key = "insurancePlan",
                type = DataSpecType.TEXT,
                label = "Insurance Plan",
                mandatory = false)
        val NumberOfDependantSpec =
            DataSpec(
                key = "numberOfDependant",
                type = DataSpecType.TEXT,
                label = "Number of Dependants",
                mandatory = false)
    }

    fun getDataSpecs(onboardingOption: BulkOnboardingOptions): List<DataSpec> {
        return when (onboardingOption.context) {
            BulkOnboardingContext.EOR -> dataSpecForEor()
            else -> return emptyList()
        }
    }

    fun getDataForSpecs(dataSpecs: List<DataSpec>, contractIdSet: Set<Long>): InsuranceDataContext {
        // TODO block by ContractBenefit GRPC

        return InsuranceDataContext(emptyMap())
    }

    private fun dataSpecForEor() =
        listOf(InsuranceTypeSpec, InsurancePlanSpec, NumberOfDependantSpec)
}

data class InsuranceDataContext(val contractIdToInsuranceMap: Map<Long, EmployeeDataChunk>)
