package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.domain.model.bulk.*
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.CountrySpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EmployeeIdSpec
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService.Companion.EndOnSpec
import com.multiplier.contract.onboarding.service.bulk.v2.module.*
import com.multiplier.contract.onboarding.types.BulkOnboardingContext
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import com.multiplier.contract.onboarding.types.ContractTerm
import com.multiplier.contract.onboarding.types.ContractType
import com.multiplier.contract.onboarding.usecase.EmployeeDataInput
import com.multiplier.contract.onboarding.usecase.ModuleParams
import mu.KotlinLogging
import org.springframework.stereotype.Service

@Service
class EorBulkOnboardingUseCaseV2(
    private val bulkContractModule: BulkContractModule,
    private val bulkMemberModule: BulkMemberModule,
    private val bulkContractOnboardingModule: BulkContractOnboardingModule,
    private val bulkCompensationModule: BulkCompensationModuleV2,
    private val bulkMemberLegalDataModule: BulkMemberLegalDataModule,
    private val bulkComplianceModule: BulkComplianceModule,
    private val bulkTimeOffModule: BulkTimeOffModule,
    private val bulkPostOnboardingTriggerModule: BulkPostOnboardingTriggerModule,
    private val companyServiceAdapter: CompanyServiceAdapter,
) : BulkOnboardingUseCase() {
    private val log = KotlinLogging.logger {}

    override fun getDataSpecs(
        onboardingOptions: BulkOnboardingOptions,
        moduleParams: ModuleParams?
    ): List<DataSpec> {
        return super.getDataSpecs(onboardingOptions, moduleParams).map {
            it.copy(group = groupBySource(it.source))
        }
    }

    override fun getBulkOnboardingModules(): List<BulkDataModule> {
        return listOf(
            bulkContractModule,
            bulkMemberModule,
            bulkMemberLegalDataModule,
            bulkContractOnboardingModule,
            bulkComplianceModule,
            bulkCompensationModule,
            bulkTimeOffModule,
            bulkPostOnboardingTriggerModule)
    }

    override fun validate(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): BulkValidationResultV2 {
        val enrichedEmployeeData = enrichEmployeeData(employeeDataInput, options)
        val bulkValidationResults = super.validate(enrichedEmployeeData, options)
        return addGroupToValidationResults(bulkValidationResults)
    }

    private fun addGroupToValidationResults(bulkValidationResult: BulkValidationResultV2) =
        bulkValidationResult.copy(
            validationResults =
                bulkValidationResult.validationResults.mapValues {
                    it.value.map { validationResult ->
                        validationResult.copy(groupName = groupByModule(it.key))
                    }
                })

    override fun filterEmployeeDataForModule(
        employeeData: List<EmployeeData>,
        moduleIdentifier: String
    ): List<EmployeeData> {
        return when (moduleIdentifier) {
            BulkComplianceModule.MODULE_NAME -> employeeData.complianceData()
            BulkCompensationModuleV2.MODULE_NAME ->
                addEmployeeIdToData(employeeData.compensationData(BulkOnboardingContext.EOR))
            else -> employeeData.basicDetails()
        }
    }

    override fun overrideModuleParams(
        moduleParams: ModuleParams?,
        options: BulkOnboardingOptions
    ): ModuleParams? {
        // override workplaceEntityId as primary entity id
        val legalEntity = companyServiceAdapter.getCompanyPrimaryLegalEntity(options.companyId)
        if (legalEntity != null && moduleParams != null) {
            return moduleParams.copy(workPlaceEntityId = legalEntity.id)
        }
        return moduleParams
    }

    private fun addEmployeeIdToData(dataList: List<EmployeeData>): List<EmployeeData> {
        // add row identifier from sheet as employee id since compensation service needs a group
        // key.
        // there is no validation for this nor do we save this on out end which is why this is added
        // here
        return dataList.map { employee ->
            val rowId = employee.identification.rowIdentifierFromSheet
            val updatedData = employee.data + (EmployeeIdSpec.key to rowId.orEmpty())
            employee.copy(data = updatedData)
        }
    }

    private fun groupBySource(source: String?) =
        when (source) {
            BulkCompensationModuleV2.COMPENSATION_SCHEMA_DATA_SPEC ->
                EmployeeData.EOR_COMPENSATION_DATA_GROUP
            BulkComplianceModule.COMPLIANCE_SCHEMA_DATA_SPEC ->
                EmployeeData.COMPLIANCE_AND_LEAVES_DATA_GROUP
            else -> EmployeeData.BASIC_DETAILS_GROUP
        }

    private fun groupByModule(module: String) =
        when (module) {
            BulkComplianceModule.MODULE_NAME -> EmployeeData.COMPLIANCE_AND_LEAVES_DATA_GROUP
            BulkCompensationModuleV2.MODULE_NAME -> EmployeeData.EOR_COMPENSATION_DATA_GROUP
            else -> EmployeeData.BASIC_DETAILS_GROUP
        }

    private fun enrichEmployeeData(
        employeeDataInput: EmployeeDataInput,
        options: BulkOnboardingOptions
    ): EmployeeDataInput {
        val basicDetails =
            employeeDataInput.employeeData.firstOrNull {
                it.group == EmployeeData.BASIC_DETAILS_GROUP
            }
        val term = getEmploymentTerm(basicDetails?.data?.get(EndOnSpec.key))

        return employeeDataInput.copy(
            employeeData =
                employeeDataInput.employeeData.map {
                    it.copy(
                        data =
                            it.data
                                .minus("contractId") // Disables upsert capability
                                .plus("type" to ContractType.EMPLOYEE.name)
                                .plus("term" to term)
                                .plus(CountrySpec.key to options.countryCode.name))
                })
    }

    fun getEmploymentTerm(inputEndOn: String?): String {
        return if (inputEndOn.isNullOrBlank()) ContractTerm.PERMANENT.name
        else ContractTerm.FIXED.name
    }
}
