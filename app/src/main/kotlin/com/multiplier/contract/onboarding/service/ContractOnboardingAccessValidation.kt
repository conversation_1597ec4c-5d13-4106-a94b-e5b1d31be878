package com.multiplier.contract.onboarding.service

import com.multiplier.common.exception.toSystemException
import com.multiplier.common.transport.auth.AccessDeniedResult
import com.multiplier.common.transport.auth.MPLAuthorization
import com.multiplier.common.transport.auth.MplAccessDeniedException
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.common.transport.user.UserContext
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.extensions.isCompanyExperience
import com.multiplier.contract.onboarding.service.extensions.isMemberExperience
import org.springframework.stereotype.Service

@Service
class ContractOnboardingAccessValidation(
    private val currentUser: CurrentUser,
    private val contractService: ContractServiceAdapter,
    private val mplAuthorization: MPLAuthorization,
) {
    fun validateByContractId(contractId: Long) {
        if (!currentUser.isCompanyExperience && !currentUser.isMemberExperience) {
            throwAccessDenied()
        }
        val contract = contractService.getContractById(contractId)
        if (currentUser.isCompanyExperience) {
            throwIfCurrentUserCompanyIdDoesNotMatchTargetContract(contract.companyId)
        }
        if (currentUser.isMemberExperience) {
            throwIfCurrentUserMemberIdDoesNotMatchTargetContract(contract.memberId)
        }
    }

    private fun throwAccessDenied() {
        throw MplAccessDeniedException(
            authzFailure =
                AccessDeniedResult(
                    resourceName = "onboarding",
                    userContext = validateUserContext(),
                    missingPermissions = emptyList(),
                ))
    }

    private fun validateUserContext(): UserContext {
        if (currentUser.context == null) {
            throw ErrorCodes.VALIDATION_FAILED.toSystemException("User context is not set")
        }

        return requireNotNull(currentUser.context)
    }

    private fun throwIfCurrentUserCompanyIdDoesNotMatchTargetContract(companyId: Long?) {
        if (!mplAuthorization.isCompanyUserForCompanyId(companyId)) {
            throwAccessDenied()
        }
    }

    private fun throwIfCurrentUserMemberIdDoesNotMatchTargetContract(memberId: Long?) {
        if (!mplAuthorization.isMyMemberId(memberId)) {
            throwAccessDenied()
        }
    }
}
