package com.multiplier.contract.onboarding.graphql

import com.multiplier.contract.onboarding.DgsConstants
import com.multiplier.contract.onboarding.service.reminders.OnboardingCtaReminderService
import com.multiplier.contract.onboarding.types.EmailTemplateDataResponse
import com.netflix.graphql.dgs.DgsComponent
import com.netflix.graphql.dgs.DgsQuery
import com.netflix.graphql.dgs.InputArgument
import org.springframework.security.access.prepost.PreAuthorize

@DgsComponent
class OnboardingReminderDataFetcher(
    private val onboardingCtaReminderService: OnboardingCtaReminderService
) {

    @DgsQuery(field = DgsConstants.QUERY.OpsEmailCtaReminder)
    @PreAuthorize("(@me.allowed('trigger.operations.onboarding-reminder.cta'))")
    fun getOpsEmailCtaReminder(
        @InputArgument("contractId") contractId: Long,
    ): List<EmailTemplateDataResponse> {
        return onboardingCtaReminderService.fetchAllCtaTemplatesWithData(contractId)
    }
}
