package com.multiplier.contract.onboarding.domain.model

import com.multiplier.contract.onboarding.types.OnboardingTask
import com.multiplier.contract.onboarding.types.OnboardingTaskUser
import com.multiplier.contract.onboarding.types.Persona

fun com.multiplier.contract.onboarding.domain.model.OnboardingTask.mapToGraph(
    index: Int
): OnboardingTask {
    return OnboardingTask.newBuilder()
        .order(index)
        .name(this.name.name)
        .completed(this.completed)
        .pendingOn(this.pendingOn?.mapToGraph())
        .build()
}

fun Person.mapToGraph(): OnboardingTaskUser {
    return OnboardingTaskUser.newBuilder()
        .id(this.id)
        .persona(if (this.persona == null) null else Persona.valueOf(this.persona.name))
        .firstName(this.firstName)
        .lastName(this.lastName)
        .userId(this.userId)
        .build()
}
