package com.multiplier.contract.onboarding.adapter

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.github.benmanes.caffeine.cache.Caffeine
import com.multiplier.contract.onboarding.config.FeignConfig
import feign.RequestInterceptor
import java.time.Duration
import org.springframework.beans.factory.annotation.Value
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.client.RestTemplate

data class User(
    val activationKey: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true) data class UserResponse(val activationKey: String?)

@Service
class UserServiceAdapter(
    private val userServiceClient: UserServiceClient,
) {

    fun getUserByEmail(email: String): User {
        return User(activationKey = userServiceClient.getUserByEmail(email).activationKey)
    }
}

@FeignClient(
    value = "user-service-api",
    url = "\${user-service.url}",
    configuration = [FeignConfig::class, UserServiceFeignConfig::class])
interface UserServiceClient {

    @PostMapping("/account") fun getUserByEmail(@RequestBody email: String): UserResponse
}

@Configuration
class UserServiceFeignConfig(
    @Value("\${user-service.username}") val username: String,
    @Value("\${user-service.password}") val password: String,
    @Value("\${user-service.url}") val userServiceUrl: String,
) {

    private val jwtCache =
        Caffeine.newBuilder().expireAfterWrite(Duration.ofMinutes(5)).build<String, String>()

    @Bean
    fun jwtHeaderInterceptor(): RequestInterceptor = RequestInterceptor { template ->
        if (template.feignTarget().url().startsWith(userServiceUrl)) {
            template.header("Authorization", "Bearer ${getJwtToken()}")
        }
    }

    private fun getJwtToken(): String? = jwtCache.get(username) { authenticate() }

    private fun authenticate(): String? {
        val auth =
            AuthRequest(
                username = username,
                password = password,
            )

        val headers = HttpHeaders().apply { contentType = MediaType.APPLICATION_JSON }
        val entity = HttpEntity(jacksonObjectMapper().writeValueAsString(auth), headers)

        return RestTemplate()
            .postForEntity("$userServiceUrl/authenticate", entity, AuthResponse::class.java)
            .body
            ?.idToken
    }

    data class AuthRequest(val username: String, val password: String)

    data class AuthResponse(@JsonProperty("id_token") val idToken: String)
}
