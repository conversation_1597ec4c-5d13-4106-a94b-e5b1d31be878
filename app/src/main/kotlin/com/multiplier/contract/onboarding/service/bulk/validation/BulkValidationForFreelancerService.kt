package com.multiplier.contract.onboarding.service.bulk.validation

import com.multiplier.contract.onboarding.domain.model.bulk.EmployeeData
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkCompensationDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkContractDataService
import com.multiplier.contract.onboarding.service.bulk.mapper.BulkMemberDataService
import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import org.springframework.stereotype.Service

@Service
class BulkValidationForFreelancerService(
    private val bulkMemberDataService: BulkMemberDataService,
    private val bulkContractDataService: BulkContractDataService,
    private val bulkCompensationDataService: BulkCompensationDataService,
) : BulkValidationServiceInterface {
    override fun validateEmployeeData(
        employeeData: List<EmployeeData>,
        options: BulkOnboardingOptions,
    ): EmployeeValidationResults {
        if (employeeData.isEmpty()) {
            return EmployeeValidationResults.EMPTY
        }

        val validatedMembers = bulkMemberDataService.validate(employeeData, options)
        val validatedContracts = bulkContractDataService.validate(employeeData, options)
        val validatedCompensations = bulkCompensationDataService.validate(employeeData, options)

        return EmployeeValidationResults(
            members = validatedMembers,
            contracts = validatedContracts,
            compensations = validatedCompensations,
            legalData = emptyList(),
            bankData = emptyList(),
            compliances = emptyList(),
            orgManagementData = emptyList(),
            emergencyData = emptyList(),
            educationData = emptyList(),
            previousEmployerData = emptyList(),
            addressData = emptyList(),
        )
    }

    override fun validateCompanyOrThrow(options: BulkOnboardingOptions) {
        // No validation required for freelancer
    }

    override fun validateCompanyLegalEntityOrThrow(options: BulkOnboardingOptions) {
        // No validation required for freelancer
    }
}
