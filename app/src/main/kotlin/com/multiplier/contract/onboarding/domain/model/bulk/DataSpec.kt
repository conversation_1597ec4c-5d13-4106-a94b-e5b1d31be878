package com.multiplier.contract.onboarding.domain.model.bulk

import com.multiplier.contract.onboarding.service.toWords

enum class DataSpecType {
    TEXT,
    NUMBER,
    BOOLEAN,
    DATE,
    MONTH_YEAR,
    SELECT
}

/**
 * A data spec holds the information about a data field for which the user needs to provide input.
 */
data class DataSpec(
    val key: String,
    val type: DataSpecType = DataSpecType.TEXT,
    val label: String = key.toWords(),
    val mandatory: Boolean = true,
    val description: String? = null,
    val allowedValues: Collection<String> = emptyList(),
    val source: String? = null,
    val prefix: String? = null,
    val group: String? = null,
) {
    fun keyWithPrefix(): String = if (prefix.isNullOrBlank()) key else "${prefix}.$key"
}
