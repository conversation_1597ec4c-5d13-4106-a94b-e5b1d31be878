package com.multiplier.contract.onboarding.service

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException
import java.util.*
import mu.KotlinLogging

private val log = KotlinLogging.logger {}

/** Constants for editable parameter keys */
object EditableParamConstants {
    const val CUTOFF_DATE_KEY = "cutOffDate"
    const val NEXT_PAYROLL_MONTH_YEAR_KEY = "nextPayrollMonthYear"
}

interface EditableParam {
    val key: String
    val required: Boolean
        get() = true

    /** Validates the parameter value including required check */
    fun validate(value: String?): Boolean {
        // Common required check
        // Handle null/blank values based on required flag
        if (value.isNullOrBlank()) {
            return if (required) {
                false // Required field is missing - validation fails
            } else {
                true // Optional field is missing - validation passes
            }
        }

        // Value is present - proceed with specific validation

        // Delegate to specific validation logic
        return validateValue(value.trim())
    }

    /** Implement specific validation logic for the parameter type */
    fun validateValue(value: String): Boolean

    /**
     * Transforms the validated value into the final format that will be used in the email template.
     * This function is called after validation and allows parameters to normalize, format, or
     * transform the value before it's merged with the template data.
     *
     * @param value The validated value to transform
     * @return The transformed value, or the original value if no transformation is needed
     */
    fun transform(value: String): String {
        return value // Default implementation returns the value unchanged
    }
}

class CutoffParam(override val key: String = EditableParamConstants.CUTOFF_DATE_KEY) :
    EditableParam {

    companion object {
        // Only support "15th January 2025" format - ordinal suffix is mandatory
        private val DATE_FORMATTER =
            DateTimeFormatter.ofPattern("d['st']['nd']['rd']['th'] MMMM yyyy", Locale.ENGLISH)
        private val ORDINAL_SUFFIX_REGEX = Regex("""^\d+(st|nd|rd|th)\s+\w+\s+\d{4}$""")
    }

    override fun validateValue(value: String): Boolean {
        // First check if the format matches our expected pattern with ordinal suffix
        if (!ORDINAL_SUFFIX_REGEX.matches(value)) {
            return false
        }

        try {
            val parsedDate = LocalDate.parse(value, DATE_FORMATTER)
            // Check if the date is after today
            return parsedDate.isAfter(LocalDate.now())
        } catch (e: DateTimeParseException) {
            log.error(e) { "Failed to parse date: ${e.message}" }
            return false
        }
    }

    /**
     * Parses the validated date string to LocalDate Should only be called after validate() returns
     * true
     */
    fun parseDate(value: String): LocalDate? {
        if (value.isBlank()) return null

        return try {
            LocalDate.parse(value.trim(), DATE_FORMATTER)
        } catch (e: DateTimeParseException) {
            null
        }
    }
}

class MonthYearParam(
    override val key: String = EditableParamConstants.NEXT_PAYROLL_MONTH_YEAR_KEY
) : EditableParam {

    companion object {
        // Valid month names (case-insensitive)
        private val VALID_MONTHS =
            setOf(
                "january",
                "february",
                "march",
                "april",
                "may",
                "june",
                "july",
                "august",
                "september",
                "october",
                "november",
                "december")

        // Regex pattern to match "Month Year" format (e.g., "January 2025")
        // Only allows exactly one space between month and year
        private val MONTH_YEAR_PATTERN = Regex("""^([a-zA-Z]+) (\d{4})$""")
    }

    override fun validateValue(value: String): Boolean {
        val matchResult = MONTH_YEAR_PATTERN.matchEntire(value.trim())
        if (matchResult == null) {
            return false
        }

        val (monthName, yearString) = matchResult.destructured
        val year = yearString.toIntOrNull() ?: return false

        // Validate month name
        if (!VALID_MONTHS.contains(monthName.lowercase())) {
            return false
        }

        // Validate year (should be reasonable - between 2020 and 2050)
        return year in 2020..2050
    }

    override fun transform(value: String): String {
        val matchResult = MONTH_YEAR_PATTERN.matchEntire(value.trim())
        if (matchResult == null) {
            return value // Return original if invalid format (shouldn't happen after validation)
        }

        val (monthName, yearString) = matchResult.destructured
        val normalizedMonth = monthName.lowercase().trim()

        return if (VALID_MONTHS.contains(normalizedMonth)) {
            "${normalizedMonth.replaceFirstChar { it.uppercase() }} $yearString"
        } else {
            value // Return original value if not a valid month (shouldn't happen after validation)
        }
    }
}
