package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.domain.model.PayrollCycle
import java.time.LocalDate
import java.time.YearMonth

data class PayrollCycleContracts(
    val cutoffDate: LocalDate,
    val payrollMonth: YearMonth,
    val payFrequency: PayrollCycle.PayFrequency? = null,
    val payDay: Int,
    val payDate: LocalDate? = null,
    val contractStartFrom: LocalDate,
    val contractStartTo: LocalDate,
    val contractIds: List<Long> = emptyList(),
) {
    constructor(
        cycle: PayrollCycleContracts,
        contractIds: List<Long>
    ) : this(
        cutoffDate = cycle.cutoffDate,
        payrollMonth = cycle.payrollMonth,
        payFrequency = cycle.payFrequency,
        payDay = cycle.payDay,
        payDate = cycle.payDate,
        contractStartFrom = cycle.contractStartFrom,
        contractStartTo = cycle.contractStartTo,
        contractIds = contractIds,
    )
}
