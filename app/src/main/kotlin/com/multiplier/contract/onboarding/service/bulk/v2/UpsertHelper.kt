package com.multiplier.contract.onboarding.service.bulk.v2

import com.multiplier.contract.onboarding.types.BulkOnboardingOptions
import mu.KotlinLogging

private val log = KotlinLogging.logger {}

fun <T> bulkUpsert(
    operation: String,
    inputs: List<CreationInput<T>>,
    options: BulkOnboardingOptions,
    refKeyExtractor: (CreationInput<T>) -> Any?,
    upsertMethod: (List<CreationInput<T>>, BulkOnboardingOptions) -> List<CreationResult>
): List<CreationResult> {
    val (inputsWithRefKey, inputsWithoutRefKey) = inputs.partition { refKeyExtractor(it) != null }
    val creationResults: List<CreationResult> = upsertMethod(inputsWithRefKey, options)
    val missingResults: List<CreationResult> =
        getMissingCreationResults(inputsWithRefKey, creationResults, operation)
    val cancelledResults: List<CreationResult> =
        inputsWithoutRefKey.map {
            CreationResult.error(
                it.requestId,
                "$operation cancelled due to an internal error: reference key is missing")
        }

    val allCreationResults =
        (creationResults + missingResults + cancelledResults).map {
            it.copy(
                inputMemberId = inputs.findMemberId(it.requestId),
                inputContractId = inputs.findContractId(it.requestId))
        }
    allCreationResults
        .filter { !it.success }
        .forEach {
            val input = inputs.find { input -> input.requestId == it.requestId }
            if (input != null) {
                log.error {
                    "$operation failed for requestId: ${it.requestId}, memberId: ${input.memberId}, contractId: ${input.contractId}, errorCount: ${it.errors.size}"
                }
            } else {
                log.error {
                    "$operation failed for requestId: ${it.requestId}, errorCount: ${it.errors.size}"
                }
            }
        }

    return allCreationResults
}

private fun List<CreationInput<*>>.findMemberId(requestId: String): Long? {
    return find { it.requestId == requestId }?.memberId
}

private fun List<CreationInput<*>>.findContractId(requestId: String): Long? {
    return find { it.requestId == requestId }?.contractId
}

fun <T> bulkUpsert(
    operation: String,
    inputs: List<CreationInput<T>>,
    options: BulkOnboardingOptions,
    aggregatedBulkCreationResult: BulkCreationResultV2,
    refKeyExtractor: (CreationInput<T>) -> Any?,
    upsertMethod: (List<CreationInput<T>>, BulkOnboardingOptions) -> List<CreationResult>
): BulkCreationResultV2 {
    if (inputs.isEmpty()) {
        log.warn { "No inputs for $operation" }
        return aggregatedBulkCreationResult
    }

    val results = bulkUpsert(operation, inputs, options, refKeyExtractor, upsertMethod)
    logBulkCreationResults(results, aggregatedBulkCreationResult, operation)

    return aggregatedBulkCreationResult.addErrorsFrom(results)
}

private fun getMissingCreationResults(
    inputsSentForCreation: List<CreationInput<*>>,
    creationResults: List<CreationResult>,
    operation: String
): List<CreationResult> {
    val missingRequestIds =
        inputsSentForCreation.map { it.requestId }.toSet() -
            creationResults.map { it.requestId }.toSet()
    return missingRequestIds.map {
        CreationResult.error(
            it, "$operation result not found. This could be due to an internal system error.")
    }
}

fun logBulkCreationResults(
    creationResults: List<CreationResult>,
    bulkCreationResult: BulkCreationResultV2,
    operation: String,
) {
    val successCount = creationResults.count { it.success }
    val failureCount = creationResults.count { !it.success }
    log.info {
        "Bulk creation results for $operation: success: $successCount, failure: $failureCount"
    }

    val successRequestIds = creationResults.filter { it.success }.map { it.requestId }
    if (successRequestIds.isNotEmpty()) {
        val contractIds = successRequestIds.mapNotNull { bulkCreationResult.getContractId(it) }
        if (contractIds.isNotEmpty()) {
            log.info("$operation completed successfully for contracts: $contractIds")
        } else {
            val memberIds = successRequestIds.mapNotNull { bulkCreationResult.getMemberId(it) }
            log.info("$operation completed successfully for members: $memberIds")
        }
    }
    val failedRequestIds = creationResults.filter { !it.success }.map { it.requestId }
    if (failedRequestIds.isNotEmpty()) {
        val contractIds = failedRequestIds.mapNotNull { bulkCreationResult.getContractId(it) }
        if (contractIds.isNotEmpty()) {
            log.warn("$operation failed for contracts: $contractIds")
        } else {
            val memberIds = failedRequestIds.mapNotNull { bulkCreationResult.getMemberId(it) }
            log.warn("$operation failed for members: $memberIds")
        }
    }
}
