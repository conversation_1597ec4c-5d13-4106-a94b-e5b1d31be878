package com.multiplier.contract.onboarding.graphql.dataloader

import com.multiplier.contract.onboarding.DgsConstants
import com.multiplier.contract.onboarding.domain.Clock
import com.multiplier.contract.onboarding.graphql.extensions.toActivationCutoffData
import com.multiplier.contract.onboarding.service.ActivationCutoffService
import com.multiplier.contract.onboarding.types.ActivationCutoffData
import com.multiplier.transaction.graphql.futureGraphApi
import com.netflix.graphql.dgs.DgsDataLoader
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionStage
import mu.KotlinLogging
import org.dataloader.MappedBatchLoader

@DgsDataLoader(name = DgsConstants.CONTRACTONBOARDING.ActivationCutoffData, maxBatchSize = 500)
class ActivationCutoffDataDataLoader(
    private val activationCutoffService: ActivationCutoffService,
    private val clock: Clock,
) : MappedBatchLoader<Long, ActivationCutoffData?> {

    private val log = KotlinLogging.logger {}

    override fun load(onboardingIds: Set<Long>): CompletionStage<Map<Long, ActivationCutoffData?>> {
        if (onboardingIds.isEmpty()) {
            return CompletableFuture.completedFuture(emptyMap())
        }

        return futureGraphApi {
                val checkingDate = clock.today()
                val onboardingIdToCutoffDateMap =
                    activationCutoffService.getActivationCutoffDateByOnboardingId(
                        onboardingIds.toList(), checkingDate)

                onboardingIds.associateWith {
                    onboardingIdToCutoffDateMap[it].toActivationCutoffData(checkingDate)
                }
            }
            .thenApply { it as Map<Long, ActivationCutoffData?> }
            .exceptionally {
                log.error(it) {
                    "[ActivationCutoffDataDataLoader] failed to load onboarding cutoff date for ${onboardingIds.size} onboarding ids"
                }
                emptyMap()
            }
    }
}
