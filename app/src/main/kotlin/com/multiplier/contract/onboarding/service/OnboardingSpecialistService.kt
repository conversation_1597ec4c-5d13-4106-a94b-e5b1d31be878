package com.multiplier.contract.onboarding.service

import com.multiplier.common.exception.toBusinessException
import com.multiplier.common.transport.user.CurrentUser
import com.multiplier.contract.onboarding.adapter.CompanyServiceAdapter
import com.multiplier.contract.onboarding.adapter.ContractServiceAdapter
import com.multiplier.contract.onboarding.adapter.MemberServiceAdapter
import com.multiplier.contract.onboarding.adapter.OperationsUserServiceAdapter
import com.multiplier.contract.onboarding.exception.ErrorCodes
import com.multiplier.contract.onboarding.service.extensions.isCompanyExperience
import com.multiplier.contract.onboarding.service.extensions.isMemberExperience
import com.multiplier.contract.onboarding.service.extensions.userId
import com.multiplier.contract.onboarding.service.notifications.NotificationService
import com.multiplier.contract.onboarding.service.notifications.OpsEmailResolver
import com.multiplier.contract.onboarding.service.reminders.Notification
import com.multiplier.contract.onboarding.types.NotificationType
import java.util.*
import org.springframework.stereotype.Service

@Service
class OnboardingSpecialistService(
    private val operationsUserService: OperationsUserServiceAdapter,
    private val memberService: MemberServiceAdapter,
    private val companyService: CompanyServiceAdapter,
    private val contractService: ContractServiceAdapter,
    private val notificationService: NotificationService,
    private val currentUser: CurrentUser,
    private val opsEmailResolver: OpsEmailResolver,
) {
    companion object {
        private const val CONTRACT_ID = "contractId"
    }

    fun sendEmailToOnboardingSpecialist(contractId: Long, message: String): String {
        val recipient = getOnboardingSpecialistEmail(contractId)
        val data =
            mapOf(
                "message" to message,
                "contractId" to contractId.toString(),
                "requesterType" to getRequesterType(),
            )
        val requester = getRequester(contractId)

        val notification =
            Notification(
                type = NotificationType.OnboardingSpecialistEmail,
                subject = "Onboarding Specialist Help Required for ContractID $contractId",
                from = opsEmailResolver.getSenderEmail(),
                to = recipient,
                cc = listOf(requester.nameAndEmail),
                attachments = Collections.emptyList(),
                data = data)

        notificationService.send(notification)

        return requester.email
    }

    private fun getOnboardingSpecialistEmail(contractId: Long): String {
        val contract = contractService.getContractById(contractId)
        val company = companyService.getCompany(contract.companyId)

        if (company.isTest) {
            return opsEmailResolver.getOnboardingSpecialistTestEmail()
        }

        val onboardingSpecialist =
            operationsUserService.getOperationsUsersForContracts(listOf(contractId))

        onboardingSpecialist.values.firstOrNull()?.email?.let {
            return it
        }

        throw ErrorCodes.ONBOARDING_SPECIALIST_NOT_FOUND.toBusinessException(
            "No onboarding specialist found for contract $contractId",
            context = mapOf(CONTRACT_ID to contractId))
    }

    private fun getRequesterType(): String {
        if (currentUser.isCompanyExperience) {
            return "customer"
        }

        if (currentUser.isMemberExperience) {
            return "member"
        }

        throw ErrorCodes.EXPERIENCE_NOT_FOUND.toBusinessException()
    }

    private fun getRequester(contractId: Long): Requester {

        if (currentUser.isCompanyExperience) {
            val companyUser = companyService.getCompanyUserByUserId(currentUser.userId)

            return Requester(
                email = companyUser.email,
                nameAndEmail =
                    "${companyUser.firstName} ${companyUser.lastName} <${companyUser.email}>",
            )
        }

        if (currentUser.isMemberExperience) {
            val member = memberService.getMemberByUserId(currentUser.userId)

            if (member?.email?.isNotEmpty()!!) {
                return Requester(
                    email = member.email,
                    nameAndEmail = "${member.firstName} ${member.lastName} <${member.email}>",
                )
            }
        }

        throw ErrorCodes.MEMBER_NOT_FOUND.toBusinessException(
            "No member found for contract $contractId", context = mapOf(CONTRACT_ID to contractId))
    }

    private class Requester(
        val email: String,
        val nameAndEmail: String,
    )
}
