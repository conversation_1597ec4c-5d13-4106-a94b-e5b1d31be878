package com.multiplier.contract.onboarding.service.reminders

import com.multiplier.contract.onboarding.service.convertToMap
import com.multiplier.contract.onboarding.service.toFormattedDateOrEmpty
import com.multiplier.contract.onboarding.types.NotificationType
import java.time.LocalDate
import java.time.YearMonth

data class CompanyOnboardingNotificationEmailDto(
    val notificationType: NotificationType,
    val subject: String,
    val companyUserEmails: List<String>,
    val saleUserEmail: String,
    val csmUserEmail: String,
    val onboardingOpsUserEmails: List<String>,
    val companyLogoLink: String?,
    val companyName: String,
    val companyCountry: String?,
    val onboardingCount: Int = 0,
    val employeeFullNames: List<String>,
    val payrollMonth: YearMonth?,
    val cutoffDate: LocalDate,
    val daysToCutoff: Int = 0,
    val pendingOnboardings: List<PendingOnboardingEmailDto>,
    val contractOnboardingsLink: String,
) {

    fun toPropertyMap(): Map<String, Any> =
        convertToMap(
            overrides =
                mapOf(
                    "employeeFullNames" to employeeFullNames.joinToString(","),
                    "pendingOnboardings" to pendingOnboardings.map { it.toPropertyMap() },
                    "payrollMonth" to payrollMonth.toFormattedDateOrEmpty(),
                    "cutoffDate" to cutoffDate.toFormattedDateOrEmpty(),
                ))
}
