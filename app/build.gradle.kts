import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

plugins {
    id("io.spring.dependency-management")
    id("java")
    id("org.liquibase.gradle")
    id("org.springframework.boot")

    kotlin("jvm")
    kotlin("kapt")
    kotlin("plugin.lombok")
    kotlin("plugin.spring")
    kotlin("plugin.allopen")
    kotlin("plugin.jpa")
    kotlin("plugin.serialization")
}

java.sourceCompatibility = JavaVersion.VERSION_17

dependencies {
    implementation(project(":schema"))
    runtimeOnly("org.postgresql:postgresql")
    runtimeOnly("io.grpc:grpc-api:1.63.0")

    runtimeOnly("org.codehaus.janino:janino") // Logback conditions
    runtimeOnly("net.logstash.logback:logstash-logback-encoder") // Structured (JSON) logging

    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.cloud:spring-cloud-starter-openfeign:4.1.3")
    implementation("org.springframework.kafka:spring-kafka")
    implementation("org.hibernate.orm:hibernate-envers")
    implementation("com.netflix.graphql.dgs:graphql-dgs-spring-boot-starter")
    implementation("net.devh:grpc-spring-boot-starter:3.1.0.RELEASE")
    implementation("io.sentry:sentry-spring-boot-starter")
    implementation("org.projectlombok:lombok")

    implementation("com.multiplier.platform:spring-starter") {
        exclude(module = "access-control-spring", group = "com.multiplier")
    }
    implementation("com.multiplier:access-control-spring")
    implementation("com.multiplier.transaction:transaction-database-jpa-spring")
    implementation("com.multiplier:contract-service-schema")
    implementation("com.multiplier:core-service-schema")
    implementation("com.multiplier:member-service-schema")
    implementation("com.multiplier:country-service-schema")
    implementation("com.multiplier:company-service-schema")
    implementation("com.multiplier:contract-onboarding-service-graph")
    implementation("com.multiplier:pigeon-service-schema")
    implementation("com.multiplier:pigeon-service-client")
    implementation("com.multiplier:pay-se-schema")
    implementation("com.multiplier:payroll-service-schema")
    implementation("com.multiplier:org-management-service-schema")
    implementation("com.multiplier.grpc:grpc-common")
    implementation("com.multiplier.grpc:grpc-bulkupload")
    implementation("com.multiplier:timeoff-service-schema")
    implementation("com.multiplier:compensation-service-grpc-schema")
    implementation("com.multiplier.messaging:bulk-upload-service")

    implementation("com.graphql-java:graphql-java-extended-scalars")
    implementation("com.graphql-java:java-dataloader:3.2.2")

    implementation("org.liquibase:liquibase-core")
    implementation("io.hypersistence:hypersistence-utils-hibernate-62")
    implementation("org.zalando:problem-spring-web")
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core")
    implementation("io.jsonwebtoken:jjwt-api")
    implementation("io.jsonwebtoken:jjwt-impl")
    implementation("io.jsonwebtoken:jjwt-jackson")
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json")
    implementation("org.mapstruct:mapstruct")
    implementation("me.moallemi.tools:kotlin-date-range")
    implementation("net.javacrumbs.shedlock:shedlock-core")
    implementation("net.javacrumbs.shedlock:shedlock-spring")
    implementation("net.javacrumbs.shedlock:shedlock-provider-jdbc-template")
    implementation("org.apache.httpcomponents:fluent-hc")
    implementation("com.github.ben-manes.caffeine:caffeine")
    implementation("com.github.ozlerhakan:poiji")
    implementation("com.github.daniel-shuy:kafka-protobuf-serde")
    implementation( "com.multiplier", "growthbook-sdk", "1.1.2")
    implementation("io.netty:netty-common")
    implementation("io.netty:netty-handler")
    implementation("net.minidev:json-smart")

    implementation("io.growthbook.sdk:GrowthBook")

    developmentOnly("org.springframework.boot:spring-boot-devtools")

    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        exclude(module = "junit")
        exclude(module = "mockito-core")
        exclude(group = "com.vaadin.external.google", module = "android-json")
    }
    testImplementation("io.github.serpro69:kotlin-faker")
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testImplementation("io.mockk:mockk")
    testImplementation("com.h2database:h2")
    testImplementation("org.mockito:mockito-inline")
    testImplementation("org.springframework.boot:spring-boot-starter-webflux")
    testImplementation("com.ninja-squad:springmockk")
    testImplementation("io.grpc:grpc-testing")
    testImplementation("org.testcontainers:testcontainers")
    testImplementation("org.testcontainers:junit-jupiter")
    testImplementation("org.testcontainers:postgresql")

    kapt("org.mapstruct:mapstruct-processor")

    implementation("org.json:json")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.17.1")
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310")
    implementation("com.google.protobuf:protobuf-java-util")

    annotationProcessor("org.projectlombok:lombok")
    annotationProcessor ("org.springframework.boot:spring-boot-configuration-processor")

    implementation("io.github.microutils:kotlin-logging-jvm")
    implementation("org.apache.poi:poi-ooxml")

    println("Operating System Name: ${System.getProperty("os.name")}")
    println("Operating System Architecture: ${System.getProperty("os.arch")}")

    if (
        System.getProperty("os.name").toLowerCase().contains("mac os x") &&
        listOf("aarch64", "aarch_64").contains(System.getProperty("os.arch"))
    ) {
        testRuntimeOnly("io.netty:netty-resolver-dns-native-macos:4.1.90.Final") {
            artifact {
                classifier = "osx-aarch_64"
            }
        }

    }
}


if (!project.hasProperty("runList")) {
    project.ext.set("runList", "main")
}

project.ext.set(
    "diffChangelogFile",
    "src/main/resources/liquibase/changelog/" + LocalDateTime.now()
        .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "_changelog.xml"
)

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict", "-Xjvm-default=all")
        jvmTarget = "17"
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}

tasks.withType<org.springframework.boot.gradle.tasks.run.BootRun> {
    val debuggerPort = System.getProperty("DEBUGGER_PORT")?.toInt() ?: 5005

    jvmArgs = listOf(
        "-Xdebug",
        "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=0.0.0.0:${debuggerPort}"
    )
}

tasks.test {
    jvmArgs(
        "--add-opens", "java.base/java.time=ALL-UNNAMED",
        "--add-opens", "java.base/java.lang=ALL-UNNAMED",
        "--add-opens", "java.base/java.lang.reflect=ALL-UNNAMED"
    )
}

tasks.getByName("jar") {
    enabled = false
}

tasks.jacocoTestReport {
    reports {
        html.required.set(true)
        xml.required.set(true)
        xml.outputLocation.set(file("${buildDir}/reports/jacoco.xml"))
    }
}

kapt {
    keepJavacAnnotationProcessors = true
    includeCompileClasspath = false
}
