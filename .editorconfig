root = true

[*]
max_line_length = 180
charset = utf-8
end_of_line = lf
indent_style = space
insert_final_newline = true

[*.{kt,kts}]
max_line_length = 120
indent_size = 4
ij_kotlin_packages_to_use_import_on_demand = unset
ktlint_code_style = ktlint_official
ktlint_function_signature_body_expression_wrapping = default
ktlint_ignore_back_ticked_identifier = true
ktlint_standard_chain-method-continuation = disabled # disabled due to count setting not working
ktlint_standard_multiline-expression-wrapping = disabled
ktlint_standard_string-template-indent = disabled # depends on multiline-expression-wrapping

[*.kts]
max_line_length = 180
ktlint_standard_max-line-length = disabled
ktlint_standard_argument-list-wrapping = disabled
ktlint_standard_chain-method-continuation = disabled

[**/build/**/*.kt]
ktlint = disabled
