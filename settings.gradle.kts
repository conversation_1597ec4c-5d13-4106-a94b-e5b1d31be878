buildCache {
    local {
        isEnabled = true
        isPush = true
    }
}

rootProject.name = "contract-onboarding-service"

include("app")
include("schema")

pluginManagement {
    repositories {
        gradlePluginPortal()
        mavenCentral()
    }
}
plugins {
    id("io.github.florian-besser-mult.plugin-resolution") version "0.1.58"
}

dependencyResolutionManagement {
    versionCatalogs {
        create("multiplier") {
            version("plugins", "0.1.58")

            plugin("publish-with-summary", "com.multiplier.plugin.publish-with-summary").versionRef("plugins")
            plugin("fixture-generator", "com.multiplier.plugin.fixture-generator").versionRef("plugins")
            plugin("lib-resolution", "com.multiplier.plugin.lib-resolution").versionRef("plugins")
        }
    }
}
