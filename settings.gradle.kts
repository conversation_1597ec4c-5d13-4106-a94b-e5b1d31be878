rootProject.name = "graph-registry"

enableFeaturePreview("TYPESAFE_PROJECT_ACCESSORS")

// Keep these sorted alphabetically
include(
    "subgraph",
    "subgraph:analytics-service",
    "subgraph:approval-service",
    "subgraph:bulk-upload-service",
    "subgraph:background-verification-service",
    "subgraph:common",
    "subgraph:company-service",
    "subgraph:compensation-service",
    "subgraph:contract-offboarding-service",
    "subgraph:contract-onboarding-service",
    "subgraph:offboarding-experience-service",
    "subgraph:contract-service",
    "subgraph:core-service-fed",
    "subgraph:country-service",
    "subgraph:customer-integration-service",
    "subgraph:demo-account-orchestrator-service",
    "subgraph:deposit-service",
    "subgraph:docgen-service",
    "subgraph:embedded-compliance",
    "subgraph:equipment-service",
    "subgraph:expense-service",
    "subgraph:field-mapping-service",
    "subgraph:freelancer-service",
    "subgraph:leave-compliance-service",
    "subgraph:member-experience-service",
    "subgraph:member-service",
    "subgraph:metering-service",
    "subgraph:notifications-hub-service",
    "subgraph:offboarding-compliance-service",
    "subgraph:org-management-service",
    "subgraph:partner-service",
    "subgraph:pay-se",
    "subgraph:payable-service",
    "subgraph:payment-instruction-service",
    "subgraph:payroll-connector",
    "subgraph:payroll-document-service",
    "subgraph:payroll-input-service",
    "subgraph:payroll-orchestrator",
    "subgraph:payroll-processor",
    "subgraph:payroll-payment-service",
    "subgraph:payroll-reconciliation",
    "subgraph:payroll-schema-service",
    "subgraph:payroll-service",
    "subgraph:pigeon",
    "subgraph:platform-utility",
    "subgraph:pricing-service",
    "subgraph:product-catalogue-service",
    "subgraph:recruitment-service",
    "subgraph:risk-assessment-service",
    "subgraph:salary-calculator",
    "subgraph:timeoff-service",
    "subgraph:timesheet-service",
    "subgraph:billing-service",
    "subgraph:vas-benefits-service",
    "subgraph:vas-incidental-service",
    "subgraph:wallet-orchestrator-service",
    "supergraph",
)
// Keep these sorted alphabetically

pluginManagement {
    repositories {
        gradlePluginPortal()
        mavenCentral()
    }
}
plugins {
    id("io.github.florian-besser-mult.plugin-resolution") version "0.1.58"
}

dependencyResolutionManagement {
    versionCatalogs {
        create("multiplier") {
            version("plugins", "0.1.58")

            plugin("publish-with-summary", "com.multiplier.plugin.publish-with-summary").versionRef("plugins")
            plugin("lib-resolution", "com.multiplier.plugin.lib-resolution").versionRef("plugins")
        }
        create("libs") {
            version("kotlin", "1.9.22")
            version("spring.boot", "2.7.18")
            version("dgs", "5.3.0")
            version("graphql", "19.9")
            version("extendedValidation", "19.1")

            // Plugins
            plugin("kotlin-jvm", "org.jetbrains.kotlin.jvm").versionRef("kotlin")
            plugin("dgs-codegen", "com.netflix.dgs.codegen").version("5.5.0")

            // Spring
            library("spring-boot-starter-web", "org.springframework.boot", "spring-boot-starter-web").versionRef("spring.boot")

            // GraphQL
            library("graphql-dgs", "com.netflix.graphql.dgs", "graphql-dgs").versionRef("dgs")
            library("graphql-java", "com.graphql-java", "graphql-java").versionRef("graphql")
            library("graphql-java-extended-validation", "com.graphql-java", "graphql-java-extended-validation").versionRef("extendedValidation")

            // Serialization
            library("jackson-annotations", "com.fasterxml.jackson.core", "jackson-annotations").version("2.13.4")
        }
    }
}
