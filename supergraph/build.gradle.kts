plugins {
    alias(libs.plugins.kotlin.jvm)

    alias(libs.plugins.dgs.codegen)

    alias(multiplier.plugins.publish.with.summary)
}
configure<com.multiplier.plugin.PublishWithSummaryExtension> {
    artifactId = "supergraph"
}

val graphGroup:String by rootProject.extra
val generatedResourcesPath: String by rootProject.extra
val generatedSchemaPath: String by rootProject.extra

val supergraphConfigFile = file("supergraph-config.yml")

dependencies {
    // Project
    api(projects.subgraph.common)

    // Spring
    compileOnly(libs.spring.boot.starter.web)

    // Serialization
    implementation(libs.jackson.annotations)
}

val generatePackageJson = tasks.register("generatePackageJson") {
    group = PublishingPlugin.PUBLISH_TASK_GROUP
    description = "Generates supergraph package.json"

    val versionName: String by rootProject.extra
    inputs.property("versionName", versionName)
    outputs.file("package.json")

    val packageJson = """{
    |  "name": "@multiplier/supergraph",
    |  "version": "$versionName",
    |  "description": "The supergraph definition of multiplier."
    |}
    |
    """.trimMargin() // keep the last (empty) linebreak

    doLast {
        File(projectDir, "package.json").writeText(packageJson)
    }
}

val cleanSupergraph = tasks.register<Exec>("cleanSupergraph") {
    group = graphGroup
    description = "Cleans supergraph"

    workingDir = rootProject.projectDir

    commandLine("npm", "run", "clean")
}

val generateSupergraph = tasks.register<Exec>("generateSupergraph") {
    group = graphGroup
    description = "Generates supergraph"

    inputs.file(supergraphConfigFile)

    projects.subgraph.dependencyProject.subprojects {
        dependsOn(tasks.named("mergeSubgraph"))

        layout.buildDirectory.dir(generatedSchemaPath).orNull?.also {
            inputs.dir(it)
        }
    }

    outputs.file("supergraph-api.json")
    outputs.file("supergraph-schema.graphql")

    workingDir = rootProject.projectDir

    commandLine("npm", "run", "generate-supergraph")
}

val cleanSupergraphAuth = tasks.register<Exec>("cleanSupergraphAuth") {
    group = graphGroup
    description = "Cleans supergraph"

    workingDir = rootProject.projectDir

    commandLine("npm", "run", "clean")
}

val mergeScript = file("generate-supergraph-auth.js")
val supergraphAuthFile = file("supergraph-auth.graphql")
val generateSupergraphAuth = tasks.register<Exec>("generateSupergraphAuth") {
    group = graphGroup
    description = "Generates supergraph schema with auth directive"

    inputs.file(mergeScript)
    inputs.file(supergraphConfigFile)

    projects.subgraph.dependencyProject.subprojects {
        dependsOn(tasks.named("mergeSubgraph"))

        layout.buildDirectory.dir(generatedSchemaPath).orNull?.also {
            inputs.dir(it)
        }
    }

    outputs.file(supergraphAuthFile)

    workingDir = rootProject.projectDir

    commandLine("npm", "run", "generate-supergraph-auth")
}

val copySupergraphAuth = tasks.register<Copy>("copySupergraphAuth") {
    group = graphGroup
    description = "Copies supergraph schema with auth directive to generated schema path"

    dependsOn(generateSupergraphAuth)

    from(supergraphAuthFile)
    into(layout.buildDirectory.dir(generatedSchemaPath))
}

tasks.named("clean") {
    dependsOn(cleanSupergraph)
    dependsOn(cleanSupergraphAuth)

    doLast {
        delete(file("package.json"))
    }
}

tasks.withType<ProcessResources> {
    dependsOn(generateSupergraphAuth)
    dependsOn(copySupergraphAuth)

    from(layout.buildDirectory.dir(generatedResourcesPath))
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    compilerOptions { jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17 }
}

plugins.withType<JavaPlugin> {
    configure<JavaPluginExtension> {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
}

tasks.withType<com.netflix.graphql.dgs.codegen.gradle.GenerateJavaTask> {
    dependsOn(generateSupergraphAuth)
    dependsOn(copySupergraphAuth)

    schemaPaths = mutableListOf(layout.buildDirectory.dir(generatedSchemaPath).get())
    packageName = "com.multiplier.graph"
    generateClient = false
    typeMapping += mapOf(
        "ID" to "kotlin.Long",
        "UUID" to "java.util.UUID",
        "Upload" to "org.springframework.web.multipart.MultipartFile",
        "Date" to "java.time.LocalDate",
        "Time" to "java.time.LocalTime",
        "DateTime" to "java.time.LocalDateTime",
        "JSON" to "kotlin.collections.Map<*, *>",
        "Object" to "Any",
    )
}

val publishNpm = tasks.register<Exec>("publishNpm") {
    group = PublishingPlugin.PUBLISH_TASK_GROUP
    description = "Publishes NPM module"

    dependsOn(generatePackageJson)
    dependsOn(generateSupergraph)
    dependsOn(generateSupergraphAuth)
    dependsOn(copySupergraphAuth)

    val versionName: String by rootProject.extra

    // Skip the task if versionName ends with SNAPSHOT
    onlyIf {
        val isSnapshot = versionName.endsWith("-SNAPSHOT")
        if (isSnapshot) {
            logger.info("Skipping publishNpm task because version $versionName is a SNAPSHOT version")
        }
        !isSnapshot
    }

    workingDir = rootProject.projectDir

    commandLine("npm", "publish", "$projectDir")
}

tasks.named(PublishingPlugin.PUBLISH_LIFECYCLE_TASK_NAME) {
    dependsOn(publishNpm)
}
