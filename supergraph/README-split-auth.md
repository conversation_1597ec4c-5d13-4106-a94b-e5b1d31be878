# GraphQL Subgraph Splitting for Authorization

This directory contains scripts to extract and split GraphQL subgraphs into individual query/mutation files with their related types and authorization directives.

## Overview

The splitting process works in two phases:

1. **Extract Subgraphs**: Generates complete subgraph schemas from individual services
2. **Split Operations**: Creates individual files for each query/mutation with all related types

## Generated Files

All generated files are saved to: `build/generated/resources/split-auth/`

### File Naming Convention

Files are named using the pattern:
```
{subgraph-name}_{operation-type}_{operation-name}-split-auth.graphql
```

Examples:
- `analytics_query_getAvailableReports-split-auth.graphql`
- `analytics_mutation_test-split-auth.graphql`
- `payroll-schema-service_query_componentCategories-split-auth.graphql`

### File Contents

Each generated file contains:
- A single Query or Mutation type with one operation
- All related types used by that operation (recursively collected)
- Authorization directives (`@authorize`) preserved from the original schema
- Input types, enums, scalars, and object types as needed

## Usage

### Option 1: NPM Script (Recommended)
```bash
npm run generate-split-auth
```

### Option 2: Direct Script Execution
```bash
# Run both steps
node supergraph/generate-split-auth.js

# Or run individually
node supergraph/extract-subgraphs.js
node supergraph/split-subgraphs-auth.js
```

## Example Output

For a query like:
```graphql
type Query {
  getAvailableReports(input: ReportID): [ReportConfig]! @authorize(member: ["view.member.reports"])
}
```

The generated file will include:
- The Query type with the single operation
- ReportConfig type definition
- ReportID input type definition  
- All nested types used by ReportConfig (Filter, FilterTypes enum, etc.)
- All authorization directives preserved

## Requirements

- Node.js 18+
- All dependencies installed (`npm install`)
- Subgraph schemas must be available or generatable

## Troubleshooting

If you encounter issues:

1. **Missing schemas**: The script will attempt to generate missing schemas automatically
2. **No operations found**: Some subgraphs may only contain type definitions without queries/mutations
3. **Build errors**: Ensure all dependencies are installed and Node.js version is compatible

## Technical Details

### Type Collection Algorithm

The script uses recursive type collection to ensure all related types are included:
- Follows field types and argument types
- Handles nested object types, interfaces, and unions
- Includes enum values and input types
- Prevents infinite recursion with depth limiting
- Skips built-in GraphQL scalar types

### Authorization Preservation

All `@authorize` directives are preserved with their original arguments:
- Member permissions: `@authorize(member: ["permission"])`
- Company permissions: `@authorize(company: ["permission"])`
- Operations permissions: `@authorize(operations: ["permission"])`

This ensures that authorization rules are maintained in the split files for downstream processing.
