const fs = require('node:fs');
const path = require('node:path');
const yaml = require('js-yaml');
const { loadFilesSync } = require('@graphql-tools/load-files');
const { mergeTypeDefs } = require('@graphql-tools/merge');
const { print } = require('graphql');

console.log("Extracting individual subgraph schemas...");

process.chdir(__dirname);

const configPath = 'supergraph-config.yml';
console.log(`Loading config ${process.cwd()}/${configPath}`);
if (!fs.existsSync(configPath)) {
    console.error(`Config file not found: ${configPath}`);
    process.exit(1);
}
const config = yaml.load(fs.readFileSync(configPath, 'utf8'));

// Create output directory
const outputDir = 'build/generated/resources/extracted-subgraphs';
if (fs.existsSync(outputDir)) {
    fs.rmSync(outputDir, { recursive: true });
}
fs.mkdirSync(outputDir, { recursive: true });

console.log('Loading common schema files');
const commonSchemaFiles = loadFilesSync(path.resolve('../subgraph/common/service-schema/**/*.graphql'));

console.log('Processing each subgraph...');
Object.entries(config.subgraphs).forEach(([name, subgraph]) => {
    if (!subgraph?.schema?.file) {
        console.warn(`Skipping subgraph "${name}" - No schema file defined`);
        return;
    }

    console.log(`Processing subgraph: ${name}`);

    try {
        // Check if the schema file exists
        const schemaFilePath = path.resolve(subgraph.schema.file);
        if (!fs.existsSync(schemaFilePath)) {
            console.warn(`Schema file not found for ${name}: ${schemaFilePath}`);
            console.log(`Attempting to generate schema for ${name}...`);

            // Try to generate the schema by running the merge script
            const serviceDir = path.dirname(path.dirname(path.dirname(schemaFilePath)));
            const mergeScriptPath = path.resolve('../subgraph/merge-subgraph.js');

            if (fs.existsSync(serviceDir) && fs.existsSync(mergeScriptPath)) {
                const { execSync } = require('child_process');
                try {
                    execSync(`cd "${serviceDir}" && node "${mergeScriptPath}"`, { stdio: 'inherit' });
                    console.log(`Generated schema for ${name}`);
                } catch (error) {
                    console.error(`Failed to generate schema for ${name}:`, error.message);
                    return;
                }
            } else {
                console.warn(`Cannot generate schema for ${name} - service directory or merge script not found`);
                return;
            }
        }

        // Load the individual subgraph schema
        const subgraphSchema = loadFilesSync(schemaFilePath);

        // Merge with common schema
        const fullSubgraphSchema = mergeTypeDefs([...subgraphSchema, ...commonSchemaFiles]);

        // Write to individual file
        const outputPath = path.join(outputDir, `${name}.graphql`);
        fs.writeFileSync(outputPath, print(fullSubgraphSchema));

        console.log(`✓ Extracted ${name} schema to ${outputPath}`);

    } catch (error) {
        console.error(`Error processing subgraph ${name}:`, error.message);
    }
});

console.log(`\nCompleted! Individual subgraph schemas saved to ${outputDir}/`);
console.log(`Each file contains the complete schema for that subgraph including common types and directives.`);
