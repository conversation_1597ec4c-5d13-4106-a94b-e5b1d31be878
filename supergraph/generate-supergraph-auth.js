const fs = require('node:fs');
const path = require('node:path');
const yaml = require('js-yaml');
const { loadFilesSync } = require('@graphql-tools/load-files');
const { mergeTypeDefs } = require('@graphql-tools/merge');
const { print } = require('graphql');

console.log("Generating supergraph schema with auth directive...");

const supergraphAuthFile = 'supergraph-auth.graphql';

process.chdir(__dirname)

const configPath = 'supergraph-config.yml';
console.log(`Loading config ${process.cwd()}/${configPath}`);
if (!fs.existsSync(configPath)) {
    console.error(`Config file not found: ${configPath}`);
    process.exit(1);
}
const config = yaml.load(fs.readFileSync(configPath, 'utf8'));

console.log('Loading subgraph schemas');
const subgraphSchemas = Object.entries(config.subgraphs).flatMap(([name, subgraph]) => {
    if (!subgraph?.schema?.file) {
        console.warn(`Skipping subgraph "${name}" - No schema file defined`);
        return [];
    }

    return loadFilesSync(path.resolve(subgraph.schema.file));
});

const commonSchemaFiles = loadFilesSync(path.resolve('../subgraph/common/service-schema/**/*.graphql'));

console.log('Merging schema');
const mergedSchema = mergeTypeDefs([...subgraphSchemas, ...commonSchemaFiles]);

console.log(`Writing ${supergraphAuthFile} to ${path.resolve(supergraphAuthFile)}`);
fs.writeFileSync(supergraphAuthFile, print(mergedSchema));

console.log('Successfully merged supergraph schema with auth directive');
