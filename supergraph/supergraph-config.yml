subgraphs:
  # Keep these sorted alphabetically
  analytics:
    routing_url: MULTIPLIER_ANALYTICS_SERVICE_URL
    schema:
      file: ../subgraph/analytics-service/build/generated/resources/schema/subgraph.graphql
  approval:
    routing_url: MULTIPLIER_APPROVAL_SERVICE_URL
    schema:
      file: ../subgraph/approval-service/build/generated/resources/schema/subgraph.graphql
  background-verification:
    routing_url: MULTIPLIER_BGV_SERVICE_URL
    schema:
      file: ../subgraph/background-verification-service/build/generated/resources/schema/subgraph.graphql
  billing:
    routing_url: MULTIPLIER_BILLING_SERVICE_URL
    schema:
      file: ../subgraph/billing-service/build/generated/resources/schema/subgraph.graphql
  bulkupload:
    routing_url: MULTIPLIER_BULK_UPLOAD_SERVICE_URL
    schema:
      file: ../subgraph/bulk-upload-service/build/generated/resources/schema/subgraph.graphql
  company:
    routing_url: MULTIPLIER_COMPANY_SERVICE_URL
    schema:
      file: ../subgraph/company-service/build/generated/resources/schema/subgraph.graphql
  compensation:
    routing_url: MULTIPLIER_COMPENSATION_SERVICE_URL
    schema:
      file: ../subgraph/compensation-service/build/generated/resources/schema/subgraph.graphql
  offboarding-experience:
    routing_url: MULTIPLIER_OFFBOARDING_EXPERIENCE_SERVICE_URL
    schema:
      file: ../subgraph/offboarding-experience-service/build/generated/resources/schema/subgraph.graphql
  compliance-core:
    routing_url: MULTIPLIER_COMPLIANCE_CORE_SERVICE_URL
    schema:
      file: ../subgraph/compliance-core/build/generated/resources/schema/subgraph.graphql
  contract-offboarding:
    routing_url: MULTIPLIER_CONTRACT_OFFBOARDING_SERVICE_URL
    schema:
      file: ../subgraph/contract-offboarding-service/build/generated/resources/schema/subgraph.graphql
  contract-onboarding:
    routing_url: MULTIPLIER_CONTRACT_ONBOARDING_SERVICE_URL
    schema:
      file: ../subgraph/contract-onboarding-service/build/generated/resources/schema/subgraph.graphql
  contract:
    routing_url: MULTIPLIER_CONTRACT_URL
    schema:
      file: ../subgraph/contract-service/build/generated/resources/schema/subgraph.graphql
  core:
    routing_url: MULTIPLIER_CORE_SERVICE_URL
    schema:
      file: ../subgraph/core-service-fed/build/generated/resources/schema/subgraph.graphql
  country:
    routing_url: MULTIPLIER_COUNTRY_URL
    schema:
      file: ../subgraph/country-service/build/generated/resources/schema/subgraph.graphql
  customer-integration:
    routing_url: MULTIPLIER_CUSTOMER_INTEGRATION_URL
    schema:
      file: ../subgraph/customer-integration-service/build/generated/resources/schema/subgraph.graphql
  demo-account-orchestrator:
    routing_url: MULTIPLIER_DEMO_ACCOUNT_ORCHESTRATOR_SERVICE_URL
    schema:
      file: ../subgraph/demo-account-orchestrator-service/build/generated/resources/schema/subgraph.graphql
  deposit:
    routing_url: MULTIPLIER_DEPOSIT_SERVICE_URL
    schema:
      file: ../subgraph/deposit-service/build/generated/resources/schema/subgraph.graphql
  docgen:
    routing_url: MULTIPLIER_DOCGEN_URL
    schema:
      file: ../subgraph/docgen-service/build/generated/resources/schema/subgraph.graphql
  equipment:
    routing_url: MULTIPLIER_EQUIPMENT_URL
    schema:
      file: ../subgraph/equipment-service/build/generated/resources/schema/subgraph.graphql
  expense:
    routing_url: MULTIPLIER_EXPENSE_URL
    schema:
      file: ../subgraph/expense-service/build/generated/resources/schema/subgraph.graphql
  field-mapping:
    routing_url: MULTIPLIER_FIELD_MAPPING_SERVICE_URL
    schema:
      file: ../subgraph/field-mapping-service/build/generated/resources/schema/subgraph.graphql
  freelancer:
    routing_url: MULTIPLIER_FREELANCER_URL
    schema:
      file: ../subgraph/freelancer-service/build/generated/resources/schema/subgraph.graphql
  leave-compliance:
    routing_url: MULTIPLIER_LEAVE_COMPLIANCE_URL
    schema:
      file: ../subgraph/leave-compliance-service/build/generated/resources/schema/subgraph.graphql
  member:
    routing_url: MULTIPLIER_MEMBER_SERVICE_URL
    schema:
      file: ../subgraph/member-service/build/generated/resources/schema/subgraph.graphql
  member-experience:
    routing_url: MULTIPLIER_MEMBER_EXPERIENCE_SERVICE_URL
    schema:
      file: ../subgraph/member-experience-service/build/generated/resources/schema/subgraph.graphql
  metering:
    routing_url: MULTIPLIER_METERING_SERVICE_URL
    schema:
      file: ../subgraph/metering-service/build/generated/resources/schema/subgraph.graphql
  notifications-hub:
    routing_url: MULTIPLIER_NOTIFICATIONS_HUB_SERVICE_URL
    schema:
      file: ../subgraph/notifications-hub-service/build/generated/resources/schema/subgraph.graphql
  offboarding-compliance:
    routing_url: MULTIPLIER_OFFBOARDING_COMPLIANCE_SERVICE_URL
    schema:
      file: ../subgraph/offboarding-compliance-service/build/generated/resources/schema/subgraph.graphql
  org-management:
    routing_url: MULTIPLIER_ORG_MANAGEMENT_SERVICE_URL
    schema:
      file: ../subgraph/org-management-service/build/generated/resources/schema/subgraph.graphql
  partner:
    routing_url: MULTIPLIER_PARTNER_SERVICE_URL
    schema:
      file: ../subgraph/partner-service/build/generated/resources/schema/subgraph.graphql
  payable:
    routing_url: MULTIPLIER_PAYABLE_SERVICE_URL
    schema:
      file: ../subgraph/payable-service/build/generated/resources/schema/subgraph.graphql
  payment-instruction:
    routing_url: MULTIPLIER_PAYMENT_INSTRUCTION_SERVICE_URL
    schema:
      file: ../subgraph/payment-instruction-service/build/generated/resources/schema/subgraph.graphql
  payment:
    routing_url: MULTIPLIER_PAY_SE_URL
    schema:
      file: ../subgraph/pay-se/build/generated/resources/schema/subgraph.graphql
  payroll-connector:
    routing_url: MULTIPLIER_PAYROLL_CONNECTOR_URL
    schema:
      file: ../subgraph/payroll-connector/build/generated/resources/schema/subgraph.graphql
  payroll-document:
    routing_url: MULTIPLIER_PAYROLL_DOCUMENT_URL
    schema:
      file: ../subgraph/payroll-document-service/build/generated/resources/schema/subgraph.graphql
  payroll-input:
    routing_url: MULTIPLIER_PAYROLL_INPUT_URL
    schema:
      file: ../subgraph/payroll-input-service/build/generated/resources/schema/subgraph.graphql
  payroll-orchestrator:
    routing_url: MULTIPLIER_PAYROLL_ORCHESTRATOR_URL
    schema:
      file: ../subgraph/payroll-orchestrator/build/generated/resources/schema/subgraph.graphql
  payroll-processor:
    routing_url: MULTIPLIER_PAYROLL_PROCESSOR_URL
    schema:
      file: ../subgraph/payroll-processor/build/generated/resources/schema/subgraph.graphql
  payroll-payment:
    routing_url: MULTIPLIER_PAYROLL_PAYMENT_URL
    schema:
      file: ../subgraph/payroll-payment-service/build/generated/resources/schema/subgraph.graphql
  payroll-reconciliation:
    routing_url: MULTIPLIER_PAYROLL_RECONCILIATION_URL
    schema:
      file: ../subgraph/payroll-reconciliation/build/generated/resources/schema/subgraph.graphql
  payroll-schema-service:
    routing_url: MULTIPLIER_PAYROLL_SCHEMA_SERVICE_URL
    schema:
      file: ../subgraph/payroll-schema-service/build/generated/resources/schema/subgraph.graphql
  payroll:
    routing_url: MULTIPLIER_PAYROLL_URL
    schema:
      file: ../subgraph/payroll-service/build/generated/resources/schema/subgraph.graphql
  pigeon:
    routing_url: MULTIPLIER_PIGEON_URL
    schema:
      file: ../subgraph/pigeon/build/generated/resources/schema/subgraph.graphql
  platform-utility:
    routing_url: MULTIPLIER_PLATFORM_UTILITY_URL
    schema:
      file: ../subgraph/platform-utility/build/generated/resources/schema/subgraph.graphql
  pricing:
    routing_url: MULTIPLIER_PRICING_SERVICE_URL
    schema:
      file: ../subgraph/pricing-service/build/generated/resources/schema/subgraph.graphql
  product-catalogue:
    routing_url: MULTIPLIER_PRODUCT_CATALOGUE_SERVICE_URL
    schema:
      file: ../subgraph/product-catalogue-service/build/generated/resources/schema/subgraph.graphql
  recruitment:
    routing_url: MULTIPLIER_RECRUITMENT_URL
    schema:
      file: ../subgraph/recruitment-service/build/generated/resources/schema/subgraph.graphql
  risk-assessment:
    routing_url: MULTIPLIER_RISK_ASSESSMENT_SERVICE_URL
    schema:
      file: ../subgraph/risk-assessment-service/build/generated/resources/schema/subgraph.graphql
  salary-calculator:
    routing_url: MULTIPLIER_SALARY_CALCULATOR_URL
    schema:
      file: ../subgraph/salary-calculator/build/generated/resources/schema/subgraph.graphql
  timeoff:
    routing_url: MULTIPLIER_TIMEOFF_URL
    schema:
      file: ../subgraph/timeoff-service/build/generated/resources/schema/subgraph.graphql
  timesheet:
    routing_url: MULTIPLIER_TIMESHEET_SERVICE_URL
    schema:
      file: ../subgraph/timesheet-service/build/generated/resources/schema/subgraph.graphql
  vas-benefits:
    routing_url: MULTIPLIER_VAS_BENEFITS_SERVICE_URL
    schema:
      file: ../subgraph/vas-benefits-service/build/generated/resources/schema/subgraph.graphql
  vas-incidental:
    routing_url: MULTIPLIER_VAS_INCIDENTAL_SERVICE_URL
    schema:
      file: ../subgraph/vas-incidental-service/build/generated/resources/schema/subgraph.graphql
  wallet-orchestrator-service:
    routing_url: MULTIPLIER_WALLET_ORCHESTRATOR_SERVICE_URL
    schema:
      file: ../subgraph/wallet-orchestrator-service/build/generated/resources/schema/subgraph.graphql

  # Keep these sorted alphabetically
