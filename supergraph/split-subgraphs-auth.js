const fs = require('node:fs');
const path = require('node:path');
const yaml = require('js-yaml');
const { loadFilesSync } = require('@graphql-tools/load-files');
const { mergeTypeDefs } = require('@graphql-tools/merge');
const { buildASTSchema, printType, print } = require('graphql');
const { pruneSchema } = require('@graphql-tools/utils');

console.log("Splitting subgraphs into individual query/mutation files with auth...");

process.chdir(__dirname);

const configPath = 'supergraph-config.yml';
console.log(`Loading config ${process.cwd()}/${configPath}`);
if (!fs.existsSync(configPath)) {
    console.error(`Config file not found: ${configPath}`);
    process.exit(1);
}
const config = yaml.load(fs.readFileSync(configPath, 'utf8'));

const extractedSubgraphsDir = 'build/generated/resources/extracted-subgraphs';
if (!fs.existsSync(extractedSubgraphsDir)) {
    console.error(`Extracted subgraphs directory not found: ${extractedSubgraphsDir}`);
    console.error('Please run extract-subgraphs.js first');
    process.exit(1);
}

// Create centralized output directory
const outputDir = 'build/generated/resources/split-auth';
if (fs.existsSync(outputDir)) {
    fs.rmSync(outputDir, { recursive: true });
}
fs.mkdirSync(outputDir, { recursive: true });
console.log(`Output directory created: ${outputDir}`);

// Function to collect related types recursively
function collectRelatedTypes(typeName, typeMap, collected = new Set(), depth = 0) {
    if (!typeName || collected.has(typeName) || depth >= 10) return;

    // Skip built-in GraphQL types
    if (typeName.startsWith('__') || ['String', 'Int', 'Float', 'Boolean', 'ID'].includes(typeName)) {
        return;
    }

    collected.add(typeName);
    const type = typeMap[typeName];
    if (!type) return;

    // Handle different type kinds
    if ('getFields' in type) {
        // Object or Interface type
        Object.values(type.getFields()).forEach(field => {
            const cleanType = field.type.toString().replace(/[[\]!]/g, '');
            collectRelatedTypes(cleanType, typeMap, collected, depth + 1);

            // Also collect argument types
            if (field.args) {
                field.args.forEach(arg => {
                    const argType = arg.type.toString().replace(/[[\]!]/g, '');
                    collectRelatedTypes(argType, typeMap, collected, depth + 1);
                });
            }
        });
    } else if ('getValues' in type) {
        // Enum type - no further recursion needed
    } else if ('ofType' in type) {
        // List or NonNull wrapper
        const wrappedType = type.ofType ? type.ofType.toString().replace(/[[\]!]/g, '') : null;
        if (wrappedType) {
            collectRelatedTypes(wrappedType, typeMap, collected, depth + 1);
        }
    }
}

// Function to generate schema chunks for each field
function generateSchemaChunks(subgraphName, fields, operationType, typeMap, outputDir) {
    // Ensure the subgraph directory exists
    const subgraphDir = path.join(outputDir, subgraphName);
    if (!fs.existsSync(subgraphDir)) {
        fs.mkdirSync(subgraphDir, { recursive: true });
    }

    Object.entries(fields).forEach(([fieldName, field]) => {
        console.log(`  Processing ${operationType.toLowerCase()}: ${fieldName}`);

        const relatedTypes = new Set();
        const rootType = field.type.toString().replace(/[[\]!]/g, '');
        collectRelatedTypes(rootType, typeMap, relatedTypes, 0);

        // Also collect types from field arguments
        if (field.args) {
            field.args.forEach(arg => {
                const argType = arg.type.toString().replace(/[[\]!]/g, '');
                collectRelatedTypes(argType, typeMap, relatedTypes, 0);
            });
        }

        // Build the schema string
        let schemaString = `type ${operationType} {\n  ${fieldName}`;

        // Add arguments if they exist
        if (field.args && field.args.length > 0) {
            const argsString = field.args.map(arg => `${arg.name}: ${arg.type}`).join(', ');
            schemaString += `(${argsString})`;
        }

        schemaString += `: ${field.type}`;

        // Add directives if they exist
        if (field.astNode && field.astNode.directives && field.astNode.directives.length > 0) {
            field.astNode.directives.forEach(directive => {
                schemaString += ` @${directive.name.value}`;
                if (directive.arguments && directive.arguments.length > 0) {
                    const directiveArgs = directive.arguments.map(arg => {
                        let value = '';
                        if (arg.value.kind === 'ListValue') {
                            const listValues = arg.value.values.map(v => `"${v.value}"`).join(', ');
                            value = `[${listValues}]`;
                        } else if (arg.value.kind === 'StringValue') {
                            value = `"${arg.value.value}"`;
                        } else {
                            value = arg.value.value;
                        }
                        return `${arg.name.value}: ${value}`;
                    }).join(', ');
                    schemaString += `(${directiveArgs})`;
                }
            });
        }

        schemaString += '\n}';

        // Add all related types
        relatedTypes.forEach(typeName => {
            if (typeMap[typeName]) {
                schemaString += '\n\n' + printType(typeMap[typeName]);
            }
        });

        // Create filename and write file
        const filename = `${subgraphName}/${operationType.toLowerCase()}_${fieldName}.graphql`;
        const outputPath = path.join(outputDir, filename);
        fs.writeFileSync(outputPath, schemaString);

        console.log(`    ✓ Generated ${filename}`);
    });
}

// Process each subgraph using extracted schemas
console.log('Processing each subgraph...');
const extractedFiles = fs.readdirSync(extractedSubgraphsDir).filter(file => file.endsWith('.graphql'));

extractedFiles.forEach(file => {
    const subgraphName = path.basename(file, '.graphql');
    console.log(`\nProcessing subgraph: ${subgraphName}`);

    try {
        // Read the extracted subgraph schema
        const schemaPath = path.join(extractedSubgraphsDir, file);
        const schemaContent = fs.readFileSync(schemaPath, 'utf8');

        // Build and prune the schema
        const schema = buildASTSchema(mergeTypeDefs([schemaContent]), { assumeValid: true });
        const prunedSchema = pruneSchema(schema);

        const typeMap = prunedSchema.getTypeMap();
        const queryType = prunedSchema.getQueryType();
        const mutationType = prunedSchema.getMutationType();

        const queryFields = queryType ? queryType.getFields() : {};
        const mutationFields = mutationType ? mutationType.getFields() : {};

        // Generate schema chunks for queries and mutations
        if (Object.keys(queryFields).length > 0) {
            console.log(`  Found ${Object.keys(queryFields).length} queries`);
            generateSchemaChunks(subgraphName, queryFields, 'Query', typeMap, outputDir);
        }

        if (Object.keys(mutationFields).length > 0) {
            console.log(`  Found ${Object.keys(mutationFields).length} mutations`);
            generateSchemaChunks(subgraphName, mutationFields, 'Mutation', typeMap, outputDir);
        }

        if (Object.keys(queryFields).length === 0 && Object.keys(mutationFields).length === 0) {
            console.log(`  No queries or mutations found in ${subgraphName}`);
        }

        console.log(`✓ Completed processing ${subgraphName}`);

    } catch (error) {
        console.error(`Error processing subgraph ${subgraphName}:`, error.message);
        console.error(`  Schema file: ${path.join(extractedSubgraphsDir, file)}`);
    }
});

console.log('\n🎉 Completed splitting all subgraphs!');
console.log(`Generated files are saved in: ${outputDir}`);
console.log('Each file contains a single query/mutation with all its related types and authorization directives.');
