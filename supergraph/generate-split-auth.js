#!/usr/bin/env node

const { execSync } = require('child_process');

console.log('🚀 Starting GraphQL subgraph splitting process...\n');

try {
    // Change to the supergraph directory
    process.chdir(__dirname);

    console.log('Step 1: Extracting individual subgraph schemas...');
    execSync('node extract-subgraphs.js', { stdio: 'inherit' });

    console.log('\nStep 2: Splitting subgraphs into individual query/mutation files...');
    execSync('node split-subgraphs-auth.js', { stdio: 'inherit' });

    console.log('\n✅ Process completed successfully!');
    console.log('📁 Generated files are available in: build/generated/resources/split-auth/');

} catch (error) {
    console.error('\n❌ Process failed:', error.message);
    process.exit(1);
}
