#!/bin/bash

# Define the relative hooks path
HOOKS_PATH=".github/.git-hooks"

# Exit error if .github/.git-hooks/pre-commit does not exists
if [ ! -f "$HOOKS_PATH/pre-commit" ]; then
  echo "Error: $HOOKS_PATH/pre-commit missing. Please create it."
  exit 1
fi

# Check if hooks are already set up correctly
CURRENT_HOOKS_PATH=$(git config --get core.hooksPath)
if [ "$CURRENT_HOOKS_PATH" = "$HOOKS_PATH" ]; then
  echo "Git hooks are already set up correctly"
  exit 0
fi

# Set up the hooks
git config core.hooksPath "$HOOKS_PATH"
echo "Git hooks configured to use repository hooks in $HOOKS_PATH"