#!/bin/bash

format_code() {
  ./gradlew spotlessApply
}

check_code() {
  ./gradlew compileTestKotlin spotlessCheck --stacktrace
}

UNSTAGED_FILES=$(git diff --name-only)

stash_files() {
  if [ -n "$UNSTAGED_FILES" ]; then
    (git commit --allow-empty-message --no-verify && git stash) > /dev/null 2>&1
  fi
}

pop_stash() {
  if [ -n "$UNSTAGED_FILES" ]; then
    (git stash pop && git reset --soft HEAD~1) > /dev/null 2>&1
  fi
}

# Check if there are any staged changes in code files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACMR | grep -E "\.(kt|java)$")

linter_check() {
  format_code > /dev/null 2>&1
  git add $STAGED_FILES

  cmdout=$(check_code 2>&1)
  if [ $? -ne 0 ]; then
    echo -e "======Pre-commit checks failed. Please fix the issues before committing.======\n\n\n"
    echo "$cmdout"
    echo -e "\n\n\n======Pre-commit checks failed. Please fix the issues before committing.======"
    exit 1
  fi
}

# Only run the checks if there are changes in code files
if [ -n "$STAGED_FILES" ]; then
  echo "Changes detected in code files. Running pre-commit checks..."

  stash_files
  (linter_check 2>&1)
  result=$?
  pop_stash

  if [ "$result" -ne "0" ]; then
    exit 1
  else
    exit 0
  fi
fi