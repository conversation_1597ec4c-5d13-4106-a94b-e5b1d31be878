name: PR build and Sonar analysis
on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]
jobs:
  aws:
    uses: ./.github/workflows/aws.yml
    secrets: inherit

  build:
    name: Build
    runs-on: ["self-hosted","mgt-stg-tech-runner-ec2"]
    needs: [ aws ]
    env:
       CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
       AWS_ACCOUNT_ID: ${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: false
          gradle-home-cache-cleanup: true

      - name: Prepare # This will cache all (including runtime) dependencies
        run: ./gradlew downloadDependencies --build-cache --info

      - name: Build
        run: ./gradlew testClasses build -x check --build-cache --info

  test:
    name: Test
    runs-on: ["self-hosted","mgt-stg-tech-runner-ec2"]
    needs: [ aws, build ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
      AWS_ACCOUNT_ID: ${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: true

      - name: Test
        run: ./gradlew test jacocoTestReport --build-cache --info -Dorg.gradle.jvmargs=-Xmx2560m

      - name: Test Report
        uses: dorny/test-reporter@v1.9.1
        if: always()
        with:
          name: JUnit Tests Report
          path: app/build/test-results/test/*.xml
          reporter: java-junit
          list-suites: 'failed'
          list-tests: 'failed'
          fail-on-error: 'false'

      - name: Cache Code Coverage Report
        uses: actions/cache@v4
        with:
          path: '**/reports/jacoco/test/jacocoTestReport.xml'
          key: coverage-${{ github.run_id }}

  sonar:
    name: Sonar
    runs-on: ["self-hosted","mgt-stg-tech-runner-ec2"]
    needs: [ aws, test ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
      AWS_ACCOUNT_ID: ${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }}
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # Needed to get PR information, if any
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - name: Cache SonarCloud packages
        uses: actions/cache@v4
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar

      - name: Cache Code Coverage Report
        uses: actions/cache@v4
        with:
          path: '**/reports/jacoco/test/jacocoTestReport.xml'
          key: coverage-${{ github.run_id }}

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          cache-read-only: true

      - name: Sonar
        run: ./gradlew sonar --build-cache --info
