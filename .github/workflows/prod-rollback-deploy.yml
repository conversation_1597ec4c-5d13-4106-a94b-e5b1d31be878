name: Deploy to Production [Rollback]
run-name: Deploy ${{ github.ref_name }} to production [Rollback]
on:
  workflow_dispatch:
    tags:
      - '**'

##
# ECR_REPOSITORY: prd-app-tech-contractonboardingservice-ecr
# container-name: contractOnboardingService
# service: contractOnboardingService

jobs:
 deploy:
    # only a tag is allowed to deploy
    if: startsWith(github.ref, 'refs/tags/')
    name: Deploy
    uses: Multiplier-Core/devops-github-shared-pipelines/.github/workflows/be-generic-deployment.yml@main
    secrets: inherit
    with:
      deploy_target: production
      service_name: contractOnboardingService
      ecr_repository: prd-app-tech-contractonboardingservice-ecr
      allow_rollback: true
