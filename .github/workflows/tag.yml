name: Tag version

on:
  push:
    branches:
      - main

run-name: Tag version

jobs:
  tag:
    name: Tag
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Create Tag
        id: create-tag
        uses: paulhatch/semantic-version@v5.4.0
        with:
          tag_prefix: ""
          major_pattern: "(MAJOR)"
          minor_pattern: "(MINOR)"
          version_format: "${major}.${minor}.${patch}+${increment}"
          bump_each_commit: false

      - name: Push Tag (${{ steps.create-tag.outputs.version_tag }})
        run: |
          git tag ${{ steps.create-tag.outputs.version_tag }}
          git push origin --tags
          echo "### Version" >> $GITHUB_STEP_SUMMARY
          echo "${{ steps.create-tag.outputs.version_tag }}" >> $GITHUB_STEP_SUMMARY
