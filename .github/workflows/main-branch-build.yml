name: Build Graph

on:
  push:
    branches:
      - main
  pull_request:
    types: [ opened, synchronize, reopened ]

jobs:
  aws:
    uses: ./.github/workflows/aws.yml
    secrets: inherit

  build:
    name: Build
    runs-on: ubuntu-latest
    needs: [ aws ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
      AWS_ACCOUNT_ID: ${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: zulu
          java-version: 17

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v3

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install Node Dependencies
        run: npm ci

      - name: Build
        run: ./gradlew build --build-cache --info -Dorg.gradle.jvmargs=-Xmx1g

      - name: Generate Supergraph
        run: ./gradlew generateSupergraph --build-cache --info

      - name: Upload Supergraph
        uses: actions/upload-artifact@v4
        with:
          name: generated-supergraph
          path: |
            supergraph/supergraph-api.json
            supergraph/supergraph-schema.graphql
            supergraph/supergraph-auth.graphql

  validate:
    if: github.ref != 'refs/heads/main'
    name: Validate backwards compatibility
    runs-on: ubuntu-latest
    needs: [ aws, build ]
    env:
      CODEARTIFACT_AUTH_TOKEN: ${{ needs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}
      AWS_ACCOUNT_ID: ${{ secrets.ACC_PRD_AWS_ACCOUNT_ID }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Download Supergraph
        uses: actions/download-artifact@v4
        with:
          name: 'generated-supergraph'
          path: supergraph

      - name: Download [main] Supergraph
        uses: dawidd6/action-download-artifact@v6
        with:
          workflow: 'main-branch-build.yml'
          branch: 'main'
          name: 'generated-supergraph'

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install Node Dependencies
        run: npm login

      - name: Install Node Dependencies
        run: npm ci

      - name: Diff
        run: npm run diff
