name: Run bulk onboarding E2E tests
on:
  workflow_dispatch:

env: 
  role_to_assume: arn:aws:iam::${{ vars.ACC_STG_AWS_ACCOUNT_ID }}:role/stg-apse1-github-workflow-iamrole
  prd_role_to_assume: arn:aws:iam::${{ vars.ACC_PROD_AWS_ACCOUNT_ID }}:role/prd-apse1-github-workflow-iamrole
  AWS_MAIN_REGION: ap-southeast-1
  ACC_PROD_AWS_ACCOUNT_ID: ${{ vars.ACC_PROD_AWS_ACCOUNT_ID }}

jobs:
  aws:
    uses: ./.github/workflows/aws.yml
    secrets: inherit

  e2e:
    name: Bulk onboarding e2e tests
    runs-on: [ "self-hosted","mgt-stg-tech-runner-ec2" ]
    needs: [ aws ]
    defaults:
      run:
        working-directory: bulk-onboarding-testsuite
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: kishaningithub/setup-python-amazon-linux@v1
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Checkout shared pipeline repo
        id: checkout-shared-pipelines
        uses: actions/checkout@v4
        with: 
          repository: Multiplier-Core/devops-github-shared-pipelines
          token: ${{ secrets.REPO_ORG_READ_TOKEN }}
          path: shared-pipelines
          ref: v2

      - name: AWS Credentials
        id: aws-credentials
        uses: ./shared-pipelines/.github/composite/aws-credentials
        with: 
          role_to_assume: ${{ env.role_to_assume }}
          prd_role_to_assume: ${{ env.prd_role_to_assume }}
          aws_region: ${{ env.AWS_MAIN_REGION }}
          codeartifact_aws_region: ${{ env.AWS_MAIN_REGION }}

      - name: Retrieve secrets from AWS Parameter Store
        run: |
          JDBC_URL=$(aws ssm get-parameter --name "/stg/app/tech/services/coreService/db/url/param" --with-decryption --query "Parameter.Value" --output text)
          DB_HOST=$(echo $JDBC_URL | sed -E 's|jdbc:postgresql://([^:/]+).*|\1|')
          DB_NAME=$(echo $JDBC_URL | sed -E 's|.*/([^/?]+).*|\1|')
          DB_USER=$(aws ssm get-parameter --name "/stg/app/tech/services/coreService/db/user/param" --with-decryption --query "Parameter.Value" --output text)
          DB_PASSWORD=$(aws ssm get-parameter --name "/stg/app/tech/services/coreService/db/user/password/param" --with-decryption --query "Parameter.Value" --output text)
          echo "DB_HOST=${DB_HOST}" >> $GITHUB_ENV
          echo "DB_NAME=${DB_NAME}" >> $GITHUB_ENV
          echo "DB_USER=${DB_USER}" >> $GITHUB_ENV
          echo "DB_PASSWORD=${DB_PASSWORD}" >> $GITHUB_ENV

      - name: Run e2e test script
        run: python db_queries.py test