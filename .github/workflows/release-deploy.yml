name: Deploy to release
run-name: Deploy to release ${{ github.ref_name }}
on:
  workflow_dispatch:
    tags:
      - '**'

##
# ECR_REPOSITORY: rel-app-tech-contractonboardingservice-ecr
# container-name: contractOnboardingService
# service: contractOnboardingService

jobs:
  deploy:
    # only a tag is allowed to deploy
    if: startsWith(github.ref, 'refs/tags/')
    name: Deploy
    uses: Multiplier-Core/devops-github-shared-pipelines/.github/workflows/be-generic-deployment.yml@main
    secrets: inherit
    with:
      deploy_target: release
      service_name: contractOnboardingService
      ecr_repository: rel-app-tech-contractonboardingservice-ecr
