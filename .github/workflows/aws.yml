name: AWS

on:
  workflow_call:
    secrets:
      ACC_PROD_AWS_ACCESS_KEY_ID:
        required: true
      ACC_PROD_AWS_SECRET_ACCESS_KEY:
        required: true
    outputs:
      CODEARTIFACT_AUTH_TOKEN:
        value: ${{ jobs.aws.outputs.CODEARTIFACT_AUTH_TOKEN }}

jobs:
  aws:
    name: AWS CodeArtifact Token
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.ACC_PROD_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.ACC_PROD_AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-southeast-1

      - name: Fetch AWS CodeArtifact Token
        run: echo "CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain multiplier-artifacts --domain-owner 778085304246 --query authorizationToken --output text)" >> $GITHUB_ENV
    outputs:
      CODEARTIFACT_AUTH_TOKEN: ${{ env.CODEARTIFACT_AUTH_TOKEN }}
