ARG RUN_BASEIMAGE=************.dkr.ecr.ap-southeast-1.amazonaws.com/mgt-global-tech-multiplierbaseimage-ecr:amazoncorretto__17-al2023__latest_v2

FROM public.ecr.aws/docker/library/gradle:8.13-jdk17 AS build
ARG CODEARTIFACT_AUTH_TOKEN
ARG GRPC_CRT
ARG GRPC_CRT_KEY
COPY --chown=gradle:gradle . /home/<USER>/src
WORKDIR /home/<USER>/src
ENV CODEARTIFACT_AUTH_TOKEN ${CODEARTIFACT_AUTH_TOKEN}
ENV CI=$CI
ENV AWS_ACCOUNT_ID=************

RUN git config --global --add safe.directory /home/<USER>/src
RUN mkdir -p /home/<USER>/src/app/src/main/resources/certificates
RUN echo "${GRPC_CRT}" >> /home/<USER>/src/app/src/main/resources/certificates/server.crt
RUN echo "${GRPC_CRT_KEY}" >> /home/<USER>/src/app/src/main/resources/certificates/server.key
RUN chown -R gradle:gradle /home/<USER>/src/app/src/main/resources
RUN gradle build --no-daemon -x test

FROM ${RUN_BASEIMAGE}
ARG GRPC_CRT
ARG CODEARTIFACT_AUTH_TOKEN
ARG ENV_VAR
ENV ENV_VAR=$ENV_VAR
ARG APM_AGENT_VERSION
ENV APM_AGENT_VERSION=${APM_AGENT_VERSION}
EXPOSE 8080 9090
WORKDIR /app

COPY --from=build /home/<USER>/src/app/build/libs/*.jar /app/application.jar
COPY ./entrypoint.sh .

RUN echo "${GRPC_CRT}" >> server.crt && \
    $JAVA_HOME/bin/keytool -import -trustcacerts -alias core-service -file server.crt -keystore $JAVA_HOME/lib/security/cacerts --storepass changeit -noprompt && \
    curl -Lo elastic-apm-agent.jar "https://search.maven.org/remotecontent?filepath=co/elastic/apm/elastic-apm-agent/${APM_AGENT_VERSION}/elastic-apm-agent-${APM_AGENT_VERSION}.jar" && \
    chmod +x entrypoint.sh
ENTRYPOINT ["./entrypoint.sh"]
