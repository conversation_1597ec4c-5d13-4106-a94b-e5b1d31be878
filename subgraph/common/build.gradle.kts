plugins {
    alias(libs.plugins.kotlin.jvm)

    id("maven-publish")
}

dependencies {
    // GraphQL
    compileOnly(libs.graphql.dgs)
    compileOnly(libs.graphql.java)
    compileOnly(libs.graphql.java.extended.validation)

    // Spring
    compileOnly(libs.spring.boot.starter.web)

    // Serialization
    implementation(libs.jackson.annotations)
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    compilerOptions {
        jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_11
    }
}

plugins.withType<JavaPlugin> {
    configure<JavaPluginExtension> {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
}
