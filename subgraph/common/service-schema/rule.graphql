type Rule {
    type: RuleType
    conditions: [Condition!]
}

type Condition {
    key: ConditionKey!
    operator: ConditionOperator!
    values: [String!]
}


enum RuleType {
    ALL
    BY_CONDITION
}

enum ConditionKey {
    ENTITY
    NAME
    DEPARTMENT
    COUNTRY
    GENDER
    # Add any new condition keys here
}

enum ConditionOperator {
    EQUALS
    NOT_EQUALS
    # Add any new operators here
}


input RuleInput {
    type: RuleType
    conditions: [ConditionInput!]
}

input ConditionInput {
    key: ConditionKey!
    operator:ConditionOperator!
    values: [String!]
}
