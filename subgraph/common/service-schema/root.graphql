type Query
type Mutation

type TaskResponse {
    success: <PERSON><PERSON><PERSON>,
    message: String
}

enum PaymentAccountType {
    PERSONAL
    BUSINESS
}


## Because TimeOffTypeDefinition is cross-referenced between core-service and timeoff-service, as a workaround these things are moved to here from core & timeoff

# todo : Move this type to timeoff.graphql
type TimeOffTypeDefinition {
    id : ID
    type: String  # timeoff_type.key ('annual', 'sick'...)
    required: Boolean # Mandatory grouping = true, Custom Leave grouping = false
    label: String
    description: String
    typeId: ID # timeoff_type.id (1, 2,...14)

    # Period for which a fixed entitlement is based on
    # Essentially a denominator for each fixed leave entitlement
    # We can consider QUARTER, MONTH, ANNUAL, CONTRACT as the basis for fixed types
    # However, more complex bases for dynamic allocation computation could exist
    basis: String

    # Multiple validation definitions for each selectable unit
    validation: [TimeOffValidation]
    # Configuration to encompass allocation config, etc..
    configuration: TimeoffConfiguration
    policyName: String
    status : TimeOffTypeDefinitionStatus

    # Since this is in root graph, no way to return entity/contract objects
    # hence temporarily returning entity names and employee count
    assignedEntityNames : [String!] @deprecated
    assignedEntityInfo : TimeOffPolicyEntityInfo @authorize(company: ["view.company.timeoff.policies"], operations: ["view.operations.timeoff.policies"])
    assignedEmployeeCount : Int @authorize(company: ["view.company.timeoff.policies"], operations: ["view.operations.timeoff.policies"])
    updatedOn : DateTime
    createdOn : DateTime
    timeOffTypeIsPaidLeave: Boolean # timeoff_type.is_paid_leave
}

type TimeOffPolicyEntityInfo {
    id : ID                 # Entity ID
    name : String           # Entity Name
    type : EntityType       # Entity Type
}

union TimeOffValidation = TimeOffFixedValidation

type TimeOffFixedValidation {
    minimum: Float
    maximum: Float
    defaultValue: Float
    unit: TimeOffUnit
    allowedContractStatuses: [ContractStatus]
    isUnlimitedLeavesAllowed: Boolean
}

enum TimeOffTypeDefinitionStatus {
    ACTIVE
    DELETED
}

type TimeoffConfiguration {
    allocation: AllocationConfig
    carryForward: CarryForwardConfig
    futureLeaveConfig: FutureLeaveConfig
}

type CarryForwardConfig {
    enabled: Boolean
    minForEligibility: CarryForwardLimit
    maxLimit: CarryForwardLimit
    expiry: TimeOffDuration
}

type FutureLeaveConfig {
    enabled: Boolean
    noOfFutureYearsAllowed : Int
    lapsableLeaveConfig : LapsableLeaveConfig
}

type LapsableLeaveConfig {
    enabled: Boolean
    expiry: TimeOffDuration
}

type AllocationConfig {
    basis: AllocationBasis
    prorated: Boolean
}

type CarryForwardLimit {
    type: LimitValueType
    value: Float
    unit: TimeOffUnit
}

type TimeOffDuration {
    value: Float
    unit: TimeOffUnit
}

type TimeOffRequirements {
    # Example contract clause to be shown to consumer
    # Potentially parameterised using {{ params }}
    clause: String
    definitions: [TimeOffTypeDefinition]
}

## END workaround for TimeOffTypeDefinition

## workaround for `updateAndFixTimeOffs` mutation

# Domain
interface ComplianceParam {
    key: String
    label: String
}

# Domain
# Provides a description and current value of the compliance param attached to the contract graph
type ComplianceParamPeriodLimit implements ComplianceParam {
    key: String
    label: String
    description: String
    value: Int
    unit: ComplianceParamPeriodUnit
}

enum ComplianceParamPeriodUnit {
    YEARS
    MONTHS
    DAYS
    WEEKS
    NONE    # For params where unit is not applicable.
}

## END workaround for `updateAndFixTimeOffs` mutation

type Address {
    key : String            # And identifier/label with some possible-uniquness...
    street: String
    line1: String
    line2: String
    city: String
    state: String
    province: String
    country: CountryCode
    zipcode: String
    postalCode: String
    landmark: String
}

type BankAccount {
    id: ID
    version: Int
    accountName: String
    accountNumber: String
    bankName: String
    branchName: String
    currency: CurrencyCode
    country: CountryCode
    swiftCode: String
    localBankCode: String
    bankStatements: [FileLink]
    paymentAccountRequirementType: String @deprecated # moved to requirement context
    accountRequirementContext : AccountRequirementContext
    accountDetails: [BankAccountDetail]
    bankIdentifier: String
}

type AccountRequirementContext {
    accountType: PaymentAccountType
    transferType: TransferType
    sourceCurrency: CurrencyCode
    targetCurrency: CurrencyCode
    targetCountry: CountryCode
    paymentPartner: PaymentPartnerCode
    paymentAccountRequirementType: String
}

type BankAccountDetail {
    key: String
    value: String
    label: String
}

type LegalEntity {
    id: ID
    kybId: ID
    legalName: String
    registrationNo: String
    currency: CurrencyCode
    address: Address
    phone: String
    bankAccount: BankAccount
    offeringCodes: [OfferingCode]
    capabilities: [Capability]
    status: LegalEntityStatus
    payrollData: [DataFieldValue]
    holidays(year: Int, month: Int, date: Int, input: LegalEntityHolidayInput): [Holiday!] #use LegalEntityHolidayInput, other params will be removed
    createdOn: DateTime
    updatedOn: DateTime
}

input LegalEntityHolidayInput {
    years: [Int!]
    month: Int
    date: Int
}

enum LegalEntityStatus {
    DRAFT
    ACTIVE
}

enum Capability {
    EOR
    AOR
    FREELANCER
    GROSS_TO_NET
    SALARY_DISBURSEMENT
    ORG_MANAGEMENT
    TIME_OFF
    TIME_SHEET
    EXPENSES
}

type DataFieldValue {
    key: String
    value: String
    documents: [File]
}

type PageResult {
    count: Int
    pageSize: Int
    pageNumber: Int
    totalCount: Int
    totalPages: Int
}

# Defines a legal data. Ex: Passport, National Identification/Identity Card/Number, Adhar, Social No, Europe, UTR, Britain, Driver's License
type LegalData {
    id: ID
    key: String                     # The main key value with some possible-uniquness...
    value: String                   # Passport, National Identification/Identity Card/Number, Adhar
    label: String                   # Country-specific local name.. Ex - { International, NIC } is {Norway, SocialNumber}
    identifier: String              # Identifier for the key. Eg: if key is "identityProof", then identifier can be "Passport No"
    country: CountryCode @deprecated
    createdOn: DateTime
    updatedOn: DateTime
}

type MonthYear {
    year: Int
    month: Int
}

type ScheduleTime {
    value: Int!
    unit: ScheduleTimeUnit!
}

input ScheduleTimeInput {
    value: Int!
    unit: ScheduleTimeUnit!
}

type WorkingHours {
    startTime: Time   # Work start time (in 24-hour format) per day
    endTime: Time     # Work end time (in 24-hour format) per day
}

# The date or day the payment happen according to the "pay frequency".
type PayFrequencyDate {
    # When PayFrequency = "MONTHLY" => identifier = "PAYOUT_DATE"
    # When PayFrequency = "SEMIMONTHLY" => identifier = "FIRST_PAYOUT_DATE" or "SECOND_PAYOUT_DATE"
    # When PayFrequency = "BIWEEKLY" or "WEEKLY" => identifier = "PAYOUT_DAY"
    identifier: PayFrequencyDateIdentifier

    # Applicable when PayFrequency = "MONTHLY" or "SEMIMONTHLY"
    dateOfMonth: Int

    # Applicable when PayFrequency = "WEEKLY" or "BIWEEKLY"
    dayOfWeek: DayOfWeek
}

type TimeRange {
    startTime: Time     # Start time (in 24-hour format)
    endTime: Time       # End time (in 24-hour format)
}

type AuditUser {
    userId : ID
    experience : String
}

type IntParam {
    key: String
    value: Int
}

type StringParam {
    key: String
    value: String
}

type DateParam {
    key: String
    value: DateTime
}

type CountryCount {
    country: CountryCode
    count: Int
}

type KeyValue {
    key: String
    value: String
}

type FieldAccessibilityRule {
    field: String
    editable: Boolean
    visible: Boolean
    reason: String
    requiresApproval: Boolean
}

type AccessibilityResponse {
    editable: Boolean
    reason: String
    requiresApproval: Boolean
    rules: [FieldAccessibilityRule!]
}

type Duration {
    length: Float,
    unit: TimeUnit
}

type Amount {
    amount: Float
    currency: CurrencyCode
}

input AmountInput {
    amount: Float
    currency: CurrencyCode
}
