type CustomFieldDefinition {
    id: ID!
    fieldKey: String!
    fieldName: String!
    fieldType: CustomFieldType!
    description: String
    category: String!
    objectType: String!
    applicabilities: [CustomFieldApplicability!]
    status: CustomFieldStatus!
    validationRules: CustomFieldValidationRules
    accessControl: CustomFieldAccessControl
    configs: [ConfigMapEntry!]
}

enum CustomFieldType {
    TEXT
    NUMBER
    BOOLEAN
    DATE
    EMAIL
    URL
    PHONE
    SELECT
    MULTI_SELECT
}

enum CustomFieldStatus {
    ACTIVE
    INACTIVE
    DELETED
}

enum CustomFieldContractType {
    EOR_EMPLOYEE
    DIRECT_EMPLOYEE
    CONTRACTOR
}

type CustomFieldApplicability {
    contractType: CustomFieldContractType!
    countryAndStates: [CountryAndState!]
    companyEntityIds: [ID!]
    allCountries: Boolean # default will be false
    allCompanyEntities: Boolean # default will be false
}

type CountryAndState {
    country: String
    states: [String!]
}

type CustomFieldValue {
    id: ID!
    fieldDefinitionId: ID!
    objectType: String!
    objectId: String!
    fieldValue: String!
    isDeleted: Boolean!
}

type CustomFieldValidationRules {
    # TEXT, TEXTAREA, EMAIL, URL, PHONE
    minLength: Int
    maxLength: Int
    pattern: String # regex pattern for validation

    # NUMBER
    minValue: Float
    maxValue: Float
    integerOnly: Boolean

    # DATE
    minDate: String # ISO8601 date string
    maxDate: String # ISO8601 date string

    # SELECT, MULTI_SELECT
    allowedValues: [String!]
}

# permission to create custom fields are managed via @authorize directive in sub-graph

type CustomFieldAccessControl {
    rolePermissions: [CustomFieldRolePermission!]!
    # extend with more access control rules as needed
}

type CustomFieldRolePermission {
    accessLevel: CustomFieldAccessLevel!
    roles: [String!]!
}

enum CustomFieldAccessLevel {
    NO_ACCESS
    VIEW_ONLY
    FULL_ACCESS
}

type ConfigMapEntry {
    key: String!
    value: ConfigValue!
}

type ConfigValue {
    string: String
    strings: [String!]
}

type CustomFieldCategoryConfig {
    objectType: String!
    categories: [String!]!
}

input CustomFieldApplicabilityInput {
    contractType: CustomFieldContractType!
    countryAndStates: [CountryAndStateInput!]
    companyEntityIds: [ID!]
    allCountries: Boolean # default will be false
    allCompanyEntities: Boolean # default will be false
}

input CountryAndStateInput {
    country: String
    states: [String!]
}

input CustomFieldDefinitionInput {
    id: ID
    fieldKey: String
    fieldName: String!
    fieldType: CustomFieldType!
    description: String
    category: String!
    objectType: String!
    applicabilities: [CustomFieldApplicabilityInput!]
    status: CustomFieldStatus
    validationRules: CustomFieldValidationRulesInput
    accessControl: CustomFieldAccessControlInput
    configs: [ConfigMapEntryInput!]
}

input CustomFieldValidationRulesInput {
    # TEXT, TEXTAREA, EMAIL, URL, PHONE
    minLength: Int
    maxLength: Int
    pattern: String # regex pattern for validation

    # NUMBER
    minValue: Float
    maxValue: Float
    integerOnly: Boolean

    # DATE
    minDate: String # ISO8601 date string
    maxDate: String # ISO8601 date string

    # SELECT, MULTI_SELECT
    allowedValues: [String!]
}

input CustomFieldAccessControlInput {
    rolePermissions: [AccessControlRolePermissionInput!]!
}

input AccessControlRolePermissionInput {
    accessLevel: CustomFieldAccessLevel!
    roles: [String!]!
}

input ConfigValueInput {
    string: String
    strings: [String!]
}

input ConfigMapEntryInput {
    key: String!
    value: ConfigValueInput!
}

input CustomFieldValueInput {
    id: ID
    fieldDefinitionId: ID!
    objectType: String!
    objectId: String!
    fieldValue: String!
    isDeleted: Boolean
}
