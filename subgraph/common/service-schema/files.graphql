interface File {
    id: ID
    name: String
    extension: String       # Can used for class styling. Also. sometimes we save thru CDN to avoid ramson-attact as their alorithms picks files with exetensions to encode.
    contentType: String
    versionId: ID
    versionedOn: DateTime
    versionFiles: [File]
}

#deprecated - This moved the props to the interface and this not needed anymore..
type FileWithVersion implements File {
    id: ID
    name: String
    extension: String
    contentType: String
    versionId: ID
    versionedOn: DateTime
    versionFiles: [File]
    versions: [FileWithVersion] @deprecated
}

type FileLink implements File {
    id: ID
    name: String
    extension: String
    contentType: String
    link: String
    size: Int               # File size in byte length. Int supports file size up to 2 GB
    versionId: ID
    versionedOn: DateTime
    versionFiles: [File]
    versions: [FileWithVersion] @deprecated
}

type DocumentReadable implements File {
    id: ID
    idV2: String
    name: String
    extension: String
    contentType: String
    link: String
    blob: String
    size: Int               # File size in byte length. Int supports file size up to 2 GB
    htmlPreview: String
    status: DocumentStatus
    versionId: ID
    versionedOn: DateTime
    versionFiles: [File]
    versions: [FileWithVersion] @deprecated
}

type Image implements File {
    id: ID
    name: String
    extension: String       # Always an image type
    contentType: String
    link: String
    blob: String
    versionId: ID
    versionedOn: DateTime
    versionFiles: [File]
    versions: [FileWithVersion] @deprecated
}
