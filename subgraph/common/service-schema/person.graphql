enum Persona {
    COMPANY
    MEMBER
    PARTNER
    OPERATIONS
}

interface Person {
    id: ID
    persona: Persona
    firstName: String
    lastName: String
    userId: String
    emails: [EmailAddress]
    phoneNos: [PhoneNumber]
}

###############################
# Types
###############################

type PhoneNumber {
    type: String
    phoneNo: String
}

type EmailAddress {
    type: String
    email: String
}

