input DateRange {
    startDate: DateTime
    endDate: DateTime
}

input PageRequest {
    """pageSize is the size of page"""
    pageSize: Int
    """pageNumber is the zero-based page number"""
    pageNumber: Int
    sort: [Sort!]
}

input EmailAddressInput {
    name : String
    value: String
}

input PhoneNumberInput {
    name : String
    value: String
}

input AddressInput {
    street: String
    line1: String
    line2: String
    city: String
    state: String
    province: String
    country: CountryCode
    zipcode: String
    postalCode: String
    landmark: String
}

input Sort {
    orderBy: String
    order: Order
}

input ContractFilters {
    contractId: ID
    contractIds : [ID]
    country: CountryCode
    companyId: ID
    startDateRange: DateRange
    createdDateRange: DateRange
    endedDateRange: DateRange
    validTillDateRange: DateRange
    onboardingOperationsUserId: ID
    onboardingStatus: ContractOnboardingStatus
    onboardingStatuses: [ContractOnboardingStatus!]
    offboardingStatuses: [ContractOffboardingStatus!]
    isLegacyOffboarding: Boolean
    contractType: ContractType
    contractStatus: ContractStatus
    memberNameContains: String
    memberEmailContains: String
    benefitPackageName: String
    eorPartnerId: ID # a partner id - but will be used for eor contracts
    isMultiplierEorPartner: Boolean
    toBeAssignedEorPartner: Boolean
    csmUserId: ID
    assignedToOpsUser: Boolean # Fetch the contracts only if ops user is assigned
    isTestContract: Boolean #Fetch the contracts tagged as Test by the customer.
    companyNameContains: String #Fetch the contract based on the company name.
    isPayrollActive: Boolean #Fetch by the payroll active status
    payrollStartDateRange: DateRange #Fetch by payroll start date range
    excludedTypes: [ContractType!] # Fetch by contract type is not in
    payrollFormsUploadDateRange: DateRange
    payrollFormsUploadStatus: PayrollFormsUploadStatus
    requiresPayrollForms: Boolean
    activationDateRange: DateRange
    contractTypes: [ContractType!] # this filter will override contractType
    depositInvoiceId: ID @deprecated
    depositInvoiceStatus: InvoiceStatus @deprecated
    depositInvoiceFullyPaidOnDateRange: DateRange @deprecated
    depositPayableFilters: DepositPayableFilters
    workStatus: CountryWorkStatus
    eorPartnerIds: [ID!]
    excludedStatuses: [ContractStatus!]
    contractStatuses: [ContractStatus!]
    legalEntityId: ID
}

input CompanyFilters {
    companyId: ID
    companyName: String # Not implemented currently.
    associatedEmailDomain: String # Takes an email domain
    companyStatuses: [CompanyStatus] # For filtering based on multiple company statuses
    createdDateRange: DateRange
    country: CountryCode
    assignType: CompanyAssignTypeFilter # nullable. if null, defaults to ALL. currentUser MUST be operationsUser if value is not ALL.
    csmUserId: ID
    salesUserId: ID
}

input DepositPayableFilters {
    invoiceStatuses: [InvoiceStatus]
}

input MonthYearInput {
    year: Int
    month: Int
}

input TextFieldInput {
    key: String
    value: String
}

input DateFieldInput {
    key: String
    value: Date
}

input TimeRangeInput {
    startTime: Time # Start time (in 24-hour format)
    endTime: Time   # End time (in 24-hour format)
}

input KeyValueInput {
    key: String
    value: String
}

input BulkOnboardingOptions {
    countryCode: CountryCode
    contractType: ContractType
    companyId: ID
    entityId: ID
    context: BulkOnboardingContext! = GLOBAL_PAYROLL
}
