### wallet enums ###

enum WalletType {
    INDIVIDUAL
    CORPORATE
}

enum WalletStatus {
    CREATED
    ONBOARDING
    ACTIVE
    INACTIVE
}

enum WalletCategory {
    SALARY
    EXPENSE_CARD
}


enum WalletOnboardingStep {
    INITIATED
    DOCUMENT_COLLECTION
    KYC_VERIFICATION
    INTERNAL_APPROVAL
    ACTIVATION
}

enum WalletOnboardingStatus {
    INITIATED
    DOCUMENT_COLLECTION_IN_PROGRESS
    DOCUMENT_COLLECTION_COMPLETED
    KYC_VERIFICATION_IN_PROGRESS
    KYC_CLARIFICATION_REQUIRED
    REJECTED
    COMPLETED
    FAILED
}


### wallet options ###

type WalletOptions {
    type: WalletType
    category: WalletCategory
    country: CountryCode
    onboardingOptions : [WalletOnboardingOptions]
}

type WalletOnboardingOptions {
    step : WalletOnboardingStep
    onboardingRequirements: WalletPartnerOnboardingRequirements
}

type WalletPartnerOnboardingRequirements {
    partner: PaymentPartnerCode
    transferType: TransferType
    walletAccountRequirement: WalletAccountPartnerRequirement
}

type WalletAccountPartnerRequirement {
    requirementFields: [PaymentRequirementField]
}

input WalletOptionsFilter {
    category: WalletCategory
}



######################################################

interface WalletWithdrawalOption {
    type : WalletWithdrawalOptionType
}

type PayOutWithdrawalOption implements WalletWithdrawalOption {
    type : WalletWithdrawalOptionType
    walletPartner: PaymentPartnerCode,
    payOutWithdrawalOptions: [ContractPaymentOption]
}

input WalletWithdrawalOptionsFilter {
    walletType: WalletType,
    walletCategory: WalletCategory
}

enum WalletWithdrawalOptionType {
    PAY_OUT_WITHDRAWALS
}

