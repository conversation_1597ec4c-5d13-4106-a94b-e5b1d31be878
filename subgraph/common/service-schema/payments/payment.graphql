####### PAYIN METHOD #######
type PayInMethodContext {
    country: CountryCode
    sourceCurrency: CurrencyCode
    targetCurrency: CurrencyCode
}

interface PayInMethod {
    id: ID!
    payInMethodType: PayInMethodType
    context: PayInMethodContext
    workflowSteps: [PayInMethodWorkflowStep]!
    isActive: Boolean
    isEnabled: Boolean
}

interface PayInMethodWorkflowStep {
    step: PayInMethodDefinitionStep,
    data: [AccountDetails]!
}

type AccountDetails {
    payInAccountRequirementType: String
    data: [KeyValue]
    documents: [DocumentData]
}

## PAYIN METHOD SCHEMA DEFINITION ##
type PayInMethodRequirement {
    id: ID!
    partner: String
    context: PayInMethodContext
    payInMethodType: PayInMethodType
    definition: [PayInMethodDefinition]
}

type PayInMethodDefinition {
    step : PayInMethodDefinitionStep
    requirement: [PayInMethodAccountRequirement]
}

type PayInMethodAccountRequirement {
    payInAccountRequirementType : String,
    fields : [PaymentRequirementField]
}

type PaymentAccountPartnerRequirement {
    paymentAccountRequirementType: String
    requirementFields: [PaymentRequirementField]
}

## Payment Requirements ##
# Duplicating Contract.PaymentRequirementField for backward compatibility
type PaymentRequirementField {
    key: String
    label: String
    isMandatory: Boolean
    type: String
    regex: String
    allowedValues: [PaymentRequirementAllowedValue]
    hasSubFields: Boolean
    subFields: [PaymentRequirementSubField]
    errorMessage: String
    disabledOn: String
}

type PaymentRequirementSubField {
    key: String
    value: [PaymentRequirementField]
}

type PaymentRequirementAllowedValue {
    key: String
    value: String
}

type DocumentData {
    key : String
    document: DocumentReadable
}

enum PaymentPriorityType {
    URGENT_PAYMENT,
    NON_URGENT_PAYMENT
    WPS_NON_URGENT_PAYMENT
}

####### contract payment options ########

type ContractPaymentOption {
    payOutCurrency: CurrencyCode,
    payOutCountry: CountryCode,
    sourceCurrency: CurrencyCode
}

input ContractPaymentOptionsRequest {
    requestType: ContractPaymentOptionsRequestType,
    filters: [ContractPaymentOptionFilter]
}

input ContractPaymentOptionFilter {
    paymentPartner: PaymentPartnerCode, ## if null returns for all partners
    payOutCurrency: CurrencyCode!,
    payOutCountry: CountryCode!
}

enum ContractPaymentOptionsRequestType {
    WALLET_WITHDRAWAL
    DEFAULT
}

