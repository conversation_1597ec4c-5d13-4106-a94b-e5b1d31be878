enum ContractStatus {
    ONBOARDING
    ACTIVE
    OFFBOARDING
    ENDED
    DELETED
}

enum ContractType {
    <PERSON><PERSON><PERSON><PERSON><PERSON>EE
    HR_MEMBER
    FREELANCER
    CONTRACTOR
}

enum ContractTerm {
    FIXED
    PERMANENT
}

enum TimeOffUnit {
    YEARS
    MONTHS
    DAYS
    WEEKS
}

enum LimitValueType {
    FIXED,
    PERCENTAGE
}

enum AllocationBasis {
    ANNUALLY
    MONTHLY
    DAILY
    QUARTERLY
    WEEKLY
    ONCE
}

enum LegalDocumentCategory {
    PROOF
    PAYROLL # Payroll related documents
    LEGAL
    VISA_PRIMARY
    VISA_SECONDARY
    OTHER
}

enum PayrollFormsUploadStatus {
    SUBMITTED,
    PENDING,
    NOT_APPLICABLE
}

enum DocumentStatus {
    CREATED
    UPLOADED
    GENERATION_IN_PROGRESS
    GENERATION_SUCCESS
    GENERATION_FAILED
    SENT_FOR_SIGNING
    VIEWED
    SIGNING
    DROPPED
    SIGNED
}

# There are more than 60+ genders...
enum Gender {
    FEMALE
    MALE
    UNSPECIFIED
}

enum CurrencyCode {
    AED
    AFN
    ALL
    AMD
    ANG
    AOA
    ARS
    AUD
    AWG
    AZN
    BAM
    BBD
    BDT
    BGN
    BHD
    BIF
    BMD
    BND
    BOB
    BOV
    BRL
    BSD
    BTN
    BWP
    BYN
    BYR
    BZD
    CAD
    CDF
    CHE
    CHF
    CHW
    CLF
    CLP
    CNH
    CNY
    COP
    COU
    CRC
    CUC
    CUP
    CVE
    CZK
    DJF
    DKK
    DOP
    DZD
    EGP
    ERN
    ETB
    EUR
    FJD
    FKP
    GBP
    GEL
    GGP
    GHS
    GIP
    GMD
    GNF
    GTQ
    GYD
    HKD
    HNL
    HRK
    HTG
    HUF
    IDR
    ILS
    IMP
    INR
    IQD
    IRR
    ISK
    JEP
    JMD
    JOD
    JPY
    KES
    KGS
    KHR
    KMF
    KPW
    KRW
    KWD
    KYD
    KZT
    LAK
    LBP
    LKR
    LRD
    LSL
    LTL
    LYD
    MAD
    MDL
    MGA
    MKD
    MMK
    MNT
    MOP
    MRU
    MUR
    MVR
    MWK
    MXN
    MXV
    MYR
    MZN
    NAD
    NGN
    NIO
    NOK
    NPR
    NZD
    OMR
    PAB
    PEN
    PGK
    PHP
    PKR
    PLN
    PYG
    QAR
    RON
    RSD
    RUB
    RWF
    SAR
    SBD
    SCR
    SDG
    SEK
    SGD
    SHP
    SLL
    SOS
    SPL
    SRD
    SSP
    STN
    SVC
    SYP
    SZL
    THB
    TJS
    TMT
    TND
    TOP
    TRY
    TTD
    TVD
    TWD
    TZS
    UAH
    UGX
    USD
    USN
    UYI
    UYU
    UYW
    UZS
    VEF
    VES
    VND
    VUV
    WST
    XAF
    XAG
    XAU
    XBA
    XBB
    XBC
    XBD
    XCD
    XDR
    XOF
    XPD
    XPF
    XPT
    XSU
    XTS
    XUA
    YER
    ZAR
    ZMK
    ZMW
    ZWD
    ZWL
}

enum CountryCode {
    ABW
    AFG
    AGO
    AIA
    ALA
    ALB
    AND
    ARE
    ARG
    ARM
    ASM
    ATA
    ATF
    ATG
    AUS
    AUT
    AZE
    BDI
    BEL
    BEN
    BES
    BFA
    BGD
    BGR
    BHR
    BHS
    BIH
    BLM
    BLR
    BLZ
    BMU
    BOL
    BRA
    BRB
    BRN
    BTN
    BVT
    BWA
    CAF
    CAN
    CCK
    CHE
    CHL
    CHN
    CIV
    CMR
    COD
    COG
    COK
    COL
    COM
    CPV
    CRI
    CUB
    CUW
    CXR
    CYM
    CYP
    CZE
    DEU
    DJI
    DMA
    DNK
    DOM
    DZA
    ECU
    EGY
    ERI
    ESH
    ESP
    EST
    ETH
    FIN
    FJI
    FLK
    FRA
    FRO
    FSM
    GAB
    GBR
    GEO
    GGY
    GHA
    GIB
    GIN
    GLP
    GMB
    GNB
    GNQ
    GRC
    GRD
    GRL
    GTM
    GUF
    GUM
    GUY
    HKG
    HMD
    HND
    HRV
    HTI
    HUN
    IDN
    IMN
    IND
    IOT
    IRL
    IRN
    IRQ
    ISL
    ISR
    ITA
    JAM
    JEY
    JOR
    JPN
    KAZ
    KEN
    KGZ
    KHM
    KIR
    KNA
    KOR
    KWT
    LAO
    LBN
    LBR
    LBY
    LCA
    LIE
    LKA
    LSO
    LTU
    LUX
    LVA
    MAC
    MAF
    MAR
    MCO
    MDA
    MDG
    MDV
    MEX
    MHL
    MKD
    MLI
    MLT
    MMR
    MNE
    MNG
    MNP
    MOZ
    MRT
    MSR
    MTQ
    MUS
    MWI
    MYS
    MYT
    NAM
    NCL
    NER
    NFK
    NGA
    NIC
    NIU
    NLD
    NOR
    NPL
    NRU
    NZL
    OMN
    PAK
    PAN
    PCN
    PER
    PHL
    PLW
    PNG
    POL
    PRI
    PRK
    PRT
    PRY
    PSE
    PYF
    QAT
    REU
    ROU
    RUS
    RWA
    SAU
    SDN
    SEN
    SGP
    SGS
    SHN
    SJM
    SLB
    SLE
    SLV
    SMR
    SOM
    SPM
    SRB
    SSD
    STP
    SUR
    SVK
    SVN
    SWE
    SWZ
    SXM
    SYC
    SYR
    TCA
    TCD
    TGO
    THA
    TJK
    TKL
    TKM
    TLS
    TON
    TTO
    TUN
    TUR
    TUV
    TWN
    TZA
    UGA
    UKR
    UMI
    URY
    USA
    UZB
    VAT
    VCT
    VEN
    VGB
    VIR
    VNM
    VUT
    WLF
    WSM
    YEM
    ZAF
    ZMB
    ZWE
}

enum Order {
    ASC
    DESC
}

enum ContractOnboardingStatus {
    NULL_STATE # Only for the purposes of managing the workflow.
    DRAFT
    CREATED
    CREATED_CUSTOM                  # New, for freelancer & hr-member
    REVOKED                         # Indicates that onboarding is in a suspended state
    INVITED                         @deprecated # To remove, changed to MEMBER_INVITED
    ORDER_FORM_SENT_TO_EMPLOYER     # New, order form sent to employer for AOR
    ORDER_FORM_SIGNED_BY_EMPLOYER   # New, order form signed by employer for AOR
    SIGNATURE_EMPLOYER_SENT         # New, sent to employer for signature
    SIGNATURE_EMPLOYER_SIGNED       # New, employer signature done
    SIGNATURE_EMPLOYEE_SENT         # New, sent to employee for signature
    SIGNATURE_EMPLOYEE_SIGNED       # New, employee signature done
    SIGNATURE_MULTIPLIER_SENT       # New, multiplier signature sent
    SIGNATURE_MULTIPLIER_SIGNED     # New, multiplier signature signed
    CONTRACT_PREPARING_CONFIRMATION # Awaiting confirmation to start preparing contract
    VISA_DOCUMENTS_STARTED
    VISA_DOCUMENTS_SUBMITTED
    VISA_DOCUMENTS_VERIFIED
    VISA_INVOICE_PAID
    CONTRACT_PREPARING              # New
    CONTRACT_WET_INK_PREPARING      # New
    VISA_APPLICATION_PREPARING
    VISA_APPLICATION_FILING
    VISA_APPLICATION_APPROVED
    CONTRACT_UPDATING               # New, ops start updating contract data
    MEMBER_INVITE_PENDING
    MEMBER_INVITED                  # New
    MEMBER_STARTED
    MEMBER_DATA_ADDED
    MEMBER_DATA_REVIEWED            # New
    MEMBER_COMPLETED
    MEMBER_VERIFICATION_IN_PROGRESS # New
    MEMBER_VERIFICATION_COMPLETED   # New
    MSA_SIGNING_PENDING             # New
    MSA_SIGNING_IN_PROGRESS         # New
    VERIFICATION_IN_PROGRESS        @deprecated # To remove, changed to MEMBER_VERIFICATION_IN_PROGRESS
    RESOLUTION_IN_PROGRESS          @deprecated # To remove
    VERIFICATION_DONE               @deprecated # To remove
    PREPARING_CONTRACT              @deprecated # To remove, changed to CONTRACT_PREPARING
    SIGNING_IN_PROGRESS             @deprecated # To remove, divided into more granular statuses
    ORDER_FORM_SENT_TO_MULTIPLIER   # New, order form sent to multiplier for AOR
    ORDER_FORM_SIGNED_BY_MULTIPLIER # New, order form signed by multiplier for AOR
    ICA_SENT_TO_MULTIPLIER          @deprecated # To remove, change to use SIGNATURE_MULTIPLIER_SENT
    ICA_SIGNED_BY_MULTIPLIER        @deprecated # To remove, change to use SIGNATURE_MULTIPLIER_SIGNED
    ACTIVE
    DROPPED                         # The contract has been dropped
}

enum ContractOnboardingStep {
    START # Only for the purposes of managing the workflow.
    #Company Experience
    DEFINITION_MEMBER_ELIGIBILITY
    DEFINITION_MEMBER_BASIC_DETAILS
    DEFINITION_COUNTRY_SPECIFIC_DETAILS # New
    DEFINITION_COMPENSATION_DETAILS
    DEFINITION_BANK_DETAILS
    DEFINITION_BENEFITS_DETAILS
    DEFINITION_COMPLIANCE_DETAILS
    DEFINITION_DESCRIPTION_OF_WORK
    DEFINITION_CONTRACT                # Physical Employment-Contract generated/uploaded.
    ONBOARDING_REVIEW
    ONBOARDING_SIGNING      # Now this is only for company experience
    ONBOARDING_MULTIPLIER_SIGNING # for onboarding multiplier signing
    ONBOARDING_CONTRACT     # Only for HR Member/Freelancer with their own contract
    ONBOARDING_CONTRACT_PREPARING_CONFIRMATION # Awaiting confirmation to start preparing contract
    ONBOARDING_CONTRACT_WET_INK_PREPARING_CONFIRMATION # New Awaiting confirmation for wet ink
    ONBOARDING_CONTRACT_WET_INK_PREPARING # New Preparing wet ink contract
    VISA_ELIGIBILITY
    VISA_APPLICATION
    ONBOARDING_CONTRACT_UPDATING    # New, step for company experience when ops start updating contract data
    ONBOARDING_MEMBER
    ONBOARDING_MSA_PENDING  # New
    ONBOARDING_ACTIVATION   # New

    #Company and Member Experience.
    ONBOARDING_VERIFYING


    #Member Experience
    MEMBER_WELCOME
    MEMBER_WELCOME_VISA
    MEMBER_VISA_ELIGIBILITY
    MEMBER_VISA_APPLICATION
    MEMBER_BASIC_DETAILS
    MEMBER_VISA_APPROVED
    MEMBER_LEGAL_DETAILS
    MEMBER_BANK_DETAILS
    MEMBER_LEGAL_DOCUMENTS
    MEMBER_BENEFIT_DETAILS
    MEMBER_REVIEW

    #For Workflow
    ONBOARDED
}

enum CompanyStatus {
    CREATED
    INVITED
    INVITE_ACCEPTED
    UNVERIFIED
    ACTIVE
    INACTIVE
}

enum CompanyUserCapability {
    SIGNATORY
    BILLING_CONTACT
}

enum ApprovalCategory {
    EXPENSE
    TIME_OFF
    TIME_SHEET
    MEMBER_PAYABLE @deprecated(reason: "unused since mid 2022, when we stopped using the `approval framework` for memberPayable")
    LEGAL_DOCUMENTS
    MEMBER_DATA
}

enum PayableQueryMode {
    DEFAULT
    DRYRUN_DETAILED
    DRYRUN_SUMMARIZED
}

enum RateType {
    CTC         # Total cost to company
    GROSS       # Gross amount without deductions
    NET         # The amount recived by member
}

enum PayItemRateType {
    VALUE,
    PERC_BASE_PAY,
    BASE_PAY_DAYS,
    CTC_DAYS,
    CTC_PERCENTAGE,
}

enum RateFrequency {
    HOURLY
    DAILY
    WEEKLY
    SEMIMONTHLY
    MONTHLY     # Default
    BI_MONTHLY
    QUATERLY
    TRI_ANNUALLY
    HALFYEARLY
    ANNUALLY
    ONCE
    CUSTOM
    BI_WEEKLY
}

enum PayAmountType {
    FIXED_AMOUNT
    VARIABLE_AMOUNT
    FIXED_PERCENTAGE
    VARIABLE_PERCENTAGE
}

enum ScheduleTimeUnit {
    YEAR
    MONTH
    DAY
    QUARTER
}

enum PaySupplementStatus {
    PENDING             # Pay supplement is pending
    CREATED             # Pay supplement was created
    PROCESSING          # Selected into payroll
    TO_BE_PAID          # pay supplements were not paid during the payroll cycle
    PAID                # Pay supplement was paid from a payroll
    REJECTED            # Rejected by OPS.
    REVOKED             # Revoked/Deleted by COMPANY. Matches DELETED status in Alpha
}

# Salary calculator currently uses the below three statuses.
# TODO: Replace with proper 2 Digit codes (Data type : String to be compatible with other stateCode implementations.
enum StateCode {
    DELAWARE
    NEWYORK
    CALIFORNIA
    ALASKA
    ARIZONA
    COLORADO
    ALABAMA
    GEORGIA
    NORTH_DAKOTA
    CONNECTICUT
    ARKANSAS
    FLORIDA
    HAWAII
    NORTH_CAROLINA
    SOUTH_CAROLINA
    RHODE_ISLAND
    LOUISIANA
    SOUTH_DAKOTA
    WISCONSIN
    KENTUCKY
    WYOMING
    MAINE
    MARYLAND
    MICHIGAN
    MINNESOTA
    MISSISSIPPI
    MISSOURI
    MONTANA
    NEBRASKA
    NEVADA
    OKLAHOMA
    KANSAS
    IDAHO
    ILLINOIS
    INDIANA
    IOWA
    VERMONT
    PENNSYLVANIA
    VIRGINIA
    TENNESSEE
    TEXAS
    UTAH
    WEST_VIRGINIA
    WASHINGTON
    NEW_HAMPSHIRE
    MASSACHUSETTS
    OREGON
    OHIO
    NEW_MEXICO
    NEW_JERSEY
    WASHINGTON_DC
}

enum CountryWorkStatus {
    RESIDENT
    HAS_VISA
    REQUIRES_VISA
    UNSPECIFIED     # The job doesn't bother is visa status.
    OBTAINED_VISA   # Indicates that ops has obtained the requisite visa for this contract
    EXPAT @deprecated(reason: "[FINX-1341] Use REQUIRES_VISA instead")
}

enum NotificationType {
    ResetPassword
    ChangePassword
    AddCompanyMember
    SendToCompanyForSigning
    SendToMemberForSigning
    SendToMemberForWelcome
    SendToMemberForWelcomeContractor
    SendToAdminAfterMemberSignContract
    SendToMultiplierForSigning
    SendToMemberForWetInkContract
    SendToCompanyForWetInkContract
    MemberInvite
    VisaInviteForEligibilityToMember
    VisaMemberAddedToOps
    VisaDocumentsSubmittedToMember
    VisaDocumentsSubmittedToOps
    VisaDocumentsVerifiedToMember
    VisaSendContractReminderToCompany
    VisaApprovedToMember
    MemberCompletedOnboardingToMemberAndAdmins
    MemberVerificationComplete
    ContractActivatedToEmployer @deprecated
    ContractActivatedToMember
    CompanyAdminActivatedToOps
    CompanyInvitation
    CompanyUserInvitationAdmin
    CompanyUserInvitationManager
    CompanyBillingContactUserInvitation
    OpsEmployerOnboardedMember
    OpsMemberCompletedOnboarding
    OpsReportSentForReview
    OpsStartDateChanged
    OpsContractAssignedToOnboardingSpecialist
    OpsContractDeleted
    OpsAssignCustomerToCsm
    OpsContractRevoked
    OpsMemberOnboardingForACountryWithoutContract
    OpsMemberOnboardingForACountryWithWetInkRequirement
    OpsMemberRetirementPlanDetails
    OpsOffboardingContractsReminder
    AutoInvoiceEnabledForContractor
    AutoInvoiceDisabledForContractor
    AutoInvoiceEnabledForFutureContractors
    AutoInvoiceDisabledForFutureContractors
    AutoInvoiceUpdatedForContractor
    AutoInvoiceUpdatedForFutureContractors
    FreelancerInvoiceSubmittedToCompany @deprecated(reason: "Use FreelancerInvoiceRaisedToCustomer instead")
    FreelancerInvoiceSubmittedToMember
    FreelancerInvoiceApprovedToMember
    FreelancerInvoiceRejectedToMember
    FreelancerInvoiceRevisedSuccessfullyToMember
    FreelancerInvoicePaidToMember
    FreelancerInvoiceApprovedToOps
    FreelancerInvoiceSubmittedToOps
    FreelancerInvoiceFirstReminderToMember
    FreelancerInvoiceSecondReminderToMember
    FreelancerInvoiceThirdReminderToMember
    FreelancerInvoiceRaisedToCustomer
    FreelancerInvoiceUpdatedToCustomer
    FreelancerInvoicePaymentReceivedToCustomer
    FreelancerInvoicePaidToCustomer
    FreelancerInvoiceReminderToCustomer
    FreelancerInvoicePartialPayInReceivedToOps
    FreelancerInvoicePartialPayInReceivedToCompany
    PartnerPayrollInitiationWithoutUpdates
    PartnerPayrollInitiationWithUpdates
    PayrollPartnerUserInvite
    PayslipMemberEmail
    ExpenseCreatedToCompanyUsers
    ExpenseApprovedToMember
    ExpenseApprovedToOps
    ExpenseRejectedToMember
    ExpenseReminderToEmployer
    ExpenseUpdateToEmployer
    ExpenseUpdateToOps @deprecated
    ExpenseDeleteToEmployer
    ExpenseDeleteToOps @deprecated
    ExpenseSubmissionReminderToMember
    ExpenseRequestClarificationToMember
    OpsPaySupplementAdded
    OpsPaySupplementUpdated
    OpsPaySupplementRevoked
    PaySupplementReminderToCompanyAdmins
    PaySupplementReminderToCompanyAdminsMonthly
    PaySupplementReminderToCompanyAdminsMonthlyCountrySpecificCutOff
    OpsContractRequestChangesForMember
    TimeoffCreatedEmailToApprover
    TimeoffApproveEmailToMember
    TimeoffRejectEmailToMember
    SalaryCalculatorRequestEmail
    DataChangeNotifyToOps
    TimesheetApprovedEmailToMember
    TimesheetApprovedEmailToOps
    TimesheetRejectedEmailToMember
    TimesheetSubmittedEmailToCompanyUser
    CompanyCreationOpsEmailOrganic
    MemberJoiningDay
    MemberJoiningDayBatch
    MemberJoiningDayPayrollForms
    TRSEmailToEmployer
    TRSReminderEmailToEmployer
    TRSEmailToEmployee
    TRSEmailToOps
    TRSEmailToMultiplierForSignature
    OfferLetterEmail
    EsopEmailToMember
    EsopEmailToSignatory
    LegalDocumentCreatedEmailToOps
    LegalDocumentApprovedEmailToMember
    LegalDocumentRejectedEmailToMember
    OpsMemberChangeRequest
    MemberChangeRequestToCompanyAdmin
    MemberChangeRequestStatus
    MemberChangeRequestStatusApproved
    MemberChangeRequestStatusRejected
    MemberCompleteOnboardingReminder
    MemberOffBoardingEmailToMember
    MemberOffBoardingEmailToOps
    MemberOffBoardingCancelEmailToMember
    MemberOffBoardingCancelEmailToOps
    MemberOffBoardingFutureDateContractEndEmailToMember
    EquipmentQuotationCreatedEmailToOps
    EquipmentProcurementCreatedEmailToOps
    FreeLancerInvoicePaidConfirmedToOps
    FreeLancerInvoicePayInConfirmedToOps
    SendContractAddendumForSigningToCompany
    SendContractAddendumForSigningToMember
    SendContractAddendumForSigningToMultiplier
    UpcomingMonthlyInvoiceEmailToCompany
    MsaEmailToCompany
    MsaSignedEmailToCXLeaders
    TRSPartnerEOREmailToOps
    TRSPartnerEOREmailToEmployee
    MemberPendingPayrollFormsSubmissionReminderToMember
    ProbationEndReminderToCompany
    TRSHrMemberEmailToOps
    AdminContractRevoked
    AdminContractDeleted
    EmployeeContractRevised
    FreelancerPayoutFailClient
    FreelancerPayoutFailFreelancer
    ContractChangeRequestBlockedEmailToCompanyUser
    ContractChangeRequestDelayedEmailToCompanyUser
    ContractChangeRequestUnblockedEmailToCompanyUser
    ContractAdditionalPaysCheckEmailToUser
    FirstContractOnboardingReminderToCompany
    SecondContractOnboardingReminderToCompany
    ThirdContractOnboardingReminderToCompany
    FirstContractOnboardingReminderToMember
    SecondContractOnboardingReminderToMember
    ThirdContractOnboardingReminderToMember
    PaymentBundleMarkAsPaidEmailToOps
    PaymentBundleUnpaidReminderEmailToCompany
    BundlePaymentReceivedEmailToOps
    InvoicePaidEmailToCompany
    EorInvoiceRaisedToCompanyAdmin
    PendingApprovalEmailToNewApprover
    ContractFixedTermRenewableExpiringReminder
    ContractFixedTermNonRenewableExpiringReminder
    TimesheetAutoSubmitEmailToHourlyRateMembers
    TimesheetAutoSubmitEmailToNonHourlyRateMembers
    DailyTimecardReminderToMember
    TimesheetRejectedHoursEmailToMember
    TimesheetApproveSubmittedHoursEmailOnMidCycleToAdmin
    TimesheetApproveSubmittedHoursEmailOnEndCycleToAdmin
    TimesheetUnapprovedPerClientReminderToCSM
    TimesheetApprovedHoursEmailToHourlyMember
    TimesheetApprovedHoursEmailToNonHourlyMember
    InsuranceClientEmailsForAgeBasedCost
    ContractOrderFormToCompany
    ContractOrderFormToMultiplier
    InputFileDownloadSuccessToOps
    InputFileDownloadFailedToOps
    PreRegistrationContractActivationEmailToOps
    PerformanceReviewSignatoryEmailToEmployee
    ACHDirectDebitSetupOpsEmail
    ACHDirectDebitPreNoteEmail
    ACHDirectDebitOpsEmail
    PerformanceReviewReminderEmailToEmployee
    PerformanceReviewReminderEmailToCompanySignatory
    UpdateBasicDetailsOnMultiplierEmailToMember
    UpdateBankDetailsOnMultiplierEmailToMember
    OpsCompanyIsFxSensitive
    PayslipFileValidationResultEmail
    DepositInvoicePaidSlackEmail
    BulkOnboardingSuccessToOps
    BulkOnboardingFailedToSlack
    OnboardingSpecialistEmail
    IntegrationEmployeeImportCompletedEmailToAdmin
    ApproverUpdationEmailToOldApprover
    TimeoffTypeCreatedEmailToAdmin
    TimeoffTypeUpdatedEmailToAdmin
    TimeoffTypeDeletedEmailToAdmin
    TimeoffPolicyCreatedEmailToAdmin
    TimeoffPolicyUpdatedEmailToAdmin
    TimeoffPolicyDeletedEmailToAdmin
    TimeoffPolicyAssignmentEmailToAdmin
    DirectDebitFailedStatusEmailToFinance
    DirectDebitProcessingFailedEmailToCustomer
    DirectDebitProcessingFailedEmailToFinance
    LegalEntityActivatedToAdmin
    PayrollMemberInvite
    ReviewEorPayrollReminder
    ReviewEorPayrollReportReminder
    ReviewPeoPayrollReminder
    ApprovePeoPayrollReportReminder
    DisputeInvoiceEmailToUser
    AutoDebitPreNotification
    AutoDebitDisputeRecreationNotification
    AutoDebitManualCreationNotification
    DepartmentCreatedEmailToAdmin
    DepartmentDeletedEmailToAdmin
    ContractQuestionnaireEmailToCompany
    ContractQuestionnaireEmailToOps
    IntegrationCredentialsExpiredEmailToAdmin
    IntegrationConnectedEmailToAdmin
    IntegrationDisconnectedEmailToAdmin
    IntegrationEmployeeSyncTurnedOnEmailToAdmin
    IntegrationSyncResultsEmailToAdmin
    IntegrationAutoSyncFailedEmailToAdmin
    IntegrationSFTPUpsertSuccessEmailToAdmin
    IntegrationSFTPValidationFailureEmailToAdmin
    IntegrationSFTPAccessRequestEmailToOps
    ContractEmployeeIdAndWorkEmailReminderToCompany
    ContractWorkEmailReminderOnStartDayToCompany
    FundClearedWithChargebackPeriod
    FundClearedWithoutChargebackPeriod
    HRISOnlyMemberInvite
    GPAndHRISMemberInvite
    HRISRoleChangeEmailToEmployee
    PayrollCycleOutputApprovedEmailToOps
    BenefitChangeRequestReceivedEmailToMember
    BenefitChangeRequestUpdatedEmailToMember
    IntegrationGPSyncReportToAdmin
    HRISOnlyInsuranceOfferedEmailToOps
    IntegrationEORSyncFailedEmailToAdmin
    ResignationInitiatedToAdminsDifferentLwd
    ResignationInitiatedToAdminsMatchingLwd
    ResignationInitiatedToOffboardingTeams
    LwdAcknowledgedToAdminsDifferentLwd
    LwdAcknowledgedToEmployeeDifferentLwd
    LwdAcknowledgedToEmployeeMatchingLwd
    ResignationLetterSignedToAdmins
    ResignationLetterSignedToEmployee
    ResignationLetterSignedToOffboardingTeams
    FnfInputSubmissionInitialNotificationToAdmins
    FnfInputSubmissionFollowupReminderToAdmins
    FnfInputSubmissionEscalatedReminderToAdmins
    FnfInputSubmissionWeeklyRecurringReminderToAdmins
    ShortfallPayConfirmationInitialNotificationToAdmins
    ShortfallPayConfirmationEscalatedReminderToAdmins
    ShortfallPayConfirmationWeeklyRecurringReminderToAdmins
    DepositInvoiceGeneratedEmailToCompany
    StaleGPSyncDeactivatedToCompany
    BulkOnboardingFileUploadedSuccessfully
    BulkOnboardingEmployeeAddedToTeam
    BulkExpenseFileUploadedSuccessfully
    BulkExpensesAddedToSystem
    YearEndTaxFormMemberEmail
    TaxSlipMemberEmail
    BulkMemberPayableCompleted
    BulkMemberPayableValidationPending
    BulkMemberPayableReviewPending
    BulkMemberPayableApprovalPending
    MemberPayableApproved
    MemberPayableDeleted
    SendToMemberAfterPayrollFormSubmission
    OpsMemberSignContractReminderCta
    OpsMemberUpdateDetailsAndKYCReminderCta
    OpsMemberUpdatePayrollFormsReminderCta
    USAOpsMemberSignContractReminderCta
    USAOpsMemberUpdateDetailsAndKYCReminderCta
    USAOpsMemberUpdatePayrollFormsReminderCta
    OpsMemberSignContractContractorReminderCta
    OpsMemberUpdateDetailsContractorReminderCta
    OpsMemberVerificationRequiredReminderCta
    PaymentSummaryReportDownloadToContractor
    PaymentSummaryReportDownloadToAdmin
    PaymentSummaryReportDownloadSuccessToContractor
    PaymentSummaryReportDownloadSuccessToAdmin
    PaymentSummaryReportDownloadFailureToContractor
    PaymentSummaryReportDownloadFailureToAdmin
    CompanyBenefitSetupConfirmation
    ModifyDependentChangeRequestReceivedConfirmation
    AddDependentChangeRequestReceivedConfirmation
    DeleteDependentChangeRequestReceivedConfirmation
    ChangeRequestApprovedOrRejectedConfirmation
    BackgroundVerificationCompleted
    CountryComplianceEditRequestAdded
    CompensationVariablePayComponentsReminder
    OfflineToOnlineContractEmailToAdmin
    DowApprovedToUser
    DowRejectedToUser
}

enum FxSensitivity {
    LOW
    HIGH
}

# salary paying frequency for permanent members
enum PayFrequency {
    WEEKLY
    SEMIMONTHLY
    BIWEEKLY
    MONTHLY
    ANNUALLY
    SEMIANNUALLY
    QUARTERLY
    DAILY
    ONETIME
    HOURLY
}

enum LegalBindingType {
    MULTIPLIER
    PARTNER
    CLIENT
}

enum PayFrequencyDateIdentifier {
    PAYOUT_DATE
    FIRST_PAYOUT_DATE
    SECOND_PAYOUT_DATE
    PAYOUT_DAY
}

enum DayOfWeek {
    SUNDAY
    MONDAY
    TUESDAY
    WEDNESDAY
    THURSDAY
    FRIDAY
    SATURDAY
}

enum CompanyUserRole {
    PRIMARY_ADMIN
    ADMIN
    MANAGER
    PAYROLL_ADMIN
    BILLING_ADMIN
    HR_ADMIN
}

# Various data change statuses
enum DataChangeStatus {
    NOT_VIEWED,
    VIEWED
}


enum MSAWorkflowStep {
    GENERATE_MSA
    SEND_MSA
    SIGNING_MSA
}

enum MSAWorkflowStatus {
    MSA_GENERATION_PENDING
    MSA_GENERATED
    MSA_SENT_FOR_SIGNING
    MSA_SIGNED
}

enum FetchStage {
    CONTRACT_GENERATION         # Basic details collection stage for contract.
    MEMBER_LEGAL_DATA_CAPTURE   # Member on-boarding legal data collection stage.
}

# Define whether compliance requirement definitions are country specific or default.
enum ComplianceDefinitionScope {
    COUNTRY_SPECIFIC
    DEFAULT
}

# Define whether the legal data requirements apply to local only, expat only or all.
enum LegalDataRequirementApplicability {
    LOCAL_ONLY
    EXPAT_ONLY
    ALL
}

enum ComplianceParamDataFieldType {
    TEXT
    TOGGLE
    DROPDOWN
}

enum DomainType {
    LEGAL_DATA # Data required for Legal Data repos.
    MEMBER_DATA # Data required for member data repos.
    LEGAL_ENTITY_PAYROLL_BASIC_INFO
    LEGAL_ENTITY_PAYROLL_TAX
    LEGAL_ENTITY_PAYROLL_INSURANCE
    LEGAL_ENTITY_PAYROLL_BANK
    LEGAL_ENTITY_PAYROLL_CONTACT
    LEGAL_ENTITY_PAYROLL_DOCUMENT
}

enum CalculationPayableType {
    COMPANY
    EMPLOYEE
}

enum CompanyAssignTypeFilter {
    ALL
    ME
    UNASSIGNED
}

enum InvoiceStatus {
    DRAFT
    SUBMITTED
    AUTHORIZED
    DELETED
    VOIDED
    PAID
    PENDING
    OVERDUE
}

enum ContractOffboardingStatus {
    DRAFT
    INITIATED
    SHORTFALL_PAY_DETAILS_PENDING
    RESIGNATION_LETTER_PENDING
    FNF_DETAILS_PENDING
    FNF_PENDING
    FNF_SETTLED
    COMPLETED
    CANCELLED
}

enum ExpenseStatus {
    DRAFT                   # Expense is created, but not submitted
    REVOKED                 # DELETED
    APPROVAL_IN_PROGRESS    # Multi party approval - not now.
    APPROVED                # Approved by Company
    REJECTED                # Rejected by Company
    PROCESSING              # Selected for payment in the payroll
    PAID                    # Payment has been made.
    TO_BE_PAID              # should have been paid last cycle but was not paid, to be paid in next cycle
    ON_HOLD                 # Expense has an unresolved clarification
}

enum ExpenseView {
    ALL_PENDING             # Check if current user is included in allowed approvers or default approvers in any approver task
    PENDING_ON_ME           # Check if current user is included in allowed approvers or default approvers in active approver task
    HISTORY                 # Finalzed expenses such as approved, rejected, paid, etc.
}

enum TimeOffStatus {
    DRAFT
    SUBMITTED @deprecated(reason: "Time off was created. Don't use SUBMITTED. Use APPROVAL_IN_PROGRESS instead")
    DELETED
    APPROVAL_IN_PROGRESS    # Multi party approval
    APPROVED                # Approved by Company
    REJECTED                # Rejected by Company
    TAKEN
}

enum DeductionUnit {
    PERCENTAGE
    FIXED_AMOUNT_PER_MONTH
}

enum ContractBenefitStatus {
    DRAFT                    # status when benefit not confirmed by the member
    NEW                      # Default benefit status for new employee
    ACTIVE                   # Activated by adding Plan Tier by in the member insurance profile section
    ONBOARDING               # Plan details have been edited and added, but plan has not been activated yet
    OFFBOARDED               # Member has been terminated or contract end date is done
    DEACTIVATED              # Insurance plan has been removed for the member
    OPTED_OUT                # Member has opted out of the insurance plan
}


enum ContractBenefitDependentStatus {
    DELETED
    ACTIVE
    UNDER_REVIEW
    DEACTIVATED
}


enum ContractBenefitDocumentType {
    HEALTH_CARD
    OTHER
}

enum PaymentMethod {
    BANK_TRANSFER
    CARD
    CHEQUE
    CRYPTO
    ACH_DIRECT_DEBIT
    SEPA_DIRECT_DEBIT
    EGIRO_DIRECT_DEBIT
}

enum PaymentType {
    DIRECT_TRANSFER
}

enum CompanyUserStatus {
    CREATED
    INVITED
    ACTIVE
    DELETED
}

enum ContractOffboardingType {
    RESIGNATION
    TERMINATION
    DID_NOT_JOIN
}

enum SeverancePayOption {
    NOT_APPLICABLE
    NO_SEVERANCE
    ONE_WEEK
    TWO_WEEKS
    THREE_WEEKS
    FOUR_WEEKS
    ONE_MONTH
    TWO_MONTHS
    THREE_MONTHS
    SIX_MONTHS
}

##### PAYMENTS #####
enum TransferType {
    CRYPTO
    FIAT
}

enum PayInMethodType {
    ACH_DIRECT_DEBIT
    SEPA_DIRECT_DEBIT
    EGIRO_DIRECT_DEBIT
}

enum PayInMethodDefinitionStep {
    COLLECTING_BANK_DETAILS
    VERIFICATION
    DOCUMENT_SIGNING
    MANDATE_SUBMISSION
    ACTIVATION
    CANCEL
    DELETED
}

enum PaymentPartnerCode {
    WISE
    HSBC
    MUFG
    CITI
    PANCRETA
    REFFEISEN
    UNDEFINED_PARTNER
    ABS
    PLAID
    NIUM
}
##### PAYMENTS #####


##### This should be owned by FinX and is populated
##### as a result of order form's activation
##### An entity shouldn't have an offering code, but rather the "new" CompanyProductEntitlement module should be source for this
enum OfferingCode {
    GLOBAL_PAYROLL
    EOR
    AOR
    FREELANCER
    HRIS
    VALUE_ADDED_SERVICES
}

enum Relationship {
    SPOUSE
    DAUGHTER
    SON
    FATHER
    MOTHER
}

enum ExpenseSource {
    INTERNAL
    EXTERNAL
}

enum BulkOnboardingContext {
    GLOBAL_PAYROLL,
    HRIS_PROFILE_DATA,
    EOR,
    AOR,
    FREELANCER
}

enum TimeUnit {
    HOURS,
    MINUTES,
    DAYS,
    WEEKS,
    MONTHS,
    YEARS
}

enum BillingFrequency {
    ANNUALLY
    ONETIME
    SEMIANNUALLY
    QUARTERLY
    MONTHLY
    SEMIMONTHLY
    BIWEEKLY
    WEEKLY
    DAILY
    HOURLY
}

enum EntityType {
    COMPANY_ENTITY,
    EOR_PARTNER_ENTITY # can be multiplier entity or partner entity
}

enum PayrollEntityType {
    EOR
    EOR_PARTNER
    GLOBAL_PAYROLL
    PEO
    EOR_MULTIPLIER_PAYROLL
    EOR_PARTNER_PAYROLL
    CONTRACTOR_PAYROLL
}

enum EntityIDType {
    CONTRACT
}

enum DataSeedStatus {
    NOT_STARTED,
    STARTED,
    COMPLETED,
    FAILED
}

enum ContractingEntityType {
    MULTIPLIER_EOR_ENTITY,
    PARTNER_EOR_ENTITY,
    COMPANY_ENTITY,
}

enum BulkUploadGroup {
    ONBOARDING
    PAYROLL
    TIMEOFF
    TIMESHEET
    EXPENSE
    SALARY_REVISION
    DEDUCTION
    PAY_SUPPLEMENT
    INVOICE
    BACKFILL # temporary group for compensation data backfill
}

enum BulkUploadModule {
    GLOBAL_PAYROLL_MEMBER_ONBOARDING
    AOR_MEMBER_ONBOARDING
    FREELANCER_MEMBER_ONBOARDING
    EOR_MEMBER_ONBOARDING
    TIMEOFF
    TIMESHEET_BULK_UPLOAD_EMPLOYEE_DATA
    TIMESHEET_BULK_UPLOAD_ENTITY_SETUP_DATA
    EXPENSE
    SALARY_REVISION
    COMPENSATION_REVISION
    DEDUCTION
    PAY_SUPPLEMENT
    COMPENSATION_SCHEMA
    PAY_SCHEDULE
    EXPENSE_POLICY
    TIMEOFF_POLICY
    TIMESHEET_POLICY
    CONTRACTOR_INVOICE
    INVITE_MEMBER
    COMPENSATION_REVISION_BACKFILL
    COMPENSATION_BACKFILL
    TIMESHEET_EOR_BULK_UPLOAD_EMPLOYEE_DATA
}

enum BackgroundVerificationStatus {
    NOT_APPLICABLE
    PENDING
    PASSED
    FAILED
    INITIATED_BY_OPS
    NEW
}

enum SeverityLevel {
    ERROR
    WARN
    INFO
}
