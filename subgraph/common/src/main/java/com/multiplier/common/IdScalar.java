package com.multiplier.common;

import graphql.language.StringValue;
import graphql.schema.Coercing;
import graphql.schema.CoercingParseLiteralException;
import graphql.schema.CoercingParseValueException;
import graphql.schema.CoercingSerializeException;

/**
 * Refer https://netflix.github.io/dgs/scalars
 */
public class IdScalar implements Coercing<Long, String> {
    @Override
    public String serialize(Object dataFetcherResult) throws CoercingSerializeException {

        if (dataFetcherResult instanceof Long) {
            return ((Long) dataFetcherResult).toString();
        } else {
            throw new CoercingSerializeException("Not a valid Id(Long).");
        }
    }

    @Override
    public Long parseValue(Object input) throws CoercingParseValueException {
        try {
            return Long.parseLong(input.toString());
        } catch (NumberFormatException e) {
            throw new CoercingParseValueException("Value is not a valid ID(Long).", e);
        }
    }

    @Override
    public Long parseLiteral(Object input) throws CoercingParseLiteralException {

        if (input instanceof StringValue) {
            return Long.parseLong(((StringValue) input).getValue());
        }

        throw new CoercingParseLiteralException("Value is not a valid ID(Long).");
    }
}