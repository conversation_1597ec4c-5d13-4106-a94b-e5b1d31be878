package com.multiplier.common;

import graphql.language.StringValue;
import graphql.schema.Coercing;
import graphql.schema.CoercingParseLiteralException;
import graphql.schema.CoercingParseValueException;
import graphql.schema.CoercingSerializeException;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;

/**
 * Refer https://netflix.github.io/dgs/scalars
 */


public class DateScalar implements Coercing<LocalDate, String> {
    @Override
    public String serialize(Object dataFetcherResult) throws CoercingSerializeException {
        if (dataFetcherResult instanceof LocalDate) {
            return ((LocalDate) dataFetcherResult).toString();
        } else {
            throw new CoercingSerializeException("Not a valid Date");
        }
    }

    @Override
    public LocalDate parseValue(Object input) throws CoercingParseValueException {
        try {
            return LocalDate.parse(input.toString());
        } catch (DateTimeParseException e) {
            throw new CoercingParseValueException("Value is not a valid ISO date", e);
        }
    }

    @Override
    public LocalDate parseLiteral(Object input) throws CoercingParseLiteralException {
        if (input instanceof StringValue) {
            return LocalDate.parse(((StringValue) input).getValue());
        }

        throw new CoercingParseLiteralException("Value is not a valid ISO date");

    }
}
