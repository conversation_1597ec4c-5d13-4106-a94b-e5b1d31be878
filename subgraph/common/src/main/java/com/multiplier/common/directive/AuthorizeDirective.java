package com.multiplier.common.directive;

import com.netflix.graphql.dgs.context.DgsContext;
import graphql.language.ArrayValue;
import graphql.language.StringValue;
import graphql.schema.*;
import graphql.schema.idl.SchemaDirectiveWiring;
import graphql.schema.idl.SchemaDirectiveWiringEnvironment;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;

import java.nio.file.AccessDeniedException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Gorthi
 *
 * Directive implementation for {@code @authorize}
 * Please create a class like below in your spring app to wire in this directive
 *
 * @DgsDirective(name = "authorize")
 * public class AuthorizeDirectiveWiring extends AuthorizeDirective { }
 */
public class AuthorizeDirective implements SchemaDirectiveWiring {

    //TODO: Fix the header key
    private static final String PERMISSIONS_HEADER_NAME = "permissions";
    //TODO: Fix the header key
    private static final String EXPERIENCE_HEADER_NAME = "experience";

    public static final String DIRECTIVE_NAME = "authorize";

    @Override
    public GraphQLObjectType onObject(SchemaDirectiveWiringEnvironment<GraphQLObjectType> env) {
        GraphQLObjectType objectElement = env.getElement();
        GraphQLFieldsContainer fieldsContainer = env.getFieldsContainer();
        objectElement.getFieldDefinitions().forEach(
                graphQLFieldDefinition -> {
                    if (authDirectiveNotExists(graphQLFieldDefinition.getDirectives())) {
                        // applying object directives to fields only if field doesn't have the same authorize directive
                        DataFetcher<?> originalDataFetcher = env.getCodeRegistry().getDataFetcher(fieldsContainer, graphQLFieldDefinition);
                        DataFetcher<?> dataFetcher = addAuthorisationFunc(originalDataFetcher, env);
                        env.getCodeRegistry().dataFetcher(fieldsContainer, graphQLFieldDefinition, dataFetcher);
                    }
                }
        );
        return objectElement;
    }

    @Override
    public GraphQLFieldDefinition onField(SchemaDirectiveWiringEnvironment<GraphQLFieldDefinition> env) {
        GraphQLFieldsContainer fieldsContainer = env.getFieldsContainer();
        GraphQLFieldDefinition fieldDefinition = env.getFieldDefinition();

        if (fieldsContainer.getName().equalsIgnoreCase("Mutation")) {
            // applying different logic for mutation
            DataFetcher<?> originalDataFetcher = env.getCodeRegistry().getDataFetcher(fieldsContainer, fieldDefinition);
            DataFetcher<?> dataFetcher = addAuthorisationFuncForMutation(originalDataFetcher, env);
            env.getCodeRegistry().dataFetcher(fieldsContainer, fieldDefinition, dataFetcher);
        }

        DataFetcher<?> originalDataFetcher = env.getCodeRegistry().getDataFetcher(fieldsContainer, fieldDefinition);
        DataFetcher<?> dataFetcher = addAuthorisationFunc(originalDataFetcher, env);
        env.getCodeRegistry().dataFetcher(fieldsContainer, fieldDefinition, dataFetcher);

        return fieldDefinition;
    }

    /**
     * Function to add permission check logic
     * for a top level existing element
     * Contains no walking down the levels
     * @param originalDataFetcher dataFetcher to use
     * @param env env
     * @return dataFetcher ornated with authentication logic
     */
    private DataFetcher<?> addAuthorisationFunc(DataFetcher<?> originalDataFetcher, SchemaDirectiveWiringEnvironment<?> env) {
        return (DataFetcher<Object>) dfe -> {
            DgsContext context = dfe.getContext();
            HttpHeaders headers = context.getRequestData() != null
                    ? context.getRequestData().getHeaders()
                    : new HttpHeaders();
            Set<String> permissionsAllowed = getAllowedPermissions(headers);
            Optional<String> experience = getExperience(headers);
            validatePermissionCheck(experience, getPermissionsReqForDirective(env.getDirective(), experience), permissionsAllowed);
            return originalDataFetcher.get(dfe);
        };
    }

    /**
     * Method to add the authorisation check functionality
     * to the mutations which have two parts --
     * 1. top level - mutation permissions
     * 2. argument level - permissions for the arguments of the mutations
     * @param originalDataFetcher the data fetcher to use
     * @param env env
     * @return DataFetcher ornated with the authentication logic
     */
    private DataFetcher<?> addAuthorisationFuncForMutation(DataFetcher<?> originalDataFetcher, SchemaDirectiveWiringEnvironment<GraphQLFieldDefinition> env) {
        return (DataFetcher<Object>) dfe -> {
            DgsContext context = dfe.getContext();
            HttpHeaders headers = context.getRequestData() != null
                    ? context.getRequestData().getHeaders()
                    : new HttpHeaders();
            Set<String> permissionsAllowed = getAllowedPermissions(headers);
            Optional<String> experience = getExperience(headers);
            // Checks for field level permissions
            validatePermissionCheck(experience, getPermissionsReqForDirective(env.getDirective(), experience), permissionsAllowed);

            List<GraphQLArgument> arguments = env.getFieldDefinition().getArguments();
            if (!arguments.isEmpty()) {
                // Checks for argument and input level permissions
                // experience.get() will always return a value or else it would break the above permission check method
                validateForInputArguments(arguments, experience, permissionsAllowed);
            }
            return originalDataFetcher.get(dfe);
        };
    }

    /**
     * Utility method that does the final validation
     * of whether the authorize directive permissions
     * are satisfied by the context permissions
     * @param experience current experience
     * @param permissionsRequired permissions required by the directive
     * @param permissionsAllowed current allowed permissions for the context
     * @throws AccessDeniedException if required permissions are not satisfied
     */

    private static void validatePermissionCheck(Optional<String> experience, List<String> permissionsRequired, Set<String> permissionsAllowed) throws AccessDeniedException {
        if (experience.isEmpty()) {
            throw new AccessDeniedException("Experience cannot be empty!");
        }
        if (permissionsRequired.isEmpty()) {
            throw new AccessDeniedException("Permissions are missing for this experience");
        }
        if (!permissionsAllowed.containsAll(permissionsRequired)) {
            throw new AccessDeniedException("Insufficient Permissions");
        }
    }

    /**
     * Method to validate the input arguments
     * by walking down from the arguments of a mutation
     * Currently, doesn't use any DFS/BFS, improve it to use that.
     * Currently, walks down only one level, i.e, checks only the
     * top level InputType object and its fields, doesn't walk any further down
     * @param arguments arguments of the mutation
     * @param experience current experience
     * @param permissionsAllowed permissions of the current context
     * @throws AccessDeniedException if access denied
     */
    private static void validateForInputArguments(List<GraphQLArgument> arguments, Optional<String> experience, Set<String> permissionsAllowed) throws AccessDeniedException {
        // For all arguments
        for (GraphQLArgument argument : arguments) {
            // Getting the children of each argument which gives the input type object
            List<GraphQLSchemaElement> children = argument.getChildren();
            if (!children.isEmpty()) {
                GraphQLSchemaElement graphQLSchemaElement = children.get(0);
                // for every element of ONLY the first level elements of the main input type object
                for (GraphQLSchemaElement child : graphQLSchemaElement.getChildren()) {
                    GraphQLInputObjectField inputField = (GraphQLInputObjectField) child;
                    if (inputField.hasDirective(DIRECTIVE_NAME)) {
                        List<String> permissionsReqForDirective = getPermissionsReqForDirective(inputField.getDirective(DIRECTIVE_NAME), experience);
                        // If directive is configured and the experience lacks permissions, it rejects by default
                        if (permissionsReqForDirective.isEmpty()) {
                            throw new AccessDeniedException("Permissions are missing for this experience");
                        }
                        // If directive is configured and the required permissions are missing, it rejects by default
                        if (!permissionsAllowed.containsAll(permissionsReqForDirective)) {
                            throw new AccessDeniedException("Insufficient Permissions");
                        }
                    }
                }
            }
        }
    }

    /**
     * Method to get the permissions required
     * for a given directive
     * @param directive authorize directive
     * @param experience current user experience
     * @return permissions required according to the current experience
     */
    @NotNull
    private static List<String> getPermissionsReqForDirective(GraphQLDirective directive, Optional<String> experience) {
        if (experience.isEmpty()) {
            return Collections.emptyList();
        }

        GraphQLArgument permissionsList = directive.getArgument(experience.get());
        ArrayValue intermediaryVal = (ArrayValue) permissionsList.getArgumentValue().getValue();
        if (intermediaryVal == null) {
            return List.of();
        }

        return intermediaryVal
                .getValues()
                .stream()
                .map(v -> (StringValue) v)
                .map(StringValue::getValue)
                .collect(Collectors.toList());
    }

    /**
     * Method to get the experience requested
     * for the current context
     * @param headers all spring headers
     * @return experience if present
     */
    private Optional<String> getExperience(HttpHeaders headers) {
        if (headers == null) {
            return Optional.empty();
        }
        return headers.getFirst(EXPERIENCE_HEADER_NAME) != null ? Optional.of(headers.getFirst(EXPERIENCE_HEADER_NAME)) : Optional.empty();
    }

    /**
     * Method to get the allowed permissions for the
     * user using the header value from the context
     * @param headers all headers
     * @return values of allowed permissions
     */
    private Set<String> getAllowedPermissions(HttpHeaders headers) {
        if (headers == null) {
            return Set.of();
        }
        String permissionString = headers.getFirst(PERMISSIONS_HEADER_NAME) != null ? headers.getFirst(PERMISSIONS_HEADER_NAME) : "";
        return Arrays.stream(permissionString.split(",")).collect(Collectors.toSet());
    }

    /**
     * Method to check if the directive exists
     * on a graphql field/type, etc
     * from a given set of applied directives
     * @param directives all applied directives
     * @return true if authorize directive exists
     */
    private boolean authDirectiveNotExists(List<GraphQLDirective> directives) {
        if (directives.isEmpty()) {
            return true;
        }
        return directives.stream().noneMatch(d -> d.getName().equalsIgnoreCase(DIRECTIVE_NAME));
    }
}
