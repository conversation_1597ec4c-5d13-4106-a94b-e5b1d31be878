package com.multiplier.common.constraints;

import graphql.GraphQLError;
import graphql.Scalars;
import graphql.schema.GraphQLAppliedDirective;
import graphql.schema.GraphQLInputType;
import graphql.validation.constraints.AbstractDirectiveConstraint;
import graphql.validation.constraints.Documentation;
import graphql.validation.rules.ValidationEnvironment;
import graphql.validation.util.Util;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ValidEmailDirectiveConstraint extends AbstractDirectiveConstraint {

    private static final String EMAIL_REGEX = "(?:[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+" +
            "(?:\\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"" +
            "(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")" +
            "@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.)" +
            "{3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:" +
            "(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21-\\x5a\\x53-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])+)\\])";

    private static final Logger log = LoggerFactory.getLogger(ValidEmailDirectiveConstraint.class);

    private static final Pattern EMAIL_PATTERN = Pattern.compile(EMAIL_REGEX);

    public ValidEmailDirectiveConstraint() {
        super("ValidEmail");
    }

    @Override
    protected boolean appliesToType(final GraphQLInputType graphQLInputType) {
        GraphQLInputType unwrappedType = Util.unwrapNonNull(graphQLInputType);
        return Scalars.GraphQLString.equals(unwrappedType);
    }

    @Override
    protected List<GraphQLError> runConstraint(final ValidationEnvironment validationEnvironment) {
        Object validatedValue = validationEnvironment.getValidatedValue();
        GraphQLAppliedDirective directive = validationEnvironment.getContextObject(GraphQLAppliedDirective.class);

        Map<String, Object> params = mkMessageParams(validatedValue, validationEnvironment);

        if (validatedValue instanceof String) {
            final String input = validatedValue.toString().toLowerCase();

            if (!isValidEmail(input)) {
                log.error("Invalid email: {}", input);
                return mkError(validationEnvironment, directive, params);
            }
        }

        return List.of();
    }

    protected boolean isValidEmail(final String validatedValue) {
        Matcher matcher = EMAIL_PATTERN.matcher(validatedValue);
        return matcher.matches();
    }

    @Override
    protected boolean appliesToListElements() {
        return true;
    }

    @Override
    public Documentation getDocumentation() {
        return Documentation.newDocumentation()
                .messageTemplate(getMessageTemplate())
                .description("Verifies if given email input is valid.")
                .applicableTypeNames(Scalars.GraphQLString.getName())
                .directiveSDL(
                        "directive @ValidEmail(message : String = \"%s\") " + "on INPUT_FIELD_DEFINITION",
                        getMessageTemplate())
                .build();
    }

}
