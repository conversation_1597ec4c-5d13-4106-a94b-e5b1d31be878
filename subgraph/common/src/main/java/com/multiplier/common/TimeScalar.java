package com.multiplier.common;

import graphql.language.StringValue;
import graphql.schema.Coercing;
import graphql.schema.CoercingParseLiteralException;
import graphql.schema.CoercingParseValueException;
import graphql.schema.CoercingSerializeException;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeParseException;

/**
 * Refer https://netflix.github.io/dgs/scalars
 */


public class TimeScalar implements Coercing<LocalTime, String> {
    @Override
    public String serialize(Object dataFetcherResult) throws CoercingSerializeException {
        if (dataFetcherResult instanceof LocalTime) {
            return ((LocalTime) dataFetcherResult).toString();
        } else {
            throw new CoercingSerializeException("Not a valid time");
        }
    }

    @Override
    public LocalTime parseValue(Object input) throws CoercingParseValueException {
        try {
            return LocalTime.parse(input.toString());
        } catch (DateTimeParseException e) {
            throw new CoercingParseValueException("Value is not a valid ISO time", e);
        }
    }

    @Override
    public LocalTime parseLiteral(Object input) throws CoercingParseLiteralException {
        if (input instanceof StringValue) {
            return LocalTime.parse(((StringValue) input).getValue());
        }

        throw new CoercingParseLiteralException("Value is not a valid ISO time");

    }
}
