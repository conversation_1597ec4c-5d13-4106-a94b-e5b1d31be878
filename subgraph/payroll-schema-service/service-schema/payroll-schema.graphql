extend type Query {
    componentCategories : [ComponentCategory!]! @authorize(operations: ["view.operations.payroll-component"], partner: ["upload.partner.payrollreport"])
    partnerPayrollSchema(input: PartnerPayrollSchemaInput!) : PartnerPayrollSchema! @authorize(operations: ["view.operations.payroll-component"])
    partnerPayrollSchemaReport(input: PartnerPayrollSchemaInput!) : DocumentReadable! @authorize(operations: ["view.operations.payroll-component"])
}

extend type Mutation {
    categoryBulkUpload(input: Upload!): BulkUploadResponse! @authorize(operations: ["update.operations.payroll-component"])
    partnerComponentBulkUpload(input: Upload!) : BulkUploadResponse! @authorize(operations: ["update.operations.payroll-component"])
    upsertPartnerSchema(request: UpsertPartnerSchemaRequest!): PartnerPayrollSchema! @authorize(partner: ["upload.partner.payrollreport"])
}

input PartnerPayrollSchemaInput {
    partnerId: ID!
    country: CountryCode!
}

type PartnerPayrollSchema {
    components: [PartnerPayrollComponent!]!
}

type ComponentCategory {
    name: String!
    type: String!
    description: String
}

type PartnerPayrollComponent {
    id: UUID!
    partnerId : ID!
    country: CountryCode!
    name: String!
    category: String!
    displayName: String!
    description: String
    mandatory: Boolean
}

type BulkUploadResponse {
    success: Boolean!
    errors: [String!]
}

input UpsertPartnerSchemaRequest {
    partnerId : ID!
    country: CountryCode!
    components: [PartnerPayrollComponentInput!]!
}

input PartnerPayrollComponentInput {
    name: String!
    category: String!
    displayName: String!
    isActive: Boolean!
    description: String
    mandatory: Boolean
}
