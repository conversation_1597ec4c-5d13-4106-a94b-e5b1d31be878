plugins {
    alias(libs.plugins.kotlin.jvm)

    alias(libs.plugins.dgs.codegen).version("6.1.5")

    id("maven-publish")
}

dependencies {
    // Project
    api(projects.subgraph.common)

    // Serialization
    implementation(libs.jackson.annotations)
}

val serviceSchemaPath: String by rootProject.extra
val generatedResourcesPath: String by rootProject.extra
val generatedSchemaPath: String by rootProject.extra

// TODO delete this back as soon as services are split
val mergeScript = file("../merge-service-with-sub-graphs.js")
val mergeServiceWithSubGraphs = tasks.register<Exec>("mergeServiceWithSubGraphs") {
    group = "graph"

    inputs.file(mergeScript)
    inputs.dir(serviceSchemaPath)
    inputs.dir(projects.subgraph.common.dependencyProject.layout.projectDirectory.dir(serviceSchemaPath))

    val mergeProjects = listOf(
        projects.subgraph.payrollInputService,
    )

    val mergeProjectNames = mergeProjects.map {
        inputs.dir(it.dependencyProject.layout.projectDirectory.dir(serviceSchemaPath))
        it.name
    }

    outputs.dir(layout.buildDirectory.dir(generatedSchemaPath))

    commandLine("node", mergeScript, *mergeProjectNames.toTypedArray())
}

val mergeSubgraph = tasks.named("mergeSubgraph")
tasks.withType<ProcessResources> {
    dependsOn(mergeSubgraph)
    from(layout.buildDirectory.dir(generatedResourcesPath))
}

tasks.withType<com.netflix.graphql.dgs.codegen.gradle.GenerateJavaTask> {
    dependsOn(mergeSubgraph)

    schemaPaths = mutableListOf(layout.buildDirectory.dir(generatedSchemaPath).get())
    packageName = "com.multiplier.graph"
    generateClient = false
    typeMapping = mutableMapOf(
        "ID" to "kotlin.Long",
        "Upload" to "org.springframework.web.multipart.MultipartFile",
        "Date" to "java.time.LocalDate",
        "Time" to "java.time.LocalTime",
        "DateTime" to "java.time.LocalDateTime",
        "UUID" to "java.util.UUID",
    )
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    compilerOptions {
        jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17
    }
    kotlinOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

plugins.withType<JavaPlugin> {
    configure<JavaPluginExtension> {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
}
