# Reference: https://www.notion.so/usemultiplier/Scheduled-Payouts-1f9f9e1ba7a7804b903dc9c58155a07d

extend type Query {
    # Get all payout schedules for a company
    getPayoutSchedules(companyId: ID!): [PayoutSchedule!]! @authorize(company: ["view.company.payout-schedules"], member: ["view.member.payout-schedules"])

    # Get a specific payout schedule by ID
    getPayoutScheduleById(scheduleId: ID!): PayoutSchedule @authorize(company: ["view.company.payout-schedule"], member: ["view.member.payout-schedule"])

    # Get payout schedule runs for a schedule
    getPayScheduleRunsByScheduleId(scheduleId: ID!): [PayoutScheduleRun!]! @authorize(company: ["view.company.pay-schedule-runs-by-schedule-id"])

    # Get payout timeline based on a specific date or frequency
    getPayScheduleTimelineByDate(payoutDate: Date, scheduleId: ID, frequency: PayoutFrequency): PayoutTimeline! @authorize(company: ["view.company.pay-schedule-timeline-by-date"])

    # Get contracts assigned to a specific payout schedule
    getContractsForPayoutSchedule(scheduleId: ID!): [PayoutScheduleContract!]! @authorize(company: ["view.company.contracts-for-payout-schedule"])
}

extend type Mutation {
    # Create a new payout schedule
    createPayoutSchedule(input: CreatePayoutScheduleInput!): PayoutSchedule! @authorize(company: ["create.company.payout-schedule"])

    # Update an existing payout schedule
    updatePayoutSchedule(input: UpdatePayoutScheduleInput!): PayoutSchedule! @authorize(company: ["update.company.payout-schedule"])

    # Delete a payout schedule
    deletePayoutSchedule(scheduleId: ID!): PayoutSchedule! @authorize(company: ["delete.company.payout-schedule"])

    # Assign a contract to a payout schedule
    assignScheduleToContract(contractId: ID!, scheduleId: ID!): PayoutScheduleContract! @authorize(company: ["assign.company.schedule-to-contract"])

    # Unassign a contract from its payout schedule
    unassignScheduleFromContract(contractId: ID!): PayoutScheduleContract! @authorize(company: ["unassign.company.schedule-from-contract"])

    # Bulk assign contracts to a payout schedule
    bulkAssignScheduleToContracts(contractIds: [ID!]!, scheduleId: ID!): [PayoutScheduleContract!]! @authorize(company: ["bulk-assign.company.schedule-to-contracts"])
}

type PayoutSchedule {
    id: ID!
    companyId: ID!
    name: String!
    frequency: PayoutFrequency!
    payoutDates: [PayoutDateConfig!]!
    isAutoInvoiceEnabled: Boolean
    autoInvoiceDate: [Int!]
    payoutScheduleContracts: [PayoutScheduleContract]
    createdAt: DateTime!
    updatedAt: DateTime!
}

type PayoutScheduleContract {
    id: ID!
    scheduleId: ID!
    payoutAmount: Float # computed amount based on billing rate, frequency and schedule payout frequency
    payoutCurrency: CurrencyCode
    contract: Contract @provides(fields: "id")
    createdAt: DateTime!
    updatedAt: DateTime!
}

enum PayoutFrequency {
    MONTHLY
    SEMI_MONTHLY
    EVERY_TWO_WEEKS
}

type PayoutScheduleRun {
    id: ID!
    scheduleId: ID!
    description: String # Month and date description
    invoices: [MemberPayable]
}

type PayoutDateConfig {
    dates: [Int!]  # For MONTHLY, SEMI_MONTHLY (e.g., [1, 16] for 1st and 16th)
    weekday: Int   # For EVERY_TWO_WEEKS (0-6, Sunday-Saturday)
}

type PayoutTimeline {
    invoiceGenerationDate: Int!
    invoiceApprovalByDate: Int!
    payInDate: Int!
    payoutDate: Int!
}

input CreatePayoutScheduleInput {
    companyId: ID!
    frequency: PayoutFrequency!
    payoutDates: [PayoutDateConfigInput!]!
    isAutoInvoiceEnabled: Boolean
    autoInvoiceDate: [Int!]
}

input UpdatePayoutScheduleInput {
    id: ID!
    isAutoInvoiceEnabled: Boolean
    autoInvoiceDate: [Int!]
}

input PayoutDateConfigInput {
    dates: [Int!]
    weekday: Int
}
