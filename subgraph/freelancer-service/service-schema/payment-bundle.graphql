extend type Query {
    paymentBundles(filters: PaymentBundleFilters): [PaymentBundle] @authorize(company: ["view.company.payment.bundle"], operations: ["view.operations.payment.bundle"])
    paymentBundlePreview(items: [PaymentBundlePreviewItemInput]): PaymentBundlePreview @authorize(company: ["view.company.payment.bundle"])
}

extend type Mutation {
    paymentBundleCreate(input: PaymentBundleCreateInput!): PaymentBundle @authorize(company: ["create.company.payment.bundle"], operations: ["create.operations.payment.bundle"])
    paymentBundleDelete(id: ID!): PaymentBundle @authorize(company: ["delete.company.payment.bundle"], operations: ["delete.operations.payment.bundle"])
    paymentBundleUpdate(input: PaymentBundleUpdateInput): PaymentBundle @authorize(company: ["update.company.payment.bundle"])   # can only update bundle payment method
    paymentBundleMarkAsProcessing(id: ID!, paymentAdvices: [Upload!]): PaymentBundle @authorize(company: ["update.company.payment.bundle"])
    paymentBundleMarkAsPaid(id: ID!): PaymentBundle @authorize(company: ["update.company.payment.bundle"])  # use only for ad hoc requests. There is no UI associated with this
}

input PaymentBundlePreviewItemInput {
    id: ID!
    type: PaymentBundleItemType!
}

type PaymentBundlePreview {
    items: [PaymentBundlePreviewItem!]!
    totalAmount: Float
    currency: CurrencyCode
}

type PaymentBundlePreviewItem {
    item: PaymentBundlePreviewItemPayable
    type: PaymentBundleItemType

    # For EOR members, billing amount = companyPayable.totalAmount
    # For freelancers, billing amount = amount convert into the company billing currency
    # The filed value will get calculated by BE.
    billingAmount: Float

    # For EOR, management fee will be zero (0).
    # For others (eg: freelancers), relevant management fee will be calculated by BE.
    managementFee: Float

    # For EOR, payoutProcessingFee fee will be zero (0).
    # For others (eg: freelancers), relevant payoutProcessingFee fee will be calculated by BE.
    payoutProcessingFee: Float

    externalId: ID  # memberPayable id or companyPayable id
}

union PaymentBundlePreviewItemPayable = CompanyPayable | FreelancerPayable | ExpenseMemberPayable | PaySupplementMemberPayable

input PaymentBundleFilters {
    paymentBundleId: ID
    companyId: ID
    referenceId: String
    createdDateRange: DateRange
    """Whether bundles of test companies should be returned. By default only non-test are respected"""
    includesTestCompanies: Boolean = false
    sourceType: PaymentBundleSourceType
}

enum PaymentBundleItemType {
    COMPANY_PAYABLE
    FREELANCER_PAYABLE
}

type PaymentBundleItem {
    id: ID
    companyPayable: CompanyPayable
    type: PaymentBundleItemType # logical field, values calculated by BE (not in DB)
}

enum PaymentBundleStatus {
    CREATED         # bundle created not yet paid
    PROCESSING      # client paid for at least one invoice in the bundle
    DELETED         # bundle deleted by user
    PAID            # client paid for all the invoices inside the bundle
}

enum PaymentBundleSourceType {
    COMPANY_EXPERIENCE
    AUTO_DEBIT_SERVICE
}

type PaymentBundle {
    id: ID!
    company: Company
    items: [PaymentBundleItem!]!
    status: PaymentBundleStatus
    paymentMethodInfo: PaymentMethodInfo
    totalAmount: Float
    currency: CurrencyCode
    referenceId: String         # Ops will find the "payin" with this ref id and verify OR the system will auto-detect the payin
    bankAccount: BankAccount    # Multiplier Bank account details depending on the selected country and currency
    payInInfo: PayInInfo        # Credentials required for non-bank transfer payment method
    instructionFile: DocumentReadable
    paymentAdvices: [DocumentReadable!]
    createdOn: DateTime
    updatedOn: DateTime
    sourceType: PaymentBundleSourceType
    paymentBundlePayInDetail: PaymentBundlePayInDetail
}

type PaymentBundlePayInDetail {
    id: ID!
    reconciliationDate: DateTime
}

input PaymentBundleCreateInput {
    paymentMethod: PaymentMethod!
    bundleItems: [PaymentBundleItemInput!]
    payInMethodId: ID
    companyId: ID
    source: PaymentBundleSourceType = COMPANY_EXPERIENCE
}

input PaymentBundleItemInput {
    id: ID!
    type: PaymentBundleItemType!
}

input PaymentBundleUpdateInput {
    id: ID!
    paymentMethod: PaymentMethod!
    payInMethodId: ID
}
