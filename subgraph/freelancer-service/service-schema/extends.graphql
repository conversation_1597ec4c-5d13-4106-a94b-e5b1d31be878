type PayInInfo @key(fields: "id") @extends {
    id: ID! @external
}

type CompanyPayable @key(fields: "id") @extends {
    id: ID! @external
    type: CompanyPayableType @external
    memberPayableCompanyInvoice: MemberPayableCompanyInvoice @requires(fields: "type")
}

# This enum is currently used in company service & freelancer service only.
# Because of that we defined this enum in company graph & freelancer graph ONLY (instead of common graph).
enum CompanyPayableType {
    FIRST_INVOICE
    SECOND_INVOICE
    FREELANCER          # Every company_payable of this "FREELANCER" type has a 1-1 mapping with payment.member_payable_company_invoice, via company_payable_id (FK), the others don't
    DEPOSIT
    UNKNOWN
    INSURANCE
    GLOBAL_PAYROLL_FUNDING
    ANNUAL_PLAN
    ANNUAL_PLAN_AOR
    GP_SERVICE_INVOICE
    VAS_INCIDENT_INVOICE
    ORDER_FORM_ADVANCE
    PAYROLL_OFFCYCLE_INVOICE
    VAS_BACKGROUND_VERIFICATION_INVOICE
}

type Contract @key(fields: "id") @extends {
    id: ID @external

    """
    MemberPayable list can filter by:
    1. id: memberPayable id
    2. status: memberPayable status
    3. fromDate: checks if payableFromDate greater than or equal to given date.
    4. toDate: checks if payableToDate less than or equal to given date.
    5. referenceId: company invoice referenceId, checks for referenceId starting with given value
    6. createdDateRange: checked against memberPayable createdOn date
    """
    memberPayables(id: ID, status: MemberPayableStatus, fromDate: Date, toDate: Date, referenceId: String, createdDateRange: DateRange, payoutDateRange: DateRange, externalId: ID, type: MemberPayableType, paymentMethod: TransferType): [MemberPayable]
}

type Company @key(fields: "id") @extends {
    id: ID @external
    memberPayables(id: ID, statuses: [MemberPayableStatus!], types: [MemberPayableType!]): [MemberPayable]
    memberPayableCompanyInvoices(id: ID, reference: String): [MemberPayableCompanyInvoice]
    paymentBundles(id: ID, statuses: [PaymentBundleStatus!]!): [PaymentBundle]        # BE will return all bundles if status list is empty
}

type Expense @key(fields: "id") @extends {
    id: ID @external
}

type PaySupplement @key(fields: "id") @extends {
    id: ID @external
}

type CompanyUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type Member implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type OperationsUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type Configuration @key(fields: "id") @extends {
    id: ID @external
    bankTransferFee(companyId: ID): Float   # Returns bank transfer fee for the given company id. If no company id is given, returns the default bank transfer fee.
}

type InvoiceDetail @key(fields: "companyPayableId") @extends {
    companyPayableId: ID! @external
    invoicePaymentBundleDetails : InvoicePaymentBundleDetails @requires(fields: "companyPayableId")
}

type CompanyPayable @extends {
    id: ID! @external
    paymentBundle: PaymentBundle @requires(fields: "id")
}

type ContractPaymentAccountV2 @key(fields: "id") {
    id: ID
    paymentAccountId: ID
    configurations: ContractPaymentAccountConfigV2
}

type ContractPaymentAccountConfigV2 {
    preferredPayOutType: TransferType
}
