# Inputs

type PaymentMethodInfo {
    bundleId: ID!
    paymentMethod: PaymentMethod
    fee: Float  @deprecated # to be replaced by processingFee
    processingFee: ProcessingFee
    processingFeePercentage: Float # Payment method's processing fee percentage value
    payInMethodId: ID # the id of the method in Company -> payInMethods
    autoDebitInfo: AutoDebitInfo
}

type AutoDebitInfo {
    id: ID!
    scheduledDebitDate: Date
}

type ProcessingFee {
    feeTotal: Float
    feeTypes: [FeeType]  # Breakdown of total fee if any (ex: swift fee, partner fee)
}

type FeeType {
    name: String
    amount: Float
    metadata: [FeeTypeMetadata]
}

type FeeTypeMetadata {
    key: String
    value: String
}