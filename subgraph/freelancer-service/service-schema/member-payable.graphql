# Please refer to https://www.notion.so/usemultiplier/Payment-faf6e8577c0c47618efeb822b9998b26
# for the full documentation on this domain.

extend type Mutation {
    memberPayableSubmit(input: MemberPayableSubmitInput!): MemberPaya<PERSON> @authorize(member: ["create.member.memberpayable"], operations: ["create.operations.memberpayable"])
    memberPayableUpdate(id: ID!, input: MemberPayableUpdateInput!): MemberPayable @authorize(member: ["update.member.memberpayable"], operations: ["update.operations.memberpayable"], company: ["update.company.memberpayable"])
    memberPayableDelete(id: ID!): MemberPayable @authorize(member: ["delete.member.memberpayable"])
    memberPayableGeneration(generateSecondHalfOnly: Boolean=false): TaskResponse @authorize(operations: ["generate.operations.memberpayable"])
    memberPayableUpdateBulkAsPaid(ids: [ID]!): MemberPayableUpdateBulkResponse @authorize(operations: ["update.operations.memberpayable.status"])
    memberPayableUpdateBulkAsRejected(input: [MemberPayableRejectInput]!): [MemberPayable] @authorize(company: ["approve.company.memberpayable", "update.company.memberpayable.status"], operations: ["update.operations.memberpayable.status"])
    memberPayableUpdateBulkAsApproved(ids: [ID!]!): [MemberPayable] @authorize(company: ["approve.company.memberpayable", "update.company.memberpayable.status"], operations: ["update.operations.memberpayable.status"]) # approval flow - 85zrgyyqv
    memberPayableUpdateForOps(input: MemberPayableUpdateForOpsInput!): MemberPayable @authorize(operations: ["update.operations.memberpayable"])
    memberPayableGenerationForFailedAutoInvoice(contractIds: [ID]!): [MemberPayable] @authorize(operations: ["create.operations.memberpayable"])
    # CompanyId or/and contractId can be used to pause/resume auto generation.
    # In the case of where a contractId was given along with its companyId, companyId will always be prioritized.
    # Cases:
    # Auto generation will be paused for all the contracts for the provided companyId if that id is blacklisted.
    # If companyId is not blacklisted, but the contractId is in the blacklist, the auto generation will be paused only for that contract.
    # Note: To resume generation for a contract, make sure that contractId and its companyId are not in the blacklist (Turn ON generation for both).
    memberPayableToggleAutoGeneration(state: MemberPayableAutoGenerationState!, companyIds: [ID], contractIds: [ID]): TaskResponse @authorize(operations: ["generate.operations.memberpayable"])

    """
    To generate the invoices, triggered by customer
    If an invoice is already created, it is skipped
    The invoice must be raised for the same member payable invoice items
    Case:
    Company user may not complete the payment for the invoice and stop midway. Hence the invoice is created and kept in draft until revisited
    """
    memberPayableCompanyInvoiceSubmit(input: MemberPayableCompanyInvoiceSubmitInput!): MemberPayableCompanyInvoice @authorize(company: ["submit.company.memberpayable.invoice"])
    memberPayableCompanyInvoiceCreatePayIn(id: ID!): PayInInfo @authorize(company: ["submit.company.memberpayable.invoice"])  # Create a pay-in in payment service and return pay-in details.
    memberPayableCompanyInvoiceConfirmPayment(id: ID!): [MemberPayable] @authorize(company: ["update.company.memberpayable.invoice.status", "update.company.memberpayable.status"], operations: ["update.operations.memberpayable.status"])
    memberPayableCompanyInvoiceMarkAsReadyForPayouts(id: ID!): MemberPayableCompanyInvoice @authorize(operations: ["view.operations.memberpayable"])
    sendInvoiceReminderNotificationToCustomer: TaskResponse @authorize(operations: ["trigger.operations.memberpayable.reminders"])
    sendInvoiceReminderNotificationToMember: TaskResponse @authorize(operations: ["trigger.operations.memberpayable.reminders"])

    bulkUpdateCompanyInvoiceStatus(companyInvoiceIds: [ID]!, status: MemberPayableCompanyInvoiceStatus!): TaskResponse @authorize(operations: ["update.operations.memberpayable"])
    bulkUploadMemberPayableExtraFees(file: Upload!): TaskResponse @authorize(operations: ["upload.operations.extrafees-file"])

    """
    For Finance Ops to initiate the payouts after the payins are confirmed received.<br>
    Ideally FE should send ids of valid member-payables (i.e. the status must be PAY_IN_RECEIVED, the contract must have paymentAccountId etc...)<br>
    BE will validate again and exclude invalid member-payables from the process.<br>

    ids: the member payable ids to be initiated
    """
    memberPayableBulkInitiatePayout(ids: [ID!]!): TaskResponse @authorize(operations: ["initiate.operations.memberpayable.payout"])

#    Auto Invoice Config Mutations
    upsertAutoInvoiceCompanyConfig(input: [AutoInvoiceCompanyConfigInput]!): [AutoInvoiceCompanyConfig] @authorize(company: ["update.company.auto-invoice-company-config"])
    upsertAutoInvoiceContractConfig(input: AutoInvoiceContractConfigInput!): AutoInvoiceContractConfig @authorize(company: ["update.company.auto-invoice-contract-config"])
    bulkUpsertAutoInvoiceSettings(input: BulkAutoInvoiceContractConfigInput!): [AutoInvoiceContractConfig] @authorize(company: ["update.company.bulk-update-auto-invoice-contract-config"])
    manualTriggerAutoInvoiceGeneration: TaskResponse @authorize(operations: ["generate.operations.memberpayable"])
    revokeApprovedContractInvoice(input: RevokeApprovedContractInvoiceInput!): ContractInvoiceConfig @authorize(company: ["revoke.company.auto-invoice"], member: ["revoke.member.auto-invoice"])
    #Timesheet Based AutoInvoice Mutation
    createInvoiceForTimesheet(timesheetId: ID!, isCreateInvoiceToggleEnable: Boolean): FreelancerPayable @authorize(company: ["update.company.generate-timesheet-invoice"], member: ["update.member.generate-timesheet-invoice"])
    dataMigrationForAutoInvoice(input: DataMigrateForAutoInvoiceInput): TaskResponse @authorize(operations: ["generate.operations.memberpayable"])
    persistCreateInvoiceToggle(contractId: ID!, isCreateInvoiceToggleEnable: Boolean): TaskResponse @authorize(company: ["update.company.generate-timesheet-invoice"])
}

extend type Query {
    memberPayableReport(filters: MemberPayableReportFilter): DocumentReadable @authorize(operations: ["export.operations.memberpayable"])
    memberPayableCompanyInvoices(filters: MemberPayableCompanyInvoiceFilters): MemberPayableCompanyInvoiceResult @authorize(operations: ["view.operations.contract"])
    memberPayableSummary : MemberPayableSummary @authorize(operations: ["view.operations.memberpayable"])
    #AutoInvoice Queries
    autoInvoiceFixedRateContracts(companyId: ID!) : [AutoInvoiceContractConfig] @authorize(company: ["view.company.auto-invoice-contract-config"], operations: ["view.operations.auto-invoice-contract-config"], member: ["view.member.auto-invoice-contract-config"])
    autoInvoiceConfigForContract(contractId: ID!): AutoInvoiceContractConfig @authorize(company: ["view.company.auto-invoice-contract-config"], member: ["view.member.auto-invoice-contract-config"], operations: ["view.operations.auto-invoice-contract-config"])
    autoInvoiceCompanyConfig(companyId: ID!): [AutoInvoiceCompanyConfig] @authorize(company: ["view.company.auto-invoice-company-config"])
    #Timesheet Based AutoInvoice Queries
    invoiceDetailsBasedOnTimesheet(timesheetId: ID!): InvoiceGenerationDetails @authorize(company: ["view.company.timesheet-invoice-details"], member: ["view.member.timesheet-invoice-details"])
    timesheetAndInvoiceDetails(timesheetId: [ID], invoiceId: [ID]): [TimesheetInvoiceDetails] @authorize(company: ["view.company.timesheet-and-invoice-mapping"], operations: ["view.operations.timesheet-and-invoice-mapping"], member: ["view.member.timesheet-and-invoice-mapping"])
}

input MemberPayableInvoiceSourceReportExportInput {
    companyId: ID
    payableMonth: PayableMonthInput # selected payroll month
    companyIds: [ID!]
}

input InvoiceSourceReportHashInput {
    hash: String!
}

input CompanyInvoiceSourceReportInput {
    companyIds: [ID!]
    monthYearCycle: MonthYearCycleInput # selected payroll month
    """
    which invoice the data should be sourced from
    1 = First Invoice
    2 = Second Invoice
    """
    invoiceType: Int
}

input DataMigrateForAutoInvoiceInput {
    companyIds: [ID]
    contractIds: [ID]
}

input MonthYearCycleInput {
    month: Int
    year: Int
    cycle: Int
}

input PayableMonthInput {
    month: Int
    year: Int
    cycle: Int
}

type MemberPayableSummary {
    payInReceivedTotalCount: Int
    failedPayOutTotalCount: Int
    payOutInitiatedTotalCount: Int
    payOutInitiatedCount(pendingDays: Int = 3): Int # member payable count where PAYOUT_INITIATED more than x days ago and still in PAYOUT_INITIATED status
}

input MemberPayableRejectInput {
    id: ID!
    reason: String!               #Reason for rejection
    additionalComment: String     #Additional comment
}

input MemberPayableUpdateInput {
    payablePeriod: PeriodInput
    title: String
    dueDate: Date @deprecated # due date is never updated after its set in creation
    description: String
    duration: Float           # number of days/hours (RateFrequency) employee worked
    items: [MemberPayableItemInput]!
    changeReason: String
}

input MemberPayableUpdateForOpsInput {
    id: ID!
    status: MemberPayableStatus
    useCase: MemberPayableUpdateForOpsUseCase
    changeReason: String
    ticket: String
}

input MemberPayableSubmitInput {
    id: ID # The memberPayable id, optional. If null -> a new memberPayable; not null -> an existing REJECTED one
    contractId: ID            # Optional for MEMBER. Can get from JWT token for MEMBER experience. null -> currentUser is member; non-null -> currentUser is creating a memberPayable on behalf of the `contractId`
    payablePeriod: PeriodInput
    title: String
    dueDate: Date @deprecated # due date is calculated in BE and set. hence no need of FE to send
    description: String
    duration: Float           # number of days/hours (RateFrequency) employee worked
    items: [MemberPayableItemInput]!
}

input PeriodInput {
    startDate: Date
    endDate: Date
}

input MemberPayableReportFilter {
    countryCodes: [CountryCode!]!
    contractIds: [ID!]!
    companyIds: [ID!]!
    memberName: String
    paymentStatuses: [MemberPayableStatus!]!
    createdDateRange: DateRange
    payoutDateRange: DateRange
    payablePeriod: PeriodInput
    memberPayableIds: [ID!]!
    companyInvoiceReferenceId: String
    externalId: ID
    type: MemberPayableType
    paymentMethod: TransferType
}

input MemberPayableCompanyInvoiceFilters {
    id: ID
    companyId: ID
    generatedDateRange: DateRange
    invoiceStatus: MemberPayableCompanyInvoiceStatus
    invoiceNumber: String
    referenceId: String
    payInMethod: PaymentMethod
}

type MemberPayableUpdateBulkResponse {
    success: Boolean
    message: String
    updateItemCount: Int
}

enum MemberPayableAutoGenerationState {
    ON
    OFF
}

enum MemberPayableUpdateForOpsUseCase {
    STATUS_UPDATE
}

input MemberPayableItemInput {
    id: ID
    description: String!
    amount: Float!
    quantity: Float
    rate: Float
    payablePeriod: PeriodInput
    # null, [], [null], [null,null]...will be considered "no docs/files". Current UIUX seems to support one file only.
    # so, don't put more than one file for now.
    supportingDocs: [Upload] @deprecated(reason: "Not in use since when the `custom invoice upload` feature was dropped in Feb 2022")
}

# This represents a claim against an employing company for work done during a period.
interface MemberPayable @key(fields: "id") {
    id: ID!
    type: MemberPayableType
    payableFromDate: Date
    payableToDate: Date
    dueDate: Date
    contract: Contract
    items: [MemberPayableItem]
    title: String
    description: String
    status: MemberPayableStatus
    changes(latest: Boolean = true, status: MemberPayableStatus): [MemberPayableChange] @deprecated(reason: "use payoutInitiatedAt, payoutCompletedAt, rejectedAt etc...instead")
    currency: CurrencyCode                              # Currency of the invoice, should be the same as Contract.
    totalAmount: Float
    totalInFunctionalCurrency: Float                    # #The amount in the currency of the company that is going to process this.
    submittedAt: DateTime
    payInReceivedOn: DateTime
    payoutInitiatedAt: DateTime
    payoutCompletedAt: DateTime
    payOutType: TransferType
    createdOn: DateTime
    updatedOn: DateTime
    createdBy: ID                                       # this is platform_user.id
    updatedBy: ID                                       # this is platform_user.id
    changeReason: String                                # rejection reason. Show this when status=REJECTED
    rejectedAt: DateTime                                # when it was rejected. Show this when status=REJECTED
    memberPayableInvoices(status: MemberPayableCompanyInvoiceStatus): [MemberPayableCompanyInvoice]
    file(documentType: MemberPayableDocumentType = INVOICE @deprecated(reason: "Redundant, BE does not use it. REMITTANCE_ADVICE related logic is not in use")): FileLink  # child data fetcher to download a member payable (PDF for now).
    paymentReceipt: DocumentReadable    # Receipt generated from partner (Wise, etc)
    payOutInfo: [MemberPayablePayOutInfo]
    memberPayableInvoiceWorkflow: InvoiceWorkFlow
}

# The implementation for freelancers that raise such payable requests
type FreelancerPayable implements MemberPayable @key(fields: "id") {
    id: ID!
    type: MemberPayableType
    payableFromDate: Date
    payableToDate: Date
    dueDate: Date
    title: String
    description: String
    contract: Contract
    items: [MemberPayableItem]
    status: MemberPayableStatus
    changes(latest: Boolean = true, status: MemberPayableStatus): [MemberPayableChange] @deprecated(reason: "use payoutInitiatedAt, payoutCompletedAt, rejectedAt etc...instead")
    currency: CurrencyCode                              # Currency of the invoice.
    totalAmount: Float
    totalInFunctionalCurrency: Float                    # #The amount in the currency of the company that is going to process this.
    submittedAt: DateTime
    payInReceivedOn: DateTime
    payoutInitiatedAt: DateTime
    payoutCompletedAt: DateTime
    payOutType: TransferType
    createdOn: DateTime
    updatedOn: DateTime
    createdBy: ID                                       # this is platform_user.id
    updatedBy: ID                                       # this is platform_user.id
    approvers: [CompanyUser]
    isApprovable: Boolean
    changeReason: String                                # rejection reason. Show this when status=REJECTED
    comment: String                                     # additional comment for rejection
    rejectedAt: DateTime                                # when it was rejected. Show this when status=REJECTED
    """Expense has its own approval flow, and PaySupplement doesn't even have one. So, adding approvedAt to here only and not the interface"""
    approvedAt: DateTime
    durationValue: Float                                # number of days/hours (RateFrequency) employee worked
    durationFrequency: RateFrequency                    # rate frequency used when invoice was created
    memberPayableInvoices(status: MemberPayableCompanyInvoiceStatus): [MemberPayableCompanyInvoice]
    file(documentType: MemberPayableDocumentType = INVOICE @deprecated(reason: "Redundant, BE does not use it. REMITTANCE_ADVICE related logic is not in use")): FileLink  # child data fetcher to download a member payable (PDF for now).
    paymentReceipt: DocumentReadable                    # Receipt generated from partner (Wise, etc)
    payOutInfo: [MemberPayablePayOutInfo]
    memberPayableInvoiceWorkflow: InvoiceWorkFlow
}

type ExpenseMemberPayable implements MemberPayable @key(fields: "id") {
    id: ID!
    expense: Expense
    type: MemberPayableType
    payableFromDate: Date
    payableToDate: Date
    dueDate: Date
    title: String
    description: String
    contract: Contract
    items: [MemberPayableItem]
    status: MemberPayableStatus
    changes(latest: Boolean = true, status: MemberPayableStatus): [MemberPayableChange] @deprecated(reason: "use payoutInitiatedAt, payoutCompletedAt, rejectedAt etc...instead")
    currency: CurrencyCode
    totalAmount: Float
    totalInFunctionalCurrency: Float
    submittedAt: DateTime
    payInReceivedOn: DateTime
    payoutInitiatedAt: DateTime
    payoutCompletedAt: DateTime
    payOutType: TransferType
    createdOn: DateTime
    updatedOn: DateTime
    createdBy: ID
    updatedBy: ID
    approvers: [CompanyUser]
    isApprovable: Boolean
    changeReason: String
    comment: String
    rejectedAt: DateTime
    durationValue: Float
    durationFrequency: RateFrequency
    memberPayableInvoices(status: MemberPayableCompanyInvoiceStatus): [MemberPayableCompanyInvoice]
    file(documentType: MemberPayableDocumentType = INVOICE @deprecated(reason: "Redundant, BE does not use it. REMITTANCE_ADVICE related logic is not in use")): FileLink
    paymentReceipt: DocumentReadable                    # Receipt generated from partner (Wise, etc)
    payOutInfo: [MemberPayablePayOutInfo]
    memberPayableInvoiceWorkflow: InvoiceWorkFlow
}

type PaySupplementMemberPayable implements MemberPayable @key(fields: "id") {
    id: ID!
    paySupplement: PaySupplement
    type: MemberPayableType
    payableFromDate: Date
    payableToDate: Date
    dueDate: Date
    title: String
    description: String
    contract: Contract
    items: [MemberPayableItem]
    status: MemberPayableStatus
    changes(latest: Boolean = true, status: MemberPayableStatus): [MemberPayableChange] @deprecated(reason: "use payoutInitiatedAt, payoutCompletedAt, rejectedAt etc...instead")
    currency: CurrencyCode
    totalAmount: Float
    totalInFunctionalCurrency: Float
    submittedAt: DateTime
    payInReceivedOn: DateTime
    payoutInitiatedAt: DateTime
    payoutCompletedAt: DateTime
    payOutType: TransferType
    createdOn: DateTime
    updatedOn: DateTime
    createdBy: ID
    updatedBy: ID
    approvers: [CompanyUser]
    isApprovable: Boolean
    changeReason: String
    comment: String
    rejectedAt: DateTime
    durationValue: Float
    durationFrequency: RateFrequency
    memberPayableInvoices(status: MemberPayableCompanyInvoiceStatus): [MemberPayableCompanyInvoice]
    file(documentType: MemberPayableDocumentType = INVOICE @deprecated(reason: "Redundant, BE does not use it. REMITTANCE_ADVICE related logic is not in use")): FileLink
    paymentReceipt: DocumentReadable                    # Receipt generated from partner (Wise, etc)
    payOutInfo: [MemberPayablePayOutInfo]
    memberPayableInvoiceWorkflow: InvoiceWorkFlow
}

type MemberPayableItem {
    id: ID
    date: Date
    description: String
    amount: Float
    amountInFunctionalCurrency: Float                   # The amount in the currency of the company that is going to process this.
    supportingDocs: [DocumentReadable] @deprecated(reason: "Not in use since when the `custom invoice upload` feature was dropped in Feb 2022")
    payableFromDate: Date
    payableToDate: Date
    quantity: Float
    rate: Float
}

enum MemberPayableType {
    FREELANCER @deprecated
    INVOICE
    EXPENSE
    PAY_SUPPLEMENT
}

type InvoiceWorkFlow {
    allSteps: [InvoiceWorkflowStepDefinition]
    currentStep: InvoiceWorkflowStepDefinition
}

type InvoiceWorkflowStepDefinition {
    step: InvoiceWorkflowStep
    statuses: [MemberPayableStatus]
    currentStatus: MemberPayableStatus
    data: [InvoiceStepData]
}

type InvoiceStepData {
    key: String
    value: String
    type: String
}

enum InvoiceWorkflowStep {
    INVOICE_CREATED,
    INVOICE_PAYMENT_INITIATED,
    INVOICE_PAYMENT_RECEIVED,
    INVOICE_PAYOUT_INITIATED,
    INVOICE_PAID
}

enum MemberPayableStatus {
    DRAFT                   # MemberInvoice is created, but not submitted. We currently don't use this
    REVOKED                 # DELETED
    APPROVAL_IN_PROGRESS    # Multi party approval - not now.
    APPROVED                # Approved by Company
    REJECTED                # Rejected by Company. Can be submitted again from this status
    PROCESSING              # Selected for payment in the payroll
    PAID                    # Payment has been made.
    UNPAID                  # MemberInvoice is created, Use in freelancer invoice
    PAYMENT_INITIATED       # Confirmed payIn & payOut initiated
    PAY_IN_PROCESSING
    PAY_IN_RECEIVED
    PARTIAL_PAY_IN_RECEIVED
    PAYOUT_INITIATED
    PAYOUT_FAILED
    PAYOUT_COMPLETED
    CREATED                 # Added to show the status for PaySupplement when it is approved.
}

"""Deprecated. This was created for REMITTANCE_ADVICE feature but remittance advice feature is not in use"""
enum MemberPayableDocumentType {
    REMITTANCE_ADVICE @deprecated
    INVOICE @deprecated
}

type MemberPayableChange {
    status: MemberPayableStatus
    actionedBy: Person
    actionedOn: DateTime
    reason: String
}

type MemberPayableCompanyInvoice {
    id: ID!
    invoiceNumber: String      # String to uniquely identify the invoice
    referenceId: String           # Generated by the backend for payment reference purpose
    bankAccount: BankAccount!     # Multiplier Bank account depending on the selected country and currency
    company: Company
    totalAmount: Float     # Total amount of the invoice including the transaction fee and the sum of the selected payables.
    billingCurrency: CurrencyCode
    paymentMethod: PaymentMethodInfo # Selected Payment method from the enum BANK_TRANSFER/CARD/CHEQUE and the fee. This corresponds to payin only
    invoiceStatus: MemberPayableCompanyInvoiceStatus
    """@deprecated: No usage found in backend codebase"""
    experienceStatus: MemberPayableCompanyExperienceStatus @deprecated

    memberPayableInvoiceItems: [MemberPayableCompanyInvoiceItem]
    paymentReceipt: DocumentReadable    # Receipt generated from partner (Wise, etc)
    document(format: MemberPayableCompanyInvoiceFormat = PDF): DocumentReadable  # Format of the document to be downloaded
    paidOn:  DateTime
    bankTransferFee: Float
    submittedAt: DateTime
    createdOn: DateTime
    updatedOn: DateTime
    createdBy: ID
    updatedBy: ID
    memberPayableInvoiceWorkflow: InvoiceWorkFlow
    payInInfo: PayInInfo # populate when create pay-in
    payoutPaymentMethodInfo: [PaymentMethodInfo]

    # Boolean flag to indicate whether this freelancer invoice is ready for payouts.
    # Someone from the finance team will intentionally mark this invoice as ready for payouts.
    # The relevant Xero invoice status may still be in UNPAID status, but we need to start the payout process
    # for all the freelancers in this invoice.
    isReadyForPayouts: Boolean

    # With the new "Payment View" use-case, we create company payable for freelancers also.
    # So this will be set (not-null) for all the new "member payable company invoices created "after" the payment view feature is released.
    # And object will be "null" for all the old ones (member payable company invoices created "before" payment view feature).
    companyPayable: CompanyPayable
}

type MemberPayableCompanyInvoiceResult {
    data: [MemberPayableCompanyInvoice]
}

type MemberPayablePayOutInfo {
    paymentPartner: String
    paidOn: DateTime
    transactionId: String
}

type MemberPayableCompanyInvoiceItem {
    memberPayable: MemberPayable
    billingAmount: Float
    managementFee: Float
}

type MemberPayableAmount {
    currency: CurrencyCode
    amount: Float
}

enum MemberPayableCompanyInvoiceStatus {
    VOID
    DRAFT
    PROCESSING

    """when the payin is received but the amount doesn't match. Similar to MemberPayableStatus.PARTIAL_PAY_IN_RECEIVED"""
    PARTIALLY_PAID

    """when the payin is received without any mismatch"""
    COMPLETED
}

"""@deprecated: No usage found in backend codebase"""
enum MemberPayableCompanyExperienceStatus {
    UNPAID
    PAYMENT_PROCESSING
    PAYMENT_INITIATED
    PAID
}

enum MemberPayableCompanyInvoiceFormat {
    PDF
    CSV
}

enum MemberPayableExtraFeeType {
    FLAT_FEE
    VARIABLE_FEE
    SWIFT_FEE
    CRYPTO_FEE
}

input MemberPayableCompanyInvoiceSubmitInput {
    paymentMethod: PaymentMethod = BANK_TRANSFER
    memberPayableIds: [ID]!
}

type CompanyInvoiceSourceReportResult {
    company: Company
    invoiceSourceReport: [CompanyCountryInvoiceSourceReport]
    companyTotalBillingAmount: Float
}

type CompanyCountryInvoiceSourceReport {
    country: CountryCode,
    items: [CompanyInvoiceSourceItem]
    companyCountryBillingTotal: Float
}

type CompanyInvoiceSourceItem {
    id: ID
    contract: Contract
    currency: CurrencyCode
    billingCurrency: CurrencyCode
    amountGross: Float
    contributions: [CompanyPayComponent]
    deductions: [CompanyPayComponent]
    additional: [CompanyPayComponent]

    totalContributionAmount: Float
    totalDeductionsAmount: Float
    totalExpenseAmount: Float
    totalSupplementAmount: Float
    totalAllowanceAmount: Float
    totalBonusAmount: Float
    totalCommissionAmount: Float

    firstInvoiceGrossSalary: Float
    firstInvoiceUnitPrice: Float
    secondInvoiceGrossSalary: Float
    secondInvoiceUnitPrice: Float

    aggregatedGross: Float
    amountTotalCost: Float

    fxRateApplied: Float
    totalBillingAmount: Float
}

type CompanyPayComponent {
    name: String
    value: Float
    type: CompanyPayComponentType
}

enum CompanyPayComponentType {
    CONTRIBUTIONS
    DEDUCTIONS,
    OTHER
}
#Auto Invoice Configs
type AutoInvoiceContractConfig {
    id: ID!
    companyId: ID!
    contractId: ID!
    isEnabled: Boolean!
    payFrequency: PayFrequency
    invoiceGenerationDates: [Int]
    customInvoiceAmount: Float
    customInvoiceCurrency: CurrencyCode
    invoiceDescription: String
}

type AutoInvoiceCompanyConfig {
    id: ID!
    companyID: ID!
    isEnabled: Boolean!
    payFrequency: PayFrequency!
    invoiceGenerationDates: [Int]
}

input AutoInvoiceCompanyConfigInput {
    companyID: ID!
    isEnabled: Boolean!
    payFrequency: PayFrequency!
    invoiceGenerationDates: [Int]
}

input AutoInvoiceContractConfigInput {
    contractId: ID!
    isEnabled: Boolean!
    payFrequency: PayFrequency
    invoiceGenerationDates: [Int]
    customInvoiceAmount: Float
    customInvoiceCurrency: CurrencyCode
    invoiceDescription: String
}

input BulkAutoInvoiceContractConfigInput {
    autoConfigContracts: [AutoInvoiceContractConfigInput]!
}

input RevokeApprovedContractInvoiceInput {
    contractId: ID
    invoiceId: ID!
}

type ContractInvoiceConfig {
    contractId: ID
    invoiceId: ID!
    status: MemberPayableStatus!
}

#Timesheet Based Auto Invoice Configs
type TimesheetInvoiceDetails {
    id: ID
    contractId: ID
    timesheetId: ID
    invoiceId: ID
    modifiedBy: ID
    modifiedOn: DateTime
}

type InvoiceGenerationDetails {
    billingAmount: Float
    billingCurrency: CurrencyCode
    billingFrequency: RateFrequency
    totalDays: Int
    totalHours: Float
    invoiceAmount: Float
    isCreateInvoiceToggleEnable: Boolean
}
