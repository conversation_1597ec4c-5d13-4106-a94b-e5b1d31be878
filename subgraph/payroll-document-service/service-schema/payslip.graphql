extend type Query {
    payslipDocuments(filter: PayslipFilter! , pageRequest: PageRequest): PayslipsPaginated! @authorize(operations: ["use.operations.payslips"])
    companyPayrollCyclePayslipPublishRules(companyPayrollCycleId: ID!): AccessibilityResponse @authorize(company: ["update.company.payslip-workflow"])
}

extend type Mutation {
    payrollCyclePayslipPublish(value: PayrollPayslipPublishInput!): PublishPayslipResponse @authorize(operations: ["publish.operations.payslip"])
    companyPayrollCyclePayslipPublish(companyPayrollCycleId: ID!): PublishPayslipResponse @authorize(company: ["update.company.payslip-workflow"])
    generatePayslipZip(payrollCycleIds: [ID!]!): TaskResponse @authorize(operations: ["update.operations.payslip"])
    generatePayrollCyclePayslipContracts(payrollCycleId: ID!): TaskResponse @authorize(operations: ["update.operations.payslip"])
}

input PayslipFilter {
    payslipIds: [UUID!]
    payrollCycleIds: [ID!]
    contractIds: [ID!]
    fromDate: Date
    toDate: Date
    month: MonthYearInput
    companyIds: [ID!]
    countries: [CountryCode!]
    entityTypes: [PayrollEntityType!]
    statuses: [PayslipDocumentStatus!]
}

type PayslipsPaginated {
    payslips: [ContractPayslip!]!
    pageResult: PageResult!
}

type ContractPayslip {
    id: UUID!
    payrollCycleId: ID! @deprecated(reason: "Use payrollCycle instead")
    payrollCycle: PayrollCycle! @authorize(company: ["view.company.payroll-cycle"], operations: ["view.operations.payroll-cycle"], member: ["use.member.payroll"])
    country: CountryCode!
    contract: Contract!
    memberName: String!
    fileName: String
    downloadUrl: String  @authorize(company: ["view.company.payslip"], operations: ["use.operations.payslips"], member: ["view.member.payslip"])
    monthYear: MonthYear!
    status: PayslipDocumentStatus!
}

input PayrollPayslipPublishInput {
    payrollCycleId: ID!
    contractIds: [ID!]
}

type PayslipCountByStatus {
    status: PayslipDocumentStatus!
    count: Int!
}

type PayslipDocumentSummary {
    count: [PayslipCountByStatus!]!
}

type PublishPayslipResponse {
    summary: PayslipDocumentSummary!
}
