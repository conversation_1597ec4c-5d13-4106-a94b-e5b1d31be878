extend type Mutation {
    payrollDocumentDemoDataUpsert(input: PayrollDocumentDemoDataUpsertInput!): PayrollDocumentDemoDataUpsertResponse! @authorize(operations: ["use.operations.payslips"])
}

input PayrollDocumentDemoDataUpsertInput {
    contractCustomPayrollDocuments: [ContractCustomPayrollDocumentInput!]
    contractPayCycleDocuments: [ContractPayCycleDocumentInput!]
    payslipDocuments: [PayslipDocumentInput!]
}

input ContractCustomPayrollDocumentInput {
    documentType: PayrollDocumentType!
    contractId: ID!
    contractName: String!
    customDocumentConfigId: UUID!
    country: String!
    status: ContractCustomPayrollDocumentStatus
    fileName: String
    absolutePath: String
    financialYearFrom: Date!
    financialYearTo: Date!
    yearMonth: MonthYearInput
    quarter: Int
    companyId: ID!
    payrollEntityType: PayrollEntityType!
}

input ContractPayCycleDocumentInput {
    documentType: PayrollDocumentType!
    payrollCycleId: ID!
    contractId: ID!
    contractName: String!
    country: String!
    status: ContractPayCycleDocumentStatus
    fileName: String
    absolutePath: String
    yearMonth: MonthYearInput!
    companyId: ID!
    payrollEntityType: PayrollEntityType!
}

input PayslipDocumentInput {
    payrollCycleId: ID!
    contractId: ID!
    contractName: String!
    status: PayslipDocumentStatus
    fileName: String
    absolutePath: String
    country: String!
    payrollEntityType: PayrollEntityType!
    yearMonth: MonthYearInput!
    companyId: ID!
}

type PayrollDocumentDemoDataUpsertResponse {
    contractPayCycleDocuments: [ContractPayCycleDocument!]
    contractCustomPayrollDocuments: [ContractCustomPayrollDocument!]
    payslipDocuments: [ContractPayslip!]
}
