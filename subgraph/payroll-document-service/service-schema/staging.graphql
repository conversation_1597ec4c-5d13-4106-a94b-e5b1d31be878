input PayrollDocumentStagingFilter {
    groupId: UUID!
    statuses: [PayrollDocumentStagingStatus!]!
}

input AssignPayrollDocumentRequest {
    contractId: ID!
    stagingIds: [UUID!]!
}

input UnassignPayrollDocumentRequest {
    groupId: UUID!
    contractId: ID!
}

type PayrollDocumentStagingsPaginated {
    groupId: UUID!
    group: PayrollDocumentGroup!
    stagings: [PayrollDocumentStaging!]!
    pagination: PaginationMetaData!
}

