extend type Query {
    companyReportDownload(request: CompanyReportDownloadRequest!): CompanyReportDownloadResponse! @authorize(company: ["view.company.payroll-report"])
}

input CompanyReportDownloadRequest {
    companyPayrollCycleId: ID!
    reportType: [PayrollDocumentType!]!
}

type PayrollDocumentReport {
    id: UUID!
    payrollCycleId: ID!
    type: PayrollDocumentType!
    fileName: String!
    downloadUrl: String
}

type CompanyReportDownloadResponse {
    reportList: [PayrollDocumentReport!]!
}
