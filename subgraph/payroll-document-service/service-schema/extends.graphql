type PayrollCycle @key(fields: "id") @extends {
    id: ID! @external
    payslipDocumentsSummary: PayslipDocumentSummary  @authorize(operations: ["use.operations.payroll-report"], company: ["view.company.payslip"])
    contractPayCycleDocumentsSummary(documentType: PayrollDocumentType!): ContractPayCycleDocumentsSummary @authorize(operations: ["use.operations.payroll-report"], company: ["view.company.payslip"])
}

type Contract @key(fields: "id") @extends {
    id: ID @external
    payslipDocuments(filter: PayslipFilter!): [ContractPayslip!]  @authorize(company: ["view.company.payslip"], operations: ["view.operations.payslip"], member: ["view.member.payslip"])
    contractPayCycleDocuments(filter: ContractPayCycleDocumentFilter!): [ContractPayCycleDocument!]  @authorize(company: ["view.company.payslip"], operations: ["view.operations.payslip"], member: ["view.member.payslip"])
    contractCustomPayrollDocuments(filter: ContractCustomPayrollDocumentFilter!): [ContractCustomPayrollDocument!]  @authorize(company: ["view.company.payslip"], operations: ["view.operations.payslip"], member: ["view.member.payslip"])
}

type Company @key(fields: "id") @extends {
    id: ID @external
}
