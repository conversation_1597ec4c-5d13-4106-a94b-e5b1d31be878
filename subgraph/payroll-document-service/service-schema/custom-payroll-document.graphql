extend type Query {
    contractCustomPayrollDocuments(filter: ContractCustomPayrollDocumentFilter! , pageRequest: PageRequest): ContractCustomPayrollDocumentsPaginated!  @authorize(operations: ["use.operations.payroll-report"])
}

input ContractCustomPayrollDocumentFilter {
    documentType: PayrollDocumentType!
    financialYearFrom: Date
    financialYearTo: Date
    month: MonthYearInput
    quarter: Int
    contractIds: [ID!]
    companyIds: [ID!]
    countries: [CountryCode!]
    entityTypes: [PayrollEntityType!]
    statuses: [ContractCustomPayrollDocumentStatus!]
    customDocumentConfigId: UUID
}

type ContractCustomPayrollDocumentsPaginated {
    documents: [ContractCustomPayrollDocument!]!
    pageResult: PageResult!
}

type ContractCustomPayrollDocument {
    id: UUID!
    documentType: PayrollDocumentType!
    country: CountryCode!
    contract: Contract!
    memberName: String!
    fileName: String
    downloadUrl: String  @authorize(operations: ["use.operations.payroll-report"], member: ["view.member.payslip"])
    status: ContractCustomPayrollDocumentStatus!
    financialYearFrom: Date
    financialYearTo: Date
    month: MonthYear
    quarter: Int
    customPayrollDocumentConfig: CustomPayrollDocumentConfig
}
