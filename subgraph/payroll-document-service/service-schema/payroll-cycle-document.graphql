extend type Query {
    contractPayCycleDocuments(filter: ContractPayCycleDocumentFilter! , pageRequest: PageRequest): ContractPayCycleDocumentsPaginated!  @authorize(operations: ["use.operations.payroll-report"])
}

extend type Mutation {
    contractCompanyPayCycleDocumentsPublish(companyPayrollCycleId: ID!) : PublishContractPayCycleDocumentsResponse @authorize(operations: ["view.company.payroll-report"], company: ["update.company.payslip-workflow"])
}

input ContractPayCycleDocumentFilter {
    documentType: PayrollDocumentType!
    payrollCycleIds: [ID!]
    contractIds: [ID!]
    month: MonthYearInput @deprecated(reason: "Use fromDate and toDate instead")
    fromDate: Date
    toDate: Date
    companyIds: [ID!]
    countries: [CountryCode!]
    entityTypes: [PayrollEntityType!]
    statuses: [ContractPayCycleDocumentStatus!]
}


type ContractPayCycleDocumentsPaginated {
    documents: [ContractPayCycleDocument!]!
    pageResult: PageResult!
}

type ContractPayCycleDocument {
    id: UUID!
    documentType: PayrollDocumentType!
    payrollCycleId: ID! @deprecated(reason: "Use payrollCycle instead")
    payrollCycle: PayrollCycle! @authorize(company: ["view.company.payroll-cycle"], operations: ["view.operations.payroll-cycle"], member: ["use.member.payroll"])
    country: CountryCode!
    contract: Contract!
    memberName: String!
    fileName: String
    downloadUrl: String  @authorize(operations: ["use.operations.payroll-report"], member: ["view.member.payslip"])
    status: ContractPayCycleDocumentStatus!
    monthYear: MonthYear!
}

type ContractPayCycleDocumentCountByStatus {
    status: ContractPayCycleDocumentStatus!
    count: Int!
}

type ContractPayCycleDocumentsSummary {
    count: [ContractPayCycleDocumentCountByStatus!]!
}

type PublishContractPayCycleDocumentsResponse {
    summary: ContractPayCycleDocumentsSummary!
}
