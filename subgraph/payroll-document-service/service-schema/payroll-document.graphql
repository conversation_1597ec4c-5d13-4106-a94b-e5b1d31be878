extend type Query {
    payrollDocumentUploadConfig(filter: PayrollDocumentUploadConfigFilter!): PayrollDocumentUploadConfig @authorize(operations: ["use.operations.payslips"])

    payrollDocumentStagings(input: PayrollDocumentStagingFilter!, pagination: PaginationInput): PayrollDocumentStagingsPaginated! @authorize(operations: ["use.operations.payslips"])
    assignedPayrollDocumentStagings(groupId: UUID!, contractId: ID!): [PayrollDocumentStaging!]! @authorize(operations: ["use.operations.payslips"])

    payrollDocumentGroup(filter: PayrollDocumentGroupFilter!): PayrollDocumentGroup @authorize(operations: ["use.operations.payslips"])
    payrollDocumentGroupById(documentGroupId: UUID!): PayrollDocumentGroup! @authorize(operations: ["use.operations.payslips"])
    payrollDocumentGroups(filter: PayrollDocumentGroupFilter!, pageRequest: PageRequest): PayrollDocumentGroupsPaginated! @authorize(operations: ["use.operations.payslips"])

    payrollDocumentContracts(documentGroupId: UUID!): [PayrollDocumentContract!]! @authorize(operations: ["use.operations.payslips"])

    customPayrollDocumentConfigs(filter: CustomPayrollDocumentConfigFilter!): [CustomPayrollDocumentConfig!]! @authorize(operations: ["use.operations.payslips"])
}

extend type Mutation {
    payrollDocumentUploadConfigUpsert(input: PayrollDocumentUploadConfigInput!): PayrollDocumentUploadConfig! @authorize(operations: ["use.operations.payslips"])

    payrollDocumentGroupCreate(input: PayrollDocumentGroupInput!): PayrollDocumentGroup! @authorize(operations: ["use.operations.payslips"])
    payrollDocumentsUpload(request: UploadPayrollDocumentRequest!): UploadPayrollDocumentResponse! @authorize(operations: ["use.operations.payslips"])
    processPayrollDocument(input: ProcessPayrollDocumentRequest!): PayrollDocumentGroup! @authorize(operations: ["use.operations.payslips"])

    assignPayrollDocument(request: AssignPayrollDocumentRequest!): [PayrollDocumentStaging!]! @authorize(operations: ["use.operations.payslips"])
    unassignPayrollDocument(request: UnassignPayrollDocumentRequest!): [PayrollDocumentStaging!]! @authorize(operations: ["use.operations.payslips"])
    confirmPayrollDocuments(groupId: UUID!): TaskResponse @authorize(operations: ["use.operations.payslips"])
    deleteUnassignedPayrollDocuments(groupIds: [UUID!]!): TaskResponse @authorize(operations: ["use.operations.payslips"])

    customPayrollDocumentConfigUpsert(input: CustomPayrollDocumentConfigInput!): CustomPayrollDocumentConfig! @authorize(operations: ["use.operations.payslips"])
}

input PayrollDocumentGroupFilter {
    type: PayrollDocumentType!
    payrollCycleId: ID
    entityType: PayrollEntityType
    country: CountryCode
    financialYearFrom: Date
    financialYearTo: Date
    entityId: ID
    frequency: PayrollDocumentFrequency
}

type PayrollDocumentContract {
    contractId: ID!
    name: String!
    status: PayrollDocumentStagingStatus!
}

type PayrollDocumentGroupsPaginated {
    page: PageResult
    groups: [PayrollDocumentGroup!]!
}

input CustomPayrollDocumentConfigFilter {
    country: CountryCode!
    type: PayrollDocumentType!
    frequency: PayrollDocumentFrequency!
}

input CustomPayrollDocumentConfigInput {
    country: CountryCode!
    type: PayrollDocumentType!
    documentCode: String!
    documentName: String!
    frequency: PayrollDocumentFrequency!
    status: CustomPayrollDocumentConfigStatus!
}
