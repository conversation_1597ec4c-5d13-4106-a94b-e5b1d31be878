input UploadPayrollDocumentRequest {
    groupId: UUID!
    documents: [PayrollDocumentInput!]!
}

input ReUploadPayrollDocumentRequest {
    documentGroupId: UUID!
    documents: [PayrollDocumentInput!]!
}

input PayrollDocumentGroupInput {
    type: PayrollDocumentType!
    payrollCycleId: ID
    entityType: PayrollEntityType
    financialYearFrom: Date
    financialYearTo: Date
    country: CountryCode
    entityId: ID
    companyId: ID
    month: MonthYearInput
    quarter: Int
    customPayrollDocumentConfigId: UUID
}

input PayrollDocumentInput {
    fileName: String!
    contentType: String
}

input ProcessPayrollDocumentRequest {
    groupId: UUID!
    configId: UUID!
}

type UploadPayrollDocumentResponse {
    group: PayrollDocumentGroup!
    documents: [PayrollDocumentData!]!
}

input PayrollDocumentUploadConfigFilter {
    documentType: PayrollDocumentType!
    country: CountryCode!
    partnerId: ID
}

input PayrollDocumentUploadConfigInput {
    country: CountryCode!
    partnerId: ID
    documentType: PayrollDocumentType!
    uploadType: PayrollDocumentUploadType!
    fileType: PayrollDocumentFileType!
}

type PayrollDocumentUploadConfig {
    id: UUID!
    country: CountryCode!
    partnerId: ID
    documentType: PayrollDocumentType!
    uploadType: PayrollDocumentUploadType!
    fileType: PayrollDocumentFileType!
}
