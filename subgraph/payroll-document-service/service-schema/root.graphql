type PayrollDocumentGroup {
    id: UUID!
    type: PayrollDocumentType!
    status: PayrollDocumentGroupStatus!
    payrollCycleId: ID @deprecated(reason: "Use payrollCycle instead")
    payrollCycle: PayrollCycle
    entityType: PayrollEntityType
    financialYearFrom: Date
    financialYearTo: Date
    country: CountryCode
    entityId: ID @deprecated(reason: "Use legalEntity instead")
    legalEntity: LegalEntity
    companyId: ID @deprecated(reason: "Use company instead")
    company: Company
    month: MonthYear
    quarter: Int
    customPayrollDocumentConfig: CustomPayrollDocumentConfig
}

type PayrollDocumentData {
    id: UUID!
    uploadUrl: String @authorize(operations: ["use.operations.payslips"])
    fileName: String!
}

type PayrollDocumentStaging {
    id: UUID!
    fileName: String!
    status: PayrollDocumentStagingStatus!
    pageNumber: Int!
    contractId: ID
    downloadUrl: String @authorize(operations: ["use.operations.payslips"])
}

type CustomPayrollDocumentConfig {
    id: UUID!
    country: CountryCode!
    type: PayrollDocumentType!
    documentCode: String!
    documentName: String!
    frequency: PayrollDocumentFrequency!
    status: CustomPayrollDocumentConfigStatus!
}
