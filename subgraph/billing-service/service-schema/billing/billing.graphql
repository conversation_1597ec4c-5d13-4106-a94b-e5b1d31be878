extend type Mutation {
    billRegenerate(input: BillingEventQueryInput!): <PERSON><PERSON><PERSON> @authorize(operations: ["update.operations.billed-item"])
    billDelete(transactionIds: [String!]!): <PERSON><PERSON><PERSON> @authorize(operations: ["update.operations.billed-item"])
    billBackfillEntityId(companyIds: [ID!]!): <PERSON><PERSON><PERSON> @authorize(operations: ["update.operations.billed-item"])
    reactivateBillingEvents(input: ReactivateBillingEventsInput!): <PERSON><PERSON><PERSON> @authorize(operations: ["update.operations.billed-item"])
}

extend type Query {
    billingEvents(input: BillingEventQueryInput): [BillingEvent!] @authorize(operations: ["view.operations.billing-event"])
}

type BillingEvent {
    transactionId: String!
    companyId: ID!
    companyProduct: String!,
    billingDuration: BillingDuration!,
    status: BillingStatus!,
}

type BillingDuration {
    startDate: Date,
    endDate: Date
}

enum BillingStatus {
    PENDING,
    IN_PROGRESS,
    COMPLETED,
    FAILED,
    ARCHIVED
}

input BillingEventQueryInput {
    duration: BillingDurationInput!
    dimensions: [BillingDimensionInput!]!
    lineCode: String!
    companyIds: [ID!]
}

input BillingDurationInput {
    startDate: Date!,
    endDate: Date
}

input BillingDimensionInput {
    key: String!,
    value: String!
}

input ReactivateBillingEventsInput {
    orderIds: [ID!]
}
