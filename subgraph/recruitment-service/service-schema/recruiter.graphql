extend type Mutation {
    upsertRecruiter(input: RecruiterInput, logo: Upload): Recruiter
    deleteRecruiter(id: ID!): TaskResponse
}

extend type Query {
    recruiters(filters: RecruiterFilters): [Recruiter]
}

###############################
# Inputs
###############################
input RecruiterInput {
    id: ID
    name: String
    description: String
    phones: [PhoneNumberInput]
    emails: [EmailAddressInput]

    features: [RecruiterFeatureInput]
}

input RecruiterFeatureInput {
    category: FeatureCategory!
    values: [String]!
}

input RecruiterFilters {
    ids: [ID]
    features: [RecruiterFeatureInput]
}


###############################
# Types
###############################

type Recruiter {
    id: ID
    logo: DocumentReadable
    name: String
    description: String
    phones: [PhoneN<PERSON><PERSON>]
    emails: [EmailAddress]
    features: [RecruiterFeature]
}

type RecruiterFeature {
    name: [String]
    category: FeatureCategory
}

enum FeatureCategory {
    COUNTRY
    JOB_ROLE
    CANDIDATE_TYPE
    HIRING_DIVERSITY
    EXPERIENCE_LEVEL
}
