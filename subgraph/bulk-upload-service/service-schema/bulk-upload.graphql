extend type Query {
    bulkUploadTemplate(options: BulkUploadOptions!, filter: BulkUploadDataFilter): DocumentReadable! @authorize(company: ["view.company.upload-job.template"], operations: ["view.operations.upload-job.template"])
    bulkUploadValidationReport(jobId: ID!, reportType: ReportType!): DocumentReadable @authorize(company: ["view.company.upload-job"], operations: ["view.operations.upload-job"])
}

extend type Mutation {
    bulkUploadJobCreate(options: BulkUploadOptions!, file: Upload!): BulkUploadJob! @authorize(company: ["create.company.upload-job"], operations: ["create.operations.upload-job"])
    bulkUploadJobValidate(jobId: ID!): BulkUploadJob! @authorize(company: ["update.company.upload-job"], operations: ["update.operations.upload-job"])
    bulkUploadJobCancel(jobId: ID!): BulkUploadJob! @authorize(company: ["update.company.upload-job"], operations: ["update.operations.upload-job"])
    bulkUploadJobStage(jobId: ID!): BulkUploadJob! @authorize(company: ["update.company.upload-job"], operations: ["update.operations.upload-job"])
    bulkUploadJobCommit(jobId: ID!, recordIds: [ID!]): BulkUploadJob! @authorize(company: ["update.company.upload-job"], operations: ["update.operations.upload-job"])
    bulkUploadDataRecordDelete(jobId: ID!, recordIds: [ID!]): [BulkUploadDataRecord!]! @authorize(company: ["update.company.upload-job"], operations: ["update.operations.upload-job"])
    bulkUploadFileReUpload(jobId: ID!, file: Upload!): BulkUploadJob @authorize(company: ["update.company.upload-job"], operations: ["update.operations.upload-job"])
    bulkJobCreate(options: BulkUploadOptions!, jobData: BulkJobDataInput!): BulkUploadJob! @authorize(company: ["create.company.upload-job"], operations: ["create.operations.upload-job"])
}

enum ReportType {
    FULL_VALIDATION_REPORT
    ERROR_ONLY_VALIDATION_REPORT
    SUCCESS_ONLY_VALIDATION_REPORT
}

enum BulkUploadJobStatus {
    CREATED,
    VALIDATION_IN_PROGRESS,
    VALIDATION_FAILED,
    VALIDATION_SUCCESS,
    VALIDATION_RESULT_REVIEWED,
    DATA_CREATION_IN_PROGRESS,
    DATA_CREATION_FAILED,
    DATA_CREATION_SUCCESS,
}

input BulkUploadDataFilter {
    contractIds: [ID!]
    entityIds: [ID!]
    customFilters: [CustomFilter!]
    pageRequest: PageRequest
}

input CustomFilter {
    key: String!
    values: [String!]!
}

input BulkUploadOptions {
    companyId: ID!
    entityId: ID
    countryCode: String
    groupName: String!
    moduleNames: [String!]!
    moduleCustomParams: [BulkUploadModuleCustomParam!]
}

input BulkUploadModuleCustomParam {
    moduleName: String!
    jsonCustomParams: String!
}

input BulkJobDataInput {
    dataGroups: [BulkJobDataGroupInput!]!
}

input BulkJobDataGroupInput {
    name: String
    moduleName: String!
    dataRows: [BulkJobDataRowInput!]!
}

input BulkJobDataRowInput {
    data: [BulkJobDataCellInput!]
}

input BulkJobDataCellInput {
    key: String!
    value: String
}

input BulkUploadDataQueryInput {
    moduleName: String! # to filter which tab data to fetch for PAYROLL view
    filter: [FilterBy!] # Filters to be applied using AND operations
    columnKeys: [String!] # Columns to be fetched - empty means all columns
    pageRequest: PageRequest
}

input FilterBy {
    key: String!
    value: String
}

type BulkUploadJob {
    id: ID!
    status: BulkUploadJobStatus!
    groupName: String!
    moduleNames: [String!]!
    companyId: ID!
    entity: LegalEntity
    countryCode: String
    createdOn: DateTime!
    createdBy: Person
    updatedOn: DateTime!
    updatedBy: Person
    hasErrorReport: Boolean!
    hasSuccessReport: Boolean!
    validationSummary: BulkUploadValidationSummary # used for Summary view - TBD, pending for Rounak confirm - remove this after Sep 2024
    validatedData(query: BulkUploadDataQueryInput!): BulkUploadValidatedData # used for Imported view
    committedData(query: BulkUploadDataQueryInput!): BulkUploadCommittedData # used for Added/Committed view
    uploadedFiles: [BulkUploadFile!]
    lastUploadedFile: BulkUploadFile
    isCancelled: Boolean!
}

type BulkUploadFile {
    id: ID!
    name: String!
    url: String!
    createdOn: DateTime!
    createdBy: Person
}

type BulkUploadValidationSummary {
    validationSummaryChunks: [BulkUploadValidationSummaryChunk!]!
}

type BulkUploadValidationSummaryChunk {
    name: String!
    identifiedRowCount: Int!
    failedRowCount: Int!
}

type BulkUploadValidatedData {
    rows: [BulkUploadDataRecord!]!
    pageResult: PageResult!
}

type BulkUploadCommittedData {
    rows: [BulkUploadDataRecord!]!
    pageResult: PageResult!
    progress: BulkUploadDataProgress
}

type BulkUploadDataRecord {
  id: ID!
  data: [BulkUploadDataCell!]!
}

type BulkUploadDataCell {
    key: String!
    value: String
}

type BulkUploadDataProgress {
    totalRecords: Int!
    processedRecords: Int!
    failedRecords: Int! # successRecords = processedRecords - failedRecords
}
