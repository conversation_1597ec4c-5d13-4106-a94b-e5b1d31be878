type Company @key(fields: "id") @extends {
    id: ID @external

    bulkUploadJob(jobId: ID!): BulkUploadJob @authorize(company: ["view.company.upload-job"], operations: ["view.operations.upload-job"])
    bulkUploadJobs(query: BulkUploadJobQueryInput!): BulkUploadJobPageResult @authorize(company: ["view.company.upload-job"], operations: ["view.operations.upload-job"]) # "TODO: for Dashboard view - for V1, BE returns everything, filter on FE"
}

input BulkUploadJobQueryInput {
    entityIds: [ID]
    groupNames: [String!]
    moduleNames: [String!]
    statuses: [BulkUploadJobStatus!]
    isCancelled: Boolean
    createdBy: ID
    pageRequest: PageRequest
}

type BulkUploadJobPageResult {
    items: [BulkUploadJob!]!
    pageResult: PageResult!
}

type OperationsUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type CompanyUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type EmergencyPointOfContact implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type Member implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}
