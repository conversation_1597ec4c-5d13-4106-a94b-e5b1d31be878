enum BillingRateType {
    VALUE
    BASE_PAY_PERCENTAGE
}

enum OperationStatus {
    SUCCESS
    FAILURE
    PARTIAL_SUCCESS
}

enum ResultType {
    INFO
    WARN
    ERROR
}

enum FieldType {
    BOOLEAN
    DATE
    DOUBLE
    INTEGER
    STRING
}

type CompensationComponentDetails {
    key: String!
    value: String
    type: FieldType!
}

input CompensationUpsertInput {
    id: String!
    keyValueInputs: [KeyValueInput!]!
}

type RecordDetails {
    id: String!
    componentDetails: [CompensationComponentDetails!]!
}

type RecordValidationResult {
    id: String!
    componentValidationResults: [ComponentValidationResult!]!
}

type ComponentValidationResult {
    componentDetails: CompensationComponentDetails!
    type: ResultType!
    message: String!
}

type CompensationDomainBulkUpsertResponse {
    entityId: ID!
    status: OperationStatus!
    recordValidationResults: [RecordValidationResult!]
}

input CompensationDomainBulkUpsertRequest {
    entityId: ID!
    commitPartially: Boolean!
    bulkUpsertInput: [CompensationUpsertInput!]!
    useCase: CompensationBulkUpsertUseCase!
    customParams: [KeyValueInput!]!
}

enum CompensationBulkUpsertUseCase {
    COMPENSATION_REVISION
    COMPENSATION_SCHEMA
    COMPENSATION_SETUP
    DEDUCTION
    PAY_SCHEDULE
    PAY_SUPPLEMENT
}

enum CompensationState {
    UPCOMING
    PROCESSING
    COMPLETED
    ACTIVE
}

enum CompensationItemState {
    DRAFT
    PENDING
    PROCESSING
    COMPLETED
    ABORTED
    REVOKED
}

enum CompensationRecordType {
    DRAFT
    APPLIED
}

enum CompensationInputStatus {
    ACTIVATE
    TERMINATE
}

enum PayScheduleFrequency {
    ANNUALLY
    SEMI_ANNUALLY
    QUARTERLY
    MONTHLY
    SEMI_MONTHLY
    BI_WEEKLY
    WEEKLY
    DAILY
    ONE_TIME
}

enum CompensationCategory {
    CONTRACT_BASE_PAY_ADDITIONAL
    CONTRACT_BASE_PAY
    CONTRACT_BASE_PAY_BREAKUP
    MONTH_PAY_13TH_14TH
    CONTRACT_ALLOWANCE
    EMPLOYER_CONTRIBUTION
    EMPLOYEE_CONTRIBUTION
    PAY_SUPPLEMENT
    EMPLOYEE_DEDUCTION
    EMPLOYER_DEDUCTION
    TOTAL_COST_TO_COMPANY
    ARREARS_BASE_PAY_BREAKUP
}

enum CompensationSchemaItemType {
    INPUT
    FIXED
    CALCULATED
    ALL
}
