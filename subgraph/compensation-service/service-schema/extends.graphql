type Contract @key(fields: "id") @extends {
    id: ID @external

    """
    # Get all compensations for the contract (For now this may serve include compensations of all the statuses).
    # Note: This query will be used once after the compensation service is fully implemented.
    """
    compensationRecords(
        filter: CompensationRecordsFilter
    ): [CompensationDetails!] @authorize(company: ["view.company.compensation"], operations: ["view.operations.compensation"], member: ["view.member.compensation"])
}
