extend type Query {
    compensationRecords(filter: CompensationRecordsFilter!, page: PageRequest): CompensationRecordsResponse! @authorize(company: ["view.company.compensation"], operations: ["view.operations.compensation"], member: ["view.member.compensation"])
    upsertCompensationRules(bulkUpsertRequest: CompensationDomainBulkUpsertRequest!): AccessibilityResponse  @authorize(company: ["update.company.compensation"], operations: ["update.operations.compensation"])
    latestEndedCompensationRecords(filter: LatestEndedCompensationRecordsFilter!): [CompensationDetails!] @authorize(company: ["view.company.compensation"], operations: ["view.operations.compensation"], member: ["view.member.compensation"])
    compensationItems(filter: CompensationItemsFilter!): CompensationItemsResponse! @authorize(company: ["view.company.compensation"], operations: ["view.operations.compensation"])
    previewAutoGeneratedCompensations(request: PreviewAutoGeneratedCompensationsRequest!): PreviewAutoGeneratedCompensationsResponse! @authorize(company: ["view.company.compensation"], operations: ["view.operations.compensation"])
}

extend type Mutation {
    upsertCompensation(bulkUpsertRequest: CompensationDomainBulkUpsertRequest!): CompensationDomainBulkUpsertResponse @authorize(company: ["update.company.compensation"], operations: ["update.operations.compensation"])
    generateCompensationItems(request: GenerateCompensationItemsRequest!): TaskResponse @authorize(operations: ["update.operations.compensation"])
    bulkUpdateCompensationStatus(request: BulkCompensationStatusUpdateRequest!): BulkCompensationStatusUpdateResponse @authorize(operations: ["update.operations.compensation"])
    revokeCompensations(request: RevokeCompensationsRequest!): RevokeCompensationsResponse! @authorize(company: ["update.company.compensation"], operations: ["update.operations.compensation"])
    revokeCompensationItems(request: RevokeCompensationItemsRequest!): RevokeCompensationItemsResponse!@authorize(company: ["update.company.compensation"],operations: ["update.operations.compensation"])
    updateCompensationItems(request: UpdateCompensationItemsRequest!): UpdateCompensationItemsResponse!@authorize(company: ["update.company.compensation"],operations: ["update.operations.compensation"])
    upsertCompensationByMember(request: UpsertCompensationByMemberRequest!): UpsertCompensationByMemberResponse!
        @authorize(
            operations: ["update.operations.compensation"],
            member: ["update.member.compensation"]
        )
}

input LatestEndedCompensationRecordsFilter {
    contractIds: [ID!]!
    categories: [String!]!
}

input CompensationItemsFilter {
    entityId: ID
    companyId: ID
    compensationIds: [UUID!]!
    contractIds: [ID!]!
    categories: [String!]!
    states: [CompensationItemState!]!
}

type CompensationItemsResponse {
    data: [CompensationDetails!]!
}

input CompensationRecordsFilter {
    ids: [UUID!] # Either compensation input ids/compensation ids based on the CompensationRecordType. When ids are provided, exactly one CompensationRecordType must be specified.
    entityId: ID
    companyId: ID
    contractIds: [ID!]
    categories: [String!]
    startDate: Date
    endDate: Date
    status: CompensationState
    activeAsOn: Date
    recordTypes: [CompensationRecordType!]
    countries: [CountryCode!]
    contractTypes: [ContractType!]
    memberName: String
}

type CompensationRecordsResponse {
    data: [CompensationDetails!]!
    page: PageResult!
}

type CompensationDetails {
    entityId: ID!
    contractId: ID! @deprecated(reason: "Use contract.id instead")
    contract: Contract
    recordDetails: [RecordDetails!]!
    resultType: ResultType
    message: String
}

input GenerateCompensationItemsRequest {
    compensationIds: [UUID!]!
}

input BulkCompensationStatusUpdateRequest {
    contractIds: [ID!]!
    status: CompensationInputStatus!
}

type BulkCompensationStatusUpdateResponse {
    status: OperationStatus!
    results: [UpdateCompensationStatusResult!]!
}

type UpdateCompensationStatusResult {
    contractId: ID!
    resultType: ResultType!
    message: String
}

input RevokeCompensationsRequest {
    compensationIds: [UUID!]!
    asOfDate: Date
}

input RevokeCompensationItemsRequest {
    compensationItemIds: [UUID!]!
}

input CompensationItemUpdateInput {
    compensationItemId: UUID!
    keyValueInputs: [KeyValueInput!]!
}

input UpdateCompensationItemsRequest {
    updates: [CompensationItemUpdateInput!]!
}

type RevokeCompensationsResponse {
    status: OperationStatus!
    results: [RevokeCompensationResult!]!
}

type RevokeCompensationItemsResponse {
    status: OperationStatus!
    results: [RevokeCompensationItemResult!]!
}

type RevokeCompensationItemResult {
    compensationItemId: UUID!
    resultType: ResultType!
    message: String
}

type UpdateCompensationItemsResponse {
    status: OperationStatus!
    results: [UpdateCompensationItemResult!]!
}

type RevokeCompensationResult {
    compensationId: UUID!
    resultType: ResultType!
    message: String
}

type UpdateCompensationItemResult {
    compensationItemId: UUID!
    resultType: ResultType!
    message: String
    updatedDetails: RecordDetails
}

input PreviewAutoGeneratedCompensationsRequest {
    entityId: ID!
    offeringType: OfferingCode!
    country: CountryCode!
    schemaName: String
    contractId: ID
    input: [KeyValueInput!]!
}

type PreviewAutoGeneratedCompensationsResponse {
    resultType: ResultType!
    message: String
    data: [RecordDetails!]!
}

input UpsertCompensationByMemberRequest {
    contractId: ID!
    keyValueInputs: [KeyValueInput!]!
}

type UpsertCompensationByMemberResponse {
    contractId: ID!
    status: OperationStatus!
    componentValidationResults: [ComponentValidationResult!]
}
