extend type Query {
    paySchedules(request: PayScheduleRequest!, page: PageRequest): PayScheduleResponse
        @authorize(
            operations: ["view.operations.pay-schedule"],
        )

    getEligiblePaySchedules(request: GetEligiblePaySchedulesRequest): [PayScheduleDetails!]!
        @authorize(
            operations: ["view.operations.pay-schedule"],
            company: ["view.company.pay-schedule"],
        )

    getEligibleBillingFrequencies(request: GetEligibleBillingFrequenciesRequest): [BillingFrequency!]!
        @authorize(
            operations: ["view.operations.pay-schedule"],
            company: ["view.company.pay-schedule"],
        )
}

extend type Mutation {
    upsertPaySchedule(bulkUpsertRequest: CompensationDomainBulkUpsertRequest!): CompensationDomainBulkUpsertResponse
        @authorize(
            operations: ["update.operations.pay-schedule"],
        )
}

input GetEligibleBillingFrequenciesRequest {
    entityId: ID!
    countryCode: CountryCode!
    offeringCode: OfferingCode!
    state: String
    grossSalaryPayFrequency: PayScheduleFrequency
    compensationCategory: CompensationCategory
}

input GetEligiblePaySchedulesRequest {
    entityId: ID!
    countryCode: CountryCode!
    offeringCode: OfferingCode!
    state: String
    grossSalaryPayFrequency: PayScheduleFrequency
    billingFrequency: BillingFrequency
    compensationCategory: CompensationCategory
}

input PayScheduleRequest {
    companyIds: [ID!]
    entityIds: [ID!]
    referenceStartDate: Date
    referenceEndDate: Date
    offeringType: OfferingCode
    country: CountryCode
}

type PayScheduleResponse {
    data: [PayScheduleDetails!]!
    page: PageResult!
    resultType: ResultType
    message: String
}

type PayScheduleDetails {
    entityId: ID!
    companyId: ID!
    recordDetails: [RecordDetails!]!
}
