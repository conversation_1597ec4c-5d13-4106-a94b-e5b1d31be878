extend type Query {
    compensationSchema(compensationSchemaFilter: CompensationSchemaFilter!): [CompensationSchemaResponse!]!  @authorize(company: ["view.company.compensation-schema"], operations: ["view.operations.compensation-schema"])
    getApplicableCompensationSchemas(request: CompensationSchemaRequest!): [CompensationSchemaResponse!]! @authorize(company: ["view.company.compensation-schema"], operations: ["view.operations.compensation-schema"])
    getCompensationSchemaByContractId(contractId: ID!): CompensationSchemaResponse!
        @authorize(
            company: ["view.company.compensation-schema"],
            member: ["view.member.compensation-schema"],
            operations: ["view.operations.compensation-schema"]
        )
    upsertCompensationSchemaRules(bulkUpsertRequest: CompensationDomainBulkUpsertRequest!): AccessibilityResponse  @authorize(company: ["update.company.compensation-schema"], operations: ["update.operations.compensation-schema"])
}

extend type Mutation {
    upsertCompensationSchema(bulkUpsertRequest: CompensationDomainBulkUpsertRequest!): CompensationDomainBulkUpsertResponse @authorize(company: ["update.company.compensation-schema"], operations: ["update.operations.compensation-schema"])
}

input CompensationSchemaFilter {
    entityIds: [ID!]!
    itemTypes: [CompensationSchemaItemType!]
}

input CompensationSchemaRequest {
    entityId: ID!
    offeringType: OfferingCode!
    country: CountryCode
    categories: [String!]
    itemTypes: [CompensationSchemaItemType!]
    # Determines whether only the default compensation schema should be returned.
    # If false, all applicable schemas will be returned.
    # If not provided, the default schema will be returned (for backward compatibility).
    returnDefaultOnly: Boolean
}

type CompensationSchemaResponse {
    entityId: ID!
    compensationSchemaRecordDetails: [RecordDetails!]!
}
