extend type Query {
  # Get a paginated list of notifications
  notifications(
    filter: NotificationFilter,
    pageRequest: PageRequest
  ): NotificationPage
    @authorize(company: ["view.company.notifications"], operations: ["view.operations.notifications"], member: ["view.member.notifications"])

  # Get a specific notification by ID
  notification(id: ID!): Notification
    @authorize(company: ["view.company.notifications"], operations: ["view.operations.notifications"], member: ["view.member.notifications"])

  # Get the count of unread notifications
  unreadNotificationCount(companyId: ID, contractId: ID): Int
    @authorize(company: ["view.company.notifications"], operations: ["view.operations.notifications"], member: ["view.member.notifications"])
}

extend type Mutation {
  # Mark a notification as read
  markNotificationAsRead(id: ID!): Boolean!
    @authorize(company: ["update.company.notifications"], operations: ["update.operations.notifications"], member: ["update.member.notifications"])

  # Mark a notification as clicked (user interacted with it)
  markNotificationAsClicked(id: ID!): <PERSON><PERSON><PERSON>!
    @authorize(company: ["update.company.notifications"], operations: ["update.operations.notifications"], member: ["update.member.notifications"])

  # Mark all notifications as read
  markAllNotificationsAsRead(companyId: ID, contractId: ID): Int!
    @authorize(company: ["update.company.notifications"], operations: ["update.operations.notifications"], member: ["update.member.notifications"])

  # Mark a notification action as completed (for ACTION_REQUIRED notifications)
  markNotificationActionCompleted(notificationId: ID!, actionId: ID!): Boolean!
    @authorize(company: ["update.company.notifications"], operations: ["update.operations.notifications"], member: ["update.member.notifications"])
}

# Filter for notifications query
input NotificationFilter {
  # Filter by notification categories
  categories: [NotificationCategory!]

  # Filter by source types
  sources: [SourceType!]

  # Filter by source subtypes
  sourceSubTypes: [String!]

  # Filter by user persona
  userPersonas: [Persona!]

  # Filter by specific company ID
  companyId: ID

  # Filter by specific contract ID
  contractId: ID

  # Filter by read status
  read: Boolean

  # Filter by completion status (for ACTION_REQUIRED notifications)
  isCompleted: Boolean

  # Filter by date range
  dateRange: DateRange
}

# Notification page result with pagination
type NotificationPage {
  # List of notifications
  data: [Notification!]!

  # Pagination information
  pageResult: PageResult
}

# Notification entity
type Notification {
  # Unique identifier
  id: ID!

  # Notification title
  title: String!

  # Notification message content
  message: String!

  # Category of notification (INFORMATION, UPDATES, ACTION_REQUIRED)
  category: NotificationCategory!

  # Source of the notification
  source: SourceType!

  # Subtype of the source (e.g., APPROVAL_REQUESTED, DOCUMENT_SIGNED)
  sourceSubType: String

  # Optional source ID (e.g., contract ID, expense ID)
  sourceId: ID

  # Target user persona (which type of user this notification is for)
  userPersona: Persona

  # ID of the company this notification is for (if applicable)
  companyId: ID

  # ID of the contract this notification is for (if applicable)
  contractId: ID

  # Optional metadata as JSON
  metadata: JSON

  # Whether the notification has been read
  read: Boolean!

  # Whether the notification has been clicked/interacted with
  clicked: Boolean!

  # List of actions that can be taken on this notification
  # Each action is defined as {type: "button", actionDisplayText: "...", link: "..."}
  actions: [NotificationAction!]

  # Whether the action has been completed (for ACTION_REQUIRED notifications)
  isCompleted: Boolean

  # When the notification was created
  createdOn: DateTime!

  # When the notification was last updated
  updatedOn: DateTime

  # When the notification was read
  readOn: DateTime

  # When the notification was clicked
  clickedOn: DateTime

  # When the action was completed (for ACTION_REQUIRED notifications)
  completedOn: DateTime

  # Optional expiry date for the notification
  expiresOn: DateTime

  # Analytics tracking data
  analytics: NotificationAnalytics
}

# Action that can be taken on a notification
type NotificationAction {
  # Type of action
  type: ActionType!

  # Display text for the action
  actionDisplayText: String!

  # Link to navigate to when action is clicked
  link: String!

  # Optional metadata for the action
  metadata: JSON
}

# Categories of notifications
enum NotificationCategory {
  # Informational notifications that don't require action
  INFORMATION

  # Updates about changes or events
  UPDATES

  # Notifications that require user action
  ACTION_REQUIRED
}

# Types of actions
enum ActionType {
  # Button action
  BUTTON
}

# Source of the notification
enum SourceType {
  # Expense-related notifications
  EXPENSE

  # Time off-related notifications
  TIMEOFF

  # Contract-related notifications
  CONTRACT

  # Document-related notifications
  DOCUMENT

  # Payment-related notifications
  PAYMENT

  # Approval-related notifications
  APPROVAL

  # Onboarding-related notifications
  ONBOARDING
}

# Analytics data for notifications
type NotificationAnalytics {
  # Number of times the notification was viewed
  viewCount: Int!

  # Number of times the notification was clicked
  clickCount: Int!

  # Time to first view (in seconds) from creation
  timeToFirstView: Int

  # Time to action (in seconds) from first view
  timeToAction: Int

  # Whether the notification led to a conversion/completion
  ledToConversion: Boolean

  # Additional analytics data
  additionalData: JSON
}