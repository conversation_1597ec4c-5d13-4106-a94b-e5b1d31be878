extend type Query {
    salaryCalculation(params: SalaryCalculationParam): SalaryCalculation @authorize(operations: ["view.operations.salary-calculator"], company: ["view.company.salary-calculator"])
    salaryCalculationParams(payableType: [CalculationPayableType!], country: CountryCode): [AvailableSalaryCalculationParams!] @authorize(operations: ["view.operations.salary-calculator"], company: ["view.company.salary-calculator"])
    salaryCalculationPdf(params: SalaryCalculationParam): FileLink @authorize(operations: ["view.operations.salary-calculator"], company: ["view.company.salary-calculator"])
    salaryCalculationMinimumWage(params: SalaryCalculationMinimumWageParam!): SalaryCalculationMinimumWage @authorize(operations: ["view.operations.salary-calculator"], company: ["view.company.salary-calculator"])
}

extend type Mutation {
    salaryCalculationEmail(emails: [String], params: SalaryCalculationParam): TaskResponse @authorize(operations: ["view.operations.salary-calculator"], company: ["view.company.salary-calculator"])
    salaryCalculatorRequestEmail(params: SalaryCalculationRequestEmailParam): TaskResponse @authorize(operations: ["view.operations.salary-calculator"], company: ["view.company.salary-calculator"])
}

input SalaryCalculationMinimumWageParam {
    country: CountryCode!
    state: String
    currency: CurrencyCode
    frequency: RateFrequency!
}

input SalaryCalculationRequestEmailParam {
    country: CountryCode!
    currency: CurrencyCode!
    amount: Float!
    frequency: RateFrequency!
    payableType: [CalculationPayableType!]
}

input SalaryCalculationParam {
    country: CountryCode!
    state: String
    rateType: RateType!
    currency: CurrencyCode!
    amount: Float!
    frequency: RateFrequency!
    payableType: [CalculationPayableType!]!
    constantOverrides: [ConstantOverride!]
}

input ConstantOverride {
    name: String!
    value: Float
}

type AvailableSalaryCalculationParams {
    country: CountryCode!
    states: [String!]!
    rateType: [RateType!]!
    currency: [CurrencyCode!]!
    frequency: [RateFrequency!]!
    payableType: [CalculationPayableType!]!
}

type SalaryCalculationMinimumWage {
    currency: CurrencyCode!
    amount: Float!
    frequency: RateFrequency!
}

type SalaryCalculation {
    companyPayable: [CalculationBreakdown!]!
    employeePayable: [CalculationBreakdown!]!
}

type CalculationEntry {
    name: String!
    currency: CurrencyCode
    amount: Float!
    note: String
}

type CalculationEntryGroup {
    name: String!
    currency: CurrencyCode
    amount: Float!
    entries: [CalculationEntry!]!
    note: String
}

type CalculationBreakdown {
    name: String!
    entries: [CalculationEntryGroup!]!
    total: CalculationEntry!
    frequency: RateFrequency
    note: String
}
