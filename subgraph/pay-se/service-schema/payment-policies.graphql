extend type Query {
    getPreferredPaymentPartnerConfig(input: [GetPreferredPaymentPartnerConfigInput]): [PreferredPartnerConfigResponse] @authorize(operations: ["view.operations.payment-options"])
}

extend type Mutation {
    addPreferredPaymentPartnerConfig(input: [AddPreferredPaymentPartnerConfigInput!]!): [PreferredPartnerConfigResponse] @authorize(operations: ["update.operations.payment-options"])
}

type PreferredPartnerConfigResponse {
    destinationCountry: CountryCode!
    targetCurrency: CurrencyCode!
    preferredPartner: PaymentPartnerCode
    dynamicContext: DynamicContext
}

type DynamicContext {
    paymentType: PaymentType
    allowedAccountRequirementType: [String]
}

input GetPreferredPaymentPartnerConfigInput {
    destinationCountry: CountryCode!
    targetCurrency: CurrencyCode!
}

input AddPreferredPaymentPartnerConfigInput {
    destinationCountry: CountryCode!
    targetCurrency: CurrencyCode!
    preferredPartner: PaymentPartnerCode!
    dynamicContext: DynamicContextInput
}

input DynamicContextInput {
    paymentType: PaymentType
    allowedAccountRequirementType: [String]
}
