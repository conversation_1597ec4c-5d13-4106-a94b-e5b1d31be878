extend type Mutation {
    payInProcess(startTime: DateTime!, endTime: DateTime!, paymentPartnerCode: String!, currency: String): <PERSON><PERSON><PERSON> @authorize(operations: ["payin.operations.process"])
}

type PayInInfo @key(fields: "id") {
    id: ID!
    amount: Float
    currency: String
    metadata: [PayInMetadata]   # Metadata specific to paymentMethod (ex: for Stripe, we have publishable_key and client_secret)
    bundlePaymentPartner: BundlePaymentPartner
}

type PayInMetadata {
    key: String
    value: String
}

enum PayInStatus {
    PENDING
    COMPLETED
    CANCELLATION_IN_PROGRESS
    CANCELLED
    FAILED
    INITIATED
    PROCESSING
}
