type Query {
    paymentOk: <PERSON><PERSON><PERSON> @deprecated @authorize(operations: ["view.operations.paymentaccount.payments"]) #It just returns true @Deprecate
}

extend type Mutation {
    PennyTestInitiate(data : PennyTestInitiateRequestData!) : TaskResponse @authorize(operations: ["generate.operations.pif"])
}

input PennyTestInitiateRequestData {
     amount : TransferValueAmount!
     batchPaymentFileId : String!
     paymentPartner: PaymentPartnerCode
}

input TransferValueAmount {
     amount: Float
     destinationCurrency: String
}

type Payment {
    id: ID!
    status: PaymentStatus
    transactions: [Transaction] # Ordered
    transferValue: TransferValue
    audit: Audit # Optional
}

type Audit {
    createdBy: ID # Qualify System/Manual
    createdOn: DateTime
    completedOn: DateTime
}

enum PaymentStatus {
    INITIATED
    PROCESSING
    FAILED
    COMPLETED
    NOT_WHITELISTED
}

## TRANSFER VALUE ##
type TransferValue { # Always Destination Transfer Value
    amount: Float # 100
    destinationCurrency: String # USD, BTC
    type: TransferType # CRYPTO/FIAT
    # breakdown: [TransferValueBreakdown] # [{'conv_fee', 20, 'usd'}, {'payment_amount', 70, 'usd'}]
}

type TransferValueBreakdown {
    component: String
    value: Float
    currency: String
}
