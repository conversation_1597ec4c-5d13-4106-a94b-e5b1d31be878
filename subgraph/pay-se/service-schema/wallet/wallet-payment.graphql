type WalletPayment {
    id: ID!
    status: WalletPaymentStatus!
    transactions: [WalletTransaction!]!
}

interface WalletTransaction {
    id: ID!

    # circular reference for graph
    walletPayment: WalletPayment
    walletTransactionStatus: WalletTransactionStatus
    description: String
    paymentPartner: PaymentPartner
    amount: Amount
}

type FiatTransaction implements WalletTransaction {
    id: ID!
    walletPayment: WalletPayment
    walletTransactionStatus: WalletTransactionStatus
    description: String
    paymentPartner: PaymentPartner
    amount: Amount
}

enum WalletTransactionStatus {
    INITIATED
    PENDING_ON_PARTNER
    COMPLETED
    CANCELLED
    FAILED
    PROCESSING
}

enum WalletPaymentStatus {
    PENDING
    PROCESSING
    COMPLETED
    CANCELLED
    FAILED
}
