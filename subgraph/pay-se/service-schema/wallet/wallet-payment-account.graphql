type WalletPaymentAccount @key(fields: "id") {
    id: ID!
    partnerAccount: [WalletPaymentPartnerAccount]
    metadata: [KeyValue]
}

type WalletPaymentPartnerAccount {
    id: ID!
    status : WalletPaymentPartnerAccountStatus!
    transferType: TransferType!
    accountType: PaymentAccountType!
    paymentPartner: PaymentPartner!
    countryCode: CountryCode!
    balances: [WalletBalance!]!
    payments: [WalletPayment!]
    beneficiaries : [WalletBeneficiary!]
    details: [KeyValue!]

    # circular reference for graph
    paymentAccount: WalletPaymentAccount
}

type WalletBeneficiary {
    id: ID!
    beneficiaryID: ID! #member
    details: [KeyValue]
    partnerBeneficiaryId: String!
}

enum WalletPaymentPartnerAccountStatus {
    DEACTIVATED
    IN_PROGRESS
    REQUEST_FOR_INFORMATION
    ACTIVATED
}

enum WalletBeneficiaryStatus {
    ACTIVE
    INACTIVE
}
