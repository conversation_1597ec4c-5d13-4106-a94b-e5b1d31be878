extend type Query {
    getPaymentOptions(filters: PaymentOptionFilters!): [PaymentOptions] @authorize(company: ["view.company.payment-options"], member: ["view.member.payment-options"], operations: ["view.operations.payment-options"])
}

extend type Mutation {
    addPaymentOption(input: PaymentOptionInput!): [PaymentOptions] @authorize(operations: ["update.operations.payment-options"])
    removePaymentOption(input: RemovePaymentOptionInput!): [PaymentOptions] @authorize(operations: ["update.operations.payment-options"])
}

input PaymentOptionFilters {
    taxResidency: CountryCode!
    contractType: ContractType
    paymentPartners: [PaymentPartnerCode] @deprecated(reason: "payment options v2 doesnt include this")
}

input PaymentOptionSupportFilters {
    targetCurrency: CurrencyCode!
    targetCountry: CountryCode!
}

input PaymentOptionInput {
    taxResidency: CountryCode!
    contractType: ContractType!
    paymentOptions: [PaymentOptionDetailsInput]
}

input RemovePaymentOptionInput {
    taxResidency: CountryCode!
    contractType: ContractType!
    payOutCountry: CountryCode!
    sourceCurrency: CurrencyCode
    payOutCurrency: CurrencyCode!
    paymentPartner: PaymentPartnerCode @deprecated(reason: "payment options v2 doesnt include this")
}

input PaymentOptionDetailsInput {
    isAutomated: Boolean @deprecated(reason: "payment options v2 doesnt include this")
    payOutCountry: CountryCode!
    sourceCurrency: CurrencyCode
    payOutCurrency: CurrencyCode!
    paymentPartner: PaymentPartnerCode @deprecated(reason: "payment options v2 doesnt include this")
}

type PaymentOptions {
    taxResidency: CountryCode
    contractType: ContractType
    options(filter: [PaymentOptionSupportFilters]): [PaymentOptionInfo]
}

type PaymentOptionInfo {
    paymentCountry: CountryCode
    paymentCurrency: CurrencyCode
    partnerSupport: PartnerSupportInfo
}

type PartnerSupportInfo {
    isAutomated: Boolean
    paymentMethod: String
    paymentPartner: PaymentPartnerCode
    sourceCurrency: CurrencyCode
    accountType: PaymentAccountType
    transferType: TransferType
    paymentProcessingMethods: [PaymentProcessingMethod]
    paymentAccountRequirements: PaymentAccountPartnerRequirement
}


type PaymentProcessingMethod {
    automated: Boolean
    executionInfo: PaymentExecutionInfo
    paymentSLA: PaymentSLA
}

type PaymentExecutionInfo {
    executionType: String ## URGENT , NON_URGENT
}

type PaymentSLA {
    cost: Amount
    estimatedDeliveryTime: Duration
}



