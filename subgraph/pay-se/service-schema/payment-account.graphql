extend type Query {
    paymentAccount(id: ID!): PaymentAccount @authorize(operations: ["view.operations.paymentaccount"])
}

type Mutation {
    refreshPaymentAccountRequirements: <PERSON><PERSON><PERSON> @authorize(operations: ["refresh.operations.paymentaccountrequirements"])
    updatePaymentPartnerAccountStatus(request: PaymentPartnerAccountStatusUpdateRequest): TaskResponse @authorize(operations: ["update.operations.payment-partner-account"])
    refreshPaymentAccountConfiguration(request: RefreshPaymentAccountConfigurationInput): TaskResponse @authorize(operations: ["refresh.operations.paymentaccountrequirements"])
}


type PaymentAccount {
    id: ID!
    sourceSystem: String
    payments: [Payment]
    paymentPartnerAccounts: [PaymentPartnerAccount]
}

type PaymentPartnerAccount {
    id: ID
    isActive: Boolean
    beneficaryID: String
    currency: String
    transferType: TransferType
    accountType: PaymentAccountType
    paymentPartner: PaymentPartner
    paymentAccount: PaymentAccount
}

enum PaymentPartnerAccountStatus {
    ACTIVE
    INACTIVE
}

input PaymentPartnerAccountStatusUpdateRequest {
    paymentPartnerAccountId: ID!
    status: PaymentPartnerAccountStatus!
    reason: String!
}

input RefreshPaymentAccountConfigurationInput {
    paymentCountryCodes: [CountryCode]
    paymentPartners: [PaymentPartnerCode]
}
