type PaymentPartner {
    id: ID!
    name: String
    code: String
    paymentPartnerType: PaymentPartnerType
}

enum PaymentPartnerType {
    FIAT
    CRYPTO
}

type BundlePaymentPartner @key(fields: "payInId") {
    payInId: ID!
    payInStatus: PayInStatus
    paymentTransaction: [PaymentTransaction]
}

type PaymentTransaction {
    transactionId: ID!
    status: TransactionStatus
    partnerTransactionStatus: PartnerTransactionStatus
    metadata: [KeyValue]
}

enum PartnerTransactionStatus {
    INITIATED
    ACCEPTED_SETTLEMENT_CONFIRMED
    ACCEPTED_SETTLEMENT_PENDING_DD
    ACCEPTED_PAYMENT_DETECTED
    REJECTED
    REVERSED
    FAILED
    UNDEFINED
}
