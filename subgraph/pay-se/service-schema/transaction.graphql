interface Transaction {
    id: ID!
    payment: Payment
    status: TransactionStatus
    creditor: LedgerSide
    debitor: LedgerSide
    description: String
    paymentPartner: PaymentPartner
    isTerminalTransaction: Boolean
    transferValue: TransferValue
}

type FiatTransaction implements Transaction {
    id: ID!
    payment: Payment
    status: TransactionStatus
    creditor: LedgerSide
    debitor: LedgerSide
    description: String
    paymentPartner: PaymentPartner
    isTerminalTransaction: Boolean
    transferValue: TransferValue
}

## LEDGER ##
type LedgerSide {
    account: PaymentPartnerAccount
    transferValue: TransferValue
}

# Union of statuses of all transaction types,
# UNION of enums not allowed in graphql for now.
enum TransactionStatus {
    INITIATED
    PENDING_ON_PARTNER
    PENDING_ON_MULTIPLIER
    COMPLETED
    CANCELLATION_IN_PROGRESS
    CANCELLED
    FAILED
    REFUND_INITIATED
    REFUND_PROCESSING
    REFUNDED
    PROCESSING
}
