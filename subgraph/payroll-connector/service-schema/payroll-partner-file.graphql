extend type Query {
    payrollPartnerFileTemplate(filter: PayrollPartnerFileTemplateFilter!): PayrollPartnerFileTemplateResponse @authorize(operations: ["view.operations.payroll.partner-input-file-template"])
    defaultPayrollPartnerFileTemplate(countryCode: CountryCode!): PayrollPartnerFileTemplateResponse @authorize(operations: ["view.operations.payroll.partner-input-file-template"])
}

extend type Mutation {
    upsertPayrollPartnerFileTemplate(input: PayrollPartnerFileTemplateInput!): PayrollPartnerFileTemplateResponse @authorize(operations: ["update.operations.payroll.partner-input-file-template"])
    uploadPayrollPartnerFileTemplate(input: UploadPayrollPartnerFileTemplateInput!): UploadPayrollPartnerFileTemplateResponse! @authorize(operations: ["update.operations.payroll.input-file-template"])
}

input PayrollPartnerFileTemplateFilter {
    countryCode: CountryCode!,
    partnerId: ID
}

input UploadPayrollPartnerFileTemplateInput {
    countryCode: CountryCode!
    partnerId: ID!
    name: String!
}

input PayrollPartnerFileTemplateInput {
    id: UUID!
    name: String!
    countryCode: CountryCode!
    partnerId: ID!
    createdBy: ID!
    createdOn: DateTime!
    updatedBy: ID
    updatedOn: DateTime
    fileType: PayrollPartnerTemplateFileType!
#    sheets: [Sheet!]!// removed due to error, as discussed with anumit
#    customFields: [CustomField]
}

type PayrollPartnerFileTemplateResponse {
    id: UUID!
    name: String!
    countryCode: CountryCode!
    partnerId: ID!
    createdBy: ID!
    createdOn: DateTime!
    updatedBy: ID
    updatedOn: DateTime
    fileType: PayrollPartnerTemplateFileType!
    sheets: [Sheet!]!
    customFields: [CustomField]
}

type Sheet {
    displayName: String!
    headerPresentAt: Int
    type: PayrollSheetType
    columns: [ColumnComponent!]!
    isMandatory: Boolean
    order: Int!
    orientation: OrientationType!
    customFields: [CustomField]
}

type ColumnComponent {
    displayName: String
    payrollComponent: PayrollColumnComponent!
    valueType: ComponentValueType!
    value: String
    defaultValue: String
    isMandatory: Boolean!
    description: String
    order: Int
}

type CustomField {
    name: String!
    value: String
    type: CustomSectionFieldType!
}

type UploadPayrollPartnerFileTemplateResponse {
    countryCode: CountryCode!
    partnerId: ID!
    uploadUrl: String!
}

enum PayrollPartnerTemplateFileType {
    XLSX
}

enum OrientationType {
    HORIZONTAL
    VERTICAL
}

enum ComponentValueType {
    STRING
    FLOAT
    INT
    BOOLEAN
    DATE
    DATETIME
}

enum PayrollSheetType {
    MASTER_TRACKER
    NEW_HIRE
    ALLOWANCE
    ACTIVE_ALLOWANCE
    ALLOWANCE_CHANGE
    TERMINATION
    PAY_SUPPLEMENT
    PERFORMANCE_REVIEW
    EXPENSE
    TIME_OFF
    TIMESHEET
    TIMESHEET_NEW
    BASIC_DETAIL_CHANGES
    BANK_CHANGES
    LEGAL_CHANGES
    VOLUNTARY_DEDUCTIONS
}

enum CustomSectionFieldType {
    STRING
    NUMBER
    BOOLEAN
}

enum PayrollColumnComponent {
    # ContractDataOp subtypes
    CONTRACT_ID,
    EMPLOYEE_ID,
    MEMBER_STATUS,
    MEMBER_FIRST_NAME,
    EMPLOYEE_FULL_NAME,
    MEMBER_LAST_NAME,
    EMPLOYEE_COUNTRY,
    MEMBER_GENDER,
    MEMBER_DEPARTMENT,
    MEMBER_DESIGNATION,
    CONTRACT_START_DATE,
    REPORTING_MANAGER,
    MEMBER_EMAIL_ID,
    MEMBER_LEGAL_DETAILS,
    BANK_DETAILS,
    LAST_WORKING_DAY,
    OFFBOARDING_TYPE,
    TERMINATION_REASON,

    # TimeOffEntryOp subtypes
    TIME_OFF_ID,
    TIME_OFF_TYPE,
    TIME_OFF_CATEGORY,
    TIME_OFF_TYPE_CATEGORY,
    NO_OF_DAYS,
    TIME_OFF_DATE,
    COMMENTS,

    # ExpenseOp subtypes
    EXPENSE_ID,
    EXPENSE_TYPE,
    EXPENSE_TITLE,
    CURRENCY,
    INVOICE_AMOUNT,
    APPROVAL_DATE,
    LINK,

    # CompensationOp and CompensationItemOp subtypes
    JOINING_DATE,
    COMPONENT_NAME,
    RATE_TYPE,
    BILLING_RATE,
    BILLING_FREQUENCY,
    BILLING_RATE_VALUE,
    PAY_SCHEDULE_NAME,
    IS_INSTALLMENT,
    START_DATE,
    END_DATE,
    NUMBER_OF_INSTALLMENTS,
    IS_TAXABLE,
    IS_PRORATED,
    IS_FIXED,
    IS_MANDATORY,
    IS_PART_OF_BASE_PAY,
    IS_ARREARS,

    # TimesheetEntryOp subtypes
    DATE,
    DAY_TYPE,
    HOUR_TYPE,
    NO_OF_HOURS,
    MULTIPLICATION_FACTOR,

    # DataChangeOp subtypes
    CHANGE_TYPE,
    FIELD_NAME,
    OLD_VALUE,
    NEW_VALUE,
    EFFECTIVE_DATE,

    ENTITY_ID,
    PAY_SCHEDULE_FREQUENCY,
    PAY_PERIOD_START_DATE,
    PAY_PERIOD_END_DATE,
    EXPECTED_PAY_DATE,
    PAY_DATE_REFERENCE
}
