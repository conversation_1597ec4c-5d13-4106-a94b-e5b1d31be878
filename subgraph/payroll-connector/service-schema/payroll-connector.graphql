extend type Query {
    # TODO: add query
    placeholder: <PERSON><PERSON><PERSON> @authorize(operations: ["view.operations.payroll-cycle"])
}

extend type Mutation {
    upsertPartnerConfig(request: PartnerConfigRequest!): TaskResponse @authorize(operations: ["view.operations.payroll-cycle"])
    processPayroll(request: ProcessPayrollRequest!): TaskResponse @authorize(operations: ["view.operations.payroll-cycle"])
    syncOutputData(request: SyncOutputDataRequest!): TaskResponse @authorize(operations: ["view.operations.payroll-cycle"])
    onboardEntity(request: OnboardEntityRequest!): TaskResponse @authorize(operations: ["view.operations.payroll-cycle"])
    onboardContracts(request: OnboardContractsRequest!): TaskResponse @authorize(operations: ["view.operations.payroll-cycle"])
}

input PartnerConfigRequest{
    partnerId: ID!
    country: CountryCode!
    resolver: String!
    autoApprovalLeadDays: Int
}

input ProcessPayrollRequest{
    payrollCycleId: ID!
}

input SyncOutputDataRequest{
    payrollCycleId: ID!
}

input OnboardEntityRequest{
    partnerId: ID!
    entityId: ID!
    entityType: PayrollEntityType!
}

input OnboardContractsRequest {
    partnerId: ID!
    contractIds: [ID!]!
}
