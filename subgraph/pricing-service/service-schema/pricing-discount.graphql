type Discount {
    id: ID!
    discountPolicy: DiscountPolicy!
    discountTerms: [DiscountTermV2!]!
}

type DiscountTermV2 {
    id: ID!
    type: DiscountTermType!
    value: Float
    customDiscount: String
    conditions: [DiscountTermCondition!]!
}

type DiscountTermCondition {
    id: ID!
    type: DiscountTermConditionType
    condition: DiscountTermConditionValue
}

union DiscountTermConditionValue =
    | PromotionalPeriodDiscountTermCondition
    | OfferingBundleDiscountTermCondition

type PromotionalPeriodDiscountTermCondition {
    from: Date,
    to: Date,
    event: String,
    relativeDurationFromEvent: String, # 1h, 2d, 5m, 5y
}

type OfferingBundleDiscountTermCondition {
    includes: [OfferingCode]
    excludes: [OfferingCode]
}

input DiscountInput {
    id: ID
    discountPolicy: DiscountPolicy!
    discountTerms: [DiscountTermInputV2!]!
}

input DiscountTermInputV2 {
    id: ID
    type: DiscountTermType!
    value: Float
    customDiscount: String
    conditions: [DiscountTermConditionInput!]!
}

input DiscountTermConditionInput {
    id: ID
    type: DiscountTermConditionType
    condition: DiscountTermConditionValueInput
}

input DiscountTermConditionValueInput {
    promotionalPeriod: PromotionalPeriodDiscountTermConditionInput
    offeringBundle: OfferingBundleDiscountTermConditionInput
}

input PromotionalPeriodDiscountTermConditionInput {
    from: Date
    to: Date
    event: String
    relativeDurationFromEvent: String
}

input OfferingBundleDiscountTermConditionInput {
    includes: [OfferingCode]
    excludes: [OfferingCode]
}

enum DiscountPolicy {
    APPLY_LARGEST
    APPLY_FIRST
    APPLY_LAST
    ADDITIVE
}

enum DiscountTermType {
    FIXED_PRICE
    FLAT
    PERCENTAGE
    CUSTOM
}

enum DiscountTermConditionType {
    PROMOTIONAL_PERIOD
    OFFERING_BUNDLE
}
