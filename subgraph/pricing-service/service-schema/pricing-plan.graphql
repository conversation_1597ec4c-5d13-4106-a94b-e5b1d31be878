extend type Mutation {
    pricingPlanCreate(input: PricingPlanCreateInput!): PricingPlan @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    pricingPlanUpdate(input: PricingPlanUpdateInput!): PricingPlan @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    pricingPlanDelete(input: PricingPlanDeleteInput!): PricingPlan @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    pricingPlanAutofill(input: PricingPlanAutofillInput!): PricingPlan @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    pricingPlanExpand(input: PricingPlanExpandInput!): PricingPlan @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    pricingPlanCloseSeats(input: PricingPlanCloseSeatsInput!): PricingPlan @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    pricingPlanMigrate(input: PricingPlanMigrateInput!): PricingPlanMigrateResponse! @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    pricingPlanActivate(id: ID!): PricingPlan! @authorize(operations: ["activate.operations.recurring-pricing-plan"])
    pricingPlanAssignSeats(input: PricingPlanAssignSeatsInput!): PricingPlan! @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])

    seatAssign(input: SeatAssignInput!): PricingPlan @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    seatRelease(input: SeatReleaseInput!): PricingPlan @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
}

extend type Query {
    pricingPlanGetSeatBasePrice(input: PricingPlanGetSeatBasePriceInput!): PricingPlanGetSeatBasePriceResponse! @authorize(operations: ["view.operations.company.pricing"])
}

input PricingPlanCreateInput {
    companyOfferingId: ID @deprecated(reason: "Use for EOR only")
    companyId: ID
    start: Date!
    end: Date!
    discount: DiscountInput
    generateAndAutofillSeats: Boolean # call autofill with seat generation logic on create
    initialContractIds: [ID!] # for initial seat assignment
    autoSeatExpansion: Boolean
    paymentTerm: PaymentTermInput
    seatConfigurations: [SeatConfigurationInput!]
}

input PricingPlanUpdateInput {
    id: ID!
    start: Date
    end: Date
    discount: DiscountInput
    initialContractIds: [ID!]
    autoSeatExpansion: Boolean
    paymentTerm: PaymentTermInput
    seatConfigurations: [SeatConfigurationInput!]
}

input PricingPlanDeleteInput {
    id: ID!
    forceDeleteIfActive: Boolean!
    vacateAndCloseAllSeats: Boolean!
}

input PricingPlanAutofillInput {
    pricingPlanId: ID!
    expandIfNecessary: Boolean # will generate necessary seats for active members
}

input PricingPlanExpandInput {
    pricingPlanId: ID!
    country: CountryCode! # Deprecated - use seatConfigurations[i].country instead
    workStatus: CountryWorkStatus @deprecated(reason: "Use seatConfigurations[i].workStatus instead")
    count: Int @deprecated(reason: "Use seatConfigurations[i].count instead")
    seatConfigurations: [SeatConfigurationInput!]
    autoSeatExpansion: Boolean
    contractIds: [ID!]
}

input PricingPlanCloseSeatsInput {
    pricingPlanId: ID!
    country: CountryCode!
    workStatus: CountryWorkStatus
    numberOfSeatsToClose: Int!
}

input SeatAssignInput {
    pricingPlanId: ID!
    contractId: ID!
    seatId: ID # in the case where the client wants to assign a specific vacant seat
    effectiveDate: Date! # For prorated delta invoice calculations
}

input PricingPlanAssignSeatsInput {
    pricingPlanId: ID!
    contractIds: [ID!]!
}

input SeatReleaseInput {
    pricingPlanId: ID!
    contractId: ID!
}

type PricingPlan @key(fields: "id") {
    id: ID!
    companyId: ID!
    offeringCode: OfferingCode
    lineCode: String
    start: Date
    end: Date
    discount: Discount # nullable for flexibility
    status: PricingPlanStatus
    autoSeatExpansion: Boolean!
    pricingCountryPlans: [PricingCountryPlan!]!

    contractIds: [ID!]! # resolved in-memory via aggregation of filled seats

    paymentTerm: PaymentTerm
}

type PricingCountryPlan {
    id: ID!
    country: CountryCode!
    workStatus: CountryWorkStatus
    seats: [PricingPlanSeat!]!
    discount: Discount # nullable for backwards-comptability
}

type PricingPlanSeat {
    id: ID!
    pricingCountryPlanId: ID!
    contractId: ID # nullable
    events: [PricingPlanSeatEvent!]!
    status: SeatStatus
    effectiveDate: Date
}

type PricingPlanSeatEvent {
    id: ID!
    seatId: ID!
    contractId: ID
    eventTime: DateTime
    eventType: SeatEventType
}

enum PricingPlanStatus {
    DRAFT # Before MSA signed
    ACTIVE
    INACTIVE
    DELETED
}

enum SeatStatus {
    OCCUPIED
    VACANT
    CLOSED
}

enum SeatEventType {
    OPEN
    FILL
    RELEASE
    CLOSE
}

input PaymentTermInput {
    interval: Int!
    timeUnit: PaymentTermTimeUnit!
}

enum PaymentTermTimeUnit {
    MONTH
    YEAR
}

type PaymentTerm {
    planId: ID!,
    interval: Int!
    timeUnit: PaymentTermTimeUnit!
}

input PricingPlanMigrateInput {
    pricingPlans: [CompanyPricingPlanMigrateInput!]!
}

input CompanyPricingPlanMigrateInput {
    companyId: ID!
    paymentTerm: PaymentTermInput!
}

input SeatConfigurationInput {
    country: CountryCode!
    workStatus: CountryWorkStatus
    count: Int!
    effectiveDate: Date
    discount: DiscountInput #making it non-mandatory for backwards-compatibility
}

type PricingPlanMigrateResponse {
    pricingPlans: [CompanyPricingPlanMigrateResponse!]!
}

type CompanyPricingPlanMigrateResponse {
    companyId: ID!
    successful: Boolean!
}

input PricingPlanGetSeatBasePriceInput {
    companyId: ID!
    planStart: Date!
    planEnd: Date!
    planStatus: PricingPlanStatus!
    offeringCode: OfferingCode!
    countryPlans: [CountryPlanPricingInput!]
}

input CountryPlanPricingInput {
    countryCode: CountryCode!
    workStatus: CountryWorkStatus
}

type PricingPlanGetSeatBasePriceResponse {
    companyId: ID!
    planStart: Date
    planEnd: Date
    planStatus: PricingPlanStatus!
    offeringCode: OfferingCode!
    countryPlans: [CountryPlanPricingResponse!]!
}

type CountryPlanPricingResponse {
    countryCode: CountryCode!
    workStatus: CountryWorkStatus
    basePrice: Float!
}

