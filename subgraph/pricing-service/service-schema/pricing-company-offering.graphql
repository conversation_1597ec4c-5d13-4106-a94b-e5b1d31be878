extend type Query {
    companyOfferingPricing(filter: CompanyOfferingPricingFilter!): [CompanyOfferingPricing] @authorize(company: ["view.company.payables"], operations: ["view.operations.payables"])
}

extend type Mutation {
    companyOfferingPricingCreate(input: CompanyOfferingPricingInput!): CompanyOfferingPricing @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    companyOfferingPricingActivate(companyOfferingPricingIds: [ID!]!): [CompanyOfferingPricing] @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    companyOfferingPricingDeactivate(companyOfferingPricingIds: [ID!]!): [CompanyOfferingPricing] @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    companyOfferingPricingDiscountUpdate(companyOfferingPricingId: ID!, input: DiscountInput!): CompanyOfferingPricing @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])

    companyOfferingFeeComponentUpdate(companyOfferingFeeComponentId: ID!, input: CompanyOfferingFeeComponentInput!): CompanyOfferingPricing @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
    companyOfferingFeeComponentDiscountUpdate(companyOfferingFeeComponentId: ID!, input: DiscountInput!): CompanyOfferingPricing @authorize(operations: ["update.operations.company.pricing"]) # Unused

    companyOfferingFeeComponentTierCreate(companyOfferingPricingId: ID!, input: CompanyOfferingFeeComponentTierInput!): CompanyOfferingPricing @authorize(operations: ["update.operations.company.pricing"]) # Unused
    companyOfferingFeeComponentTierUpdate(companyOfferingPricingId: ID!, input: CompanyOfferingFeeComponentTierInput!): CompanyOfferingPricing @authorize(operations: ["update.operations.company.pricing"]) # Unused
}

type CompanyOfferingPricing {
    id: ID!
    companyId: ID!
    companyOfferingId: ID!
    offeringCode: OfferingCode!
    companyOfferingFeeComponentTiers: [CompanyOfferingFeeComponentTier!]!
    companyOfferingFeeComponents: [CompanyOfferingFeeComponent!]!
    pricingPlans: [PricingPlan!]!
    discount: Discount
    status: CompanyOfferingPricingStatus
}

type CompanyOfferingFeeComponentTier {
    id: ID!
    offeringCode: OfferingCode!
    feeComponentTierDefinitionId: ID
    tierLowerBound: Int
    tierUpperBound: Int
}

type CompanyOfferingFeeComponent {
    id: ID!
    feeComponentDefinitionId: ID
    companyOfferingFeeComponentTier: CompanyOfferingFeeComponentTier
    offeringCode: OfferingCode!
    key: String!
    countryCode: CountryCode
    fee: Float!
    feeUnit: FeeUnit!
    currencyCode: CurrencyCode
    frequencyUnit: FrequencyUnit!
    feeComponentType: FeeComponentType!
    variableBaseUnit: VariableBaseUnit
    discount: Discount
}

input CompanyOfferingPricingFilter {
    ids: [ID!]
    companyIds: [ID!]
    offeringCode: [OfferingCode!]
}

input CompanyOfferingPricingInput {
    companyOfferingId: ID!
    companyOfferingFeeComponentTiers: [CompanyOfferingFeeComponentTierInput!]!
    discount: DiscountInput
}

input CompanyOfferingFeeComponentTierInput {
    id: ID
    offeringCode: OfferingCode!
    feeComponentTierDefinitionId: ID
    tierLowerBound: Int
    tierUpperBound: Int
    companyOfferingFeeComponents: [CompanyOfferingFeeComponentInput!]!
}

input CompanyOfferingFeeComponentInput {
    id: ID
    feeComponentDefinitionId: ID
    offeringCode: OfferingCode!
    key: String!
    countryCode: CountryCode
    fee: Float!
    feeUnit: FeeUnit!
    currencyCode: CurrencyCode
    frequencyUnit: FrequencyUnit!
    feeComponentType: FeeComponentType!
    variableBaseUnit: VariableBaseUnit
    discount: DiscountInput
}

enum VariableBaseUnit {
    ACTIVE_MEMBER
    MEMBER_PAYROLL_CYCLE
    HOUR
    COMPANY_ACTIVATION
    MEMBER_OFFBOARD
    MEMBER_ACTIVATION
    MEMBER_OFFCYCLE_RUN
}

enum FeeComponentType {
    FIXED
    VARIABLE
}

enum FrequencyUnit {
    ONE_TIME
    AD_HOC
    MONTHLY
    ANNUALLY
}

enum FeeUnit {
    PERCENTAGE
    CURRENCY
}

enum CompanyOfferingPricingStatus {
    ACTIVE
    INACTIVE
}
