extend type Query {
    feeComponentDefinition(filter: FeeComponentDefinitionFilter!): [FeeComponentDefinition] @authorize(operations: ["update.operations.company.pricing"]) #unused
    feeComponentTierDefinition(offeringCode: OfferingCode): [FeeComponentTierDefinition] @authorize(operations: ["update.operations.company.pricing"]) #unused
}

extend type Mutation {
    feeComponentDefinitionCreate(offeringCode: OfferingCode!, input: FeeComponentDefinitionInput!): FeeComponentDefinition @authorize(operations: ["update.operations.company.pricing"]) #unused
    feeComponentDefinitionUpdate(feeComponentDefinitionId: ID!, input: FeeComponentDefinitionInput!): FeeComponentDefinition @authorize(operations: ["update.operations.company.pricing"]) #unused
    feeComponentDefinitionActivate(feeComponentDefinitionIds: [ID!]!): [FeeComponentDefinition] @authorize(operations: ["update.operations.company.pricing"]) #unused
    feeComponentDefinitionDeactivate(feeComponentDefinitionIds: [ID!]!): [FeeComponentDefinition] @authorize(operations: ["update.operations.company.pricing"]) #unused

    feeComponentDefinitionTierCreate(offeringCode: OfferingCode!, input: FeeComponentTierDefinitionInput!): FeeComponentTierDefinition @authorize(operations: ["update.operations.company.pricing"]) #unused
    feeComponentDefinitionTierUpdate(feeComponentTierDefinitionId: ID!, input: FeeComponentTierDefinitionInput!): FeeComponentTierDefinition @authorize(operations: ["update.operations.company.pricing"]) #unused
}

type FeeComponentDefinition {
    id: ID!
    feeComponentTierDefinition: FeeComponentTierDefinition
    offeringCode: OfferingCode!
    key: String!
    countryCode: CountryCode
    fee: Float!
    feeUnit: FeeUnit!
    currencyCode: CurrencyCode
    frequencyUnit: FrequencyUnit!
    feeComponentType: FeeComponentType!
    variableBaseUnit: VariableBaseUnit
    discount: Discount
    status: FeeComponentDefinitionStatus!
}

type FeeComponentTierDefinition {
    id: ID!
    offeringCode: OfferingCode!
    tierLowerBound: Int
    tierUpperBound: Int
    feeComponentDefinitions: [FeeComponentDefinition!]
}

input FeeComponentDefinitionFilter {
    offeringCode: [OfferingCode!]
    status: [FeeComponentDefinitionStatus!]
}

input FeeComponentTierDefinitionInput {
    id: ID
    offeringCode: OfferingCode!
    tierLowerBound: Int
    tierUpperBound: Int
}

input FeeComponentDefinitionInput {
    id: ID
    feeComponentTierDefinitionId: ID!
    offeringCode: OfferingCode!
    key: String!
    countryCode: CountryCode
    fee: Float!
    feeUnit: FeeUnit!
    currencyCode: CurrencyCode
    frequencyUnit: FrequencyUnit!
    feeComponentType: FeeComponentType!
    variableBaseUnit: VariableBaseUnit
    discount: DiscountInput
}

enum FeeComponentDefinitionStatus {
    ACTIVE
    INACTIVE
}
