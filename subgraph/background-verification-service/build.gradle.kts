plugins {
    alias(libs.plugins.kotlin.jvm)

    alias(libs.plugins.dgs.codegen).version("5.5.0")

    id("maven-publish")
}

dependencies {
    // Project
    api(projects.subgraph.common)

    // Spring
    compileOnly(libs.spring.boot.starter.web)

    // Serialization
    implementation(libs.jackson.annotations)
}

val serviceSchemaPath: String by rootProject.extra
//val generatedResourcesPath: String by rootProject.extra
//val generatedSchemaPath: String by rootProject.extra

// TODO revert this back as soon as services are split
val mergedResourcesPath: String by rootProject.extra
val mergedSchemaPath: String by rootProject.extra
val generatedResourcesPath = mergedResourcesPath
val generatedSchemaPath = mergedSchemaPath

// TODO delete this back as soon as services are split
val mergeScript = file("../merge-service-with-sub-graphs.js")
val mergeServiceWithSubGraphs = tasks.register<Exec>("mergeServiceWithSubGraphs") {
    group = "graph"

    inputs.file(mergeScript)
    inputs.dir(serviceSchemaPath)
    inputs.dir(projects.subgraph.common.dependencyProject.layout.projectDirectory.dir(serviceSchemaPath))

    val mergeProjects = listOf(
        projects.subgraph.companyService,
        projects.subgraph.contractService,
        projects.subgraph.countryService,
        projects.subgraph.customerIntegrationService,
        projects.subgraph.expenseService,
        projects.subgraph.freelancerService,
        projects.subgraph.payableService,
        projects.subgraph.timesheetService,
    )

    val mergeProjectNames = mergeProjects.map {
        inputs.dir(it.dependencyProject.layout.projectDirectory.dir(serviceSchemaPath))
        it.name
    }

    outputs.dir(layout.buildDirectory.dir(generatedSchemaPath))

    commandLine("node", mergeScript, *mergeProjectNames.toTypedArray())
}

val mergeSubgraph = tasks.named("mergeSubgraph")
tasks.withType<ProcessResources> {
    // TODO revert this back as soon as services are split
    //dependsOn(mergeSubgraph)
    dependsOn(mergeServiceWithSubGraphs)
    from(layout.buildDirectory.dir(generatedResourcesPath))
}

tasks.withType<com.netflix.graphql.dgs.codegen.gradle.GenerateJavaTask> {
    // TODO revert this back as soon as services are split
    //dependsOn(mergeSubgraph)
    dependsOn(mergeServiceWithSubGraphs)

    language = "java"
    schemaPaths = mutableListOf(layout.buildDirectory.dir(generatedSchemaPath).get())
    packageName = "com.multiplier.background.verification"
    generateClient = false
    typeMapping = mutableMapOf(
        "ID" to "java.lang.Long",
        "Upload" to "org.springframework.web.multipart.MultipartFile",
        "Date" to "java.time.LocalDate",
        "Time" to "java.time.LocalTime",
        "DateTime" to "java.time.LocalDateTime",
        "UUID" to "java.util.UUID",
    )
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    compilerOptions {
        jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_17
    }
}

plugins.withType<JavaPlugin> {
    configure<JavaPluginExtension> {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
}

// TODO remove this, rename the module to 'core-service'
//  this will result in the library being 'core-service-graph' instead of 'core-service-fed'
//  - update usage (in core-service)
//  - update supergraph-config.yml
//configure<PublishingExtension> {
//    publications.named<MavenPublication>("maven") {
//        artifactId = project.name
//    }
//}
