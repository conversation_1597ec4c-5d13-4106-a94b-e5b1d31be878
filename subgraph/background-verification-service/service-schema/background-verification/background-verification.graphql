extend type Query {
    bgvContractsV2(contractIds: [ID!]): [BgvContractV2] @authorize(operations: ["view.operations.background-verification"], company: ["view.company.background-verification"])
}

extend type Mutation {
    updateBgvStatusV2(input: [UpdateBackgroundVerificationStatusInputV2!]): TaskResponse @authorize(operations: ["update.operations.background-verification"])
}

input UpdateBackgroundVerificationStatusInputV2 {
    contractId: ID!
    nextStatus: BackgroundVerificationStatus
    # Reports to be uploaded when changing status to PASSED or FAILED
    reports: [Upload!]
    # Optional comment field with max 1000 characters
    comment: String
}

type BgvContractV2 {
    id: ID!
    contractId: ID!
    createdAt: DateTime
    updatedAt: DateTime
    status: BackgroundVerificationStatus!
    createdBy: ID
    updatedBy: ID
    statusUpdatedAt: DateTime
    # Current reports for the current status
    currentReports: BgvReport
    # History of all previous reports
    reportHistory: [BgvReportHistory!]
    # Comment provided when updating status
    comment: String
}

# Represents a BGV report with associated metadata
type BgvReport {
    # The actual documents
    documents: [FileLink!]
    files: [Document!]
    # When the report was uploaded
    createdAt: DateTime
    key: BgvDocumentType
}

type BgvReportHistory {
    document: FileLink
    file: Document
    createdAt: DateTime
}

enum BgvCompanyConfigurationStatus {
    ACTIVE
    INACTIVE
}

enum BgvDocumentType {
    BGV_STATUS_REPORT_TEMPLATE
}

