plugins {
    alias(libs.plugins.kotlin.jvm)

    alias(libs.plugins.dgs.codegen).version("5.5.0")

    id("maven-publish")
}

dependencies {
    // Project
    api(projects.subgraph.common)

    // Spring
    compileOnly(libs.spring.boot.starter.web)

    // Serialization
    implementation(libs.jackson.annotations)
}

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

val generatedResourcesPath: String by rootProject.extra
val generatedSchemaPath: String by rootProject.extra

val mergeSubgraph = tasks.named("mergeSubgraph")
tasks.withType<ProcessResources> {
    dependsOn(mergeSubgraph)
    from(layout.buildDirectory.dir(generatedResourcesPath))
}

tasks.withType<com.netflix.graphql.dgs.codegen.gradle.GenerateJavaTask> {
    dependsOn(mergeSubgraph)

    language = "java"
    schemaPaths = mutableListOf(layout.buildDirectory.dir(generatedSchemaPath).get())
    packageName = "com.multiplier.compliance"
    generateClient = false
    typeMapping = mutableMapOf(
        "ID" to "java.lang.Long",
        "JSON" to "java.util.Map<String, Object>",
        "Date" to "java.time.LocalDate",
        "Time" to "java.time.LocalTime",
        "DateTime" to "java.time.LocalDateTime",


        )
}

tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
    compilerOptions {
        jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_21
    }
}

plugins.withType<JavaPlugin> {
    configure<JavaPluginExtension> {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
}
