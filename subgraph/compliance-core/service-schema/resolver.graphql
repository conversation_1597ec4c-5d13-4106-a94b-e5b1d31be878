enum ResolverType {
    CUSTOM
    ANTLR
}
type Resolver {
    id: ID!,
    name: String!,
    expression: String,
    type: ResolverType!,
    sources: [String!]!
    targets: [String!]!,
    scope: ComplianceScope
}

input ResolverCreateInput {
    name: String!,
    expression: String!,
    target: String!,
    scope: ComplianceScopeInput
}
input ResolverUpdateInput {
    id: ID!,
    name: String,
    expression: String,
    target: String,
    scope: ComplianceScopeInput
}

extend type Query {
    resolver(id: ID!): Resolver @authorize(operations: ["view.operations.compliance.resolver"])
    resolvers(scope: ComplianceScopeInput): [Resolver] @authorize(operations: ["view.operations.compliance.resolver"])
}

extend type Mutation {
    resolverCreate(input: ResolverCreateInput!): ID! @authorize(operations: ["create.operations.compliance.resolver"])
    resolverUpdate(input: ResolverUpdateInput!): Resolver! @authorize(operations: ["update.operations.compliance.resolver"])
    resolverDelete(id: ID!): ID! @authorize(operations: ["delete.operations.compliance.resolver"])
}
