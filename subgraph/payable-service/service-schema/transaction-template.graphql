extend type Mutation {

    """Create a new transaction template"""
    transactionTemplateCreate(input: TransactionTemplateCreateInput): TransactionTemplate   @authorize(operations: ["create.operations.transaction-template"])

    """Update an existing transaction template"""
    transactionTemplateUpdate(input: TransactionTemplateUpdateInput): TransactionTemplate   @authorize(operations: ["update.operations.transaction-template"])

    """Assign a transaction template to a company"""
    transactionTemplateAssign(input: TransactionTemplateAssignInput): TaskResponse  @authorize(operations: ["assign.operations.transaction-template"])

    """unassign a transaction template from a company"""
    transactionTemplateUnassign(input: TransactionTemplateUnassignInput): TaskResponse  @authorize(operations: ["unassign.operations.transaction-template"])

}


"""
Represents a transaction template.

A transaction template is a reusable configuration that can be used to generate financial transactions. It can be assigned to multiple companies
"""
type TransactionTemplate {
    id: UUID
    transactionType: FinancialTransactionType
    description: String
    jsonConfig: String
    isDefault: <PERSON>olean
}

input TransactionTemplateUnassignInput {
    transactionTemplateId: UUID!
    companyId: ID!
    transactionType: FinancialTransactionType!
}

input TransactionTemplateAssignInput {
    transactionTemplateId: UUID!
    companyId: ID!
    transactionType: FinancialTransactionType!
}

"""
Input for updating a transaction template
"""
input TransactionTemplateUpdateInput {
    """The ID of the transaction template to update"""
    id: UUID!
    """The new description for the transaction template. description will be updated only if this field is provided"""
    description: String
    """The new JSON configuration for the transaction template. jsonConfig will be updated only if this field is provided"""
    jsonConfig: String
    """The new default status for the transaction template. isDefault will be updated only if this field is provided"""
    isDefault: Boolean
}

input TransactionTemplateCreateInput {
    transactionType: FinancialTransactionType!
    description: String!
    jsonConfig: String!
    isDefault: Boolean!
}
