extend type Query {
    disputes(filter: DisputeFilter, page: PageRequest): DisputeResponse @authorize(company: ["view.company.disputes"], operations: ["view.operations.disputes"])
}

extend type Mutation {
    disputeCreate(input: CreateDisputeInput!) : Dispute! @authorize(company: ["upsert.company.dispute"])
    disputeResolve(input: ResolveDisputeInput!) : Dispute! @authorize(operations: ["upsert.operations.dispute"])
}

##input
input CreateDisputeInput {
    reasonCategory: DisputeReasonCategory!
    reasonSubCategory: DisputeReasonSubCategory #can be null
    description: String
    originType: DisputeOriginType!
    originId: ID! #This is the ID of the originType(Eg. For COMPANY_PAYABLE originType, this will be th id of the companyPayable)
    companyId: ID!
}

input ResolveDisputeInput {
    disputeId: ID!
}

##types
type Dispute {
    status: DisputeStatus!
    description: String
    reasonCategory: DisputeReasonCategory
    reasonSubCategory: DisputeReasonSubCategory
    originType: DisputeOriginType!
    originId: ID!
    origin: DisputeOrigin
}

##enums
enum DisputeStatus {
    IN_PROGRESS,
    ON_HOLD,
    RESOLVED,
    CLOSED
}

enum DisputeReasonCategory {
    INVOICE_INCORRECT_AMOUNT
    INVOICE_INCORRECT_FORMAT
    INVOICE_QUERY
}

enum DisputeReasonSubCategory {
    INVOICE_SERVICE_AMOUNT_INCORRECT
    INVOICE_MANAGEMENT_FEE_INCORRECT
    INVOICE_BILLING_CURRENCY_INCORRECT
    INVOICE_OTHER_FEES
}

enum DisputeOriginType {
    COMPANY_PAYABLE
}

input DisputeFilter {
    companyIds : [ID!]
    statuses: [DisputeStatus!]
    originType: DisputeOriginType!
}

type DisputeResponse {
    data : [Dispute!]
    page : PageResult
}

union DisputeOrigin = CompanyPayable
