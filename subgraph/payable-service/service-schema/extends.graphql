type Contract @key(fields: "id") @extends {
    id: ID @external
    depositPayable: [CompanyPayable]
}

interface MemberPay @key(fields: "id") @extends {
    id: ID @external
}

type CompanyMemberPay implements MemberPay @key(fields: "id") @extends {
    id: ID @external
}

type PayrollCyclePayment @key(fields: "id")  @extends {
    id: ID! @external
    payrollCycle: PayrollCycle @external
}

type PayrollCycle @key(fields: "id") @extends {
    id: ID! @external
    payrollMonth: MonthYear @external
}

type PayrollPayment @key(fields: "id") @extends {
    id: ID! @external
    contract: Contract @external
    payrollCyclePayment: PayrollCyclePayment @external
    firstInvoiceStatus: InvoiceStatus @deprecated(reason: "Use InvoiceDetail to get firstInvoiceStatus") @requires(fields: "contract{ id company { id } } payrollCyclePayment{ payrollCycle { payrollMonth { year month} } }")
    invoiceDetail: InvoiceDetail @requires(fields: "contract{ id company { id } } payrollCyclePayment{ payrollCycle { payrollMonth { year month} } }")
}

type InvoiceDetail @key(fields: "companyPayableId") {
    companyPayableId: ID!
    invoiceId: ID
    invoiceNo: String
    firstInvoiceStatus: InvoiceStatus
}

type ContractSnapshot @key(fields: "id") @extends {
    id: ID @external
    contractId: ID @external
    payrollCycle: PayrollCycle @external
    relatedCompanyPayables: [CompanyPayable!] @requires(fields: "contractId payrollCycle { id }")
}

type Company @key(fields: "id") @extends {
    id: ID @external
    pricing: Pricing
    payables (
        month: MonthYearInput @deprecated(reason: "Should use filters"),
        payableId:ID @deprecated(reason: "Should use filters"),
        payableQueryMode: PayableQueryMode = DEFAULT @deprecated(reason: "Should use filters"),
        filters: CompanyPayableFilters
    ): [CompanyPayable]      # For all the company payables (eg: invoices)
}
