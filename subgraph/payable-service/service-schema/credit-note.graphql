extend type Query {
    creditNotesWithPagination(input : CreditNoteQueryInput) : CreditNotesResult @authorize(company: ["view.company.payables"], operations: ["view.operations.payables"])
}

###Inputs###
input CreditNoteQueryInput {
    filters : CreditNoteFilters
    pageRequest : PageRequest
}

input CreditNoteFilters {
    ids : [ID!]
    status : CreditNoteStatus
    reason : CreditNoteReason
    invoiceIds : [ID!]
    companyIds : [ID!]
    createdDateRange: DateRange
    reference : String
    creditNoteNo : String
}

###Types####

type CreditNotesResult {
    data : [CreditNote!]
    pageResult : PageResult
}

type CreditNote {
    id : ID!
    company : Company!
    appliedInvoices : [Invoice!]
    status : CreditNoteStatus
    reason : CreditNoteReason
    externalId : String
    currencyCode : CurrencyCode
    amountTotal : Float
    amountApplied : Float
    amountUnapplied : Float
    reference : String
    creditNoteNo : String #Example : MTPLCN20002461
    month: Int
    year: Int
    createdDate : Date #the date on which the credit note is generated for. This could be in future or past as well.
    document: DocumentReadable  # Credit Note document
    items : [CreditNoteLineItem!]
    report: CompanyPayableReport  # report without "s" so we can create a new reports field if we have multiple reports in the future
    companyPayableId: ID
}

type CreditNoteLineItem {
    description : String
    quantity : Float
    totalCost : Float
    billingCost : Float
    currency : CurrencyCode
    type : PayableItemType
    contract : Contract
    taxRate : Float
}

###Enums###

enum CreditNoteStatus {
    DRAFT @deprecated(reason: "We don't have draft status in credit notes.")
    OPEN
    FULLY_APPLIED
    DELETED
}

enum CreditNoteReason {
    INVOICE_VOIDED
    INVOICE_CORRECTION
    DISCOUNTS
    DEPOSIT_REFUND
    SECOND_INVOICE
    INSURANCE_CANCELLATION
    WRITE_OFF
    EXPENSES_REFUNDS
    FREELANCER
    DOUBLE_PAYMENT
}
