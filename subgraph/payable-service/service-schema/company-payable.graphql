extend type Mutation {
    companyPayableGenerate(input: CompanyPayableInput!): CompanyPayableWithErrors @authorize(operations: ["generate.operations.payable"])
    companyPayableGenerateForContract(types: [PayableItemType!]!, contractID: ID!): Company<PERSON>aya<PERSON> @deprecated(reason: "Use the generateDepositForContract instead") @authorize(operations: ["generate.operations.payable"])
    companyBindingCreate(input: CompanyBindingInput!): CompanyBinding @authorize(operations: ["generate.operations.binding"], company: ["generate.company.binding"])
    sendInvoiceToContactEmail(input: ContactEmailInput!) : TaskResponse @authorize(operations: ["generate.operations.payable"])
    updateOrCreateCompanyBinding(input: UpdateOrCreateCompanyBindingInput!): CompanyBinding @authorize(operations: ["generate.operations.payable"])
    companyPayableSyncFromExternalSystem(input : CompanyPayableSyncInput!) : TaskResponse @authorize(operations: ["sync.operations.xero"])
    updateInvoiceAndBundleOnExternalInvoicePaid(externalInvoiceId: String!): TaskResponse @authorize(operations: ["update.operations.invoice-and-bundle"])
    companyPayableUploadInvoiceSourceReport(input: InvoiceSourceReportUploadInput!): TaskResponse @authorize(operations: ["generate.operations.payable"])
    financialTransactionGenerate(input: FinancialTransactionInput!): [TransactionCommand!]! @authorize(operations: ["generate.operations.payable"])
    financialTransactionCollect(input: FinancialTransactionInput!): Boolean @authorize(operations: ["generate.operations.payable"])
    migrateSeatId(companyIds: [ID!]): TaskResponse @authorize(operations: ["generate.operations.payable"])
    updateCompanyOnExternalSystem(input: UpdateCompanyOnExternalSystemInput!): TaskResponse @authorize(operations: ["update.operations.company"])
    resyncFinancialTransactions(input: ResyncFinancialTransactionsInput!): TaskResponse @authorize(operations: ["sync.operations.payables"])
}

extend type Query {
    companyPayableDownloadInvoiceSourceReport(input: CompanyPayableInvoiceSourceReportExportInput!): DocumentReadable! @authorize(operations: ["view.operations.payables"]) # To download detailed and summarized invoice source reports from the platform
    generateSalaryInvoiceSourceReports(input: SalaryInvoiceSourceReportGenerationInput!): TaskResponse @authorize(operations: ["view.operations.payables"])
    downloadSalaryInvoiceSourceReport(input: SalaryInvoiceSourceReportDownloadInput!): [DocumentReadable!] @authorize(operations: ["view.operations.payables"])
    companyPayableInvoiceSourceDetailedReportFetch(input: CompanyPayableInvoiceSourceReportExportInput!): [CompanyInvoiceSourceRecord] @authorize(operations: ["view.operations.payables"])
    companyPayablesWithPagination(filters: CompanyPayableFilters,
        pageRequest: PageRequest,    # supportes sorting on ["createdOn", "updatedOn"]. default = "createdOn" (descending)
    ) : CompanyPayablesResult @authorize(operations: ["view.operations.company"])
    companyPayableDownloadInvoiceSourceReportFromLink(input: InvoiceSourceReportFromLinkInput!): DocumentReadable! @authorize(resource: ["view.resource.invoice-source-report.{resource_id}"])
    downloadCompanyPayableReport(input: DownloadCompanyPayableReportInput!): DocumentReadable! @authorize(operations: ["view.operations.payables"], company: ["view.company.payables"])
    financialTransactionInspect(transactionId: String!): FinancialTransactionInspectorResponse! @authorize(operations: ["view.operations.payable"])
    financialTransactionInspectV2(filter: FinancialTransactionFilter!): FinancialTransactionInspectorResponse! @authorize(operations: ["view.operations.payable"])
}

input ContactEmailInput {
    companyPayableID: ID
    contractID: ID
}

input UpdateOrCreateCompanyBindingInput {
    companyId: ID!
    externalId: String!
}

input CompanyPayableInput {
    companyId: ID @deprecated(reason: "Use companyIds instead")
    invoiceDate: DateTime
    invoiceDueDate: DateTime
    invoiceReference: String
    companyIds : [ID]
    invoiceType: Int
    invoiceMonth : PayableMonthInput
    autoSubmit: Boolean = false
    forcedContractIds: [ID!]
}

input CompanyPayableInvoiceSourceReportExportInput {
    companyId: ID
    companyIds: [ID]
    payableMonth: PayableMonthInput # selected payroll month
    """
    which invoice the data should be sourced from
    1 = First Invoice
    2 = Second Invoice
    """
    invoiceType: Int
    storeFile: Boolean = false
}

input SalaryInvoiceSourceReportGenerationInput {
    companyIds: [ID!]
    payableMonth: PayableMonthInput!
}

input SalaryInvoiceSourceReportDownloadInput {
    companyId: ID!
    payableMonth: PayableMonthInput!
    companyPayableId: ID
}

input DownloadCompanyPayableReportInput {
    companyId: ID!
    payableMonth: PayableMonthInput! # selected payroll month
    type: CompanyPayableReportType!
    payableId: ID
}

input InvoiceSourceReportUploadInput {
    file: Upload
}

input InvoiceSourceReportByHashInput {
    hash: String!
}

input InvoiceSourceReportByInvoiceIdInput {
    invoiceId: ID!
}

input InvoiceSourceReportFromLinkInput {
    companyId: ID!
    invoiceMonth: Int!
    invoiceYear: Int!
    authCode: String!
    companyPayableId: ID
}

input PayableMonthInput {
    month: Int
    year: Int
    cycle: Int
}

input CompanyBindingInput {
    companyId: ID
}

input CompanyPayableFilters {
    payableIds: [ID]
    companyId: ID
    companyIds: [ID]
    monthYear: PayableMonthInput
    invoiceNo: String
    reference: String
    statuses: [PayableStatus]
    queryMode: PayableQueryMode = DEFAULT
    createdDateRange: DateRange
    payableType: CompanyPayableType
}

input CompanyPayableSyncInput {
    companyIds : [ID!] # the company ids for which we want to sync the invoices.
}

input UpdateCompanyOnExternalSystemInput {
    companyIds: [ID!]!
}

type CompanyBinding @key(fields: "id") {
    id: ID!
    company: Company
    contactId: String @deprecated(reason: "use customerId instead, when Netsuite is fully implemented this will be removed.")
    customerId: String
    externalSystem: ExternalSystemType
}

type CompanyPayable @key(fields: "id") {
    id: ID!
    company: Company @authorize(operations: ["view.operations.payables"], company: ["view.company.payables"]) # The company associate with this payable.
    status: PayableStatus       # Payable status. Eg: DRAFT
    invoice: Invoice            # Payable invoice.
    creditNote: CreditNote
    items: [PayableItem]        # All payable items.
    date: DateTime              # Payable date, to identify the time applicability for this payable.
    totalAmount: Float          # Payable total amount. Total value of all payable items.
    currency: CurrencyCode
    month: Int
    year: Int
    createdOn: DateTime         # Payable created date
    updatedOn: DateTime         # Payable last updated date
    type: CompanyPayableType    # Company payable type (Eg: First invoice, second invoice, freelancer, deposit, etc)
    report: CompanyPayableReport # report without "s" so we can create a new reports field if we have multiple reports in the future
}

type CompanyPayableReport {
    companyId: ID!
    month: Int!
    year: Int!
    type: CompanyPayableReportType
}

type Invoice {
    id: ID!
    createdDate: DateTime       # Invoice created date.
    dueDate: DateTime           # Invoice due date.
    fullyPaidOnDate: DateTime
    reference: String           # Reference text
    status: InvoiceStatus       # Invoice status. Eg: DRAFT
    invoiceNo: String           # Xero invoice no. Eg: "INV-0011"
    invoiceId: String           # Xero invoice id. Eg: "e453d445-0eda-44fb-9f99-fd5b165530f2"
    amountPaid: Float
    amountDue: Float
    document: DocumentReadable  # Invoice document
    externalLink: String        # Xero/Netsuite invoice online link. eg:  "https://in.xero.com/eWUOj9oSlsjFyU6yiyrC"
    externalSystem: ExternalSystemType
    creditNotes: [CreditNote!]
    reason:  InvoiceReason
    type: InvoiceType
    totalAmount: Float
    companyPayableId: ID
}

union PayableItem = MemberPayrollCost | GenericPayableCost

# Total payroll cost of a member, including their gross salary, expenses,
# supplements and statutory contributions.
type MemberPayrollCost {
    memberPay: MemberPay
    totalCost: Float        #Amount in member currency
    billingCost: Float      #Amount in billing currency
    currency: CurrencyCode  #Member currency
    description: String
    type: PayableItemType
    contract: Contract
}

# Generic type for similar payable costs
type GenericPayableCost {
    description: String
    totalCost: Float        #Amount in member currency
    billingCost: Float      #Amount in billing currency
    currency: CurrencyCode  #Member currency
    type: PayableItemType
    contract: Contract
}

type CompanyPayableWithErrors {
    companyPayables : [CompanyPayable]
    errorResponses : [TaskResponse]
}

type CompanyInvoiceSourceRecord {
    id: ID
    contract: Contract
    currency: CurrencyCode
    billingCurrency: CurrencyCode
    amountGross: Float
    contributions: [CompanyPayableComponent]
    deductions: [CompanyPayableComponent]
    additional: [CompanyPayableComponent]

    totalContributionAmount: Float
    totalDeductionsAmount: Float
    totalExpenseAmount: Float
    totalSupplementAmount: Float
    totalAllowanceAmount: Float
    totalBonusAmount: Float
    totalCommissionAmount: Float

    firstInvoiceGrossSalary: Float
    firstInvoiceUnitPrice: Float
    secondInvoiceGrossSalary: Float
    secondInvoiceUnitPrice: Float

    aggregatedGross: Float
    amountTotalCost: Float

    fxRateApplied: Float
    totalBillingAmount: Float
}

type CompanyPayableComponent {
    name: String
    value: Float
    type: CompanyPayableComponentType
}

type CompanyPayablesResult {
    data: [CompanyPayable]
    pageResult: PageResult
}

enum CompanyPayableComponentType {
    CONTRIBUTIONS
    DEDUCTIONS,
    OTHER
}

enum PayableStatus {
    DRAFT
    SUBMITTED
    AUTHORIZED
    DELETED
    VOIDED
    PARTIALLY_PAID
    PAID
    PENDING
    OVERDUE
}

enum InvoiceStatus {
    DRAFT
    SUBMITTED
    AUTHORIZED
    DELETED
    VOIDED
    PAID
    PENDING
    OVERDUE
}

enum PayableItemType {
    MEMBER_PAYROLL_COST
    MEMBER_PRORATED_PAYROLL_COST
    BILLED_INSURANCE_PREMIUM
    MEMBER_INSURANCE_COST
    MEMBER_DEPOSIT
    MEMBER_MANAGEMENT_FEE
    MEMBER_PAYROLL_COST_FOR_FREELANCER
    MEMBER_MANAGEMENT_FEE_FOR_FREELANCER
    MEMBER_PROCESSING_FEE_FOR_FREELANCER
    MEMBER_GROSS_SALARY
    MEMBER_GROSS_SALARY_REVERSAL
    MEMBER_MANAGEMENT_FEE_REVERSAL
    MEMBER_PROCESSING_FEE_FOR_EOR
    MEMBER_PEO_PAYROLL_COST @deprecated(reason: "use MEMBER_GLOBAL_PAYROLL_FUNDING_COST to sync with business terms")
    MEMBER_GLOBAL_PAYROLL_FUNDING_COST
    ANNUAL_MEMBER_MANAGEMENT_FEE
    MEMBER_TOTAL_PAYROLL_LESS_EXPENSE_COST
    MEMBER_TOTAL_EXPENSE_COST
    BILLED_GROSS_SALARY_SUPPLEMENTARY
    BILLED_GROSS_SALARY
    BILLED_MANAGEMENT_FEE
    MANAGEMENT_FEE_EOR_PAYROLL
    VAT_PAYROLL_COST
    VAT_PAYROLL_EXPENSE
    VAT_MANAGEMENT_FEE_EOR_PAYROLL
    VAT_ANNUAL_MANAGEMENT_FEE_EOR
    ANNUAL_MANAGEMENT_FEE_AOR @deprecated(reason: "use ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR or ANNUAL_MANAGEMENT_FEE_AOR_FREELANCER instead")
    VAT_ANNUAL_MANAGEMENT_FEE_AOR @deprecated(reason: "use ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR or ANNUAL_MANAGEMENT_FEE_AOR_FREELANCER instead")
    ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR
    ANNUAL_MANAGEMENT_FEE_AOR_FREELANCER
    VAT_ANNUAL_MANAGEMENT_FEE_AOR_CONTRACTOR
    VAT_ANNUAL_MANAGEMENT_FEE_AOR_FREELANCER
    SEVERANCE_DEPOSIT_EOR_PAYROLL
    VAT_BILLED_MANAGEMENT_FEE
    ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL
    VAT_ADDITIONAL_MANAGEMENT_FEE_EOR_PAYROLL
    ONE_TIME_SETUP_FEE
    PAYSLIP_MINIMUM_COMMITMENT
    PER_PAYSLIP_FEE
    TOTAL_PAYMENTS
    STAT_FILING
    YEAR_END_DOCUMENTATION
    MEMBER_PAYOUT_FEE_FOR_FREELANCER
    VAS_INCIDENT_LAPTOP_PAYMENT
    VAS_INCIDENT_LAPTOP_MANAGEMENT_FEE
    VAS_INCIDENT_MONITOR_PAYMENT
    VAS_INCIDENT_MONITOR_MANAGEMENT_FEE
    VAS_INCIDENT_ACCESSORIES_PAYMENT
    VAS_INCIDENT_ACCESSORIES_MANAGEMENT_FEE
    VAS_INCIDENT_DISCOUNT
    SEVERANCE_DEPOSIT_EOR_PAYROLL_REFUND
    ORDER_FORM_ADVANCE_EOR
    ORDER_FORM_ADVANCE_AOR
    ORDER_FORM_ADVANCE_GLOBAL_PAYROLL
    VAS_INCIDENT_PICKUP_DELIVERY_AMOUNT
    VAS_INCIDENT_PICKUP_DELIVERY_FEE
    VAS_INCIDENT_STORAGE_AMOUNT
    VAS_INCIDENT_STORAGE_FEE
    VAS_INCIDENT_CONTRACT_CUSTOMISATION_AMOUNT
    VAS_INCIDENT_CONTRACT_CUSTOMISATION_FEE
    VAS_INCIDENT_LEGAL_CONSULTATION_FEE
    VAS_INCIDENT_OTHERS_SERVICE_AMOUNT
    VAS_INCIDENT_OTHERS_SERVICE_FEE
    VAS_INCIDENT_OTHERS_VISA_FEE
    ORDER_FORM_ADVANCE_ADJUSTMENT_EOR_SERVICE_FEE
    ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_CONTRACTOR
    ORDER_FORM_ADVANCE_ADJUSTMENT_AOR_FREELANCER
    ORDER_FORM_ADVANCE_ADJUSTMENT_GP_ONE_TIME_SETUP_FEE
    ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PAYSLIP_MINIMUM_COMMITMENT
    ORDER_FORM_ADVANCE_ADJUSTMENT_GP_PER_PAYSLIP_FEE
    ORDER_FORM_ADVANCE_ADJUSTMENT_GP_TOTAL_PAYMENTS
    ORDER_FORM_ADVANCE_ADJUSTMENT_GP_STAT_FILING
    ORDER_FORM_ADVANCE_ADJUSTMENT_GP_YEAR_END_DOCUMENTATION
    PAYROLL_OFFCYCLE_COST
    PAYROLL_OFFCYCLE_EXPENSE
    PAYROLL_OFFCYCLE_MANAGEMENT_FEE
    VAT_PAYROLL_OFFCYCLE_COST
    VAT_PAYROLL_OFFCYCLE_EXPENSE
    VAT_PAYROLL_OFFCYCLE_MANAGEMENT_FEE
    BANK_FEE
    STANDARD_BACKGROUND_VERIFICATION_CHECKS_FEE
    OFFBOARDING_FEE_EOR
    VAT_OFFBOARDING_FEE_EOR
    INSURANCE_PLATFORM_FEE
}

enum PayableQueryMode {
    DEFAULT
    DRYRUN_DETAILED
    DRYRUN_SUMMARIZED
}

enum CompanyPayableType {
    FIRST_INVOICE
    SECOND_INVOICE
    FREELANCER # Every company_payable of this "FREELANCER" type has a 1-1 mapping with payment.member_payable_company_invoice, via company_payable_id (FK), the others don't
    DEPOSIT
    UNKNOWN
    INSURANCE
    GLOBAL_PAYROLL_FUNDING
    ANNUAL_PLAN
    ANNUAL_PLAN_AOR
    GP_SERVICE_INVOICE
    VAS_INCIDENT_INVOICE
    ORDER_FORM_ADVANCE
    PAYROLL_OFFCYCLE_INVOICE
    VAS_BACKGROUND_VERIFICATION_INVOICE
}

enum ExternalSystemType {
    XERO
    NETSUITE
}

enum InvoiceReason {
    INVOICE_VOIDED
    OTHERS
    CORRECTION_OF_INVOICE
    FIRST_INVOICE
    SECOND_INVOICE
    DEPOSITS
    INSURANCE
    VISA
    FIRST_INVOICE_SUPPLEMENTARY
    GP_SERVICE_INVOICE
    VAS_INCIDENT_INVOICE
    PAYROLL_OFFCYCLE_INVOICE
    VAS_BACKGROUND_VERIFICATION_INVOICE
}

enum InvoiceType {
    GROSS
    SALARY
    DEPOSIT
    INSURANCE
    FREELANCER
    GLOBAL_PAYROLL_FUNDING
    ANNUAL_PLAN
    ANNUAL_PLAN_AOR
    GP_SERVICE_INVOICE
    VAS_INCIDENT_INVOICE
    ORDER_FORM_ADVANCE
    PAYROLL_OFFCYCLE_INVOICE
    VAS_BACKGROUND_VERIFICATION_INVOICE
}

enum CompanyPayableReportType {
    GROSS_INVOICE_SOURCE_REPORT
    SALARY_INVOICE_SOURCE_REPORT
}

enum RecordType {
    INVOICE
    CREDIT_NOTE
    DEBIT_NOTE
}


# BEGIN - IE types/inputs section
input FinancialTransactionInput {
    financialTransactionType: FinancialTransactionType
    companyIds: [ID]
    dateRange: DateRange
    transactionDate: DateTime
    frequency: String
    companyIdToForcedContractIds: [CompanyIdToForcedContractIdsInput!]
    autoSubmit: Boolean = false
    companyIdToEntityIds: [CompanyIdToEntityIdsInput!]
    payrollCycleIds: [String!]
}

input CompanyIdToForcedContractIdsInput {
    companyId: ID!
    forcedContractIds: [ID!]
}

input CompanyIdToEntityIdsInput {
    companyId: ID!
    entityIds: [ID!]
}

input ResyncFinancialTransactionsInput {
    records: [FinancialTransactionRecordInput!]!
}

input FinancialTransactionRecordInput {
    id: String!
    type: RecordType!
}

input FinancialTransactionFilter {
    transactionIds: [String!]
    payrollCycleIds: [String!]
    transactionTypes: [FinancialTransactionType!]
}

enum FinancialTransactionType {
    FIRST_INVOICE
    SECOND_INVOICE
    DEPOSIT_INVOICE
    GLOBAL_PAYROLL_FUNDING_INVOICE
    INSURANCE_INVOICE
    ANNUAL_PLAN_INVOICE
    ANNUAL_PLAN_AOR_INVOICE
    GP_SERVICE_INVOICE
    FREELANCER_INVOICE
    VENDOR_BILL
    VAS_INCIDENT_INVOICE
    ORDER_FORM_ADVANCE_INVOICE
    PAYROLL_OFFCYCLE_INVOICE
    VAS_BACKGROUND_VERIFICATION_INVOICE
}

interface FinancialTransaction {
    company: Company
    dueDate: DateTime
    date: DateTime
    reference: String
    year: Int
    month: Int
    currency: CurrencyCode
    items: [TransactionPayableItem]
    transactionId: String
}

type CreditNoteTransaction implements FinancialTransaction {
    company: Company
    dueDate: DateTime
    date: DateTime
    reference: String
    year: Int
    month: Int
    currency: CurrencyCode
    items: [TransactionPayableItem]
    transactionId: String
}

type DefaultFinancialTransaction implements FinancialTransaction {
    company: Company
    dueDate: DateTime
    date: DateTime
    reference: String
    year: Int
    month: Int
    currency: CurrencyCode
    items: [TransactionPayableItem]
    transactionId: String
}

type InvoiceTransaction implements FinancialTransaction {
    company: Company
    dueDate: DateTime
    date: DateTime
    reference: String
    year: Int
    month: Int
    currency: CurrencyCode
    items: [TransactionPayableItem]
    transactionId: String
}

type TransactionPayableItem {
    lineItemType: String
    contractId: ID
    companyId: ID
    description: String
    amountInBaseCurrency: Float
    billableCost: Float
    baseCurrency: String
    cycle: String
    countryCode: String
    countryName: String
    itemCount: Int
    periodStartDate: String
    periodEndDate: String
    seatId: ID
    taxType: String
}

type TransactionCommand {
    transactionId: String
    transactionType: FinancialTransactionType
    companyId: ID
    dateRange: DateRangeOutput
    transactionDate: DateTime
    cycle: String
    tracingContext: [TracingContextEntry]
    transactionStatus: String
    transactionAction: String
}

type TracingContextEntry {
    key: String
    value: String
}

type DateRangeOutput {
    startDate: DateTime,
    endDate: DateTime
}

type FinancialTransactionInspectorResponse {
    transactionCommands: [TransactionCommand]
    financialTransactions: [FinancialTransaction]
}

# END - IE types/inputs section
