extend type Mutation {
    """
    **Manually Advance Invoice Balances Resync**

    This mutation is specifically used for **manual invoices** to resync their balances. It performs the following steps:

    * **Validate Company Balance**: Ensures the company balance doesn't have any manual adjustments that would conflict with the resync.
    * **Wipe Existing Balance**: Clears out the current balances to prepare for the new data.
    * **Validate Input & Register**: Validates the provided input data and then registers these new balances accordingly.
    """
    manualAdvanceInvoiceBalancesResync(input: [ManualAdvanceInvoiceBalancesResyncInput!]!): [AdvanceCollectionBalancesResyncResult!]! @authorize(operations: ["generate.operations.payable"])

    """
    **Advance Collection Balances Resync**

    This mutation is used for **system-generated invoices** to resync their balances. It will:

    * **Validate No Adjustment**: Ensures that the company's balances have no manual adjustments.
    * **Wipe Existing Balances**: Clears out all existing balances for the specified company/companies.
    * **Validate & Register**: Validates companies has all system payables + registers these new balances accordingly.
    """
    advanceCollectionBalancesResync(companyIds: [ID!]!): [AdvanceCollectionBalancesResyncResult!]! @authorize(operations: ["sync.operations.payables"])
}

input ManualAdvanceInvoiceBalancesResyncInput {
    companyId: ID!
    balances: [AdvanceCollectionBalanceInput!]!
}

input AdvanceCollectionBalanceInput {
    entityId: ID
    lineCode: String!
    dimensions: [KeyValueInput!]!
    targetType: String!
    amount: AmountInput!
    invoiceNo: String!
}

# Succeed all or fail all, no partial success
type AdvanceCollectionBalancesResyncResult {
    executionResult: TaskResponse!
    companyId: ID!
    balanceIds: [ID!] # empty in case of failure
}
