extend type Mutation {
    countryValueAddedTaxCreate(input: CountryValueAddedTaxInput!): CountryValueAddedTax @authorize(operations: ["use.operations.company.vat"])
    companyCountryValueAddedTaxCreate(input: CompanyCountryValueAddedTaxInput!): CompanyCountryValueAddedTax @authorize(operations: ["use.operations.company.vat"])
    countryValueAddedTaxUpdate(input: CountryValueAddedTaxInput!): CountryValueAddedTax @authorize(operations: ["use.operations.company.vat"])
    companyCountryValueAddedTaxUpdate(input: CompanyCountryValueAddedTaxInput!): CompanyCountryValueAddedTax @authorize(operations: ["use.operations.company.vat"])
    countryValueAddedTaxDelete(input: CountryValueAddedTaxDeleteInput!): TaskResponse @authorize(operations: ["use.operations.company.vat"])
    companyCountryValueAddedTaxDelete(input: CompanyCountryValueAddedTaxDeleteInput!): TaskResponse @authorize(operations: ["use.operations.company.vat"])
}

input CountryValueAddedTaxInput {
    countryCode: CountryCode
    valueAddedTax: ValueAddedTaxInput
}

input CompanyCountryValueAddedTaxInput {
    companyId: ID
    countryCode: CountryCode
    valueAddedTax: ValueAddedTaxInput
}

input CountryValueAddedTaxDeleteInput {
    countryCode: CountryCode
}

input CompanyCountryValueAddedTaxDeleteInput {
    companyId: ID
    countryCode: CountryCode
}

input ValueAddedTaxInput {
    type: ValueAddedTaxType
    value: Float
}

type CountryValueAddedTax {
    countryCode: CountryCode
    valueAddedTax: ValueAddedTax
}

type CompanyCountryValueAddedTax {
    company: Company
    countryCode: CountryCode
    valueAddedTax: ValueAddedTax
}

type ValueAddedTax {
    type: ValueAddedTaxType
    value: Float
}

enum ValueAddedTaxType {
    PERCENTAGE
    ABSOLUTE
}
