extend type Mutation {
    contractSeveranceHistoryCreate(
        input: ContractSeveranceHistoryCreateInput!
    ): [ContractSeveranceHistory!]! @authorize(operations: ["create.operations.contract-severance-history"])
}

type ContractSeveranceHistory {
    id: ID!
    contractId: ID!
    companyPayable: CompanyPayable!
    amount: Amount!
    startDate: Date!
    endDate: Date!
}

input ContractSeveranceHistoryCreateInput {
    companyIds: [ID]
    timePeriod: TimePeriodInput!
    batchSize: Int
}

input TimePeriodInput {
    startDate: Date
    endDate: Date
}
