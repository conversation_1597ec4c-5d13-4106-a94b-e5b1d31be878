extend type Query {
    employeePricingForCountry(country: CountryCode) : [EmployeePricing] @authorize(company: ["view.company.payables"], operations: ["view.operations.payables"])
    employeePricingForRegion(region: Region) : [EmployeePricing] @authorize(company: ["view.company.payables"], operations: ["view.operations.payables"])
    employeePricingDefaultCountries : EmployeePricingDefault @authorize(company: ["view.company.payables"], operations: ["view.operations.payables"])
    #pricing(companyId: ID!) : Pricing
}

extend type Mutation {
    updateCompanyPricing(
        companyId: ID!,
        pricing: PricingInput!,
        skipCreatingMsaAddendum: Boolean = false
    ) : Pricing @authorize(operations: ["update.operations.company.pricing|update.operations.payables"])
}

type Pricing {  # High level composition of pricing for a particular company.
    id: ID
    company: Company
    services: String
    setupFee: Float
    serviceFee: Float       @deprecated
    deposit: Float          @deprecated "deposit is no longer a fixed value. Use depositTerm instead"
    resourceClaim: String   @deprecated "removing the entire section from the front-end"
    billingCurrencyCode: CurrencyCode
    paymentTermInDays: Int
    depositTerm: DepositTerm
    discountTerms: [DiscountTerm]
    customDiscountTerm: String
    globalPricing: [GlobalPricing]
    visaGlobalPricing: [VisaGlobalPricing]
    offboardingGlobalPricing: [OffboardingGlobalPricing]
    employeePricing: [EmployeePricing] # This always will have exceptional countries
    visaPricing: [EmployeePricing]
}

type DepositTerm {
    termInMonths: Int
    maxDeposit: Float
}

type DiscountTerm @key(fields: "id")  {
    id: ID
    discount: Float!
    discountType: DiscountType!
    discountRules: [DiscountRule]  # the set of rules that should match in order to get the discount value in the term
}

union DiscountRule = DiscountRuleMemberBased | DiscountRuleTimeBound | DiscountRuleCountry | DiscountRuleRegion | DiscountRuleSalaryBased | DiscountRuleDeadlineBased

type DiscountRuleSalaryBased  {
    id: ID!
    salaryLowerBound: Float
    salaryUpperBound: Float
}

type DiscountRuleMemberBased  {
    id: ID!
    memberLowerBound: Int
    memberUpperBound: Int
}

type DiscountRuleTimeBound  {
    id: ID!
    fromMonth: Int
    toMonth: Int
}

type DiscountRuleCountry  {
    id: ID!
    countries: [CountryCode]! # (empty + anyCountry=false) for all countries individually
    anyCountry: Boolean
    excludedCountries: [CountryCode!]
}

type DiscountRuleRegion  {
    id: ID!
    region: Region
}

type DiscountRuleDeadlineBased {
    id: ID!
    deadline: Date
    type: DiscountRuleDeadlineType
}

enum DiscountRuleDeadlineType {
    MSA_SIGNED
    MEMBER_ONBOARDED
}

interface EmployeePricing {
    id: ID!
    employeeType: ContractType
    fixedRate: Float
}

type CountryEmployeePricing implements EmployeePricing {
    id: ID!
    employeeType: ContractType
    fixedRate: Float
    country: CountryCode
}

type RegionEmployeePricing implements EmployeePricing {
    id: ID!
    employeeType: ContractType
    fixedRate: Float
    region: Region
}

type VisaEmployeePricing implements EmployeePricing {
    id: ID!
    employeeType: ContractType
    fixedRate: Float
    country: CountryCode
    validUntil: Date
}

type GlobalPricing {
    employeeType: ContractType
    fixedRate: Float
}

type VisaGlobalPricing {
    employeeType: ContractType
    fixedRate: Float
}

type OffboardingGlobalPricing {
    employeeType: ContractType!
    offboardingFeeType: OffboardingFeeType!
    status: OffboardingFeeStatus!
}

type EmployeePricingDefault {
    employeePricings: [EmployeePricing] # Pricings for exceptional countries only
    globalPricing: [GlobalPricing]
    visaGlobalPricing: [VisaGlobalPricing]
    visaPricing: [EmployeePricing]
}

input PricingInput {
    services: String!
    setupFee: Float!
    serviceFee: Float   #deprecated will be removed after UI changes are done
    deposit: Float
    resourceClaim: String   #deprecated will be removed after UI changes are done
    billingCurrencyCode: CurrencyCode
    paymentTermInDays: Int   # period from invoice generation date to due date
    depositTerm: DepositTermInput
    discountTerms: [DiscountTermInput]!
    customDiscountTerm: String
    globalPricing: [CompanyGlobalPricingInput]
    visaGlobalPricing: [CompanyVisaGlobalPricingInput]
    employeePricing: [CompanyEmployeePricingInput]!
    visaPricing: [CompanyVisaPricingInput]
    offboardingGlobalPricing: [OffboardingGlobalPricingInput!]
}

input DepositTermInput {
    termInMonths: Int
    maxDeposit: Float
}

input CompanyEmployeePricingInput {
    region: Region
    country: CountryCode
    employeeType: ContractType!
    fixedRate: Float!
}

input CompanyVisaPricingInput {
    country: CountryCode
    employeeType: ContractType!
    fixedRate: Float!
    validUntil: Date
}

input CompanyGlobalPricingInput {
    employeeType: ContractType!
    fixedRate: Float!
}

input CompanyVisaGlobalPricingInput {
    employeeType: ContractType!
    fixedRate: Float!
}

input DiscountTermInput {
    discount: Float!
    discountType: DiscountType!
    discountRules: [DiscountRuleInput]
}

input DiscountRuleInput {
    memberBasedDiscount: DiscountRuleMemberBasedInput
    salaryBasedDiscount: DiscountRuleSalaryBasedInput
    timeBasedDiscount: DiscountRuleTimeBoundInput
    countryBasedDiscount: DiscountRuleCountryInput
    regionBasedDiscount: DiscountRuleRegionInput
    deadlineBasedDiscount: DiscountRuleDeadlineBasedInput
    globalDiscount: Boolean
}

input DiscountRuleSalaryBasedInput {
    salaryLowerBound: Float
    salaryUpperBound: Float
}

input DiscountRuleMemberBasedInput {
    memberLowerBound: Int
    memberUpperBound: Int
}

input DiscountRuleTimeBoundInput {
    fromMonth: Int
    toMonth: Int
}

input DiscountRuleCountryInput {
    anyCountry: Boolean!
    countries: [CountryCode]!
    excludedCountries: [CountryCode!]
}

input DiscountRuleRegionInput {
    region: Region
}

input DiscountRuleDeadlineBasedInput {
    deadline: Date!
    deadlineType: DiscountRuleDeadlineType!
}

input OffboardingGlobalPricingInput {
    employeeType: ContractType!
    offboardingFeeType: OffboardingFeeType!
    status: OffboardingFeeStatus!
}

enum DiscountType {
    """The 'discount' field contains the percentage value. i.e. fee = 500, discount 50, discounted value = 250 [500 - (500 * 50%)]"""
    PERCENT
    """The 'discount' field contains the absolute discount value. i.e. fee = 500, discount 50, discounted value = 450 [500 - 50]"""
    ABSOLUTE
    """The 'discount' field contains the flat price value. i.e. fee = 500, discount 50, discounted value = 50 [regardless the fee]"""
    FLAT_PRICE
}

enum Region {
    ASIA
    AFRICA
    # TODO: @Teja to confirm
}

enum OffboardingFeeType {
    STANDARD,
    COMPLEX
}

enum OffboardingFeeStatus {
    ACTIVE,
    INACTIVE
}
