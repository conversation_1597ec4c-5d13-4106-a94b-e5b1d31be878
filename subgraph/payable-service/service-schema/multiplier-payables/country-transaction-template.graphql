extend type Mutation {

    """Create a new Country transaction template"""
    countryTransactionTemplateCreate(input: CountryTransactionTemplateCreateInput): CountryTransactionTemplate   @authorize(operations: ["create.operations.country-transaction-template"])

    """Update an existing Country transaction template"""
    countryTransactionTemplateUpdate(input: CountryTransactionTemplateUpdateInput): CountryTransactionTemplate   @authorize(operations: ["update.operations.country-transaction-template"])
}


type CountryTransactionTemplate {
    id: UUID
    transactionType: FinancialTransactionType
    description: String
    jsonConfig: String
    countryCode: String
}

input CountryTransactionTemplateCreateInput {
    transactionType: FinancialTransactionType!
    description: String!
    jsonConfig: String!
    countryCode: CountryCode!
}

input CountryTransactionTemplateUpdateInput {
    id: UUID!
    description: String
    jsonConfig: String
    countryCode: CountryCode
}
