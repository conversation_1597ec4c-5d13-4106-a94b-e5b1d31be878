extend type Query {
    reconciliationWorkflow(payrollCycleId: ID!): ReconciliationWorkflowResponse!  @authorize(operations: ["use.operations.payroll"])
    reconciliationReport(payrollCycleId: ID!): DocumentReadable! @authorize(operations: ["use.operations.payroll"])
}

extend type Mutation {
    reconciliationWorkflowRefresh(payrollCycleId: ID!): ReconciliationWorkflowResponse!  @authorize(operations: ["use.operations.payroll"])
    reconciliationMismatchSetIgnored(input: ReconciliationSetIgnoredInput!): ReconciliationWorkflowResponse!  @authorize(operations: ["use.operations.payroll"])
    reconciliationSetIgnoreAll(input: ReconciliationSetIgnoredAllInput!): ReconciliationSetIgnoredAllResponse!  @authorize(operations: ["use.operations.payroll"])
}

input ReconciliationSetIgnoredInput {
    payrollCycleId: ID!
    contractIds: [ID!]!
    ignored: Boolean!
}

input ReconciliationSetIgnoredAllInput {
    payrollCycleId: ID!
    ignored: Boolean!
}

type ReconciliationSetIgnoredAllResponse {
    updatedCount: Int!
}

type ReconciliationWorkflowResponse {
    payrollCycleId: ID!
    workflowStatus: ReconWorkflowStatus!
    status: ReconciliationStatus
    summary: ReconciliationSummary
    detail(input: ReconciliationDetailRequest!): ReconciliationDetailResponse
}

type ReconciliationSummary {
    totalContractCount: Int!
    reconciledContractCount: Int!
    ignoredContractCount: Int!
}

input ReconciliationDetailRequest {
    contractIds: [ID!]
    status: ReconItemGroupStatus!
    pageRequest: PageRequest # supports sort by contractId, status
}

enum ReconciliationDetailSortBy {
    CONTRACT_ID,
    RECON_STATUS
}

type ReconciliationDetailResponse {
    contracts: [ContractReconItem!]!
    pageResult: PageResult!
}

type ContractReconItem {
    contract: Contract!
    status: ReconItemStatus!
    ignored: Boolean
    payItemReconciliations: [PayCategoryReconciliation!]!
}

type PayCategoryReconciliation {
    category: PayComponentCategory!
    inputAmount: Float # would be null if ReconItemStatus is MISSING_IN_INPUT
    outputAmount: Float # would be null if ReconItemStatus is MISSING_IN_OUTPUT
    inputCurrency: CurrencyCode
    outputCurrency: CurrencyCode
    status: ReconItemStatus!
    payItemReconciliations: [PayItemReconciliation!]!
}

type PayItemReconciliation {
    name: String!
    inputAmount: Float # would be null if ReconItemStatus is MISSING_IN_INPUT
    outputAmount: Float # would be null if ReconItemStatus is MISSING_IN_OUTPUT
    inputCurrency: CurrencyCode
    outputCurrency: CurrencyCode
    status: ReconItemStatus!
}

enum ReconWorkflowStatus {
    CREATED,
    IN_PROGRESS,
    FAILED,
    IN_REVIEW,
    COMPLETED,
}

enum ReconciliationStatus {
    PASS,
    FAIL
}

enum ReconItemGroupStatus {
    UNRESOLVED, # = not reconciled, like MISSING_IN_INPUT, MISSING_IN_OUTPUT or DATA_MISMATCHED
    IGNORED, # = ignored
    RECONCILED # = reconciled, MATCHED
}

enum ReconItemStatus {
    MISSING_IN_INPUT,
    MISSING_IN_OUTPUT,
    DATA_MISMATCHED,
    MATCHED
}
