extend type Query {
    demoAccountDataStatus(companyId: ID!): DemoAccountDataStatus @authorize(company: ["create.company.contract"])
    demoAccountWorkflow(companyId: ID!): [DemoAccountWorkflow] @authorize(company: ["create.company.contract"])
}

extend type Mutation {
    updateDemoAccountStatus(dataSeedStatus: DataSeedStatus!): DemoAccountDataStatus @authorize(company: ["create.company.contract"])
    createDemoAccountWorkflow(input: DemoAccountWorkflowInput!): DemoAccountWorkflow @authorize(company: ["create.company.contract"])
}

type DemoAccountDataStatus {
    id: ID!
    companyId: ID!
    dataSeedStatus: DataSeedStatus
}

type DemoAccountWorkflow {
    id: ID!
    companyId: ID!
    status: DataSeedStatus
}

input DemoAccountWorkflowInput {
    companyId: ID!
    contractTypes: [ContractType!]
    hrMemberCountries: [CountryCode!]
    eorCountries: [CountryCode!]
    hrMemberConfigurations: [CountryConfigurationInput!]
    eorMemberConfigurations: [CountryConfigurationInput!]
}

input CountryConfigurationInput {
    country: CountryCode!
    count: Int
}

