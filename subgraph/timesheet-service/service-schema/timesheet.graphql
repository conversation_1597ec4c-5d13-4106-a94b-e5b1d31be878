extend type Query {
    # Query to get all timesheet related configurations. Defining this in timesheet domain
    # will helps us later when we are extracting "timesheet" into a separate microservice.
    timesheetConfigs(filters: TimesheetConfigFilters): TimesheetConfigs     @authorize(company: ["view.company.timesheet"], operations: ["view.operations.timesheet"], member: ["view.member.timesheet"])
    timesheetWithPagination(filters: TimesheetFilters!, pageRequest: PageRequest!): TimesheetResult     @authorize(operations: ["view.operations.contract.timesheet"])
    timesheetReport(filters: TimesheetFilters!): DocumentReadable           @authorize(operations: ["view.operations.contract.timesheet"])
    timesheetContractConfigs(contractId: ID!): TimesheetContractConfig      @authorize(operations: ["view.operations.timesheet"]) @deprecated(reason: "Not implemented, no usage")
    timesheetAccessibility(resourceName: String, contractId: ID!): FieldAccessibilityRule   @authorize(company: ["view.company.timesheet"], operations: ["view.operations.timesheet"])
    timesheetPoliciesDocument(filters: TimesheetPolicyFilters!): DocumentReadable! @authorize(operations: ["view.operations.timesheet.policies"], company: ["view.company.timesheet.policies"])

    # Only for OPs platform usage.
    timesheetPolicies(filters: TimesheetPolicyFilters!): [TimesheetPolicy] @authorize(operations: ["view.operations.timesheet.policies"])
}

extend type Mutation {
    timesheetCreateOrUpdate(timesheetId: ID, input : TimesheetCreateUpdateInput): Timesheet     @authorize(member: ["create.member.timesheet"])
    timesheetEntryCreateOrUpdate(timesheetId: ID!, input: TimesheetEntryInput!): TimesheetEntry @deprecated(reason: "Use timesheetEntriesCreateOrUpdate instead")
    timesheetEntriesCreateOrUpdate(timesheetId: ID!, input: [TimesheetEntryInput!]!): [TimesheetEntry]  @authorize(company: ["create.company.timesheet.entry"], member: ["create.member.timesheet.entry"])
    timesheetEntriesSubmit(timesheetEntryIds: [ID!]!): [TimesheetEntry]     @authorize(company: ["change.company.timesheet.status"], member: ["change.member.timesheet.status"])
    timesheetEntriesApprove(timesheetEntryIds: [ID!]!): [TimesheetEntry]    @authorize(company: ["change.company.timesheet.status"])
    timesheetEntriesReject(timesheetEntryIds: [ID!]!, comment: String): [TimesheetEntry]                @authorize(company: ["change.company.timesheet.status"])
    timesheetEntriesRevoke(timesheetEntryIds: [ID!]!): [TimesheetEntry]     @authorize(member: ["change.member.timesheet.status"]) # for member, moves entries from SUBMITTED to PENDING
    timesheetChangeStatus(timesheetId: ID!, status: TimesheetStatus!, comment: String): Timesheet       @authorize(company: ["change.company.timesheet.status"], member: ["change.member.timesheet.status"])

    # allow members/admins to delete added time sheet entries, this will bring timesheet entry to initial state where it was before editing
    timesheetEntriesDelete(timesheetEntryIds: [ID!]!) : [TimesheetEntry]    @authorize(company: ["delete.company.timesheet.entry"], member: ["delete.member.timesheet.entry"])

    # Create bulk timesheets (based on the uploaded file data)
    timesheetCreateBulk(file: Upload!, startDate: Date!, endDate: Date!): TaskResponse @authorize(operations: ["create.operations.timesheet"]) @deprecated(reason: "Use timesheetUploadBulk instead")

    # Upload bulk timesheets data given in uploaded template file.
    timesheetUploadBulk(file: Upload!, input: TimesheetUploadBulkInput!): TimesheetUploadResult @authorize(company: ["create.company.timesheet"], operations: ["create.operations.timesheet"])



    # Only for OPs (platform usage)
    timesheetPolicyDelete(policyId: ID!): TimesheetPolicy       @authorize(operations: ["delete.operations.timesheet.policies"])

    # Only for OPs (product support team usage)
    timesheetConfigUpdate(key: String!, value: String): TaskResponse                    @authorize(operations: ["create.operations.timesheet"])
    timesheetDelete(timesheetId: ID!): TaskResponse                                     @authorize(operations: ["delete.operations.timesheet"])
    timesheetEntryUpdateStatusAsPending(timesheetEntryId: ID!, confirmNoPayrollImpact: Boolean!): TaskResponse      @authorize(operations: ["update.operations.timesheet.entry"])
    timesheetTimeOffSync(input: TimeOffSyncInput!): TaskResponse                        @authorize(operations: ["update.operations.timesheet.entry"])

}

type Timesheet @key(fields: "id") {
    id: ID
    contract: Contract
    startDate: Date
    endDate: Date
    status: TimesheetStatus
    comment: String
    submittedOn: DateTime
    approvedOn: DateTime
    updatedOn: DateTime
    createdOn: DateTime
    totalDays: Int
    totalHours: Float   @deprecated(reason: "Use TimesheetTotalTimeSummary.totalTimeInMinutes instead")

    totalClockedInDays: Int @deprecated(reason: "Now calculated on FE. The column in DB shouldn't be used anywhere. Details: 865cqtgah")    # Total number of days member clocked-in (save) time values. (Eg: 13 days)
    totalSubmittedDays: Int     # Total number of days member submitted the time values. (Eg: 10 days)
    totalApprovedDays: Int      # Total number of days approved by the admin/manager. (Eg: 8 days)
    totalRejectedDays: Int      # Total number of days rejected by the admin/manager. (Eg: 2 days)
    cutOffDate: Date   @authorize(company: ["view.company.timesheet.cut-off"], operations: ["view.operations.timesheet"], member: ["view.member.timesheet.cut-off"])         # Cut-off date for this timesheet. (Eg: 2023-04-17)

    # The way how the time is inputted by the member (by giving start/end time or by giving total hours)
    timeValueInputMethod: TimeValueInputMethod

    totalTimeSummary: TimesheetTotalTimeSummary     @authorize(company: ["view.company.timesheet.total-time-summary"], operations: ["view.operations.timesheet"], member: ["view.member.timesheet.total-time-summary"])
    totalTimeSummaryV2: TimesheetTotalTimeSummaryV2 @authorize(company: ["view.company.timesheet.total-time-summary"], operations: ["view.operations.timesheet"], member: ["view.member.timesheet.total-time-summary"])
    timesheetEntries: [TimesheetEntry]              @authorize(company: ["view.company.timesheet.timesheet-entry"], operations: ["view.operations.timesheet"], member: ["view.member.timesheet.timesheet-entry"])
}

type TimesheetTotalTimeSummary {
    totalTimeInMinutes: Int        # Total minutes of the timesheet. (Eg: 500 minutes => 8 hours, 20 minutes)
    totalLateTimeInMinutes: Int    # Total late time in minutes (applicable for fixed term members).

    # List of items with their total time.
    # Eg:
    #   - REGULAR_DAY, REGULAR_HOURS, total time = 270 minutes
    #   - REGULAR_DAY, OVERTIME_HOURS, total time = 160 minutes
    #   - SPECIAL_HOLIDAY, NIGHT_SHIFT_HOURS, total time = 60 minutes
    #   ... etc.
    totalTimeSummaryItems: [TimesheetTotalTimeSummaryItem]
}

type TimesheetTotalTimeSummaryV2 {
    totalTimeInMinutes: Int        # Total minutes of the timesheet. (Eg: 500 minutes => 8 hours, 20 minutes)
    totalLateTimeInMinutes: Int    # Total late time in minutes (applicable for fixed term members).

    # List of items with their total time.
    # Eg:
    #   - REGULAR_DAY, REGULAR_HOURS, total time = 270 minutes
    #   - REGULAR_DAY, OVERTIME_HOURS, total time = 160 minutes
    #   - SPECIAL_HOLIDAY, NIGHT_SHIFT_HOURS, total time = 60 minutes
    #   ... etc.
    totalTimeSummaryItemsV2: [TimesheetTotalTimeSummaryItemV2]
}

type TimesheetTotalTimeSummaryItem {
    dayType: DayType            # Type of the working day. (Eg: REGULAR_DAY, REST_DAY, REGULAR_HOLIDAY, etc)
    hoursType: HoursType        # Type of the working hours. (Eg: REGULAR_HOURS, OVERTIME_HOURS, etc)
    totalTimeInMinutes: Int     # Total time in minutes. (Eg: 3400 minutes)
}

type TimesheetTotalTimeSummaryItemV2 {
    dayTypeName: String          # Name of the type of the working day. (Eg: Regular day, Rest day, Regular holiday, etc)
    hourTypeName: String         # Name of the type of the working hours. (Eg: Regular hours, Overtime hours, etc)
    totalTimeInMinutes: Int      # Total time in minutes. (Eg: 3400 minutes)
}

type TimesheetEntry {
    id: ID
    date: Date
    hours: Float
    dayType: DayType                    # Type of the working day. (Eg: REGULAR_DAY, REST_DAY, REGULAR_HOLIDAY, etc)
    regularTimeInMinutes: Int           # Regular work time in minutes excluding break hours. (Eg: 60 minutes)
    overtimeTotalInMinutes: Int         # Overtime total in minutes after regular working time.
    overtimeAtNightInMinutes: Int       # Out of "overtime", how many minutes worked on night for an extra differential.
    nightShiftTimeInMinutes: Int        # Night shift time (minutes worked in night for an extra night shift differential).
    lateTimeInMinutes: Int              # Late time in minutes (applicable for fixed term members).

    status: TimesheetEntryStatus        # Status of the timesheet entry. (Eg: OPEN, SUBMITTED, APPROVED, REJECTED)
    workTimes: [TimeRange]              # List of work time ranges for the given date.
    breakTimes: [TimeRange]             # List of break time ranges for the given date.
    updatedOn: DateTime
    submittedOn: DateTime
    approvedOn: DateTime
    rejectedOn: DateTime
    addedByInfo: AuditUser
    approvedByInfo: AuditUser
    rejectedByInfo: AuditUser

    # Is this a paid leave day. If true, then no need to enter any time values.
    # Day type will set as REGULAR_DAY and regularTimeInMinutes will set with the regular work shift hours (default 8 hours).
    isPaidLeave: Boolean
    comment: String                     # for now rejection reason only
    entryHours: [TimesheetEntryHour]    @authorize(company: ["view.company.timesheet.timesheet-entry"], operations: ["view.operations.timesheet"], member: ["view.member.timesheet.timesheet-entry"])
    isEdited: Boolean
    timeOffSyncInfo: TimesheetEntryTimeOffSyncInfo
}

type TimesheetEntryHour {
    dayType: TimesheetDayType!
    hourType: TimesheetHourType!
    workTimeInMinutes: Int!
}

type TimesheetConfigs {
    countryConfigs: TimesheetCountryConfigs
}

type TimesheetCountryConfigs {
    countryCode: CountryCode

    countryStateCode: StateCode

    # Country specific list of day types.
    # Eg:
    #   - For country = PHL, then dayTypes = [REGULAR_DAY, REST_DAY, REGULAR_HOLIDAY, SPECIAL_HOLIDAY,... etc.]
    #   - For country = LKA, then  dayTypes = [REGULAR_DAY]
    dayTypes: [DayType]

    # Country specific list of hours types.
    # Eg:
    #   - For country = PHL, then hoursTypes = [REGULAR_HOURS, OVERTIME_HOURS, OVERTIME_AT_NIGHT_HOURS, NIGHT_SHIFT_HOURS]
    #   - For country = LKA, then  dayTypes = [REGULAR_HOURS, OVERTIME_HOURS]
    hoursTypes: [HoursType]

}

type TimesheetResult {
    data: [Timesheet]
    pageResult: PageResult
}

type TimesheetUploadResult {
    success: Boolean
    message: String
    errors: [TimesheetUploadError]
}

type TimesheetUploadError {
    contractId: ID
    message: String
}

type ContractTimesheetSummary {
    contract: Contract
    totalDaysToApprove: Int
    cutOffDate: Date
    totalSubmittedHoursToApprove: Float
}

type TimesheetPolicy {
    id: ID
    status: TimesheetPolicyStatus
    effectiveDate: Date                 # Policy is active from this date onwards
    endDate: Date                       # Policy is expired after this date, endDate = null means policy is not going to expire
    rules: [TimesheetPolicyRule]
}

type TimesheetPolicyRule {
    id: ID,
    triggerConditions: [TimesheetPolicyRuleTriggerCondition]        # This policy rule will be triggered if all the "trigger conditions" in the list are met (AND operations)
    actions: [TimesheetPolicyRuleAction]                            # All the actions in the list to be applied if this policy rule is triggered
}

# Multiple "triggers" can be defined for a single trigger condition and all the triggers are connected with OR operation.
# If any of the triggers are met (true), then this trigger condition is met (triggered).
type TimesheetPolicyRuleTriggerCondition {
    triggers: [TimesheetPolicyRuleTrigger]
}

interface TimesheetPolicyRuleTrigger {
    type: TimesheetPolicyRuleTriggerType
}

type DayTypeHourTypeTrigger implements TimesheetPolicyRuleTrigger {
    type: TimesheetPolicyRuleTriggerType
    dayType: TimesheetDayType
    hourType: TimesheetHourType
}

type TimesheetDayType {
    id: ID
    name: String
}

type TimesheetHourType {
    id: ID
    name: String
}

interface TimesheetPolicyRuleAction {
    type: TimesheetPolicyRuleActionType
}

type PayMultiplyFactorAction implements TimesheetPolicyRuleAction {
    type: TimesheetPolicyRuleActionType
    payMultiplyFactor: Float
}

type TimesheetContractConfig {
    dayTypes: [TimesheetDayType]
    hourTypes : [TimesheetHourType]
}

type TimesheetEntryTimeOffSyncInfo {
    timeOffId: ID
    syncOperation: TimeOffSyncOperation
    session: TimeOffSyncSession
    updatedOn: DateTime
    isEntryUpdated: Boolean
    comment: String
    isPaidHalfDayLeave: Boolean
}

# Enums
enum TimesheetStatus {
    OPEN
    PARTIALLY_SUBMITTED @deprecated
    SUBMITTED
    APPROVED
    REJECTED
}

# Only used for EOR contracts
enum DayType {
    NOT_APPLICABLE                  # The dates in timesheet before contract.startDate and after contract.endDate
    REGULAR_DAY                     # Regular working days according to contract work shift (default is MONDAY to FRIDAY)
    REST_DAY                        # Non-working days according to contract work shift (default is SAT to SUN)
    REGULAR_HOLIDAY                 # Regular holidays
    SPECIAL_HOLIDAY                 # Special holidays (applicable for PHL)
    REST_DAY_REGULAR_HOLIDAY        # Rest days which fall on regular holidays
    REST_DAY_SPECIAL_HOLIDAY        # Rest days which fall on special holidays
}

# Only used for EOR contracts
enum HoursType {
    REGULAR_HOURS
    OVERTIME_HOURS
    OVERTIME_AT_NIGHT_HOURS
    NIGHT_SHIFT_HOURS
}

enum TimeValueInputMethod {
    START_TIME_END_TIME
    TOTAL_HOURS
}

enum TimesheetEntryStatus {
    PARTIALLY_CREATED   # Timesheet entry created (saved) with NO end time. (Eg: 8:00 - N/A). Partially saved in the system.
    PENDING             # Timesheet entry created (saved) with proper start & end times. (Eg: 8:00 - 17:00). Pending to submit.
    SUBMITTED           # Timesheet entry submitted by the user.
    APPROVED            # Timesheet entry approved by the admin/manager.
    REJECTED            # Timesheet entry rejected by the admin/manager.
    PAID                # For future use-cases
}

enum TimesheetPolicyStatus {
    DATA_IN_REVIEW,
    INACTIVE,
    ACTIVE,
    EXPIRED,
    DELETED
}

enum TimesheetPolicyRuleTriggerType {
    DAY_TYPE_HOUR_TYPE_TRIGGER
}

enum TimesheetPolicyRuleActionType {
    PAY_MULTIPLY_FACTOR_ACTION
}

enum TimeOffSyncOperation {
    APPROVED
    CANCELLED
    NO_CHANGE
}

enum TimeOffSyncSession {
    MORNING
    AFTERNOON
    FULL_DAY
}

# Inputs
input TimesheetEntryInput {
    date: Date
    hours: Float
    dayType: DayType                    # Type of the working day. (Eg: REGULAR_DAY, REST_DAY, REGULAR_HOLIDAY, etc)
    regularTimeInMinutes: Int           # Regular work time in minutes excluding break hours. (Eg: 60 minutes)
    overtimeTotalInMinutes: Int         # Overtime total in minutes after regular working time.
    overtimeAtNightInMinutes: Int       # Out of "overtime", how many minutes worked on night for an extra differential.
    nightShiftTimeInMinutes: Int        # Night shift time (minutes worked in night for an extra night shift differential).
    lateTimeInMinutes: Int              # Late time in minutes (applicable for fixed term members).
    workTimes: [TimeRangeInput]         # List of work time ranges for the given date.
    breakTimes: [TimeRangeInput]        # List of break time ranges for the given date.
    isPaidLeave: Boolean

    # use below fields only for HR Member timesheets
    entryHourInputs: [TimesheetEntryHourInput!]
    isEdited: Boolean = false
}

input TimesheetEntryHourInput {
    dayTypeId: ID!
    hourTypeId: ID!
    workTimeInMinutes: Int!
}

input TimesheetFilters {
    timesheetIds: [ID]
    contractIds: [ID]
    companyIds: [ID]
    country: CountryCode
    contractType: ContractType
    contractStatus: ContractStatus
    paymentFrequency: PayFrequency
    hoursType: HoursType
    timesheetStatus: TimesheetStatus
    startDateRange: DateRange
    updateDateRange: DateRange
    approvedDateRange: DateRange
    memberName: String
    includeDeleted: Boolean
}

input TimesheetCreateUpdateInput {
    contractId: ID
    startDate: Date
    endDate: Date
}

input TimesheetConfigFilters {
    countryCode: CountryCode
    countryStateCode: StateCode
}

input TimesheetUploadBulkInput {
    startDate: Date!
    endDate: Date!
    companyId: ID!
}

input TimesheetSummaryFilter {
    contractTypes: [ContractType!]
    statuses: [ContractStatus!]
}

input TimesheetPolicyFilters {
    companyId: ID # If company id is null return global policies (parent)
    entityId: ID # if entity id is null return all global company policies (child)
}

input CompanyTimesheetPolicyFilter {
    contractId: ID # If contract id is present ignore all other filters
    entityIds : [ID!]
}

input TimeOffSyncInput {
    timesheetIds: [ID!]!
}
