type Contract @key(fields: "id") @extends {
    id: ID @external
    timesheets(id: ID): [Timesheet]     @authorize(company: ["view.company.contract.timesheet"], operations: ["view.operations.contract.timesheet"], member: ["view.member.contract.timesheet"])
}

type Company @key(fields: "id") @extends {
    id: ID @external
    timesheets(id: ID, timesheetFilters: TimesheetFilters): [Timesheet]              @authorize(company: ["view.company.timesheet"], operations: ["view.operations.timesheet"])
    timesheetSummaries(filters: TimesheetSummaryFilter): [ContractTimesheetSummary!] @authorize(company: ["view.company.timesheet"], operations: ["view.operations.timesheet"])

    # If filter is null or all fields are null/empty return all policies for company
    timesheetPolicies(filter : CompanyTimesheetPolicyFilter) : [TimesheetPolicy]     @authorize(company: ["view.company.timesheet.policies"], operations: ["view.operations.timesheet.policies"], member: ["view.member.timesheet.policies"])
}
