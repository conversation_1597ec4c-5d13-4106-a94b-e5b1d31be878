extend type Mutation {
    # Trigger automatic timesheet creation for EoR members
    triggerEmployeeTimesheetsGeneration: TaskResponse

    # Trigger automatic timesheet submission for EoR members
    # if current date = null -> current date will treated as today
    triggerEmployeeTimesheetsAutoSubmission(currentDate: Date): TaskResponse

    # Trigger automatic timesheet submission for EoR members in batch-wise
    # if current date = null -> current date will treated as today
    # if batchSize = null -> the entire timesheets list will try to process at one go
    triggerEmployeeTimesheetsAutoSubmissionInBatches(currentDate: Date, batchSize: Int): TaskResponse

    # Send timesheets auto-submission email to EoR members
    triggerTimesheetsAutoSubmissionEmailProcess: TaskResponse

    # Send daily reminder to fill previous day's timecard
    triggerMemberTimeCardDailyReminders: TaskResponse

    # Send reminder email to approve submitted hours to all admins
    triggerTimesheetApproveSubmittedHoursReminderEmailProcess: TaskResponse

    # Send notification to member for approved hours considered for next payroll cycle
    triggerMemberTimesheetApprovedHoursReminders: TaskResponse

    # Trigger automatic timesheet entry approval for monthly members
    # if current date = null -> current date will treated as today
    triggerEmployeeTimesheetEntryAutoApproval(currentDate: Date): TaskResponse

    # Trigger automatic timesheet entry approval for monthly members in batch-wise
    # if current date = null -> current date will treated as today
    # if batchSize = null -> the entire timesheets list will try to process at one go
    triggerEmployeeTimesheetEntryAutoApprovalInBatches(currentDate: Date, batchSize: Int): TaskResponse

    # Create any missing timesheets for a given contract. Only OPs can call this.
    timesheetGenerateIfMissing(contractId: ID!): TaskResponse

    # Send reminder email to approve submitted hours to all admins (with a specific date)
    triggerTimesheetApproveSubmittedHoursReminderEmailsToAdmins(currentDate: Date!): TaskResponse

    # Manually execute the SendTimesheetUnapprovedPerClientReminderToCSM scheduled job
    triggerTimesheetUnapprovedPerClientReminderToCSM(
        """null indicates today (for a normal production run); another date indicates a backdated/future run (e.g. for testing)"""
        currentDate: Date
    ): TaskResponse

    # Trigger timesheet generation for EOR members in a batch wise
    triggerTimesheetGenerationBatchWise(currentDate: Date!): TaskResponse

    # Trigger timesheet policy activation
    triggerTimesheetPolicyActivation(currentDate: Date): TaskResponse

    # Trigger time-off sync failed events
    triggerTimeOffSyncFailedEvents(date: Date): TaskResponse    @authorize(operations: ["update.operations.timesheet.entry"])
}
