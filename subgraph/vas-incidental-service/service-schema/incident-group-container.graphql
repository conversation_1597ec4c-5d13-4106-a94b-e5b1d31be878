type IncidentGroupContainer {
    id: ID!
    incidentGroups: [IncidentGroup]
    status: IncidentGroupContainerStatus!
    companyPayable: CompanyPayable
    company: Company!
    invoiceContext: IncidentGroupContainerInvoiceContext
}

type IncidentGroupContainerInvoiceContext {
    createdDate: DateTime
    dueDate: DateTime
    isAuthorised: <PERSON>olean
}

enum IncidentGroupContainerStatus {
    CREATED
    METERED
    INVOICED
    DELETED
    FAILED
    COMPLETED
}

