extend type Mutation {
    incidentGroupContainerCreate(input: IncidentGroupContainerInput!): IncidentGroupContainer! @authorize(operations: ["create.operations.vas.incident"])
    incidentGroupContainerSendEmail(input: IncidentGroupContainerSendEmailInput!): TaskResponse! @authorize(operations: ["email.operations.vas.incident"])
    incidentGroupContainerGenerateInvoice(groupContainerId: ID!): IncidentGroupContainer! @authorize(operations: ["create.operations.vas.incident"])
    incidentGroupContainerDelete(input: IncidentGroupContainerDeleteInput!): IncidentGroupContainer! @authorize(operations: ["delete.operations.vas.incident"])
}

input IncidentGroupContainerInput {
    id: ID #if not passed, it is a fresh container create request, else it is an request for an existing container.
    companyId: ID!
    invoiceContext: IncidentGroupContainerInvoiceContextInput
    incidentGroups: [IncidentGroupInput!]!
}

input IncidentGroupContainerInvoiceContextInput {
    createdDate: DateTime
    dueDate: DateTime
    isAuthorised: <PERSON>olean
}

input IncidentGroupInput {
    description: String
    incidentType: IncidentType!
    unitAmount: AmountInput!
    chargePolicy: IncidentChargePolicyInput
    quantity: Int!
    feeDiscountInput: IncidentFeeDiscountInput
    metadata: [IncidentGroupMetadata] # This will include data like contractId etc.
}

input IncidentChargePolicyInput {
    mode: IncidentChargeMode!
    rate: IncidentChargePolicyRateInput!
}

input IncidentChargePolicyRateInput {
    amount: AmountInput
    quantity: IncidentQuantityInput
}

input IncidentQuantityInput {
    value: Float!
    incidentQuantityChargeRateUnit: IncidentQuantityChargeRateUnit!
}

input IncidentFeeDiscountInput {
    type: IncidentFeeDiscountType!
    fixed: FixedIncidentFeeDiscountInput
    percentage: PercentageIncidentFeeDiscountInput
}

input PercentageIncidentFeeDiscountInput {
    description: String
    value: Float!
}

input FixedIncidentFeeDiscountInput {
    description: String
    value: AmountInput!
}

# The possible values for the metadata key are:
#  "contractId" - "1234"
input IncidentGroupMetadata {
    key: String,
    value: String
}

input IncidentGroupContainerSendEmailInput {
    containerId: ID!
    emailType: IncidentGroupContainerEmailType!
}

input IncidentGroupContainerDeleteInput {
    id: ID!
}

enum IncidentGroupContainerEmailType {
    INCIDENTAL_INVOICE_FOR_APPROVAL_NOTIFICATION
}
