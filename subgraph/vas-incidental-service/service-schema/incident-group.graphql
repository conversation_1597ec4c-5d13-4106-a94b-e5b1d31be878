type IncidentGroup {
    id: ID!
    incidents: [Incident]
    incidentFeeDiscount: IncidentFeeDiscount
}

interface IncidentFeeDiscount {
    description: String
    type: IncidentFeeDiscountType!
}

type FixedIncidentFeeDiscount implements IncidentFeeDiscount {
    description: String
    amount: Amount!
    type: IncidentFeeDiscountType!
}

type PercentageIncidentFeeDiscount implements IncidentFeeDiscount {
    description: String
    percentage: Float!
    type: IncidentFeeDiscountType!
}

enum IncidentFeeDiscountType {
    PERCENTAGE
    FIXED
}
