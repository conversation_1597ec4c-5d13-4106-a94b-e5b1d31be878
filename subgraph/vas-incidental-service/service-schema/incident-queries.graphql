extend type Query {
    incidentGroupContainers(filters: IncidentGroupContainerFilters, pageRequest: PageRequest): [IncidentGroupContainer] @authorize(operations: ["view.operations.vas.incident"]) @deprecated(reason: "This query is deprecated and will be removed in a future version")
    incidentGroupContainersWithPagination(filters: IncidentGroupContainerFilters, pageRequest: PageRequest): IncidentGroupContainersResponse! @authorize(operations: ["view.operations.vas.incident"])
    incidentServiceTypeToIncidentTypeMapping(filters: IncidentServiceTypeToIncidentTypeMappingFilters): [IncidentServiceTypeToIncidentTypeMapping] @authorize(operations: ["view.operations.vas.incident"])
    incidentGroupContainerGet(id: ID!): IncidentGroupContainer! @authorize(operations: ["view.operations.vas.incident"])
}

input IncidentGroupContainerFilters {
    status: IncidentGroupContainerStatus
}

input IncidentServiceTypeToIncidentTypeMappingFilters {
    serviceType: IncidentServiceType
}

type IncidentServiceTypeToIncidentTypeMapping {
    serviceType: IncidentServiceType
    incidentTypes: [IncidentType]
}

type IncidentGroupContainersResponse {
    incidentGroupContainers: [IncidentGroupContainer!]!
    pageResult: PageResult!
}
