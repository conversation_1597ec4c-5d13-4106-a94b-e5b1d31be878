interface OthersIncident implements Incident {
    id: ID!
    description: String
    type: IncidentType!
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}

type OthersIncidentServiceFee implements OthersIncident & Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}

type OthersIncidentService implements OthersIncident & Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}

type OthersIncidentExpatSetup implements OthersIncident & Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}
