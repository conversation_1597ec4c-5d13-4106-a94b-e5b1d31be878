interface Incident {
    id: ID!
    type: IncidentType!
    description: String
    incidentTime: DateTime #the time when the incident happened. Could be different from the time when the incident was reported
    amount: Amount @deprecated (reason: "Use chargePolicy.rate instead")
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode @deprecated(reason: "this is not utilized, but due to backwards compatibility, we can't remove it yet.")
}

type IncidentChargePolicy {
    mode: IncidentChargeMode!
    rate: IncidentChargePolicyRate!
}

union IncidentChargePolicyRate = IncidentAmountChargePolicyRate | IncidentQuantityChargePolicyRate

type IncidentAmountChargePolicyRate {
    amount: Amount!
}

type IncidentQuantityChargePolicyRate {
    value: Float!
    incidentQuantityChargeRateUnit: IncidentQuantityChargeRateUnit!
}

enum IncidentType {
    LAPTOP
    MONITOR
    ACCESSORIES
    DELIVERY           @deprecated # Changed to PICKUP_DELIVERY
    PICKUP_DELIVERY
    STORAGE
    CONTRACT_CUSTOMISATION
    LEGAL_CONSULTATION
    OTHERS_SERVICE_FEE
    OTHERS_SERVICE
    EXPAT_SETUP
}

enum IncidentChargeMode {
    AMOUNT,
    QUANTITY
}

enum IncidentQuantityChargeRateUnit {
    HOURS
}
