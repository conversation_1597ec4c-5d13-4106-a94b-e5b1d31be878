interface LogisticsIncident implements Incident {
    id: ID!
    description: String
    type: IncidentType!
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}

type LogisticsIncidentPickupAndDelivery implements LogisticsIncident & Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}
