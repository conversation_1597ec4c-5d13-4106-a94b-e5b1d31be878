interface AssetIncident implements Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}

type LaptopIncident implements AssetIncident & Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    contract: Contract
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}

type MonitorIncident implements AssetIncident & Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    contract: Contract
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}

type AccessoriesIncident implements AssetIncident & Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    contract: Contract # required? They could be company level accessories like office play station etc.
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}
