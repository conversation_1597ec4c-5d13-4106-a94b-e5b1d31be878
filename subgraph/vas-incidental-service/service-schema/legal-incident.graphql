interface LegalIncident implements Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}

type LegalIncidentContractCustomisation implements LegalIncident & Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}

type LegalIncidentConsultation implements LegalIncident & Incident {
    id: ID!
    type: IncidentType!
    description: String
    amount: Amount @deprecated(reason: "Use chargePolicy.rate instead")
    incidentTime: DateTime
    chargePolicy: IncidentChargePolicy
    serviceType: IncidentServiceType
    country: CountryCode
}
