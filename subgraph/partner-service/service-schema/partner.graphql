extend type Query {
    partner(id: ID, userId: ID): Partner                # TODO : Filters {userId} to be implemented.
    partners (country: CountryCode, capability: PartnerCapability): [Partner]
}

extend type Mutation {
    partnerInvite(partnerId: ID!): Partner # Sends invite to each and every partnerUsers for initial login
    partnerAcceptInvite(partnerId: ID): Partner
    partnerUserDelete(partnerUserId: ID): TaskResponse
    partnerCreate(partner: CreatePartnerInput): Partner
    partnerAddCountryCapability(partnerId: ID, partnerCountry: PartnerCountryInput): Partner
    partnerUserAcceptInvite: PartnerUser
    partnerUserUpdateOperator(partnerUserId: ID, countryCode: CountryCode!, userType: PartnerUserType!) : PartnerUser
    partnerUserChangeRole(partnerUserId: ID, newRole: String) : PartnerUser
    partnerUserCreate(partnerUser: CreatePartnerUserInput, partnerId: ID) : PartnerUser
    partnerEORAssignContract(partnerId: ID!, contractId: ID!): ContractEORPartner
    partnerMappingBackfillCSV(file: Upload!): TaskResponse
}

input CreatePartnerInput {
    name: String!
    partnerCountries: [PartnerCountryInput!]!
    partnerUsers: [CreatePartnerUserInput!]
}

input PartnerCountryInput {
    capability: PartnerCapability!
    countryCode: CountryCode!
    currency: CurrencyCode # only for PartnerCapability = PAYROLL
    emergencyPointOfContact: EmergencyPointOfContactInput # only for PartnerCapability = BENEFIT
}

input CreatePartnerUserInput {
    email: String!
    firstName: String
    lastName: String
    phoneNo: String
    role: PartnerRole!
    type: PartnerUserType!
    country: CountryCode!
}

input EmergencyPointOfContactInput {
    partnerUserId: ID # if passed, below attributes must be null, because they will be taken from the partner user
    name: String
    email: String
}

enum PartnerRole {
    PRIMARY_ADMIN
    ADMIN
}

enum PartnerUserType {
    PAYROLL_PRIMARY
    PAYROLL_SECONDARY
    PAYMENTS
}

enum PartnerUserStatus {
    CREATED
    INVITED
    ACTIVE
    # DELETED To be done later
}

type EORPartnerCountry implements PartnerCountry @key(fields: "id") {
    id: ID
    capability: PartnerCapability #EOR
    partner: Partner
    country: CountryCode
    memberContracts: [Contract]
    entity: LegalEntity
    operator: [PartnerUser]
    isMultiplierEntity: Boolean
}

type ContractEORPartner{
    contract: Contract
    eorPartnerCountry: EORPartnerCountry
}

type PartnerUser @key(fields: "id") {
    id: ID
    firstName: String
    lastName: String
    emails: [EmailAddress]
    phoneNos: [PhoneNumber]
    role: String
    type: PartnerUserType
    userId: ID
    countryCode: CountryCode
    partner: Partner
}
