extend type Mutation  {
    resendMeteringReadyEvent(input: MeteringReadyEventInput!): TaskResponse @authorize(operations: ["update.operations.order"])
}

input MeteringReadyEventInput {
    id: String!,
    date: DateTime,
    name: String,
    lastUpdated: DateTime,
    value: Float,
    rawEventIds: [String!],
    dimensionTags: [DimensionTag!]
}

input DimensionTag {
  key: String!,
  value: String!
}
