extend type Query {
    payrollParsedDataPaginated(filter: PayrollParsedDataPaginatedRequest!): PayrollParsedDataPaginatedResponse! @authorize(partner: ["upload.partner.payrollreport"])
}

extend type Mutation {
    uploadPayrollOutputFile(payrollCycleId: ID!, fileName: String!): PayrollProcessorFile! @authorize(partner: ["upload.partner.payrollreport"])
    parsePayrollOutputFile(request: ParsePayrollOutputRequest!): PayrollParsedDataPaginatedResponse! @authorize(partner: ["upload.partner.payrollreport"])
    updateParsedHeaderSchema(request: UpdatePayrollOutputHeaderRequest!): PayrollParsedDataPaginatedResponse! @authorize(partner: ["upload.partner.payrollreport"])
}

input PayrollParsedDataPaginatedRequest {
    payrollCycleId: ID!
    filter: PayrollParsedDataFilter!
}

input ParsePayrollOutputRequest {
    payrollCycleId: ID!
    filter: PayrollParsedDataFilter!
}

input HeaderUpdateData {
    id: UUID!,
    rawHeader: String!,
    schemaHeader: String!,
    category: String!
}

input UpdatePayrollOutputHeaderRequest {
    payrollCycleId: ID!,
    updates: [HeaderUpdateData!]!
    filter: PayrollParsedDataFilter
}

input PayrollParsedDataFilter {
    pageRequest: PageRequest!
    validationCode: ParsingValidationCode
}

type PayrollParsedDataPaginatedResponse {
    payrollCycleId: ID!
    workflowId: UUID!
    parsedData: [ParsedPayrollData!]!
    parsedSchemaHeaders: [ParsedPayrollHeader!]!
    validationResults: [PayrollOutputValidationResult!] #fileLevel
    pageResult: PageResult!
}

type PayrollOutputValidationResult {
    validationCode: ParsingValidationCode! # The result message category
    message: String! # The result message
    type: SeverityLevel! # level of the message
    rawHeader: [String!]
    schemaHeader: [String!]
    actualValue: Float
    expectedValue: Float
    contractId: ID
}

type ParsedPayrollHeader {
    id: UUID!,
    rawHeader: String!,
    schemaHeader: String!,
    needsReview: Boolean!,
    category: String!
}

type ParsedPayrollData {
    id: UUID!,
    contractId: ID!,
    components: [ParsedPayrollDataComponents!]!
    validationResults: [PayrollOutputValidationResult!]
}

type ParsedPayrollDataComponents {
    componentName: String!
    componentValue: String!
}

