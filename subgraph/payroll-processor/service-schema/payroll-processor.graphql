extend type Query {
    payrollCycleSchema(payrollCycleId: ID!): PayrollSchema! @authorize(partner: ["upload.partner.payrollreport"])
    payrollOutputData(payrollCycleId: ID!): PayrollOutputResponse! @authorize(partner: ["upload.partner.payrollreport"]) @deprecated
    processorFile(request: ProcessorFileRequest!): PayrollProcessorFile! @authorize(operations: ["use.operations.payroll"], partner: ["upload.partner.payrollreport"])
    payrollInputBulkFile(request: PayrollInputBulkFileRequest!): PartnerPayrollDocument! @authorize(operations: ["download.operations.input-file"])
}

extend type Mutation {
    processPayrollOutput(payrollCycleId: ID!): PayrollOutputResponse! @authorize(partner: ["upload.partner.payrollreport"]) @deprecated
    updatePayrollOutputHeader(request: UpdatePayrollOutputHeaderRequest!): [ParsedPayrollHeader!]! @authorize(partner: ["upload.partner.payrollreport"]) @deprecated
}

input ProcessorFileRequest {
    payrollCycleId: ID!,
    fileType: PayrollFileType!,
}

input PayrollInputBulkFileRequest {
    payrollCycleIds: [ID!]!,
    fileName: String!
}

type PayrollProcessorFile {
    id: UUID!,
    payrollCycleId: ID!,
    fileName: String!,
    downloadUrl: String @authorize(operations: ["use.operations.payroll"], partner: ["upload.partner.payrollreport"])
    uploadUrl: String @authorize(operations: ["use.operations.payroll"], partner: ["upload.partner.payrollreport"])
    fileType: PayrollFileType!
}

type PayrollOutputResponse {
    payrollCycleId: ID!,
    parsedData: [ParsedPayrollData!]!,
    headers: [ParsedPayrollHeader!]!
}

type PayrollSchema {
    categories: [PayrollSchemaCategory!]!
}

type PayrollSchemaCategory {
    name: String!
    description: String!
    components: [PayrollSchemaComponent!]!
}

type PayrollSchemaComponent {
    name: String!
    description: String!
    mandatory: Boolean!
}

type PartnerPayrollDocument {
    id: ID
    downloadUrl: String!
    fileName: String!
}
