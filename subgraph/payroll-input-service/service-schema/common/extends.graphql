type PayrollCycle @key(fields: "id") @extends {
    id: ID! @external
    contracts: [PayrollContract!] @authorize(operations: ["view.operations.payroll-data.input"], company: ["view.company.payroll-data.input"])
    employeeDataChangesV2: EmployeeDataChangesV2  @authorize(operations: ["view.operations.payroll-data.input"], company: ["view.company.payroll-data.input"])
    inputHeadCountSummaryV2: InputHeadcountSummaryV2  @authorize(operations: ["view.operations.payroll-data.input"], company: ["view.company.payroll-data.input"])
    payrollInputV2(filter: PayrollInputContractFilter, pageRequest: PageRequest!): PayrollInputContractResponse @authorize(operations: ["view.operations.payroll-data.input"], company: ["view.company.payroll-data.input"])
}

input PayrollInputContractFilter {
    payrollContractTypes: [PayrollContractType!] @deprecated(reason: "Use payrollActive instead")
    payrollActive: Boolean
    memberName: String
    country: CountryCode
    anyVariance: Boolean # only used for companyPayrollInput query
    anyCompensationChange: Boolean # only used for companyPayrollInput query
    anyBankDetailsChange: Boolean # only used for companyPayrollInput query
}
