extend type Query {
    payrollContract(filter: PayrollContractFilter!): PayrollContract @authorize(operations: ["view.operations.payroll-data.input"], company: ["view.company.payroll-data.input"])
}

input PayrollContractFilter {
    payrollCycleId: ID!
    contractId: ID!
}

type PayrollContract @key(fields: "id"){
    id: UUID!
    contractId: ID!
    employeeId: String
    name: String!
    position: String!
    status: ContractStatus!
    input: PayrollInputContract @authorize(operations: ["view.operations.payroll-data.input"], company: ["view.company.payroll-data.input"])
}
