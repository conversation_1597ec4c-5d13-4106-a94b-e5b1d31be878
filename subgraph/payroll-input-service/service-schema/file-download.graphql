extend type Mutation {
    payrollFileDownloadAsync(payrollCycleId: ID!, companyPayrollCycleId: ID, reportType: InputFileDownloadType!, forceRegenerate: Boolean!): PayrollInputFileDownload @authorize(operations: ["download.operations.input-file"], company: ["view.company.payroll-report"])
}

enum InputFileDownloadType {
    DRAFT_INPUT_FILE
    PAYROLL_INPUT_VARIANCE
}

type PayrollInputFileDownload {
    id: UUID
    type: InputFileDownloadType!
    key: String
    status: PayrollInputFileDownloadStatus!
    payrollFile: PayrollInputFile
}

type PayrollInputFile {
    id: UUID
    name: String
    downloadUrl: String
    extension: String
    contentType: String
    createdOn: DateTime
}

enum PayrollInputFileDownloadStatus {
    RECEIVED
    PROCESSING
    COMPLETED
    FAILED
    TIMED_OUT
}
