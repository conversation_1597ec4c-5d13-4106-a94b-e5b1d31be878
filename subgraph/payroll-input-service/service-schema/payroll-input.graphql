extend type Mutation {
    addPayItemToCycle(request: AddPayItemToCycleRequest!): TaskResponse @authorize(operations: ["update.operations.payroll-data.input"])
    archivePayItems(request: ArchivePayItemsRequest!): TaskResponse @authorize(operations: ["update.operations.payroll-data.input"])
    postponePayItems(request: PostponePayItemsRequest!): TaskResponse @authorize(operations: ["update.operations.payroll-data.input"])
}

input AddPayItemToCycleRequest{
    contractId: ID!
    domain: PayItemDomainType!
    ids: [UUID!]!
    cycleId: ID
}

input ArchivePayItemsRequest {
    domain: PayItemDomainType!
    ids: [UUID!]!
}

input PostponePayItemsRequest {
    domain: PayItemDomainType!
    ids: [UUID!]!
}
