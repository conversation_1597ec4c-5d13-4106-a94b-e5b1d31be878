interface PayrollInputPaySupplement implements DomainPayItem @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    startDateInclusive: Date!
    endDateInclusive: Date
    billingFrequency: BillingFrequency!
    paymentFrequency: PayFrequency!
    rateType: PayItemRateType!
    componentName: String!
    componentCategory: String!
    currency: CurrencyCode!
    billingRate: Float!
    calculatedAmount: Float!
    effectiveDate: Date!
    companyId: ID!
    entityId: ID!
    payScheduleName: String!
    isTaxable: Boolean!
    isFixed: Boolean!
    isProrated: Boolean!
    isMandatory: Boolean!
    isPartOfBasePay: Boolean!
    isInstallment: Boolean!
    numberOfInstallments: Int
    currentInstallment: Int
    isArrears: Boolean!
    label: String!
    overriddenEffectiveDate: Date
}

type PayrollInputStagingPaySupplement implements DomainPayItem & StagingPayItem & PayrollInputPaySupplement @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    startDateInclusive: Date!
    endDateInclusive: Date
    billingFrequency: BillingFrequency!
    paymentFrequency: PayFrequency!
    rateType: PayItemRateType!
    componentName: String!
    componentCategory: String!
    currency: CurrencyCode!
    billingRate: Float!
    calculatedAmount: Float!
    effectiveDate: Date!
    companyId: ID!
    entityId: ID!
    payScheduleName: String!
    isTaxable: Boolean!
    isFixed: Boolean!
    isProrated: Boolean!
    isMandatory: Boolean!
    isPartOfBasePay: Boolean!
    isInstallment: Boolean!
    numberOfInstallments: Int
    currentInstallment: Int
    isArrears: Boolean!
    label: String!
    isArchived: Boolean
    overriddenEffectiveDate: Date
}

type PayrollInputSnapshotPaySupplement implements DomainPayItem & SnapshotPayItem & PayItemPayrollCycleInfo & PayrollInputPaySupplement @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    payrollCycleId: ID!
    startDateInclusive: Date!
    endDateInclusive: Date
    cutOff: Date!
    billingFrequency: BillingFrequency!
    paymentFrequency: PayFrequency!
    rateType: PayItemRateType!
    componentName: String!
    componentCategory: String!
    currency: CurrencyCode!
    billingRate: Float!
    calculatedAmount: Float!
    effectiveDate: Date!
    companyId: ID!
    entityId: ID!
    payScheduleName: String!
    isTaxable: Boolean!
    isFixed: Boolean!
    isProrated: Boolean!
    isMandatory: Boolean!
    isPartOfBasePay: Boolean!
    isInstallment: Boolean!
    numberOfInstallments: Int
    currentInstallment: Int
    isArrears: Boolean!
    label: String!
    overriddenEffectiveDate: Date
}
