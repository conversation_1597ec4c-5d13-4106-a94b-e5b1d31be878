interface PayrollInputTimeOff implements DomainPayItem @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    timeOffId: ID!
    contractId: ID!
    effectiveDate: Date!
    entryDate: Date!
    startSession: TimeOffSession!
    endSession: TimeOffSession!
    noOfDays: Float!
    timeOffTypeKey: String!
    description: String
    approvedOn: DateTime
    typeId: ID!
    typeLabel: String!
    typeDescription: String
    paidType: TimeOffPaidType!
    componentCategory: String!
    componentName: String!
    overriddenEffectiveDate: Date
}

type PayrollInputStagingTimeOff implements DomainPayItem & StagingPayItem & PayrollInputTimeOff @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    timeOffId: ID!
    contractId: ID!
    effectiveDate: Date!
    entryDate: Date!
    startSession: TimeOffSession!
    endSession: TimeOffSession!
    noOfDays: Float!
    timeOffTypeKey: String!
    description: String
    approvedOn: DateTime
    typeId: ID!
    typeLabel: String!
    typeDescription: String
    paidType: TimeOffPaidType!
    componentCategory: String!
    componentName: String!
    isArchived: Boolean
    overriddenEffectiveDate: Date
}

type PayrollInputSnapshotTimeOff implements DomainPayItem & SnapshotPayItem & PayItemPayrollCycleInfo & PayrollInputTimeOff @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    timeOffId: ID!
    contractId: ID!
    payrollCycleId: ID!
    effectiveDate: Date!
    cutOff: Date!
    entryDate: Date!
    startSession: TimeOffSession!
    endSession: TimeOffSession!
    noOfDays: Float!
    timeOffTypeKey: String!
    description: String
    approvedOn: DateTime
    typeId: ID!
    typeLabel: String!
    typeDescription: String
    paidType: TimeOffPaidType!
    componentCategory: String!
    componentName: String!
    overriddenEffectiveDate: Date
}
