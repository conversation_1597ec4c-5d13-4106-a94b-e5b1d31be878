interface PayrollInputCompensationChange implements DomainPayItem @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    entityId: ID!
    componentName: String!
    componentCategory: String!
    currency: CurrencyCode!
    rateType: PayItemRateType!
    billingRate: Float!
    billingFrequency: BillingFrequency!
    effectiveDate: Date!
    startDate: Date!
    endDate: Date
    payScheduleName: String!
    numberOfInstallments: Int
    compensationSource: CompensationChangeSource!
    sourceId: String!
    isTaxable: Boolean!
    isFixed: Boolean!
    isProrated: Boolean!
    isMandatory: Boolean!
    isPartOfBasePay: Boolean!
    isInstallment: Boolean!
    label: String!
    overriddenEffectiveDate: Date
}

type PayrollInputStagingCompensationChange implements DomainPayItem & StagingPayItem & PayrollInputCompensationChange @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    entityId: ID!
    componentName: String!
    componentCategory: String!
    currency: CurrencyCode!
    rateType: PayItemRateType!
    billingRate: Float!
    billingFrequency: BillingFrequency!
    effectiveDate: Date!
    startDate: Date!
    endDate: Date
    payScheduleName: String!
    numberOfInstallments: Int
    compensationSource: CompensationChangeSource!
    sourceId: String!
    isTaxable: Boolean!
    isFixed: Boolean!
    isProrated: Boolean!
    isMandatory: Boolean!
    isPartOfBasePay: Boolean!
    isInstallment: Boolean!
    label: String!
    isArchived: Boolean
    overriddenEffectiveDate: Date
}

type PayrollInputSnapshotCompensationChange implements DomainPayItem & SnapshotPayItem & PayItemPayrollCycleInfo & PayrollInputCompensationChange @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    payrollCycleId: ID!
    cutOff: Date!
    entityId: ID!
    componentName: String!
    componentCategory: String!
    currency: CurrencyCode!
    rateType: PayItemRateType!
    billingRate: Float!
    billingFrequency: BillingFrequency!
    effectiveDate: Date!
    startDate: Date!
    endDate: Date
    payScheduleName: String!
    numberOfInstallments: Int
    compensationSource: CompensationChangeSource!
    sourceId: String!
    isTaxable: Boolean!
    isFixed: Boolean!
    isProrated: Boolean!
    isMandatory: Boolean!
    isPartOfBasePay: Boolean!
    isInstallment: Boolean!
    label: String!
    overriddenEffectiveDate: Date
}
