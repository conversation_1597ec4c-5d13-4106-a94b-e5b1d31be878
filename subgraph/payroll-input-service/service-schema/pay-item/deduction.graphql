interface PayrollInputDeduction implements DomainPayItem @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    startDateInclusive: Date!
    endDateInclusive: Date
    billingFrequency: BillingFrequency!
    paymentFrequency: PayFrequency!
    rateType: PayItemRateType!
    componentName: String!
    componentCategory: String!
    currency: CurrencyCode!
    billingRate: Float!
    calculatedAmount: Float!
    effectiveDate: Date!
    companyId: ID!
    entityId: ID!
    payScheduleName: String!
    isTaxable: Boolean!
    isFixed: Boolean!
    isProrated: Boolean!
    isMandatory: Boolean!
    isPartOfBasePay: Boolean!
    isInstallment: Boolean!
    numberOfInstallments: Int
    currentInstallment: Int
    isArrears: Boolean!
    label: String!
    overriddenEffectiveDate: Date
}

type PayrollInputStagingDeduction implements DomainPayItem & StagingPayItem & PayrollInputDeduction @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    startDateInclusive: Date!
    endDateInclusive: Date
    billingFrequency: BillingFrequency!
    paymentFrequency: PayFrequency!
    rateType: PayItemRateType!
    componentName: String!
    componentCategory: String!
    currency: CurrencyCode!
    billingRate: Float!
    calculatedAmount: Float!
    effectiveDate: Date!
    companyId: ID!
    entityId: ID!
    payScheduleName: String!
    isTaxable: Boolean!
    isFixed: Boolean!
    isProrated: Boolean!
    isMandatory: Boolean!
    isPartOfBasePay: Boolean!
    isInstallment: Boolean!
    numberOfInstallments: Int
    currentInstallment: Int
    isArrears: Boolean!
    label: String!
    isArchived: Boolean
    overriddenEffectiveDate: Date
}

type PayrollInputSnapshotDeduction implements DomainPayItem & SnapshotPayItem & PayItemPayrollCycleInfo & PayrollInputDeduction @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    payrollCycleId: ID!
    startDateInclusive: Date!
    endDateInclusive: Date
    cutOff: Date!
    billingFrequency: BillingFrequency!
    paymentFrequency: PayFrequency!
    rateType: PayItemRateType!
    componentName: String!
    componentCategory: String!
    currency: CurrencyCode!
    billingRate: Float!
    calculatedAmount: Float!
    effectiveDate: Date!
    companyId: ID!
    entityId: ID!
    payScheduleName: String!
    isTaxable: Boolean!
    isFixed: Boolean!
    isProrated: Boolean!
    isMandatory: Boolean!
    isPartOfBasePay: Boolean!
    isInstallment: Boolean!
    numberOfInstallments: Int
    currentInstallment: Int
    isArrears: Boolean!
    label: String!
    overriddenEffectiveDate: Date
}
