interface PayrollInputTimeSheet implements DomainPayItem @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    timesheetId: ID!
    contractId: ID!
    effectiveDate: Date!
    entryDate: Date!
    dayTypeName: String!
    isPaidLeave: Boolean!
    approvedOn: DateTime
    hoursTypeName: String!
    numberOfMinutes: Int!
    multiplicationFactor: Float!
    componentCategory: String!
    componentName: String!
    overriddenEffectiveDate: Date
}

type PayrollInputStagingTimeSheet implements DomainPayItem & StagingPayItem & PayrollInputTimeSheet @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    timesheetId: ID!
    contractId: ID!
    effectiveDate: Date!
    entryDate: Date!
    dayTypeName: String!
    isPaidLeave: Boolean!
    approvedOn: DateTime
    hoursTypeName: String!
    numberOfMinutes: Int!
    multiplicationFactor: Float!
    componentCategory: String!
    componentName: String!
    isArchived: Boolean
    overriddenEffectiveDate: Date
}

type PayrollInputSnapshotTimeSheet implements DomainPayItem & SnapshotPayItem & PayItemPayrollCycleInfo & PayrollInputTimeSheet @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    timesheetId: ID!
    contractId: ID!
    payrollCycleId: ID!
    effectiveDate: Date!
    cutOff: Date!
    entryDate: Date!
    dayTypeName: String!
    isPaidLeave: Boolean!
    approvedOn: DateTime
    hoursTypeName: String!
    numberOfMinutes: Int!
    multiplicationFactor: Float!
    componentCategory: String!
    componentName: String!
    overriddenEffectiveDate: Date
}
