interface PayrollInputExpense implements DomainPayItem @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    effectiveDate: Date!
    expenseTitle: String!
    currency: CurrencyCode!
    totalAmount: Float!
    externalId: String
    externalViewLink: String
    componentCategory: String!
    componentName: String!
    overriddenEffectiveDate: Date
}

type PayrollInputStagingExpense implements DomainPayItem & StagingPayItem & PayrollInputExpense @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    effectiveDate: Date!
    expenseTitle: String!
    currency: CurrencyCode!
    totalAmount: Float!
    externalId: String
    externalViewLink: String
    componentCategory: String!
    componentName: String!
    isArchived: Boolean
    overriddenEffectiveDate: Date
}

type PayrollInputSnapshotExpense implements DomainPayItem & SnapshotPayItem & PayItemPayrollCycleInfo & PayrollInputExpense @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    payrollCycleId: ID!
    effectiveDate: Date!
    cutOff: Date!
    expenseTitle: String!
    currency: CurrencyCode!
    totalAmount: Float!
    externalId: String
    externalViewLink: String
    componentCategory: String!
    componentName: String!
    overriddenEffectiveDate: Date
}
