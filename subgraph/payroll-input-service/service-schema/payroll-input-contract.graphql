type PayrollInputContract @key(fields: "id") {
    id : UUID!
    payrollContractTypes: [PayrollContractType!] @deprecated(reason: "Use payrollActive instead")
    payrollActive: Boolean!
    snapshotStatus: PayrollSnapshotStatus!
    currency: CurrencyCode!
    totalBaseSalary: PaySummary
    totalAdditionalAllowances: PaySummary
    totalDeductions: PaySummary
    totalExpenses: PaySummary
    totalPaySupplements: PaySummary
    totalTimeOffs: TimeOffSummary

    baseSalaryBreakdowns: [PayDetail!]
    additionalAllowances: [PayDetail!]
    paySupplements: [PayDetail!]
    deductions: [PayDetail!]
    expenses: [PayDetail!]
    unprocessedPayItems: UnprocessedPayItems,
}

type UnprocessedPayItems {
    compensations: [PayrollInputCompensation!],
    compensationChanges: [PayrollInputCompensationChange!],
    paySupplements: [PayrollInputPaySupplement!],
    deductions: [PayrollInputDeduction!],
    expenses: [PayrollInputExpense!],
    timeOffs: [PayrollInputTimeOff!]
    timeSheets: [PayrollInputTimeSheet!]
}

type PayrollInputContractResponse {
    pageResult: PageResult!
    data: [PayrollContract!]
}

type EmployeeDataChangesV2 {
    compensation: Int
    bankDetails: Int
}

type InputHeadcountSummaryV2 {
    previousCount: Int
    newJoiner: Int
    leaver: Int
    totalCount: Int
}

type TimeOffSummary {
    paid: Float
    unpaid: Float
}

type PaySummary {
    currency: CurrencyCode
    absolute: Float
    hourly: Float
}

type PayDetail {
    name: String
    componentCategory: String
    value: Float
    rate: BillingFrequency
}
