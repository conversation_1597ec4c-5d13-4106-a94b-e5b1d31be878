interface DomainPayItem @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    overriddenEffectiveDate: Date
}

interface StagingPayItem implements DomainPayItem @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    isArchived: Boolean
    overriddenEffectiveDate: Date
}

interface SnapshotPayItem implements DomainPayItem & PayItemPayrollCycleInfo @key(fields: "id") {
    id: UUID!
    domainType: PayrollInputDomainType!
    domainId: String!
    contractId: ID!
    payrollCycleId: ID!
    effectiveDate: Date!
    cutOff: Date!
    overriddenEffectiveDate: Date
}

interface PayItemPayrollCycleInfo {
    payrollCycleId: ID!
    effectiveDate: Date!
    cutOff: Date!
}
