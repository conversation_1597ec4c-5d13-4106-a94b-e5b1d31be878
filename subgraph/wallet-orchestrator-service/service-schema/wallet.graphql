type MultiplierWallet @key(fields: "id") {
    id: ID!
    walletAccounts: [WalletAccount!]
}

interface WalletAccount {
    id: ID!
    type: WalletType
    status: WalletStatus
    category: WalletCategory!
    transferType: TransferType!
    country: CountryCode!
    onboardingDetails: WalletOnboardingDetail
    walletPaymentAccount: WalletPaymentAccount!
}

type FiatWalletAccount implements WalletAccount {
    id: ID!
    type: WalletType!
    status: WalletStatus!
    category: WalletCategory!
    transferType: TransferType!
    country: CountryCode!
    onboardingDetails: WalletOnboardingDetail
    walletPaymentAccount: WalletPaymentAccount!
}


