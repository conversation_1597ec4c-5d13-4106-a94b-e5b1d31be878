extend type Mutation {
    upsertWalletOption(input: WalletOptionInput) : WalletOptionDefinition  @authorize(operations: ["update.wallet.onboarding-options"])
}

input WalletOptionInput {
    type: WalletType
    category: WalletCategory
    country: CountryCode
    walletPartner: PaymentPartnerCode
    dimensions: WalletOptionDimensionInput
}

input WalletOptionDimensionInput {
    contractDimension: WalletOptionContractDimension
}

input WalletOptionContractDimension {
    contractType : ContractType
}

type WalletOptionDefinition {
    type: WalletType
    category: WalletCategory
    country: CountryCode
    walletPartner: PaymentPartnerCode
    dimensions: [WalletOptionDimension]
}

interface WalletOptionDimension {
    type: WalletOptionDimensionType
}

type ContractWalletOptionDimension implements WalletOptionDimension {
    type: WalletOptionDimensionType
    contractType: ContractType
}

enum WalletOptionDimensionType {
    CONTRACT
}
