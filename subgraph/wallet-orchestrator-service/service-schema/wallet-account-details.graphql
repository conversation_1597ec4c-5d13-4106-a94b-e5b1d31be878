type WalletOnboardingDetail {
    id: ID!
    details: [KeyValue],
    metadata: [KeyValue]
    documents: [WalletDocumentData!]
    kycDetails: [WalletOnboardingKycDetails]
    onboardingWorkflow: WalletOnboardingWorkflow!
    createdOn: DateTime
    updatedOn: DateTime
}

type WalletOnboardingWorkflow {
    currentStep: WalletOnboardingStep
    currentStatus: WalletOnboardingStatus
    allSteps: [WalletOnboardingStep]
    allStatus: [WalletOnboardingStatus]
}

type WalletOnboardingKycDetails {
    result: KycResult
    clarificationReasons: [KycResultReason]
    rejectReasons: [KycResultReason]
}

type KycResultReason {
    field: String
    reason: String
    metadata: [KeyValue]
    createdOn: DateTime
    updatedOn: DateTime
}

enum KycResult {
    ACCEPTED
    REJECTED
    CLARIFICATION_REQUIRED
}

type WalletDocumentData {
    key : String
    document: DocumentReadable
}
