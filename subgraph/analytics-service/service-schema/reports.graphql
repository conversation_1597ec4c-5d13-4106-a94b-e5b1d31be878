extend type Mutation {
    createReportTemplate(input: CreateReportTemplateInput!): ReportTemplate! @authorize(company: ["create.company.report-templates"])
    updateReportTemplate(input: UpdateReportTemplateInput!): ReportTemplate! @authorize(company: ["update.company.report-templates"])
    deleteReportTemplate(id: UUID!): TaskResponse! @authorize(company: ["delete.company.report-templates"])
    generateReportAsync(input: GenerateReportInput): UUID! @authorize(company: ["generate.company.report"])
}

extend type Query {
    getReportTemplates(input: GetReportTemplatesInput!): [ReportTemplate!]! @authorize(company: ["view.company.report-templates"])
    viewReportTemplate(input: ViewReportTemplatesInput!): ReportTemplatePaginated! @authorize(company: ["view.company.report-templates"])
    getReports(input: GetReportInput!): [ReportInfo!]! @authorize(company: ["view.company.generated-reports"])
}

type ReportTemplate {
    id: UUID!
    name: String!
    description: String
    dataSetId: String! # Empty if composite report
    categories: [String!]!
    isCustomTemplate: Boolean!
    isInternal: Boolean!
    availableFilters: [ReportTemplateFilter!]! # Empty if composite report
    availableColumns: [ReportTemplateColumn!]! # Empty if composite report
    config: ReportTemplateConfig!
}

type ReportInfo {
    id: UUID!
    status: ReportGenerationStatus!
    downloadLink: String
    expirationTime: DateTime
}

type ReportTemplatePaginated {
    columns: [ReportColumn!]!
    pageResult: PageResult!
}

type ReportTemplateFilter {
    name: String!
    description: String
    columnName: String!
    isMandatory: Boolean!
    dataType: ReportTemplateFieldDataType!
    supportedOperators: [FilterOperator!]!
    values: [String!]!
}

type ReportTemplateColumn {
    displayName: String!
    columnName: String!
    isMandatory: Boolean!
    isSortable: Boolean!
    dataType: ReportTemplateFieldDataType!
    order: Int!
}

type ReportTemplateConfig {
    joiningConfig: [ReportTemplateJoiningConfig!]! # Empty if NOT a composite report
    filters: FilterExpression # Empty if composite report
    selectedColumns: [SelectedColumn!]!
    sortingColumns: [SortingColumn!]!
}

type ReportTemplateJoiningConfig {
    leftTemplate: ReportTemplate! # Points to left template in JOIN
    rightTemplate: ReportTemplate! # Points to right template in JOIN
    leftJoinColumn: String!
    rightJoinColumn: String!
    joinOperator: JoinOperator!
    order: Int!
}

type SelectedColumn {
    dataSetId: String! # Defines which dataset this column belongs to. In normal reports, all will point to the same dataset. For composite reports, they differ.
    columnName: String!
    customColumnName: String
    order: Int!
}

type SortingColumn {
    dataSetId: String! # Defines which dataset this column belongs to. In normal reports, all will point to the same dataset. For composite reports, they differ.
    columnName: String!
    sortType: SortType!
    order: Int!
}

type FilterExpression {
    AND: [FilterExpression!]
    OR: [FilterExpression!]
    XOR: [FilterExpression!]
    NOT: FilterExpression
    condition: ConfiguredFilter
}

type ConfiguredFilter {
    columnName: String!
    operator: FilterOperator!
    filterValues: [String!]!
}

type ReportColumn {
    name: String!
    value: [String]!
}

input GetReportInput {
    reportIds: [UUID!]!
}

input GenerateReportInput {
    reportTemplateId: UUID!
    format: ReportFileFormat!
    filter: FilterExpressionInput
}

input CreateReportTemplateInput {
    name: String!
    description: String
    dataSetId: String! # Empty if composite report
    config: ReportTemplateConfigInput!
}

input UpdateReportTemplateInput {
    id: UUID!
    name: String
    description: String
    config: UpdateReportTemplateConfigInput
}

input GetReportTemplatesInput {
    ids: [UUID!]
}

input ReportTemplateConfigInput {
    joiningConfig: [ReportTemplateJoiningConfigInput!]! # Empty if NOT a composite report
    filters: [FilterExpressionInput!]! # 0..N if composite report, otherwise only 0..1
    selectedColumns: [SelectedColumnInput!]!
    sortingColumns: [SortingColumnInput!]!
}

input UpdateReportTemplateConfigInput {
    filters: [FilterExpressionInput!]! # Empty if composite report
    selectedColumns: [SelectedColumnInput!]!
    sortingColumns: [SortingColumnInput!]!
}

input ReportTemplateJoiningConfigInput {
    leftDataSetId: String!
    rightDataSetId: String!
    leftJoinColumn: String!
    rightJoinColumn: String!
    joinOperator: JoinOperator!
    order: Int!
}

input SelectedColumnInput {
    dataSetId: String!
    columnName: String!
    customColumnName: String
    order: Int!
}

input SortingColumnInput {
    dataSetId: String!
    columnName: String!
    sortType: SortType!
    order: Int!
}

input FilterExpressionInput {
    AND: [FilterExpressionInput!] # Only one of these fields should be set
    OR: [FilterExpressionInput!] # Only one of these fields should be set
    XOR: [FilterExpressionInput!] # Only one of these fields should be set
    NOT: FilterExpressionInput # Only one of these fields should be set
    condition: ConfiguredFilterInput # Only one of these fields should be set
}

input ConfiguredFilterInput {
    dataSetId: String!
    columnName: String!
    operator: FilterOperator!
    filterValues: [String!]!
}

input ViewReportTemplatesInput {
    dataSetId: String! # Empty if composite report
    config: ReportTemplateConfigInput!
    pageRequest: PageRequest
}

enum SortType {
    ASC
    DESC
}

enum ReportTemplateFieldDataType {
    DATETIME
    ENUM
    COLLECTION
    NUMERIC
    TEXT
    BOOLEAN
}

enum FilterOperator {
    # General
    EQUAL
    NOT_EQUAL

    # DateTime
    BEFORE
    AFTER
    LAST_N_DAYS

    # Numeric
    GREATER_THAN
    LESS_THAN
    GREATER_THAN_OR_EQUAL_TO
    LESS_THAN_OR_EQUAL_TO
}

enum ReportGenerationStatus {
    INVALID
    ONGOING
    FINISHED
    FAILED
}

enum ReportFileFormat {
    XLSX
    CSV
    # Note: PDF is intentionally not supported due to possible layout overflow
}

enum JoinOperator {
    EQUAL
}

