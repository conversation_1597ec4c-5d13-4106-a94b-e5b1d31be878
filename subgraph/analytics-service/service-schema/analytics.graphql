extend type Mutation {
    test: <PERSON><PERSON><PERSON> @authorize(company: ["*"])
}
extend type Query {
    #if ReportID is not null then return the ReportConfig containing info regarding the report
    #else returns ReportConfig containing info regarding all reports
    getAvailableReports(input: ReportID):[ReportConfig]! @authorize(member: ["view.member.reports"], company: ["view.company.reports"])

    getInvoiceReportConfig(input: ReportID):[ReportConfig]! @authorize(member: ["view.member.reports"], company: ["view.company.reports"])

    #input should be a JSON string containing report name and filters
    #returns JSON string containing all the data
    getReport(input: String): String! @authorize(member: ["view.member.reports"], company: ["view.company.reports"])
    getReportV2(input: String): DocumentReadable! @authorize(member: ["view.member.reports"], company: ["view.company.reports"])
    generateAsyncReport(input: String): GenerateReportPayload! @authorize(member: ["view.member.reports"], company: ["view.company.reports"]) @deprecated(reason: "Use generateReportaAsync instead")
    generateReportaAsync(input: String): GenerateAsyncReportResponse! @authorize(member: ["view.member.reports"], company: ["view.company.reports"])
}

input ReportID{
    id: String
}

type ReportConfig {
    title:String
    id: String
    description:String
    filters: [Filter]!
    formats: [Formats]
    reportGenerationType: ReportGenerationType
    reportSource: ReportSource
}

type Filter{
    parameterTitle: String
    parameterName:String
    isMandatory: Boolean
    possibleValues:[String]!
    type: FilterTypes
}

enum FilterTypes{
    TEXT
    NUMBER
    DROPDOWN
    MULTI_DROPDOWN
    MULTI_TEXT_INPUT
    MONTH
    MONTH_RANGE
    DATE_RANGE
    RANGE
}

enum ReportSource {
    WAREHOUSE
    DOMAIN_SPECIFIC
    HYBRID
}

enum Formats {
    CSV_REPORT
    PDF_REPORT
    INVOICES_ZIP
}

enum ReportGenerationType {
    SYNC
    ASYNC
}

enum ReportStatus {
    IN_PROGRESS
    FAILED
    DATA_NOT_FOUND
}

type GenerateReportPayload {
    id: String
    status: [ReportStatus]!
    message: String
    createdAt: DateTime
}

type GenerateAsyncReportResponse {
    id: String
    status: ReportStatus!
    message: String
    createdAt: DateTime
}
