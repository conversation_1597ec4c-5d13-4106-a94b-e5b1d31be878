type ApprovalRule {
    orRulesGroup: OrRulesGroup
    andRulesGroup: AndRulesGroup
    stringRule: StringRule
    numberRule: NumberRule
    multiStringRule: MultiStringRule
}

type OrRulesGroup {
    rules: [ApprovalRule!]!
}

type AndRulesGroup {
    rules: [ApprovalRule!]!
}

type StringRule {
    approvalRequestMetadataKey: String!
    operator: StringOperator!
    targetValue: String!
}

type NumberRule {
    approvalRequestMetadataKey: String!
    operator: NumberOperator!
    targetValue: Float!
}

type MultiStringRule {
    approvalRequestMetadataKey: String!
    operator: MultiStringOperator!
    targetValues: [String!]!
}

enum StringOperator {
    EQUALS
    NOT_EQUALS
}

enum NumberOperator {
    EQUALS
    NOT_EQUALS
    GREATER_THAN
    LESS_THAN
    GREATER_OR_EQUALS
    LESS_OR_EQUALS
}

enum MultiStringOperator {
    IN
    NOT_IN
}
