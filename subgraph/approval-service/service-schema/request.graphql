extend type Query {
    approvalRequests(ids: [ID!]!): [ApprovalRequest!] @authorize(operations: ["view.operations.approval.request"])
}

extend type Mutation {
    resolveDgsIssue: <PERSON><PERSON><PERSON> @authorize(operations: ["this.is.not.a.valid.permission"]) # resolve DGS runtime issue.
}

type ApprovalRequest @key(fields: "id") {
    id: ID!
    type: ApprovalType
    domain: ApprovalDomain
    status: ApprovalRequestStatus
    approvalConfig: ApprovalConfig
    raisedBy: ApprovalRequestActor
    originalObjectId: String
    metadata: [ApprovalMetadata!]!
    approverTasks: [ApproverTask!]!
    activeApproverTask: [ID!]!
}

enum ApprovalDomain {
    EXPENSES
    COUNTRY_CONFIG
}

enum ApprovalRequestStatus {
    PENDING
    APPROVED
    REJECTED
    WITHDRAWN
}

type ApprovalMetadata {
    key: String
    value: String
}

type ApproverTask {
    id: ID!
    status: ApproverTaskStatus
    stage: Int
    decisionStrategy: ApproverDecisionStrategy
    allowedApprovers: [ApprovalRequestActor!]!
    resolvedOn: DateTime
    approverDecisions: [ApproverDecision!]!
    defaultApprovers: [ApprovalRequestActor!]!
    clarificationRequests: [ApprovalClarificationRequest!]!
}

enum ApproverTaskStatus {
    DORMANT
    PENDING
    APPROVED
    REJECTED
    CANCELLED
    ON_HOLD
    AUTO_APPROVED
}

enum ApproverDecisionStrategy {
    ONE
    ALL
}

type ApprovalRequestActor @key(fields: "id") {
    id: ID! # platform_user.id
    firstName: String
    lastName: String
    email: String
    roles: [String!]!
    isActivated: Boolean
    isBlocked: Boolean
}

type ApproverDecision {
    id: ID!
    decision: ApproverDecisionAction
    actionedBy: ApprovalRequestActor
    note: String
}

enum ApproverDecisionAction {
    APPROVE
    REJECT
}

type ApprovalClarificationRequest {
    id: ID!
    initiator: ApprovalRequestActor
    initiatorMessage: String
    initiatedOn: DateTime
    resolver: ApprovalRequestActor
    resolvedBy: ApprovalRequestActor
    resolverMessage: String
    resolvedOn: DateTime
}
