extend type Query {
    approvalConfigs(ids: [UUID!]!): [ApprovalConfig!] @authorize(operations: ["view.operations.approval-configs"], company: ["view.company.approval-configs"])
}
type ApprovalConfig @key(fields: "id") {
    id: UUID!
    name: String
    description: String
    approvalType: ApprovalType
    approvalStages: [ApprovalStage!]! # Simplified from domain
    status: ApprovalConfigStatus
}

enum ApprovalType {
    SEQUENTIAL
}

type ApprovalStage {
    stage: Int
    approvers: ApprovalStageApprovers
    ruleSet: ApprovalStageRuleSet
}

type ApprovalStageApprovers {
    type: ApprovalStageType
    value: [String!]!
    decisionStrategy: ApproverDecisionStrategy
}

type ApprovalStageRuleSet {
    rule: ApprovalRule!
    action: ApprovalStageRuleAction!
}

enum ApprovalStageRuleAction {
    AUTO_APPROVE
}

enum ApprovalStageType {
    USER,
    ROLE,
    HIERARCHY,
    INTERNAL_ROLE
}

enum DecisionStrategy {
    ONE,
    ALL,
}

enum ApprovalConfigStatus {
    ACTIVE,
    ARCHIVED,
    DRAFT,
}
