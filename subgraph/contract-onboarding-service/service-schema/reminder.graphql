type Query {
    opsEmailCtaReminder(contractId: ID!): [EmailTemplateDataResponse!]! @authorize(operations: ["trigger.operations.onboarding-reminder.cta"])
}

type Mutation {
    sendOpsEmailCtaReminder(contractId: ID!, templateName: NotificationType!): SendEmailResponse! @authorize(operations: ["trigger.operations.onboarding-reminder.cta"])
    sendEditableOpsEmailCtaReminder(contractId: ID!, templateName: NotificationType!, editableTemplateParams: JSON): SendEmailResponse! @authorize(operations: ["trigger.operations.onboarding-reminder.cta"])
}

type SendEmailResponse {
    success: Boolean!
    message: String
}

type EmailTemplateDataResponse {
    subject: String!
    bodyHtml: String!
    reminder: Reminder
    templateName: NotificationType!
    templateProperties: TemplateProperties
}

type TemplateProperties {
    isEditable: Boolean!
    editableParams: [String]
}

type Reminder {
    templateName: String!
    count: Int!
    lastSentOn: DateTime!
    editableTemplateParams: JSON
}
