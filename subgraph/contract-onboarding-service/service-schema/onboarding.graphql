extend type Query {
    bulkOnboardingTemplateDownload(options: BulkOnboardingOptions!, pageRequest: PageRequest): DocumentReadable @authorize(company: ["download.company.contract.onboarding-template"], operations: ["download.operations.contract.onboarding-template"])
    countryAllowancesConfig(country: CountryCode!): [Allowance!]! @authorize(company: ["view.company.compensation"], operations: ["view.operations.compensation"])
}

extend type Mutation {
    triggerOnboardingReminderEmailProcess(simulatedSendingDate: Date, companyIds: [ID!]): TaskResponse @authorize(operations: ["trigger.operations.onboarding.notification"])
    triggerCompleteWorkEmailAndEmployeeIdReminderEmailProcess(simulatedSendingDate: Date, companyIds: [ID!]): TaskResponse @authorize(operations: ["trigger.operations.onboarding.notification"])

    bulkOnboardingValidate(employeeDataFile: Upload!, options: BulkOnboardingOptions!): BulkOnboardingValidationResult @authorize(company: ["create.company.contract"], operations: ["create.operations.contract"])
    bulkOnboardingTrigger(employeeDataFile: Upload!, options: BulkOnboardingOptions!): BulkOnboardingJob @authorize(company: ["create.company.contract"], operations: ["create.operations.contract"])
    sendEmailToOnboardingSpecialist(contractId: ID!, message: String!): SendEmailToOnboardingSpecialistResponse @authorize(company: ["view.company.contract.onboarding"], member: ["view.member.contract.onboarding"])

    """
    This directly updates the onboarding record by setting values based on the input.
    This is intended for Product Ops only (alternative of database-updater PRs).
    """
    contractOnboardingUpdateStep(input: ContractOnboardingUpdateStepInput!): TaskResponse @authorize(operations: ["update.operations.contract.onboarding"])
}

input ContractOnboardingUpdateStepInput {
    contractId: ID!
    currentStep: ContractOnboardingStep!
    status: ContractOnboardingStatus!
    experience: String!
    revokedBy: String
}

type Allowance {
    country: CountryCode!
    name: String!
    label: String!
    mandatory: Boolean!
}

enum BulkOnboardingJobStatus {
    IN_PROGRESS
    SUCCESS
    FAILED
    VALIDATION_FAILED
}

type BulkOnboardingJob {
    id: String!
    startTime: DateTime!,
    status: BulkOnboardingJobStatus!,
    validationResult: BulkOnboardingValidationResult!
    onboardingResult: BulkOnboardingResult
}

type BulkOnboardingValidationResult {
    validEmployeeCount: Int!
    errorEmployeeCount: Int!
    totalEmployeeCount: Int!
    errors: [BulkOnboardingMessage!]
    warnings: [BulkOnboardingMessage!]
    validationErrors: [BulkOnboardingMessage!]
    validationResultFile: DocumentReadable
}

type BulkOnboardingMessage {
    employeeId: String
    employeeName: String
    rowNumber: Int
    columnName: String
    message: String!
}

type BulkOnboardingResult {
    onboardedEmployeeCount: Int!
    failedEmployeeCount: Int!
    errors: [BulkOnboardingMessage!]
    warnings: [BulkOnboardingMessage!]
}

type SendEmailToOnboardingSpecialistResponse {
    success: Boolean!
    message: String
    requesterEmail: String
}
