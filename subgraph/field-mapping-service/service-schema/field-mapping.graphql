extend type Query {
    # Field Mapping Profile queries
    fieldMappingProfile(id: UUID!): FieldMappingProfile @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    fieldMappingProfiles(companyId: ID): [FieldMappingProfile!]! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])

    # Suggestion queries
    fieldMappingSuggestion(id: UUID!): FieldMappingSuggestion @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    fieldMappingSuggestions(companyId: ID!): [FieldMappingSuggestion!]! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    fieldMappingSuggestionSimplified(sourceFields: [String!]!, targetFields: [String!]!): [FieldMappingSuggestionSimplified!]!  @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
}

extend type Mutation {
    # Field Mapping Profile mutations
    fieldMappingCreate(input: CreateFieldMappingProfileInput!): FieldMappingProfile! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    fieldMappingUpdate(id: UUID!, input: UpdateFieldMappingProfileInput!): FieldMappingProfile! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    fieldMappingDelete(id: UUID!): Boolean! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])

    # Rule mutations
    fieldMappingRuleAdd(profileId: UUID!, input: FieldMappingRuleInput!): FieldMappingProfile! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    fieldMappingRuleUpdate(profileId: UUID!, ruleId: UUID!, input: FieldMappingRuleInput!): FieldMappingProfile! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    fieldMappingRuleRemove(profileId: UUID!, ruleId: UUID!): FieldMappingProfile! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])

    # Execution mutations
    fieldMappingExecute(profileId: UUID!, sourceData: JSON!): MappingExecutionResult! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    fieldMappingExecuteBatch(profileId: UUID!, sourceDataBatch: [JSON!]!): BatchMappingExecutionResult! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])

    # Suggestion mutations
    fieldMappingSuggestionGenerate(input: GenerateSuggestionsInput!): GenerateSuggestionsResult! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
    fieldMappingSuggestionSave(profileId: UUID!, suggestionId: UUID!): Boolean! @authorize(company: ["view.company.company.has-integration"], operations: ["view.operations.company.integration"])
}

# Transformation types
enum TransformationType {
    DIRECT_MAPPING
    VALUE_TRANSFORMATION
    CONCATENATION
    SPLIT
    DATE_FORMAT_CONVERSION
    REGEX_EXTRACTION
    BOOLEAN_CONVERSION
    NUMERIC_TRANSFORMATION
    CONDITIONAL_MAPPING
    FUNCTIONAL_TRANSFORMATION
}

# Field data types
enum FieldDataType {
    STRING
    INTEGER
    DECIMAL
    BOOLEAN
    DATE
    DATETIME
    EMAIL
    PHONE
    ENUM
    OBJECT
    ARRAY
}

# Field Mapping Profile types
type FieldMappingProfile {
    id: UUID!
    name: String!
    description: String
    companyId: ID
    isActive: Boolean!
    createdAt: DateTime!
    updatedAt: DateTime!
    rules: [FieldMappingRule!]!
    config: JSON
}

type FieldMappingRule {
    id: UUID!
    sourceField: String!
    sourceLabel: String!
    targetField: String!
    targetLabel: String!
    transformationType: TransformationType!
    isRequired: Boolean!
    defaultValue: String
    order: Int!
    transformationConfig: FieldMappingRuleTransformationConfig
}

type FieldMappingRuleTransformationConfig {
    mappings: JSON
    function: String
    conditions: [FieldMappingRuleCondition!]
    default: String
}

type FieldMappingRuleCondition {
    operator: String
    value: String
    result: String
}

# Suggestion types
type FieldMappingSuggestion {
    id: UUID!
    sourceField: String!
    sourceLabel: String!
    targetField: String!
    targetLabel: String!
    confidenceScore: Float!
    sampleValues: [String!]!
    transformationType: TransformationType!
    transformationConfig: JSON
    reasoning: String
    createdAt: DateTime!
    companyId: ID
}

# Execution result types
type MappingExecutionResult {
    profileId: UUID!
    transformedData: JSON!
}

type BatchMappingExecutionResult {
    profileId: UUID!
    transformedDataBatch: [JSON!]!
}

# Input types
input CreateFieldMappingProfileInput {
    name: String!
    description: String
    companyId: ID
    isActive: Boolean
    rules: [FieldMappingRuleInput!]
    configMap: JSON
}

input UpdateFieldMappingProfileInput {
    name: String
    description: String
    companyId: ID
    isActive: Boolean
    rules: [FieldMappingRuleInput!]
    configMap: JSON
}

input FieldMappingRuleInput {
    sourceField: String!
    sourceFieldLabel: String
    targetField: String!
    targetFieldLabel: String
    transformationType: TransformationType!
    transformationConfig: FieldMappingRuleTransformationConfigInput
    isRequired: Boolean
    defaultValue: String
    order: Int
}

input FieldMappingRuleTransformationConfigInput {
    mappings: JSON
    function: String
    conditions: [FieldMappingRuleConditionInput!]
    default: String
}

input FieldMappingRuleConditionInput {
    operator: String
    value: String
    result: String
}

input GenerateSuggestionsInput {
    companyId: ID!
    sampleData: [JSON!]!
    targetKeys: [String!]!
}

type GenerateSuggestionsResult {
    suggestions: [FieldMappingSuggestion!]!
    unmappedTargetFields: [String!]!
    unmappedSourceFields: [String!]!
}

type FieldMappingSuggestionSimplified {
    sourceField: String!
    targetField: String!
    confidenceScore: Float!
}
