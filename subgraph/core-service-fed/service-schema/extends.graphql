# for TimeOffApprovalItem
type TimeOff @key(fields: "id") @extends {
    id: ID! @external
    approverInfos: [ItemApproverInfo!]
}

type Expense @key(fields: "id") @extends {
    id: ID @external
    approverInfos: [ItemApproverInfo!]
}

type Member implements Person @key(fields: "id") @extends {
    id: ID @external

    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type MemberChangeRequest @key(fields: "id") @extends {
    id: ID! @external
}

type LegalDocument @key(fields: "id") @extends {
    id: ID! @external
}

type Company @key(fields: "id") @extends {
    id: ID @external
    approvers (userId: ID, category: ApprovalCategory): [Approver]  # Approvers filter by companyUserId (i.e. userId = companyUserId)
    requestsCreated(status: RequestStatus): [Request]
    csmUser: OperationsUser
    salesUser: OperationsUser
    accountManagerUser: OperationsUser
}

type CompanyUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
    approver: Approver
}

type Contract @key(fields: "id") @extends {
    id: ID @external

    ### below fields are leftovers after contract graph was extracted

    # @FE: For each BenefitType, in case there are more than one element, take the last updated one and ignore others. Details: https://app.clickup.com/t/1x6334g
    benefits(contractBenefitFilters: ContractBenefitFilters): [ContractBenefit]                                    # contract benefit list can filter by plan, from/to date( startOn ) and dependents ( by having dependents or not )


    contractDataChanges(status: DataChangeStatus): [DataChange]
    contractLatestDataChange: [DataChange]

    """
    To know expense/timeoff approvers of the contract.<br>
    Useful for the company admin when assigning members to managers, or for the member when submitting a timeoff/expense
    """
    categoryApproverInfos(
        """
        whether expense/timeoff approvers (i.e. managers) have to be ACTIVE; false means 'managers in all statuses' are valid;
        does not affect how admins will be fetched in case 'returnAdminsIfNoManagers is true & no managers found'
        """
        activeManagersOnly: Boolean = false,
        """whether this query should return all active admins (instead of nothing) when 'no managers of the category are found for the contract'"""
        returnAdminsIfNoManagers: Boolean = false
    ): [CategoryApproverInfo]
}

type CompanyPayroll @key(fields: "id") @extends {
    id: ID @external
}

interface MemberPay @key(fields: "id") @extends {
    id: ID @external
    contract: Contract @external
    currency: CurrencyCode @external
    amountTotalCost: Float @external
}

type CompanyMemberPay implements MemberPay @key(fields: "id") @extends {
    id: ID @external
    contract: Contract @external
    currency: CurrencyCode @external
    amountTotalCost: Float @external
}

type PartnerMemberPay implements MemberPay @key(fields: "id") @extends {
    id: ID @external
    contract: Contract @external
    currency: CurrencyCode @external
    amountTotalCost: Float @external
}

type PayrollPartnerCountry implements PartnerCountry @key(fields: "id") @extends {
    id: ID @external
    capability: PartnerCapability @external
    country: CountryCode @external
    partner: Partner @external
}

interface MemberPayable @key(fields: "id") @extends {
    id: ID @external
}

type FreelancerPayable implements MemberPayable @key(fields: "id") @extends {
    id: ID! @external
}

type ExpenseMemberPayable implements MemberPayable @key(fields: "id") @extends {
    id: ID! @external
}

type PaySupplementMemberPayable implements MemberPayable @key(fields: "id") @extends {
    id: ID! @external
}

type ContractOnboarding @key(fields: "id") @extends {
    id: ID @external
    contract: Contract @external
    handler: OperationsUser @requires(fields: "contract { id }")
}

type Document @key(fields: "id") @extends {
    id: ID @external
}

