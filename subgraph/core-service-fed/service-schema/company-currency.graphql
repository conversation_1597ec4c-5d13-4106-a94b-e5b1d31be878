extend type Mutation {
    assignCompanyCurrencySlab(input: SetCompanyCurrencySlabInput!): TaskResponse @authorize(operations: ["update.operations.fxrates"])
    unassignCompanyCurrencySlab(input: SetCompanyCurrencySlabInput!): TaskResponse @authorize(operations: ["update.operations.fxrates"])
}

extend type Query {
    getAllCurrencySlabs(filters: CompanyCurrencySlabFilters): [CurrencySlab!]! @authorize(operations: ["view.operations.fxrates"])
}

input SetCompanyCurrencySlabInput {
    companyId: ID!,
    currencySlab: ID!
}

input CompanyCurrencySlabFilters {
    companyId: ID
}


type CurrencySlab{
    id: ID!,
    name: String!,
    slabRate: [SlabRate]!,
}

type SlabRate {
    sourceCurrency: CurrencyCode!,
    targetRates: [TargetRate!]!
}

type TargetRate{
    currency: CurrencyCode!,
    rate: Float!
}
