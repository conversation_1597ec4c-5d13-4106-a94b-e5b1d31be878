extend type Query {
    contractBenefitReport(id: ID!) : DocumentReadable
    contractBenefitsReport(filters: ContractBenefitFilters!): DocumentReadable                          # for ops users only
    contractBenefitDocument(id: ID!) : DocumentReadable @deprecated # refactor to use field resolver
    contractBenefitsWithPagination(filters: ContractBenefitWithPaginationFilters, pageRequest: PageRequest): ContractBenefitsResult
    contractBenefitBanners(filters: ContractBenefitBannerFilters): [ContractBenefitBanner]
    contractBenefitChangeRequests(filters: ContractBenefitChangeRequestFilters): [ContractBenefitChangeRequestResponse]
}

extend type Mutation {
    # Note: currently code logic still considers INSURANC<PERSON> the only benefit
    contractUpdateBenefits(id: ID!, input: ContractUpdateBenefitsInput!): Contract                                                      # id refers to the ContractId
    contractUpdateBenefitDependentBasicDetails(contractBenefitId: ID!, input: [ContractUpdateBenefitDependentInput]!): Contract
    contractUpdateBenefitDependentDocuments(contractBenefitDependentId: ID!, key: ContractBenefitDependentDocumentType, documents: [Upload]): Contract
    contractDeleteBenefitDependentDocuments(contractBenefitDependentId: ID!, key: ContractBenefitDependentDocumentType, documentIds: [ID]): Contract
    contractUpdateBenefitDependentPlan(contractBenefitId: ID!, input: [ContractUpdateBenefitDependentInput]!): Contract
    contractCreateBenefitChangeRequests(input: ContractBenefitChangeRequestInput!): ContractBenefitChangeRequestResponse @deprecated(reason: "change in response type")
    contractBenefitCreateChangeRequests(input: ContractBenefitChangeRequestInput!): [ContractBenefitChangeRequestResponse]

    # for members with reasons
    optOutInsurance(contractBenefitId: ID!, reasons: [String!]!): ContractBenefit # reason: [Already insured by another provider], [Other reason(s): my personal reason goes here]

    # for OPS
    updateInsurance(contractId: ID!, benefitUpdate: InsuranceUpdateInput!, dependentsUpdate: [ContractUpdateBenefitDependentInput!]): ContractBenefit   # id is null -> create, if id provided -> update
    updateInsuranceStatus(contractId: ID!, statuses: InsuranceStatuses!) : InsuranceStatusResponse
    upsertContractBenefitBanners(input: ContractBenefitBannerInput!): [ContractBenefitBanner]
    updateContractBenefitChangeRequest(input: [ContractBenefitChangeRequestUpdateInput!]!): [ContractBenefitChangeRequestResponse]
    triggerUpdateExpiredBanners: TaskResponse
    triggerUpdateScheduledBanners: TaskResponse
    migrateBenefitPartners(input: [MigrateBenefitPartnerInput]!): MigrateBenefitPartnerResponse
    renewInsurance(input: RenewInsuranceInput!): MigrateBenefitPartnerResponse
}

# --------------------------------------
# Mutation types....
# --------------------------------------

input InsuranceUpdateInput {
    # basics
    contractBenefitId: ID           # id is null -> create, if id provided -> update
    benefitId: String               #The benefit package id, example = "417dd1c0-a29b-4a19-a83f-d57e60bff68f"
    insuranceTier: BenefitTier
    healthId: String
    healthCard: HealthCardFile
    startOn: DateTime
    endOn: DateTime
    maxDependentCount: Int
    includeDependents: Boolean

    # billings
    discountTermPercentage: Float   # discount percentage against an employee insurance
    employerPayPercentage: Float    # employer's percentage from the total payment
    employeePayPercentage: Float    # employee's percentage from the total payment
}

input ContractUpdateBenefitsInput {
    benefitId: String                      #The benefit package id, example = "417dd1c0-a29b-4a19-a83f-d57e60bff68f"
    status: ContractBenefitStatus
    params: [BenefitParamInput]
    startOn: DateTime
    endOn: DateTime
    dependentCount: Int
    discountTermPercentage: Float   # discount percentage against an employee insurance
    employerPayPercentage: Float    # employer's percentage from the total payment
    employeePayPercentage: Float    # employee's percentage from the total payment
    isSelfPurchased: Boolean
}

input ContractUpdateBenefitDependentInput {
    contractBenefitDependentId: ID
    benefitId: String
    firstName: String
    lastName: String
    gender: Gender
    dateOfBirth: DateTime
    relationshipToMember: Relationship
    country: CountryCode
    countryStateCode: String
    plan: [BenefitParamInput]
    startOn: DateTime
    endOn: DateTime
    legalData: [ContractBenefitDependentLegalDataInput!]
    healthCard: HealthCardFile
    healthId: String
    status: ContractBenefitDependentStatus
}

input ContractBenefitDependentLegalDataInput {
    key: String
    value: String
}

input ContractBenefitWithPaginationFilters {
    contractIds: [ID!]
    coverageStartDateRange: DateRange
    contractStartDateRange: DateRange
    contractBenefitStatuses: [ContractBenefitStatus!]
    countries: [CountryCode!]
    companyIds: [ID!]
    onboardingStatuses: [ContractOnboardingStatus!]
    contractStatus: [ContractStatus!]
    hasPendingChangeRequests: Boolean
    benefitPackageIds: [String!]
    MonthsRemainingToRenewal: Int @deprecated
    monthsRemainingToMasterPolicyRenewal: Int
}

input ContractBenefitFilters {
    contractId: ID
    country: CountryCode
    companyId: ID
    memberNameContains: String
    onboardingStatus: ContractOnboardingStatus
    contractStatuses: [ContractStatus]
    contractStartDateRange: DateRange
    benefitType: BenefitType
    contractBenefitPlan: BenefitTier
    """@deprecated. Use contractBenefitStatuses instead"""
    contractBenefitStatus: ContractBenefitStatus
    contractBenefitStatuses: [ContractBenefitStatus]
    coverageStartDateRange: DateRange
    hasDependents: Boolean                          # filter by having dependents or not
    dependentsPlan: BenefitTier
}

input BenefitParamInput {
    key: String
    value: String
}

input HealthCardFile {
    fileId: Int     # Either an existing file Id
    file: Upload    # or a new upload
}

input InsuranceStatuses {
    currentStatus: ContractBenefitStatus
    nextStatus: ContractBenefitStatus!
}

input ContractBenefitBannerFilters {
    contractIds: [ID!]
    types: [ContractBenefitBannerType!]
    statuses: [ContractBenefitBannerStatus!]
}

input ContractBenefitBannerInput {
    startDate: DateTime
    endDate: DateTime
    contractIds: [ID]
    type: ContractBenefitBannerType
    visibleToAllActiveContractsWithDependents: Boolean
}

input ContractBenefitChangeRequestInput {
    contractId: ID!
    contractBenefitId: ID
    type: ContractBenefitChangeRequestType!
    changes: ContractBenefitChangeRequestChangeInput
    benefitContractBannerId: ID
}

input ContractBenefitChangeRequestChangeInput {
    dependentDetailsChanges: [ContractBenefitChangeRequestDependentDetailsChangeInput!]
}

input ContractBenefitChangeRequestDependentDetailsChangeInput {
    contractBenefitDependentId: ID!
    type: ContractBenefitChangeRequestType
    changes: [ContractBenefitChangeRequestDependentDetailsInput!]!
    documentChanges: [ChangeRequestDocumentChanges!]
    changeRequestId: ID
}

input ChangeRequestDocumentChanges {
    documents: [Upload]
    key: ContractBenefitDependentDocumentType
    removeDocument: Boolean
}

input ContractBenefitChangeRequestDependentDetailsInput {
    field: String!
    value: String!
}

input ContractBenefitChangeRequestUpdateInput {
    id: ID!
    status: ContractTypeChangeRequestStatus!
    reason: String
}

input ContractBenefitChangeRequestFilters {
    contractIds: [ID!]
    dependentIds: [ID!]
    status: ContractTypeChangeRequestStatus @deprecated(reason: "use statuses")
    statuses: [ContractTypeChangeRequestStatus!]
    changeRequestType: [ContractBenefitChangeRequestType!]
}

input MigrateBenefitPartnerInput {
    contractId: ID!
    newPartnerName: String!
    startDate: DateTime
    endDate: DateTime
    membershipNo: String
}

input RenewInsuranceInput {
    benefitPartnerCountryId: ID!
    partnerName: String!
    startDate: DateTime
    endDate: DateTime
}

# --------------------------------------
# Query types....
# --------------------------------------

type ContractBenefit {
    id: ID
    benefit: Benefit
    contract: Contract
    dependents(dependentPlan: BenefitTier): [ContractBenefitDependent]      # to filter dependents by their respective tier
    status: ContractBenefitStatus
    params: [ContractBenefitParam]
    startOn: DateTime
    endOn : DateTime
    dependentCount: Int
    discountTermPercentage: Float   # discount percentage against an employee insurance
    employerPayPercentage: Float    # employer's percentage from the total payment
    employeePayPercentage: Float    # employee's percentage from the total payment
    benefitEndDate : Date @deprecated # Renamed to endOn and updated Type
    billAmount : Float
    externalPolicyId: String
    externalPolicyFileUrl: String
    benefitPackageCost: BenefitPackageCost
    insurancePremium: InsurancePremium
    files: [ContractBenefitDocument]
    changeRequests(statuses: [ContractTypeChangeRequestStatus!]): [ContractBenefitChangeRequestResponse]
    pendingChangeRequestsCount: Int
    dependentsModificationExpiryDate: DateTime
    isSelfPurchased: Boolean
}

type ContractBenefitDocument {
    key: ContractBenefitDocumentType
    documents: [FileLink] @deprecated(reason: "Use `files` instead")
    files: [Document!]
}

type InsurancePremium {
    benefitType: BenefitType
    billingDuration: BenefitPartnerBillingFrequency

    employerPayPercentage: Float
    employeePayPercentage: Float
    employerPayAmount: Float  # totalPremium * employerPayPercentage
    employeePayAmount: Float  # totalPremium * employeePayPercentage

    self: InsuranceIndividualPremium
    dependents: [InsuranceIndividualPremium]

    totalPlatformFee: Float
    totalPremium: Float  # self + dependent * n + totalPlatformFee
    localCurrency: CurrencyCode
    totalPlatformFeeInLocalCurrency: Float
    totalPremiumInLocalCurrency: Float
    totalAnnualPlatformFeeInLocalCurrency: Float
    totalAnnualPremiumInLocalCurrency: Float
    totalAnnualPlatformFeeInUsd: Float
    totalAnnualPremiumInUsd: Float

    discountedPlatformFee: Float
    discountedTotalPremium: Float
}

type InsuranceIndividualPremium {
    id: ID     # contract benefit id or dependent contract benefit id
    firstName: String
    lastName: String

    startOn: Date
    endOn: Date
    billingPeriodInMonths: Int  # min((endOn - startOn), 12)

    benefitPackageCost: BenefitPackageCost

    subTotalAmount: Float  # without platformFee

    platformFee: Float
    subTotalPlatformFee: Float
    localCurrency: CurrencyCode
    platformFeeInLocalCurrency: Float
    subTotalPlatformFeeInLocalCurrency: Float
    subTotalAmountInLocalCurrency: Float
    annualSubTotalAmountInLocalCurrency: Float
    annualSubTotalPlatformFeeInLocalCurrency: Float
    annualPlatformFeeInUsd: Float
    annualSubTotalAmountInUsd: Float
}

type ContractBenefitDependent {
    id: ID
    contractBenefit: ContractBenefit
    benefit: Benefit                                                # Dependent's own benefit package
    firstName: String
    lastName: String
    gender: Gender
    dateOfBirth: DateTime
    relationshipToMember: Relationship
    country: CountryCode
    countryStateCode: String
    documents: [ContractBenefitDependentDocument]
    addingWorkflow: ContractBenefitAddingDependentWorkflow
    startOn: DateTime
    endOn : DateTime
    legalData: [ContractBenefitDependentLegalData]
    externalPolicyId: String
    externalPolicyFileUrl: String
    benefitPackageCost: BenefitPackageCost
    status: ContractBenefitDependentStatus
    changeRequests(statuses: [ContractTypeChangeRequestStatus!]): [ContractBenefitChangeRequestResponse]
    changeRequestList(filters: ContractBenefitChangeRequestFilters): [ContractBenefitChangeRequestResponse]
}

type ContractBenefitDependentLegalData {
    key: String
    value: String
}

type ContractBenefitAddingDependentWorkflow {
    currentStep: ContractBenefitAddingDependentWorkflowStep
    steps: [ContractBenefitAddingDependentWorkflowStep]
}

type ContractBenefitParam {
    key: String
    value: String
}

type ContractBenefitDependentDocument {
    key: ContractBenefitDependentDocumentType
    documents: [FileLink] @deprecated(reason: "Use `files` instead")
    files: [Document!]
}

type ContractBenefitsResult {
    data: [ContractBenefit!]
    pageResult: PageResult
    countResult: ContractBenefitsCountResult
}

type ContractBenefitsCountResult {
    countriesCount: Int
    employeesCount: Int
}

type InsuranceStatusResponse {
    contractId: ID
    contractBenefitId: ID
    status: ContractBenefitStatus!
    response: TaskResponse
}

type ContractBenefitBanner {
    id: ID!
    startDate: DateTime
    endDate: DateTime
    contractId: ID!
    type: ContractBenefitBannerType
    status: ContractBenefitBannerStatus!
    createdBy: ID
    createdOn: DateTime
    updatedOn: DateTime
}

type ContractBenefitChangeRequestResponse {
    id: ID!
    contractId: ID!
    type: ContractBenefitChangeRequestType!
    changes: ContractBenefitChangeRequestChange!
    status: ContractTypeChangeRequestStatus!
    createdBy: ID
    createdOn: DateTime
    updatedOn: DateTime
    reason: String
    changeRequestData: ChangeRequestData
}

type ChangeRequestData {
    dependentId: ID
    firstName: String
    lastName: String
    gender: Gender
    dateOfBirth: DateTime
    relationshipToMember: Relationship
    country: CountryCode
    documents: [ContractBenefitDependentDocument]
    legalData: [ContractBenefitDependentLegalData]
    status: ContractBenefitDependentStatus
}

type ContractBenefitChangeRequestChange {
    dependentDetailsChanges: ContractBenefitChangeRequestDependentDetailsChange
    dependentChanges: [ContractBenefitChangeRequestDependentDetailsChange] @deprecated
}

type ContractBenefitChangeRequestDependentDetailsChange {
    contractBenefitDependentId: ID!
    changes: [ContractBenefitChangeRequestDependentDetails]
    fieldChanges: [ChangeRequestDependentChangeDetails]
}

type ChangeRequestDependentChangeDetails {
    field: String!
    oldValue: String
    newValue: String
    oldDocument: [ContractBenefitDependentDocument]
    newDocument: [ContractBenefitDependentDocument]
}

type ContractBenefitChangeRequestDependentDetails {
    field: String!
    oldValue: String!
    newValue: String!
}

type MigrateBenefitPartnerResponse {
    contractsSuccessfullyMigrated: [ID]
    contractsFailed: [ContractsMigrationFailedResponse]
}

type ContractsMigrationFailedResponse {
    contractId: ID
    errorMessage: String
}

# --------------------------------------
# Enums
# --------------------------------------

enum ContractBenefitAddingDependentWorkflowStep {
    CAPTURE_BASIC_DETAILS
    UPLOAD_DOCUMENTS
    CAPTURE_PLAN_DETAILS
    CONFIRM
}

enum ContractBenefitDependentDocumentType {
    PROOF_OF_IDENTITY
    PROOF_OF_ADDRESS
    PROOF_OF_RELATIONSHIP
    HEALTH_CARD
    OTHER
}

enum ContractBenefitBannerType {
    DEPENDENT_DOB_CHANGE
}

enum ContractBenefitBannerStatus {
    SCHEDULED
    ACTIVE
    APPROVED_BY_MEMBER
    PENDING_APPROVAL
    APPROVED_BY_OPS
    REJECTED_BY_OPS
    PARTIAL_REVIEW_BY_OPS
    NO_ACTION_FROM_MEMBER
}

enum ContractBenefitChangeRequestType {
    CHANGE_DEPENDENT_DETAILS
    ADD_DEPENDENT
    MODIFY_DEPENDENT_DETAILS
    DELETE_DEPENDENT
}

enum ContractTypeChangeRequestStatus {
    PENDING
    APPROVED
    REJECTED
    DRAFT
    PENDING_WITH_PROVIDER
}
