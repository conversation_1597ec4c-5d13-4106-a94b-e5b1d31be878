extend type Query {
    benefit(id: String!): Benefit                                                        # Benefit ID is a UUID
    benefits(
        """Don't use this parameter. Use filters instead"""
        country: CountryCode

        """Don't use this parameter. Use filters instead"""
        countryStateCode: String

        """Don't use this parameter. Use filters instead"""
        type: BenefitType

        filters: BenefitFilters
    ): [Benefit]
    benefitActivityLogs(filters: ActivityDocumentFilter!) : [ActivityDocument]
    benefitPackageCost(id: String!): BenefitPackageCost
    benefitPackageCosts(filters: BenefitPackageCostFilters): [BenefitPackageCost]

    insuranceRelation:[InsuranceRelation]
    insuranceCoverageGroups(input: BenefitPartnerCountryFilters!): [InsuranceCoverageGroup]
    insuranceTier: [InsuranceTier]

    companyBenefits(input: CompanyCountryBenefitInput): CompanyCountryBenefitResponse @authorize(company: ["view.company.insurance"])
}

extend type Mutation {
    benefitCreate(input: [BenefitInput!]!): [Benefit]
    benefitUpdate(id: String!, input: BenefitInput!): Benefit
    benefitDelete(id: String!): Benefit
    benefitSetTierPackage(id: String!, tier: BenefitTierInput!): Benefit                          #Set default package for a tier
    benefitUpdateDocument(id: String!, key: BenefitDocumentType!, documents: [Upload!]!): Benefit
    benefitPackageMappingCSV(file: Upload!): TaskResponse

    benefitActivityAddOperation(file: Upload!): TaskResponse
    benefitActivityAddOperationWrapper(file: Upload!): BenefitPartnerCountryResponse
    benefitActivityUpdateOperation(file: Upload!): TaskResponse @deprecated
    benefitActivityUpdateOperationWithId(id: String!, file: Upload!): TaskResponse

    benefitActivityUpdateOperations(id: ID!, file: Upload!): TaskResponse

    benefitPartnerUpdateDocuments(id: ID!, data: [BenefitDocumentInput!]!): [BenefitDocument]

    createInsuranceRelation(input: InsuranceRelationInput!): InsuranceRelation
    createInsuranceCoverageGroup(input: [InsuranceCoverageInput]!): InsuranceCoverageGroup
    createInsuranceTier(input: InsuranceTierInput!): InsuranceTier

    updateCompanyBenefit(input: UpdateCompanyCountryBenefitInput): TaskResponse @authorize(company: ["update.company.insurance"])
    deleteCompanyBenefits(companyBenefitIds: [ID!]): TaskResponse @authorize(company: ["use.operations.benefits"])

    captureNewInsuranceRequest(input: NewInsuranceRequestInput): TaskResponse

    benefitDataCorrectionBulkUpload(file: Upload!): BenefitDataCorrectionResponse
}

type Benefit {
    id: String!
    type: BenefitType                                             # Only one benefit type for now - INSURANCE
    packageName: String                                           # Name of the package
    description: String                                           # Description/summary about the benefit package
    factSheetUrl: String
    provider: BenefitPartnerCountry
    country: CountryCode
    countryStateCode: String
    location: String                                              # Some benefits can be changed based on the location
    currency: CurrencyCode
    cost: Float                                                   # Total package cost
    costInLocalCurrency: Float                                 # Premium in the local currency
    costingType: BenefitCostingType
    frequency: RateFrequency
    features: [BenefitFeatures]
    compareBenefits(benefitId: String): BenefitComparison         # For now we don't have to pass the ID, this will return the comparison for a country
    memberOnboardingKit(benefitId: String): BenefitMemberOnboardingKit
    benefitStatus: BenefitStatus
    createdOn: DateTime
    updatedOn: DateTime
    startDate: DateTime
    endDate: DateTime
    createdBy: ID
    updatedBy: ID
    benefitDocuments: [BenefitDocument]
    packageTier: BenefitPackageTier @deprecated
    isRecommended: Boolean
    billFrequency : RateFrequency
    contractType: ContractType
    dependentMaxCount: Int
    showOpsOnly: Boolean
    tier: InsuranceTier
    visibleTo: String
    recommendToMember: Boolean
}

type CompanyCountryBenefitResponse {
    company: Company! @deprecated
    companyId: ID!
    benefits: [CountryBenefitResponse]
}

type CountryBenefitResponse {
    countryCode: CountryCode
    insuranceDetails: [InsuranceDetail]
}

type InsuranceDetail {
    memberType: ContractType
    benefitPartnerCountryId: ID
    benefitPackageId: String
    benefitPartnerCountry: BenefitPartnerCountry @authorize(company: ["view.company.insurance"])
    benefitPackage: Benefit @authorize(company: ["view.company.insurance"])
    employerContributionPercentage: Int
    employeeContributionPercentage: Int
    enrolledMembersCount: Int @authorize(company: ["view.company.insurance"])
    createdAt: DateTime
    updatedAt: DateTime
    benefitStatus: CompanyCountryBenefitStatus
    dependentCount: Int
    billingCurrency: CurrencyCode
    totalAmount: Float
    employerContributionAmount: Float
    employeeContributionAmount: Float
}

type BenefitDataCorrectionResponse {
    numSuccessfulRows: Int
    numFailedRows: Int
    failedRows: [BenefitDataCorrectionFailedRow]
}

type BenefitDataCorrectionFailedRow {
    rowNumber: Int
    contractId: Int
    errorMessage: String
    originalRowData: String
}

input NewInsuranceRequestInput {
    countryCode: CountryCode!
}

input UpdateCompanyCountryBenefitInput {
    companyId: ID!
    countryCode: CountryCode!
    memberType: ContractType!
    benefitStatus: CompanyCountryBenefitStatus!
    benefitPackageId: String!
    benefitPartnerCountryId: ID!
    employerContributionPercentage: Int
    employeeContributionPercentage: Int @deprecated
    dependentCount: Int
    billingCurrency: CurrencyCode
}

input CompanyCountryBenefitInput {
    companyId: ID!
    countryCode: CountryCode
    memberType: ContractType
    benefitStatus: CompanyCountryBenefitStatus!
}

input BenefitFilters {
    benefitId: String
    providerId: ID
    type: BenefitType @deprecated               # moved to partner schema: BenefitPartnerCountryFilters.insuranceType
    tier: BenefitTier @deprecated
    status: BenefitStatus
    isDefault: Boolean @deprecated
    country: CountryCode @deprecated            # moved to partner schema: BenefitPartnerCountryFilters.countries
    countryStateCode: String @deprecated
    packageName: String @deprecated
    contractType: ContractType @deprecated      # moved to partner schema: BenefitPartnerCountryFilters.memberType
    showOpsOnly: Boolean
}

input BenefitInput {
    type: BenefitType
    packageName: String
    description: String
    factSheetUrl: String
    providerId: ID
    country: CountryCode
    countryStateCode: String
    location: String
    currency: CurrencyCode
    cost: Float
    frequency: RateFrequency
    features: [BenefitFeaturesInput]
    benefitStatus: BenefitStatus
    startDate: DateTime
    endDate: DateTime
    packageTier: BenefitTierInput
    costingType: BenefitCostingType
    contractType: ContractType
}

input BenefitFeaturesInput{
    key: String
    label: String
    value: String
}

input BenefitTierInput{
    tier: BenefitTier
    isDefault: Boolean
}

type BenefitFeatures {
    key: String
    label: String
    value: String
}

enum CompanyCountryBenefitStatus {
    DRAFT
    ACTIVE
    EXPIRED
}

enum BenefitType {
    INSURANCE
    FAMILY_INSURANCE
}

enum BenefitTier {
    BRONZE
    SILVER
    GOLD
    GOLD_PLUS
}

enum BenefitStatus{
   ACTIVE
   INACTIVE
   DELETED
}

enum BenefitDocumentType{
   FACT_SHEET
   INSURANCE_DOCUMENT
   INSURANCE_COMPARISON_DOCUMENT
}

enum BenefitCostingType{
    FIXED
    AGE_BASED
}

type BenefitPackageTier{
    tier: BenefitTier
    isDefault: Boolean
}

type BenefitDocument {
    benefitDocumentType: BenefitDocumentType
    identifier: String
    documents: [FileLink] @deprecated(reason: "Use `files` instead")
    files: [Document!]
}

type BenefitComparison {
    benefits: [Benefit]
    comparisonFileUrl: String @deprecated(reason: "use benefits.benefitDocuments instead")
    comparisonFile: BenefitDocument
}

type BenefitMemberOnboardingKit {
    benefits: [Benefit]
    memberOnboardingKitFile: BenefitDocument
}

type ActivityDocument {
    partnerCountryId: ID
    file: FileLink @deprecated(reason: "Use `document` instead")
    document: Document
    opsUserEmail: String
    status: ActivityDocumentStatus
    type: ActivityType
    errorMessages: Object
}

input ActivityDocumentFilter {
    partnerCountryId: String
}

enum ActivityDocumentStatus {
    SUCCESS
    FAILED
}

enum ActivityType {
    PARTNER_COUNTRY_ADD
    PARTNER_COUNTRY_UPDATE
}

scalar Object

type BenefitPackageCost {
    id: String!
    benefitPackageId: String
    ageLowerRange: Int
    ageHigherRange: Int
    cost: Float
    premium: Float
    premiumFrequency: BenefitPremiumFrequency
    premiumCurrency: CurrencyCode
    status: BenefitPackageCostStatus
    memberPremiumInLocalCurrency: Float
    memberPremiumInUSD: Float
    memberPremiumFrequency: BenefitPremiumFrequency
    gender: GenderForBenefitPackageCost
}

input BenefitPackageCostFilters {
    benefitPackageCostId: String
    benefitPackageId: String
    status: BenefitPackageCostStatus
}

enum BenefitPremiumFrequency {
    MONTHLY
    ANNUALLY
}

enum BenefitPackageCostStatus {
    ACTIVE
    INACTIVE
}

enum GenderForBenefitPackageCost {
    MALE
    FEMALE
    ALL
}

type BenefitPartnerCountryResponse {
    benefitPartnerCountry: BenefitPartnerCountry
    response: TaskResponse
}

input BenefitDocumentInput {
    benefitDocumentType: BenefitDocumentType
    identifier: String
    documents: [Upload]
}

input InsuranceRelationInput {
    familyMember: String!
}

type InsuranceRelation {
    id: ID
    familyMember: String
}

input InsuranceCoverageInput {
    relationId: ID
    maxCount: Int!
}

type InsuranceCoverage {
    relation: InsuranceRelation
    maxCount: Int!
}

type InsuranceCoverageGroup {
    id: ID
    name: String
    group: [InsuranceCoverage]
}

input InsuranceTierInput {
    name: String!
}

type InsuranceTier {
    id: ID
    name: String
}

enum InsuranceDiscountComponent {
    PLATFORM_FEE,
    PREMIUM
}

enum InsuranceDiscountType {
    PERCENTAGE_DISCOUNT,
    DISCOUNT_ON_VALUE,
    DISCOUNTED_VALUE
}

enum InsuranceValidityType {
    LIMITED_PERIOD,
    LIFETIME
}

enum InsuranceDiscountStatus {
    ACTIVE,
    EXPIRED,
    INACTIVE,
}
