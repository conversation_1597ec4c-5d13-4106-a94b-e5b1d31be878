extend type Query {
    bgvContracts(contractIds: [ID!]): [BgvContract]
}

extend type Mutation {
    updateBgvStatus(input: [UpdateBackgroundVerificationStatusInput!]): TaskResponse
}

input UpdateBackgroundVerificationStatusInput {
    contractId: ID!
    nextStatus: BackgroundVerificationStatus
}
type BgvContract {
    id: ID!
    contractId: ID!
    createdAt: DateTime
    updatedAt: DateTime
    status: BackgroundVerificationStatus!
    createdBy: ID
    updatedBy: ID
    statusUpdatedAt: DateTime
}
enum BgvCompanyConfigurationStatus {
    ACTIVE
    INACTIVE
}
