extend type Mutation {
    triggerExpenseReminderEmailProcess: TaskResponse
    triggerTimeoffUpdateProcess: TaskResponse

    # Trigger upcoming monthly invoice email sending process.
    # Can call by OPS only.
    triggerUpcomingMonthlyInvoiceEmailProcess(companyIds: [ID!], invoiceMonth: MonthYearInput): TaskResponse

    # Trigger end contract for freelancer on LWD+1 day.
    # Can call by OPS only.
    triggerEndContractForFreelancerAfterLWD: TaskResponse

    # Trigger probation end reminder email sending process.
    # Can call by OPS only.
    triggerProbationEndReminderEmailProcess(companyIds: [ID!], sendingDate: Date): TaskResponse

    # Trigger unpaid bundle reminder email sending process
    # Can call by OPS only
    triggerUnpaidPaymentBundleReminderEmailProcess: TaskResponse

    # Trigger change request delayed email sending process
    triggerChangeRequestDelayedEmailProcess: TaskResponse

    # For OpsOffboardingContractsReminder emails
    triggerOffboardingReminderEmailToOps: TaskResponse

    # For contract benefit change status to ENDED
    triggerContractBenefitSetEndedStatus(endOn: Date): TaskResponse

}
