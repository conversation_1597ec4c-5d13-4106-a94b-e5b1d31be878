extend type Mutation {
    # TODO: the data type specific mutations can be replaced by a single mutation in couple of ways.
    # One way is using a custom directive until GraphQL support this (or forever) https://github.com/graphql/graphql-spec/pull/825
    countryUpdateMemberDataText(memberId: ID!, textFieldInputs: [TextFieldInput]!) : Member @deprecated(reason: "Use `memberUpdateDataText` instead") # update country specific text data i.e. member passport, religion
    countryUpdateMemberDataDate(memberId: ID!, dateFieldInputs: [DateFieldInput]!) : Member @deprecated(reason: "Use `memberUpdateDataDate` instead") # update country specific date data i.e. issued date

}
