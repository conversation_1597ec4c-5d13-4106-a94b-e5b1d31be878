extend type Mutation {
    currencyUploadExchangeRates(effectiveDate: Date, file: Upload!): TaskResponse
    currencyRefreshFXRates: TaskResponse
    companySlabFXRateUpload(companySlabFXRates: Upload!) : TaskResponse
}

extend type Query {
    currency(code: CurrencyCode): Currency @authorize(company: ["view.company.currency"], operations: ["view.operations.currency"], member: ["view.member.currency"])
    convertCurrency(params: CurrencyConversionParams!): Float @deprecated @authorize(company: ["view.company.currency"], operations: ["view.operations.currency"], member: ["view.member.currency"])
    bulkConvertCurrency(params: BulkCurrencyConversionParams!): BulkCurrencyConversionResponse @authorize(company: ["view.company.currency"], operations: ["view.operations.currency"], member: ["view.member.currency"])
}

input CurrencyConversionParams {
    conversionId: ID
    from: CurrencyCode!
    to: CurrencyCode!
    amount: Float!
    source: CurrencyConversionSource
    forDate: Date
}

input BulkCurrencyConversionParams {
    conversions: [CurrencyConversionParams!]!
}

type Currency  @key(fields: "code") {
    code: CurrencyCode
    name: String
    convert(to: CurrencyCode, amount: Float, source: CurrencyConversionSource, forDate: Date): Float
}

type CurrencyConversionSuccess {
    conversionId: ID
    from: CurrencyCode!
    to: CurrencyCode!
    amount: Float!
    convertedAmount: Float!
}

type CurrencyConversionError {
    conversionId: ID
    errorMessage: String!
}

union CurrencyConversionResult = CurrencyConversionSuccess | CurrencyConversionError

type BulkCurrencyConversionResponse {
    converts: [CurrencyConversionResult!]!
}

enum CurrencyConversionSource {
    INTERNALLY_FIXED    # The rates decided by internal finance during a given period.
    ON_DEMAND           # Using a public FX service to get the rate. This is default.
    FALLBACK            # Fallback to the ON_DEMAND service if INTERNALLY_FIXED rate is not found.
}
