extend type Query {
    benefitPartnerCountry(id: ID): BenefitPartnerCountry
    benefitPartnerCountries(input: BenefitPartnerCountryFilters): [BenefitPartnerCountry]
    benefitPartnerCountriesWithPagination(filters: BenefitPartnerCountryFilters, pageRequest: PageRequest): BenefitPartnerCountriesResult

    benefitPartnerDataDownload(input: BenefitPartnerDataDownloadInput!): DocumentReadable!
    benefitPartnerCountryCheckExpirationStatus(id: ID!): Boolean
    benefitPartnerAddOperationTemplate(input: BenefitPartnerDataDownloadInput!): DocumentReadable! @deprecated
    benefitPartnerAddOperationActivityTemplate: DocumentReadable!
    benefitPartnerEditOperationSheetDownload(input: BenefitPartnerDataDownloadInput!): DocumentReadable!
}

extend type Mutation {
    benefitPartnerMappingBackfillCSV(file: Upload!): TaskResponse

    benefitPartnerCountryDelete(id: ID!): BenefitPartnerCountry
}

# Later
type BenefitPartnerCountry implements PartnerCountry {
    id: ID
    capability: PartnerCapability #BENEFIT
    partner: Partner
    country: CountryCode
    entity: LegalEntity
    operator: [PartnerUser]
    emergencyPointOfContact: Person
    memberType: ContractType
    createdOn: DateTime
    updatedOn: DateTime
    createdBy: ID
    updatedBy: ID
    refundPolicy: Boolean
    billingCurrency: CurrencyCode    # Currency in which Multiplier pays to partner
    billingDuration: BenefitPartnerBillingFrequency
    masterPolicyStartDate: DateTime
    masterPolicyEndDate: DateTime
    status: BenefitPartnerStatus
    backDatingDays: Int
    dependentsCutoffDays: Int
    partnerPortalWeblink: String
    enrollmentProcess: BenefitPartnerEnrollmentProcess
    terminationType: BenefitTerminationType
    erSplitApplicable: Boolean
    memberOnboardingKit: FileLink @deprecated(reason: "Use memberOnboardingKitUrl instead.")
    memberOnboardingKitUrl: String @deprecated
    ageBandApplicable: Boolean
    platformFeeApplicable: Boolean
    platformFee: Int
    policyRules: BenefitPolicyRules
    insuranceType: BenefitInsuranceType
    dependentsAllowed: Boolean
    metaData: BenefitMetaData
    partnerName: String
    partnerDetails: BenefitPartnerData
    brokerData: BenefitBrokerData
    canMarkAsExpired: Boolean
    documents: [BenefitDocument]
    benefitsDomain: BenefitsDomain
    minimumErSplitPercentage: Float
    isInsuranceMandatory: Boolean
    coverageGroup: InsuranceCoverageGroup
    benefits: [Benefit]
    isRecommended: Boolean
    showOpsOnly: Boolean
    visibleTo: String
    insuranceEntity: String
    localCurrency: CurrencyCode
    refundToMember: Boolean
    platformFeeForMemberInLocalCurrency: Int
    isOptingOutAllowed: Boolean
}

type BenefitPolicyRules {
    married: MarriedPolicyRules
    unmarried: UnmarriedPolicyRules
}

type MarriedPolicyRules {
    spouse: Boolean,
    maxChildrenAllowed: Int
    maxParentsAllowed: Int
    maxAllowedTotal: Int
}

type UnmarriedPolicyRules {
    maxParentsAllowed: Int
    maxAllowedTotal: Int
}

type BenefitMetaData { 
    pocName: String                  # emergency-point-of-contact: "john"      
    emails: [EmailAddress]           # email: [ { type : primary, value: abc@g.c }, { type: poc , value: abc@f.c } ] 
    phoneNos: [PhoneNumber]          # phone: [ { type : primary, value: +9_6000... }, { type: poc , value: +94_67777... } ] 
    companyLogo: DocumentReadable
    brokerData: BenefitBrokerData
}

type BenefitBrokerData {
    emails: [EmailAddress]
    phoneNos: [PhoneNumber]
    companyLogo: DocumentReadable  @deprecated(reason: "Use companyLogoUrl instead.")
    companyLogoUrl: String
    entity: LegalEntity
    scope: BenefitScope
    name: String
}

type BenefitPartnerData {
    emails: [EmailAddress]
    phoneNos: [PhoneNumber]
    companyLogo: String
    scope: BenefitScope
    address: Address
    bankAccount: BankAccount
}

type EmergencyPointOfContact implements Person @key(fields: "id") {
    # only supports firstName, lastName and emails but need to implement Person
    id: ID
    persona: Persona
    firstName: String
    lastName: String
    userId: String
    emails: [EmailAddress]
    phoneNos: [PhoneNumber]
}


type PartnerUser @key(fields: "id") @extends  {
    id: ID @external
}

type BenefitPartnerCountriesResult {
    data: [BenefitPartnerCountry!]
    pageResult: PageResult
}

enum PartnerUserType {
    PAYROLL_PRIMARY
    PAYROLL_SECONDARY
    PAYMENTS
}

input BenefitPartnerCountryFilters {
    name: String
    brokerName: String
    countries: [CountryCode]
    memberType: [ContractType]
    masterPolicyStartDateFrom: DateTime
    masterPolicyStartDateTo: DateTime
    masterPolicyEndDateFrom: DateTime
    masterPolicyEndDateTo: DateTime
    billingDuration: [BenefitPartnerBillingFrequency]
    backDatingDaysLower: Int
    backDatingDaysUpper: Int
    dependentsCutoffDaysLower: Int
    dependentsCutoffDaysUpper: Int
    erSplitApplicable: Boolean
    ageBandApplicable: Boolean
    platformFeeApplicable: Boolean
    insuranceType: BenefitInsuranceType
    dependentsAllowed: Boolean
    statuses: [BenefitPartnerStatus]
    coverageGroupId: ID
    monthsRemainingToRenewal: Int
}

input BenefitPartnerDataDownloadInput {
    benefitPartnerCountryIds: [ID]
}

enum PartnerRole {
    PRIMARY_ADMIN
    ADMIN
}

enum PartnerUserStatus {
    CREATED
    INVITED
    ACTIVE
    # DELETED To be done later
}

enum BenefitPartnerBillingFrequency {
    ANNUALLY
    MONTHLY
    REAL_TIME
}

enum BenefitPartnerStatus {
    ACTIVE
    EXPIRED
}

enum BenefitPartnerEnrollmentProcess {
    MANUAL
    ONLINE
}

enum BenefitTerminationType {
    IMMEDIATE
    NON_IMMEDIATE
}

enum BenefitInsuranceType {
    INDIVIDUAL
    FAMILY_INSURANCE
}

enum BenefitScope {
    GLOBAL
    LOCAL
}

enum BenefitsDomain {
    HEALTH_INSURANCE
}
