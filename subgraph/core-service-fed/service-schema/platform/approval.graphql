extend type Query {
    getAllApprovers(itemId: ID!, category: ApprovalCategory!): [Approver] # Can make them not compulsory later.
}

extend type Mutation {
    approveItem(itemId: ID!, category: ApprovalCategory!, comment: String): ApprovalItem
        @authorize(
            company: ["approvereject.company.item"],
            operations: ["approvereject.operations.item"]
        )
    rejectItem(itemId: ID!, category: ApprovalCategory!, reason: String): Approval<PERSON><PERSON>
        @authorize(
            company: ["approvereject.company.item"],
            operations: ["approvereject.operations.item"]
        )
    forwardItem(itemId: ID!, forwardUserId: ID!, category: ApprovalCategory!, comment: String): ApprovalItem
        @authorize(company: ["forward.company.item"])
}

enum ApprovalItemStatus {
    APPROVAL_IN_PROGRESS
    APPROVED
    REJECTED
    ACTION_TAKEN # used for other approvers, when one approver approves/rejects it.
}

interface ApprovalItem @key(fields: "id") {
    id: ID
    status: ApprovalItemStatus
    approverUser: [Person]
}

type Approver {
    id: ID  # -> This ID is not the DB ID as the data is composed differently in DB. This ID is the person's ID, which is unique
    categoryContracts: [ApproverCategoryContract]
    approverUser: Person
}

type ApproverCategoryContract {
    category: ApprovalCategory
    managedContracts: [Contract]
}

type ExpenseApprovalItem implements ApprovalItem @key(fields: "id") {
    id: ID
    expenseId: ID
    status: ApprovalItemStatus
    approverUser: [Person]
}

type TimeOffApprovalItem implements ApprovalItem @key(fields: "id") {
    id: ID
    timeOffId: ID
    status: ApprovalItemStatus
    approverUser: [Person]
    timeOffItem: TimeOff # seems unused (?), but keeping it for compatibility
}

# unused since mid 2022, when we stopped using the "approval framework" for memberPayable
type MemberPayableApprovalItem implements ApprovalItem @key(fields: "id") {
    id: ID
    status: ApprovalItemStatus
    approverUser: [Person]
    memberPayableId: ID
    memberPayableItem: MemberPayable
}

type LegalDocumentsApprovalItem implements ApprovalItem @key(fields: "id") {
    id: ID
    status: ApprovalItemStatus
    approverUser: [Person]
    legalDocument: LegalDocument
}

type MemberDataApprovalItem implements ApprovalItem @key(fields: "id") {
    id: ID
    status: ApprovalItemStatus
    approverUser: [Person]
    memberDataChangeRequest: MemberChangeRequest
}

# all approvers of a category, used for Contract.categoryApproverInfos
type CategoryApproverInfo {
    category: ApprovalCategory
    approverUsers: [Person]
}

"""
This reflects a record of coredb.platform.item_approver table.<br>
The table lets us know who are the approvers of an item (and their decisions).<br>
Currently used in TimeOff & Expense (extends.graphql)
"""
type ItemApproverInfo {
    status: ApprovalItemStatus
    """currently this is just CompanyUser"""
    approverUser: Person
    createdOn: DateTime
    updatedOn: DateTime
}
