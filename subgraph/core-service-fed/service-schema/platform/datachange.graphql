#TODO: mutations??
extend type Query {
    filteredDataChange(filter: DataChangeFilter, pageRequest: PageRequest): DataChangePageResults
    dataChangeReport(filter: DataChangeFilter): DocumentReadable
}

input DataChangeFilter {
    startDate: DateTime
    endDate: DateTime
    country: CountryCode
    company: String
    memberName: String
    contractID: ID
    contractStatus: ContractStatus
    contractType: ContractType
    eorPartnerId: ID
    isMultiplierEntity: Boolean
    field: [String]  # Used for fetching the data by the sections, expects the array of field names e.g. "FULL_LEGAL_NAME"
    fieldCategory: [String]  # Used for fetching the data by the category e.g. "MEMBER", "CONTRACT"
}

type DataChangePageResults {
    data: [DataChange]
    pageResult: PageResult
}

# The root type for Data Changes Events
type DataChange {
    id: ID
    fieldCategory: String
    field: String
    fieldCategoryObject: FieldCategoryObject
    createdBy: Person
    content: DataChangeContent
    status: DataChangeStatus
    metaData: DataChangeMetaData
    createdOn: DateTime
}

union FieldCategoryObject = Contract | Member
# The data change content's union
union DataChangeContent = DataChangeStringContent | DataChangeDateContent | DataChangeCompensationContent | DataChangeLegalContent

# Basic type for string data changes
type DataChangeStringContent {
    oldValue: String
    newValue: String
}

# Basic type for string array data changes
type DataChangeDateContent {
    oldValue: DateTime
    newValue: DateTime
}

# Type for compensation data changes
type DataChangeCompensationContent {
    oldValue: FixedPayComponent
    newValue: FixedPayComponent
}

# Type for legal data changes
type DataChangeLegalContent {
    oldValue: LegalData
    newValue: LegalData
}

type DataChangeMetaData {
    country: CountryCode
}

# -------------------------------------------------------------
# Reusing types from contract service
interface CompensationPayComponent {
    id: ID
    name: String
    label: String
    amount: Float
    currency: CurrencyCode
    amountType: PayAmountType
    frequency: RateFrequency
    paySchedule: ScheduleTime
    payOn: MonthYear
    isDeletable: Boolean
    condition: String
    paymentFrequency: PayFrequency
    paymentFrequencyDate: [PayFrequencyDate]
    createdOn: DateTime
    updatedOn: DateTime
}

# The date or day the payment happen according to the "pay frequency".
type PayFrequencyDate {
    # When PayFrequency = "MONTHLY" => identifier = "PAYOUT_DATE"
    # When PayFrequency = "SEMIMONTHLY" => identifier = "FIRST_PAYOUT_DATE" or "SECOND_PAYOUT_DATE"
    # When PayFrequency = "BIWEEKLY" or "WEEKLY" => identifier = "PAYOUT_DAY"
    identifier: PayFrequencyDateIdentifier

    # Applicable when PayFrequency = "MONTHLY" or "SEMIMONTHLY"
    dateOfMonth: Int

    # Applicable when PayFrequency = "WEEKLY" or "BIWEEKLY"
    dayOfWeek: DayOfWeek
}

type FixedPayComponent implements CompensationPayComponent {
    id: ID
    name: String
    label: String
    amount: Float
    currency: CurrencyCode
    amountType: PayAmountType
    frequency: RateFrequency
    paySchedule: ScheduleTime
    rateType: RateType
    payOn: MonthYear
    isDeletable: Boolean
    condition: String
    paymentFrequency: PayFrequency
    paymentFrequencyDate: [PayFrequencyDate]
    validFromInclusive: Date
    validToExclusive: Date
    createdOn: DateTime
    updatedOn: DateTime
    instalments: [Instalment!]
}

type Instalment {
    name: String
    label: String
    amount: Float
    payOn: MonthYear
    currency: CurrencyCode
}
# -------------------------------------------------------------
