extend type Query {
    companyChargebackPeriod(companyId: ID!): CompanyChargebackResult @authorize(operations: ["view.operations.company-chargeback"])
}
extend type Mutation {
    updateCompanyChargebackPeriod(request: UpdateCompanyChargebackRequest!): CompanyChargebackResult  @authorize(operations: ["update.operations.company-chargeback"])
}


type CompanyChargebackResult {
    companyId: ID!
    chargebackDays: Int!
}

input UpdateCompanyChargebackRequest {
    companyId: ID!
    chargebackDays: Int!
    description: String
}