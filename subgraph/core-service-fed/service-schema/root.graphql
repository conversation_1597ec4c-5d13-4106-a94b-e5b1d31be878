type Query {
    ok: <PERSON>ole<PERSON>
}

type Mutation {
    ok: <PERSON>olean
}

# User personas that are known by the paltform
enum Persona {
    COMPANY
    MEMBER
    PARTNER
    OPERATIONS
}

# Resources can have multiple addresses and they can choose to label/name..
type Address {
    key : String            # And identifier/label with some possible-uniquness...
    street: String
    line1: String
    line2: String
    city: String
    state: String
    province: String
    country: CountryCode
    zipcode: String
    postalCode: String
}

interface Person {
    id: ID
    persona: Persona
    firstName: String
    lastName: String
    userId: String
    emails: [EmailAddress]
    phoneNos: [PhoneNumber]
}

type LegalEntity {
    legalName: String
    registrationNo: String
    currency: CurrencyCode
    address: Address
    phone: String
    bankAccount: BankAccount
}
