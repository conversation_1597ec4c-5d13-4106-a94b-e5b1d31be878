extend type Mutation {
    initiateAutoDebit(input: AutoDebitInput!): TaskResponse @authorize(operations: ["create.operations.auto-debit"])
    autoDebitManualTrigger(invoiceDueDate: Date!) : TaskResponse @authorize(operations: ["trigger.operations.auto-debit"])
    updateAutoDebitStatus(request: AutoDebitUpdateStatusRequest!) : TaskResponse @authorize(operations: ["update.operations.auto-debit"])
    executeAutoDebitsForToday : TaskResponse @authorize(operations: ["update.operations.auto-debit"])
}

input AutoDebitInput {
    bundleId: ID!
    executionDate: Date
}

enum AutoDebitStatus {
    VOIDED,
    FAILED
}

input AutoDebitUpdateStatusRequest {
    autoDebitId: ID!
    status: AutoDebitStatus!
    reason: String!
}
