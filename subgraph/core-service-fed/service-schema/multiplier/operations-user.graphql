extend type Query {
    operationsUser(id: ID): OperationsUser # if id not present, then inferred from currentUser
    operationsUsers(roles: [OperationsUserRole!]): [OperationsUser]
    operationsDashboard: OperationsDashboard
}

extend type Mutation {
    operationsUserCreate(operationsUser: OperationsUserCreateInput!) : OperationsUser
    operationsUserAcceptInvite: OperationsUser
    operationsUserUpdate(id: ID!, operationsUserChange: OperationsUserUpdateInput!) : OperationsUser
    operationsUserDelete(id: ID!) : TaskResponse
    operationsUserUpdateRoles(id: ID!, roles: [OperationsUserRole!]!): OperationsUser
}

type OperationsDashboard {
    signedMSACount: Int # today
    sentContractCount: Int # today
    offboardingInitiatedContractCount: Int # today
    inviteStateContractCount: Int # 2 weeks
    createdStateContractCount: Int # 2 weeks
    willEndContractCount: Int # 2 weeks
    csmAllocation: [Allocation]
    csmUnassigned: Int # number of unassigned customer to csm
    aeAllocation: [Allocation]
    aeUnassigned: Int # number of unassigned customer to ae
}

type Allocation {
    user: OperationsUser
    count: Int
}

type OperationsUser implements Person @key(fields: "id") {
    id: ID # Person has id type Int
    persona: Persona # OPERATIONS
    firstName: String
    lastName: String
    userId: String
    emails: [EmailAddress]
    phoneNos: [PhoneNumber]
    calendlyLink: String
    displayPicture: DocumentReadable @deprecated(reason: "Use `profilePicture` instead")
    profilePicture: Document

    roles: [OperationsUserRole]
    onboardingContracts(status: ContractStatus): [Contract!]
}

enum OperationsUserRole {
    ADMIN
    ONBOARDING_LEAD
    ONBOARDING_SPECIALIST
    CUSTOMER_SUCCESS_LEAD
    CUSTOMER_SUCCESS_SPECIALIST
    SALES_OPS
    ACCOUNT_EXECUTIVE
    ACCOUNT_MANAGER
    FINANCE_OPS_SPECIALIST
    CONTRACT_MANAGER
    PAYROLL_SPECIALIST
    PAYROLL_LEAD
    OFFBOARDING_SPECIALIST
    DEVELOPER_ADMIN
    INSURANCE_OPS
    PAYMENT_OPERATIONS
    PRODUCT_OPERATIONS
    PARTNER_ADMIN
    PARTNER_SUPER_ADMIN
    RISK_ASSESSMENT_ADMIN
    OPERATIONS_ADMIN
    INTERNAL_PAYROLL_SERVICE
    EMPLOYMENT_CONTRACT_OPERATOR
    COMPLIANCE_EDITOR
    COMPLIANCE_APPROVER
}

input OperationsUserCreateInput {
    firstName: String
    lastName: String
    email: String       # Primary
    phoneNo: String     # Primary
    isTest: Boolean
    roles: [OperationsUserRole]
    calendlyLink: String
    displayPicture: Upload
}

input OperationsUserUpdateInput {
    firstName: String
    lastName: String
    phoneNo: String     # Primary
    isTest: Boolean
    calendlyLink: String
    displayPicture: Upload
}
