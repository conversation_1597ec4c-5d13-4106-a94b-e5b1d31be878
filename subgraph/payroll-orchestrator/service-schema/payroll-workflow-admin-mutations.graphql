extend type Mutation {
    createPayrollWorkflow(value: CreatePayrollWorkflowRequest!): PayrollWorkflowResponse! @authorize(operations: ["create.operations.payroll-cycle"])
    terminatePayrollWorkflow(value: TerminatePayrollWorkflowRequest!): PayrollWorkflowResponse! @authorize(operations: ["update.operations.payroll-cycle.status"])
    updatePayrollWorkflow(value: UpdatePayrollWorkflowRequest!): PayrollWorkflowResponse! @authorize(operations: ["update.operations.payroll-cycle.status"])
}

input CreatePayrollWorkflowRequest {
    untilDate: Date!
    configIds: [ID!]!
}

input TerminatePayrollWorkflowRequest {
    payrollCycleIds: [ID!]!
}

input UpdatePayrollWorkflowRequest {
    action: Action!
    payrollCycleIds: [ID!]!
}

type PayrollWorkflowResponse {
    payrollCycles: [PayrollCycle!]!
}
