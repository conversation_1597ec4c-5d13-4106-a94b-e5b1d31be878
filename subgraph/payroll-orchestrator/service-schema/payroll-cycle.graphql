extend type Mutation {
    createOffCycle(value: CreateOffCycleInput!): PayrollCycle @authorize(operations: ["update.operations.payroll-cycle"])
    updatePayrollCycle(value: UpdatePayrollCycleInput!): PayrollCycle @authorize(operations: ["update.operations.payroll-cycle"])
}

input CreateOffCycleInput {
    configId: ID!
    startDate: Date!
    endDate: Date!
    payDate: Date!
}

input UpdatePayrollCycleInput {
    payrollCycleId: ID!
    payrollCycleDates: PayrollCycleDates
}

input PayrollCycleDates {
    cutOffTo: Date,
    payDate: Date,
    approvePayrollReportDeadline: Date,
    submitPayrollInputDeadline: Date,
    approvePayrollInputDeadline: Date,
    payrollProcessingDeadline: Date,
    payrollCompleteDeadline: Date,
}
