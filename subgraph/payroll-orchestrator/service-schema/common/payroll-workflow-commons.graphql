enum Action {
    # Platform action
    PLATFORM_INIT
    PLATFORM_LOCK_INPUT
    PLATFORM_VALIDATE_INPUT
    PLATFORM_COMPLETE_INPUT
    PLATFORM_PROCESS_INPUT
    PLATFORM_VALIDATE_OUTPUT
    PLATFORM_RECONCILE_OUTPUT
    PLATFORM_VALIDATE_PAYSLIPS
    PLATFORM_GENERATE_INPUT_FILE

    #Ops action
    OPS_APPROVE_INPUT
    OPS_PROCESS_INPUT
    OPS_RECONCILE_OUTPUT
    OPS_UPLOAD_PAYSLIPS
    OPS_APPROVE_PAYSLIPS
    OPS_APPROVE_OUTPUT
    OPS_CLOSE_WORKFLOW

    #Customer action
    CUSTOMER_APPROVE_INPUT
    CUSTOMER_RECONCILE_OUTPUT
    CUSTOMER_APPROVE_OUTPUT

    #Partner action
    PARTNER_UPLOAD_OUTPUT
    PARTNER_SUBMIT_OUTPUT

    #Admin action
    ADMIN_TERMINATE
}

enum PayrollWorkflowState {
    CREATED
    INPUT_IN_PROGRESS
    INPUT_LOCKED
    INPUT_VALIDATED
    INPUT_APPROVED
    INPUT_COMPLETE
    PARTNER_INPUT_FILE_GENERATED
    SENT_TO_PARTNER
    OUTPUT_UPLOADED
    OUTPUT_VALIDATED
    OUTPUT_RECONCILED
    OUTPUT_SUBMITTED
    OPS_RECONCILIATION
    PAYSLIPS_UPLOADED
    PAYSLIPS_VALIDATED
    PAYSLIPS_APPROVED
    CUSTOMER_RECONCILIATION
    OUTPUT_APPROVED
    COMPLETED
    ABORTED
    FAILED
}

enum PayrollWorkflowEntityType {
    EOR
    EOR_PARTNER
    PEO
    GLOBAL_PAYROLL
    EOR_MULTIPLIER_PAYROLL
    EOR_PARTNER_PAYROLL
    CONTRACTOR_PAYROLL
}
