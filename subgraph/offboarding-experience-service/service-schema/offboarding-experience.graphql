extend type Query {
    offboardingExperienceState(contractId: ID!): OffboardingExperienceState!
        @authorize(member: ["view.member.contract.offboarding"], company: ["view.company.contract.offboarding"], operations: ["view.operations.contract.offboarding"])
}

extend type Mutation {
    offboardingExperienceInitiatialise(contractId: ID!): OffboardingExperienceState! @deprecated(reason: "Only for POC, use experience-specific mutations instead")
        @authorize(member: ["initiate.member.contract.offboarding"], company: ["initiate.company.contract.offboarding"], operations: ["initiate.operations.contract.offboarding"])

    # Member experience
    offboardingMemberExpInitiatialise(contractId: ID!, input: OffboardingMemberInitialiseInput!): OffboardingExperienceState!
        @authorize(member: ["update.member.contract.offboarding"])
    offboardingMemberExpCancel(contractId: ID!): OffboardingExperienceState!
        @authorize(member: ["update.member.contract.offboarding"])
    offboardingMemberExpUploadWetInkLetter(contractId: ID!, input: OffboardingMemberUploadWetInkLetterInput!): OffboardingExperienceState!
        @authorize(member: ["update.member.contract.offboarding"])
    offboardingMemberExpConfirmWetInkLetterSent(contractId: ID!): OffboardingExperienceState!
        @authorize(member: ["update.member.contract.offboarding"])
    offboardingMemberExpDownloadWetInkLetterTemplate(contractId: ID!): DocumentReadable!
        @authorize(member: ["update.member.contract.offboarding"])

    # Company experience
    offboardingCompanyExpAcceptWithoutModification(contractId: ID!): OffboardingExperienceState! @deprecated(reason: "No longer necessary")
        @authorize(company: ["update.company.contract.offboarding"])

    offboardingCompanyExpAcknowledge(contractId: ID!, input: OffboardingCompanyAcknowledgeInput!): OffboardingExperienceState!
        @authorize(company: ["update.company.contract.offboarding"])
    
    offboardingCompanyExpInputLeaveBalance(contractId: ID!, input: OffboardingCompanyExpLeaveBalanceInput!): OffboardingExperienceState!
        @authorize(company: ["update.company.contract.offboarding"])
    
    offboardingCompanyExpInputAdditionalFnfDetails(contractId: ID!, input: OffboardingCompanyExpAdditionalFnfDetailsInput!): OffboardingExperienceState!
        @authorize(company: ["update.company.contract.offboarding"])

    # Ops experience
    offboardingOpsExpInitiatialise(contractId: ID!, input: OffboardingOpsInitialiseInput!): OffboardingExperienceState!
        @authorize(operations: ["initiate.operations.contract.offboarding"])
    offboardingOpsExpAcknowledge(contractId: ID!, input: OffboardingOpsAcknowledgeInput!): OffboardingExperienceState!
        @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingOpsExpConfirm(contractId: ID!): OffboardingExperienceState! @deprecated(reason: "Automatically confirmed once member signs resignation letter")
        @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingOpsFnfConfirm(contractId: ID!): OffboardingExperienceState!
        @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingOpsExpComplete(contractId: ID!, input: OffboardingOpsCompleteInput!): OffboardingExperienceState! 
        @authorize(operations: ["complete.operations.contract.offboarding"])
    offboardingOpsExpInputLeaveBalance(contractId: ID!, input: OffboardingOpsLeaveBalanceInput!): OffboardingExperienceState!
        @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingOpsExpInputSupportingDocuments(contractId: ID!, input: OffboardingOpsSupportingDocumentsInput!): OffboardingExperienceState!
        @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingOpsExpCancel(contractId: ID!): OffboardingExperienceState!
        @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingOpsExpSkipResignationLetterSigning(contractId: ID!): OffboardingExperienceState!
        @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingOpsExpConfirmWetInkLetter(contractId: ID!, input: OffboardingOpsConfirmWetInkLetterInput!): OffboardingExperienceState!
        @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingOpsExpConfirmGovtPortalProof(contractId: ID!): OffboardingExperienceState!
        @authorize(operations: ["update.operations.contract.offboarding"])
}

input OffboardingMemberInitialiseInput {
    desiredLastWorkingDay: Date!
    reason: String
    documents: [Upload!]
    govtPortalProofDocuments: [Upload!]
    additionalComments: String
}

input OffboardingCompanyExpLeaveBalanceInput {
    balanceInDays: Float!
    documents: [Upload!]
    additionalComments: String
    isMultiplierBalance: Boolean!
}

input OffboardingCompanyExpAdditionalFnfDetailsInput {
    additionalFnfDetails: String
}

input OffboardingCompanyAcknowledgeInput {
    proposedLastWorkingDay: Date
    shortfallPayType: OffboardingShortfallPayType!
    amount: Float
    unit: OffboardingShortfallPayUnit
    additionalComments: String
}

input OffboardingOpsInitialiseInput {
    desiredLastWorkingDay: Date!
    reason: String
    documents: [Upload!]
    govtPortalProofDocuments: [Upload!]
    additionalComments: String
}

input OffboardingOpsAcknowledgeInput {
    proposedLastWorkingDay: Date
    shortfallPayType: OffboardingShortfallPayType!
    amount: Float
    unit: OffboardingShortfallPayUnit
    additionalComments: String
}

input OffboardingOpsCompleteInput {
    paySupplements: Boolean
    expenses: Boolean
    insurance: Boolean
    finalSettlementProcessed: Boolean
    checklistDocuments: [Upload!]
}

input OffboardingOpsLeaveBalanceInput {
    balanceInDays: Float!
}

input OffboardingOpsSupportingDocumentsInput {
    documents: [Upload!]
}

input OffboardingMemberUploadWetInkLetterInput {
    documents: [Upload!]!
}

input OffboardingOpsConfirmWetInkLetterInput {
    documents: [Upload!]!
    receivedPhysically: Boolean!
}

type OffboardingExperienceState {
    contractId: ID!
    currentStep: OffBoardingExperienceStep!
    completedSteps: [OffBoardingExperienceStep!]
    remainingSteps: [OffBoardingExperienceStep!]
    offboardingStatus: OffboardingExperienceStatus!
    offboardingDetails: OffboardingExperienceDetails!
    errorMessage: String
}

type OffboardingExperienceDetails {
    id: ID!
    contractId: ID!
    submittedOn: DateTime
    desiredLastWorkingDay: Date
    tentativeLastWorkingDay: Date
    noticePeriod: OffboardingNoticePeriod
    reason: String
    additionalComments: String
    nextStepsSummmaries: [String!]
    offboardingType: OffboardingExperienceType
    hasCustomerAcknowledged: Boolean
    opsConfirmationDetails: OpsConfirmationDetails
    opsCompletionDetails: OpsCompletionDetails
    leaveBalanceInDays: Float @deprecated(reason: "Use leaveBalanceDetails instead")
    fnfAdditionalDetails: String
    checkList: OffboardingExperienceCheckList
    documents(types: [OffboardingExperienceDocumentType!]): [OffboardingExperienceDocument!]
    resignationLetter: Document @deprecated(reason: "Pandadoc files still have to rely on blob/internal-download approach. Use resignationLetterV2 instead")
    resignationLetterV2: DocumentReadable
    shortfallPay: OffboardingExperienceShortfallPay
    leaveBalanceDetails: LeaveBalanceDetails
    hasMemberSentWetInkLetter: Boolean
    hasOpsReceivedWetInkLetter: Boolean
    wetInkLetterRecipientEntity: WetInkLetterRecipientEntity
}

type WetInkLetterRecipientEntity {
    name: String
    address: Address
}


type LeaveBalanceDetails {
    balanceInDays: Float!
    additionalComments: String
    isMultiplierBalance: Boolean!
}

type OffboardingExperienceShortfallPay {
    type: OffboardingShortfallPayType!
    amount: Float
    unit: OffboardingShortfallPayUnit
}

type OffboardingExperienceCheckList {
    insuranceChecklist: Boolean
    expensesChecklist: Boolean
    paySupplementsChecklist: Boolean
    fnfSettledConfirmedChecklist: Boolean
}

type OpsConfirmationDetails {
    actor: Actor
    confirmationDate: DateTime
}

type OpsCompletionDetails {
    actor: Actor
    completionDate: DateTime
}

type Actor {
    id: ID! # depends on domain, e.g. if ActorDomain = OPERATIONS, multiplier.operations_user.id
    domain: ActorDomain
    firstName: String
    lastName: String
    fullName: String
}

type OffboardingExperienceDocument {
    id: ID!
    type: OffboardingExperienceDocumentType
    document: Document
}

type OffboardingNoticePeriod {
    value: Int
    unit: OffboardingNoticePeriodUnit
}

enum ActorDomain {
    OPERATIONS
}

enum OffBoardingExperienceStep {
    NOT_STARTED # experience step
    UPLOAD_GOVT_PORTAL_PROOF
    RESIGNATION_LETTER_PENDING
    SHORTFALL_PAY_DETAILS_PENDING

    GOVT_PORTAL_PROOF_PENDING
    WET_INK_LETTER_PENDING
    WET_INK_OPS_CONFIRM_PENDING

    FNF_DETAILS_PENDING
    FNF_PENDING
    FNF_CHECKLIST_SUBMITTED # experience step
    FNF_SETTLED

    INITIATED_OFFBOARDING @deprecated(reason: "Using domain graph enums")
    RESIGNATION_LETTER_SENT @deprecated(reason: "Using domain graph enums")
    EMPLOYEE_RESIGNATION_LETTER_SIGNED @deprecated(reason: "Using domain graph enums")
    COMPLETED @deprecated(reason: "Using domain graph enums")
}

enum OffboardingExperienceStatus {
    CANNOT_START
    NOT_STARTED

    # from v1 flow
    INITIATED
    IN_PROGRESS
    FNF_PENDING
    COMPLETED
    CANCELLED

    AWAITING_EMPLOYEE_SIGNATURE @deprecated(reason: "unused")
    AWAITING_COMPANY_CONFIRMATION @deprecated(reason: "unused")
    APPROVED @deprecated(reason: "unused")
    REJECTED @deprecated(reason: "unused")
}

enum OffboardingExperienceType {
    RESIGNATION
}

enum OffboardingExperienceDocumentType {
    MEMBER_SUPPORTING
    OPS_SUPPORTING
    CHECKLIST
    LEAVE_BALANCE_PROOF
    WET_INK
    RELIEVING_LETTER
    GOVT_PORTAL_PROOF
}

enum OffboardingShortfallPayType {
    NO_PAY
    EMPLOYEE_PAY
    COMPANY_PAY
}

enum OffboardingShortfallPayUnit {
    DAYS
    MONTHS
}

enum OffboardingNoticePeriodUnit {
    YEARS
    MONTHS
    WEEKS
    DAYS
}
