extend type Query {
    member(id: ID, userId: ID): Member @authorize(company: ["view.company.members"], operations: ["view.operations.contract.members"], member: ["view.member"], partner: ["view.partner.member"])
    countryLegalData(country: CountryCode!): [LegalData]  @authorize(operations: ["view.operations.contract.members"])   @deprecated #no usage in code    # fetch global country legal master-data...

    """
    membersWithPagination will return the list of member by filter and pagination
    """
    membersWithPagination(
        """
        input contains the list of required input for the query
        """
        input: MembersInput
        """
        PageRequest is the pagination parameter
        """
        pageRequest: PageRequest
    ) : MembersResult @authorize(operations: ["view.operations.contract.members"])

    getMemberPersonalDetailsAccessibilityRules(memberId: ID): [SectionAccessibilityResponse] @authorize(company: ["view.company.accessibility-rules"], operations: ["view.operations.accessibility-rules"], member: ["view.member.accessibility-rules"])

    """
    Check if the value of the specified channel is verified for a member
    """
    checkChannelVerification(memberId: ID!, input: ChannelVerificationInput!): ChannelVerificationResponse @authorize(member: ["manage.member.channel-verification"])

    memberCustomFieldDefinitions: [CustomFieldDefinition] @authorize(company: ["view.company.member.custom-field-definition"], operations: ["view.operations.member.custom-field-definition"])
    memberCustomFieldCategoryConfigs: [CustomFieldCategoryConfig!] @authorize(company: ["view.company.member.custom-field-definition"], operations: ["view.operations.member.custom-field-definition"])
}

extend type Mutation {
    memberCreate(value: MemberCreateInput!): Member @authorize(company: ["create.company.members"], operations: ["create.operations.members"])
    memberUpdate(id: ID!, memberChange: MemberUpdateInput): Member @authorize(company: ["update.company.members"], operations: ["update.operations.members"], member: ["update.member.details"])
    memberChangePrimaryEmail(id: ID!, email: EmailAddressInput): Member @authorize(company: ["update.company.members"], operations: ["update.operations.members"], member: ["update.member.details"])    #Change primary email. Only can be done during definition.

    memberUpdateBasicDetails(id: ID!, data: MemberUpdateBasicDetailsInput!): Member @authorize(operations: ["update.operations.members"], member: ["update.member.details"])
    # the data: [LegalDataInput!]! must contain all key-value pairs. Missing any pair will make backend remove it.
    # E.g.: existing [a, b, c] + "data" param [b, c, d] => result: [b, c, d]. That means: a deleted, b & c updated, d inserted
    memberUpdateLegalData(id: ID!, data: [LegalDataInput!]!): Member @authorize(company: ["update.company.legaldata"],operations: ["update.operations.members"], member: ["update.member.legaldata"])
    # currently only updates and inserts, no deletes. i.e.: existing [a, b, c] + "data" param [b, c, d] => result: [a, b, c, d]
    memberUpdateDocument(id: ID!, data: [LegalDocumentInput!]!): Member @authorize(company: ["upload.company.documents"], operations: ["update.operations.members"], member: ["upload.member.documents"])
    memberUpdateVisaDocument(id: ID!, data: [LegalDocumentInput!]!): Member @authorize(operations: ["update.operations.members"], member: ["upload.member.documents"])
    memberUpdateBankDetails(id: ID!, data: BankAccountInput!): Member @authorize(company: ["update.company.bankdetails"], operations: ["update.operations.members"], member: ["update.member.bankdetails"])
    memberUpdatePayoutMethod(memberId: ID!, input: PayoutMethodInput!): PayoutMethod @authorize(company: ["update.company.payout-method"], operations: ["update.operations.members"], member: ["update.member.payout-method"])
    memberDelete(id: ID!): Member @authorize(company: ["delete.company.members"], operations: ["delete.operations.members"]) @deprecated   # Use "contractDelete" in contract graph.

    memberSendUpdateRequest(category: MemberChangeCategory!, requestInput: MemberChangeRequestInput!): Member @authorize(member: ["create.member.datachangerequest"])
    memberCancelUpdateRequest(category: MemberChangeCategory!, memberId: ID): Member @authorize(member: ["cancel.member.datachangerequest"])
    memberBackFillBankIdentifier(country: CountryCode!, memberIds: [ID]!): TaskResponse @authorize(operations: ["update.operations.members"])

    # HRIS specific mutations
    memberUpsertBasicInfo(id: ID!, input: MemberBasicInfoInput!): Member @authorize(company: ["update.company.basic-info"], operations: ["update.operations.basic-info"], member: [])
    memberUpsertPersonalInfo(id: ID!, input: MemberPersonalInfoInput!): Member @authorize(company: ["update.company.personal-info"], operations: ["update.operations.personal-info"], member: ["update.member.personal-info"])
    memberUpsertContactInfo(id: ID!, input: MemberContactInfoInput!): Member @authorize(company: ["update.company.contact-info"], operations: ["update.operations.contact-info"], member: ["update.member.contact-info"])
    memberUpsertEmergencyContact(id: ID!, input: EmergencyContactInput!): Member @authorize(company: ["update.company.emergency-contact"], operations: ["update.operations.emergency-contact"], member: ["update.member.emergency-contact"])
    memberUpsertIdentificationDetails(id: ID!, input: MemberIdentificationDetailsInput!): Member @authorize(operations: ["update.operations.members"])   @deprecated(reason: "not required")
    memberUpsertAddress(id: ID!, input: MemberAddressInput!): Member @authorize(company: ["update.company.address"], operations: ["update.operations.address"], member: ["update.member.address"])
    memberUpsertEducation(id: ID!, input: EducationInput!): Member @authorize(company: ["update.company.education-details"], operations: ["update.operations.education-details"], member: ["update.member.education-details"])
    memberUpsertPreviousEmployer(id: ID!, input: PreviousEmployerInput!): Member @authorize(company: ["update.company.previous-employer-details"], operations: ["update.operations.previous-employer-details"], member: ["update.member.previous-employer-details"])
    memberUpsertProfilePicture(
        id: ID!,
        key: String @deprecated(reason: "key is redundant and should not be required"),
        file: Upload!
    ): Member @authorize(company: ["update.company.profile-picture"], operations: ["update.operations.profile-picture"], member: ["update.member.profile-picture"])
    memberRemoveProfilePicture(id: ID!): Member @authorize(company: ["update.company.profile-picture"], operations: ["update.operations.profile-picture"], member: ["update.member.profile-picture"])
    memberUpsertCustomFields(id: ID!, input: MemberCustomFieldInput!): Member   @authorize(operations: ["update.operations.members"])    @deprecated(reason: "not required")

    # from core-service-fed
    memberUpdateDataText(id: ID!, textFieldInputs: [TextFieldInput]!) : Member @authorize(company: ["update.company.legaldata"],operations: ["update.operations.members"], member: ["update.member.legaldata"])
    memberUpdateDataDate(id: ID!, dateFieldInputs: [DateFieldInput]!) : Member @authorize(company: ["update.company.legaldata"],operations: ["update.operations.members"], member: ["update.member.legaldata"])

    """
    Send OTP to the specified channel for verification
    """
    sendGenericOtp(memberId: ID!, input: GenericOtpInput!): TaskResponse @authorize(member: ["manage.member.channel-verification"])

    """
    Verify OTP for the specified channel
    """
    verifyGenericOtp(memberId: ID!, input: GenericOtpVerificationInput!): GenericOtpVerificationResponse @authorize(member: ["manage.member.channel-verification"])

    memberCustomFieldDefinitionsUpsert(inputs: [CustomFieldDefinitionInput!]!): [CustomFieldDefinition!] @authorize(company: ["update.company.member.custom-field-definition"], operations: ["update.operations.member.custom-field-definition"])
    memberCustomFieldValuesUpsert(inputs: [CustomFieldValueInput!]!): [CustomFieldValue!] @authorize(company: ["update.company.member.custom-field-value"], operations: ["update.operations.member.custom-field-value"], member: ["update.member.custom-field-value"])
}


# --------------------------------------
# Mutation types....
# --------------------------------------

input MembersInput {
    "filters contains the list of filter and merge it by AND operation"
    filters: MemberFilters
}

input MemberFilters {
    statuses: [MemberStatus!]
    companyIds: [ID!]
    countries: [CountryCode!]
    contractTypes: [ContractType!]
    startDateRanges: [DateRange!]
    createdDateRanges: [DateRange!]
    endedDateRanges: [DateRange!]
    validTillDateRanges: [DateRange!]
    memberNameContains: [String!]
    memberEmails: [String!]
    contractIds: [ID!]
    eorPartnerIds: [ID!]
    isMultiplierEorPartner: Boolean
    hasChangeRequested: Boolean
    contractStatuses: [ContractStatus!]
    onboardingStatuses: [ContractOnboardingStatus!]
    offboardingStatuses: [ContractOffboardingStatus!]
    isLegacyOffboardingFilter: Boolean
    payrollFormsUploadDateRanges: [DateRange!]
    payrollFormsUploadStatuses: [PayrollFormsUploadStatus!]
    uploadedPayrollForms: Boolean
    activationDateRanges: [DateRange!]
    contractFilters: ContractFilters
    backgroundVerificationStatuses: [BackgroundVerificationStatus!]
}

input MemberCreateInput {
    firstName: String
    middleName: String
    lastName: String
    email: String  @ValidEmail      # Primary, the one use from creating/signing-in
    gender: Gender                  # For gender based Insurance, Contract, Restrictions.
}

input MemberUpdateInput {
    firstName: String
    middleName: String
    lastName: String
    gender: Gender
}

input MemberUpdateBasicDetailsInput {
    fullLegalName: String
    nationality: MemberNationalogyInput
    dateOfBirth : DateTime
    phone: PhoneNumberInput
    address: AddressInput
    firstName: String
    middleName: String
    lastName: String
    gender: Gender
    status: MemberStatus
    martialStatus: MartialStatus    @deprecated(reason: "Typo in the field. New field `maritalStatus` introduced")
    maritalStatus: MaritalStatus
}

input MemberNationalogyInput {
    type: Nationalogy               # Citizen, Permanent-Residence, Refugee
    country: CountryCode
}

input LegalDataInput {
    key: String                     # The main key value which is unique to the country
    value: String
    identifier: String              # Identifier for the key. Eg: if key is "identityProof", then identifier can be "Passport No"
}

input LegalDocumentInput {
    key: String                     # A identifier/label with some possible-uniqueness...
    documents: [Upload]             # PDFs or Images of Identification, used for verification.
    financialYear: Int
    category: LegalDocumentCategory
}

input BankAccountInput {
    accountName: String
    accountNumber: String
    bankName: String
    branchName: String
    currency: CurrencyCode # TODO: seems unused
    country: CountryCode # TODO: seems unused
    swiftCode: String
    localBankCode: String

    """
    null, [], [null], [null, null]...are considered "empty"<br>
    The mutation will upload these files and save into DB as "document ids" along with `bankStatementDocIds: [ID]`<br>
    If both `bankStatements` and `bankStatementDocIds` are empty, the mutation will not touch existing `bankStatementDocIds` in the DB
    """
    bankStatements: [Upload]

    """
    null, [], [null], [null, null]...are considered "empty"<br>
    The mutation will consider these "existing documents" and simply save them into DB along with "document ids" returned from uploading `bankStatements: [Upload]`<br>
    If both `bankStatements` and `bankStatementDocIds` are empty, the mutation will not touch existing `bankStatementDocIds` in the DB
    """
    bankStatementDocIds: [ID]

    # dynamic detail fields
    dynamicDetail: BankDetailDynamicInput
}

input BankDetailDynamicInput {
    accountType: PaymentAccountType
    transferType: TransferType
    sourceCurrency: CurrencyCode
    targetCurrency: CurrencyCode
    paymentPartner: PaymentPartnerCode
    paymentAccountRequirementType: String
    fieldValues: [RequirementFieldValue]
}

input RequirementFieldValue {
    key: String
    value: String
}

input MemberChangeRequestInput {
    # Basic details field
    firstName: String
    middleName: String
    lastname: String
    gender: Gender
    fullLegalName: String
    dateOfBirth: DateTime
    nationality: MemberNationalogyInput
    nationalId: String
    passportNo: String
    legalData: [LegalDataInput!]
    legalDocuments: [LegalDocumentInput!]

    #Contact Details
    phone: PhoneNumberInput
    address: AddressInput
    email: EmailAddressInput
    workEmail: EmailAddressInput

    # Bank details field
    bankAccount: BankAccountInput
    payoutMethod: PayoutMethodInput

    """proof document for this particullar request. Same behavior as BankAccountInput.bankStatements/bankStatementDocIds"""
    documents: [Upload]
    documentIds: [ID]

    raisedOnBehalfOf: ID
    addressDetails: MemberAddressInput
}

input MemberBasicInfoInput {
    firstName: String
    middleName: String
    lastName: String
    fullLegalName: String
}

input MemberPersonalInfoInput {
    dateOfBirth: Date
    nationalities: [MemberNationalogyInput!]
    religion: String
    maritalStatus: MaritalStatus
    gender: Gender
}

input MemberContactInfoInput {
    email: EmailAddressInput
    phone: PhoneNumberInput
    mobile: PhoneNumberInput
}

input EmergencyContactInput {
    name: String!
    relationship: Relationship
    mobileNo: PhoneNumberInput!
}

input MemberIdentificationDetailsInput {
    legalData: [LegalDataInput]
    legalDocuments: [LegalDocumentInput]
}

input MemberAddressInput {
    currentAddress: AddressInput
    permanentAddress: AddressInput
}

input EducationInput {
    lastSchoolAttended: String!
    degree: String!
    yearOfPassing: Date!
    gpa: Float      # gpa values will be like 2.1, 3.5, 4.0
    grade: String   # grade values will be like A+, A, B+, B, C, D, F
}

input PreviousEmployerInput {
    name: String!
    startDate: Date!
    endDate: Date!
    designation: String!
    jobDescription: String
}

input MemberCustomFieldInput {
    key: String!
    value: String
}

# --------------------------------------
# Channel Verification Input types
# --------------------------------------

input ChannelVerificationInput {
    channel: VerificationChannel!
    data: ChannelDataInput!
}

input ChannelDataInput {
    email: String
}

input GenericOtpInput {
    channel: VerificationChannel!
    data: ChannelDataInput!
}

input GenericOtpVerificationInput {
    channel: VerificationChannel!
    data: ChannelOtpDataInput!
}

input ChannelOtpDataInput {
    email: String
    otp: String!
}

# --------------------------------------
# Query types....
# --------------------------------------


type Member implements Person @key(fields: "id") {
    id: ID
    userId: String
    persona: Persona
    firstName: String
    middleName: String
    lastName: String
    fullLegalName: String
    emails: [EmailAddress]
    phoneNos: [PhoneNumber]         # Mobile, Home, Work, Can be used to contact in emergency.
    addresses : [Address]           # Used for Leaves (voting in SL), Emergency contact, Agreements.

    status: MemberStatus
    activatedOn: DateTime           @deprecated(reason: "Moved to Contract.")
    martialStatus: MartialStatus    @deprecated(reason: "Typo in the field. New field `maritalStatus` introduced")
    maritalStatus: MaritalStatus    # For leaves/time-offs, companies can be penalises for not granting parental leaves
    gender : Gender                 # For gender based Insurance, Contract, Restrictions.
    dateOfBirth : DateTime            # For age based Insurance, Contract, Restrictions, Compensations.
    age: Float                      # Derived, Korea has its own aging system.

    nationalities: [MemberNationalogy]                  # Used for Agreements.
    legalData: [LegalData]              # Used for Agreements.
    legalDocuments(category: LegalDocumentCategory): [LegalDocument]     # Used for Agreements.
    payrollFormsUploadDate: DateTime
    payrollFormsUploadStatus: PayrollFormsUploadStatus
    bankAccounts: [BankAccount]         # Used for Payroll, Benefits, Agreements.

    changeRequests: [MemberChangeRequest]

    payoutMethods: [PayoutMethod]

    # Member Details categorised for HRIS
    basicInfo: MemberBasicInfo                          # Basic details of the member
    personalInfo: MemberPersonalInfo                    # Personal details of the member
    emergencyContact: EmergencyContact                  # Emergency contact details
    contactInfo: MemberContactInfo                      # Contact details
    identificationDetails: MemberIdentificationDetails  @deprecated(reason: "not required") # Identification details
    addressDetails: MemberAddress                       # Address details
    education: [Education]                              # Education details
    previousEmployers: [PreviousEmployer]               # Previous employer details
    profilePicture: Image                               # Profile picture of the member
    customFields: [MemberCustomField] @deprecated(reason: "not required")  # Custom fields for the member
    createdOn: DateTime
    updatedOn: DateTime

    customFieldValues: [CustomFieldValue!]
}


# --------------------------------------
# Member Specific Sub types....
# --------------------------------------


# To hold nation/administrative-region specific data.
# In a scenario the the member is contracted via a PEO to mutiple companies across mutiple nations.
# Example - For a pakistani Accountant to work with a US firm, member need to hold a visa
type MemberNationalogy {
    type: Nationalogy               # Citizen, Permanent-Residence, Refugee
    country: CountryCode
}

# Defines a legal document. Ex: Passport, NIC, Adhar, Social No, Europe, UTR, Britain, Driver's License
type LegalDocument @key(fields: "id") {
    id: ID!
    key: String                     # A identifier/label with some possible-uniquness...
    documents: [File]  @deprecated (reason: "Please use `files` instead")
    files: [Document!] # PDFs or Images of Identification, used for verification.
    label: String                   # Country-specific local name..
    country: CountryCode @deprecated
    financialYear: Int
    status: LegalDocumentStatus
    category: LegalDocumentCategory
    approver: [OperationsUser]
    comment: String                 # Reason for rejecting etc.
    createdOn: DateTime
    updatedOn: DateTime
}


type MemberChangeRequest @key(fields: "id") {
    id: ID!
    items: [MemberChangeItem]
    status: MemberChangeRequestStatus
    category: MemberChangeCategory
    message: String

    createdOn: DateTime
    updatedOn: DateTime
    documents: [FileLink] @deprecated (reason: "Please use `files` instead") # just a link for url downloader component
    files: [Document!] # just a link for url downloader component
    raisedBy: ID
}

type GenderParam {
    key: String
    value: Gender
}

type MemberNationalogyParam {
    key: String
    value: MemberNationalogy
}

type PhoneNumberParam {
    key: String
    value: PhoneNumber
}

type AddressParam {
    key: String
    value: Address
}

type CurrentAddressParam {
    key: String
    value: Address
}

type PermanentAddressParam {
    key: String
    value: Address
}

type EmailAddressParam {
    key: String
    value: EmailAddress
}

type BankAccountParam {
    key: String
    value: BankAccount
}

type PayoutMethodParam {
    key: String
    value: PayoutMethod
}

type LegalDataParam {
    key: String
    value: LegalData
}

type LegalDocumentParam {
    key: String
    value: LegalDocument
    documents: [DocumentReadable] @deprecated(reason: "Please use `value.key` and `value.files` instead")
}

type IntParam {
    key: String
    value: Int
}

type StringParam {
    key: String
    value: String
}

type DateParam {
    key: String
    value: DateTime
}

type MemberBasicInfo {
    firstName: String
    middleName: String
    lastName: String
    fullLegalName: String
}

type MemberPersonalInfo {
    dateOfBirth: Date
    age: Int
    nationalities: [MemberNationalogy]
    religion: String
    maritalStatus: MaritalStatus
    gender: Gender
}

type MemberContactInfo {
    email: EmailAddress
    phone: PhoneNumber
    mobile: PhoneNumber
}

type EmergencyContact {
    name: String
    relationship: Relationship
    phoneNumber: PhoneNumber
}

type MemberIdentificationDetails {
    legalData: [LegalData]
    legalDocuments: [LegalDocument]
}

type MemberAddress {
    currentAddress: Address
    permanentAddress: Address
}

type Education {
    lastSchoolAttended: String
    degree: String
    yearOfPassing: Date
    gpa: Float              # gpa values will be like 2.1, 3.5, 4.0
    grade: String           # grade values will be like A+, A, B+, B, C, D, F
}

type PreviousEmployer {
    name: String
    startDate: Date
    endDate: Date
    designation: String
    jobDescription: String
}

type MemberCustomField {
    key: String
    value: String
}

type SectionAccessibilityResponse {
    section: MemberPersonalDetailsSection!
    visible: Boolean
    accessibility : AccessibilityResponse
}

union MemberChangeItem = IntParam | StringParam | DateParam | GenderParam | MemberNationalogyParam | PhoneNumberParam | AddressParam | EmailAddressParam | BankAccountParam | PayoutMethodParam | LegalDataParam | LegalDocumentParam | CurrentAddressParam | PermanentAddressParam

# --------------------------------------
# Channel Verification Response types
# --------------------------------------

type ChannelVerificationResponse {
    verified: Boolean!
    channel: VerificationChannel!
    meta: ChannelVerificationMeta
}

type ChannelVerificationMeta {
    canTryAgainAt: DateTime
}

type GenericOtpVerificationResponse {
    verified: Boolean!
}


# --------------------------------------
# Enums
# --------------------------------------

# Defines the type of the nationality. @TODO : Bit confusing if we should only bother of CITIZEN
enum Nationalogy {
    CITIZEN
    PERMANENT_RESIDENT
    TEMPORARY_RESIDENT
}

enum MartialStatus {    # @deprecated(reason: "Typo in type name. New type `MaritalStatus` introduced")
    SINGLE
    MARRIED
    DIVORCED
    UNSPECIFIED
}

enum MaritalStatus {
    SINGLE
    MARRIED
    DIVORCED
    UNSPECIFIED
}

# All valid status of the member entity...
enum MemberStatus {
    CREATED         # Member is being setup.
    INVITED         # Member is ready to setup.
    ACTIVE          # Member has been setup.
    SUSPENDED       # Member has been suspended
    DELETED         # Member has been deleted for some reason. Should we record the reason too.
}

enum LegalDocumentStatus {
    SUBMITTED
    APPROVED
    REJECTED
}

enum MemberChangeRequestStatus {
    SUBMITTED,
    APPROVED,
    REJECTED,
    CANCELLED
}

 enum MemberChangeCategory {
    BASIC_DETAILS,
    CONTACT_DETAILS,
    BANK_DETAILS,
    IDENTIFICATION_DETAILS,
    ADDRESS_DETAILS,
    PAYOUT_METHOD
}

enum MemberPersonalDetailsSection {
    BASIC_INFO,
    PERSONAL_INFO,
    PERSONAL_CONTACT_DETAILS,
    EMERGENCY,
    IDENTIFICATION_DETAILS,
    ADDRESS,
    FINANCIAL,
    EDUCATION,
    PREVIOUS_EMPLOYMENT,
    CHANGE_REQUESTS,
    PROFILE_PICTURE,
}

type MembersResult {
    data: [Member]
    allCount: Int
    onboardingCount: Int
    activeCount: Int
    offboardingCount: Int
    endedCount: Int
    pageResult: PageResult
}

enum PayoutMethodType {
    PAYPAL
    PAYONEER
    CRYPTO
    BANK_ACCOUNT
}

enum VerificationChannel {
    EMAIL
}

type PayoutMethodData {
    email: String!
    name: String!
    documents: [FileLink]
}

type PayoutMethod {
    id: ID!
    payoutMethodType: PayoutMethodType!
    isVerified: Boolean!
    isPrimary: Boolean!
    data: PayoutMethodData!
    bankAccounts: [BankAccount]
}

input PayoutMethodDataInput {
    email: String
    name: String
    documents: [Upload]
    documentIds: [ID]
}

input PayoutMethodInput {
    payoutMethodType: PayoutMethodType!
    isPrimary: Boolean
    isVerified: Boolean
    data: PayoutMethodDataInput
    bankAccountId: String
}
