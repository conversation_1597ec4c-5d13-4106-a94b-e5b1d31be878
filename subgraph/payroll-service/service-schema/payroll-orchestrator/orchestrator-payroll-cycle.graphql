# TODO: These schemas will be moved to Orchestrator.

type PayrollCycle @key(fields: "id")  {
    id: ID!
    configId: ID
    status: PayrollCycleStatus @authorize(company: ["view.company.payroll-cycle"], operations: ["view.operations.payroll-cycle"], member: ["use.member.payroll"], partner: ["view.partner.payroll-cycle"])
    payrollMonth: MonthYear
    payDate: Date
    defaultPayDate: Date
    entity: PayrollCycleEntity @authorize(company: ["view.company.payroll-cycle"], operations: ["view.operations.payroll-cycle"], partner: ["view.partner.payroll-cycle"])
    country: CountryCode
    frequency: PayFrequency
    startDate: Date
    defaultStartDate: Date
    endDate: Date
    defaultEndDate: Date
    cutOffFrom: Date
    cutOffTo: Date
    defaultCutOffTo: Date
    contractStartDateFrom: Date
    contractStartDateTo: Date
    contractEndDateFrom: Date
    contractEndDateTo: Date
    compensationDateFrom: Date
    compensationDateTo: Date
    paySupplementFrom: Date
    paySupplementTo: Date
    expenseFrom: Date
    expenseTo: Date
    timeSheetDateFrom: Date
    timeSheetDateTo: Date
    timeOffFrom: Date
    timeOffTo: Date
    deductionFrom: Date
    deductionTo: Date
    memberChangesFrom: Date
    memberChangesTo: Date
    oneTimePaymentDateFrom: Date @deprecated
    oneTimePaymentDateTo: Date @deprecated
    submitPayrollInputDeadline: Date
    approvePayrollInputDeadline: Date
    payrollProcessingDeadline: Date
    approvePayrollReportDeadline: Date
    defaultApprovePayrollReportDeadline: Date
    payrollCompleteDeadline: Date
    createdBy: Person @authorize(operations: ["view.operations.payroll-cycle"])
    createdOn: DateTime @authorize(operations: ["view.operations.payroll-cycle"])
    previousCycle: PayrollCycle @authorize(company: ["view.company.payroll-cycle"])
    primaryCycle: PayrollCycle @authorize(operations: ["view.operations.payroll-off-cycle"], partner: ["view.partner.payroll-off-cycle"])
    offCycles: [PayrollCycle] @authorize(operations: ["view.operations.payroll-off-cycle"], partner: ["view.partner.payroll-off-cycle"])
    cycleType: PayrollCycleType
    payrollOutputFile: PayrollReportUpload @authorize(operations: ["use.operations.payroll-report"], partner: ["use.partner.payroll"])
    workflow: PayrollCycleWorkflow @authorize(company: ["view.company.payroll-cycle"], operations: ["view.operations.payroll-cycle"], partner: ["view.partner.payroll-cycle"])
    opsPayslipUploadWorkflow: OpsPayslipUploadWorkflow @authorize(operations: ["use.operations.payslips"])
    paySupplementSummary: PaySupplementSummary @authorize(operations: ["view.operations.paysupplement|update.operations.paysupplement"])
    expenseSummary: ExpenseSummary @authorize(operations: ["view.operations.expenses|update.operations.expenses"])
    payslips: [Payslip!] @authorize(operations: ["use.operations.payslips"])
    payslipUploadDetails: PayslipUploadDetails @authorize(operations: ["use.operations.payslips"])
    payslipSummary: PayslipSummary @authorize(company: ["use.company.payslips"], operations: ["use.operations.payslips"])

    payrollInput: FileDownload @authorize(operations: ["download.operations.input-file"])
    bulkUpload: PayrollBulkUpload @authorize(operations: ["view.operations.payroll-cycle"], company: ["view.company.payroll-cycle"])

    inputHeadcountSummary: InputHeadcountSummary @authorize(company: ["view.company.payroll-cycle"], operations: ["view.operations.payroll-cycle"])
    employeeDataChanges: EmployeeDataChanges @authorize(company: ["view.company.payroll-cycle"])
    outputHeadcountSummary: OutputHeadcountSummary @authorize(company: ["view.company.payroll-cycle"], operations: ["view.operations.payroll-cycle"])
    payrollOutputSummary: PayrollOutputSummary @authorize(company: ["view.company.payroll-cycle"], operations: ["view.operations.payroll-cycle"])
    outputVariance: PayrollOutputDataVariance @authorize(company: ["view.company.payroll-cycle"])

    partnerId: ID @authorize(operations: ["view.operations.payroll-cycle"], partner: ["view.partner.payroll-cycle"])
}

enum PayFrequency {
    BIWEEKLY
    MONTHLY
    SEMIMONTHLY
    WEEKLY
}

enum PayrollCycleType {
    PRIMARY_CYCLE
    OFF_CYCLE
}

enum PayrollCycleStatus {
    CREATED,
    INPUT_IN_PROGRESS,
    INPUT_IN_CONFIRMATION,
    SENT_TO_PARTNER,
    PARTNER_RECONCILIATION,
    SENT_FOR_REVIEW,
    OPS_RECONCILIATION,
    PAYSLIPS_UPLOAD,
    PAYSLIPS_CONFIRMED,
    CUSTOMER_REVIEW,
    OUTPUT_APPROVED,
    CLOSE_PAYROLL,
    CLOSED,
    SUSPENDED
}
