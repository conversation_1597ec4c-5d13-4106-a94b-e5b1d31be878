extend type Mutation {
    createPayrollContractException(value: PayrollContractExceptionInput!): PayrollContractException @authorize(operations: ["create.operations.payroll-contract-exception"])
    deletePayrollContractException(id: ID!): TaskResponse @authorize (operations: ["delete.operations.payroll-contract-exception"])
}

extend type Query {
    getApplicableExceptionCategories(request: PayrollContractExceptionReasonCategoryRequest) : [PayrollContractExceptionReasonCategory]! @authorize(operations: ["create.operations.payroll-contract-exception"])
}

input PayrollContractExceptionInput {
    contractId: ID!
    payrollCycleId: ID!
    exceptionType: PayrollContractExceptionType
    reasonCategory: PayrollContractExceptionReasonCategory
    reason: String
}

input PayrollContractExceptionReasonCategoryRequest {
    contractSnapshotId: ID!
    type: PayrollContractType!
}

type PayrollContractException {
    id: ID
    contractId: ID
    exceptionType: PayrollContractExceptionType
    reasonCategory: PayrollContractExceptionReasonCategory
    startDate: DateTime
    endDate: DateTime
    reason: String
}

enum PayrollContractExceptionType {
    NEW_HIRE_INCLUDE,
    NEW_HIRE_EXCLUDE,
    ACTIVE_INCLUDE,
    ACTIVE_EXCLUDE,
    TERMINATION_INCLUDE,
    TERMINATION_EXCLUDE
}

enum PayrollContractExceptionReasonCategory {
    ELIGIBLE_BEFORE_ACTIVE
    END_DATE_CHANGED
    MISSED_CUTOFF
    OFFBOARDING_CANCELLED
    OTHER
    PRE_REGISTRATION
    START_DATE_CHANGED
    DID_NOT_JOIN
}
