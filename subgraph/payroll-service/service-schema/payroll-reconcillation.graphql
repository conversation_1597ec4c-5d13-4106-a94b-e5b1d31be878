type Query {
    getPayrollReconciliationData(payrollCycleId: ID!): PayrollReconciliationData @authorize(operations: ["view.operation.payroll-recon|update.operation.payroll-recon.status"])
    downloadPayrollReconciliationData(payrollCycleId: ID!): DocumentReadable @authorize(operations: ["view.operation.payroll-recon|update.operation.payroll-recon.status"])
}

type Mutation {
    updatePayrollReconciliationResultStatus(resultId: ID!, resultStatus: ReconciliationResultStatus!): ID! @authorize(operations: ["update.operation.payroll-recon.status"])
}

type PayrollReconciliationData {
    payrollDataId: String
    payrollCycle: PayrollCycle!
    results: [PayrollReconciliationResult]
}

type PayrollReconciliationResult {
    id: ID!
    contractIds: [ID]
    group: ReconciliationGroup!
    category: ReconciliationCategory!
    status: ReconciliationResultStatus!
    severityLevel: ReconciliationResultSeverity
    messages: [String]
}

enum ReconciliationGroup {
    MEMBER
    SALARY
    EXPENSE
    PAY_SUPPLEMENT
}

enum ReconciliationCategory {
    NEW_HIRE_MEMBER_MISSING
    NEW_HIRE_MEMBER_INVALID
    ACTIVE_MEMBER_MISSING
    ACTIVE_MEMBER_INVALID
    TERMINATING_MEMBER_MISSING
    TERMINATING_MEMBER_INVALID
    MEMBER_MISSING
    MEMBER_INVALID
    SALARY_MISMATCH
    SALARY_INVALID
    SALARY_MISSING
    EXPENSE_MISMATCH
    EXPENSE_INVALID
    EXPENSE_MISSING
}

enum ReconciliationResultStatus {
    NEW
    DISCARDED
    VERIFIED
}

enum ReconciliationResultSeverity {
    INFO
    WARNING
    ERROR
}
