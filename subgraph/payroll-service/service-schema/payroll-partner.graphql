# everything is this file is a copy from core-service-fed/service-schema/partner.graphql
# we need this as an intermediate step while extracting payroll partner

input CreatePayrollPartnerInput {
    name: String!
    partnerCountries: [PayrollPartnerCountryInput!]!
    partnerUsers: [CreatePayrollPartnerUserInput!]
}

input CreatePayrollPartnerUserInput {
    email: String!
    firstName: String
    lastName: String
    phoneNo: String
    role: PartnerRole!
    type: PartnerUserType!
    country: CountryCode!
}

input PayrollPartnerCountryInput {
    capability: PartnerCapability!
    countryCode: CountryCode!
    currency: CurrencyCode # only for PartnerCapability = PAYROLL
}

enum PartnerRole {
    PRIMARY_ADMIN
    ADMIN
}

enum PartnerUserType {
    PAYROLL_PRIMARY
    PAYROLL_SECONDARY
    PAYMENTS
}

enum PartnerUserStatus {
    CREATED
    INVITED
    ACTIVE
    # DELETED To be done later
}
