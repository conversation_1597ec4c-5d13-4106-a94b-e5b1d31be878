extend type Mutation {
    createDemoCycles(value: CreateDemoCyclesInput!): [PayrollCycle!]! @authorize(operations: ["create.operations.payroll-cycle"])
    updateDemoCycles(value: UpdateDemoCyclesInput!): [PayrollCycle!]! @authorize(company: ["update.company.payroll-cycle"])
}

input CreateDemoCyclesInput {
    companyIds: [ID!]
    countries: [CountryCode!]
    untilDate: Date!
}

input UpdateDemoCyclesInput {
    payrollCycleIds: [ID!]!
    status: PayrollCycleStatus!
}
