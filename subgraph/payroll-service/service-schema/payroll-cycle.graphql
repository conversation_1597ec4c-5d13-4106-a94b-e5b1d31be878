extend type Query {
    payrollCycle(id: ID!): PayrollCycle @authorize(operations: ["view.operations.payroll-cycle"], partner: ["view.partner.payroll-cycle"])
    payrollCycles(filter: PayrollCycleFilter, pageRequest: PageRequest): PayrollCyclesResponse @authorize(operations: ["view.operations.payroll-cycle"], partner: ["view.partner.payroll-cycle"])
    cycleContracts(cycleId: ID!, filter: CycleContractsFilter, pageRequest: PageRequest): CycleContractSnapshotsResponse @authorize(operations: ["view.operations.payroll-cycle"])
    payrollDocuments(cycleId: ID!, filter: CycleContractsFilter): PayrollDocumentsResponse @authorize(operations: ["view.operations.payroll-cycle"])
    """Mainly for "Support 360" feature"""
    contractSnapshots(filter: ContractSnapshotFilter, pageRequest: PageRequest): CycleContractSnapshotsResponse @authorize(operations: ["view.operations.payroll-cycle"])
    payrollCycleForDomain(domainId: ID, contractId: ID!, referenceDate: Date, domain: PayrollInputDomainType!, returnClosestToReferenceDateUpcomingCutOff: Boolean!): PayrollCycle @authorize(operations: ["view.operations.payroll-cycle"], company: ["view.company.payroll-cycle"])
    payrollCyclesForCalendar(filter: PayrollCalendarFilters!): [PayrollCycle!]! @authorize(operations: ["view.operations.payroll-cycle"], company: ["view.company.payroll-cycle"])
    eligibleCountriesForEor(payrollCycleEntityTypes: [PayrollCycleEntityType!]!): [PayrollCountryEntityType!]! @authorize(operations: ["view.operations.payroll-cycle"], company: ["view.company.payroll-cycle"])
    eligibleEntitiesForPeo(payrollCycleEntityType: PayrollCycleEntityType!): [PayrollCycleEntity!]! @authorize(operations: ["view.operations.payroll-cycle"], company: ["view.company.payroll-cycle"])
    payrollCycleReportDownload(filter: PayrollCycleReportFilter!): PayrollFile @authorize(operations: ["view.operations.payroll-cycle"])
    payrollCycleReports(payrollCycleID: ID!): [CompanyPayrollCycleReport!]! @authorize(operations: ["view.operations.payroll-cycle"])
}

input ContractSnapshotFilter {
    contractIds: [ID!]
    payrollContractTypes: [PayrollContractType!]
    excludedPayrollCycleStatuses: [PayrollCycleStatus!]
}

extend type Mutation {
    payrollCycleCreate(value: PayrollCycleInput!): PayrollCycle @authorize(operations: ["create.operations.payroll-cycle"])

    payrollOffCycleCreate(value: PayrollOffCycleCreateInput!): PayrollCycle @authorize(operations: ["create.operations.payroll-off-cycle"])

    bulkCreatePayrollCycles(value: PayrollCycleBulkCreateInput!): [PayrollCycle] @authorize(operations: ["create.operations.payroll-cycle"])
    bulkUpdatePayrollCycles(value: PayrollCycleUpdateInput!): [PayrollCycle] @authorize(operations: ["update.operations.payroll-cycle"]) @deprecated(reason: "use updatePayrollCycle in payroll-orchestrator instead")

    payrollCycleUpdateBulkAsSuspended(ids: [ID]!): [PayrollCycle] @authorize(operations: ["update.operations.payroll-cycle.status"])
    payrollCycleUpdateBulkAsClosed(ids: [ID]!): [PayrollCycle] @authorize(operations: ["update.operations.payroll-cycle.status"])

    refreshPayrollSnapshot(payrollCycleId: ID!) : TaskResponse @authorize(operations: ["create.operations.payroll-cycle"])

    refreshPayrollSnapshots(ids: [ID]!): TaskResponse @authorize(operations: ["create.operations.payroll-cycle"])
    refreshActivePayrollSnapshots: TaskResponse @authorize(operations: ["create.operations.payroll-cycle"])

    refreshPayrollSnapshotDomainForce(value: PayrollSnapshotRefreshDomainByIdInput!): TaskResponse @authorize(operations: ["update.operations.payroll-cycle"]) @deprecated(reason: "use updateDomainItems instead")
    assignPayrollSnapshotDomain(value: PayrollSnapshotAssignDomainInput!): TaskResponse @authorize(operations: ["update.operations.payroll-cycle"])

    confirmPayrollInput(payrollCycleId: ID!) : PayrollCycle @authorize(operations: ["update.operations.payroll-cycle.status"])
    moveToNextStatus(payrollCycleId: ID!, action: PayrollWorkflowAction) : PayrollCycle @authorize(operations: ["update.operations.payroll-cycle.status"])

    payrollCycleStatusBulkUpdate(ids: [ID!], status: PayrollCycleStatus!, publishEvent: Boolean): [PayrollCycle] @authorize(operations: ["update.operations.payroll-cycle.status"])

    """Use for manually trigger only"""
    reGeneratePayrollCycleInputFile(payrollCycleId: ID!): TaskResponse @authorize(operations: ["create.operations.payroll-cycle"])

    # For Ops usage only
    payrollInputAuditTrailsRemove(input: PayrollInputAuditTrailsRemoveInput!): TaskResponse @authorize(operations: ["remove.operations.payroll.input.audit-trail"])

    # For Admin manually update only
    updateDomainItems(domainIds: [String!]!, domain: PayrollInputDomainType!, force: Boolean): TaskResponse @authorize(operations: ["update.operations.payroll-cycle"])
}

input PayrollOffCycleCreateInput {
    primaryPayrollCycleId: ID!
    cutOffToDate: Date!
    payDate: Date!
}

input PayrollCycleReportFilter {
    payrollCycleId: ID!
    reportType: CompanyPayrollReport!
}

enum PayrollWorkflowAction {
    UploadOutputReport,
    SendForReview,
    ReviewOutputReport,
    ConfirmOutputReport,
    ConfirmPayslips,
    SendToCustomer,
    ConfirmInput,
    InitiatePayrollClosure,
}

input CycleContractsFilter {
    memberNames: [String!]
    payrollContractTypes: [PayrollContractType!]
    hasExceptions: Boolean
    contractIds: [ID!]
    contractStatuses: [ContractStatus!]
    startDateRange: DateRange
    activationDateRange: DateRange
    companyNames: [String!]
    endDateRange: DateRange
    payrollFormStatuses: [PayrollFormsUploadStatus!]
    excludedPayrollContractTypes: [PayrollContractType!]
    payrollContractExceptionTypes: [PayrollContractExceptionType!]
    excludedPayrollContractExceptionTypes: [PayrollContractExceptionType!]
}

type CycleContractSnapshotsResponse {
    contracts: [ContractSnapshot!]
    pageResult: PageResult
}

type ContractSnapshot @key(fields: "id") {
    id: ID
    contractId: ID
    """Related payrollCycle of the contractSnapshot. Needs to be a child fetcher with data loader to avoid N+1"""
    payrollCycle: PayrollCycle
    memberName: String
    companyName: String
    contractStatus: ContractStatus
    contractStartDate: Date
    contractActivatedDate: Date
    lastWorkingDate: Date
    submittedDate: Date
    payrollFormUploadStatus: PayrollFormsUploadStatus
    payrollContractType: [PayrollContractType] @authorize(operations: ["view.operations.payroll-cycle"], company: ["view.company.payroll-cycle"], partner: ["view.partner.payroll-cycle"])
    exceptions: [PayrollContractException] @authorize(operations: ["view.operations.payroll-cycle"], company: ["view.company.payroll-cycle"], partner: ["view.partner.payroll-cycle"])
    expenses: [ExpenseSnapshot] @authorize(operations: ["view.operations.expenses|update.operations.expenses"])
    paySupplements: [PaySupplementSnapshot] @authorize(operations: ["view.operations.paysupplement|update.operations.paysupplement"])
    # not included pay supplements = not eligible for this payroll or excluded from this payroll
    notIncludedPaySupplements: [PaySupplement!]! @authorize(operations: ["view.operations.paysupplement|update.operations.paysupplement"])
    employeeId: String
    compensations: [CompensationSnapshot!]
}

type PayrollOutputDataVariance {
    grossSalaryVariance: Float
    expensesVariance: Float
    employeeDeductionsVariance: Float
    netPayVariance: Float
    employerContributionsVariance: Float
    totalPayrollCostVariance: Float
    employeeContributionVariance: Float
    employeeTaxesVariance: Float
}

type EmployeeDataChanges {
    compensation: Int
    bankDetails: Int
}

type InputHeadcountSummary {
    previousCount: Int
    newJoiner: Int
    leaver: Int
    totalCount: Int
}

type OutputHeadcountSummary {
    active: Int
    starter: Int
    leaver: Int
    total: Int
}

type PayrollOutputSummary {
    grossSalary: Float!
    expenses: Float!
    employeeDeductions: Float!
    netPay: Float!
    employerContributions: Float!
    totalPayrollCost: Float!
    employeeContributions: Float!
    employeeTaxes: Float!
}

type PaySupplementSummary {
    processing: [ID!]
    paid: [ID!]
    revoked: [ID!]
    toBePaid: [ID!]
}

type ExpenseSummary {
    processing: [ID!]
    paid: [ID!]
    revoked: [ID!]
    toBePaid: [ID!]
}

type PayslipSummary {
    totalMembers: Int
    readyToPublish: Int
    published: Int
}

input PayrollCycleInput {
    payrollMonth: MonthYearInput!
    entityId: ID
    configId: ID!
}

input PayrollCycleBulkCreateInput {
    untilDate: Date!
    configIds: [ID!]!
}

type PayrollCyclesResponse {
    page: PageResult
    cycles: [PayrollCycle]
}

input PayrollCycleFilter {
    ids: [ID]
    countries: [CountryCode!]
    payrollCycleEntityType: PayrollCycleEntityType @deprecated(reason: "Use payrollCycleEntityTypes instead")
    payrollCycleEntityTypes: [PayrollCycleEntityType!]
    dateRange: DateRange
    frequencies: [PayFrequency!]
    statuses: [PayrollCycleStatus!]
    payDate: DateRange
    createdBy: ID
    entityId: ID
    contractIds: [ID!]
    excludedStatuses: [PayrollCycleStatus!]    # Filter for all statuses except the ones specified here
    payrollMonth: MonthYearInput
    payrollCycleTypes: [PayrollCycleType!]
    primaryCycleIds: [ID!]
    partnerName: String @deprecated
    eorPartnerIds: [ID!]
}

enum PayrollCycleStep {
    INITIATION,
    INPUT_GENERATION,
    PAYROLL_PROCESSING,
    PAY_SLIPS, @deprecated (reason: "It's merged with CLOSURE")
    CLOSURE
}

type PayrollCycleWorkflow {
    currentStep: PayrollCycleStep
    allSteps: [PayrollCycleStep]
}

type OpsPayslipUploadWorkflow {
    currentStep: OpsPayslipUploadWorkflowStep
    allSteps: [OpsPayslipUploadWorkflowStep]
}

enum OpsPayslipUploadWorkflowStep {
    PAYSLIPS_VALIDATION_IN_PROGRESS
    PAYSLIPS_VALIDATION_REPORT
    PAYSLIPS_VALIDATION_REPORT_DOWNLOADED
    PAYSLIPS_CONFIRMED
}

type PayrollCycleEntity {
    id: ID
    name: String
    entityType: PayrollCycleEntityType
    company: Company @authorize(operations: ["view.operations.payroll-cycle"], company: ["view.company.payroll-cycle"], partner: ["view.partner.payroll-cycle"])  # only available for PEO cycles
}

enum PayrollCycleEntityType {
    EOR
    PEO
    EOR_PARTNER
    GLOBAL_PAYROLL
    EOR_MULTIPLIER_PAYROLL
    EOR_PARTNER_PAYROLL
    CONTRACTOR_PAYROLL
}

type PayrollDocumentsResponse {
    documents: [PayrollDocument!]
}

type PayrollDocument {
    id: ID
    downloadUrl: String!
    fileName: String!
}

input PayrollCycleToDates {
    cutOffTo: Date,
    contractStartDateTo: Date,
    contractEndDateTo: Date,
    compensationDateTo: Date,
    timeSheetDateTo: Date,
    paySupplementTo: Date,
    expenseTo: Date,
    timeOffTo: Date,
    deductionTo: Date,
    memberChangesTo: Date,
    payDate: Date,
    approvePayrollReportDeadline: Date,
    submitPayrollInputDeadline: Date,
    approvePayrollInputDeadline: Date,
    payrollProcessingDeadline: Date,
    payrollCompleteDeadline: Date,
}

input PayrollCycleToDatesInput{
    cycleId: ID!,
    payrollCycleToDates: PayrollCycleToDates!,
}

input PayrollCycleUpdateInput{
    payrollCycleToDatesInput: [PayrollCycleToDatesInput!]!,
}

input PayrollInputAuditTrailsRemoveInput {
    entityType: PayrollInputAuditTrailEntityType! # For now removal is only support for PAY_SUPPLEMENT and EXPENSE
    entityIds: [ID!]!
}

enum PayrollInputAuditTrailEntityType {
    PAY_SUPPLEMENT
    EXPENSE
}

input PayrollSnapshotRefreshDomainByIdInput {
    domain: PayrollInputDomainType!,
    domainIds: [ID!]!
}

input PayrollSnapshotAssignDomainInput {
    domain: PayrollInputDomainType!,
    domainIds: [ID!]!
    payrollCycleId: ID # null will put the domain into staging area
}

input PayrollCalendarFilters {
    payFrequency: PayFrequency!
    pageNumber: Int
    payrollCycleEntityTypes: [PayrollCycleEntityType!]
    legalEntityId: ID
    country: CountryCode
    companyId: ID
    companyPayrollCycleId: ID
    payrollPartnerId: ID
    payrollCycleId: ID
}

type PayrollCountryEntityType {
    countryCode: CountryCode!
    entityType: PayrollCycleEntityType!
}
