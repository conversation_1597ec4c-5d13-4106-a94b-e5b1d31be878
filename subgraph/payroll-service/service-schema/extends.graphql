type Company @key(fields: "id") @extends {
    id: ID @external
    name: String
    payroll (payrollMonth: MonthYearInput): [CompanyPayroll] @authorize(operations: ["view.operations.company.payroll"], company: ["view.company.payroll"])
    paySupplements(paySupplementId: ID): [PaySupplement] @authorize(company: ["view.company.paysupplement"], operations: ["view.operations.paysupplement"])
    companyPayrollCycle(filter: CompanyPayrollCycleFilter!): [CompanyPayrollCycle!] @authorize(company: ["view.company.payroll-cycle"]) @deprecated(reason: "Use companyPayrollCycles instead")
    companyPayrollCycles(filter: CompanyPayrollCycleFilter!): [CompanyPayrollCycle!] @authorize(company: ["view.company.payroll-cycle"])
    statutoryReports(filter: CustomPayrollReportFilter!): [CustomPayrollReportDetails!] @authorize(company: ["view.company.statutory-report"])
}

type Contract @key(fields: "id") @extends {
    id: ID @external
    payrollPartner: ContractPayrollPartner @authorize(operations: ["view.operations.company.payroll|use.operations.payroll-report|use.operations.payroll|use.operations.contract-onboarding"])
    # statuses will override the status filter
    paySupplements(id: ID, status: PaySupplementStatus, fromDate: DateTime, toDate: DateTime, statuses: [PaySupplementStatus!]): [PaySupplement]
    payrolls (memberPayId: ID, payrollMonth: MonthYearInput): [MemberPay] @authorize(operations: ["view.operations.company.payroll|use.operations.payroll-report"])  # CompanyMemberPay, PartnerMemberPay
    payslips(payrollMonth: MonthYearInput, filters: PaySlipFilters): [Payslip] @authorize(company: ["view.company.payslip"], operations: ["view.operations.payslip"], member: ["view.member.payslip"])

    # whether this contract has been picked up as new contract (sent to the partner via Payroll Updates) for the given month (payslipMonth)
    # in other words, check if there will be any payslip of the given month (payslipMonth). Because if it's picked up too late, the payroll will be pushed to next month.
    # payslipMonth: usually the current or previous month. Decided by FE (e.g. `2021-09` if today is before 2021-10-21, `2021-10` if today is 2021-10-21 or later)
    # the purpose is not to show the payslip messages ("Your payslip will be available..."/"There is a delay...") if "pushed to next month" case happens
    isPickedUpForPayroll(payslipMonth: MonthYearInput!): Boolean @authorize(company: ["use.company.payslips"], member: ["use.member.payslips"])
}

type Expense @key(fields: "id") @extends {
    id: ID @external
}

type PartnerUser @key(fields: "id") @extends {
    id: ID @external
}

type MemberChangeRequest @key(fields: "id") @extends {
    id: ID! @external
}

type OperationsUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type CompanyUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type EmergencyPointOfContact implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type Member implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type PayrollCyclePayment @key(fields: "id") @extends {
    id: ID! @external
}

type Document @key(fields: "id") @extends {
    id: ID @external
}

type PerformanceReview @key(fields: "id") @extends {
    id: ID! @external
    status: PerformanceReviewStatus @external

    payrollCycle: PayrollCycle @requires(fields: "status")  @authorize(company: ["view.company.payroll-cycle"], member: ["use.member.payroll"], operations: ["view.operations.payroll-cycle"], partner: ["view.partner.payroll-cycle"])  # the cycle that has picked up this performanceReview for payroll
}

# Copied from performance.graphql. Both must be identical.
enum PerformanceReviewStatus {
    DRAFT,
    SENT_FOR_APPROVAL, # For company signatory, name kept as SENT_FOR_APPROVAL for backward compatibility
    SENT_FOR_MEMBER_APPROVAL,
    APPROVED,
    DECLINED,
    ACTIVATED,
    SENT_TO_OPS
}

enum PayrollContractType {
    ACTIVE,
    NEW_HIRE,
    TERMINATION
}

enum LegalDocumentStatus {
    APPROVED
    REJECTED
    SUBMITTED
}
