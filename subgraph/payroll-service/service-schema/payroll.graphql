extend type Query {
    companyPayroll(companyId: ID, payrollMonth: PayrollMonthInput): CompanyPayroll @deprecated @authorize(company: ["view.company.payroll"], operations: ["view.operations.company.payroll"]) #Use the Company to get this
    """payrollReports use to get the list of Payroll Report"""
    payrollReports(
        """PayrollFilter is the filter of this query, support filter by Month and Year"""
        payrollFilter: PayrollFilter,
        """PageRequest is the pagination parameter"""
        pageRequest: PageRequest
    ):
    CompanyMemberPayResult @authorize(operations: ["view.operations.company.payroll|use.operations.payroll-report"])
    payrollReportDownload(filter: CompanyPayrollReportExportInput!): DocumentReadable @authorize(company: ["use.company.reports"], operations: ["use.operations.payroll-report"])
    payrollReportDownloadForCustomer(input: String!): DocumentReadable @authorize(company: ["use.company.reports"]) # Temporary query to support the EOR G2N reports in centralized reports section. Input is a JSON of filters
    payrollParserConfig(filter: PayrollParserConfigFilter): PayrollParserConfig @authorize(operations: ["view.operations.payroll.parser-config"])
    payrollReportGeneratorConfig(filter: PayrollReportGeneratorConfigFilter): ReportGeneratorConfig @authorize(operations: ["view.operations.payroll.parser-config"])
}

extend type Mutation {
    memberPayBackfillBillingCurrency(memberPayIds: [ID!]): TaskResponse @authorize(operations: ["backfill.operations.memberpay"])

    asyncPayrollInputFilePartnerBulkDownload(filter: PayrollCycleFilter): TaskResponse @authorize(operations: ["download.operations.input-file"]) @deprecated(reason: "no longer support, use payrollInputFilePartnerBulkDownloadAsync instead")

    asyncPayrollInputFileDownload(payrollCycleId: ID): TaskResponse @authorize(operations: ["download.operations.input-file"]) @deprecated(reason: "no longer support, use payrollInputFileDownloadAsync instead")

    updatePayrollParserConfig(input: PayrollParserConfigInput!): PayrollParserConfig @authorize(operations: ["update.operations.payroll.parser-config"])

    updatePayrollReportGeneratorConfig(input: PayrollReportGeneratorConfigInput!): ReportGeneratorConfig @authorize(operations: ["update.operations.payroll.parser-config"])
}

input PayrollMonthInput {
    month: Int
    year: Int
    cycle: Int
}

input PayrollMonthRangeInput {
    from: PayrollMonthInput!
    to: PayrollMonthInput!
}

input PayrollFilter {
    """this filter allow client to filter by month and year"""
    payrollMonth: PayrollMonthInput # @deprecated use payrollMonths instead
    payrollMonths: PayrollMonthRangeInput
    country: CountryCode
    """Wildcard search"""
    companyName: String
    """Wildcard search"""
    memberName: String
    contractId: ID
    companyId: ID
    payrollCycleId: ID
    frequency: PayFrequency
    payDateRange: DateRange
    payPeriodDateRange: DateRange
    entityTypes: [PayrollCycleEntityType!]
}

input CompanyPayrollReportExportInput {
    contractId: ID
    countryCode: CountryCode
    memberNameSearchTerm: String # to search by name, strategy: contain-ignore-case...
    payrollMonth: PayrollMonthInput @deprecated # use payrollMonths instead
    payrollMonths: PayrollMonthRangeInput
    compare: ReportComparisonInput
    reportType: ExportPayrollReportType
    companyId: ID
    payrollCycleId: ID
    frequency: PayFrequency
    payDateRange: DateRange
    payPeriodDateRange: DateRange
    entityTypes: [PayrollCycleEntityType!]
}

input ReportComparisonInput {
    payrollMonth: PayrollMonthInput!
}

input PayrollParserConfigInput {
    configString: String
    countryCode: CountryCode
}

input PayrollReportGeneratorConfigInput {
    configString: String
    countryCode: CountryCode
}

input PayrollParserConfigFilter {
    countryCode: CountryCode
}

input PayrollReportGeneratorConfigFilter {
    countryCode: CountryCode
}

type PayrollParserConfig {
    id: ID
    configString: String
    countryCode: CountryCode
    createdOn: DateTime
    createdBy: ID
    updatedOn: DateTime
    updatedBy: ID
}

type ReportGeneratorConfig {
    id: ID
    configString: String
    countryCode: CountryCode
    createdOn: DateTime
    createdBy: ID
    updatedOn: DateTime
    updatedBy: ID
}

interface Payroll {
    id: ID
    month: PayrollMonth
    memberPays: [MemberPay]
    amountTotalCost: Float
}

type CompanyMemberPayResult {
    data: [CompanyMemberPay] @authorize(
        company: ["view.company.payroll"],
        operations: ["view.operations.company.payroll|use.operations.payroll-report"]
    )
    """countryCount is the total number of payroll country in the whole result"""
    countryCount: Int
    companyCount: Int
    employeeCount: Int
    pageResult: PageResult
}

type PayrollMonth {
    month: Int
    year: Int
    cycle: Int
}

interface MemberPay @key(fields: "id"){
    id: ID
    partnerPayroll: PartnerPayroll
    companyPayroll: CompanyPayroll @authorize(operations: ["view.operations.company.payroll"], company: ["view.company.payroll"])
    contract: Contract
    status: MemberPayStatus
    currency: CurrencyCode
    billingCurrency: CurrencyCode
    contributions: [PayComponent]
    deductions: [PayComponent]
    clientDeductions: [PayComponent]
    additional: [AdditionalPayComponent]        # TODO by @SquadA or @SquadB....
    payslip: Payslip @authorize(operations: ["use.operations.payslips"], company: ["use.company.payslips"], member: ["use.member.dashboard"])
    paySupplements: [PaySupplement]

    amountGross: Float
    totalEmployeeDeductions: Float
    totalEmployeeContributions: Float
    totalTaxes: Float
    amountNet: Float
    totalEmployerContributions: Float
    totalExpenseAmount: Float
    amountTotalCost: Float

    totalContributionAmount: Float
    totalDeductionsAmount: Float
    totalSupplementAmount: Float

    totalAllowanceAmount: Float
    totalBonusAmount: Float
    totalCommissionAmount: Float

    # This represents the aggregated value of Gross Pay Breakdown. This excludes allowance, bonus and commission amounts
    # Note: This is different from amountGross.
    aggregatedGross: Float

    fxRateApplied: Float
    payrollCycle: PayrollCycle @authorize(operations: ["view.operations.payroll-cycle"], company: ["view.company.payroll-cycle"], partner: ["view.partner.payroll-cycle"])

    severancePayAccruals: [PayComponent]

    billingException: Boolean

    finalNetPayAmount: Float
    totalEmployerDeductions: Float
}

type PayComponent {
    name: String
    value: Float
    type: PayComponentType
}

type AdditionalPayComponent {
    name: String
    value: String
    type: PayComponentType
}

enum MemberPayStatus {
    NEW
    PENDING
    PAID
    PAYSLIP_GENERATION_IN_PROGRESS
    PAYSLIP_GENERATION_SUCCESS
    PAYSLIP_GENERATION_FAILED
}

enum PayComponentType {
    CONTRIBUTIONS
    DEDUCTIONS,
    OTHER
}


enum ExportPayrollReportType {
    SUMMARIZED,
    DETAILED,
    COUNTRY_SUMMARIZED,
    COUNTRY_DETAILED
}

type PayrollFile {
    id: ID
    name: String
    downloadUrl: String
    uploadUrl: String
    extension: String
    contentType: String

    createdOn: DateTime
    createdBy: ID
    updatedOn: DateTime
    updatedBy: ID
}

type ValidationResponse {
    succeeded: Boolean!
    results: [ValidationResult!]
}

type ValidationResult {
    message: String!
    type: ValidationResultType!
}

enum ValidationResultType {
    ERROR
    WARN
    INFO
}
