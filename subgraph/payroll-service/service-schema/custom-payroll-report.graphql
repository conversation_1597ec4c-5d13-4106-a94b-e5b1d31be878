extend type Query {
    customPayrollReportConfigs(filter: CustomPayrollReportConfigFilter!): [CustomPayrollReportConfig!]! @authorize(operations: ["view.operations.custom-payroll-report-config"])

    customPayrollReport(id: ID!): CustomPayrollReportDetails! @authorize(operations: ["view.operations.custom-payroll-report"])
    customPayrollReports(filter: CustomPayrollReportFilter!, pageRequest: PageRequest): CustomPayrollReportsResponse! @authorize(operations: ["view.operations.custom-payroll-report"])
    downloadCustomPayrollReport(id: ID!): DocumentReadable! @authorize(operations: ["view.operations.custom-payroll-report"], company: ["view.company.statutory-report"])
}

extend type Mutation {
    customPayrollReportConfigsCreate(values: [CustomPayrollReportConfigCreateInput!]!): [CustomPayrollReportConfig!]! @authorize(operations: ["create.operations.custom-payroll-report-config"])
    customPayrollReportConfigsUpdate(values: [CustomPayrollReportConfigUpdateInput!]!): [CustomPayrollReportConfig!]! @authorize(operations: ["update.operations.custom-payroll-report-config"])

    customPayrollReportCreate(values: [CustomPayrollReportCreateInput!]!): [CustomPayrollReportDetails!]! @authorize(operations: ["create.operations.custom-payroll-report"])
    customPayrollReportFileUpdate(input: CustomPayrollReportFileUpdateInput!): CustomPayrollReportDetails! @authorize(operations: ["create.operations.custom-payroll-report"])
}

type CustomPayrollReportConfig {
    id: ID!
    status: CustomPayrollReportConfigStatus!
    country: CountryCode!
    reportCode: String!
    reportName: String!
    reportFormat: CustomPayrollReportFormat
    reportCategory: CustomPayrollReportCategory!
    reportFrequency: CustomPayrollReportFrequency!
}

type StatutoryReportDetails implements CustomPayrollReportDetails {
    id: ID!
    company: Company
    employingLegalEntity: LegalEntity
    country: CountryCode
    financialYearFrom: Date
    financialYearTo: Date
    reportCategory: CustomPayrollReportCategory!
    reportFrequency: CustomPayrollReportFrequency
    month: MonthYear
    quarter: Int
    files: [CustomPayrollReportFile!]!
    uploadedCount: UploadedFileCount
}

type CustomPayrollReportFile {
    config: CustomPayrollReportConfig!
    file: PayrollFile!
    # A separate uploadUrl added because PayrollFile is a generic type and it cannot be used with ABAC as of now
    uploadUrl: String @authorize(operations: ["create.operations.custom-payroll-report"])
}

type UploadedFileCount {
    uploaded: Int!
    total: Int!
}

type CustomPayrollReportsResponse {
    reports: [CustomPayrollReportDetails!]!
    page: PageResult!
}

input CustomPayrollReportCreateInput {
    companyId: ID!
    employingLegalEntityId: ID!
    financialYearFrom: Date!
    financialYearTo: Date!
    reportCategory: CustomPayrollReportCategory!
    reportFrequency: CustomPayrollReportFrequency!
    month: MonthYearInput
    quarter: Int
    files: [CustomPayrollReportFileInput!]!
}

input CustomPayrollReportFileInput {
    configId: ID!
    fileName: String!
}

input CustomPayrollReportFileUpdateInput {
    reportId: ID!
    files: [CustomPayrollReportFileInput!]!
}

input CustomPayrollReportConfigCreateInput {
    country: CountryCode!
    reportCode: String!
    reportName: String!
    reportFormat: CustomPayrollReportFormat
    reportCategory: CustomPayrollReportCategory!
    reportFrequency: CustomPayrollReportFrequency!
}

input CustomPayrollReportConfigUpdateInput {
    id: ID!
    status: CustomPayrollReportConfigStatus!
}

input CustomPayrollReportConfigFilter {
    country: CountryCode
    reportCategory: CustomPayrollReportCategory
    reportFrequency: CustomPayrollReportFrequency
}

input CustomPayrollReportFilter {
    companyIds: [ID!]!
    employingLegalEntityIds: [ID!]!
    countries: [CountryCode!]!
    financialYearFrom: DateRange
    financialYearTo: DateRange
}

interface CustomPayrollReportDetails {
    id: ID!
    company: Company
    employingLegalEntity: LegalEntity
    country: CountryCode
    financialYearFrom: Date
    financialYearTo: Date
    reportCategory: CustomPayrollReportCategory!
    reportFrequency: CustomPayrollReportFrequency
    month: MonthYear
    quarter: Int
    files: [CustomPayrollReportFile!]!
}

enum CustomPayrollReportCategory {
    STATUTORY
}

enum CustomPayrollReportConfigStatus {
    ACTIVE
    INACTIVE
}

enum CustomPayrollReportFormat {
    CSV
    XLS
    PDF
    TXT
}

enum CustomPayrollReportFrequency {
    ANNUALLY
    QUARTERLY
    MONTHLY
    YTD
}
