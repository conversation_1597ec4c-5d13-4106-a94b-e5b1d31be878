extend type Query {
    payslips(filters: PaySlipFilters, pageRequest: PageRequest): PayslipsResponse @authorize(operations: ["use.operations.payslips"])
}

extend type Mutation {
    # Validate uploaded payslip PDFs in the S3
    validatePayslips(payrollCycleId: ID!): TaskResponse @authorize(operations: ["validate.operations.payslip"])
    # Mark the payslips as VERIFIED and send to member
    publishPayslips(value: PayslipPublishInput!): PublishPayslipsResponse @authorize(operations: ["publish.operations.payslip"])

    # Replace or upload new payslips file directly without file content validation. This will not send the email to the member.
    forceUploadPayslip(value: PayslipForceUploadInput!, file: Upload!): Payslip @authorize(operations: ["update.operations.payslip"])

    # Update old payslips fileName from documentId
    # Temp usage only! Remove once the payslip migration is done: https://app.clickup.com/t/86ctv60vg
    migrateOldPayslips: TaskResponse @authorize(operations: ["update.operations.payslip"])

    confirmPayslips(value: PayslipConfirmInput!): ConfirmPayslipsResponse @authorize(operations: ["confirm.operations.payslip"])

    # Regenerate payslip zip file for PEO cycles
    regeneratePayslipZip(payrollCycleIds: [ID!]!): TaskResponse @authorize(operations: ["update.operations.payslip"])

    moveToNextPayslipUploadWorkflowStep(payrollCycleId: ID!): PayrollCycle @authorize(operations: ["update.operations.payslip"])
    companyPayrollPayslipPublish(companyPayrollCycleId: ID!) : PublishPayslipsResponse @authorize(company: ["update.company.payslip-workflow"])
}

type PayslipsResponse {
    page: PageResult
    payslips: [Payslip]
}

input PaySlipFilters {
    contractIds: [ID!]
    payslipId: ID
    fromDate: DateTime
    toDate: DateTime
    status: PayslipStatus
    payrollCycleIds: [ID!]
    csmUserId: ID
    payrollMonth: MonthYearInput
    countries: [CountryCode!]
    companyIds: [ID!]
    eorPartnerIds: [ID!]
    isMultiplierEorPartner: Boolean
    memberNameContains: String
    documentTypes: [DocumentType!]
}

input PayslipConfirmInput {
    payrollCycleId: ID!
}

type ConfirmedPayslipsSummary {
    totalMemberCount: Int           # Number of members for the payroll cycle
    confirmedPayslipsCount: Int     # Number of CONFIRMED payslips
    nonConfirmedPayslipsCount: Int  # Number of non-CONFIRMED payslips
}

type ConfirmPayslipsResponse{
    confirmPayslipsSummary: ConfirmedPayslipsSummary
    confirmedPayslips: [Payslip]
}

input PayslipPublishInput {
    payrollCycleId: ID!
    contractIds: [ID!]              # Should be used with payrollCycleId
}

type PublishPayslipsSummary {
    publishedPayslipsCount: Int     # Number of VERIFIED payslips
    nonPublishedPayslipsCount: Int  # Number of non-VERIFIED / not uploaded payslips
}

type PublishPayslipsResponse {
    publishPayslipsSummary: PublishPayslipsSummary
    publishedPayslips: [Payslip]
}

input PayslipForceUploadInput {
    contractId: ID!
    payrollCycleId: ID!
}

type Payslip @key(fields: "id") {
    id: ID
    month: PayrollMonth
    payrollCycle: Int @deprecated(reason: "Use payCycle instead. Keeping this field for backward compatibility")
    netAmount: Float
    currencyCode: CurrencyCode
    createdOn: DateTime
    file: FileLink @authorize(operations: ["use.operations.payslips"], company: ["use.company.payslips"], member: ["use.member.dashboard"]) @deprecated(reason: "Use payslipDocument instead. Keeping this field for backward compatibility")
    # TODO Delete
    document: Document @authorize(operations: ["use.operations.payslips"], company: ["use.company.payslips"], member: ["use.member.dashboard"]) @deprecated(reason: "Use payslipDocument instead. Keeping this field for backward compatibility")
    payslipDocument: PayslipDocument @authorize(operations: ["use.operations.payslips"], company: ["use.company.payslips"], member: ["use.member.dashboard"])
    status: PayslipStatus
    payCycle: PayrollCycle @authorize(company: ["view.company.payroll-cycle"], operations: ["view.operations.payroll-cycle"], member: ["use.member.payroll"])
    contract: Contract
    documentType: DocumentType
}

type PayslipDocument {
    name: String
    downloadUrl: String
}

enum PayslipStatus {
    GENERATION_IN_PROGRESS
    GENERATION_SUCCESSFUL
    GENERATION_FAILED
    UPLOADED                # Payslip was uploaded. Might have errors. https://app.clickup.com/t/86cub8buq
    VALIDATED               # Validation success without any errors
    READY_TO_PUBLISH        # Ops confirm
    VERIFIED                # Marking as verified after calling the 'PartnerPayrollPayslipSend' mutation. Only verified payslips will be shown in the platform
    PUBLISHED               # Not being used for now. Will be used once we deprecate VERIFIED status
}

type PayslipUploadDetails {
    uploadUrl: String                               # URL for payroll-upload folder with payrollCycleID
    validationRequest: PayslipValidationRequest     # Returns null if there are no validations have ran, else latest request
    confirmed: ConfirmedPayslipsSummary
}

type PayslipValidationRequest {
    id: ID
    status: PayslipValidationRequestStatus
    validationResults: [PayslipValidationResult]    # Returns empty if validation is not yet finished
    validationReport: PayrollFile                   # Returns null when report is not ready
}

enum PayslipValidationRequestStatus {
    INITIATED
    ENDED
}

type PayslipValidationResult {
    contractId: ID
    fileName: String
    errorTypes: [PayslipValidationErrorType]
}

enum PayslipValidationErrorType {
    NO_MATCHING_FILE
    FILE_READ_ERROR
    CONTRACT_ID_MISMATCH
    MEMBER_FIRST_NAME_MISMATCH
    MEMBER_LAST_NAME_MISMATCH
    CONTRACT_ID_SKIPPED
}

enum DocumentType {
    PAYSLIP
    TAXSLIP
    TAX_DOCUMENT
    OTHER
}
