extend type Query {
    fileDownloadHistory(filter: FileDownloadHistoryFilter!): FileDownloadHistoryResponse @authorize(operations: ["download.operations.input-file"])
    payrollFileDownload(fileDownloadId: ID!) : PayrollFileDownloadResponse @authorize(company: ["view.company.payroll-cycle"])
}

extend type Mutation {
    payrollInputFilePartnerBulkDownloadAsync(filter: PayrollCycleFilter!): InputFileDownload @authorize(operations: ["download.operations.input-file"])
    payrollInputFileDownloadAsync(payrollCycleId: ID!): InputFileDownload @authorize(operations: ["download.operations.input-file"])
}

input FileDownloadHistoryFilter {
    statuses: [PayrollFileDownloadStatus!]
}

type InputFileDownload implements FileDownload {
    id: ID
    type: PayrollFileDownloadType
    key: String
    description: String
    status: PayrollFileDownloadStatus
    document: Document
    createdOn: DateTime
    updatedOn: DateTime
    payrollFile: PayrollFile # payroll file is not supported for legacy input file generation
    cycles: [PayrollCycle!] @authorize(operations: ["view.operations.payroll-cycle"])
}

type FileDownloadHistoryResponse {
    fileDownloads: [FileDownload!]
}

type PayrollFileDownloadResponse implements FileDownload {
    id: ID
    type: PayrollFileDownloadType
    key: String
    description: String
    status: PayrollFileDownloadStatus
    document: Document @deprecated(reason: "use `payrollFile` instead")
    createdOn: DateTime
    updatedOn: DateTime
    payrollFile: PayrollFile
}

type PayrollConsolidatedReportDownloadResponse implements FileDownload {
    id: ID
    type: PayrollFileDownloadType
    key: String
    description: String
    status: PayrollFileDownloadStatus
    document: Document @deprecated(reason: "use `payrollFile` instead")
    createdOn: DateTime
    updatedOn: DateTime
    payrollFile: PayrollFile
}

interface FileDownload {
    id: ID
    type: PayrollFileDownloadType
    key: String
    description: String
    status: PayrollFileDownloadStatus
    document: Document @deprecated(reason: "use `payrollFile` instead")
    createdOn: DateTime
    updatedOn: DateTime
    payrollFile: PayrollFile
}

enum PayrollFileDownloadType {
    INPUT_FILE
    COMPANY_PAYROLL_INPUT
    COMPANY_PAYROLL_INPUT_VARIANCE
    BULK_UPLOAD_ERROR_REPORT
    YEAR_TO_DATE_REPORT
}

enum PayrollFileDownloadStatus {
    RECEIVED
    PROCESSING
    COMPLETED
    FAILED
    TIMED_OUT
}
