extend type Query {
    payrollCycleReportConfigs(sourceType: ReportSourceType!): [PayrollCycleReportConfig!]! @authorize(operations: ["use.operations.payroll-report"])
    payrollCycleReportGroups(filter: PayrollCycleReportGroupFilter!, pageRequest: PageRequest): PayrollCycleReportGroupsResponse! @authorize(operations: ["use.operations.payroll-report"])
    payrollCycleReportGroup(payrollCycle: ID!, sourceType: ReportSourceType!): PayrollCycleReportGroup! @authorize(operations: ["use.operations.payroll-report"])
}

extend type Mutation {
    payrollCycleReportConfigCreate(input: [PayrollReportCycleConfigInput!]!): [PayrollCycleReportConfig!]! @authorize(operations: ["use.operations.payroll-report"])
    payrollCycleReportConfigUpdate(input: [PayrollReportCycleConfigUpdateInput!]!): [PayrollCycleReportConfig!]! @authorize(operations: ["use.operations.payroll-report"])
    payrollCycleReportUpsert(input: PayrollCycleReportGroupUpdateInput!): PayrollCycleReportGroup! @authorize(operations: ["use.operations.payroll-report"])
}

type PayrollCycleReportConfig {
    id: ID!
    sectionName: PayrollCycleReportSectionType!
    reportName: String!
    reportKey: String!
    reportSource: ReportSourceType!
    reportFormat: CustomPayrollReportFormat
}

input PayrollReportCycleConfigInput {
    sectionName: PayrollCycleReportSectionType!
    reportName: String!
    reportKey: String!
    reportSource: ReportSourceType!
    reportFormat: CustomPayrollReportFormat
}

input PayrollReportCycleConfigUpdateInput {
    configId: ID!
    sectionName: PayrollCycleReportSectionType
    reportName: String
    reportKey: String
    reportSource: ReportSourceType
    reportFormat: CustomPayrollReportFormat
}

input PayrollCycleReportGroupFilter {
    sourceType: ReportSourceType!
    entityId: ID
    countries: [CountryCode!]
    dateRange: DateRange
}

type PayrollCycleReportGroupsResponse {
    reportGroups: [PayrollCycleReportGroup!]!
    page: PageResult!
}

type PayrollCycleReportGroup {
    payrollCycle: PayrollCycle!
    uploadedCount: UploadedFileCount!
    reports: [PayrollCycleReport!]!
}

type PayrollCycleReport {
    id: ID!
    config: PayrollCycleReportConfig!
    file: PayrollFile!
    uploadUrl: String @authorize(operations: ["use.operations.payroll-report"])
}

input PayrollCycleReportGroupUpdateInput {
    payrollCycleId: ID!
    reports: [PayrollCycleReportUpdateInput!]!
}

input PayrollCycleReportUpdateInput {
    configId: ID!
    fileName: String!
}

enum ReportSourceType {
    SYSTEM_GENERATED
    OPS_UPLOAD
    DEMO
}
