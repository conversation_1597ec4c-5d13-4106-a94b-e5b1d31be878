extend type Mutation {
    # Following were removed in https://github.com/Multiplier-Core/payroll-service/pull/477/files
    refreshContractReferencesForPayrollCycle(payrollCycleId: ID!) : TaskResponse @authorize(operations: ["use.operations.payroll-cycle"])
}

extend type Query {
    # Did not find any usage of this query.
    partnerPayroll(partnerId: ID, countryCode: CountryCode, payrollMonth: PayrollMonthInput): PartnerPayroll @deprecated  @authorize(operations: ["use.operations.payroll-cycle"]) #Use the partner to get this

    # Following were removed in https://github.com/Multiplier-Core/payroll-service/pull/1051/files
    payrollInputFile(filters: ContractFilters): DocumentReadable @authorize(operations: ["use.operations.payroll-cycle"])

    # Following were removed in https://github.com/Multiplier-Core/payroll-service/pull/1876
    payrollInputFileDownload(payrollCycleId: ID): DocumentReadable @authorize(operations: ["use.operations.payroll-cycle"])
    payrollInputFilePartnerBulkDownload(filter: PayrollCycleFilter): DocumentReadable @authorize(operations: ["use.operations.payroll-cycle"])
}
