extend type Query {
    paySupplement(id: ID!) : PaySupplement @authorize(operations: ["view.operations.paysupplement"])
    paySupplements(filters: PaySupplementFilters, pageRequest: PageRequest): PaySupplementsResponse @authorize(operations: ["view.operations.paysupplement"])
    paySupplementsReport(filters: PaySupplementFilters!): DocumentReadable @authorize(operations: ["view.operations.paysupplement"])
}

extend type Mutation {
    paySupplementCreate(paySupplementData: PaySupplementCreateInput) : PaySupplement @authorize(company: ["create.company.paysupplement"], operations: ["create.operations.paysupplement"])
    paySupplementUpdate(id: ID!, paySupplementChange: PaySupplementUpdateInput) : PaySupplement @authorize(company: ["update.company.paysupplement"])
    paySupplementRevoke(id: ID!) : PaySupplement @authorize(company: ["revoke.company.paysupplement"], operations: ["revoke.operations.paysupplement"])

    paySupplementPaid(id: ID!) : PaySupplement @authorize(operations: ["pay.operations.company.paysupplement"])
    paySupplementReject(id: ID!, reason: String) : PaySupplement @authorize(operations: ["reject.operations.company.paysupplement"])
    paySupplementUpdateBulkAsPaid(ids: [ID]!) : PaySupplementUpdateBulkResponse @authorize(operations: ["update.operations.paysupplement"])
    paySupplementUpdateBulkAsRevoked(ids: [ID]!) : PaySupplementUpdateBulkResponse @authorize(operations: ["revoke.operations.paysupplement"])
    paySupplementUpdateBulkAsToBePaid(ids: [ID]!) : PaySupplementUpdateBulkResponse @authorize(operations: ["update.operations.paysupplement"])
    paySupplementBulkUpload(file: Upload!): TaskResponse @authorize(company: ["bulkupload.company.paysupplement"])
}

input PaySupplementFilters {
    startDate: DateTime
    endDate: DateTime
    status: PaySupplementStatus @deprecated(reason: "Use statuses instead")
    contractType: ContractType @deprecated(reason: "Use contractTypes instead")
    contractStatus: ContractStatus
    country: [CountryCode!]
    contractIds: [ID!]
    paySupplementIds: [ID!]
    companyIds: [ID!]
    statuses: [PaySupplementStatus!]  # this will override the status filter
    contractTypes: [ContractType!] # this will override the contractType filter
    eorPartnerIds: [ID!]
    isMultiplierEorPartner: Boolean
    payrollCycleIds: [ID!]
}

type PaySupplementsResponse {
    page: PageResult
    paySupplements: [PaySupplement]
}

input PaySupplementCreateInput {
    contractId: ID!
    type: PaySupplementType!
    currency: CurrencyCode
    amount: Float!
    notes: String
    description: String
    compensationId: ID

    # This applies when type is VARIABLE_PERFORMANCE_BONUS.
    payOn: MonthYearInput           # Payout month (this will be the starting month when type = VARIABLE_PERFORMANCE_BONUS)
    paySchedule: ScheduleTimeInput  # Pay schedule/frequency (every n days/months/years) when type = VARIABLE_PERFORMANCE_BONUS
    title: String
}

input PaySupplementUpdateInput {
    type: PaySupplementType
    currency: CurrencyCode
    amount: Float
    notes: String
    description: String
}

type PaySupplement @key(fields: "id")  {
    id: ID
    contract: Contract
    type: PaySupplementType
    status: PaySupplementStatus
    changes(latest: Boolean = true): [PaySupplementChange]
    currency: CurrencyCode
    amount: Float
    amountInFunctionalCurrency: Float                           # The amount in MEMBER currency
    notes: String
    description: String
    createdDate: DateTime                                       # Matches the field name on Alpha for now
    updatedDate: DateTime

    # This applies when type is VARIABLE_PERFORMANCE_BONUS.
    payOn: MonthYear            # Payout month (this will be the starting month when type = VARIABLE_PERFORMANCE_BONUS)
    paySchedule: ScheduleTime   # Pay frequency (every n days/months/years) when type = VARIABLE_PERFORMANCE_BONUS
    title: String
}

enum PaySupplementType {
    BONUS
    VARIABLE_PERFORMANCE_BONUS
    ALLOWANCE
    COMMISSION
    OTHER
}

type PaySupplementUpdateBulkResponse {
    success: Boolean
    message: String
    updateItemCount: Int
}

type PaySupplementChange {
    status: PaySupplementStatus
    actionedBy: Person
    actionedOn: DateTime
    reason: String
}
