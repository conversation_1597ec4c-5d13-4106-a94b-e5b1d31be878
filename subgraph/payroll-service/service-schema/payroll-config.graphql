extend type Query {
    payrollCycleConfigs(filter: PayrollCycleConfigsFilter): PayrollCycleConfigsResponse @authorize(operations: ["view.operations.payroll-configs"], company: ["view.company.payroll-config"])
    payrollConfigsWithNextPayrollCycles(companyLegalEntityId: ID!, multiplierLegalEntityId: ID, payFrequency: PayFrequency): [PayrollCycleConfig!]! @authorize(operations: ["view.operations.payroll-configs"], company: ["view.company.payroll-config"])
    previewPayrollConfig(input: PayrollCycleConfigWithDatesInput!): PayrollCycleExample
        @authorize(
            operations: ["view.operations.payroll-configs"],
            company: ["view.company.payroll-config"],
        )
}

extend type Mutation {
    payrollCycleConfigCreate(input: PayrollCycleConfigInput): PayrollCycleConfigsResponse @authorize(operations: ["create.operations.payroll-config"])
    payrollCycleConfigBulkCreate(input: BulkPayrollCycleConfigInput): PayrollCycleConfigsResponse @authorize(operations: ["create.operations.payroll-config"])
    payrollCycleConfigBulkCreateWithExactDates(inputs: [PayrollCycleConfigWithDatesInput]): PayrollCycleConfigsResponse
        @authorize(
            operations: ["create.operations.payroll-config"],
        )
    payrollCycleConfigUpdate(input: PayrollCycleConfigUpdateInput): PayrollCycleConfigsResponse @authorize(operations: ["update.operations.payroll-config"])
    bulkPayrollCycleConfigUpdate(input: BulkPayrollConfigUpdateInput!): PayrollCycleConfigsResponse @authorize(operations: ["update.operations.payroll-config"])
    linkContractsToPayrollCycleConfig(contractIds: [ID!]!, payrollCycleConfigId: ID, validFrom: Date!): TaskResponse @authorize(operations: ["update.operations.payroll-config"])
    relinkContractToAnotherPayrollCycleConfig(contractId: ID!, payrollCycleConfigId: ID!, validFrom: Date!, disableConfig: Boolean): TaskResponse @authorize(operations: ["update.operations.payroll-config"])
    unlinkContractFromPayrollCycleConfig(contractId: ID!, validTo: Date!): TaskResponse @authorize(operations: ["update.operations.payroll-config"])
    setDisabledPayrollCycleConfigToContract(configToContractLinkId: ID!, isDisabled: Boolean!): TaskResponse @authorize(operations: ["update.operations.payroll-config"])
}

input PayrollCycleConfigWithDatesInput {
    frequency: PayFrequency!
    type: PayrollCycleEntityType!
    calendar: PayrollCalendarExactDatesInput!
    payrollConfigEntity: PayrollConfigEntityInput!
    isDefault: Boolean
}

input PayrollCalendarExactDatesInput {
    snapPayDate: Boolean
    snapToDates: Boolean
    snapCutOffDates: Boolean
    snapDeadlineDates: Boolean
    startDate: Date!
    payDate: Date!
    cutOffDate: Date!
    submitPayrollInputDeadline: Date
    approvePayrollInputDeadline: Date
    payrollProcessingDeadline: Date
    approvePayrollReportDeadline: Date
    payrollCompleteDeadline: Date
}

input PayrollCycleConfigUpdateInput {
    id: ID!
    status: PayrollCycleConfigStatus
    isDefault: Boolean
    title: String
    calendarUpdateInput: CalendarUpdateInput
    addWorkplaceEntities: [ID!]
    removeWorkplaceEntities: [ID!]
}

input PayrollCycleConfigsFilter {
    countries: [CountryCode!]
    types: [PayrollCycleEntityType!]
    payrollCycleEntityIds: [ID!]
    payFrequency: PayFrequency
}

input PayrollCycleConfigInput {
    country: CountryCode!
    frequency: PayFrequency!
    type: PayrollCycleEntityType!
    calendar: PayrollCalendarInput!
    payrollCycleEntityId: ID!
    payrollPartnerCountryId: ID!
    title: String!
    isDefault: Boolean
    billingFrequency: BillingFrequency
    workplaceEntityIds: [ID!]
    isDemo: Boolean
}

input BulkPayrollCycleConfigInput {
    frequency: PayFrequency!
    type: PayrollCycleEntityType!
    calendar: PayrollCalendarInput!
    payrollConfigEntity: [PayrollConfigEntityInput!]!
    isDefault: Boolean
}

input PayrollConfigEntityInput {
    country: CountryCode!
    payrollCycleEntityId: ID!
    payrollPartnerCountryId: ID
    title: String
    billingFrequency: BillingFrequency
}

input PayrollCalendarInput {
    snapPayDate: Boolean
    snapToDates: Boolean
    snapCutOffDates: Boolean
    snapDeadlineDates: Boolean
    startDate: Date!
    payDateRelativeToEndDate: Int!
    cutOffRelativeToPayDate: Int!
    contractStartDateRelativeToEndDate: Int!
    contractEndDateRelativeToEndDate: Int!
    compensationDateRelativeToEndDate: Int!
    paySupplementDateRelativeToEndDate: Int!
    expenseDateRelativeToEndDate: Int!
    timeSheetDateRelativeToEndDate: Int!
    timeOffDateRelativeToEndDate: Int!
    memberChangesDateRelativeToEndDate: Int!
    deductionDateRelativeToEndDate: Int!
    submitPayrollInputDeadlineRelativeToCutOff: Int
    approvePayrollInputDeadlineRelativeToCutOff: Int
    payrollProcessingDeadlineRelativeToEndDate: Int
    approvePayrollReportDeadlineRelativeToEndDate: Int @deprecated
    approvePayrollReportDeadlineRelativeToPayDate: Int
    payrollCompleteDeadlineRelativeToEndDate: Int
}

input CalendarDateInput {
    date: Int!
    # T-0: this month, T+1: next month, etc
    relativeMonth: Int!
}

input BulkPayrollConfigUpdateInput {
    configIds: [ID!]!
    calendarUpdateInput: CalendarUpdateInput
    isDefault: Boolean
    title: String
}

input CalendarUpdateInput {
    snapPayDate: Boolean,
    snapToDates: Boolean,
    snapCutOffDates: Boolean,
    snapDeadlineDates: Boolean,
    startDate: Date,
    payDateRelativeToEndDate: Int,
    cutOffRelativeToPayDate: Int,
    contractStartDateRelativeToEndDate: Int,
    contractEndDateRelativeToEndDate: Int,
    compensationDateRelativeToEndDate: Int,
    paySupplementDateRelativeToEndDate: Int,
    expenseDateRelativeToEndDate: Int,
    timeSheetDateRelativeToEndDate: Int,
    timeOffDateRelativeToEndDate: Int,
    memberChangesDateRelativeToEndDate: Int,
    deductionDateRelativeToEndDate: Int,
    submitPayrollInputDeadlineRelativeToCutOff: Int,
    approvePayrollInputDeadlineRelativeToCutOff: Int,
    payrollProcessingDeadlineRelativeToEndDate: Int,
    approvePayrollReportDeadlineRelativeToEndDate: Int @deprecated
    approvePayrollReportDeadlineRelativeToPayDate: Int
    payrollCompleteDeadlineRelativeToEndDate: Int
}

type PayrollCycleConfigsResponse {
    configs: [PayrollCycleConfig!]
}

type PayrollCycleConfig {
    id: ID
    frequency: PayFrequency
    calendar: PayrollCalendar
    type: PayrollCycleEntityType
    payrollPartnerCountry: PayrollPartnerCountry
    payrollCycleEntity: PayrollCycleEntity
    country: CountryCode
    title: String!
    isDefault: Boolean
    payrollCycles(pageNumber: Int!, startDate: Date): [PayrollCycle!]! @authorize(operations: ["view.operations.payroll-cycle"])
    example: PayrollCycleExample
    payrollCycleByReferenceDate(domain: PayrollInputDomainType!, referenceDate: Date!, returnClosestToReferenceDateUpcomingCutOff: Boolean!): PayrollCycle @authorize(company: ["view.company.payroll-cycle"])
    isDemo: Boolean
}

type PayrollCalendar {
    snapPayDate: Boolean!
    snapToDates: Boolean!
    snapCutOffDates: Boolean!
    snapDeadlineDates: Boolean
    startDate: Date!
    payDateRelativeToEndDate: Int!
    cutOffRelativeToPayDate: Int!
    contractStartDateRelativeToEndDate: Int!
    contractEndDateRelativeToEndDate: Int!
    compensationDateRelativeToEndDate: Int!
    paySupplementDateRelativeToEndDate: Int!
    expenseDateRelativeToEndDate: Int!
    timeSheetDateRelativeToEndDate: Int!
    timeOffDateRelativeToEndDate: Int!
    memberChangesDateRelativeToEndDate: Int!
    deductionDateRelativeToEndDate: Int!
    submitPayrollInputDeadlineRelativeToCutOff: Int
    approvePayrollInputDeadlineRelativeToCutOff: Int
    payrollProcessingDeadlineRelativeToEndDate: Int
    approvePayrollReportDeadlineRelativeToEndDate: Int @deprecated
    approvePayrollReportDeadlineRelativeToPayDate: Int
    payrollCompleteDeadlineRelativeToEndDate: Int
}

enum PayrollCycleConfigStatus {
    ACTIVE
    INACTIVE
}
