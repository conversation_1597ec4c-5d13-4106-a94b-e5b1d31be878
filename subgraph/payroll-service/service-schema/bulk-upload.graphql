extend type Query {
    payrollBulkUploadTemplate(payrollCycleId: ID): DocumentReadable @authorize(company: ["update.company.payroll-cycle"])

    payrollBulkUploadConfirmStatus(payrollCycleId: ID!): PayrollBulkUploadConfirmStatus @authorize(company: ["view.company.payroll-cycle"])

    # mutation access control queries
    payrollBulkUploadCreateRules(payrollCycleId: ID!, fileName: String): AccessibilityResponse @authorize(company: ["update.company.payroll-data.input"])
    payrollBulkUploadValidateRules(payrollCycleId: ID!): AccessibilityResponse @authorize(company: ["update.company.payroll-data.input"])
    payrollBulkUploadConfirmRules(payrollCycleId: ID!): AccessibilityResponse @authorize(company: ["update.company.payroll-data.input"])
}

extend type Mutation {
    payrollBulkUploadCreate(payrollCycleId: ID!, fileName: String!): UploadUrlResponse @authorize(company: ["update.company.payroll-data.input", "update.company.payroll-cycle"])
    payrollBulkUploadValidate(payrollCycleId: ID!) : BulkUploadValidationResponse @authorize(company: ["update.company.payroll-data.input", "update.company.payroll-cycle"])
    payrollBulkUploadConfirm(payrollCycleId: ID!) : TaskResponse @authorize(company: ["update.company.payroll-data.input", "update.company.payroll-cycle"])

    payrollBulkUploadTemplatesRefresh(countries: [CountryCode!]): TaskResponse @authorize(operations: ["update.operational.payroll-cycle"])
}

type UploadUrlResponse {
    url: String!
}

enum PayrollBulkUploadConfirmStatus {
    IN_PROGRESS
    COMPLETED
}

type BulkUploadValidationResponse {
    validationResponse: ValidationResponse
    bulkUploadSummary: PayrollBulkUploadSummary
    bulkUploadFile: PayrollFile
    errorReport: PayrollFileDownloadResponse
}

type PayrollBulkUploadSummary {
    totalPaySupplements: Int @deprecated(reason: "already exists inside counts field")
    totalExpenses: Int @deprecated(reason: "already exists inside counts field")
    totalTimeOff: Int @deprecated(reason: "already exists inside counts field")
    counts: [PayrollBulkUploadItemCount!]
}

type PayrollBulkUploadItemCount {
    itemType: BulkUploadItemType!
    error: Int!
    success: Int!
    warn: Int!
    employee: Int!
}

enum BulkUploadItemType {
    DEDUCTION
    EXPENSE
    HOLD_SALARY
    OFF_BOARDING
    PAY_SUPPLEMENT
    TIME_OFF
    TIME_SHEET
    SALARY_REVISION
    BANK_DETAIL
}


type PayrollBulkUpload {
    inputFile: PayrollFile!

    errors: [PayrollBulkUploadError!]

    bulkUploadSummary: PayrollBulkUploadSummary
    paySupplements: [PaySupplement!]
    # expenses, timeoff... to be added
}

type PayrollBulkUploadError {
    message: String!
    contractId: ID
}
