extend type Mutation {
    # Trigger pay supplement auto-create process which creates pay supplements based on contract's
    # additional compensations items (eg: variable performance bonus).
    # Can call by OPS only.
    triggerPaySupplementAutoCreateProcess(filters: PaySupplementAutoCreationFilters): TaskResponse @authorize(operations: ["trigger.operations.paysupplement"])

    # Trigger pay supplement reminder email sending process.
    # Can call by OPS only.
    triggerPaySupplementReminderEmailProcess: TaskResponse @authorize(operations: ["trigger.operations.paysupplement"])

    # Trigger pay supplement "MONTHLY" reminder email sending process.
    # Can call by OPS only.
    triggerPaySupplementMonthlyReminderEmailProcess: TaskResponse @authorize(operations: ["trigger.operations.paysupplement"])
}


input PaySupplementAutoCreationFilters {
    companyIds: [ID]
    contractIds: [ID]
}
