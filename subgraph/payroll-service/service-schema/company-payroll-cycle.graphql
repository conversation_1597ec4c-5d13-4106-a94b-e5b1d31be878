extend type Query {
    companyPayrollMemberPays(payrollFilter: CompanyPayrollFilter!, pageRequest: PageRequest): CompanyMemberPayResult @authorize(company: ["view.company.payroll-data.output", "view.company.payroll-cycle"])
    companyPayrollReportDownload(filter: CompanyPayrollReportInput!): DocumentReadable @authorize(company: ["view.company.payroll-report", "view.company.payroll-cycle"])
    companyPayrollInputReportDownload(filter: CompanyPayrollInputReportFilter!): DocumentReadable @authorize(company: ["view.company.payroll-data.input", "view.company.payroll-cycle"])
    companyPayrollCycle(id: ID!): CompanyPayrollCycle @authorize(company: ["view.company.payroll-cycle"])
    companyPayrollCycles(filter: CompanyPayrollCycleFilter!, pageRequest: PageRequest): CompanyPayrollCyclesResult! @authorize(company: ["view.company.payroll-cycle"])
    companyPayrollCycleReportDownload(filter: CompanyPayrollCycleReportFilter!): PayrollFile @authorize(company: ["view.company.payroll-report", "view.company.payroll-cycle"])
    companyPayrollReportDownloadV2(filter: CompanyPayrollReportFilter!): PayrollFile @authorize(company: ["view.company.payroll-report"])
    companyPayrollCycleReports(companyPayrollCycleId: ID!): [CompanyPayrollReport!] @authorize(company: ["view.company.payroll-report", "view.company.payroll-cycle"]) @deprecated(reason: "Use companyPayrollCycleReportsV2 instead")
    companyPayrollCycleReportsV2(companyPayrollCycleId: ID!): [CompanyPayrollCycleReport!]! @authorize(company: ["view.company.payroll-report", "view.company.payroll-cycle"])
    companyPayrollEntityTypes: [PayrollCycleEntityType] @authorize(company: ["view.company.payroll-cycle"]) # Temporary query to get the entity types for the logged in user's company

    # mutation access control queries
    companyPayrollInputConfirmRules(companyPayrollCycleId: ID!): AccessibilityResponse @authorize(company: ["update.company.payroll-workflow.input"])
    companyPayrollOutputConfirmRules(companyPayrollCycleId: ID!): AccessibilityResponse @authorize(company: ["update.company.payroll-workflow.output"])
    companyPayrollPayslipPublishRules(companyPayrollCycleId: ID!): AccessibilityResponse @authorize(company: ["update.company.payslip-workflow"])
}

extend type Mutation {
    companyPayrollCycleRefresh(value: RefreshCompanyPayrollCycleInput) : [CompanyPayrollCycle] @authorize(operations: ["refresh.operations.company-payroll-cycle"])
    companyPayrollInputConfirm(companyPayrollCycleId: ID!) : CompanyPayrollCycle @authorize(company: ["update.company.payroll-workflow.input"])
    companyPayrollOutputConfirm(companyPayrollCycleId: ID!) : CompanyPayrollCycle @authorize(company: ["update.company.payroll-workflow.output", "update.company.payroll-cycle"])
    companyPayrollFileDownloadAsync(request: CompanyPayrollFileDownloadRequest!): PayrollFileDownloadResponse @authorize(company: ["view.company.payroll-report", "view.company.payroll-cycle"])
    companyPayrollConsolidatedReportDownloadAsync(request: CompanyPayrollConsolidatedReportDownloadRequest!): PayrollConsolidatedReportDownloadResponse @authorize(company: ["view.company.payroll-report", "view.company.payroll-cycle"])
    # Can call by OPS only.
    # Try Re-generating the missing reports for PEO cycles. Only used by Ops.
    companyPayrollReportsRegenerate(payrollCycleIds: [ID!]!): TaskResponse @authorize(operations: ["use.operations.company-payroll-cycle"])
    # Trigger review payroll input reminder email sending process.
    # Can call by OPS only.
    triggerReviewPayrollInputReminderEmail(companyPayrollCycleIds: [ID!]!): TaskResponse @authorize(operations: ["trigger.operations.payroll.email"])
    # Trigger review payroll report reminder email sending process.
    # Can call by OPS only.
    triggerReviewPayrollReportReminderEmail(companyPayrollCycleIds: [ID!]!): TaskResponse @authorize(operations: ["trigger.operations.payroll.email"])
}

input RefreshCompanyPayrollCycleInput {
    companyPayrollCycleIds: [ID!]
}

input CompanyPayrollCycleReportFilter {
    companyPayrollCycleId: ID!
    reportId: ID
    reportType: CompanyPayrollReport
}

input CompanyPayrollReportFilter {
    legalEntityId: ID!
    financialYearStartDate: DateTime!
    financialYearEndDate: DateTime!
    frequency: CompanyPayrollReportFrequency!
    reportType: CompanyPayrollReport!
}

input CompanyPayrollFileDownloadRequest {
    companyPayrollCycleId: ID!
    type: PayrollFileDownloadType!
    isForced: Boolean
}

input CompanyPayrollConsolidatedReportDownloadRequest {
    financialYears: [Int!]
    payrollCycleEntityId: ID
    payrollType: PayrollCycleEntityType
    isForced: Boolean
    type: PayrollFileDownloadType!
}

input CompanyPayrollFilter {
    companyId: ID!
    companyPayrollCycleId: ID!
    payrollCycleIds: [ID!]
    memberName: String # to search by name, strategy: contain-ignore-case...
    country: CountryCode
}

input CompanyPayrollReportInput {
    filters: CompanyPayrollFilter!
    reportType: ExportPayrollReportType!
}

input CompanyPayrollCycleFilter {
    id: ID @deprecated(reason: "Use query companyPayrollCycle with id instead")
    payrollCycleEntityType: PayrollCycleEntityType @deprecated(reason: "Use payrollCycleEntityTypes instead")
    payrollCycleEntityTypes: [PayrollCycleEntityType!]
    payrollMonth: MonthYearInput
    frequencies: [PayFrequency!]
    statuses: [CompanyPayrollCycleStatus!]
    countries: [CountryCode!]
    companyIds: [ID!]
    payDate: DateRange
    contractIds: [ID!]
    payrollCycleTypes: [PayrollCycleType!]
    startDate: Date
    endDate: Date
    legalEntityIds: [ID!]
}

type CompanyPayrollCycle @key(fields: "id")   {
    id: ID!
    entityType: PayrollCycleEntityType!
    entity: LegalEntity # only available for PEO cycles
    companyId: ID! @deprecated # use company instead
    company: Company!
    cycleIds: [ID!]! @deprecated # use payrollCycles instead
    payrollCycles: [PayrollCycle!] @authorize(company: ["view.company.payroll-cycle"])
    companyPayrollInput(filter: CompanyPayrollInputFilter, pageRequest: PageRequest!): CompanyPayrollInputResponse @authorize(company: ["view.company.payroll-data.input", "view.company.payroll-cycle"])
    summary: CompanyPayrollCycleSummary @authorize(company: ["view.company.payroll-cycle"])
    status: CompanyPayrollCycleStatus!

    # calendar config
    payrollMonth: MonthYear
    payrollCutOffDate: Date @authorize(company: ["view.company.payroll-cycle"])
    payDate: Date
    frequency: PayFrequency
    startDate: Date
    endDate: Date
    confirmByDate: Date

    createdOn: DateTime
    createdBy: Person
}

enum CompanyPayrollCycleStatus {
    CREATED # Review Input for EOR and EOR Partner
    REVIEW_INPUT # Review Input for PEO
    PROCESSING
    CONFIRM_REPORT # Review Output
    REPORT
    COMPLETED # Move to historical cycles
}
enum ContractPayrollLabel {
    INCLUDED
    EXCLUDED
    NEW_HIRE
    ACTIVE
    TERMINATION
    ONBOARDING
    OFFBOARDING
}

type CompanyPayrollInputResponse {
    pageResult: PageResult!
    data: [CompanyPayrollInputEntry!]!
}

type CompanyPayrollInputEntry {
    id: ID
    contractId: ID! @deprecated # use contract instead
    contract: Contract!
    country: CountryCode!
    memberName: String!
    contractPayrollLabels: [ContractPayrollLabel!]!
    contractStatus: ContractStatus!
    workDays: Float! # compare cycle range to start date
    timeoff: Float! # not calculated yet, will discuss later
    memberCurrency: CurrencyCode!
    companyCurrency: CurrencyCode!
    memberToCompanyCurrencyExchangeRate: Float!
    baseSalary: Float!
    payFrequency: PayFrequency!
    rateFrequency: RateFrequency!
    expenses: Float!
    paySuplements: Float!
    allowance: Float!
    payrollContractTypes: [PayrollContractType!] # filter out active if new hires and termination is presenting
}

type CompanyPayrollCycleSummary {
    countryTotalCounts: [CountryCount]
    memberTotalCount: Int
}

input CompanyPayrollInputFilter {
    memberName: String
    country: CountryCode
    payrollContractTypes: [PayrollContractType!] # only used for companyPayrollInput query
    anyVariance: Boolean # only used for companyPayrollInput query
    anyCompensationChange: Boolean # only used for companyPayrollInput query
    anyBankDetailsChange: Boolean # only used for companyPayrollInput query
}

input CompanyPayrollInputReportFilter {
    companyId: ID!
    companyPayrollCycleId: ID!
    companyPayrollInputFilter: CompanyPayrollInputFilter
}

type CompanyPayrollCyclesResult {
    data: [CompanyPayrollCycle!]
    pageResult: PageResult!
}

type CompanyPayrollCycleReport {
    id: ID!
    reportConfig: PayrollCycleReportConfig
    reportType: CompanyPayrollReport
}

enum PayrollCycleReportSectionType {
    PAYROLL_REPORTS
    TAXES_AND_CONTRIBUTIONS
    PAYMENT_REPORTS
}

enum CompanyPayrollReport {
    GROSS_TO_NET
    TOTAL_PAYROLL_COST
    PAYROLL_INPUT
    VARIANCE_INPUT
    VARIANCE_OUTPUT
    BANK_SUMMARY
    PAYMENT_INSTRUCTION_FILE
    EMPLOYER_SUMMARY
    EMPLOYEE_SUMMARY
    ALL_PAYSLIPS
    ALL_PAYROLL_REPORTS
    GENERAL_LEDGER
    TAX_SUMMARY
    PAYROLL_SUMMARY
}

enum CompanyPayrollReportFrequency {
    ANNUALLY
    QUARTERLY
    MONTHLY
    YTD
}
