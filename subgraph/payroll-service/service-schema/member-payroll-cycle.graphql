extend type Query {
    memberPayrollCycles(filter: MemberPayrollCycleFilter!): MemberPayrollCyclesResponse @authorize(member: ["use.member.payroll"])
    latestMemberPayrollCycles: MemberPayrollCyclesResponse @authorize(member: ["use.member.payroll"])
    memberPayrollCycleDetail(filter: MemberPayrollCycleDetailFilter!): MemberPayrollCycleDetail @authorize(member: ["use.member.payroll"])
}

input MemberPayrollCycleDetailFilter {
    payrollCycleId: ID!
}

input MemberPayrollCycleFilter {
    year: Int
    payrollCycleId: ID
}

type MemberPayrollCyclesResponse {
    payrollCycles: [MemberPayrollCycle!] @deprecated(reason: "Use payrollCycleDetails instead")
    payrollCycleDetails: [MemberPayrollCycleDetail!]
}

type MemberPayrollCycle { #deprecated(reason: "Use payrollCycleDetail instead")
    payrollCycle: PayrollCycle
    memberPay: MemberPay
}

type MemberPayrollCycleDetail {
    payrollCycle: PayrollCycle
    memberPay: MemberPay
    compensations: [CompensationSnapshot!]
    paySupplements: [PaySupplementSnapshot!]
    expenses: [ExpenseSnapshot!]
    fixedAllowances: [FixedAllowance!]
    unpaidTimeOffs: [TimeOffSnapshot!]

    summary: MemberPayrollCycleSummary
}

type MemberPayrollCycleSummary {
    deductions: MemberPayrollCycleSummaryItem
    compensation: MemberPayrollCycleSummaryItem
    paySupplement: MemberPayrollCycleSummaryItem
    expense: MemberPayrollCycleSummaryItem
    fixedAllowance: MemberPayrollCycleSummaryItem
    unpaidTimeOff: MemberPayrollCycleSummaryItem
}

type MemberPayrollCycleSummaryItem {
    totalAmount: Float
    count: Int
}

type PaySupplementSnapshot {
    paySupplementId: ID!
    amount: Float
    currency: CurrencyCode
    status: PaySupplementStatus
    notes: String
    type: PaySupplementType
    description: String
}

type ExpenseSnapshot {
    expenseId: ID!
    totalAmount: Float
    currency: CurrencyCode
    status: ExpenseStatus
    expenseTitle: String
}

type TimeOffSnapshot {
    timeOffId: ID!
    description: String
    status: TimeOffStatus
    noOfDays: Int
}

type CompensationSnapshot {
    compensationId: ID!
    amount: Float
    rateType: RateType
    type: String
    currency: CurrencyCode
    rateFrequency: RateFrequency
    payFrequency: PayFrequency
}

type FixedAllowance {
    id: ID
    amount: Float
    rateType: RateType
    label: String
    note: String
    type: String
    currency: CurrencyCode
}
