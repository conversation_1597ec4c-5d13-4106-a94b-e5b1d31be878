extend type Mutation {
    # Use this to initiate the payroll for the month
    partnerPayrollCreate(payrollData: CreatePartnerPayrollInput!): PartnerPayroll @authorize(operations: ["create.operations.partner.payroll"]) @deprecated # Partner Payroll will be created during the creation of the Payroll Cycle

    partnerPayrollSendForReview(input: SendReportForReviewInput!): PartnerPayroll @authorize(partner: ["send.partner.payrollreport"])
    payrollCycleSendForReview(input: SendPayrollCycleForReviewInput!): PayrollCycle @authorize(partner: ["send.partner.payrollreport"])
    uploadPayrollFile(input: Upload!, partnerId: ID!, country: CountryCode!, payrollMonth: PayrollMonthInput!): PayrollReportUpload @authorize(partner: ["upload.partner.payrollreport"]) @deprecated # uploadPayrollOutputReport mutation will be used in the new payroll workflow
    uploadPayrollOutputReport(input: Upload!, partnerId: ID!, payrollCycle: ID!): PayrollReportUpload @authorize(partner: ["upload.partner.payrollreport"])

    # User can use this to get the contract IDs corresponding to the names and joining dates(optional) present in the payroll file
    uploadContractIdMappingFile(input: Upload!): [ContractIdMappingUpload] @authorize(operations: ["create.operations.partner.payroll"])

    # A utility to generate payslips for a payroll cycle
    # Generated payslips will not show in the platform and temporarily accessible only through PDFMonkey for testing purposes.
    partnerPayrollPayslipGenerateForPayrollCycle(payrollCycleId: ID!): TaskResponse @authorize(operations: ["create.operations.payroll.payslip"])

    # Company/Ops/Partner can use this to create/replace a contract's payslip.
    partnerPayrollUploadPayslip(value: PartnerPayrollUploadPayslipInput!, file: Upload!): TaskResponse @deprecated @authorize(operations: ["update.operations.payroll.payslip"])

    partnerPayrollAssignContract(partnerId: ID!, contractId: ID!, partnerMemberId: String) : ContractPayrollPartner @authorize(operations: ["assign.operations.contract.partner"])
    partnerPayrollStartRegistration(partnerId: ID, contractId: ID) : ContractPayrollPartner @authorize(operations: ["update.operations.partner"], partner: ["update.partner"])
    partnerPayrollRegistrationCompleted(partnerId: ID, contractId: ID) : ContractPayrollPartner @authorize(operations: ["update.operations.partner"], partner: ["update.partner"])

    updatePayrollPartnerMemberIdToContractId(contractIds: [ID], country: CountryCode) : TaskResponse @authorize(operations: ["update.operations.partner|update.operations.payroll"])
}

extend type Query {
    partnerPayrolls(input: GetPartnerPayrollInput): [PartnerPayroll] @authorize(partner: ["use.partner.payroll"], operations: ["use.operations.payroll|update.operations.payroll"])
}

# to selective pick up for exceptional/late stuff. Null means select all
input PayrollUpdateBatchCreateFilter {
    expenseIds: [ID!]
    paySupplementIds: [ID!]
    contractIds: [ID!]
    # more to come
}

input SendReportForReviewInput {
    partnerId: ID
    country: CountryCode
    month: PayrollMonthInput
}

input SendPayrollCycleForReviewInput {
    payrollCycleId: ID!
}

# TODO: This should be the only place where {partnerId, country, year, month} combination is used.
# For every other place, we should utilize `partner_payroll`.`id` @SquadA, @SquadB
input CreatePartnerPayrollInput {
    partnerId: ID
    country: CountryCode
    month: PayrollMonthInput
}

input CreatePartnerPayrollMemberPayInput {
    contractId: ID
    currency: CurrencyCode
    amountGross: Float
    contributions: [CreatePartnerPayrollPayComponentInput]
    deductions: [CreatePartnerPayrollPayComponentInput]
    amountNet: Float
    amountTotalCost: Float
}

input CreatePartnerPayrollPayComponentInput {
    name: String
    value: Float
}

# Ops can use this to generate payslips out-of-box. User can generate by specifying either of the below.
# 1. { month, partnerId }
# 2. { month, contractIds }
input PartnerPayrollGeneratePayslipsInput {
    partnerId: ID
    contractIds: [ID]
    payrollMonth: PayrollMonthInput!
    country: CountryCode
}

input PartnerPayrollSendPayslipsInput {
    partnerId: ID
    contractIds: [ID]
    payrollMonth: PayrollMonthInput!
    country: CountryCode
}

input PartnerPayrollUploadPayslipInput {
    contractId: ID!
    payrollCycle: Int
    payrollMonth: PayrollMonthInput!
    netAmount: Float!
    currencyCode: CurrencyCode!
    payrollCycleId: ID
    documentType: DocumentType
}

input GetPartnerPayrollInput {
    partnerId: ID
    payrollMonth: PayrollMonthInput
}

type PartnerPayroll implements Payroll {
    id: ID
    month: PayrollMonth
    status: PartnerPayrollStatus # Derived status from the latest payroll version
    memberPays: [PartnerMemberPay]
    partner: PayrollPartnerCountry @authorize(partner: ["use.partner.payroll"], operations: ["use.operations.payroll|update.operations.payroll"])
    currency: CurrencyCode
    amountTotalCost: Float
    payrollProcess: PayrollProcess @authorize(partner: ["use.partner.payroll"], operations: ["use.operations.payroll|update.operations.payroll"]) # Individual Query
}

type PayrollProcess {
    versions: [PartnerPayrollVersion] # Individual Query
    payrollReportUpload: PayrollReportUpload # When sent for review it becomes null
    payrollUpdates: [PayrollUpdateBatch!] # All the payroll updates that need to be processed
    workflow: [PayrollVersionWorkFlowNode] # Version selected
    currentStep: PartnerPayrollVersionStep # Current step of the version selected
}

type PayrollReportUpload {
    id: ID
    partnerId: ID
    country: CountryCode
    month: PayrollMonth
    file: FileLink @deprecated(reason: "please use document instead of file")
    document: Document
    reconciliationResults: [ReconciliationResult] # general results from the recon which are not related to a member pay
    parsedMemberPays: [ParsedMemberPay]
    payrollCycle: PayrollCycle @authorize(operations: ["view.operations.payroll-cycle"], partner: ["view.partner.payroll-cycle"])
}

type ReconciliationResult {
    message: String # The result message category
    messageKey: String # The result message translation key
    details: [String] # Details about the result
    type: ReconciliationResultType # type of the result
}

type ContractIdMappingUpload {
    inputName: String
    inputJoiningDate: DateTime
    contract: Contract
}

# Parsed Member of the uploaded report, this data is not yet a MemberPay in our domain
type ParsedMemberPay {
    rowId: ID
    name: String
    designation: String
    currency: CurrencyCode
    grossSalary: Float
    employerContributions: Float
    totalPayrollCost: Float
    employeeDeductions: Float
    netSalary: Float
    reconciliationResults: [ReconciliationResult] # Specific recon result for the member pay
    contract: Contract
}

type PayrollUpdateBatch {
    createdOn: DateTime!  # used as an ID for a batch. This is the moment when Ops triggers "get payroll updates"
    updateItems: [PayrollUpdateItem!]  # each element contains info of an update/change (an expense, a paySupplement...)
}

type PayrollUpdateItem {
    type: PayrollUpdateType # to track multiple fields on a same model type (e.g. track startDate, position...on a contract)
    data: PayrollUpdateItemData # a union type which can be an Expense/PaySupplement/etc...object
}

# Other types to come
union PayrollUpdateItemData = Expense | PaySupplement | Contract

enum PayrollUpdateType {
    NEW_MEMBER
    START_DATE
    SALARY_CHANGE       # Later
    EXPENSE
    PAY_SUPPLEMENT
    MEMBER_OFFBOARDED
}

type PartnerPayrollVersion {
    version: Int
    partnerPayroll: PartnerPayroll # for backlinking
    status: PartnerPayrollVersionStatus # Completed Status of the current Version
    memberPays: [PartnerMemberPay]
    reconciliationResults: [ReconciliationResult] # general results from payrollReportUpload
    # workflow: [PayrollVersionWorkFlowNode]
}

type PayrollVersionWorkFlowNode {
    step: PartnerPayrollVersionStep
    status: PartnerPayrollVersionStatus # Completed status for the current step
    statuses: [PartnerPayrollVersionStatus]
}

type PartnerMemberPay implements MemberPay @key(fields: "id") {
    id: ID
    partnerPayroll: PartnerPayroll
    companyPayroll: CompanyPayroll @authorize(company: ["view.company.payroll"], operations: ["view.operations.company.payroll"])
    contract: Contract
    status: MemberPayStatus
    currency: CurrencyCode
    billingCurrency: CurrencyCode
    contributions: [PayComponent]
    deductions: [PayComponent]
    clientDeductions: [PayComponent]
    additional: [AdditionalPayComponent]        # TODO by @SquadA or @SquadB....
    payslip: Payslip @authorize(operations: ["use.operations.payslips"], company: ["use.company.payslips"], member: ["use.member.dashboard"])
    reconciliationResults: [ReconciliationResult] # Specific to a partnerMemberPay - promoted from parsedMemberPay
    paySupplements: [PaySupplement]

    amountGross: Float
    totalEmployeeDeductions: Float
    totalEmployeeContributions: Float
    totalTaxes: Float
    amountNet: Float
    totalEmployerContributions: Float
    totalExpenseAmount: Float
    amountTotalCost: Float

    totalContributionAmount: Float
    totalDeductionsAmount: Float
    totalSupplementAmount: Float

    totalAllowanceAmount: Float
    totalBonusAmount: Float
    totalCommissionAmount: Float

    # This represents the aggregated value of Gross Pay Breakdown. This excludes allowance, bonus and commission amounts
    # Note: This is different from amountGross.
    aggregatedGross: Float

    fxRateApplied: Float
    payrollCycle: PayrollCycle

    severancePayAccruals: [PayComponent]

    billingException: Boolean

    finalNetPayAmount: Float
    totalEmployerDeductions: Float
}

type PayrollUpdateTaskResponse {
    success: Boolean
    message: String
    updateItemCount: Int
}

enum PartnerPayrollStatus {
    NEW
    PROCESSING
    COMPLETED
}

enum PartnerPayrollVersionStatus {
    NULL_STATUS # Will not be saved in DB, this is just for the graph
    # The status is per version and version is only created when you send a report for review
    # hence REPORT_UPLOADED is not possible to have, we can figure this out anyway by looking at the "payrollReportUpload"
    # REPORT_UPLOADED
    # REPORT_GENERATED

    REPORT_SENT_FOR_REVIEW
    REPORT_REVIEW_IN_PROGRESS
    REPORT_CHANGES_REQUESTED
    REPORT_APPROVED
    PARTNER_PAYMENT_IN_PROGRESS
    PARTNER_PAYMENT_DONE
    MEMBER_PAYMENT_IN_PROGRESS
    MEMBER_PAYMENT_DONE
    UPLOADED_PAYSLIPS
    GENERATED_PAYSLIPS              # TODO: Should make change to workflow... @TR
    PAYSLIPS_REVIEW_IN_PROGRESS
    PAYSLIPS_APPROVED
    PAYSLIPS_CHANGES_REQUESTED
    CLOSED
}

# Order is important, please don't change
enum PartnerPayrollVersionStep {
    PAYROLL_UPDATE
    PAYROLL_REPORT
    REPORT_REVIEW
    PAYMENT
    UPLOAD_PAYSLIP
    PAYROLL_HISTORY
}

enum ReconciliationResultType {
    ERROR
    WARN
    INFO
}

enum ContractPayrollPartnerStatus {
    NEW
    REGISTRATION_IN_PROGRESS
    COMPLETED
    OFFBOARDED
}

type PayrollPartnerCountry implements PartnerCountry @key(fields: "id") {
    id: ID
    capability: PartnerCapability #PAYROLL
    partner: Partner
    country: CountryCode
    contracts(status: ContractPayrollPartnerStatus): [ContractPayrollPartner] @authorize(operations: ["view.operations.company.payroll|use.operations.payroll-report|use.operations.payroll|use.operations.contract-onboarding"])
    operator: [PartnerUser] @authorize(operations: ["view.operations.users|create.operations.partner.users"])
    payroll(payrollMonth: PayrollMonthInput): [PartnerPayroll] @authorize(partner: ["use.partner.payroll"], operations: ["use.operations.payroll|update.operations.payroll"])
    currency: CurrencyCode
}

type ContractPayrollPartner @key(fields: "id") {
    id: ID
    status: ContractPayrollPartnerStatus
    contract: Contract
    payrollPartnerCountry: PayrollPartnerCountry
}
