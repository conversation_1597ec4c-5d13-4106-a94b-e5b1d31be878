
type CompanyPayroll implements Payroll @key(fields: "id") {
    id: ID
    month: PayrollMonth
    company: Company
    status: CompanyPayrollStatus
    memberPays: [CompanyMemberPay] @authorize(operations: ["view.operations.company.payroll|use.operations.payroll-report"])
    amountTotalCost: Float
}

type CompanyMemberPay implements MemberPay @key(fields: "id") {
    id: ID
    partnerPayroll: PartnerPayroll
    companyPayroll: CompanyPayroll @authorize(company: ["view.company.payroll"], operations: ["view.operations.company.payroll"])
    contract: Contract
    status: MemberPayStatus
    currency: CurrencyCode
    billingCurrency: CurrencyCode
    contributions: [PayComponent]
    deductions: [PayComponent]
    clientDeductions: [PayComponent]
    additional: [AdditionalPayComponent]        # TODO by @SquadA or @SquadB....

    amountGross: Float
    totalEmployeeDeductions: Float
    totalEmployeeContributions: Float
    totalTaxes: Float
    amountNet: Float
    totalEmployerContributions: Float
    totalExpenseAmount: Float
    amountTotalCost: Float

    amountNetCompanyCurrency: Float
    amountGrossCompanyCurrency: Float
    amountTotalCostCompanyCurrency: Float
    payslip: Payslip @authorize(operations: ["use.operations.payslips"], company: ["use.company.payslips"], member: ["use.member.dashboard"])
    paySupplements: [PaySupplement]

    totalContributionAmount: Float
    totalDeductionsAmount: Float
    totalSupplementAmount: Float

    totalAllowanceAmount: Float
    totalBonusAmount: Float
    totalCommissionAmount: Float

    # This represents the aggregated value of Gross Pay Breakdown. This excludes allowance, bonus and commission amounts
    # Note: This is different from amountGross.
    aggregatedGross: Float

    fxRateApplied: Float
    payrollCycle: PayrollCycle

    severancePayAccruals: [PayComponent]

    billingException: Boolean

    finalNetPayAmount: Float
    totalEmployerDeductions: Float
}

enum CompanyPayrollStatus {
    PENDING # Status when payroll report is sent for review at least for one company's member
    PROCESSING # When the payroll report is sent for review for all of the company's members
    COMPLETED # When the member payment is done for all of the company's members.
}
