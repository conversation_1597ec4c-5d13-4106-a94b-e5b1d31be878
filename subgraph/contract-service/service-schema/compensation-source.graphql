extend type Query {
    getCompensationSourceByEntityIds(request: CompensationSourceByEntitiesRequest!): CompensationSourceByEntityIdsResponse  @authorize(company: ["view.company.contract.compensation"], member: ["view.member.contract.compensation"], operations: ["view.operations.contract"])
    getCompensationSourceByContractIds(request: CompensationSourceByContractIdsRequest!): CompensationSourceByContractIdsResponse  @authorize(company: ["view.company.contract.compensation"], member: ["view.member.contract.compensation"], operations: ["view.operations.contract"])
    getCompensationSourceByCountries(request: CompensationSourceByCountriesRequest!): CompensationSourceForCountriesResponse @authorize(company: ["view.company.contract.compensation"], member: ["view.member.contract.compensation"], operations: ["view.operations.contract"])
}

enum CompensationSource {
    CONTRACT_SERVICE
    COMPENSATION_SERVICE
}

input CompensationSourceByEntitiesRequest {
    entityRequests: [EntityRequest!]!
}

input EntityRequest {
    entityId: ID!
    entityType: CompensationEntityType!
}

enum CompensationEntityType {
    COMPANY_ENTITY
    EOR_ENTITY
}

type CompensationSourceByEntityIdsResponse {
    sourceByEntityId: [CompensationSourceMapping!]!
}

input CompensationSourceByContractIdsRequest {
    contractIds: [ID!]!
}

type CompensationSourceByContractIdsResponse {
    sourceByContractId: [CompensationSourceMapping!]!
}

type CompensationSourceMapping {
    id: ID!
    source: CompensationSource!
}

input CompensationSourceByCountriesRequest {
    countryRequest: [CompensationSourceByCountryRequest!]!
}

input CompensationSourceByCountryRequest {
    countryCode: CountryCode!
    contractType: ContractType!
}

type CompensationSourceForCountriesResponse {
    sources: [CompensationSourceByCountryContractTypeResponse!]!
}

type CompensationSourceByCountryContractTypeResponse {
    countryContractType: CompensationCountryContractTypeResponse!
    source: CompensationSource!
}

type CompensationCountryContractTypeResponse {
    countryCode: CountryCode!
    contractType: ContractType!
}
