extend type Query {
    # Contract top-level queries For ops platform purpose only
    contract(
        id: ID!,

        """
        # If "true" => result will contain the contract with any status (ONBOARDING, ACTIVE, OFFBOARDING, ENDED, or DELETED)
        # If "false" => result will filter-out/skip any "ENDED" or "DELETED" contract.
        """
        includeAnyStatus: Boolean = false @deprecated,

        isApprovalRelated: Boolean = false # filter those contracts for which current user is the approver.
    ): Contract @authorize(member: ["view.member.contract"], company: ["view.company.contract"], operations: ["view.operations.contract"])

    contracts(
        """Don't use this parameter. User `filters` instead"""
        onboardingOperationsUserId: ID

        """Don't use this parameter. User `filters` instead"""
        onboardingStatus: ContractOnboardingStatus

        filters: ContractFilters
    ) : [Contract] @authorize(operations: ["view.operations.contract"])

    contractsWithPagination(
        """
        ContractFilters is the filter for this query
        Currently supported these fields:
        1. contractId
        2. country
        3. contractStatus
        4. memberNameContains
        5. companyNameContains
        6. startDateRange
        7. payrollStartDateRange
        8. payrollActiveStatus
        9. contractType
        10. onboardingStatus
        11. excludedTypes
        12. isTestContract
        """
        filters: ContractFilters
        """
        PageRequest is the pagination parameter
        Currently supporting sort by startOn, payrollStart
        """
        pageRequest: PageRequest
        countRequest: ContractsCountRequest
    ) : ContractsResult @authorize(operations: ["view.operations.contract"])

    # To get the onboarding structure when there is no contract.
    onboarding(type: ContractType!, legalEntityId: ID): ContractOnboarding @authorize(company: ["view.company.contract.onboarding"]) #TODO: Move under company?

    contractDetailsReport(filters: ContractFilters): DocumentReadable @authorize(operations: ["view.operations.contract.report"])
    contractPayrollDetailsReport(filters: ContractFilters) : DocumentReadable @authorize(operations: ["view.operations.contract.payroll-report"])

    isPayrollFormSubmittedForContract(contractId:ID!) : Boolean @authorize(operations: ["view.operation.payroll-form-submission"])
    getJobTitleAndDescriptionForContract(contractId:ID!) : JobTitleAndDescription @authorize(company: ["view.company.contract.description-of-work"], operations: ["view.operations.description-of-work"])

    contractAiAnalysis(prompt: String!): ContractAiAnalysisResult! @authorize(operations: ["view.operations.contract.ai-analysis"])
}

extend type Mutation {
    contractCreate(input: ContractCreateInput!): Contract @authorize(company: ["create.company.contract"])
    contractChangeCountry(id: ID!, input: ContractUpdateCountryInput!): Contract @authorize(company: ["update.company.contract.country"], operations: ["update.operations.contract.country"]) # Only works during the definition phase. Affects the overall flow.
    contractUpdateEmployment(id: ID!, input: ContractUpdateEmploymentInput!): Contract @authorize(company: ["update.company.contract.employment"], operations: ["update.operations.contract.employment"])
    contractUpdateTimeOffEntitlements(id: ID!, input: [ContractUpdateTimeOffEntitlementsInput]!): Contract @authorize(company: ["update.company.contract.timeoffentitlements"], operations: ["update.operations.contract.timeoffentitlements"])
    contractUpdatePayrollStartDateBulk(ids: [ID]!, startDate: Date!): [Contract] @authorize(operations: ["update.operations.contract.payroll-start-date"])
    contractChangeWorkStatus(id: ID!, workStatus: CountryWorkStatus!): Contract @authorize(operations: ["update.operations.contract.work-status"])

    contractInviteMember(id: ID!, offerLetter: Boolean): Contract @authorize(company: ["invite.company.contract.members"], operations: ["invite.operations.contract.members"])
    contractInviteMemberResend(id: ID!): Contract @authorize(company: ["resend-invite.company.contract.members"], operations: ["resend-invite.operations.contract.members"])
    contractMemberStarted(id: ID!): Contract @authorize(member: ["update.member.contract.onboarding"], operations: ["update.operations.contract.onboarding"]) #Member: ACTIVE, Contract: ONBOARDING, ContractOnboarding: MEMBER_STARTED
    contractMemberBenefitCompleted(id: ID!): Contract @authorize(member: ["update.member.contract.onboarding"], operations: ["update.operations.contract.onboarding"])
    contractMemberCompleted(id: ID!): Contract @authorize(member: ["update.member.contract.onboarding"], operations: ["update.operations.contract.onboarding"])

    contractStartVerification(id: ID!): Contract @authorize(operations: ["update.operations.contract.onboarding"])
    contractRequestChangesFromMember(id: ID!, description: String): Contract @authorize(company: ["create.company.contract.change-request"], operations: ["create.operations.contract.change-request"])
    contractCompleteVerification(id: ID!): Contract @authorize(operations: ["update.operations.contract.onboarding"])
    contractOfflineSignatureProcess(id: ID!): Contract @authorize(operations: ["update.operations.contract.onboarding"])
    contractActivate(id: ID!): Contract @authorize(operations: ["activate.operations.contract"])

    contractOrderFormSendForSignature(id: ID!): Contract @authorize(company: ["trigger.company.contract.signatures"], operations: ["trigger.operations.contract.signatures"])
    contractSendForSignature(id: ID!): Contract @authorize(company: ["trigger.company.contract.signatures"], operations: ["trigger.operations.contract.signatures"])
    contractSendAgreementToMemberForSignature(id: ID!): Contract @authorize(operations: ["trigger.operations.contract.signatures"]) # For Ops only, disjointed CONTRACTOR signature workflow
    contractSendReminderForSignature(id: ID!): Contract @authorize(company: ["trigger.company.contract.signatures"], operations: ["trigger.operations.contract.signatures"])
    contractCompleteEmployerSignature(id: ID!): Contract @authorize(operations: ["trigger.operations.contract.signatures"])   # For OPs only, in-case they need to update the status to "SIGNATURE_EMPLOYER_SIGNED"
    contractCompleteEmployeeSignature(id: ID!): Contract @authorize(operations: ["trigger.operations.contract.signatures"])   # For OPs only, in-case they need to update the status to "SIGNATURE_EMPLOYEE_SIGNED"

    contractOffboard(id: ID!, lastWorkingDay: Date!, offboardingNote: String) : Contract @authorize(company: ["offboard.company.contract"], operations: ["offboard.operations.contract"]) # Can only OffBoard ACTIVE contracts. Can be called by OFFBOARDING member for changing note and lastWorking day
    contractOffboardingCancel(id: ID!) : Contract @authorize(company: ["offboard.company.contract"], operations: ["offboard.operations.contract"]) # Sets the contract to ACTIVE and removes lastWorkingDay(endedOn in db) and offboardingNote
    contractEnd(id: ID!, lastWorkingDay: Date, file: Upload) : Contract @authorize(operations: ["end.operations.contract"]) #if date not passed, it defaults to current date of system.
    contractDelete(id: ID!): Contract @authorize(company: ["delete.company.contract"], operations: ["delete.operations.contract"])  # Cannot delete ACTIVE contracts
    contractRevoke(id: ID!): Contract @authorize(company: ["revoke.company.contract"], operations: ["revoke.operations.contract"])  # Move contract onboarding back to ONBOARDING_REVIEW, CONTRACT_REVOKED
    contractRevise(id: ID!, input: ContractReviseInput): Contract @authorize(company: ["revise.company.contract"], operations: ["revise.operations.contract"])  # Move contract onboarding back to ONBOARDING_REVIEW, CREATED

    contractTriggerJoingDayEmail(id: ID!, overrideStartOn: Boolean, isBatch: Boolean): TaskResponse @authorize(operations: ["trigger.operations.contract.emails"]) # When isBatch is true, id is Company id
    contractTriggerJoingDayComplianceEmail(id: ID!, overrideStartOn: Boolean): TaskResponse @authorize(operations: ["trigger.operations.contract.emails"])
    contractTriggerCompleteOnboardingReminderEmail(id: ID!, bypassStartOn: Boolean): TaskResponse @authorize(operations: ["trigger.operations.contract.emails"])
    contractTriggerPayrollFormsReminderEmail(input:ContractTriggerPayrollFormsReminderEmailInput): TaskResponse @authorize(operations: ["trigger.operations.contract.emails"])
    contractTriggerFixedTermEndingEmail: TaskResponse @authorize(operations: ["trigger.operations.contract.emails"])

    contractConfirmWetInkContractPrepare(id: ID!) : Contract @authorize(company: ["update.company.contract.onboarding"], operations: ["update.operations.contract.onboarding"]) # For employers in countries with wet ink contract requirement
    contractConfirmContractPrepare(id: ID!) : Contract @authorize(company: ["update.company.contract.onboarding"], operations: ["update.operations.contract.onboarding"]) # For employees in countries without contract, called upon clicking Confirm to prepare the contract
    contractCompleteVisaEligibilityReview(id: ID!) : Contract @authorize(operations: ["update.operations.contract.onboarding"])     # For employees require VISA, called upon clicking Complete Verify Visa Eligibility
    contractConfirmVisaInvoicePaid(id: ID!) : Contract @authorize(operations: ["update.operations.contract.onboarding"])            # For employees require VISA, called upon clicking Confirm VISA Invoice Paid
    contractConfirmVisaApplicationFiling(id: ID!) : Contract @authorize(operations: ["update.operations.contract.onboarding"])      # For employees require VISA, called upon clicking Confirm Visa Application Filing
    contractConfirmVisaApproved(id: ID!) : Contract @authorize(operations: ["update.operations.contract.onboarding"])               # For employees require VISA, called upon clicking Confirm Visa Approved

    # single onboarding
    contractAddDirectEmployee(id: ID!): Contract @authorize(company: ["update.company.contract.onboarding"], operations: ["update.operations.contract.onboarding"]) # For hr_member enabled Single Onboarding, called upon clicking Add Employee in the Review Member Details screen

    # Bulk onboarding
    contractCreateBulk(file: Upload!, contractType: ContractType, companyId: ID, legalEntityId: ID): TaskResponse @deprecated(reason: "Please use bulk upload feature") @authorize(company: ["create.company.contract.bulk"], operations: ["create.operations.contract.bulk"])   # Create bulk contracts (based on the uploaded file data)
    contractInviteMemberBulk(contractIds: [ID]!): TaskResponse @authorize(company: ["invite.company.contract.members"], operations: ["invite.operations.contract.members"])  # Send bulk member invites

    contractSetTest(id: ID!, isClientTest: Boolean): Contract @authorize(company: ["update.company.contract.is-test"], operations: ["update.operations.contract.is-test"]) # Mutation to tag contract as "Test" for the company.
    contractCreateBulkPaymentAccount(file: Upload!): TaskResponse @deprecated(reason: "deprecating since it has no usage") @authorize(operations: ["create.operations.contract.bulk"]) #creates and attaches the payment account to contract
    payrollNoteForContract(contractId: ID!, payrollNote: String): Contract @authorize(operations: ["update.operations.contract.payroll-note"]) #Adding/Updating note(optional) to the contract for the payroll

    contractWorkEmailUpdate(id: ID!, email: EmailAddressInput!): Contract @authorize(company: ["update.company.contract.work-email"], operations: ["update.operations.contract.work-email"]) # insert contract work email, this will also create/update bamboohr account for a contract

    contractUnblock(inputs: [ContractUnblockInput!]!): Contract @authorize(operations: ["unblock.operations.contract"])
    contractChangeRequestUpdate(input: ContractChangeRequestUpdateInput): Contract @authorize(operations: ["update.operations.contract.changerequests"]) # Update existing change request with new description
    contractChangeRequestStatusUpdate(changeRequestId: ID!, status: ChangeRequestStatus!): Contract @authorize(operations: ["update.operations.contract.changerequests"]) # Transition change request

    contractWorkShiftBulkUpdate(inputs: [ContractBulkUpdateWorkShiftInput!]!): TaskResponse @authorize(operations: ["update.operations.contract.work-shift"]) # Ops only, update work shift

    generateDepositForContract(contractID: ID!): CompanyPayable @authorize(operations: ["generate.operations.payable"])
    updateContractInfo(input: UpdateContractInfoInput): Contract @authorize(company: ["update.company.contract.description-of-work"])
    sendContractToContractor(contractId: ID!, isApprovedDOW: Boolean): Contract @authorize(company: ["update.company.contract.description-of-work"])
}

# --------------------------------------
# Mutation types....
# --------------------------------------
input UpdateContractInfoInput {
    dowId: ID
    contractId: ID!
    jobTitle: String
    description: String
}

input ContractBulkUpdateWorkShiftInput {
    ids: [ID!]!
    workShift: WorkShiftInput
}

input ContractReviseInput {
    # If true, move contract onboarding back to ONBOARDING_REVIEW / CREATED even if it's called by ops
    # Otherwise move contract onboarding to ONBOARDING_CONTRACT_UPDATING / CONTRACT_UPDATING (ops) or ONBOARDING_REVIEW / CREATED (customer)
    isReviseByOpsAfterSend: Boolean
}

input ContractUpdateCountryInput {
    country: CountryCode
    legalEntityId: ID,
    countryStateCode: String
    workStatus: CountryWorkStatus
    alreadyHired: Boolean
}

input ContractCreateInput {
    memberId: ID! # Should not be used anymore
    companyId: ID
    legalEntityId: ID
    type: ContractType!
    country: CountryCode!
    countryStateCode: String
    workStatus : CountryWorkStatus
    position: String
    scope: String               # Job Description
    term: ContractTerm
    startOn: DateTime
    endOn : DateTime
    alreadyHired: Boolean       # HR_MEMBER-specific property
    workShift: WorkShiftInput   # Contract's standard working days and hours. (E.g: from Monday to Friday, from 08:30 to 17:30)
    employeeId: String
    workEmail: String
    email: String!
    complianceConsent: ComplianceConsent
}

input ComplianceConsent {
    aor: AorComplianceConsentInput
}

input AorComplianceConsentInput {
    workingPracticesMatchAorAgreement: Boolean
    workerCharacterizationSupported: Boolean
}

input ContractUpdateEmploymentInput {
    country: CountryCode
    position: String
    scope: String               # Job Description
    term: ContractTerm
    startOn: DateTime
    endOn : DateTime
    workShift: WorkShiftInput   # Contract's standard working days and hours. (E.g: from Monday to Friday, from 08:30 to 17:30)
    employeeId: String
    workEmail: String
}

input WorkShiftInput {
    startDay: DayOfWeek!                # Work shift start day. (E.g: Monday)
    endDay: DayOfWeek!                  # Work shift end day. (E.g: Friday)
    breakHours: WorkingHoursInput       # Break hours per day in 24-hour format. (E.g: from 13:00 to 14:00)
    workingHours: WorkingHoursInput!    # Working hours per day in 24-hour format. (E.g: from 08:30 to 17:30)
}

input WorkingHoursInput {
    startTime: Time!   # Work start time (in 24-hour format) per day
    endTime: Time!     # Work end time (in 24-hour format) per day
}

input ContractUpdateTimeOffEntitlementsInput {
    key: String
    value: Float
    unit: TimeOffUnit
}

input ContractsCountRequest {
    all: Boolean
    onboarding: Boolean
    verification: Boolean
    testUser: Boolean
}


input ContractTriggerPayrollFormsReminderEmailInput {
    contractIds: [ID]
    todayDateAs: Date
}

input ContractUnblockInput {
    blockEventId: ID!
    reason: String!
    dataChanges: [ContractUnblockDataChangeInput!]
}


input ContractUnblockDataChangeInput {
    category: String!           #    Ex: member_details
    changes: [String!]          #    Ex: [first_name, last_name]
}

input ContractChangeRequestUpdateInput {
    changeRequestId: ID!
    description: String
}

input ChangeRequestConfigFiltersInput {
    contractId: ID
}

# --------------------------------------
# Query types....
# --------------------------------------

type ContractsResult {
    data: [Contract]
    payrollActiveCount: Int
    pageResult: PageResult
    countResult: ContractsCountResult
}

type Contract @key(fields: "id") {
    id: ID
    type: ContractType
    member: Member              # need Entity Fetcher in member-service
    company: Company            # need Entity Fetcher in company-service
    status: ContractStatus @authorize(company: ["view.company.contract.status"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    legalEntityId: ID
    multiplierLegalEntityId: ID
    contractingEntityType: ContractingEntityType
    contractingEntityId: ID
    workplaceEntityId: ID
    country: CountryCode
    countryStateCode: String    # We may need data migration for old alpha values
    currency: CurrencyCode
    workStatus: CountryWorkStatus
    position: String @authorize(company: ["view.company.contract.designation"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    term: ContractTerm @authorize(company: ["view.company.contract.employment-type"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    activatedOn: DateTime
    startOn: DateTime @authorize(company: ["view.company.contract.start-date"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    endOn: DateTime @authorize(company: ["view.company.contract.end-date"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    endedOn: DateTime @authorize(company: ["view.company.contract.end-date"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    lastWorkingDay: Date
    createdBy: ID
    createdOn: DateTime
    updatedBy: ID
    updatedOn: DateTime
    compliance: Compliance @authorize(company: ["view.company.contract.compliance"], operations: ["view.operations.contract.compliance"], member: ["view.member.contract.compliance"])
    renewal: ContractRenewalInfo
    # TODO: operations permission need to be updated to be more granular @Janko
    compensation: Compensation @authorize(company: ["view.company.contract.compensation"], member: ["view.member.contract.compensation"], operations: ["view.operations.contract"])
    performanceReviews(id: ID, status: PerformanceReviewStatus): [PerformanceReview]
    salaryReviews(id: ID, status: SalaryReviewStatus): [SalaryReview] @deprecated(reason: "Unused since Q1-2022. PerformanceReview is the actual main entity in most cases")
    alreadyHired: Boolean               # HR_MEMBER-specific property
    onboarding: ContractOnboarding      # Only available during onboarding process
    scope: String   # To capture data in old alpha. This is Job Description
    workEmail: String @authorize(company: ["view.company.contract.work-email"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    isPayrollActive: Boolean

    # Flag is used by FE to determine which of [ONBOARDING, ACTIVE] treatment to apply for Member, Company experience
    # For each experience, it is calculated based the contract start date and status
    contractStarted: Boolean

    fieldAccessibilityRules: [FieldAccessibilityRule]

    isTestContract: Boolean  # Flag to identify contracts tagged as Test for the customer.
    isTest: Boolean
    payrollNote : String

    paymentAccountRequirements: [PaymentAccountRequirement] @deprecated # moving to contractPaymentAccount

    """
    Ideally it should be https://app.clickup.com/t/3rch1m9, but we need a quick solution now, so doing this.<br>
    Of course can't define another `paymentAccount: PaymentAccount` because supergraph will fail to build due to "already defined in payment-account.graphql
    """
    paymentAccountId: ID @deprecated

    contractPaymentAccount: ContractPaymentAccount

    offboardingNote: String
    addendums(addendumIds : [ID]) : [ContractAddendum]
    offboardingAudit: OffboardingAudit

    """
    Contract's standard working days and hours. (E.g: from Monday to Friday, from 8:30 to 17:30)
    """
    workShift: WorkShift
    depositAmount: Float  # Gross Monthly Salary * Notice Period Months

    changeRequests(changeRequestIds: [ID!]): [ContractChangeRequest!]   # return all change requests of given contract if id list is not given
    contractBlocked: Boolean
    unresolvedBlockEvents: [BlockEvent!]
    employeeId: String @authorize(company: ["view.company.contract.employee-id"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    payoutScheduleId: String @authorize(member: ["view.member.payout-schedule"], company: ["view.company.payout-schedule"])

    incomingPayrollCutoffDate: Date
}

type ContractRenewalInfo {
    definition: FixedTermRestrictionDefinition
    remainingRenewalCount: Int
    remainingTenureInMonths: Int
}

type WorkShift {
    startDay: DayOfWeek         # Work shift start day. (E.g: Monday)
    endDay: DayOfWeek           # Work shift end day. (E.g: Friday)
    workingHours: WorkingHours  # Working hours per day in 24-hour format. (E.g: from 08:30 to 17:30)
    breakHours: WorkingHours    # Breaking hours per day in 24-hour format. (E.g from 13:00 to 14:00)
}

type WorkingHours {
    startTime: Time   # Work start time (in 24-hour format) per day
    endTime: Time     # Work end time (in 24-hour format) per day
}

type ContractsCountResult {
    all: Int
    onboarding: Int
    verification: Int
    testUser: Int
}

type OffboardingAudit {
    initiatedBy: Person       # User that initiated offboarding e.g: CompanyUser or OperationsUser
    initiatedOn: DateTime     # Time of the initiation happened
}

type ContractChangeRequest {
    id: ID!
    status: ChangeRequestStatus
    contractId: ID!
    description: String!
    createdOn: DateTime
    updatedOn: DateTime
}

type BlockEvent {
    id: ID!
    source: BlockSource!
    status: BlockStatus!
    contractId: ID!
    contractStatus: ContractStatus!
    createdOn: DateTime

    blocker: BlockActor!
    blockReason: String!
    blockData: BlockData
    turnAroundTimeInHours: Int

    unblockerCandidates: [BlockActor!]
    unblocker: BlockActor
    unblockReason: String
}

type JobTitleAndDescription {
    jobTitle: String
    jobDescription: String
}

type BlockActor {
    type: BlockActorType!
    userId: ID!
    domainUserId: ID # this can be OperationsUserId or CompanyUserId or MemberId
}

union BlockData = ChangeRequestBlockData

type ChangeRequestBlockData {
    changeRequestId: ID!
}

type ContractChangeRequestConfig {
    turnAroundTimeInHours: Int
    earliestJoiningDateWithTurnAround: Date
}

type Configuration @key(fields: "id") @extends {
    id: ID @external
    contractChangeRequestConfig(filters: ChangeRequestConfigFiltersInput): ContractChangeRequestConfig
}

type ContractAiAnalysisResult {
    result: String!
}

# --------------------------------------
# Enums
# --------------------------------------

enum BlockStatus {
    BLOCKED
    UNBLOCKED
}

enum BlockActorType {
    OPS_USER
    COMPANY_USER
    MEMBER
}

enum BlockSource {
    CONTRACT_CHANGE_REQUEST
}

enum ChangeRequestStatus {
    CREATED
    UPDATED
    VIEWED
    RESOLVED
}
