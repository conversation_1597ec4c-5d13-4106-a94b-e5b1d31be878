type Company @key(fields: "id") @extends {
    id: ID @external

    """
    # If includeAnyStatus = true => result will contain contracts with any statuses (ONBOARDING, ACTIVE, OFFBOARDING, ENDED, or DELETED)
    # If includeAnyStatus = false => result will filter-out/skip any "ENDED" or "DELETED" contracts.
    """
    contracts(status: ContractStatus, contractId: ID, category: ApprovalCategory, includeAnyStatus: Boolean = false, companyContractsFilter: CompanyContractsFilter): [Contract] @authorize(company: ["view.company.contract"], operations:["view.operations.contract"])

    """
    Similar to `contracts`, but will be dedicated for Team View page (as part of HRIS).<br>
    The main difference is around Reporting Managers now being able to see Team View page (only their direct/indirect subordinates).<br>
    Super Admin (or similar role) keeps seeing all contracts. Abac logic and most stuff are also copied over.<br>
    Details: https://app.clickup.com/t/86cvd78qw | https://multiplier-group.slack.com/archives/C02B2S7A83T/p1715925057440889?thread_ts=1715854492.186609&cid=C02B2S7A83T<br>
    """
    contractsWithPagination(
        filter: ContractsFilter,
        pageRequest: PageRequest
    ): PaginatedContractsResult @authorize(company: ["view.company.contract"], operations:["view.operations.contract"])

    performanceReviews(id: ID, status: PerformanceReviewStatus): [PerformanceReview] @authorize(company: ["view.company.contract.compensation"], operations:["view.operations.contract"])
    salaryReviews(id: ID, status: SalaryReviewStatus): [SalaryReview] @deprecated(reason: "Unused since Q1-2022. PerformanceReview is the actual main entity in most cases")
}

input CompanyContractsFilter {
    isApprovalRelated: Boolean = false # filter only those contracts for which current user is the approver.
    statuses: [ContractStatus]
    types: [ContractType]
}

input FixedTermRestrictionFilter {
    contractType: ContractType
}

input ContractsFilter {
    status: ContractStatus
    contractId: ID
    includeAnyStatus: Boolean = false
    contractView: ContractView
    needsAttention: Boolean
    searchQuery: String
}

enum ContractView {
    ACTIVE
    ONBOARDING
    OFFBOARDING
    INACTIVE
    ENDED
    BLOCKED
    ALL
}

type OperationsUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type CompanyUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type EmergencyPointOfContact implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type EORPartnerCountry @key(fields: "id") @extends {
    id: ID @external
}

type Member implements Person @key(fields: "id") @extends {
    id: ID @external

    contracts (
        # Filter by given contract status
        status: ContractStatus,

        # If "true" => result will contain contracts with any statuses (ONBOARDING, ACTIVE, OFFBOARDING, ENDED, and DELETED)
        # If "false" => result will NOT contain any "ENDED" and "DELETED" contracts.
        includeAnyStatus: Boolean = false,

        # If "true" => result will contain contracts with any member statuses
        # If "false" => result will NOT contain any "SUSPENDED" and "DELETED" members.
        includeAnyMemberStatus: Boolean = false
    ): [Contract]

    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type State @key(fields: "code") @extends {
    code: String @external
}

type FixedTermRestrictionDefinition {
    maxRenewalCount: Int # -1 means no limit, 0 means not allowed
    maxRenewalDurationInMonths: Int
    maxTenureInMonths: Int # -1 means no limit
    minRenewalDurationInMonths: Int
}

type CountryCompliance @key(fields: "countryCode countryState { code }") @extends {
    countryCode: CountryCode @external
    countryState: State @external
    fixedTermRestriction(filter: FixedTermRestrictionFilter): FixedTermRestrictionDefinition
}

type CompanyPayable @key(fields: "id") @extends {
    id: ID! @external
}

type ContractViewCounts {
    active: Int
    onboarding: Int
    offboarding: Int
    blocked: Int
    inactive: Int
    ended: Int
    all: Int
    needsAttention: Int
}

type PaginatedContractsResult {
    data: [Contract]
    pageResult: PageResult
    viewCounts: ContractViewCounts
}

type MultiplierWallet @key(fields: "id") @extends {
    id: ID! @external
}
