extend type Mutation {
    contractAssignOperationsUser(contractId: ID!, operationsUserId: ID!): Contract @authorize(operations: ["assign.operations.contract"])
    contractUnassignOperationsUser(contractId: ID!): Contract @authorize(operations: ["assign.operations.contract"])
    contractDrop(input: ContractDropInput!): Contract @authorize(operations: ["drop.operations.contract.onboarding"])
}

# --------------------------------------
# Mutation types....
# --------------------------------------

input ContractOnboardingInput {
    completed: ContractOnboardingStep
    payload: ContractOnboardingParamInput
}

input ContractOnboardingParamInput {
    key: String
    value: String
}

input ContractDropInput {
    contractId: ID!
    note: String
}

# --------------------------------------
# Query types....
# --------------------------------------

type ContractOnboarding @key(fields: "id") {
    id: ID,
    status: ContractOnboardingStatus
    statuses:[ContractOnboardingStatus]
    steps: [ContractOnboardingStep]
    current: ContractOnboardingStep
    payload: [ContractOnboardingParam]
    contractDocumentWorkflow: ContractDocumentWorkflow # Contract Document's workflow
    contract: Contract
    isBulkOnboarded: Boolean
}

type ContractDocumentWorkflow {
    currentStatus: ContractDocumentStatus
    statuses: [ContractDocumentStatus]
}

type ContractOnboardingParam {
    key: String
    label: String
    value: String
}

enum ContractDocumentStatus {
    CUSTOM_CONTRACT
    DRAFT_CONTRACT
    PREPARING_CONTRACT
    SIGNING_IN_PROGRESS
    EMPLOYER_SIGNED,
    EMPLOYEE_SIGNED,
    CONTRACT_SIGNED
    CONTRACT_NOT_SIGNED
}
