extend type Mutation {
    updateContractPaymentDetails(id: ID!, input: ContractUpdatePaymentAccountInput!): ContractPaymentAccount @authorize(operations: ["update.operations.contract.payment-details"])
}

input ContractUpdatePaymentAccountInput {
    preferredPayOutType: TransferType
}

type ContractPaymentAccount @key(fields: "id") {
    id: ID
    paymentAccountId : ID
    configurations: ContractPaymentAccountConfig
    supportedPaymentOptions: [PaymentOption] @authorize(member: ["view.member.contract-payment-account.payment-options"], operations:["view.operations.contract-payment-account.payment-options"]) @deprecated

    contractPaymentOptions: [ContractPaymentOption]
    contractPaymentOptionSupport(filters: [ContractPaymentOptionsRequest!]!): [PaymentOption]

    multiplierWallet: MultiplierWallet
    walletOptions(filters: WalletOptionsFilter): [WalletOptions]
    walletWithdrawalOptions(filters: WalletWithdrawalOptionsFilter!): [WalletWithdrawalOption]
}

type PaymentOption {
    payOutCountry: CountryCode
    payOutCurrency: CurrencyCode
    sourceCurrency: CurrencyCode
    accountType: PaymentAccountType
    transferType: TransferType
    paymentPartner: PaymentPartnerCode
    partnerAccountRequirements: PartnerAccountRequirement
}

type ContractPaymentAccountConfig {
    preferredPayOutType: TransferType
}

type PartnerAccountRequirement {
    paymentAccountRequirementType: String
    requirementFields: [RequirementField]
}

type PaymentAccountRequirement {
    accountType: PaymentAccountType
    transferType: TransferType
    sourceCurrency: CurrencyCode
    targetCurrency: CurrencyCode
    paymentPartner: PaymentPartnerCode
    paymentAccountRequirementType: String
    requirementFields: [RequirementField]
}

type RequirementField {
    key: String
    label: String
    isMandatory: Boolean
    type: String
    regex: String
    allowedValues: [AllowedValue]
    hasSubFields: Boolean
    subFields: [RequirementSubField]
    errorMessage: String
}

type RequirementSubField {
    key: String
    value: [RequirementField]
}

type AllowedValue {
    key: String
    value: String
}
