#------------------------------------------------------
#            Value types from country graph
#------------------------------------------------------

enum ComponentType {
    FIXED               # replaced by PayAmountType.FIXED_AMOUNT
    PERCENTAGE          # replaced by PayAmountType.FIXED_PERCENTAGE
    VARIABLE_AMOUNT
    VARIABLE_PERCENTAGE
}

# Input parameters for member data requirements
input MemberDataRequirementsFilter {
    workStatus: CountryWorkStatus
    contractType: ContractType
    fetchStage: FetchStage
    onlyDynamicRequirements: Boolean
}

input RequirementFiltersInput {
    workStatus: CountryWorkStatus
    contractType: ContractType
    contractTerm: ContractTerm
    contractStatus: ContractStatus
    applyTo: LegalDataRequirementApplicability
    joinDate: Date
    financialYear: Int
    category: LegalDocumentCategory
}

# Definition
# The legal entity by country. It could be multiplier or partner entity
type CountryLegalEntity {
    type: LegalBindingType
    legalEntity: LegalEntity
}

# Definition
# The compliance requirement definitions for country.
type ComplianceRequirementDefinition {
    usedScope: ComplianceDefinitionScope       # This indicate whether this compliance requirement definitions are country specific or default ones. (Countries with no compliance will get default set of definitions)
    paramDefinitions: [ComplianceParamDefinition]   # Compliance param definitions.
}

# Definition
# This refers to configuration that provides the validation ruleset for a particular
# compliance param. This is meant for consumers to determine how best to mutate the compliance
# params.
type ComplianceParamDefinition {
    param: ComplianceParam
    required: Boolean # determines whether a clause can be removed from the eventual contract
    editable: Boolean # determines whether a clause's details can be edited, i.e. 3 months -> 2 months
    enabled: Boolean  # determines whether a clause is enabled/disabled by default
    validation: [ComplianceParamValidation]
    dataFieldType: ComplianceParamDataFieldType

    # ComplianceParams listed here must be enabled before this ComplianceParam can be enabled
    # Consumers should automatically disable this ComplianceParam if any in the dependency list is disabled
    dependencies: [ComplianceParam]
}

union ComplianceParamValidation = ComplianceParamPeriodLimitUnitValidation

# Definition
# Validations specific to period limits
type ComplianceParamPeriodLimitUnitValidation {
    minimum: Int
    maximum: Int
    defaultValue: Int
    unit: ComplianceParamPeriodUnit
}

type DocumentTemplate {
    key: String                 # Key to identify a type of document
    templateId: String          # The DocGen template id.
    usedScope: ComplianceDefinitionScope
}

"""
Defines a data field definition so the UI can render accordingly.
The intention is to replace all the dynamic UI generation definitions by this
i.e. LegalDataRequirement, LegalDocument etc...
"""
type DataFieldDefinition {
    id: ID
    domainType: DomainType          # The domain of the data whether it is member data or legal data
    key: String                     # A identifier/label with some possible-uniqueness...
    label: String                   # label for the field i.e. Passport No
    description: String             # The description probably to show as a help text
    required: Boolean               # whether this field is required
    dataType: DataFieldType         # whether its a Text Field, Date Field etc...
    dependsOn: [ControllingField!]
    editable: Boolean
}

union DataFieldType = TextField
    | DateField
    | DropDownField
    | DropDownTextField
    | CheckboxField
    | AutoCompleteField
    | FileField
# Can extend with other new data types i.e. DocUploadField etc...

type TextField {
    minLength: Int
    maxLength: Int
    pattern: String             # A regular expression to match the text field
    defaultValue: String        # default value or the current value
    type: String                # Type of the text input - Could be ALPHANUMERIC or NUMERIC or TEXT
}

type DropDownField {
    values: [String]
}

type DropDownTextField {
    values: [String]        # Drop down values
    defaultText: String     # Default input text or the current text value
    minLength: Int          # Min character length for text input field
    maxLength: Int          # Max character length for text input field
    type: String            # Type of the text input - Could be ALPHANUMERIC or NUMERIC or TEXT
}

type DateField {
    minDate: Date
    maxDate: Date
    defaultValue: Date
}

type CheckboxField {
    defaultValue: Boolean!
}

type AutoCompleteField {
    optionListType: String
}

type ControllingField {
    key: String!
}

type FileField {
    acceptMultiple: Boolean
}

# Definition
# Defines a legal document. Ex: Passport, NIC, Adhar, Social No, Europe, UTR, Britain, Driver's License
type LegalDocumentRequirement {
    key: String                     # A identifier/label with some possible-uniquness...
    label: String                   # Country-specific local name..
    description: String
    category: LegalDocumentCategory
    required: Boolean
    startDate: Date
    endDate: Date
    acceptMultiple: Boolean
    applyTo: LegalDataRequirementApplicability
    template: [DocumentReadable]
    deadline: Date
    legalDocumentInfo: String
}

type Holiday {
    key: String  # a unique key for this holiday
    label: String # kebab-case version of the key
    year: Int
    month: Int
    date: Int
    countryCode: CountryCode
    name: String
    type: String    # type of holiday (ex: special vs national holidays)
}
