extend type Mutation {
    upsertPerformanceReview(review: PerformanceReviewInput!) : PerformanceReview @authorize(company: ["create.company.contract.compensation-abac"], operations: ["create.operations.contract.compensation"])
    deletePerformanceReview(id: ID!) : [PerformanceReview] @authorize(company: ["delete.company.contract.compensation"], operations: ["delete.operations.contract.compensation"])
    sendToSignatoryPerformanceReview(reviewId: ID!) : PerformanceReview @authorize(company: ["create.company.contract.compensation"], operations: ["create.operations.contract.compensation"])
    activateReviews(date: DateTime) : TaskResponse @authorize(operations: ["activate.operations.contract.compensation"])
    """86cuedugx - For Ops to sync offline-signed salary revisions to the system. Note: only EMPLOYEE (EOR) & HR_MEMBER (PEO) are supported"""
    performanceReviewBulkCreateAsApproved(inputs: [PerformanceReviewBulkInput!]!) : TaskResponse @authorize(operations: ["create.operations.contract.compensation"])
    backfillPerformanceReviewApprovedOn(ids: [ID!]!) : [PerformanceReview] @authorize(operations: ["create.operations.contract.compensation"])
    upsertPerformanceReviewV2(review: PerformanceReviewInputV2!) : PerformanceReview @authorize(company: ["create.company.contract.compensation-abac"], operations: ["create.operations.contract.compensation"])
    activatePerformanceReview(input: ActivatePerformanceReviewInput) : TaskResponse @authorize(operations: ["activate.operations.contract.compensation"])
}

extend type Query {
    performanceReviewNotes(input: PerformanceReviewNoteInput): PerformanceReviewNote @authorize(company: ["view.company.contract.compensation"], operations: ["view.operations.contract.compensation"])
}

input PerformanceReviewBulkInput {
    performanceReview: PerformanceReviewInput!          # details of the PR, required
    addendumDocument: Upload                            # the offline addendum that client & member have signed, optional
    companySignedOn: DateTime                           # usually mentioned in the addendum, in UTC; if null is given, BE will set it to now()
    memberSignedOn: DateTime                            # usually mentioned in the addendum, in UTC; if null is given, BE will set it to now()
}

input PerformanceReviewInput {
    id: ID
    contractId: ID!
    salaryReview: SalaryReviewInput
    designation: DesignationInput
    effectiveDate: DateTime!
    comment: String
}

input PerformanceReviewInputV2 {
    id: ID
    contractId: ID!
    salaryReview: SalaryReviewInputV2
    designation: DesignationInput
    effectiveDate: DateTime!
    comment: String
}
input DesignationInput {
    name: String!
    jobDescription: String!
}

input SalaryReviewInput {
    id: ID
    contractId: ID!
    revisedCompensation: ContractUpdateCompensationInput!
    effectiveDate: DateTime!
    comment: String
}

input SalaryReviewInputV2 {
    id: ID
    contractId: ID!
    revisedCompensation: [[CompensationComponentDetailsInput!]!]!
    effectiveDate: DateTime!
    comment: String
}

input ActivatePerformanceReviewInput {
    performanceReviewId: ID!
    inputs: [CompensationDataInput!]!
}

input CompensationDataInput {
    id: String!
    keyValueInputs: [KeyValueInput!]!
}

input CompensationComponentDetailsInput{
    key: String!
    value: String
}
# This separation was an overkill, but still in use today (as a field in type PerformanceReview)
type SalaryReview {
    id: ID!
    contract: Contract
    revisedCompensation: Compensation # some other name
    currentCompensation: Compensation # For base pay
    state: SalaryReviewStatus
    comment: String
    effectiveDate: DateTime
    createdOn: DateTime
    reviewDocument: DocumentReadable @deprecated(reason: "Unused since Q1-2022. PerformanceReview is the actual main entity in most cases")
    revisedCompensationRecords: [SalaryReviewCompensationDetails!]
    currentCompensationRecords: [SalaryReviewCompensationDetails!]
}

type SalaryReviewCompensationDetails {
    entityId: ID!
    contractId: ID!
    recordDetails: [SalaryReviewCompensationRecordDetails!]!
}

type SalaryReviewCompensationRecordDetails {
    id: String!
    componentDetails: [SalaryReviewCompensationComponentDetails!]!
}

type SalaryReviewCompensationComponentDetails {
    key: String!
    value: String!
    type: SalaryReviewCompensationFieldType!
}

enum SalaryReviewCompensationFieldType {
    BOOLEAN
    DATE
    DOUBLE
    INTEGER
    STRING
}

enum SalaryReviewStatus {
    DRAFT,
    SENT_FOR_APPROVAL, # For company signatory, name kept as SENT_FOR_APPROVAL for backward compatibility
    SENT_FOR_MEMBER_APPROVAL,
    APPROVED,
    DECLINED @deprecated(reason: "No usage found in the code and DB"),
    ACTIVATED,
    SENT_TO_OPS
}

type PerformanceReview @key(fields: "id") {
    id: ID!
    contract: Contract
    salaryReview: SalaryReview
    status: PerformanceReviewStatus
    designation: Designation  @authorize(company: ["view.company.contract.designation"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    previousDesignation: Designation
    effectiveDate: DateTime @authorize(company: ["view.company.performance.effective-from"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    comment: String
    createdOn: DateTime
    reviewDocument: DocumentReadable @authorize(company: ["view.company.performance.salary-review.review-document"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    companySignedOn: DateTime
    memberSignedOn: DateTime # also when PR becomes APPROVED
}

type Designation {
    name: String
    jobDescription: String
}

enum PerformanceReviewStatus {
    DRAFT,
    SENT_FOR_APPROVAL, # For company signatory, name kept as SENT_FOR_APPROVAL for backward compatibility
    SENT_FOR_MEMBER_APPROVAL,
    APPROVED,
    DECLINED @deprecated(reason: "No usage found in the code and DB"),
    ACTIVATED,
    SENT_TO_OPS
}

input PerformanceReviewNoteInput {
    effectiveDate: Date!        # selected from calendar by the user
    currentDate: Date           # for testing purpose only, ignore this
    contractId: ID              # BE will need this to find the expected payroll cycle if `effectiveDate.month` is the current/past month, so just pass it
}

# (outdated) Refer to: https://www.notion.so/usemultiplier/Salary-Revision-Changes-On-dates-68e91e027a7844148e1a84c2ee1fd113#ea5147baeeb7487989c3402823ec76e9
# Up to date: https://www.notion.so/usemultiplier/Salary-Revision-Revise-GetSalaryRevisionNotes-query-performanceReviewNotes-for-Communications-5668b6370c844c8283ac1af601d2d766
type PerformanceReviewNote {
    message: String @deprecated(reason: "FE should build the notes. BE will be returning null to avoid any confusion")
    month: Int                                      # the payroll cycle MONTH which is expected to pick up this performance review
    year: Int                                       # the year of the `month`, to be clear if necessary
    semimonthlySegment: SemimonthlySegment          # Which half of the month that the payroll cycle is indicating, only for SEMIMONTHLY. The other frequencies will have this `null`
    payrollCyclePayDate: Date                       # The pay date of the cycle. Can be null when no specific cycle found. This is mainly to show BIWEEKLY clauses
    cutOffDate: Date                                # if null: show only "...before <month> cut off"; if not null: show "...before formatDependingOnDesign(<cutOffDate>)>
    type: PerformanceReviewNoteType                 # simply to show different wording clauses on FE; should not affect the core logic
}

enum SemimonthlySegment {
    FIRST_HALF               # 1st half of month, e.g. (early cut off: Sep 1-15, cutOff = Aug 31, payDate = Sep 15) or (late cut off: Aug 16-31, cutOff = Aug 31, payDate = Sep 10)
    SECOND_HALF              # 2nd half of month, e.g. (early cut off: Sep 16-30, cutOff = Sep 15, payDate = Sep 30) or (late cut off: Sep 1-15, cutOff = Seo 15, payDate = Sep 25)
}

enum PerformanceReviewNoteType {
    REFLECTIVE  # "Arrears will..." clause
    EFFECTIVE   # "Updated compensation will..." clause
}
