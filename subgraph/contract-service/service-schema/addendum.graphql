extend type Mutation {
    contractAddendumGenerate(addendumId : ID!) : ContractAddendum! @authorize(operations: ["update.operations.contract.addendum"])
    contractAddendumSendForSignature(addendumId : ID!) : ContractAddendum @authorize(operations: ["update.operations.contract.addendum"])
}

type ContractAddendums {
    addendums : [ContractAddendum]
}

type ContractAddendum {
    id : ID
    document : DocumentReadable
    createdOn : DateTime
    addendumOnboarding : ContractAddendumOnboarding
}

type ContractAddendumOnboarding {
    id : ID
    workflows : [ContractAddendumWorkflow]
}

type ContractAddendumWorkflow {
    experience : Persona
    currentStatus : ContractAddendumWorkflowStatus
}

enum ContractAddendumStep {
    GENERATE
    SENT
    SIGNING
}

enum ContractAddendumWorkflowStatus {
    ADDENDUM_GENERATION_PENDING
    ADDENDUM_GENERATED
    ADDENDUM_SENT_FOR_SIGNING
    ADDENDUM_SIGNED_COMPANY
    ADDENDUM_SIGNED_MEMBER
    ADDENDUM_SIGNED_MULTIPLIER
}
