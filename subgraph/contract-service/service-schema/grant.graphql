input GrantInput {
    id: ID
    value: GrantValueInput #deprecated
    values: [GrantValueInput!]
    schedule: GrantScheduleInput!
    grantDate: Date!
    condition: String
}

input GrantValueInput {
    value: Float!
    type: GrantValueType!
    currency: CurrencyCode
}

input GrantScheduleInput {
    cliffPeriod: ScheduleTimeInput
    vestingPeriod: ScheduleTimeInput
    vestingSchedule: [VestingScheduleInput]
}

input VestingScheduleInput {
    time: ScheduleTimeInput!
    vestingValue: VestingValueInput!
    frequency: VestingFrequency!
}

input VestingValueInput {
    value: Float!
    type: VestingValueType!
}

interface Grant {
    id: ID!
    value: GrantValue @deprecated
    values: [GrantValue!]
    schedule: GrantSchedule!
    grantDate: Date!
    condition: String
    document: File
}

type ESOPGrant implements Grant {
    id: ID!
    value: GrantValue @deprecated
    values: [GrantValue!]
    schedule: GrantSchedule!
    grantDate: Date!
    condition: String
    document: File
}

# type RSUGrant implements Grant {}
# type StockGrant implements Grant {}

type GrantSchedule {
    cliffPeriod: ScheduleTime
    vestingPeriod: ScheduleTime
    vestingSchedule: [VestingSchedule]
}

type VestingSchedule {
    time: ScheduleTime
    vestingValue: VestingValue
    frequency: VestingFrequency
}

type VestingValue {
    value: Float!
    type: VestingValueType!
}

type GrantValue {
    value: Float!
    type: GrantValueType!
    currency: CurrencyCode
}

enum VestingValueType {
    PERCENTAGE
    UNIT
    CASH
}

enum VestingFrequency {
    YEARLY
    SEMI_ANNUALLY
    MONTHLY
    DAILY
    QUARTERLY
}

enum GrantValueType {
    CASH
    UNIT
}

enum GrantType {
    ESOP
    RSU
    STOCK
}


