extend type Mutation {
    contractUpdateCompliance(contractId: ID!, input: ContractUpdateComplianceInput!): Contract @authorize(company: ["update.company.contract.compliance"], operations: ["update.operations.contract.compliance"])
    contractAgreementCustomUpload(contractId: ID!, file: Upload): Contract @authorize(company: ["upload.company.contract"]) #Upload custom agreements for cases that are supported.
    contractAgreementReplace(contractId: ID!, contract: Upload!): Contract @authorize(company: ["upload.company.contract"], operations: ["upload.operations.contract"]) # Replace existing agreement documents.
    contractAgreementReplaceWetInk(contractId: ID!, contract: Upload!): Contract @authorize(operations: ["upload.operations.contract"]) # Replace existing agreement documents with wet ink signature contract.
    regenerateAgreement(input: RegenerateAgreementInput): TaskResponse @authorize(operations: ["regenerate.operations.contract"]) @deprecated(reason: "Use contractAgreementRegenerate instead")
    contractAgreementRegenerate(input: RegenerateAgreementInput): TaskResponse @authorize(operations: ["regenerate.operations.contract"]) # Force regenerate for pandadoc.
    orderFormRegenerate(input: RegenerateOrderFormInput): TaskResponse @authorize(operations: ["regenerate.operations.contract"]) # Force regenerate for pandadoc.

    contractSendOfferLetter(id: ID!): TaskResponse @authorize(company: ["update.company.contract.compliance"], operations: ["update.operations.contract.compliance"]) # Sends offer letter as email to member

    contractLinkTemplate(contractId: ID!, templateReference: String!): TaskResponse @authorize(operations: ["update.operations.contract.compliance"]) # Link custom PandaDoc template to contract
}

input ContractUpdateComplianceInput {
    # Should be made generic to allow other kinds of inputs
    # Can possibly solve via composition of optional inputs
    complianceParams : [ComplianceParamPeriodLimitInput]
    contractAgreementType: ContractAgreementType
    preferredContractAgreementTemplate: ID @deprecated(reason: "To be removed. Use preferredV2ContractAgreementTemplate instead.")
    preferredContractAgreementTemplateV2: UUID
}

input ComplianceParamPeriodLimitInput {
    key: String
    value: Int
    unit: ComplianceParamPeriodUnit
}

input RegenerateAgreementInput {
    contractId: ID!
    preferredContractAgreementTemplate: ID @deprecated(reason: "To be removed. Use preferredV2ContractAgreementTemplate instead.")
    preferredContractAgreementTemplateV2: UUID
    createNewDocument : Boolean = false
    ignoreStatusCheck: Boolean  = false
}

input RegenerateOrderFormInput {
    contractId: ID!
}

# Domain
interface Compliance @key(fields: "contract { id }") {
    type: LegalBindingType
    contract: Contract
    legalEntity: LegalEntity
    contractAgreement: DocumentReadable  @authorize(company: ["view.company.compliance.contract-agreement-document"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    complianceParams(key: String): [ComplianceParam]
    contractAgreementType: ContractAgreementType
    signature: Signature
    contractAgreementTemplate: DocumentReadable
}

type ComplianceMultiplierEOR implements Compliance @key(fields: "contract { id }") {
    type: LegalBindingType #MULTIPLIER
    contract: Contract
    legalEntity: LegalEntity
    complianceParams(key: String): [ComplianceParam]
    contractAgreement: DocumentReadable  @authorize(company: ["view.company.compliance.contract-agreement-document"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    contractAgreementType: ContractAgreementType
    offerLetter: DocumentReadable
    signature: Signature
    certificateOfEmployment(date: Date!): DocumentReadable
    contractAgreementTemplate: DocumentReadable
    preferredContractAgreementTemplate: PreferredContractAgreementTemplateResponse
    preferredContractAgreementTemplateV2: PreferredContractTemplateResponse
}

type PreferredContractAgreementTemplateResponse @key(fields: "documentId"){
    documentId:ID
}

## V2, only to be used in those cases where we have UUIDs
type PreferredContractTemplateResponse @key(fields: "documentIdV2"){
    documentIdV2:UUID
}


type CompliancePartnerEOR implements Compliance @key(fields: "contract { id }") {
    type: LegalBindingType #PARTNER
    contract: Contract
    legalEntity: LegalEntity
    partner: EORPartnerCountry
    complianceParams(key: String): [ComplianceParam]
    contractAgreementType: ContractAgreementType
    contractAgreement: DocumentReadable  @authorize(company: ["view.company.compliance.contract-agreement-document"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    offerLetter: DocumentReadable
    signature: Signature
    contractAgreementTemplate: DocumentReadable
}

type CompliancePEO implements Compliance @key(fields: "contract { id }") {
    type: LegalBindingType #CLIENT
    contract: Contract
    legalEntity: LegalEntity
    complianceParams(key: String): [ComplianceParam]
    contractAgreement: DocumentReadable  @authorize(company: ["view.company.compliance.contract-agreement-document"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    contractAgreementType: ContractAgreementType
    signature: Signature
    contractAgreementTemplate: DocumentReadable
}

type ComplianceFreelance implements Compliance @key(fields: "contract { id }") {
    type: LegalBindingType #CLIENT
    contract: Contract
    legalEntity: LegalEntity
    complianceParams(key: String): [ComplianceParam]     # compliance params are common to all countries
    contractAgreement: DocumentReadable  @authorize(company: ["view.company.compliance.contract-agreement-document"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    contractAgreementType: ContractAgreementType
    signature: Signature
    freelancerType: FreelancerType
    contractAgreementTemplate: DocumentReadable
}

type ComplianceContractor implements Compliance @key(fields: "contract { id }") {
    type: LegalBindingType #CLIENT
    contract: Contract
    legalEntity: LegalEntity
    complianceParams(key: String): [ComplianceParam]     # compliance params are common to all countries
    contractAgreement: DocumentReadable  @authorize(company: ["view.company.compliance.contract-agreement-document"], operations: ["view.operations.contract"], member: ["view.member.contract"])
    contractAgreementType: ContractAgreementType
    contractAgreementTemplate: DocumentReadable
    orderForm: DocumentReadable
    orderFormTemplate: DocumentReadable
    signature: Signature
}

enum FreelancerType {
    INDIVIDUAL
    BUSINESS
}

enum ContractAgreementType {
    MULTIPLIER_TEMPLATE
    UPLOADED_FINAL
    CUSTOM_TEMPLATE
}

type Signature {
    memberSignedDate: DateTime        # to capture "employeeSignedDate" data in old alpha contract, TBD: corrct place to capture this?
    employerSignedDate: DateTime      # to capture "employerSignedDate" data in old alpha contract, TBD: corrct place to capture this?
    employerSignatory: String
}
