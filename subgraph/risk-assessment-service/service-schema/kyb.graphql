extend type Query {
    kybList(ids: [ID]!): [KYB] @authorize(operations: ["view.operations.kyb"]) # will be removed when KYB is included into LegalEntity
    kyb(id: ID!): KY<PERSON> @authorize(operations: ["view.operations.kyb"])
}

extend type Mutation {
    startNewKYBAssessment(kybId: ID!, reportExists: Boolean!): KYBAssessment @authorize(operations: ["update.operations.kyb"])
    uploadAssessmentDocument(document: Upload!): ID @authorize(operations: ["update.operations.kyb"])
    deleteAssessmentDocument(id: ID!): TaskResponse @authorize(operations: ["update.operations.kyb"])
    sendKYBRequirementsEmail(input: KYBRequirementsInput!): TaskResponse @authorize(operations: ["update.operations.kyb"])
}

type KYB {
    id: ID!
    entity: KYBEntity
    kybAssessments: [KYBAssessment]
}

type KYBEntity {
    company: Company
    legalEntity: LegalEntity
}

type KYBAssessment {
    id: ID!
    status: KYBAssessmentStatus
    riskAssessment: [KYBRiskAssessment]
    reportExists: Boolean
    completedOn: DateTime
    validUntil: DateTime
    expiredOn: DateTime
}

interface KYBRiskAssessment {
    id: ID!
    type: AssessmentType!
    status: RiskAssessmentStatus
    schema: KYBSchema
    riskScore: AssessmentRiskScore
    collectedRiskData: KYBCollectedRiskData
    completedOn: DateTime
    validUntil: DateTime
    expiredOn: DateTime
}

type KYBCollectedRiskData {
    fields: [KYBCollectedData]
    report: KYBPartnerReport
}

type KYBCollectedData {
    kybRequirementType: String!
    data: [KYBRiskData]
    documents: [KYBRiskDocument]
}

type KYBRiskData {
    key: String
    value: String
    metaData: [KYBMetaData] # e.g. [{comment: "text value"}]
}

type KYBPartnerReport {
    reports: [DocumentReadable]
}

type KYBRiskDocument {
    key: String
    documents: [DocumentReadable]
    metaData: [KYBMetaData] # e.g. [{emailSent: "true"}]
}

type KYBSchema {
    requirements: [KYBRequirement]
}

type KYBRequirement {
    kybRequirementType: String
    fields: [KYBRequirementField]
}

type KYBRequirementField {
    type: String
    key: String
    label: String
    allowedValues: [KYBAllowedValue]
    additionalRequirements: [KYBAdditionalRequirement]
}

type KYBAllowedValue{
    key: String
    value: String
}

type KYBMetaData {
    key: String
    value: String
}

enum AssessmentRiskScore {
    LOW
    MEDIUM
    HIGH
}

enum KYBAdditionalRequirement {
    COMMENT
    EMAIL
}

enum RiskAssessmentStatus {
    INITIATED
    COMPLETED
    OUTDATED
}

enum KYBAssessmentStatus {
    PENDING
    PROCESSING
    COMPLETED
    EXPIRED
    NOT_APPLICABLE
}

enum AssessmentType {
    COMPLIANCE
    FINANCIAL
}

input KYBEmailRequirementInput {
    kybRequirementType: String!
    requiredDocuments: [String]  # note: include a list of keys
}

input KYBRequirementsInput {
    kybAssessmentId: ID!
    requiredComplianceDocuments: [KYBEmailRequirementInput]
    requiredFinancialDocuments: [KYBEmailRequirementInput]
}

input KYBMetaDataInput {
    key: String
    value: String
}
