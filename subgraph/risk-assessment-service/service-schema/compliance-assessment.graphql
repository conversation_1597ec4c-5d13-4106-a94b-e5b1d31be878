extend type Mutation {
    updateComplianceAssessmentData(input: ComplianceAssessmentDataInput!): ComplianceRiskAssessment @authorize(operations: ["update.operations.kyb"])
    updateComplianceAssessmentDocuments(input: ComplianceAssessmentDocumentsInput!): ComplianceRiskAssessment @authorize(operations: ["update.operations.kyb"])
    completeComplianceAssessment(id: ID!, riskScore: AssessmentRiskScore!): ComplianceRiskAssessment @authorize(operations: ["update.operations.kyb"])
}

type ComplianceRiskAssessment implements KYBRiskAssessment {
    id: ID!
    type: AssessmentType!
    status: RiskAssessmentStatus
    schema: KYBSchema
    riskScore: AssessmentRiskScore
    collectedRiskData: KYBCollectedRiskData
    completedOn: DateTime
    validUntil: DateTime
    expiredOn: DateTime
}

input ComplianceAssessmentDataInput {
    id: ID!
    data: [ComplianceDataRequirementInput]
}

input ComplianceAssessmentDocumentsInput {
    id: ID!
    documents: [ComplianceDocumentRequirementInput]
    reports: [ID]
}

input ComplianceDataRequirementInput{
    kybRequirementType: String!
    data: [ComplianceDataInput]!
}

input ComplianceDataInput {
    key: String
    value: String
    metaData: [KYBMetaDataInput] # following same structure as type. this should contain {comment : "text"}
}

input ComplianceDocumentRequirementInput{
    kybRequirementType: String!
    data: [ComplianceDocumentInput]!
}

input ComplianceDocumentInput {
    key: String
    documentList: [ID]
    metaData: [KYBMetaDataInput]
}
