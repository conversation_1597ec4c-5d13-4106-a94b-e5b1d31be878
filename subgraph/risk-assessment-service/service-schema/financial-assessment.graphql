extend type Mutation {
    updateFinancialAssessmentData(input: FinancialAssessmentDataInput!): FinancialRiskAssessment @authorize(operations: ["update.operations.kyb"])
    updateFinancialAssessmentDocuments(input: FinancialAssessmentDocumentsInput!): FinancialRiskAssessment @authorize(operations: ["update.operations.kyb"])
    completeFinancialAssessment(id: ID!, riskScore: AssessmentRiskScore!): FinancialRiskAssessment @authorize(operations: ["update.operations.kyb"])
}

type FinancialRiskAssessment implements KYBRiskAssessment {
    id: ID!
    type: AssessmentType!
    status: RiskAssessmentStatus
    schema: KYBSchema
    riskScore: AssessmentRiskScore
    collectedRiskData: KYBCollectedRiskData
    completedOn: DateTime
    validUntil: DateTime
    expiredOn: DateTime
}

input FinancialAssessmentDataInput {
    id: ID!
    data: [FinancialDataRequirementInput]
}

input FinancialAssessmentDocumentsInput {
    id: ID!
    documents: [FinancialDocumentRequirementInput]
    reports: [ID]
}

input FinancialDataRequirementInput {
    kybRequirementType: String!
    data: [FinancialDataInput]!
}

input FinancialDataInput {
    key: String
    value: String
    metaData: [KYBMetaDataInput] # following same structure as type. this should contain comment : {comment : "text"}
}


input FinancialDocumentRequirementInput {
    kybRequirementType: String!
    data: [FinancialDocumentInput]!
}

input FinancialDocumentInput {
    key: String
    documentList: [ID]
    metaData: [KYBMetaDataInput]
}
