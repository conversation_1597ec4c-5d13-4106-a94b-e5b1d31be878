const fs = require('fs');
const { mergeTypeDefs } = require('@graphql-tools/merge')
const { print } = require('graphql');
const { loadFilesSync } = require('@graphql-tools/load-files');

console.log("Merging service with sub-graphs")

const serviceSchemaFiles = loadFilesSync('service-schema/**/*.graphql');
const commonSchemaFiles = loadFilesSync('../common/service-schema/**/*.graphql');

const projects = process.argv.slice(2);
const files = projects.map(project => loadFilesSync(`../${project}/service-schema/**/*.graphql`));

const subgraph = mergeTypeDefs([...serviceSchemaFiles, ...files, ...commonSchemaFiles]);

const resourcesPath = `build/generated/resources/merged/schema`;
if (fs.existsSync(resourcesPath)) {
    fs.rmSync(resourcesPath, { recursive: true });
}

fs.mkdirSync(resourcesPath, { recursive: true });
fs.writeFileSync(`${resourcesPath}/subgraph.graphql`, print(subgraph));

console.log("Merged service with sub-graphs")
