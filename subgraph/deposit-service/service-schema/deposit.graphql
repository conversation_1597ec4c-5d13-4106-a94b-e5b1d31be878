interface Deposit {
    id: ID!
    amount: Amount
    type: DepositType
    status: DepositStatus
    companyPayable: CompanyPayable #to make it mandatory or not, we are mandating it for now.
    company: Company
    policies: [DepositPolicy]
    createdDate: Date
    dueDate: Date
}

enum DepositType {
    EMPLOYMENT
#    VISA
#    CRYPTO_PAYOUT_DEPOSIT
}

enum DepositStatus {
    CREATED
    PAYMENT_IN_PROGRESS
    PAYMENT_FAILED
    PAID
    REFUND_IN_PROGRESS
    REFUND_FAILED
    REFUNDED
    REVOKED
}
