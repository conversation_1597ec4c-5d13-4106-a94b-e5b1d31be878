interface DepositPolicy {
    id: ID!
    type: DepositPolicyType
    amount: Amount
    deposit: Deposit
}

type SalaryPeriodPolicy implements DepositPolicy {
    id: ID!
    type: DepositPolicyType
    deposit: Deposit
    amount: Amount
    periodInDays: Int #notice period in days because we want to compute everything in most atomic days
    payInDays: Float
}

type AdditionalPayFixedPolicy implements DepositPolicy {
    id: ID!
    type: DepositPolicyType
    deposit: Deposit
    amount: Amount
    periodInDays: Int
    payInDays: Float
}

type LeaveEntitlementSurplusPolicy implements DepositPolicy {
    id: ID!
    type: DepositPolicyType
    deposit: Deposit
    amount: Amount
    leaveSurplusInDays: Int
    payInDays: Float
}

enum DepositPolicyType {
    SALARY_PERIOD,
    COUNTRY_LEAVE_ENTITLEMENT_SURPLUS,
    ADDITIONAL_PAY_FIXED
    ADDITIONAL_PAY_VARIABLE
}

# for future reference
#type RiskAssessmentPolicy implements DepositPolicy {
#    type: DepositPolicyType!
#    amount: Amount!
#    multiplicationFactor: Float!
#    policies: [DepositPolicy]
#    deposit: Deposit!
#}
