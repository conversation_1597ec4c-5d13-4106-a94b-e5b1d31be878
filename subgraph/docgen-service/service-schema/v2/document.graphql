extend type Mutation {
    documentCreate(input: CreateDocumentInput!): DocumentResponse @authorize(operations: ["regenerate.operations.contract"])
    documentRegenerate(documentId: UUID!): DocumentResponse @authorize(operations: ["regenerate.operations.contract"])
    documentUpdateStatus(input: UpdateDocumentStatusInput!): DocumentResponse @authorize(operations: ["regenerate.operations.contract"])
}

extend type Query {
    documentGetById(id: UUID!): DocumentResponse @authorize(operations: ["view.operations.contract"])
    documentDownload(documentId: UUID!): String @authorize(operations: ["view.operations.contract"])
    documentsFilter(filter: DocumentFilter!): [DocumentResponse] @authorize(operations: ["view.operations.contract"])
    documentGenerateSignUrl(input: GenerateSignUrlInput!): GenerateSignUrlResponse @authorize(operations: ["view.operations.contract"])
    documentGenerateViewUrl(input: GenerateViewUrlInput!): GenerateViewUrlResponse @authorize(operations: ["view.operations.contract"])
}

input DocumentFilter {
    name: String
    templateId: UUID,
    folderId: String @deprecated(reason: "Use folderIds instead")
    folderIds: [String]
    templateType: TemplateType
    createdBy: ID
}

input CreateDocumentInput {
    name: String
    templateId: UUID!
    params: [ParameterInput!]
    recipients: [DocumentRecipientInput!]
    folderId: String
    isTest: Boolean
}

input ParameterInput {
    key: String!
    value: String!
}

input DocumentRecipientInput {
    name: String!
    email: String!
    role: String!
}

input GenerateSignUrlInput {
    documentId: UUID!
    email: String!
}

input GenerateViewUrlInput {
    documentId: UUID!
}

input UpdateDocumentStatusInput {
    documentId: UUID!
    status: Status!
}

type GenerateSignUrlResponse {
    signUrl: String!
}

type GenerateViewUrlResponse {
    viewUrl: String!
}

type DocumentResponse {
    id: UUID!
    name: String
    status: Status
    templateId: UUID
    envelopeId: UUID
    folderId: String
    recipients: [DocumentRecipient!]
    isTest: Boolean
}

type DocumentRecipient {
    name: String!
    email: String!
    role: String!
    hasCompleted: Boolean
    status: RecipientStatus
    signingOrder: Int
}

enum Status {
    CREATED
    UPLOADED
    GENERATION_IN_PROGRESS
    GENERATION_SUCCESS
    GENERATION_FAILED
    SENT_FOR_SIGNING
    VIEWED
    SIGNING
    DROPPED
    SIGNED
}

enum RecipientStatus {
    NOT_SENT
    SENT
    VIEWED
    COMPLETED
}
