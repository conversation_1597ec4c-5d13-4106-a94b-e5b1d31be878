extend type Query {
    templatesV2(filter: TemplateFilterV2!, pageRequest: PageRequest): TemplatesV2<PERSON><PERSON>ult @authorize(operations: ["view.operations.docgen.template"])
    templateV2(id: String!): TemplateV2 @authorize(operations: ["view.operations.docgen.template"])
}

type TemplatesV2Result {
    data: [TemplateV2]
    pageResult: PageResult
}

extend type Mutation {
    createTemplate(input: CreateTemplateInput!): TemplateV2 @authorize(operations: ["upsert.operations.docgen.template"])
    @deprecated updateTemplate(input: UpdateTemplateInput!): TemplateV2 @authorize(operations: ["upsert.operations.docgen.template"])
    templateUpdate(input: TemplateUpdateInput!): TemplateV2 @authorize(operations: ["upsert.operations.docgen.template"])
    linkCustomTemplate(input: LinkCustomTemplateInput!): TemplateV2 @authorize(operations: ["upsert.operations.docgen.template"])

}

input TemplateUpdateInput {
    id: UUID!
    fileUploadRequest: FileUploadInput!
}

input LinkCustomTemplateInput {
    templateId: UUID!
    fileUploadRequest: FileUploadInput!
    contractId: String!
}

type TemplateV2 {
    id: String!
    name: String!
    status: TemplateStatus!
    type: TemplateType!
    config: TemplateConfig
    previewUrl: String
    params: [String]
    roles: [Role]
    isLatest: Boolean!
}

enum TemplateStatus {
    INACTIVE
    ACTIVE
}

enum InputType {
    TEXT
    SIGN_HERE
    DATE
    DATE_SIGNED
}

enum TemplateType {
    TEMPLATE_TYPE_UNSPECIFIED
    CONTRACT_EOR
    CONTRACT_EOR_FIXED
    CONTRACT_FREELANCE
    COMPANY_MSA
    PAYROLL_REPORT
    MEMBER_LEGAL_DOCUMENT
    CONTRACT_HR_MEMBER_CUSTOM
    CONTRACT_FREELANCE_CUSTOM
    EXPENSE_RECEIPT
    PAYROLL_SALARY_CALCULATION
    PAYROLL_PAYSLIP
    CONTRACT_OFFER_LETTER
    UNKNOWN
    PERFORMANCE_REVIEW
    SALARY_REVIEW
    CONTRACT_ESOP
    CONTRACT_FREELANCE_RECURRING
    CONTRACT_ADDENDUM
    COMPANY_MSA_ADDENDUM
    CONTRACT_CONTRACTOR_ORDER_FORM
    CONTRACT_CONTRACTOR
    PAYROLL_INPUT
    COMPANY_PAY_IN_DOCUMENT_WITH_MSA
    COMPANY_PAY_IN_DOCUMENT_WITHOUT_MSA
    CONTRACT_QUESTIONNAIRE
    RESIGNATION_LETTER
}

type InputField {
    inputType: InputType!
    anchor: String!
}

type Role {
    name: String!
    signingOrder: Int!
    tabs: [InputField!]!
}

type TemplateConfig {
    country: String
    countryStateCode: String
    lang: String
    entityId: ID
    contractId: ID
}

input FileUploadInput {
    file: Upload!
}

input CreateTemplateInput {
    name: String!
    fileUploadRequest: FileUploadInput!
    roles: [RoleInput!]!
    type: TemplateType!
    config: TemplateConfigInput
}

input UpdateTemplateInput {
    id: String!
    status: TemplateStatus
    name: String
    fileUploadRequest: FileUploadInput
    roles: [RoleInput!]
    type: TemplateType
    config: TemplateConfigInput
}

input TemplateConfigInput {
    country: String
    countryStateCode: String
    lang: String
    entityId: ID
    contractId: ID
}

input RoleInput {
    name: String!
    signingOrder: Int!
    tabs: [InputFieldInput!]!
}

input InputFieldInput {
    inputType: InputType!
    anchor: String!
}

input TemplateFilterV2 {
    latestVersion: Boolean = true
    nameMatching: String
    statuses: [TemplateStatus!]
    types: [TemplateType!]
    config: TemplateConfigInput
}
