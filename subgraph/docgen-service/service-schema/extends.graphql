type Company @key(fields: "id") @extends {
    id: ID @external
    templates(filter: TemplateFilter!): [Template] @authorize(operations: ["view.operations.docgen.template"],company: ["view.company.contract.compliance"])
    templatesV2(filter: TemplateFilterV2!): [TemplateV2] @authorize(operations: ["view.operations.docgen.template"],company: ["view.company.contract.compliance"])
}

input CompanyTemplateFilterV2 {
    types: [TemplateType!]
    country: String
    stateCode: String
    language: String
}


    type PreferredContractAgreementTemplateResponse @key(fields: "documentId") @extends {
    documentId:ID @external
    template: Template @authorize(operations: ["view.operations.docgen.template"],company: ["view.company.contract.compliance"])
}

type PreferredContractTemplateResponse @key(fields: "documentIdV2") @extends {
    documentIdV2: UUID @external
    templateV2: TemplateV2 @authorize(operations: ["view.operations.docgen.template"],company: ["view.company.contract.compliance"])
}
