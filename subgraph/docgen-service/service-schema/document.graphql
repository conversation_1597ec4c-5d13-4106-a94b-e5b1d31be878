type Document @key(fields: "id") {
    id: ID
    name: String
    downloadUrl: String
    viewUrl: String
    uploadUrl: String
    extension: String
    contentType: String
    versionId: ID # to keep it compatible with the old version
    versionedOn: DateTime # to keep it compatible with the old version
    versionFiles: [Document!] # to keep it compatible with the old version
    size: Int # to keep it compatible with the old version
}
