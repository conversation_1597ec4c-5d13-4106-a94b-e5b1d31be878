extend type Mutation {
    # WARNING: ALWAYS ADD PROPER AUTHORIZATION WHEN ADDING NEW MUTATION
    templateUpsert(input: TemplateInput!): Template @authorize(operations: ["upsert.operations.docgen.template"])
    templateDelete(id: ID!): Template @authorize(operations: ["delete.operations.docgen.template"])
}

extend type Query {
    templates(filter: TemplateFilter!): [Template] @authorize(operations: ["view.operations.docgen.template"]) #latest versions
}

input TemplateFilter {
    entityId: ID # companyId
    contractId: ID
    types: [String!]
    strategy: Strategy = PANDA_DOC
    country: CountryCode
    stateCode: String
    language: Language = EN
}

enum Language {
    EN
}

enum Strategy {
    STATIC,
    PDF_MONKEY,
    PANDA_DOC
}

enum Workspace {
    DEFAULT,
    PANDA_DOC,
    PANDA_DOC_MSA
}

input TemplateInput {
    id: ID
    country: CountryCode
    countryState: String
    entityId: ID
    externalId: String
    language: Language #defaults to EN
    strategy: Strategy!
    version: Int
    type: String!
    workspace: Workspace
    contractId: ID
    displayName: String # when not provided for entityId it will be auto-generated
    metadata: TemplateMetadataInput
}

input TemplateMetadataInput {
    languagesCount: Int
}

type Template @key(fields: "id") {
    id: ID!
    country: CountryCode
    countryState: String
    entityId: ID
    externalId: String
    language: Language!
    strategy: Strategy!
    type: String!
    version: Int!
    workspace: Workspace
    contractId: ID
    link: String
    displayName: String
    metadata: TemplateMetadata
}

type TemplateMetadata {
    languagesCount: Int
}
