extend type Mutation {
    createSftpAccessRequest(input: SftpAccessRequestInput!): SftpAccessRequest @authorize(operations: ["create.operations.integration.sftp-access"], company: ["create.company.integration.sftp-access"])
}

input SftpAccessRequestInput {
    companyId: ID!
    entityId: ID!
    bulkModule: BulkUploadModule!
}

type SftpAccessRequest {
    id: ID!
    companyId: ID!
    entityId: ID!
    bulkModule: BulkUploadModule!
    status: SftpAccessRequestStatus!
    mainSftpDirectory: String
}

enum SftpAccessRequestStatus {
    PENDING
    APPROVED
    REJECTED
    REVOKED
}
