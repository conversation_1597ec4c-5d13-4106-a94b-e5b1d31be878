extend type Mutation {
    accountingSyncCompanyPayables(
        accountingSyncCompanyPayablesInput: AccountingSyncCompanyPayablesInput!
    ): [AccountingSyncCompanyPayablesResponse]! @authorize(operations: ["sync.operations.integration.accounting"])

    accountingCreatePaymentForCompanyPayable(
        accountingCreatePaymentForCompanyPayableInput: AccountingCreatePaymentForCompanyPayableInput!
    ): AccountingCreatePaymentForCompanyPayableResponse! @authorize(operations: ["sync.operations.integration.accounting"])
}

input AccountingSyncCompanyPayablesInput {
    companyPayableIds: [ID!]!
}

input AccountingCreatePaymentForCompanyPayableInput {
    companyPayableId: ID!
}

type AccountingSyncCompanyPayablesResponse {
    companyPayableId: ID!,
    taskResponse: TaskResponse!
}

type AccountingCreatePaymentForCompanyPayableResponse {
    companyPayableId: ID!,
    taskResponse: TaskResponse!
}
