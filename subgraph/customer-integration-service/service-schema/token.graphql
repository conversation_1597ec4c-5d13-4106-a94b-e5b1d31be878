extend type Query {
    platformMergeDevLinkToken(companyId: ID!, platformId: ID!): String  @deprecated(reason: "Use `platformMergeDevLinkTokenV2` instead") @authorize(company: ["generate.company.platform.link-token"], operations: ["generate.operations.platform.link-token"])  # getting merge dev link token for a company

    platformMergeDevLinkTokenV2(input: MergeDevLinkTokenInput!): MergeDevLinkTokenResponse! @authorize(company: ["generate.company.platform.link-token"], operations: ["generate.operations.platform.link-token"]) # getting merge dev link token for a company
}

extend type Mutation {
    # Knit integration token (Workday)
    platformCompanyTokenUpdate(companyId: ID!, platformId: ID!, token: String!): TaskResponse @authorize(company: ["view.company.company.has-integration", "update.company.platform.account-token"], operations: ["update.operations.platform.account-token"])

    # Knit Auth Token Creation
    providerKnitAuthToken(originOrgId: String!, originOrgName: String!, originUserEmail: String!,
        originUserName: String!, platformId: String!, clearErrors: Boolean): KnitAuthResponse @authorize(company: ["generate.company.platform.link-token"], operations: ["generate.operations.platform.link-token"])

    getTrinetCredential(input: TrinetCredentialInput!): TrinetCredentialResult @deprecated(reason: "Use `verifyTriNetCredential` instead") @authorize(company: ["generate.company.platform.link-token"], operations: ["generate.operations.platform.link-token"])

    verifyTriNetCredential(input: TrinetCredentialInputV2!): TrinetCredentialResult @authorize(company: ["generate.company.platform.link-token"], operations: ["generate.operations.platform.link-token"])

    # MergeDev Account Token Creation
    platformMergeDevPublicToken(input: MergeDevPublicTokenInput!): MergeDevPublicTokenResponse! @authorize(company: ["view.company.company.has-integration", "update.company.platform.account-token"], operations: ["update.operations.platform.account-token"])
}

type KnitAuthResponse {
    success: Boolean!
    msg: KnitAuthToken
    error: KnitAuthTokenError
}

type KnitAuthToken {
    token: String
}

type KnitAuthTokenError {
    msg: String
}

input TrinetCredentialInput {
    companyUserName: String!
    companyUserPassword: String!
    companyId: ID!
    externalCompanyId: String
}

type TrinetCredentialResult {
    success: Boolean
    message: String!
}

input TrinetCredentialInputV2 {
    companyUserName: String!
    companyUserPassword: String!
    externalCompanyId: String!
}

input MergeDevLinkTokenInput {
    companyId: ID!
    companyName: String!
    companyUserEmail: String!
    platformId: ID!
}

input MergeDevPublicTokenInput{
    companyId: ID!
    platformId: ID!
    publicToken: String!
}

union MergeDevLinkTokenResponse = MergeDevLinkTokenSuccessResponse | MergeDevLinkTokenErrorResponse
union MergeDevPublicTokenResponse = MergeDevPublicTokenSuccessResponse | MergeDevPublicTokenErrorResponse

type MergeDevLinkTokenSuccessResponse {
    token: MergeDevLinkToken
}

type MergeDevLinkTokenErrorResponse {
    error: MergeDevError
}

type MergeDevPublicTokenSuccessResponse {
    msg: String
}

type MergeDevPublicTokenErrorResponse {
    error: MergeDevError
}

type MergeDevLinkToken {
    token: String
}

type MergeDevError {
    msg: String
}
