const fs = require('fs');
const { loadFilesSync } = require('@graphql-tools/load-files');
const { mergeTypeDefs } = require('@graphql-tools/merge');
const { print } = require('graphql');

console.log("Merging subgraph")

const commonSchemaFiles = loadFilesSync('../common/service-schema/**/*.graphql');

const files = loadFilesSync(`service-schema/**/*.graphql`);
const subgraph = mergeTypeDefs([...files, ...commonSchemaFiles]);

const schemaPath = `build/generated/resources/schema`;

if (fs.existsSync(schemaPath)) {
    fs.rmSync(schemaPath, { recursive: true });
}

fs.mkdirSync(schemaPath, { recursive: true });
fs.writeFileSync(`${schemaPath}/subgraph.graphql`, print(subgraph));

console.log("Merged subgraph")
