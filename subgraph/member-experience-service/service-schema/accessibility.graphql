extend type Mutation {
    dummyAccessibilityMutation: <PERSON><PERSON><PERSON> @authorize(company: ["view.company.accessibility-rules"], operations: ["view.operations.accessibility-rules"], member: ["view.member.accessibility-rules"])
}
extend type Query {
    getComponentAccessibilityRules(page: MemberExperiencePage, contractID: ID): ComponentAccessibilityResponse @authorize(company: ["view.company.accessibility-rules"], operations: ["view.operations.accessibility-rules"], member: ["view.member.accessibility-rules"])
    getSectionAccessibilityRules(sections: [Section!], contractID: ID): [PageSection] @authorize(company: ["view.company.accessibility-rules"], operations: ["view.operations.accessibility-rules"], member: ["view.member.accessibility-rules"])
}

type ComponentAccessibilityResponse {
    page: MemberExperiencePage!
    sections:[PageSection]
}

type PageSection{
    name: Section
    visible: Boolean
    editable: Boolean
    reason: String
    rules: [FieldAccessibilityRule!]
}
