extend type Mutation {
    upsertCountryDocument(input: CountryDocumentUpdateInput file: Upload!): File<PERSON><PERSON> @authorize(operations: ["update.operations.country.document"])
    upsertCountryContractClauses(inputs: [CountryContractClauseUpsertInput!]!): TaskResponse @authorize(operations: ["update.operations.country.contract-clause"])
    upsertCountryLegalDataDefinitions(inputs: [CountryLegalDataDefinitionUpsertInput!]!): TaskResponse @authorize(operations: ["update.operations.country.legal-data-definition"])
    upsertCountryExclusions(inputs: [CountryExclusionsUpsertInput!]!): TaskResponse @authorize(operations: ["update.operations.country.country-exclusion"])
    addDescriptionOfWorkByCountry(input: [DescriptionOfWorkInput!]!): [DescriptionOfWork!]! @authorize(operations: ["update.operations.description-of-work"])
    createDescriptionOfWork(input: CreateDescriptionOfWorkInput!): [DescriptionOfWork!]! @authorize(operations: ["update.operations.description-of-work"], company: ["update.company.contract.description-of-work"])
    submitDescriptionOfWork(input: JobDescriptionInput!): [JobDescriptionDetails!]! @authorize(operations: ["update.operations.description-of-work"], company: ["update.company.contract.description-of-work"])
    updateDescriptionOfWorkStatus(id: ID!, status: DescriptionOfWorkStatus!): JobDescriptionDetails! @authorize(operations: ["update.operations.description-of-work"])
    deleteDescriptionOfWork(id: ID!): TaskResponse! @authorize(operations: ["update.operations.description-of-work"])
    addGlobalDescriptionOfWork(input: [GlobalDescriptionOfWorkInput!]!): [GlobalDescriptionOfWork!]! @authorize(operations: ["update.operations.description-of-work"])
    updateDescriptionOfWork(input: UpdateDescriptionOfWorkInput!): JobDescriptionDetails! @authorize(operations: ["update.operations.description-of-work"], company: ["update.company.contract.description-of-work"])
    updateJobDescriptionStatus(input: StatusUpdateInput!): JobDescriptionDetails! @authorize(operations: ["update.operations.description-of-work"])
    changeDescriptionOfWorkStatus(input: ChangeDescriptionOfWorkStatusInput!): JobDescriptionDetails! @authorize(operations: ["update.operations.description-of-work"])
}


extend type Query {
    country(country: CountryCode!): Country @authorize(operations: ["view.operations.country.configurations"], company: ["view.company.country.configurations"], member: ["view.member.country.configurations"])
    # input priority: countries > country > mainCurrency
    countries(country: CountryCode, countries: [CountryCode], mainCurrency: CurrencyCode): [Country] @authorize(operations: ["view.operations.country.configurations"], company: ["view.company.country.configurations"], member: ["view.member.country.configurations"])
    countryCallingCodes(filters: CountryCallingCodeFilters): [CountryCallingCodeDefinition] @authorize(operations: ["view.operations.country.configurations"], company: ["view.company.country.configurations"], member: ["view.member.country.configurations"])
    supportedCountries(contractType: ContractType!): [Country] @authorize(operations: ["view.operations.country.configurations"], company: ["view.company.country.configurations"], member: ["view.member.country.configurations"])
    countryExclusions(companyId: ID, countryExclusionServiceTypes: [CountryExclusionServiceType]): [CountryExclusionConfig] @authorize(operations: ["view.operations.country.configurations"], company: ["view.company.country.configurations"], member: ["view.member.country.configurations"])
    getDescriptionOfWorkByCountry(countryCode: String!): [DescriptionOfWork] @authorize(company: ["view.company.contract.description-of-work"])
    getDescriptionOfWorkByCompanyId(companyId: ID!, status: DescriptionOfWorkStatus): [JobDescriptionDetails!]! @authorize(company: ["view.company.contract.description-of-work"])
    getAllDescriptionOfWork: [JobDescriptionSummary!]! @authorize(operations: ["view.operations.description-of-work"])
    getAllGlobalDescriptionOfWork: [GlobalDescriptionOfWork!]! @authorize(company: ["view.company.contract.description-of-work"])

}

type Country @key(fields: "code") {
    name: String
    code: CountryCode
    region: String
    currency: Currency
    compliance(contractType: ContractType, countryStateCode: String): CountryCompliance  # TODO: Move everything that is deprecated to here.
    mainCurrency: CurrencyCode
    supportedCurrencies(contractType: ContractType): [CurrencyCode]    # Currencies we support for this country.
    compliantCurrencies: [CurrencyCode] @deprecated    # Currencies this country allows to legally pay through for compliant employments.
    complianceRequirements(contractType: ContractType, term: ContractTerm): [ComplianceParamDefinition] @deprecated
    compensationStructureDefinition(type: ContractType, term: ContractTerm): CompensationStructureDefinition @deprecated
    bankAccountFields: BankAccountFields    # This is the indicate the different labels the fields can have in different countries
    memberLegalDataRequirements(workStatus: CountryWorkStatus, contractType: ContractType): [LegalDataRequirement] @deprecated
    memberDocumentRequirements(workStatus: CountryWorkStatus, contractType: ContractType): [LegalDocumentRequirement] @deprecated
    holidays(year: Int!, month: Int, date: Int) : [Holiday]
    countryStates: [State]
    isMultiplierVisaAssistance: Boolean
    insights(filter: InsightDefinitionFilter): [InsightDefinition]
    labourStandards(filter: CountryLabourStandardsFilter): CountryLabourStandards
    documents(filter: CountryDocumentFilter): [CountryDocument] # Any documents that can be associated on a country level, i.e. onboarding guides, compensation pamphlets, market summary
    payrollInputNotes: [PayrollInputNote!]
    configs(stateCode: String, key: String): [CountryConfig!] # return country config as default if state config does not exist
    legalEntityPayrollDataRequirements(country: CountryCode): LegalEntityPayrollDataRequirements
    fiscalYears(years: [Int!]): [FiscalYear] ## if years are not provided, return current fiscal year based on today
}



type FiscalYear {
    fiscalYear: Int
    startDate: Date
    endDate: Date
}

"""In DB, `notes` are different for every combination of (countryCode, domain, experience) """
type PayrollInputNote {
    id: ID                              # mostly for React-keys or debugging purpose; later might come from non-DB sources, so for now leaving it nullable
    countryCode: CountryCode!           # for now no such "country-default" values; even `payrollInputNotes` is inside Country, still returning `countryCode` for easier debugging and open to changes
    domain: PayrollInputNoteDomain!     # for now no such "domain-default" values
    experience: Persona!                # for now no such "experience-default" values; only relevant to MEMBER and COMPANY
    notes: [String!]!                   # FE should show the notes in the same element order in the array
}

"""For now only applies to expense, but later can be in freelancer invoice, time off etc..."""
enum PayrollInputNoteDomain {
    EXPENSE
}

input CountryDocumentFilter {
    stateCode: String
    type: CountryDocumentType
}

input CreateDescriptionOfWorkInput {
    jobTitle: String!
    jobDescription: String!
    companyIds: [ID!]
    countryCodes: [CountryCode!]
    applyToAllCountries: Boolean
    applyToAllCompanies: Boolean
    status: DescriptionOfWorkStatus!
}


input JobDescriptionInput {
    jobTitle: String!
    jobDescription: String!
    companyIds: [ID!]
    applyToAllCompanies: Boolean
    status: DescriptionOfWorkStatus!
}

input UpdateDescriptionOfWorkInput {
    id: ID!
    jobTitle: String!
    jobDescription: String!
    status: DescriptionOfWorkStatus
}

input ChangeDescriptionOfWorkStatusInput {
    id: ID!
    status: DescriptionOfWorkStatus!
    rejectionReason: String
}

input StatusUpdateInput {
    id: ID!
    status: String!
    rejectionReason: String
}

input GlobalDescriptionOfWorkInput {
    jobTitle: String!
    jobDescription: String!
}

input DescriptionOfWorkInput {
    countryCode: CountryCode!
    jobTitle: String!
    jobDescription: String!
}

input CountryDocumentUpdateInput {
    documentId: String
    country: CountryCode!
    stateCode: String
    type: CountryDocumentType
}

input CountryContractClauseUpsertInput {
    countryCode: CountryCode!
    key: String!
    stateCode: String
    contractType: ContractType
    contractTerm: ContractTerm
    english: String!
    local: String
}

input CountryLegalDataDefinitionUpsertInput {
    countryCode: CountryCode
    contractType: ContractType!
    fetchStage: FetchStage!
    key: String!
    domainType: DomainType!
    applyTo: LegalDataRequirementApplicability!
    label: String!
    description: String
    displayOrder: Int
    isActive: Boolean = true
    isRequired: Boolean = true
    isEditable: Boolean = true
    dependsOn: [ControllingFieldInput]
    type: LegalDataType!
    textField: DataTextFieldInput
    dateField: DataDateFieldInput
    dropDownField: DataDropDownFieldInput
    dropDownTextField: DataDropDownTextFieldInput
    checkboxField: DataCheckboxFieldInput
    autoCompleteField: DataAutoCompleteFieldInput
}

input CountryExclusionsUpsertInput {
    companyId: ID
    countryExclusionServiceType: CountryExclusionServiceType!
    countryCode: CountryCode!
    isExcluded: Boolean!
    stateCode: String
}

enum CountryDocumentType {
    ONBOARDING_GUIDE
    OFFBOARDING_GUIDE
    VISA_GUIDE
}

input CountryCallingCodeFilters {
    countries: [CountryCode]
}

type State @key(fields: "code"){
    code: String
    name: String
}

# Definition
# This is the indicate the different labels the fields can have in different countries
# TODO: seems unused
type BankAccountFields {
    accountName: String
    accountNumber: String
    bankName: String
    branchName: String
    swiftCode: String
}

type CountryCallingCodeDefinition {
    country: CountryCode
    callingCode: String
}

type CountryDocument {
    id: ID
    type: CountryDocumentType
    file: FileLink
}

type CountryConfig {
    country: CountryCode!
    stateCode: String
    key: String!
    description: String
    value: CountryConfigValue!
    enabled: Boolean!
    preconditions: [CountryConfigPrecondition!]
}

union CountryConfigValue = IntConfigValue | FloatConfigValue | StringConfigValue | BooleanConfigValue

interface NumberConfigValue {
    unit: NumberConfigUnit
}

type IntConfigValue implements NumberConfigValue {
    value: Int!
    unit: NumberConfigUnit
}

type FloatConfigValue implements NumberConfigValue {
    value: Float!
    unit: NumberConfigUnit
}

enum NumberConfigUnit {
    DAYS
    WEEKS
    MONTHS
    YEARS
}

type StringConfigValue {
    value: String!
}

type BooleanConfigValue {
    value: Boolean!
}

type CountryConfigPrecondition {
    contractType: ContractType
    contractTerm: ContractTerm
}

type CountryExclusionConfig {
    service: CountryExclusionServiceType
    countries: [CountryCode] @deprecated
    countryStates: [CountryStates]
}

type CountryStates {
    country: CountryCode
    states: [String]
}

type DescriptionOfWork {
    id: ID!
    title: String!
    description: String
    status: String!
    countryCode: CountryCode!
}

type GlobalDescriptionOfWork {
    id: ID!
    title: String!
    description: String
    status: String!
}

type JobDescriptionDetails {
    id: ID!
    title: String!
    description: String
    status: String!
    updatedAt: DateTime
    rejectionReason: String
}

type JobDescriptionSummary {
    id: ID!
    descriptionTitle: String!
    description: String
    requestBy: String!
    companyName: String
    status: String!
    createdAt: DateTime
}

enum DescriptionOfWorkStatus {
    APPROVED,
    PENDING,
    REJECTED,
    DRAFT,
    DELETED
}

enum CountryExclusionServiceType {
    AOR
    COMPANY_CREATION
    EOR
    FREELANCER
    MEMBER_ONBOARDING
    VISA
}
