extend type Query {
    complianceParamsDefinition(contractType: ContractType!, country: CountryCode!, countryStateCode: String, term: ContractTerm): [ComplianceParamDefinition] @authorize(operations: ["view.operations.country.configurations"], company: ["view.company.country.configurations"], member: ["view.member.country.configurations"]) @deprecated
    compensationStructureDefinition(country: CountryCode, countryStateCode: String, term: ContractTerm, type: ContractType): CompensationStructureDefinition @authorize(operations: ["view.operations.country.configurations"], company: ["view.company.country.configurations"], member: ["view.member.country.configurations"]) @deprecated

    """
    Designed for Ops usage<br>
    countryCode=null => [global, all countries]<br>
    isActive=null => [true, false]<br>
    (null, null) => whole table<br>
    """
    contractDetailRestrictions(countryCode: CountryCode, isActive: Boolean): [ContractDetailRestriction!] @authorize(operations: ["view.operations.country.configurations"], company: ["view.company.country.configurations"], member: ["view.member.country.configurations"])
}

extend type Mutation {
    countryUpdateLegalDocumentRequirement(countryCode: CountryCode, requirementInput: LegalDocumentRequirementInput) : LegalDocumentRequirement @authorize(operations: ["update.operations.country.legal-document-requirement"])
    countryComplianceParameterUpdate(input: CountryComplianceParameterInput) : ComplianceParamDefinition @authorize(operations: ["update.operations.country.compliance-parameter"])
    countryLegalDataUpsert(input: CountryLegalDataInput!) : DataFieldDefinition @authorize(operations: ["update.operations.legal-data-definition"])
    countryLegalDataDelete(legalDataId: ID!) : TaskResponse @authorize(operations: ["update.operations.legal-data-definition"])
    countryUpdateOnboardingFlowEnablement(input: UpdateCountryOnboardingEnablementInput!): CountryCompliance @authorize(operations: ["update.operations.country.onboarding-enablement"])
    countryCompensationDefinitionUpsert(input: CountryCompensationDefinitionInput!): CompensationStructureDefinition @authorize(operations: ["update.operations.compensation-definition"])
    countryCompensationDefinitionDelete(compensationId: ID!) : TaskResponse @authorize(operations: ["update.operations.compensation-definition"])

    contractDetailRestrictionsUpsert(inputs: [ContractDetailRestrictionUpsertInput!]): [ContractDetailRestriction!] @authorize(operations: ["upsert.operations.contract-detail-restriction"])
    contractDetailRestrictionsDelete(ids: [ID!]): TaskResponse @authorize(operations: ["delete.operations.contract-detail-restriction"])
}


input ComplianceParamTypeInput {
    id: ID
    key: String
    description: String
    dataFieldType: ComplianceParamDataFieldType
}

input CountryComplianceParameterInput {
    id: ID
    countryCode: CountryCode
    countryState: String
    contractType: ContractType
    contractTerm: ContractTerm
    minimum: Int
    maximum: Int
    defaultValue: Int
    unit: ComplianceParamPeriodUnit
    required: Boolean
    complianceParamTypeId: ID
    editable: Boolean
    enabled: Boolean
    dependencies: [ControllingFieldInput!]
}

type ComplianceParamType {
    id: ID
    key: String
    description: String
    dataFieldType: ComplianceParamDataFieldType
}

input CountryLegalDataInput {
    id: ID
    country: CountryCode!
    contractType: ContractType
    applyTo: LegalDataRequirementApplicability!
    fetchStage: FetchStage!
    domainType: DomainType

    key: String!
    label: String!
    description: String
    required: Boolean!
    type: LegalDataType!
    dependsOn: [ControllingFieldInput]
    textField: DataTextFieldInput
    dateField: DataDateFieldInput
    dropDownField: DataDropDownFieldInput
    dropDownTextField: DataDropDownTextFieldInput
    checkboxField: DataCheckboxFieldInput
    autoCompleteField: DataAutoCompleteFieldInput

}

enum LegalDataType {
    ALPHA_NUMERIC
    NUMERIC
    WHOLE_NUMBER
    TEXT
    CHECKBOX

    DATE
    DROPDOWN
    DROPDOWN_TEXT
    AUTOCOMPLETE
}

input ControllingFieldInput {
    key: String!
}

input DataTextFieldInput {
    minLength: String
    maxLength: String
    pattern: String
    defaultValue: String
    type: LegalDataType
}

input DataDateFieldInput {
    minDate: Date
    maxDate: Date
    defaultValue: Date
}

input DataDropDownFieldInput {
    values: [String!]!
}

input DataDropDownTextFieldInput {
    values: [String!]!
    defaultText: String
    minLength: Int
    maxLength: Int
    type: LegalDataType
}

input DataCheckboxFieldInput {
    defaultValue: Boolean!
}

input DataAutoCompleteFieldInput {
    optionListType: String!
}

input UpdateCountryOnboardingEnablementInput {
    country: CountryCode!
    state: String
    enableStatus: Boolean!
}

input CountryCompensationDefinitionInput {
    id: ID
    country: CountryCode!
    term: ContractTerm!
    contractType: ContractType!

    components: [CountryCompensationComponentInput!]
}

input CountryCompensationComponentInput {
    key: String!
    label: String!
    description: String
    currency: CurrencyCode!
    isBase: Boolean!
    isDeletable: Boolean!
    componentType: ComponentType! #deprecated
    amountType: PayAmountType
    amount: Float
    frequency: RateFrequency
    paySchedule: ScheduleTimeInput
    type: RateType
    basedOn: String
    payOn: MonthYearInput
    condition: String
    paymentFrequency: PayFrequency
}


# deprecated
enum ComponentType {
    FIXED               # replaced by PayAmountType.FIXED_AMOUNT
    PERCENTAGE          # replaced by PayAmountType.FIXED_PERCENTAGE
    VARIABLE_AMOUNT
    VARIABLE_PERCENTAGE
}


# Definition
# All country compliance understanding.
type CountryCompliance @key(fields: "countryCode countryState { code }") {
    countryCode: CountryCode
    countryState: State
    currencies: [CurrencyCode]     # Currencies this country allows to legally pay through for compliant employments.

    # Compliance requirements for the country. Usually goes into the agreement.
    requirements(
        """
        Don't use this `contractType` and `term` parameters as they are deprecated. User `inputParams` instead.
        """
        contractType: ContractType, term: ContractTerm,

        """
        User this to pass input parameters when querying.
        """
        inputParams: RequirementsInputs
    ): ComplianceRequirementDefinition

    limitations: [ComplianceLimitationDefinition]
    legalEntity (type: LegalBindingType): CountryLegalEntity
    availableDocuments(contractType: ContractType, term: ContractTerm): [DocumentTemplate]      #TODO: To be implmented in future

    # These data has to be collected early in the workflow. To be filled by the company.
    memberDataPreFetch(workStatus: CountryWorkStatus): [DataFieldDefinition] @deprecated

    memberDataRequirements(filter: MemberDataRequirementsFilter ): [DataFieldDefinition]
    memberDocumentRequirements(
        workStatus: CountryWorkStatus, # deprecated
        contractType: ContractType, # deprecated
        filters: RequirementFiltersInput!
    ): [LegalDocumentRequirement] @deprecated

    compensationStructureDefinition(type: ContractType, term: ContractTerm): CompensationStructureDefinition
    financialYearStartMonth: Int
    onboardingFlowEnabled: Boolean
    joiningDateRestriction(workStatus: CountryWorkStatus):  ComplianceJoiningDateRestriction
    country13th14thMonthPay: Country13th14thMonthPay

    """Priority: country-specific restrictions > global restrictions"""
    contractDetailRestrictions: [ContractDetailRestriction!]
}

# Input parameters for member data requirements
input MemberDataRequirementsFilter {
    workStatus: CountryWorkStatus
    contractType: ContractType
    fetchStage: FetchStage
    onlyDynamicRequirements: Boolean
}

input RequirementFiltersInput {
    workStatus: CountryWorkStatus
    contractType: ContractType
    contractTerm: ContractTerm
    contractStatus: ContractStatus
    applyTo: LegalDataRequirementApplicability
    joinDate: Date
    financialYear: Int
    category: LegalDocumentCategory
}

enum FetchStage {
    CONTRACT_GENERATION         # Basic details collection stage for contract.
    MEMBER_LEGAL_DATA_CAPTURE   # Member on-boarding legal data collection stage.
}

# Input parameters for the country compliance requirements query.
input RequirementsInputs {
    contractType: ContractType
    contractTerm: ContractTerm
    contractStartDate: DateTime
    contractEndDate: DateTime
}

# Definition
# The legal entity by country. It could be multiplier or partner entity
type CountryLegalEntity {
    type: LegalBindingType
    legalEntity: LegalEntity
}

# Definition
# This refers to the compliance limitations by country.
type ComplianceLimitationDefinition {
    key: String!            # Eg: contractTerm
    label: String           # Eg: contract-term
    value: String!          # Eg: PERMANENT
    enabled: Boolean        # Eg: false
    description: String     # Eg: Country SGP does not provide Permanent Employment in the employee’s first year of employment
    countryState: String
    preconditions: [ComplianceLimitationPrecondition!]  # defined as an array, a limitation shall be applied when any of its preconditions is met (OR logic)
}

# Definition
# The compliance requirement definitions for country.
type ComplianceRequirementDefinition {
    usedScope: ComplianceDefinitionScope       # This indicate whether this compliance requirement definitions are country specific or default ones. (Countries with no compliance will get default set of definitions)
    paramDefinitions: [ComplianceParamDefinition]   # Compliance param definitions.
}

# Definition
# Precondition for country compliance limitation, a precondition is met when all of its (sub) conditions are met (AND logic)
type ComplianceLimitationPrecondition {
    contractType: ContractType  # Eg: EMPLOYEE, HR_MEMBER, ...
    contractTerm: ContractTerm  # Eg: PERMANENT, FIXED
}

# Definition
# Define whether compliance requirement definitions are country specific or default.
enum ComplianceDefinitionScope {
    COUNTRY_SPECIFIC
    DEFAULT
}


# Definition
# This refers to configuration that provides the validation ruleset for a particular
# compliance param. This is meant for consumers to determine how best to mutate the compliance
# params.
type ComplianceParamDefinition {
    param: ComplianceParam
    required: Boolean # determines whether a clause can be removed from the eventual contract
    editable: Boolean # determines whether a clause's details can be edited, i.e. 3 months -> 2 months
    enabled: Boolean  # determines whether a clause is enabled/disabled by default
    validation: [ComplianceParamValidation]
    dataFieldType: ComplianceParamDataFieldType

    # ComplianceParams listed here must be enabled before this ComplianceParam can be enabled
    # Consumers should automatically disable this ComplianceParam if any in the dependency list is disabled
    dependencies: [ComplianceParam]
}

enum ComplianceParamDataFieldType {
    TEXT
    TOGGLE
    DROPDOWN
}

union ComplianceParamValidation = ComplianceParamPeriodLimitUnitValidation

# Definition
# Validations specific to period limits
type ComplianceParamPeriodLimitUnitValidation {
    minimum: Int
    maximum: Int
    defaultValue: Int
    unit: ComplianceParamPeriodUnit
}

type DocumentTemplate {
    key: String                 # Key to identify a type of document
    templateId: String          # The DocGen template id.
    usedScope: ComplianceDefinitionScope
}

# Definition
# This refers to configuration that gives structure to the compensation form
type CompensationStructureDefinition {
    id: ID
    countryCode: CountryCode
    countryState: State
    basePay: FixedPayComponentDefinition
    other: [FixedPayComponentDefinition] @deprecated #Deprecating this to support multiple pay component types, please use additional pays
    additionalPays: [CompensationPayComponentDefinition]
    showAdditionalRequirementInput: Boolean
    allowCustomFixedComponents: Boolean
    deductions: [DeductionDefinition]
}

type DeductionDefinition {
    id: ID
    groupKey: String # loose groupings that may have different contribution requirements or types, e.g. 401K, RRSP, etc.
    description: String @deprecated(reason: "It is no longer needed, as we handle description in Locize directly.")
    contributionBy: ContributionBy
    isMandatory: Boolean
    deductionRequirement: DeductionRequirement @deprecated(reason: "Use deductionConstraint instead.")
    deductionConstraint: DeductionRequirement
}

enum ContributionBy {
    EMPLOYER
    EMPLOYEE
}

# Allow FixedPercentageDeductionRequirement (now) and in future something like
# FreePercentageDeductionRequirement where user can enter whatever value.
interface DeductionRequirement {
    name: String
    type: DeductionRequirementType @deprecated(reason: "use 'name' property instead.")
}

"""
Deprecated. Use string instead.
"""
enum DeductionRequirementType {
    EMPLOYER_MATCH_LIMIT @deprecated (reason: "Whole enum is deprecated. Instead of using the enum value the string is going to be used.")
    NOT_USED @deprecated (reason: "Whole enum is deprecated. Instead of using the enum value the string is going to be used.")
    EMPLOYEE_MATCH_LIMIT @deprecated
}

"""
Deprecated. Use `FixedDeductionRequirement` type instead.
"""
type FixedPercentageDeductionRequirement implements DeductionRequirement {
    type: DeductionRequirementType @deprecated(reason: "Use FixedDeductionRequirement.type instead.")
    name: String
    values: [Float] @deprecated(reason: "Use FixedDeductionRequirement.options instead.")
}

type FixedDeductionRequirement implements DeductionRequirement {
    type: DeductionRequirementType @deprecated(reason: "Use FixedDeductionRequirement.name instead.")
    name: String
    options: [DeductionValue]
}

type DeductionValue {
    value: Float!
    unit: DeductionUnit!
}

interface CompensationPayComponentDefinition {
    name: String
    label: String
    description: String
    frequency: RateFrequency
    rateType: RateType
    payOn: MonthYear
    isDeletable: Boolean
}

# Definition
type FixedPayComponentDefinition implements CompensationPayComponentDefinition {
    name: String
    label: String
    description: String
    frequency: RateFrequency
    rateType: RateType
    payOn: MonthYear
    isDeletable: Boolean
}

type PercentPayComponentDefinition implements CompensationPayComponentDefinition {
    name: String
    label: String
    description: String
    frequency: RateFrequency
    rateType: RateType
    payOn: MonthYear
    basedOn: FixedPayComponentDefinition
    isDeletable: Boolean
}

"""
Defines a data field definition so the UI can render accordingly.
The intention is to replace all the dynamic UI generation definitions by this
i.e. LegalDataRequirement, LegalDocument etc...
"""
type DataFieldDefinition {
    id: ID
    domainType: DomainType          # The domain of the data whether it is member data or legal data
    key: String                     # A identifier/label with some possible-uniqueness...
    label: String                   # label for the field i.e. Passport No
    description: String             # The description probably to show as a help text
    required: Boolean               # whether this field is required
    dataType: DataFieldType         # whether its a Text Field, Date Field etc...
    dependsOn: [ControllingField!]
    editable: Boolean
}

union DataFieldType = TextField
    | DateField
    | DropDownField
    | DropDownTextField
    | CheckboxField
    | AutoCompleteField
    | FileField
# Can extend with other new data types i.e. DocUploadField etc...

type TextField {
    minLength: Int
    maxLength: Int
    pattern: String             # A regular expression to match the text field
    defaultValue: String        # default value or the current value
    type: String                # Type of the text input - Could be ALPHANUMERIC or NUMERIC or TEXT
}

type DropDownField {
    values: [String]
}

type DropDownTextField {
    values: [String]        # Drop down values
    defaultText: String     # Default input text or the current text value
    minLength: Int          # Min character length for text input field
    maxLength: Int          # Max character length for text input field
    type: String            # Type of the text input - Could be ALPHANUMERIC or NUMERIC or TEXT
}

type DateField {
    minDate: Date
    maxDate: Date
    defaultValue: Date
}

type CheckboxField {
    defaultValue: Boolean!
}

type AutoCompleteField {
    optionListType: String
}

type FileField {
    acceptMultiple: Boolean
}

input LegalDocumentRequirementInput {
    key: String
    countryCode: CountryCode
    category: LegalDocumentCategory
    state: String
    label: String
    startDate: Date
    endDate: Date
    description: String
    required: Boolean
    acceptMultiple: Boolean
    applyTo: LegalDataRequirementApplicability
    documents: [Upload]
}

enum DomainType {
    LEGAL_DATA # Data required for Legal Data repos.
    MEMBER_DATA # Data required for member data repos.
    LEGAL_ENTITY_PAYROLL_BASIC_INFO
    LEGAL_ENTITY_PAYROLL_TAX
    LEGAL_ENTITY_PAYROLL_INSURANCE
    LEGAL_ENTITY_PAYROLL_BANK
    LEGAL_ENTITY_PAYROLL_CONTACT
    LEGAL_ENTITY_PAYROLL_DOCUMENT
}

# Definition
# Defines a legal data requirement for a member
# This is to be replaced by DataFieldDefinition in hte future
type LegalDataRequirement {
    key: String                     # A identifier/label with some possible-uniquness...
    label: String                   # Country-specific local name.. Ex - { International, NIC } is {Norway, SocialNumber}
    description: String
    required: Boolean
    type: String
    minChars: Int
    maxChars: Int
    applyTo: LegalDataRequirementApplicability  #TODO: UI is not using this field. To be removed?
    dependsOn: [ControllingField]
}

# Definition
# Define whether the legal data requirements apply to local only, expat only or all.
enum LegalDataRequirementApplicability {
    LOCAL_ONLY
    EXPAT_ONLY
    ALL
}

# Definition
# Defines a legal document. Ex: Passport, NIC, Adhar, Social No, Europe, UTR, Britain, Driver's License
type LegalDocumentRequirement {
    key: String                     # A identifier/label with some possible-uniquness...
    label: String                   # Country-specific local name..
    description: String
    category: LegalDocumentCategory
    required: Boolean
    startDate: Date
    endDate: Date
    acceptMultiple: Boolean
    applyTo: LegalDataRequirementApplicability
    template: [DocumentReadable]
    deadline: Date
    legalDocumentInfo: String
}

type LegalEntityPayrollDataRequirements {
    country: CountryCode
    requirements: [DataFieldDefinition]
}

type ControllingField {
    key: String!
}

type ComplianceJoiningDateRestriction {
    earliestJoiningDate: Date!
    checkpoints: ComplianceJoiningCheckpoints
    holidays: [Holiday!]!
}

type ComplianceJoiningCheckpoints {
    earliestVisaCompletionDate: Date
    # more checkpoints to be added here...
}

type Country13th14thMonthPay {
    is13thMonthPayApplicable: Boolean
    is14thMonthPayApplicable: Boolean
    isIncludedInGross: Boolean
    description13thMonthPay: String
    description14thMonthPay: String
    note: String
    payFrequencyType: Country13th14thMonthPayFrequencyType
    schedules: [Country13th14thMonthPaySchedule]
}

type Country13th14thMonthPaySchedule {
    country13th14thMonthPayType: Country13th14thMonthPayType
    monthlySalaryMultiple: Float
    scheduleTimeUnit: ScheduleTimeUnit
    payoutDay: Int
}

enum Country13th14thMonthPayFrequencyType{
    INSTALLMENT
    PAYROLL
}

enum Country13th14thMonthPayType {
    THIRTEENTH_MONTH_PAY
    FOURTEENTH_MONTH_PAY
}

# Contract Detail Restriction area

"""
DB: country.contract_detail_restriction.
Case sensitive: No.
"""
type ContractDetailRestriction {
    id: ID
    countryCode: CountryCode                                # null indicates "global" effect
    field: ContractDetailRestrictionField
    searchTerm: String                                      # e.g. "COO"
    operator: ContractDetailRestrictionOperator
    alternatives: [String!]                                 # e.g. ["Head of operations", "Head of operating"]
    isActive: Boolean
    createdOn: DateTime
    updatedOn: DateTime
}

enum ContractDetailRestrictionField {
    JOB_TITLE
    JOB_DESCRIPTION
}

enum ContractDetailRestrictionOperator {
    EQUAL
    START_WITH
    CONTAIN
}

"""
For now we don't allow to reactivate an "inactive" record i.e. setting is_active=true (except db update).<br>
All fields need to be present (even you don't change them),
otherwise they will be saved as null/empty (countryCode, alternatives) or will be rejected (field, searchTerm, operator)
"""
input ContractDetailRestrictionUpsertInput {
    id: ID                                                  # null = insert, not-null = update
    countryCode: CountryCode                                # null indicates "global" effect
    field: ContractDetailRestrictionField
    searchTerm: String                                      # e.g. "COO"
    operator: ContractDetailRestrictionOperator
    alternatives: [String!]                                 # e.g. ["Head of operations", "Head of operating"]
}

# Contract Detail Restriction area - END
