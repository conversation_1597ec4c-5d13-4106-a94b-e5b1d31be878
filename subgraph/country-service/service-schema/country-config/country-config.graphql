extend type Query {
    # Get all configs for a specific country
    countryConfigs(
        schema: String!, # use CountryConfigSchema
        countryCode: CountryCode!,
        profileCriteria: ProfileCriteriaInput,
        countryStateCode: String
    ): [CountryConfigs!] @authorize(operations: ["view.operations.country.configurations"])
}

extend type Mutation {
    # Upsert country configs - would affect the given country
    updateCountryConfigs(
        schema: String!, # use CountryConfigSchema
        countryCode: CountryCode!,
        countryStateCode: String
        configs: [CountryConfigsInput!]!
    ): Bo<PERSON><PERSON> @authorize(operations: ["update.operations.country.configurations"]) @deprecated(reason: "use proposeEditRequest instead")

    # Disable these for now, would implement later when requested with product requirement
    # # Copy config from 1 country to another - for quick setup
    # cloneCountryConfigs(
    #     schema: String!, # use CountryConfigSchema
    #     profileCriteria: ProfileCriteria!,
    #     fromcountryCode: CountryCode!,
    #     fromCountryStateCode: String,
    #     tocountryCode: CountryCode!,
    #     toCountryStateCode: String
    # ): [CountryConfigs!] @authorize(operations: ["update.operations.country.configurations"])

    # # Delete a config would affect all countries
    # deleteCountryConfigs(input: [DeleteCountryConfigsInput!]!): Boolean @authorize(operations: ["update.operations.country.configurations"])
}

# ---------- enums

enum CountryConfigSchema {
    COUNTRY_READINESS
}

enum ConfigType {
    TEXT
    BOOLEAN
}

# ---------- inputs

input ProfileCriteriaInput {
    criteria: [CriterionInput!]!
}

input CriterionInput {
    key: String!
    value: String
}

input CountryConfigsInput {
    profileCriteria: ProfileCriteriaInput, # null = apply to all profiles
    section: String!
    field: String!
    textValue: TextConfigsValueInput
    booleanValue: BooleanConfigsValueInput
}

input TextConfigsValueInput {
    value: String
}

input BooleanConfigsValueInput {
    value: Boolean
}

# input DeleteCountryConfigsInput {
#     schema: String! # use CountryConfigSchema
#     sections: [DeleteCountryConfigsSectionInput!]!
# }

# input DeleteCountryConfigsSectionInput {
#     section: String!
#     keysToDelete: [String!]!
# }

# ---------- types

type ProfileCriteria {
    criteria: [Criterion!]!
}

type Criterion {
    key: String!
    value: String
}

type CountryConfigs {
    profileCriteria: ProfileCriteria, # null = apply to all profiles
    section: String!
    field: String!
    type: ConfigType!
    value: ConfigsValue!
    options: ConfigsOptions!
}

union ConfigsValue = TextConfigsValue | BooleanConfigsValue

type TextConfigsValue {
    value: String
}

type BooleanConfigsValue {
    value: Boolean
}

interface ConfigsOptions {
    order: Int
    applyToAllProfiles: Boolean
}

type TextOptions implements ConfigsOptions {
    order: Int
    multiline: Boolean
    defaultValue: String
    applyToAllProfiles: Boolean
}

type BooleanOptions implements ConfigsOptions {
    order: Int
    defaultValue: Boolean
    applyToAllProfiles: Boolean
}
