extend type Query {
    getEditRequests(
        input: GetEditRequestsInput!,
    ): GetEditRequestsResponse @authorize(operations: ["view.operations.country.configurations"])

    getChangeLogs(
        input: GetChangeLogsInput!,
    ): GetChangeLogsResponse @authorize(operations: ["view.operations.country.configurations"])
}

extend type Mutation {
    proposeEditRequest(
        input: ProposeEditRequestInput!,
    ): EditRequest @authorize(operations: ["update.operations.country.configurations"])

    ## for approver

    approveEditRequest(
        input: ApproveEditRequestInput!,
    ): EditRequest @authorize(operations: ["approve.operations.country.configurations"])

    rejectEditRequest(
        input: RejectEditRequestInput!,
    ): EditRequest @authorize(operations: ["approve.operations.country.configurations"])
}

# ---------- inputs

input GetEditRequestsInput {
    ids: [ID!]
    indexes: [EditRequestIndex!]
    countries: [CountryCode!]
    statuses: [EditRequestStatus!]
    requestedBy: [ID!] # for Approver to see Editors requests, Editor could only see their own
    pageRequest: PageRequest
}

input GetChangeLogsInput {
    ids: [ID!]
    indexes: [EditRequestIndex!]
    countries: [CountryCode!]
    approvedOn: DurationInput
    pageRequest: PageRequest
}

input ProposeEditRequestInput {
    index: EditRequestIndex!
    countryCode: CountryCode!
    countryStateCode: String

    changeProposal: EditRequestChangeProposalInput!

    summary: String!
    notifyViaEmail: Boolean
    comment: String
}

input EditRequestChangeProposalInput {
    countryConfigsInput: EditRequestCountryConfigsChangeProposalInput
    compliancePolicyInput: EditRequestCompliancePolicyChangeProposalInput
}

input EditRequestCountryConfigsChangeProposalInput {
    schema: String! # use CountryConfigSchema
    configs: [CountryConfigsInput!]!
}

input EditRequestCompliancePolicyChangeProposalInput {
    index: CompliancePolicyIndex!
    category: CompliancePolicyCategory!
    body: String
    impactedArea: [ImpactedAreaInput!]
    sources: [SourceInput!]
}

input ImpactedAreaInput {
    tag: String
    description: String
}

input SourceInput {
    name: String
    content: String
}

input DurationInput {
    from: Date!
    to: Date # null = today
}

input ApproveEditRequestInput {
    id: ID!
    feedback: String
}

input RejectEditRequestInput {
    id: ID!
    feedback: String!
}

# ---------- types

type GetEditRequestsResponse {
    editRequests: [EditRequest!]!
    pageResult: PageResult!
}

type GetChangeLogsResponse {
    changeLogs: [ChangeLog!]!
    pageResult: PageResult!
}

interface EditRequestItem {
    id: ID!
    changeProposal: ChangePayload!
}

type EditRequest implements EditRequestItem {
    id: ID!
    index: EditRequestIndex!
    countryCode: CountryCode!
    countryStateCode: String

    summary: String!
    comment: String
    status: EditRequestStatus!

    changeProposal: ChangePayload!

    approval: ApprovalRequest
}

type ChangeLog implements EditRequestItem {
    id: ID!
    index: EditRequestIndex!
    countryCode: CountryCode!
    countryStateCode: String

    summary: String!
    comment: String

    changeProposal: ChangePayload!

    approval: ApprovalRequest
}

union ChangePayload = CountryConfigsChanges | CompliancePolicyChanges

type CountryConfigsChanges {
    schema: String!, # use CountryConfigSchema
    original: [CountryConfigs!]!
    updated: [CountryConfigs!]!
}

type CompliancePolicyChanges {
    index: CompliancePolicyIndex!
    category: CompliancePolicyCategory!
    original: CompliancePolicy!
    updated: CompliancePolicy!
}

# ---------- enums

enum EditRequestStatus {
    PENDING
    APPROVED
    REJECTED
}

enum EditRequestIndex {
    COUNTRY_WIDGET
    ANNUAL_LEAVE
}
