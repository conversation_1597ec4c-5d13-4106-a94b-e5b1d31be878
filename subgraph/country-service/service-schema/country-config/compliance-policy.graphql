extend type Query {
    compliancePolicy(
        index: CompliancePolicyIndex!,
        category: CompliancePolicyCategory!,
        countryCode: CountryCode!,
        countryStateCode: String
    ): CompliancePolicy @authorize(operations: ["view.operations.country.compliance-policy"])
}

type CompliancePolicy {
    index: CompliancePolicyIndex!
    category: CompliancePolicyCategory!

    countryCode: CountryCode!
    countryStateCode: String

    body: String!
    impactedArea: [ImpactedArea!]
    sources: [Source!]
}

type ImpactedArea {
    tag: String
    description: String
}

type Source {
    name: String
    content: String
}

type SystemRule {
    id: ID!
}

enum CompliancePolicyIndex {
    LEAVE_POLICY
    BENEFIT_POLICY
    COMPENSATION_POLICY
    EMPLOYMENT_LAW_POLICY
    EMPLOYMENT_TERMS_AND_CONDITIONS_POLICY
    SEPARATION_POLICY
}

enum CompliancePolicyCategory {
    ANNUAL_LEAVE
    SICK_LEAVE
    MATERNITY_LEAVE
    OTHER_LEAVES
    PUBLIC_HOLIDAYS
    HEALTH_INSURANCE_BENEFITS
    MANDATORY_HEALTH_INSURANCES
    MEDICAL_CHECKUPS
    HEALTH_AND_SAFETY_TRAININGS
    SOCIAL_SECURITY_PENSION_CONTRIBUTION
    THIRTEENTH_FOURTEENTH_MONTH_PAY
    PAYROLL_FREQUENCY
    TAX_AND_SALARY_CALCULATIONS
    MINIMUM_WAGES
    COUNTRY_PAYROLL_CURRENCY
    PAYSLIP_LOCAL_LANGUAGE
    CONTRACTS_FEASIBILITY
    SIGNATURE_AND_BILLINGUAL_REQUIREMENT
    BACKDATING_ALLOWED_OR_NOT
    PRE_REGISTRATION_REQUIREMENTS
    FIXED_TERM_RESTRICTIONS_IMPLEMENTATION
    NON_SOLICITATION
    NON_COMPETE
    PROBATION
    WORKING_HOURS
    NOTICE_PERIOD
    TERMINATION_PROCESS
    SEVERANCE_PAY
}
