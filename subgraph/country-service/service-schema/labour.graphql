input CountryLabourStandardsFilter {
    stateCode: String
}

type CountryLabourStandards {
    countryCode: CountryCode
    countryStateCode: State
    workShift: CountryWorkShiftStandards         # Country specific work shift standards
    compensation: CountryCompensationStandards   # Country specific compensation standards
}

# Country specific work shift standards.
type CountryWorkShiftStandards {
    startDay: DayOfWeek                 # Work shift start day. (Eg: Mon)
    endDay: DayOfWeek                   # Work shift end day. (Eg: Fri)
    workingHoursInfoText: String        # Work shift info text for specific countries (Eg: The average working hours in The Philippines ranges from 160 ~ 176 hours per month.)
    defaultWorkingHours: WorkingHours   # Default working hours/times. (Eg: from 08:00 to 17:00)
    averageWorkingHoursPerMonth: Int    # Average working hours per month. (Eg: 160 hours for PHL)
}

# Country specific default compensation standards.
type CountryCompensationStandards {
    rateFrequency: CountryRateFrequency
    paymentFrequency: CountryPaymentFrequency
    hourlyRates: CountryHourlyRates
}

# Billing rate frequency for country.
type CountryRateFrequency {
    list: [RateFrequency]           # Supported rate frequency list (Eg: HOURLY / SEMIMONTHLY / MONTHLY, etc)
    defaultValue: RateFrequency     # Default value
}

# Payment frequency for country.
type CountryPaymentFrequency {
    list: [PayFrequency]                # Supported payment frequency list. (Eg: WEEKLY / SEMIMONTHLY / MONTHLY, etc)
    data: [CountryPaymentFrequencyData] # Supported payment frequencies match for each rate frequency. (Eg: rateFrequency=HOURLY, payFrequencies=[SEMIMONTHLY, MONTHLY], etc)
    defaultValue: PayFrequency          # Default value
    payDates: [PayFrequencyDate]        # The date/dates the payment happen according to the default "pay frequency" value
}

type CountryPaymentFrequencyData {
    rateFrequency: RateFrequency
    payFrequencies: [PayFrequency]
}

# Hourly rates for country.
type CountryHourlyRates {
    helpUrl: String                     # Help or "learn more" url to navigate
    values: [CountryHourlyRateValue]    # List of hourly rate values for a country
}

# Hourly rate value.
type CountryHourlyRateValue {
    key: String             # Eg: "night-shift-differential"
    label: String           # Eg: "Night Shift Differential"
    description: String
    value: String           # Eg: "10%"
}
