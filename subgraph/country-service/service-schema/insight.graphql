# Generic Domain model
enum InsightCategory {
    COMPLIANCE
    TIME_OFF
    BENEFIT
    COMPENSATION
}

interface InsightDefinition {
    id: ID!
    category: InsightCategory
    description: String # Only string-type insights are modelled for now

    actions: [InsightAction]
}

input InsightDefinitionFilter {
    stateCode: String,
    categories: [InsightCategory]

    contractType: ContractType
    contractTerm: ContractTerm
    contractStartDate: Date
    contractEndDate: Date
}

type TimeOffInsightDefinition implements InsightDefinition {
    id: ID!
    category: InsightCategory
    description: String
    actions: [InsightAction]

    recommendedValue: Float
    timeOffUnit: TimeOffUnit

    timeOffTypeDefinition: TimeOffTypeDefinition
}

type ComplianceInsightDefinition implements InsightDefinition {
    id: ID!
    category: InsightCategory
    description: String
    actions: [InsightAction]

    recommendedValue: Float
    complianceParamPeriodUnit: ComplianceParamPeriodUnit

    complianceParam: ComplianceParam
}

type BenefitInsightDefinition implements InsightDefinition {
    id: ID!
    category: InsightCategory
    description: String
    actions: [InsightAction]

    benefitType: String # Benefits don't have definitions
}

type CompensationInsightDefinition implements InsightDefinition {
    id: ID!
    category: InsightCategory
    description: String
    actions: [InsightAction] # type: leave, timeOffKey: sick, value: 7

    compensationStructureDefinition: CompensationPayComponentDefinition
}

type InsightAction {
    key: String
    value: String
}
