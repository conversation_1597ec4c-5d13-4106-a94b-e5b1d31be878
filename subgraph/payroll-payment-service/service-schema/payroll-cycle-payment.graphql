extend type Query {
    payrollCyclePayments(filters: PayrollCyclePaymentFilters, pageRequest: PageRequest): PayrollCyclePaymentResult @authorize(operations: ["view.operations.payroll-payments"])
    payrollCyclePayment(id: ID!): PayrollCyclePayment @authorize(operations: ["view.operations.payroll-payments"])
    payrollPaymentBatches(filters: PayrollPaymentBatchFilters, pageRequest: PageRequest): PayrollPaymentBatchResult @authorize(operations: ["view.operations.payroll-payments"])
    payrollPaymentBatch(id: ID!): PayrollPaymentBatch @authorize(operations: ["view.operations.payroll-payments"])
}

extend type Mutation {
    createPayrollPaymentBatch(payrollPaymentBatch: PayrollPaymentBatchInput!): PayrollPaymentBatch @authorize(operations: ["update.operations.payroll-payments|generate.operations.pif"])
    recreatePayrollPaymentBatch(id: ID!): PayrollPaymentBatch @authorize(operations: ["update.operations.payroll-payments|generate.operations.pif"])
    deletePayrollPaymentBatch(id: ID!): TaskResponse @authorize(operations: ["update.operations.payroll-payments"])
    updatePayrollPaymentBatchStatus(ids: [ID]!, payrollPaymentBatchStatus: PayrollPaymentBatchStatus!): [PayrollPaymentBatch] @authorize(operations: ["update.operations.payroll-payments"])
    updatePayrollPaymentStatus(ids: [ID]!, status: PayrollPaymentStatus!): [PayrollPayment] @authorize(operations: ["update.operations.payroll-payments"])
    executeBatchPayment(ids: [ID]!): [PayrollPaymentBatch] @authorize(operations: ["execute.operations.payroll-payments"])
    syncPayrollCycle(ids: [ID]!) : TaskResponse @authorize(operations: ["update.operations.payroll-payments"])
}

type PayrollCyclePaymentResult {
    data: [PayrollCyclePayment]
    pageResult: PageResult
}

type PayrollPaymentResult {
    data: [PayrollPayment]
    pageResult: PageResult
}

type PayrollPaymentBatchResult {
    data: [PayrollPaymentBatch]
    pageResult: PageResult
}

type PayrollCyclePayment @key(fields: "id") {
    id: ID!
    payrollCycle: PayrollCycle
    status: PayrollCyclePaymentStatus
    type: PayrollCyclePaymentType
    payrollPaymentsPaginated(filters: PayrollPaymentFilters, pageRequest: PageRequest): PayrollPaymentResult
}

type PayrollPayment @key(fields: "id")   {
    id: ID!
    contract: Contract
    payrollCyclePayment: PayrollCyclePayment
    payrollPaymentType: PayrollPaymentType
    payrollPaymentStatus: PayrollPaymentStatus
    payrollPaymentInfo: PayrollPaymentInfo
    paidOn: DateTime
    executionType: PayrollPaymentExecutionType
}

type PayrollPaymentInfo {
    id: ID!
    amount: Float
    currency: CurrencyCode
    payrollPaymentBatch: PayrollPaymentBatch
}

interface PayrollPaymentBatch {
    id: ID!
    payrollPaymentBatchName: String
    payrollPaymentBatchStatus: PayrollPaymentBatchStatus
    payrollPaymentBatchType: PayrollPaymentBatchType
    payrollPaymentBatchPaymentPriorityType: PaymentPriorityType
    payrollPaymentBatchPayoutDate: DateTime
    payrollPaymentsPaginated(filters: PayrollPaymentFilters, pageRequest: PageRequest): PayrollPaymentResult
    errors: [PayrollPaymentBatchError]
    createdOn: DateTime
}

type PayrollPaymentFileBatch implements PayrollPaymentBatch {
    id: ID!
    payrollPaymentBatchName: String
    payrollPaymentBatchStatus: PayrollPaymentBatchStatus
    payrollPaymentBatchType: PayrollPaymentBatchType
    payrollPaymentBatchPaymentPriorityType: PaymentPriorityType
    payrollPaymentBatchPayoutDate: DateTime
    payrollPaymentsPaginated(filters: PayrollPaymentFilters, pageRequest: PageRequest): PayrollPaymentResult
    batchFile: DocumentReadable
    errors: [PayrollPaymentBatchError]
    createdOn: DateTime
}

type PayrollPaymentBatchError {
    payrollPayment: PayrollPayment
    messages: [String]
    errorType: PayrollPaymentBatchErrorType
}

enum PayrollPaymentBatchType {
    FILE
}

enum PayrollPaymentBatchStatus {
    IN_PROGRESS
    CREATED
    PROCESSING
    PAID
    DELETED
    FAILED
}

enum PayrollPaymentBatchStep {
    EXECUTE
    CREATE
    DELETE
}

enum PayrollPaymentType {
    SALARY_PAYMENT
    EXPENSES_PAYMENT
    PAY_SUPPLEMENT_PAYMENT
}

enum PayrollPaymentStatus {
    PENDING
    PROCESSING
    PAID
    FAILED
    VOID
}

enum PayrollPaymentExecutionType {
    MANUAL
    SYSTEM
}

enum PaymentPriorityType {
    URGENT_PAYMENT
    NON_URGENT_PAYMENT
}

enum PayrollCyclePaymentStatus {
    CREATED
    PROCESSING
    COMPLETED
    VOID
}

input PayrollPaymentBatchInput {
    payrollPayments: [ID]!
    payoutDate: Date!
    priority: PaymentPriorityType
    paymentPartner: PaymentPartnerCode
}

input PayrollCyclePaymentFilters {
    payrollMonth: Date
    country: [String]
    status: [PayrollCyclePaymentStatus]
    payrollCycleId: String
    types: [PayrollCyclePaymentType]
}

input PayrollPaymentFilters {
    payrollPaymentStatus: [PayrollPaymentStatus]
    contractId: ID
    companyName: String
    companyId: String
    firstInvoiceStatus: [String]
    memberName: String
    executionType: [PayrollPaymentExecutionType]
}

input PayrollPaymentBatchFilters {
    payrollCyclePaymentId: ID
    batchName: String
    batchStatuses: [PayrollPaymentBatchStatus]
    batchStepStatusFilters: [PayrollPaymentBatchStepStatusFilter]
}

input PayrollPaymentBatchStepStatusFilter {
    batchStep: PayrollPaymentBatchStep!
    batchStatuses: [PayrollPaymentBatchStatus]!
}

enum PayrollPaymentBatchErrorType {
    VALIDATION_ERROR
    INTERNAL_ERROR
}

enum PayrollCyclePaymentType {
    EOR
    PEO
}
