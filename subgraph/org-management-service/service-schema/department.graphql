extend type Query {
    # Returns list of departments filtered by id or status. Employees within the department will be filtered by employee filters.
    departments(filters: DepartmentFilters): [Department!]!     @authorize(company: ["view.company.department"], operations: ["view.operations.department"])
}

extend type Mutation {
    departmentCreate(input: DepartmentCreateInput!): Department     @authorize(company: ["create.company.department"], operations: ["create.operations.department"])
    departmentUpdate(input: DepartmentUpdateInput!): Department     @authorize(company: ["update.company.department"], operations: ["update.operations.department"])
    departmentDelete(input: DepartmentDeleteInput!): Department     @authorize(company: ["delete.company.department"], operations: ["delete.operations.department"])
    departmentAssignEmployees(input: DepartmentAssignEmployeesInput!): Department   @authorize(company: ["update.company.contract.departments|assign.company.department-employees"], operations: ["update.operations.contract.departments|assign.operations.department-employees"])
}

input DepartmentFilters {
    id: ID
    companyId: ID
    statuses: [DepartmentStatus!]
}

input DepartmentEmployeeFilters {
    countries: [CountryCode!]
    designations: [String!]
    contractTypes: [ContractType!]
}

input DepartmentCreateInput {
    name: String!
    companyId: ID!
    description: String
    hod: HeadOfDepartmentInput
}

input DepartmentUpdateInput {
    id: ID!
    name: String!
    description: String
    hod: HeadOfDepartmentInput
}

input DepartmentDeleteInput {
    id: ID!
}

input HeadOfDepartmentInput {
    # Either contractId or managerId must be provided.
    contractId: ID
    managerId: ID
}

input DepartmentAssignEmployeesInput {
    departmentId: ID!
    employeeContractIds: [ID!]!
    employeeManagerIds: [ID!]!
}

type Department {
    id: ID!
    name: String!
    description: String
    status: DepartmentStatus    # Department status. Will be CREATED by default.
    hod: HeadOfDepartment
    employees(filters: DepartmentEmployeeFilters): OrgDirectory     @authorize(company: ["view.company.department"], operations: ["view.operations.department"])     # List of managers and contracts in the department.
}

type HeadOfDepartment {
    # Either contract or manager must be returned since Head Of Department may not have a contract (eg: company user with no contract).
    contract: Contract
    manager: Manager
}

enum DepartmentStatus {
    CREATED
    DELETED
}
