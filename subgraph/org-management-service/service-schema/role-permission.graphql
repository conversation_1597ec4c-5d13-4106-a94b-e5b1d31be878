extend type Query {

    # Deprecated queries
    orgRoles(filter: OrgRoleFilters!): [OrgRole!]           @deprecated(reason: "Not required and no FE or BE usages")  @authorize(operations: ["view.operations.manager"])
    permissions: [Permission!]                              @deprecated(reason: "Not required and no FE or BE usages")  @authorize(operations: ["view.operations.manager"])
    orgRoleAvailableManagers(companyId: ID!): [Manager!]    @deprecated(reason: "Not required and no FE or BE usages")  @authorize(operations: ["view.operations.manager"])
}

extend type Mutation {

    # Deprecated mutations
    orgRoleCreate(role: OrgRoleCreateInput!): OrgRole   @deprecated(reason: "Not required and no FE or BE usages")  @authorize(operations: ["create.operations.manager"])
    orgRoleUpdate(role: OrgRoleUpdateInput!): OrgRole   @deprecated(reason: "Not required and no FE or BE usages")  @authorize(operations: ["create.operations.manager"])
    orgRoleDelete(id: ID!): OrgRole                     @deprecated(reason: "Not required and no FE or BE usages")  @authorize(operations: ["create.operations.manager"])
    orgRoleAssignManagers(input: OrgRoleAssignManagersInput!): OrgRole @deprecated(reason: "Not required and no FE or BE usages")  @authorize(operations: ["create.operations.manager"])
}

type OrgRole {
    id: ID!
    name: String!
    companyId: ID!
    permissions: [Permission!]
    managers: [Manager!]
    createdOn: DateTime
    updatedOn: DateTime
}

"""
Platform permission (pre-defined)
"""
type Permission {
    id: ID!
    name: String!
}

input OrgRoleFilters {
#   find by ID if not null
    id: ID

#   if ID is null, use below filter(s)
    companyId: ID
}

input OrgRoleCreateInput {
    companyId: ID!
    name: String!
    permissions: [ID!]!
}

input OrgRoleUpdateInput {
    id: ID!
    name: String!
    permissions: [ID!]!
}

input OrgRoleAssignManagersInput {
    roleId: ID!
    managerIds: [ID!]!
}
