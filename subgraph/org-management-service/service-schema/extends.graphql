type Company @key(fields: "id") @extends {
    id: ID @external
}

type Contract @key(fields: "id") @extends {
    id: ID @external
    reportsToManager: Manager       @authorize(company: ["view.company.contract.reports-to-manager"], member: ["view.member.contract.reports-to-manager"], operations: ["view.operations.contract.reports-to-manager"])        # The manager that this contract needs to reports to
    orgAttributes: OrgAttributes    @authorize(company: ["view.company.contract.org-attributes"], member: ["view.member.contract.org-attributes"], operations: ["view.operations.contract.org-attributes"])
}

type CompanyUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type Member @key(fields: "id") @extends {
    id: ID @external
}
