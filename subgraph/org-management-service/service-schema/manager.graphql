extend type Query {
    # Returns the complete organization directory.
    # NOTE:
    # - This returns [Contract] data type, which can cause unauthorized contract-related data access when used in member experience.
    # - Recommend to use orgDirectoryV2 instead.
    orgDirectory(filters: OrgDirectoryFilters!): OrgDirectory!  @authorize(company: ["view.company.orgdirectory"], operations: ["view.operations.orgdirectory"])

    # Recommend to use this new query instead of above "orgDirectory".
    orgDirectoryV2(filters: OrgDirectoryFilters!): OrgDirectoryV2!  @authorize(company: ["view.company.orgdirectory"], operations: ["view.operations.orgdirectory"], member: ["view.member.orgdirectory"])

    manager(filters: ManagerFilters!): Manager  @authorize(company: ["view.company.manager"], member: ["view.member"], operations: ["view.operations.manager"])
    managerDirectReportsRule(managerId:ID!): Rule    @authorize(company: ["view.company.manager-direct-reports-rule"], operations: ["view.operations.manager-direct-reports-rule"])  # Return given manager's rule that used during direct reports assignment.
}

extend type Mutation {
    managerCreate(input: ManagerCreateInput!): Manager  @authorize(company: ["create.company.manager"], operations: ["create.operations.manager"])
    managerDelete(input: ManagerDeleteInput!): Manager  @authorize(company: ["delete.company.manager"], operations: ["delete.operations.manager"])
    managerAssignDirectReports(input: ManagerAssignDirectReportsInput!): Manager    @authorize(company: ["assign.company.manager.directreport"], operations: ["assign.operations.manager.directreport"])
    managerValidateDirectReports(input: ManagerValidateDirectReportsInput!): DirectReportValidationResult   @authorize(company: ["validate.company.manager.directreport"], operations: ["validate.operations.manager.directreport"])

    # Assigns the given "reports to" manager to the given org-directory member (contract/manager).
    # Returns the updated org-directory member (contract/manager).
    orgDirectoryAssignReportsTo(input: OrgDirectoryAssignReportsToInput!): OrgDirectory     @authorize(company: ["assign.company.manager.reportsto"], operations: ["assign.operations.manager.reportsto"])
    orgDirectoryValidateReportsTo(input: OrgDirectoryValidateReportsToInput!): ReportsToValidationResult    @authorize(company: ["validate.company.manager.reportsto"], operations: ["validate.operations.manager.reportsto"])

    syncManagerHierarchyToLegacyApprovers(
        input: [SyncManagerHierarchyToLegacyApproversInput!]!
    ): TaskResponse @authorize(operations: ["sync.operations.manager.reportees"])

    # Deprecated mutations
    managerUpdate(input: ManagerUpdateInput!): Manager  @authorize(operations: ["create.operations.manager"]) @deprecated(reason: "Not required and no BE implementation as well")
}

input SyncManagerHierarchyToLegacyApproversInput {
    companyId: ID!
    approvalCategories: [ApprovalCategory!]!
}

input ManagerFilters {
    id: ID
    contractId: ID
    companyID: ID
}

input OrgDirectoryFilters {
    companyId: ID!
    departmentIds: [ID!]
    costCenterIds: [ID!]
    rule: RuleInput
    excludeTestContracts: Boolean = false   @deprecated(reason: "Now BE decide this and will only include test contracts for test company and non-test contracts for non-test company.")
    contractTypes: [ContractType!]
    contractStatuses: [ContractStatus!] # If not provided, ACTIVE statuses will be considered.
    includeNotStartedContracts: Boolean = false # If true, then contracts with future start dates also be included.
}

input ManagerCreateInput {
    companyId: ID!
    companyUserId: ID
    contractId: ID
    reportsToManagerId: ID
    orgAttributes: OrgAttributesInput
}

input OrgAttributesInput {
    departmentId: ID
    costCenterId: ID
}

input ManagerUpdateInput {
    id: ID!
    orgAttributes: OrgAttributesInput
}

input ManagerDeleteInput {
    id: ID!
    reason: String
}

input ManagerAssignDirectReportsInput {
    managerId: ID!
    directReportContractIds: [ID!]
    directReportManagerIds: [ID!]
    ruleInput: RuleInput
}

input ManagerValidateDirectReportsInput {
    managerId: ID!
    directReportContractIds: [ID!]
    directReportManagerIds: [ID!]
}

input OrgDirectoryAssignReportsToInput {
    # The "reports to" manager that need to be assigned to the given org-directory member (contract/manager).
    reportsToManagerId: ID!

    # Either contractId or managerId must be provided.
    contractId: ID
    managerId: ID
}

input OrgDirectoryValidateReportsToInput {
    reportsToManagerId: ID!

    # Either contractId or managerId MUST be provided.
    contractId: ID
    managerId: ID
}


type Manager {
    id: ID!
    company: Company
    companyUser: CompanyUser        @authorize(company: ["view.company.manager"], member: ["view.member"], operations: ["view.operations.manager"])         # The company user associated to this manager. If null, the manager's is still onboarding
    contract: Contract               # The contract associated to this manager (if any). If not null means this manager is also a member/contract.
    reportsToManager: Manager       @authorize(company: ["view.company.member-work-info.reports-to"], member: ["view.member"], operations: ["view.operations.company.members"]) # The primary manager that this manager needs to report to.
    directReports: [DirectReport!]  @authorize(company: ["view.company.member-work-info.direct-reports"], member: ["view.member"], operations: ["view.operations.company.members"])   # The contracts or subordinate-managers who are directly report to this manager.
    orgAttributes: OrgAttributes    @authorize(company: ["view.company.manager.org-attributes"], member: ["view.member"], operations: ["view.operations.manager.org-attributes"])     # Contains all the attributes that are specific to the organization/company.
    totalSubordinates: Int           # The total number of subordinates (direct and indirect) of this manager.
    orgContract: OrgContract         # Recommended to use this instead of "contract" field
}

# Represents a single user.
# This user can be a contract, a manager, or both (e.g. if contract is null, meaning not a contract/member)
type DirectReport {
    contract: Contract
    manager: Manager
}

# Represents the organization attributes/info of both managers & contracts.
type OrgAttributes {
    department: Department  @authorize(company: ["view.company.member-work-info.department"], member: ["view.member"], operations: ["view.operations.company.members"])    # The department this manager belongs to.
    costCenter: CostCenter      # The cost center this manager belongs to.
}

type OrgDirectory {
    contracts: [Contract!]
    managers: [Manager!]
    # ... any others in the future
}

type OrgDirectoryV2 {
    contracts: [OrgContract!]
    managers: [Manager!]
    # ... any others in the future
}

type OrgContract {
    id: ID
    type: ContractType
    country: CountryCode
    status: ContractStatus
    joiningDate: DateTime
    legalEntityId: ID
    member: Member
    position: String
    workEmail: String
    orgAttributes: OrgAttributes    @authorize(company: ["view.company.contract.org-attributes"], member: ["view.member.contract.org-attributes"], operations: ["view.operations.contract.org-attributes"])
    reportsToManager: Manager       @authorize(company: ["view.company.contract.reports-to-manager"], member: ["view.member.contract.reports-to-manager"], operations: ["view.operations.contract.reports-to-manager"])
}

type DirectReportValidationResult {
    successEntries: OrgDirectory
    failedEntries: OrgDirectory
}

type ReportsToValidationResult {
    success: Boolean!
    message: String
}
