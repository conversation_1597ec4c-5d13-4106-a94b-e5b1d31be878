
extend input FileGenerationRequestData {
    nachaDebitInstructionFileGenerateData: NachaDebitInstructionFileGenerateData
    nachaPrenoteInstructionFileGenerateData: NachaPrenoteInstructionFileGenerateData
}

input NachaDebitInstructionFileGenerateData {
    companyId: ID!
    invoiceIds: [ID]!
    expectedPayoutDate : Date!
    fileDataOverrideInfo : NachaFileDataOverride
    companyBankOverrideInfo: CompanyBankOverrideInfo
    invoiceOverrideInfo: [InvoiceOverrideInfo]
}

input NachaFileDataOverride {
    fileIdModifier: String
}

input NachaPrenoteInstructionFileGenerateData {
    companyId: ID!
    fileDataOverrideInfo : NachaFileDataOverride
    companyBankOverrideInfo: CompanyBankOverrideInfo
}

input InvoiceOverrideInfo {
    id: ID!
    value: String!
    reference: String!,
    dueDate: Date!
}

input CompanyBankOverrideInfo {
    routingNumber: String!
    bank: String!
    accountNumber: String!
    companyName: String!
    accountType: AccountType
}

enum AccountType{
    CHECKING,
    SAVINGS
}