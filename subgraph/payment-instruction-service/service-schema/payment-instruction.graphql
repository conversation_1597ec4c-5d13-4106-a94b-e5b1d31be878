extend input FileGenerationRequestData {
    paymentInstructionFileGenerateData: PaymentInstructionFileGenerateData
}

input PaymentInstructionFileGenerateData {
    contractIDs: [ID]!
    payrollDate: Date
    paymentExecutionDate: Date
    paymentFileSubType: String # NON_URGENT_PAYMENT, URGENT_PAYMENT, DEFAULT_PAYMENT, etc.
    overrideInfo: OverrideInfo
}

input PaymentInstructionFileGenerateInput {
    contractIDs: [ID]!
    countryCode: CountryCode!
    bank: BankCode!
    payrollDate: Date
    paymentExecutionDate: Date
    overrideInfo: OverrideInfo
}

input OverrideInfo {
    payrollOverride: [Override] # contractID:payrollAmount
}

input Override {
    key: String
    value: String
}







