extend type Mutation {
    PaymentInstructionFileGenerate(data: PaymentInstructionFileGenerateInput!) : TaskResponse @authorize(operations: ["generate.operations.pif"])
    PaymentInstructionFileGenerateV2(data: FileGenerationRequestData!) : TaskResponse @authorize(operations: ["generate.operations.pif"])
}

extend type Query {
    GetPaymentInstructionFile(paymentInstructionId: ID!) : DocumentReadable @deprecated(reason: "Use PaymentInstructionFile") @authorize(operations: ["view.operations.pif"])
    GetPaymentInstructionDocument(paymentInstructionId: ID!) : Document @authorize(operations: ["view.operations.pif"])
    GetPaymentInstructionDocumentV2(paymentInstructionId: ID!) : PaymentInstructionFileGenerationReport @authorize(operations: ["view.operations.pif"])
}

input FileGenerationRequestData {
    bankCode: BankCode!
    countryCode: CountryCode!
    fileType: PaymentInstructionFileType!
    paymentFileSubType: String! # NON_URGENT_PAYMENT, URGENT_PAYMENT, DEFAULT_PAYMENT, etc.
}

type PaymentInstructionFileGenerationReport {
    pifId: ID!
    file: Document
    status: PaymentInstructionStatus!
    errors: [FileGenerationErrors]
}

type FileGenerationErrors {
    type: PaymentInstructionErrorType
    level: PaymentInstructionErrorLevel
    identifier: String
    errorMessage: String
}

enum PaymentInstructionFileType {
    ISO20022XML,
    NACHA_PRENOTE,
    NACHA_DEBIT,
    ISO20022XML_DIRECT_DEBIT
}

enum BankCode {
    HSBC
    CITI
}

enum PaymentInstructionStatus {
    FILE_GENERATED
    FILE_GENERATION_IN_PROGRESS
    FILE_GENERATION_FAILED
}

enum PaymentInstructionErrorType {
    VALIDATION_ERROR
    INTERNAL_ERROR
}

enum PaymentInstructionErrorLevel {
    INFO
    WARNING
    ERROR
}
