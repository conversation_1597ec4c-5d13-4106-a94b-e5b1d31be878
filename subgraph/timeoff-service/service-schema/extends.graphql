input CountryFilter {
    code: CountryCode
    stateCode: String
}

input TimeOffRequirementFilters {
    contractType: ContractType,
    term: ContractTerm,
    contractStatus: ContractStatus,
    country: CountryFilter,
    companyId: ID
}

type Company @key(fields: "id") @extends {
    id: ID @external
    timeOffs(id: ID, fromDate: DateTime, toDate: DateTime): [TimeOff] @authorize(company: ["view.company.time-off"], operations:["view.operations.timeoffs"])
    timeOffTypeInfos(filter : TimeOffTypeFilter): [TimeOffTypeInfo] @authorize(company: ["view.company.timeoff.types"])

    # timeOffPolicy refers to the existing TimeOffTypeDefinition
    # if filter is null or all fields are null/empty return all policies for company
    timeOffPolicies(filter : CompanyTimeOffPolicyFilter) : [TimeOffTypeDefinition] @authorize(company: ["view.company.timeoff.policies"])


    # ----------------------------------------------------
    # Deprecated Queries
    # ----------------------------------------------------
    timeOffReport(input: TimeOffReportInput!): DocumentReadable @authorize(operations:["view.operations.timeoffs"]) @deprecated(reason: "Timeoff reports are supported via new report module. Hence service level reports are deprecated")
    timeOffBalanceReport: DocumentReadable @authorize(operations:["view.operations.timeoffs"]) @deprecated(reason: "Timeoff reports are supported via new report module. Hence service level reports are deprecated")
    timeOffRequirements(filters : TimeOffRequirementFilters): TimeOffRequirements @authorize(operations:["view.operations.timeoffs"]) @deprecated(reason: "Use timeOffPolicies instead")


}

type Contract @key(fields: "id") @extends {
    id: ID @external
    timeOff(id: ID, fromDate: DateTime, toDate: DateTime): ContractTimeOff @authorize(company: ["view.company.time-off"], member:["view.member.time-off"])
    timeoffSummaryInfo(filter : TimeOffSummaryInfoFilter) : [TimeOffSummaryInfo!] @authorize(company: ["view.company.time-off"], member: ["view.member.time-off"])



    # ----------------------------------------------------
    # Deprecated Queries
    # ----------------------------------------------------
    timeOffs(filters: TimeOffFilter): [TimeOff] @authorize(operations:["view.operations.timeoffs"]) @deprecated(reason: "Use contract.timeOff.timeoffs instead")
}

type State @key(fields: "code") @extends {
    code: String @external
}

type CountryCompliance @key(fields: "countryCode countryState { code }") @extends {
    countryCode: CountryCode @external
    countryState: State @external
    timeOffRequirements(contractType: ContractType, term: ContractTerm, contractStatus: ContractStatus, filters : TimeOffRequirementFilters): TimeOffRequirements
}

interface Compliance @key(fields: "contract { id }") @extends {
    contract: Contract @external
    timeOffEntitlement: [ContractTimeOffEntitlement] @authorize(company: ["view.company.contract.compliance"], member:["view.member.contract.compliance"], operations: ["view.operations.contract.compliance"])
}

type ComplianceMultiplierEOR implements Compliance @key(fields: "contract { id }") @extends {
    contract: Contract @external
    timeOffEntitlement: [ContractTimeOffEntitlement] @authorize(company: ["view.company.contract.compliance"], member:["view.member.contract.compliance"], operations: ["view.operations.contract.compliance"])
}

type CompliancePartnerEOR implements Compliance @key(fields: "contract { id }") @extends {
    contract: Contract @external
    timeOffEntitlement: [ContractTimeOffEntitlement] @authorize(company: ["view.company.contract.compliance"], member:["view.member.contract.compliance"], operations: ["view.operations.contract.compliance"])
}

type CompliancePEO implements Compliance @key(fields: "contract { id }") @extends {
    contract: Contract @external
    timeOffEntitlement: [ContractTimeOffEntitlement] @authorize(company: ["view.company.contract.compliance"], member:["view.member.contract.compliance"], operations: ["view.operations.contract.compliance"])
}

type ComplianceFreelance implements Compliance @key(fields: "contract { id }") @extends {
    contract: Contract @external
    timeOffEntitlement: [ContractTimeOffEntitlement] @authorize(company: ["view.company.contract.compliance"], member:["view.member.contract.compliance"], operations: ["view.operations.contract.compliance"])
}

type ComplianceContractor implements Compliance @key(fields: "contract { id }") @extends {
    contract: Contract @external
    timeOffEntitlement: [ContractTimeOffEntitlement] @authorize(company: ["view.company.contract.compliance"], member:["view.member.contract.compliance"], operations: ["view.operations.contract.compliance"])
}

extend type Query {
    getTimeOffsForPayroll(input: PayrollTimeOffFilter!): [TimeOff] @authorize(operations: ["view.operations.timeoffs"]) @deprecated(reason: "Payroll service will get timeoffs for payroll via gRPC. Hence not exposing via the timeoff graph")
}
