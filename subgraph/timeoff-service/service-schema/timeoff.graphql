extend type Mutation {
    """
    # i.e. submit (send for approval). The timeoff can be a new one or existing DRAFT one, based on `input.id`. Refer to input TimeOffCreateInput.
    # Note: FE is sending `contractId: ID` even in "member" exp (personal view) which is a security issue: MemberA can raise a timeoff for memberB by mistake or intention!.
    # TODO: 1. FE: not to pass `contractId` if in member exp. 2. BE: will ignore `contractId` if in member exp, and query the contract from `currentUser` instead.
    # In company exp (team view): `contractId` is still used as usual, to indicate whom the companyUser is raising on behalf of.
    """
    timeOffCreateV2(input: TimeOffCreateInputV2!): TimeOff @authorize(company: ["create.company.time-off"], member: ["create.member.timeoffs"])

    """to create a (new) timeoff with status=DRAFT. If you want to update an existing DRAFT timeoff, use `timeOffUpdate`, not this"""
    timeOffSaveAsDraftV2(input: TimeOffSaveAsDraftInputV2!): TimeOff @authorize(company: ["create.company.time-off"], member: ["create.member.timeoffs"])

    """to set the status back to DRAFT."""
    timeOffRevoke(id: ID!): TimeOff @authorize(company: ["revoke.company.time-off"], member: ["revoke.member.timeoffs"])

    timeOffDelete(id: ID!): TaskResponse @authorize(company: ["delete.company.time-off"], member: ["delete.member.timeoffs"])                  # Delete. Can OPS, Owner

    triggerLegacyTimeoffReallocation: TaskResponse @authorize(operations:["timeoff.operations.reallocate"]) # trigger the reallocation
    triggerTimeoffAllocation(input: TimeOffAllocationInput): TaskResponse @authorize(operations:["timeoff.operations.allocate"]) # trigger the reallocation
    triggerTimeoffCarryForward(input: TimeOffAllocationInput): TaskResponse @authorize(operations:["timeoff.operations.carry-forward"])
    triggerCarryForwardExpiration(input: TimeOffAllocationInput): TaskResponse @authorize(operations:["timeoff.operations.carry-forward"])
    backfillTimeoffAllocationRecords(input: TimeOffAllocationInput): TaskResponse @authorize(operations:["timeoff.operations.allocate"])
    backfillTimeoffCarryForwardRecords(input: TimeOffAllocationInput): TaskResponse @authorize(operations:["timeoff.operations.carry-forward"])
    """ temporary mutation to fix non-prorated timeoff allocation """
    fixTimeoffAllocations(input: FixTimeoffAllocationInput): TaskResponse @authorize(operations:["timeoff.operations.fix-allocation"])
    """to update end_date/end_session based on back_to_work_date/back_to_work_session"""

    timeOffUpdateCountryDefinitions(file: Upload!): TaskResponse @authorize(operations:["update.operations.timeoff-definition"])

    backfillTimeoffApprovedOn(ids: [ID!]): TaskResponse @authorize(operations:["update.operations.timeoffs"]) # if ids null, then backfill all
    fixEntitlementChangeRecords(contractIds: [ID!]): TaskResponse @authorize(operations:["update.operations.entitlement-change-records"]) # if contractIds is null/empty, nothing will happen. Details: https://app.clickup.com/t/865cqd1v6

    """
    Typical use case: to move a timeoff to weekend/holiday as some contracts still work on those days. Doesn't change noOfDays, sessions or anything else.
    To prevent complicated bugs related to timeoff summary/cycle, there are following rules you should ask the user to follow first:
    - timeoff must be first submitted by the user (status = APPROVAL_IN_PROGRESS/APPROVED/TAKEN)
    - can only change to a date range within the months of the old date range. E.g.:
    - if old date range is Jul N1 - Jul N2: new date range must be Jul N3 - Jul N4
    - if old date range is Jul N1 - Aug N2: new date range must be Jul N3 - Jul 4, or Jul N3 - Aug N4, or Aug N3 - Aug N4
    If you're sure above validations are not relevant in your case (summary cycle is in another month etc...), pass ignoresValidations=true to "sudo" it (be careful!).
    If you're not sure, please @Thang @Max for more details
    """
    changeTimeOffDateV2(input: TimeOffChangeDateInput!): TaskResponse @authorize(operations:["update.operations.timeoff-date"])

    """Deprecated: Use changeTimeOffDateV2 instead"""
    changeTimeOffDate(id: ID!, startDate: Date!, endDate: Date!, ignoresValidations: Boolean = false): TaskResponse @authorize(operations:["update.operations.timeoff-date"]) @deprecated(reason: "Use changeTimeOffDateV2 instead")

    timeOffTypeCreate(input: TimeOffTypeCreateInput!): TimeOffTypeInfo @authorize(company: ["create.company.time-off-type"])
    timeOffTypeUpdate(input: TimeOffTypeUpdateInput!): TimeOffTypeInfo @authorize(company: ["update.company.time-off-type"])
    timeOffTypeDelete(id: ID!): TimeOffTypeInfo @authorize(company: ["delete.company.time-off-type"])

    timeOffPolicyCreate(input : TimeOffPolicyCreateInput!) : TimeOffTypeDefinition @authorize(company: ["create.company.time-off-policy"])
    timeOffPolicyDelete(policyId : ID!) : TimeOffTypeDefinition @authorize(company: ["delete.company.time-off-policy"], operations:["delete.operations.time-off-policy"])
    timeOffPolicyUpdate(input : TimeOffPolicyUpdateInput!) : TimeOffTypeDefinition @authorize(company: ["update.company.time-off-policy"])
    timeOffPolicyAssignUsers(input: TimeOffPolicyAssignUsersInput!): TimeOffTypeDefinition @authorize(company: ["assign.company.time-off-policy"])
    timeOffPolicyValidateUsers(input: TimeOffPolicyValidateUsersInput!): TimeOffPolicyUsersValidationResult @authorize(company: ["validate.company.time-off-policy"])

    backfillEntitlementDefinitionIds(companyId: ID) : TaskResponse @authorize(operations:["update.operations.entitlements"]) # if companyId = null, backfill for all data
    timeOffBulkRevoke(ids: [ID!]!): TaskResponse @authorize(operations:["update.operations.timeoffs"]) # Ops only
    migrateCompanyPoliciesToEntityPolicies(companyIds : [ID!], forAll: Boolean = false) : TaskResponse @authorize(operations:["update.operations.entitlements"]) # if `forAll` is true and `companyIds` are null, migrate all company definitions for all companies
    backfillTimeoffSummaryStatus(companyIds : [ID!], forAll: Boolean = false) : TaskResponse @authorize(operations:["update.operations.entitlements"]) # if `forAll` is true and `companyIds` are null, migrate all company definitions for all companies
    backfillTimeoffUsageAndSummaryCounts(companyIds : [ID!], forAll: Boolean = false) : TaskResponse @authorize(operations:["update.operations.entitlements"]) # if `forAll` is true and `companyIds` are null, migrate all company definitions for all companies
    backfillTimeoffEntries(companyIds : [ID!], forAll: Boolean = false) : TaskResponse @authorize(operations:["update.operations.entitlements"]) # if `forAll` is true and `companyIds` are null, migrate all company definitions for all companies
    updateTimeOffSummary(input: [UpdateTimeOffSummaryInput!]!): TaskResponse @authorize(operations:["update.operations.timeoffs"])
    timeOffCreateV2Ops(input: TimeOffCreateInputOps!): TaskResponse @authorize(operations:["create.operations.timeoff"])


    # -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    # Deprecated Mutations
    # -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
    timeOffCreate(contractId: ID, input: TimeOffCreateInput!): TimeOff @authorize(company: ["create.company.time-off"], member: ["create.member.timeoffs"]) @deprecated(reason: "use timeOffCreateV2 instead")
    timeOffSaveAsDraft(input: TimeOffSaveAsDraftInput!): TimeOff @authorize(company: ["create.company.time-off"], member: ["create.member.timeoffs"]) @deprecated(reason: "use timeOffSaveAsDraftV2 instead")
    """Update. Owner who created it. Does not change status"""
    timeOffUpdate(id: ID!, input: TimeOffUpdateInput!): TimeOff  @authorize(company: ["update.company.time-off"], member: ["update.member.timeoffs"]) @deprecated(reason: "Update is possible only for draft timeoffs, use timeOffSaveAsDraftV2 instead")

    migrateFromBackToWorkDateToEndDate: TaskResponse @authorize(operations:["update.operations.timeoffs"]) @deprecated(reason: "This was designed for one time use only.")
    # if `forAll` is true and `companyId` is null, back fill rules for all company definitions
    backfillRulesForExistingCompanyDefinitions(companyId: ID, forAll: Boolean = false) : TaskResponse  @authorize(operations:["update.operations.timeoffs"]) @deprecated(reason: "This was designed for one time use only.")
    updateAndFixTimeOffs(input: UpdateAndFixTimeOffsInput): TaskResponse @authorize(operations:["update.operations.timeoffs"]) @deprecated(reason: "There is no implementation in back-end. Hence deprecating this for further use")
    timeOffUpdateCompanyDefinitions(file: Upload!, companyId: ID!): TaskResponse @authorize(operations:["update.operations.timeoffs"]) @deprecated(reason: "Company can create company policies from UI. Hence deprecating this ops mutation further use")

}

extend type Query {
    timeOffs(filter: TimeOffFilter, pageRequest: PageRequest): TimeOffsResponse @authorize(operations:["view.operations.timeoffs"])
    timeOffSummaries(filter: TimeOffSummaryFilter, pageRequest: PageRequest): TimeOffSummariesResponse @authorize(operations:["view.operations.timeoffs"])

    # todo: Once TimeOffTypeDefinition graph move to root -> make this query a child fetcher of policy query
    timeOffPolicyAssignmentDetails(policyId : ID!) : TimeOffPolicyAssignmentDetailsResult @authorize(company: ["details.company.time-off-policy"])
    timeOffContractRequirements(contractId: ID!): TimeOffContractRequirements @authorize(company: ["view.company.contract.compliance"], member: ["view.member.contract.compliance"], operations: ["view.operations.contract.compliance"])

    # Only for OPs platform usage.
    timeOffPolicies(filter : TimeOffPolicyFilter) : [TimeOffTypeDefinition] @authorize(operations: ["view.operations.timeoff.policies"])


    timeOffBalanceEncashments(input: TimeOffEncashmentInput) : [TimeOffBalanceEncashment] @authorize(company: ["view.company.timeoffs.leave-encashment"], member: ["view.member.timeoffs.leave-encashment"], operations: ["view.operations.timeoffs.leave-encashment"])

    timeOffCalendar(filter : TimeOffCalendarFilter) : TimeOffCalendar @authorize(company: ["view.company.timeoff.calendar"], member: ["view.member.timeoff.calendar"])
    timeOffSummaryBreakdown(input : TimeOffSummaryBreakdownInput!) : [TimeOffSummaryBreakdown!]! @authorize(company: ["view.company.timeoff.summary-break-down"], member: ["view.member.timeoff.summary-break-down"])

    timeOffBreakdown(input : TimeOffBreakdownInput!) : [TimeOffBreakdown!]! @authorize(company: ["view.company.timeoff.breakdown"], member: ["view.member.timeoff.breakdown"])
}

input UpdateTimeOffSummaryInput {
    summaryId: ID!
    typeId: ID!
    contractId: ID!

    # If any of below fields are not given. we are not updating those fields. Caller can set only the fields he wants to update
    allocatedDaysCount: Float
    takenCount: Float
    pendingDaysCount: Float
    entitledDaysCount: Float
    carriedDaysCount: Float
}

type TimeOffBalanceEncashment {
    type : TimeOffTypeInfo

    totalProratedAllocatedCount : Float
    totalCarryForwardCount : Float
    totalTakenCount : Float
    encashableBalance : Float
}

type TimeOffCalendar {
    startDate : Date,
    endDate : Date,
    events : [TimeOffCalendarEvent!]!
}

type TimeOffCalendarEvent {
    date : Date,
    description : String
    session : TimeOffSession
    type : TimeOffCalendarEventType
}

type TimeOffSummaryBreakdown {
    summaryStartDate : Date
    summaryEndDate : Date
    entitledDays : Float
    applyingDays : Float
    balance : TimeOffBalance
    usedDays : TimeOffUsedDays
    isCurrentSummary : Boolean
    errors : [String!]!
}

type TimeOffBalance {
    totalRemainingDays : Float      # total balance remaining
    carryForwardDays : Float        # total balance that can be carried to next cycle
    carryForwardExpiryDate : Date   # date when carry forward balance expires
}

type TimeOffUsedDays {
    total: Float                  # Total days of time off used
    allocated: Float              # Days used from the allocated time-off balance
    lapsable: Float               # Days used from lapsable (non-carry forward) time-off balance
    carryForward: Float           # Days used from the balance carried forward from the previous period
    nextCycleCarryForward: Float  # Days used from the balance eligible to be carried forward to the next/future period
}

input TimeOffChangeDateInput {
    id: ID!,
    startDate: Date!,
    endDate: Date!,
    noOfDays: Float,
    ignoresValidations: Boolean = false
}

input TimeOffSummaryBreakdownInput {
    contractId : ID!
    typeId : ID!
    timeOffStartDate : TimeOffDateInput
    timeOffEndDate : TimeOffDateInput
}

input TimeOffCalendarFilter {
    contractId : ID!
    startDate : Date # If startDate is null, it will use the current leave cycle start date
    endDate : Date   # If endDate is null, it will use the furthest leave cycle end date
}

input TimeOffEncashmentInput {
    contractId : ID!
    lastWorkingDate : Date!
}

input TimeOffPolicyFilter {
    entityFilters : [EntityFilter!]
}

input EntityFilter {
    id : ID,
    type : EntityType
}

input UpdateAndFixTimeOffsInput {
    companyIds: [ID!]
    contractIds: [ID!]
    countryCodes: [CountryCode!]
    statuses: [ContractStatus!]
    isTestContracts: Boolean!

    shouldCreateRequiredEntitlementIfMissing: Boolean
    shouldCreateNonRequiredEntitlementIfMissing: Boolean
    shouldSetComplianceLeavePolicyToCreatedDefaultEntitlement: Boolean
    shouldSetComplianceLeavePolicyToUpdatedDefaultEntitlement: Boolean
    shouldCreateUpdateAllocationSummary: Boolean
}

input FixTimeoffAllocationInput {
    timeoffSummaryIds: [ID!]
    dryRun: Boolean = true
}

input ContractUpdateTimeOffEntitlementsInput {
    key: String
    value: Float
    unit: TimeOffUnit
}

input TimeOffReportInput {
    startDate: DateTime!
    endDate: DateTime!
}

input TimeOffSaveAsDraftInput {
    """contractId is optional. a) null -> this is a member. Always leave it null in member exp!; b) 123 -> this is a companyUser saving as draft a timeoff on behalf of contractId=123"""
    contractId: ID
    type: String!
    noOfDays: Float!
    description: String
    startDate: TimeOffDateInput!
    """deprecated, to be removed once FE completely migrates to `endDate`"""
    backToWorkDate: TimeOffDateInput
    """to replace `backToWorkDate` - https://app.clickup.com/t/2kchkk4"""
    endDate: TimeOffDateInput
}

input TimeOffSaveAsDraftInputV2 {
    timeOffId : ID  # If id is given and timeoff is in Draft state, we update the data in draft timeoff
    startDate : TimeOffDateInput!
    endDate : TimeOffDateInput!
    contractId : ID   # contract id should be passed only in company exp, BE will ignore this field in member exp
    typeId : ID!
    description: String
}

input TimeOffCreateInput {
    """id is optional. a) id=null -> submitting a new time off. b) id=123 -> submitting an existing DRAFT time off with id=123"""
    id: ID
    type: String!
    """deprecated, don't use isDraft! The mutation will ignore isDraft and always submit for approval"""
    isDraft: Boolean = false
    noOfDays: Float!
    description: String
    startDate: TimeOffDateInput!
    """deprecated, to be removed once FE completely migrates to `endDate`"""
    backToWorkDate: TimeOffDateInput
    """to replace `backToWorkDate` - https://app.clickup.com/t/2kchkk4"""
    endDate: TimeOffDateInput
}

input TimeOffCreateInputV2 {
    timeOffId : ID  # If id is given, get the DRAFT timeoff and submit it
    startDate : TimeOffDateInput!
    endDate : TimeOffDateInput!
    contractId : ID # contract id should be passed only in company exp, BE will ignore this field in member exp
    typeId : ID!
    description: String
}

input TimeOffCreateInputOps {
    companyId: ID!
    timeoffCreateInputs: [TimeOffCreateInputV2!]!
}

input TimeOffUpdateInput {
    type: String!
    """deprecated, don't use isDraft! BE doesn't change the timeoff status in `timeOffUpdate`"""
    isDraft: Boolean = false
    noOfDays: Float!
    description: String
    startDate: TimeOffDateInput!
    """deprecated, to be removed once FE completely migrates to `endDate`"""
    backToWorkDate: TimeOffDateInput
    """to replace `backToWorkDate` - https://app.clickup.com/t/2kchkk4"""
    endDate: TimeOffDateInput
}

input TimeOffDateInput {
    """deprecated. All time values are always 00:00:00. Use `dateOnly` instead"""
    date: DateTime
    dateOnly: Date
    session: TimeOffSession
}

input TimeOffAllocationInput {
    expiryDate: Date
    companyIds: [ID!]
    excludedContractIds: [ID!]
}

input TimeOffTypeFilter {
    ids: [ID!]              # timeoff_type.id
    isPaidLeave: Boolean
}

input TimeOffTypeCreateInput {
    name: String!  # mapped to timeoff_type.label
    isPaidLeave: Boolean!
    description: String
}

input TimeOffTypeUpdateInput {
    typeId: ID!     # mapped to timeoff_type.id
    name: String!   # mapped to timeoff_type.label
    isPaidLeave: Boolean!
    description: String
}

input TimeOffPolicyCreateInput {
    timeOffTypeId : ID!
    policyName : String!
    allocatedCount : TimeOffDurationInput! # mapped to definition.validation.default_value/unit
    carryForwardConfig : CarryForwardConfigInput!
    isUnlimitedLeavesAllowed : Boolean
    timeoffPolicyEntityInput : TimeOffPolicyEntityInput # todo : make this field mandatory once FE changes are done
    futureLeaveConfig : FutureLeaveConfigInput
}

input FutureLeaveConfigInput {
    enabled: Boolean
    futureYearsAllowed : Int = 1 # default value is 1
    lapsableLeaveConfig : LapsableLeaveConfigInput
}

input LapsableLeaveConfigInput {
    enabled: Boolean = false
    expiry : TimeOffDurationInput # default expiry is 1 year
}

input TimeOffDurationInput {
    value: Float
    unit: TimeOffUnit
}

input CarryForwardConfigInput {
    enabled: Boolean
    maxLimit: TimeOffDurationInput
    expiryConfig: CarryForwardExpiryInput
}

input CarryForwardExpiryInput {
    enabled: Boolean
    expiry: TimeOffDurationInput
}

input CompanyTimeOffPolicyFilter {
    timeOffPolicyIds : [ID!]
    timeOffTypeIds : [ID!]
    entityIds : [ID!]
}

input TimeOffPolicyAssignUsersInput {
    timeOffTypeId: ID!
    timeOffPolicyId: ID!
    timeOffPolicyRule: RuleInput  # rule to save in db against this timeoff_policy_id
    contractIds: [ID!]!
}


input TimeOffPolicyValidateUsersInput {
    timeOffTypeId: ID!
    timeOffPolicyId: ID!
    contractIds: [ID!]!
}

input TimeOffPolicyUpdateInput {
    id : ID!                                         # definition.id
    policyName : String!
    allocatedCount : TimeOffDurationInput!           # mapped to definition.validation.deafult_value/unit
    carryForwardConfig : CarryForwardConfigInput     # definitions.carryForwardConfig
    isUnlimitedLeavesAllowed : Boolean
}

input TimeOffPolicyEntityInput {
    type : EntityType,
    id : ID
}

input TimeOffSummaryInfoFilter {
    statuses : [TimeOffSummaryStatus!]!
}

type ContractTimeOff {
    timeOffs: [TimeOff]
    summary(period: String, type: String): [TimeOffType] @deprecated(reason: "Use contract.timeoffSummaries instead")
}

type TimeOff @key(fields: "id") {
    id: ID!
    contract: Contract
    type: TimeOffType
    status: TimeOffStatus
    startDate: TimeOffDate
    backToWorkDate: TimeOffDate @deprecated(reason: "use endDate instead. https://app.clickup.com/t/2kchkk4")
    endDate: TimeOffDate
    noOfDays: Float
    description: String
    changeReason: String
    isActionable: Boolean
    approvers: [Person]
    timeoffEntries: [TimeOffEntry]
    createdOn: DateTime
    updatedOn: DateTime
    createdBy: ID # this is user_id, not member.id/company_user.id. Can be null
    updatedBy: ID # this is user_id, not member.id/company_user.id. Can be null or -1 (async related)
}

type TimeOffDate {
    date: DateTime @deprecated(reason: "All time values are always 00:00:00. Use `dateOnly` instead")
    session: TimeOffSession
    dateOnly: Date
}

enum TimeOffSession {
    MORNING
    AFTERNOON
    FULL_DAY
}

type TimeOffSummaryInfo {
    id: ID,
    typeId: ID,
    typeName: String,
    periodStart: Date,
    periodEnd: Date,
    status: TimeOffSummaryStatus,

    entitledCount: Float,

    proratedEntitledCount: Float,
    carryForwardCount: Float,
    carryForwardExpiredCount: Float,

    pendingCount: Float,
    takenCount: Float,

    usedDays: TimeOffUsedDays,
    balance: TimeOffBalance
}

# TODO: rename to TimeOffTypeSummary (or just TimeOffSummary to match JPA/DB names?)
type TimeOffType {
    key: String
    typeId : ID   # timeoff_type.id
    contract: Contract # requires @DgsEntityFetcher(name = "Contract") in core-service
    definition: TimeOffTypeDefinition
    periodStart: DateTime                   # The period for which this count is
    periodEnd: DateTime                    # The period for which this count is
    entitled: Float
    allocated: Float
    carried: Float
    pending: Float
    taken: Float                           # Count taken so far for this type within this period.
    remaining: Float
    status: TimeOffSummaryStatus
    isPaidLeave: Boolean
}

# represents `timeoff_type` table
type TimeOffTypeInfo {
    type: String    # mapped to timeoff_type.key
    typeId: ID      # mapped to timeoff_type.id
    label: String
    description: String
    usage: Int @authorize(company: ["view.company.timeoff.types"])
    status: TimeOffTypeStatus
    isPaidLeave: Boolean
    companyId : ID
    createdOn: DateTime
    updatedOn: DateTime
}

type ContractTimeOffEntitlement {
    type: String @deprecated(reason: "use timeOffType instead")
    definition: TimeOffTypeDefinition
    value: Float # Allow fractional
    unit: TimeOffUnit
    timeOffType: TimeOffTypeInfo
    isMandatory: Boolean
}

input PayrollTimeOffFilter {
    contractIds: [ID]!
    timeOffStatus: [TimeOffStatus]!
    timeOffReportInput: TimeOffReportInput!
}

input TimeOffFilter {
    ids: [ID!]
    contractIds: [ID!]
    timeOffStatus: [TimeOffStatus!]
    contractCountry: CountryCode
    contractStatus: ContractStatus
    startDateRange: DateRange
    types: [String!]
}

input TimeOffSummaryFilter {
    contractIds: [ID!]
    contractCountry: CountryCode
    contractStatus: ContractStatus
    contractType: ContractType
    status: TimeOffSummaryStatus
}

type TimeOffsResponse {
    page: PageResult!
    timeOffs: [TimeOff!]
    allCount: Int!
    pendingCount: Int!
    approvedCount: Int!
}

type TimeOffSummariesResponse {
    page: PageResult!
    timeOffSummaries: [TimeOffType!]
}

type TimeOffPolicyUsersValidationResult {
    validContractsToAssign: [Contract!]!
    invalidContractsToAssign: [Contract!]!
    invalidContractsToUnassigned: [Contract!]!
}

type TimeOffPolicyAssignmentDetailsResult {
    assignedContracts: [Contract!]
    rule: Rule
}

type TimeOffContractRequirements {
    assignedEntitlements: [ContractTimeOffEntitlement] # already assigned entitlements to the contract
    availableEntitlements: [ContractTimeOffEntitlement] # all available entitlements for the contract
    clause: String # A string describe country timeoff requirements
}

input TimeOffBreakdownInput {
    id: ID
    contractId : ID
    startDate : TimeOffDateInput
    endDate : TimeOffDateInput
}

type TimeOffBreakdown {
    startDate : Date           # 2025-06-01
    endDate : Date             # 2025-06-05
    noOfDays: Float            # 4.0
    timeOffEntries: [TimeOffEntry]
}

type TimeOffEntry {
    date : Date                  # 2025-06-01
    session : TimeOffSession     # MORNING, AFTERNOON, FULL_DAY
    type : TimeOffEntryType      # HOLIDAY, RESTDAY, TIMEOFF, EXISTING_TIMEOFF
    value : Float                # 0.5 for half day, 1 for full day
}

enum TimeOffTypeStatus {
    ACTIVE
    DELETED
}

enum TimeOffCalendarEventType {
    HOLIDAY
    TIMEOFF
    RESTDAY
}

enum TimeOffEntryType {
    HOLIDAY
    RESTDAY
    TIMEOFF
    EXISTING_TIMEOFF
}

enum TimeOffSummaryStatus {
    ACTIVE
    EXPIRED
    UPCOMING
}
