extend type Query {
    # Test query that returns a simple health check response
    healthCheck: HealthCheckResponse!

    # Get test table record by ID
    testTableById(id: ID!): TestTableResponse

    # Get all test table records
    allTestTables: [TestTableResponse!]!
}

extend type Mutation {
    createTestTableEntry: TestTableResponse!
}

type HealthCheckResponse {
    status: String!
    message: String!
    timestamp: String!
}

type TestTableResponse {
    id: ID!
    createdOn: String!
}
