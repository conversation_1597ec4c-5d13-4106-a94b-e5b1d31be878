extend type Query {
    leaveCompliance(countryCode: String!): CountryLeaveCompliance @authorize(company: ["view.company.leave.annual-leave"], operations: ["view.operations.leave.annual-leave"])
    holidayCompliances(countryCodes: [CountryCode!]!, types: [HolidayType!]!, year: Int!): [HolidayCompliance!]! @authorize(company: ["view.company.leave.holiday"], operations: ["view.operations.leave.holiday"])
}

extend type Mutation {
    upsertLeaveCompliance(leaveCompliance: CountryLeaveComplianceRequest!): CountryLeaveCompliance @authorize(operations: ["update.operations.leave.annual-leave"])
    upsertHolidayCompliance(holidayCompliance: HolidayComplianceRequest!): HolidayCompliance! @authorize(operations: ["update.operations.leave.holiday"])
}

type CountryLeaveCompliance {
    countryCode: String!,
    state: String,
    annualLeave: AnnualLeaveCompliance
    sickLeave: SickLeaveCompliance
    maternityLeave: MaternityLeaveCompliance
}

type AnnualLeaveCompliance {
    preReq: ComplianceDetail,
    entitlement: ComplianceDetail,
    accrual: ComplianceDetail,
    carryForward: ComplianceDetail,
    encashment: ComplianceDetail,
    summary: JSON
}

type SickLeaveCompliance {
    entitlement: ComplianceDetail,
    summary: JSON
}

type MaternityLeaveCompliance {
    entitlement: ComplianceDetail,
    summary: JSON
}

type ComplianceDetail {
    models: [ModelConfig]
}

type ModelConfig {
    name: String,
    configs: [JSON]
}


input CountryLeaveComplianceRequest {
    countryCode: String!,
    state: String,
    annualLeave: AnnualLeaveComplianceRequest
    sickLeave: SickLeaveComplianceRequest
    maternityLeave: MaternityLeaveComplianceRequest
}

input AnnualLeaveComplianceRequest {
    preReq: ComplianceDetailRequest,
    entitlement: ComplianceDetailRequest,
    accrual: ComplianceDetailRequest,
    carryForward: ComplianceDetailRequest,
    encashment: ComplianceDetailRequest,
    summary: JSON
}

input SickLeaveComplianceRequest {
    entitlement: ComplianceDetailRequest,
    summary: JSON
}

input MaternityLeaveComplianceRequest {
    entitlement: ComplianceDetailRequest,
    summary: JSON
}

input ComplianceDetailRequest {
    models: [ModelRequest!]!
}

input ModelRequest {
    name: String!,
    configs: [JSON!]!
}

input HolidayComplianceRequest {
    countryCode: CountryCode!,
    year: Int!,
    gregorianModels: [GregorianHolidayModelRequest!]
}

input GregorianHolidayModelRequest {
    type: HolidayType!
    configs: [GregorianCalendarConfigRequest!]!
}

input GregorianCalendarConfigRequest {
    name: String!
    date: Int!
    month: Int!
    mandatory: Boolean!
    isSubjectToChange: Boolean!
    specificRegions: [String!]
    note: String
}

type HolidayCompliance {
    countryCode: CountryCode!
    models: [HolidayModel!]!
}

type HolidayModel {
    name: HolidayModelName!
    type: HolidayType!
    configs: [HolidayConfig!]!
}

union HolidayConfig = GregorianCalendarConfig

type GregorianCalendarConfig {
    name: String!
    date: Int!
    month: Int!
    year: Int!
    mandatory: Boolean!
    isSubjectToChange: Boolean!
    specificRegions: [String!]
    note: String
}

enum HolidayType {
    NATIONAL
    BANK
    SPECIAL
}

enum HolidayModelName {
    GregorianCalendarModel
}
