type Company @key(fields: "id") @extends {
    id: ID @external
    equipments: [Equipment!] @authorize(company: ["view.company.equipment.equipments"])
}

type Contract @key(fields: "id") @extends {
    id: ID @external
    equipmentProcurements: [EquipmentProcurement!] @authorize(company: ["view.company.equipment.procurements"])
    equipments: [Equipment!] @authorize(company: ["view.company.equipment.equipments"], member: ["view.member.equipment.equipments"])
}

type Country @key(fields: "code") @extends {
    code: CountryCode @external
    productList(filter: ProductFilter!): ProductList @authorize(company: ["view.company.equipment.products"])
    productFilterSpec(status: ProductStatus): ProductFilterSpec @authorize(company: ["view.company.equipment.products"])
}
