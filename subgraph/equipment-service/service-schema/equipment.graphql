extend type Mutation {
    equipmentQuotationCreate(input: EquipmentQuotationInput!): TaskResponse @authorize(company: ["create.company.equipment.quotation"])
    equipmentProcurementCreate(input: EquipmentProcurementInput!): EquipmentProcurement @authorize(company: ["create.company.equipment.procurement"])
    equipmentProductsUpdate(file: Upload!): TaskResponse @authorize(operations: ["update.operations.equipment.products"])
    equipmentsUpdate(file: Upload): TaskResponse @authorize(operations: ["update.operations.equipment.equipments"])
}

extend type Query {
    equipmentIncompleteProcurementsDownload(filter: EquipmentProcurementFilter): EquipmentDownloadResponse @authorize(operations: ["view.operations.equipment.procurements"])
    equipmentsDownload(filter: EquipmentFilter): EquipmentDownloadResponse @authorize(operations: ["view.operations.equipment.equipments"])
}

type ProductList {
    products: [Product!]
    filterSpec: ProductFilterSpec
}

type ProductFilterSpec {
    countries: [CountryCode!]
    types: [String!]
    brands: [String!]
}

type EquipmentDownloadResponse {
    result: TaskResponse!
    file: DocumentReadable
}

type Product {
    id: ID!
    sku: String!
    name: String!
    type: String!
    brand: String!
    modelNumber: String
    description: String
    countryCode: CountryCode!
    price: Float!
    currencyCode: CurrencyCode!
    images: [ProductImage!]
    color: ProductColor
    status: ProductStatus!
    dynamicDetails: [ProductDetailCategory!]
}

type EquipmentProcurement {
    id: ID!
    contractId: ID
    companyId: ID
    requestedOn: Date!
    completedOn: Date
    detail: String
    status: EquipmentProcurementStatus
    totalAmount: Float
    currencyCode: CurrencyCode
    items: [EquipmentProcurementItem!]
}

type EquipmentProcurementItem {
    id: ID!
    productId: ID!
    name: String
    type: String
    description: String
    amount: Float
    currencyCode: CurrencyCode
}

type Equipment {
    id: ID!
    productId: ID!
    contractId: ID
    companyId: ID
    serialNumber: String
    purchasedOn: Date
    type: String
    name: String
    description: String
    status: EquipmentStatus
    documents: [EquipmentProcurementDocument!]
    dynamicDetails: [EquipmentDetail]
}

type EquipmentProcurementDocument {
    id: ID!
    type: String!
    document: DocumentReadable!
}

type ProductImage {
    id: ID
    url: String
}

type ProductColor {
    name: String!
    code: String
}

type ProductDetailCategory {
    key: String!
    details: [ProductDetail!]
}

type ProductDetail {
    key: String!
    value: String!
}

type EquipmentDetail {
    key: String!
    value: String!
}

input ProductFilter {
    countryCode: CountryCode
    type: String
    brand: String
    status: ProductStatus
    productId: ID
}

input EquipmentProcurementFilter {
    countryCodes: [CountryCode!]
    companyIds: [ID!]
    contractIds: [ID!]
    procurementIds: [ID!]
}

input EquipmentFilter {
    companyIds: [ID!]
    contractIds: [ID!]
    procurementIds: [ID!]
    equipmentIds: [ID!]
    statuses: [EquipmentStatus!]
}

input EquipmentQuotationInput {
    countryCode: CountryCode
    type: String
    brand: String
    detail: String
}

input EquipmentProcurementInput {
    productIds: [ID!]
    detail: String
    clients: [ClientDetail!]
}

input ClientDetail {
    id: ID!
    type: String!
}

enum ProductStatus {
    ACTIVE
    INACTIVE
}

enum EquipmentProcurementStatus {
    REQUESTED
    COMPLETED
    CANCELLED
}

enum EquipmentStatus {
    FREE
    ASSIGNED
    EXPIRED
}
