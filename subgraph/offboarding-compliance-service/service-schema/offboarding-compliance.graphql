extend type Query {
    getMultiplierOffboardingData(offboardingId: ID): [MultiplierOffboardingData] @authorize(operations: ["view.operations.contract.offboarding"])
}

extend type Mutation {
    dummyOffboarding: <PERSON><PERSON><PERSON> @authorize(company: ["*"])
}

type MultiplierOffboardingData {
    offboardingId: ID!
    contractOffboardingStatus: MultiplierContractOffboardingStatus!
    contractOffboardingType: MultiplierContractOffboardingType!
    lastWorkingDay: DateTime
    fnfDeadline: DateTime
    fnfPendingTimestamp: DateTime
    fnfSettledTimestamp: DateTime
}

enum MultiplierContractOffboardingStatus {
    FNF_PENDING,
    FNF_SETTLED,
    COMPLETED,
    CANCELLED
}

enum MultiplierContractOffboardingType {
    RESIGNATION,
    TERMINATION,
    DID_NOT_JOIN,
}
