extend type Mutation {
    emailTemplateUpsert(data: EmailTemplateUpsertInput!) : TaskResponse @authorize(operations: ["upsert.operations.emailtemplate"])
}

extend type Query {
    emailTemplates: [EmailTemplate]! @authorize(operations: ["view.operations.emailtemplate", "upsert.operations.emailtemplate"])
    emailTemplate(id: ID!): EmailTemplate @authorize(operations: ["view.operations.emailtemplate", "upsert.operations.emailtemplate"])
    emailTemplateByType(type: String!): EmailTemplate @authorize(operations: ["view.operations.emailtemplate", "upsert.operations.emailtemplate"])
}

type EmailTemplate {
    id: ID!
    templateType: String
    defaultSubject: String
    isActive: Boolean
    file: DocumentReadable @deprecated(reason: "Use the document")
    document: Document
}

input EmailTemplateUpsertInput {
    file: Upload!
    templateType: String!
    defaultSubject: String!
    isActive: Boolean
}
