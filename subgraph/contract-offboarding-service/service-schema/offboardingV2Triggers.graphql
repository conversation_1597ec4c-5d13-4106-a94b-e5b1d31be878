extend type Mutation {
    offboardingInputLwd(input: OffboardingInputLwdInput!): Offboarding @authorize(member: ["update.member.contract.offboarding"], company: ["update.company.contract.offboarding"], operations: ["update.operations.contract.offboarding"])
    offboardingInputAnyLwd(input: OffboardingInputLwdInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingInputReason(input: OffboardingInputReasonInput!): Offboarding @authorize(member: ["update.member.contract.offboarding"], company: ["update.company.contract.offboarding"], operations: ["update.operations.contract.offboarding"])
    offboardingInputComments(input: OffboardingInputCommentsInput!): Offboarding @authorize(member: ["update.member.contract.offboarding"], company: ["update.company.contract.offboarding"], operations: ["update.operations.contract.offboarding"])
    offboardingInputCompanyLeaveBalanceProofDocuments(input: OffboardingInputCompanyLeaveBalanceProofDocumentsInput!): Offboarding @authorize(company: ["update.company.contract.offboarding"])
    offboardingInputMemberSupportingDocuments(input: OffboardingInputMemberSupportingDocumentsInput!): Offboarding @authorize(member: ["update.member.contract.offboarding"])
    offboardingInputOpsSupportingDocuments(input: OffboardingInputOpsSupportingDocumentsInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingInputChecklistDocuments(input: OffboardingInputChecklistDocumentsInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingInputShortfallPayment(input: OffboardingInputShortfallPaymentInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"], company: ["update.company.contract.offboarding"])
    offboardingMemberConfirmWetInkLetterSent(offboardingId: ID!): Offboarding @authorize(member: ["update.member.contract.offboarding"])
    offboardingOpsConfirmResignation(input: OffboardingOpsConfirmResignationInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingInputInsuranceChecklist(input: OffboardingInputInsuranceChecklistInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingInputExpensesChecklist(input: OffboardingInputExpensesChecklistInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingInputPaySupplementsChecklist(input: OffboardingInputPaySupplementsChecklistInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingInputFnfSettledConfirmation(input: OffboardingInputFnfSettledConfirmationInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingInputLeaveBalance(input: OffboardingInputLeaveBalanceInput!): Offboarding @authorize(company: ["update.company.contract.offboarding"], operations: ["update.operations.contract.offboarding"])
    offboardingInputAdditionalFnfDetails(input: OffboardingInputAdditionalFnfDetailsInput!): Offboarding @authorize(company: ["update.company.contract.offboarding"])
    offboardingInputWetInkDocuments(input: OffboardingInputWetInkDocumentsInput!): Offboarding @authorize(member: ["update.member.contract.offboarding"], operations: ["update.operations.contract.offboarding"])
    offboardingConfirmWetInkDocuments(input: OffboardingConfirmWetInkDocumentsInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingInputGovtPortalProof(input: OffboardingInputGovtPortalProofInput!): Offboarding @authorize(member: ["update.member.contract.offboarding"], operations: ["update.operations.contract.offboarding"])
    offboardingConfirmGovtPortalProof(input: OffboardingConfirmGovtPortalProofInput!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
    offboardingSkipResignationLetterSigning(offboardingId: ID!): Offboarding @authorize(operations: ["update.operations.contract.offboarding"])
}

input OffboardingInputLwdInput {
    offboardingId: ID!
    lastWorkingDay: Date!
}

input OffboardingInputReasonInput {
    offboardingId: ID!
    reason: String!
}

input OffboardingInputCommentsInput {
    offboardingId: ID!
    comments: String!
}

input OffboardingInputCompanyLeaveBalanceProofDocumentsInput {
    offboardingId: ID!
    documentIds: [ID!]!
}

input OffboardingInputOpsSupportingDocumentsInput {
    offboardingId: ID!
    documentIds: [ID!]!
}

input OffboardingInputMemberSupportingDocumentsInput {
    offboardingId: ID!
    documentIds: [ID!]!
}

input OffboardingInputChecklistDocumentsInput {
    offboardingId: ID!
    documentIds: [ID!]!
}

input OffboardingInputShortfallPaymentInput {
    offboardingId: ID!
    shortfallPayType: ShortfallPayType!
    amount: Float
    unit: ShortfallPayUnit
}

enum ShortfallPayType {
    NO_PAY
    EMPLOYEE_PAY
    COMPANY_PAY
}

enum ShortfallPayUnit {
    DAYS
    MONTHS
}

input OffboardingInputInsuranceChecklistInput {
    offboardingId: ID!
    insuranceChecklistCompleted: Boolean!
}

input OffboardingInputExpensesChecklistInput {
    offboardingId: ID!
    expensesChecklistCompleted: Boolean!
}

input OffboardingInputPaySupplementsChecklistInput {
    offboardingId: ID!
    paySupplementsChecklistCompleted: Boolean!
}

input OffboardingInputFnfSettledConfirmationInput {
    offboardingId: ID!
    fnfSettledConfirmed: Boolean!
}

input OffboardingInputLeaveBalanceInput {
    offboardingId: ID!
    balanceInDays: Float!
    comments: String
    isMultiplierBalance: Boolean = true
}

input OffboardingInputAdditionalFnfDetailsInput {
    offboardingId: ID!
    additionalFnfDetails: String
}

input OffboardingOpsConfirmResignationInput {
    offboardingId: ID!
}

input OffboardingInputWetInkDocumentsInput {
    offboardingId: ID!
    documentIds: [ID!]!
}

input OffboardingInputGovtPortalProofInput {
    offboardingId: ID!
    documentIds: [ID!]!
}

input OffboardingConfirmGovtPortalProofInput {
    offboardingId: ID!
}

input OffboardingConfirmWetInkDocumentsInput {
    offboardingId: ID!
    receivedPhysically: Boolean
}
