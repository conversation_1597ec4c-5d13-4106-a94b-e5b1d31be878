extend type Mutation {
    offboardingInitialize(input: OffboardingInitializeInput!): Offboarding @authorize(member: ["initiate.member.contract.offboarding"], company: ["initiate.company.contract.offboarding"], operations: ["initiate.operations.contract.offboarding"])

    """
     If `targetState` is not provided, and there is only one possible next state beside CANCELLED, then it will be used.
     `CANCELLED` state must be explicitly specified.

     Possible state means the state that can be reached by the current state (all required actions completed).
    """
    offboardingNextState(offboardingId: ID!, targetState: OffboardingState): Offboarding @authorize(member: ["update.member.contract.offboarding"], company: ["update.company.contract.offboarding"], operations: ["update.operations.contract.offboarding"])
}

extend type Query {
    getTentativeLastWorkingDay(contractId: ID!): LastWorkingDayResponse @authorize(member: ["view.member.contract.offboarding"], company: ["view.company.contract.offboarding"], operations: ["view.operations.contract.offboarding"])
    getLastWorkingDayRange(contractId: ID!): LastWorkingDayRangeResponse @authorize(member: ["view.member.contract.offboarding"], company: ["view.company.contract.offboarding"], operations: ["view.operations.contract.offboarding"])
}

type LastWorkingDayResponse {
    date: Date
}

type LastWorkingDayRangeResponse {
    from: Date,
    to: Date
}

input OffboardingInitializeInput {
    contractId: ID!
    type: OffboardingType!
}

type Offboarding {
    id: ID
    contractId: ID
    type: OffboardingType
    state: OffboardingState
    lastWorkingDay: Date
    fnfDeadline: Date
    leaveBalanceInDays: Float
    leaveBalanceComments: String
    isMultiplierLeaveBalance: Boolean
    fnfAdditionalDetails: String
    hasMemberSentWetInkLetter: Boolean
    receivedPhysicalWetInkLetter: Boolean
    reason: String
    comments: String
    shortfallPay: OffboardingShortFallPay
    checkList: OffboardingCheckList
    documents(types: [ContractOffboardingDocumentType!]): [OffboardingDocument!] # return all if types is null or empty
    anchorTentativeLwd: Date
    anchorLwdRange: DateRangeOutput

    createdOn: DateTime
    updatedOn: DateTime

    connectingTransitions: [OffboardingTransition!]!
    pastTransitions: [OffboardingPastTransition!]!
}

type OffboardingDocument {
    id: ID
    documentId: ID
    documentUUID: UUID
    type: ContractOffboardingDocumentType
}

type OffboardingTransition {
    from: OffboardingState
    to: OffboardingState
    actionStatuses: [OffboardingActionStatus!]!
}

type OffboardingPastTransition {
    from: OffboardingState
    to: OffboardingState
    createdBy: ID
    createdOn: DateTime
}

type OffboardingActionStatus {
    requiredAction: OffboardingAction
    requiredTriggerResult: OffboardingTriggerResult

    isDone: Boolean
    currentStatus: OffboardingTriggerStatus
    currentTriggerResult: OffboardingTriggerResult
    currentTriggerCreatedBy: ID
    currentTriggerCreatedOn: DateTime
    reason: String
}

type OffboardingShortFallPay {
    type: ShortfallPayType
    amount: Float
    unit: ShortfallPayUnit
}

type OffboardingCheckList {
    insuranceChecklist: Boolean
    expensesChecklist: Boolean
    paySupplementsChecklist: Boolean
    fnfSettledConfirmedChecklist: Boolean
}

enum OffboardingType {
    RESIGNATION
    #    TERMINATION
    #    DID_NOT_JOIN
}

enum OffboardingState {
    DRAFT
    SHORTFALL_PAY_DETAILS_PENDING
    RESIGNATION_LETTER_PENDING
    WET_INK_LETTER_PENDING
    WET_INK_OPS_CONFIRM_PENDING
    GOVT_PORTAL_PROOF_PENDING
    FNF_DETAILS_PENDING
    FNF_PENDING
    FNF_SETTLED
    CANCELLED
}

enum OffboardingAction {
    INPUT_LWD
    INPUT_ANY_LWD
    MEMBER_SIGNS_RESIGNATION_LETTER
    OPS_CONFIRM_RESIGNATION
    INPUT_SHORTFALL_PAY
    INPUT_LEAVE_BALANCE
    INPUT_PAY_SUPPLEMENTS_DETAILS
    INPUT_EXPENSES_DETAILS
    INPUT_INSURANCE_DETAILS
    INPUT_FNF_SETTLED_CONFIRMATION
    SETTLE_FNF
    CANCEL
    INPUT_WET_INK_DOCUMENTS
    OPS_CONFIRM_WET_INK_DOCUMENTS
    INPUT_GOVT_PORTAL_PROOF
    VERIFY_GOVT_PORTAL_PROOF
}

enum OffboardingTriggerStatus {
    GUARD_FAILED
    SUCCESS
}

enum OffboardingTriggerResult {
    LWD_EQ_COMPLIANCE_NOTICE_PERIOD
    LWD_LT_COMPLIANCE_NOTICE_PERIOD
    LWD_GT_COMPLIANCE_NOTICE_PERIOD
    MEMBER_SIGNED_RESIGNATION_LETTER
    OPS_CONFIRMED_RESIGNATION
    SHORTFALL_PAY_PROVIDED
    LEAVE_BALANCE_PROVIDED
    PAY_SUPPLEMENTS_DETAILS_PROVIDED
    EXPENSES_DETAILS_PROVIDED
    INSURANCE_DETAILS_PROVIDED
    FNF_SETTLED_CONFIRMED
    FNF_SETTLED
    CANCELLED
    WET_INK_DOCUMENTS_PROVIDED
    WET_INK_DOCUMENTS_CONFIRMED
    GOVT_PORTAL_PROOF_PROVIDED
    GOVT_PORTAL_PROOF_VERIFIED
}

type DateRangeOutput {
    startDate: DateTime,
    endDate: DateTime
}
