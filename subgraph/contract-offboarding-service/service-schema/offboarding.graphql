extend type Mutation {
    contractOffboardingVerify(
        input: ContractOffboardingVerifyInput!
    ): ContractOffboarding @authorize(company: ["verify.company.contract.offboarding"], operations: ["verify.operations.contract.offboarding"])
    contractOffboardingComplete(
        input: ContractOffboardingCompleteInput!
    ): ContractOffboarding @authorize(company: ["complete.company.contract.offboarding"], operations: ["complete.operations.contract.offboarding"])
    contractOffboardingReschedule(
        input: ContractOffboardingRescheduleInput!
    ): ContractOffboarding @authorize(company: ["reschedule.company.contract.offboarding"], operations: ["reschedule.operations.contract.offboarding"])
    contractOffboardingCancelV2(
        input: ContractOffboardingCancelInput!
    ): ContractOffboarding @authorize(company: ["cancel.company.contract.offboarding"], operations: ["cancel.operations.contract.offboarding"])
    contractOffboardingResignationInitialise(
        input: ContractOffboardingResignationInitialiseInput!
    ): ContractOffboarding @authorize(company: ["initiate.company.contract.offboarding"], operations: ["initiate.operations.contract.offboarding"])
    contractOffboardingTerminationInitialise(
        input: ContractOffboardingTerminationInitialiseInput!
    ): ContractOffboarding @authorize(company: ["initiate.company.contract.offboarding"], operations: ["initiate.operations.contract.offboarding"])
    contractOffboardingDidNotJoinInitialise(
        input: ContractOffboardingDidNotJoinInitialiseInput!
    ): ContractOffboarding @authorize(company: ["initiate.company.contract.offboarding"], operations: ["initiate.operations.contract.offboarding"])
    contractOffboardingTriggerEndContractScheduler: TaskResponse @authorize(operations: ["trigger.operations.contract.offboarding"])
    contractOffboardingTriggerSendRelievingLetterScheduler: TaskResponse @authorize(operations: ["trigger.operations.contract.offboarding"])
    contractOffboardingTriggerSendAutomateOffboardingReminder: TaskResponse @authorize(operations: ["trigger.operations.contract.offboarding"])
    contractOffboardingTriggerFixedTermScheduler: TaskResponse @authorize(operations: ["trigger.operations.contract.offboarding"])
}

input ContractOffboardingResignationInitialiseInput {
    contractId: ID!
    lastWorkingDay: Date
    notes: String
    documents: [Upload]
    leaveBalanceToBeEncashed: PeriodValue
    exGratiaToBePaid: PeriodValue
}

input ContractOffboardingTerminationInitialiseInput {
    contractId: ID!
    clauses: [String] # Clause keys, will shift to extensive configuration later
    reason: String
    lastWorkingDay: Date
    documents: [Upload]
    notes: String
    severancePay: SeverancePayOption
    leaveBalanceToBeEncashed: PeriodValue
    exGratiaToBePaid: PeriodValue
    severancePayForDirectEmployee: PeriodValue
}

input ContractOffboardingDidNotJoinInitialiseInput {
    contractId: ID!
    reason: ContractOffboardingDidNotJoinReason
    notes: String
}

input ContractOffboardingVerifyInput {
    contractOffboardingId: ID!
}

input ContractOffboardingCompleteInput {
    contractOffboardingId: ID!
    verificationChecklistDocuments: [Upload]
    pendingPaySupplements: String
    pendingExpenses: String
    insurance: String
    finalSettlementProcessed: String
}

input ContractOffboardingRescheduleInput {
    contractOffboardingId: ID!
    revisedDate: Date
    documents: [Upload]
}

input ContractOffboardingCancelInput {
    contractOffboardingId: ID!
}

input PeriodValue {
    value: Float
    unit: TimeUnit
}

# Add this output type
type PeriodValueOutput {
    value: Float
    unit: TimeUnit
}

type ContractOffboarding {
    id: ID
    contractId: ID
    type: ContractOffboardingType @authorize(company: ["view.company.contract.offboarding.termination-type"], member: ["view.member.contract"], operations: ["view.operations.contract"])
    initiatedOn: DateTime @authorize(company: ["view.company.contract.offboarding.termination-date"], member: ["view.member.contract"], operations: ["view.operations.contract"])
    initiatedBy: ContractOffboardingInitiator
    lastWorkingDay: Date
    notes: String
    documents(
        type: ContractOffboardingDocumentType
    ): [ContractOffboardingDocument]
    verificationChecklist: ContractOffboardingVerificationChecklist
    status: ContractOffboardingStatus
    didNotJoinReason: ContractOffboardingDidNotJoinReason
    terminationReason: String @authorize(company: ["view.company.contract.offboarding.termination-reason"], member: ["view.member.contract"], operations: ["view.operations.contract"])
    terminationClauses: [String]
    severancePay: SeverancePayOption,
    createdBy: ID,
    createdOn: DateTime,
    updatedBy: ID,
    updatedOn: DateTime,
    source: ContractOffboardingSource,
    exGratiaToBePaid: PeriodValueOutput,
    leaveBalanceToBeEncashed: PeriodValueOutput,
    severancePayForDirectEmployee: PeriodValueOutput
}

type ContractOffboardingDocument {
    id: ID
    name: String
    contractOffboardingId: ID
    contractId: ID
    type: ContractOffboardingDocumentType
    document: DocumentReadable @deprecated(reason: "Use `file` instead")
    file: Document
    documentUuid: UUID
}

type ContractOffboardingVerificationChecklist {
    id: ID
    contractOffboardingId: ID
    contractId: ID

    # Filtered CHECKLIST documents
    checklistDocuments: [ContractOffboardingDocument]

    # Checklist Toggles
    pendingPaySupplements: String
    pendingExpenses: String
    insurance: String
    finalSettlementProcessed: String
}

# Please also update the enum OffboardingExperienceDocumentType
enum ContractOffboardingDocumentType {
    SUPPORTING # used in v1
    CHECKLIST
    RESIGNATION_LETTER
    OPS_SUPPORTING
    MEMBER_SUPPORTING
    WET_INK_DOCUMENT
    GOVT_PORTAL_PROOF
    LEAVE_BALANCE_PROOF
    RELIEVING_LETTER
}

enum ContractOffboardingInitiator {
    EMPLOYER
    OPERATIONS
    EMPLOYEE
}

enum ContractOffboardingDidNotJoinReason {
    NO_INFORMATION
    JOINED_ANOTHER_COMPANY
    OTHERS
}

enum ContractOffboardingSource {
    MULTIPLIER_PLATFORM
    EXTERNAL_SERVICE
}
