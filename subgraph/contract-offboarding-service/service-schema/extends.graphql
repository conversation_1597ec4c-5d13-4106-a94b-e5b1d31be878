type Contract @key(fields: "id") @extends {
  id: ID @external
  offboarding: ContractOffboarding @authorize(member: ["view.member.contract.offboarding"], company: ["view.company.contract.offboarding"], operations: ["view.operations.contract.offboarding"])
  isValidLastWorkingDay(
    dateToCheck: Date!
    offboardingType: ContractOffboardingType!
  ): LastWorkingDayValidation @authorize(company: ["view.company.contract.offboarding"], operations: ["view.operations.contract.offboarding"])
  offboardingV2: Offboarding @authorize(member: ["view.member.contract.offboarding"], company: ["view.company.contract.offboarding"], operations: ["view.operations.contract.offboarding"])
  isOffboardingV2Supported(
    offboardingType: OffboardingType!
  ): OffboardingCanInitializeValidation
}

type LastWorkingDayValidation {
  isValid: Boolean
  message: String
  minimumLastWorkingDay: Date
}

type OffboardingCanInitializeValidation {
    canInitialize: Boolean
    reason: String
    requiresWetInk: Boolean
    requiresGovtPortalProof: Boolean
}

type Document @key(fields: "id") @extends {
  id: ID @external
}

