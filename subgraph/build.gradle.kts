
val graphGroup: String by rootProject.extra
val serviceSchemaPath: String by rootProject.extra
val generatedSchemaPath: String by rootProject.extra

val mergeScript = file("merge-subgraph.js")
subprojects {
    apply<com.multiplier.plugin.PublishWithSummaryPlugin>()
    configure<com.multiplier.plugin.PublishWithSummaryExtension> {
        artifactId = "${project.name}-graph"
    }

    tasks.register<Exec>("mergeSubgraph") {
        group = graphGroup

        outputs.cacheIf { true }

        inputs.file(mergeScript)
        inputs.dir(serviceSchemaPath)
        inputs.dir(projects.subgraph.common.dependencyProject.layout.projectDirectory.dir(serviceSchemaPath))

        outputs.dir(layout.buildDirectory.dir(generatedSchemaPath))

        workingDir = projectDir

        commandLine("node", mergeScript)
    }
}
