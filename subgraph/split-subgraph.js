const fs = require('fs');
const { loadFilesSync } = require('@graphql-tools/load-files');
const { mergeTypeDefs } = require('@graphql-tools/merge');
const { pruneSchema } = require('@graphql-tools/utils');
const { buildASTSchema, printType } = require('graphql');

console.log("Merging subgraph");

const commonSchemaFiles = loadFilesSync('../common/service-schema/**/*.graphql');
const files = loadFilesSync(`service-schema/**/*.graphql`);
const subgraph = mergeTypeDefs([...files, ...commonSchemaFiles]);

const schemaPath = `build/generated/resources/split`;
if (fs.existsSync(schemaPath)) {
    fs.rmSync(schemaPath, { recursive: true });
}
fs.mkdirSync(schemaPath, { recursive: true });

const schema = buildASTSchema(subgraph, { assumeValid: true });
const prunedSchema = pruneSchema(schema);

const typeMap = prunedSchema.getTypeMap();
const queryType = prunedSchema.getQueryType();
const mutationType = prunedSchema.getMutationType();

const queryFields = queryType ? queryType.getFields() : {};
const mutationFields = mutationType ? mutationType.getFields() : {};

function collectRelatedTypes(typeName, collected = new Set(), depth = 0) {
    if (!typeName || collected.has(typeName) || depth >= 10) return;
    collected.add(typeName);
    const type = typeMap[typeName];
    if (!type || !('getFields' in type)) return;

    Object.values(type.getFields()).forEach(field => {
        const cleanType = field.type.toString().replace(/[[\]!]/g, '');
        collectRelatedTypes(cleanType, collected, depth + 1);
    });
}

function generateSchemaChunks(fields, typeName) {
    Object.entries(fields).forEach(([fieldName, field]) => {
        const relatedTypes = new Set();
        const rootType = field.type.toString().replace(/[[\]!]/g, '');
        collectRelatedTypes(rootType, relatedTypes, 0);

        let schemaString = `type ${typeName} {\n  ${fieldName}: ${field.type}\n}`;
        relatedTypes.forEach(typeName => {
            if (typeMap[typeName]) {
                schemaString += '\n' + printType(typeMap[typeName]);
            }
        });

        fs.writeFileSync(`${schemaPath}/${typeName.toLowerCase()}_${fieldName}-split.graphql`, schemaString);
    });
}

generateSchemaChunks(queryFields, 'Query');
generateSchemaChunks(mutationFields, 'Mutation');

console.log("Merged and split subgraph");
