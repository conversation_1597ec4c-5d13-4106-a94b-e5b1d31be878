extend type Query {
    expenseValidationInfo(input: ExpenseValidationInfoInput!): ExpenseValidationInfo!
        @authorize(
            company: ["view.company.expenses"],
            member: ["view.member.expenses"],
            operations: ["view.operations.expenses"]
        )
}

input ExpenseValidationInfoInput {
    contractId: ID                  # required for Company/Ops exp; for company_id/entity_id/relevant expense data of the contract
    categoryId: ID                  # required for dateLimit & amountLimit
    expenseDate: Date               # required for amountLimit
    expenseAmount: Float            # required for amount-based approval rules
}

type ExpenseValidationInfo {
    dateLimit: ExpenseDateRange                 # null means no limit; requires contractId & categoryId
    amountLimit: ExpenseAmountLimit             # null means no limit; requires contractId & categoryId & expenseDate
    attachmentRequired: Boolean
}

type ExpenseDateRange {
    start: Date                             # e.g. 2024-04-19 = (today 2024-07-18).minusDays(90) in case of "90 days" rule. Server decides the "today" value (in UTC), so deviation between time zone is expected
    end: Date                               # null = no "end" edge (i.e. today)
    threshold: Int                          # the static threshold from the rule, e.g. 3 (months), 90 (days). Assuming no decimal values
    unit: ExpenseDateThresholdUnit          # the unit of the `threshold`
}

enum ExpenseDateThresholdUnit {
    DAY
    WEEK
    MONTH
}

type ExpenseAmountLimit {
    basis: ExpenseAmountLimitBasis
    limit: Float
    remaining: Float
    currency: CurrencyCode
}

enum ExpenseAmountLimitBasis {
    DAILY
    WEEKLY
    MONTHLY
}