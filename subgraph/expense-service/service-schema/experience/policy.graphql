extend type Query {
    expensePolicies(filter : ExpensePolicyFilter) : [ExpensePolicy!] @authorize(company: ["view.company.expenses.policy"], operations:["view.operations.expenses.policy"])
}

extend type Mutation {
    """policy id is actually the expense_category.id"""
    expensePolicyDelete(id : ID!) : ExpensePolicy @authorize(operations:["delete.operations.expenses.policy"])
}

input ExpensePolicyFilter {
    entityIds : [ID!]                           # legal entity ids
}

"""Actually an expense category with its rules"""
type ExpensePolicy {
    id: ID                                      # same as `category.id` for now
    category: ExpenseCategory
    amountLimit: ExpenseAmountLimit
    dateLimit: ExpenseDateRange                 # use `threshold` + `unit`
}