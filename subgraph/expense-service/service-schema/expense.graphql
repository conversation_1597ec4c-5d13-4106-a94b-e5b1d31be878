extend type Query {
    expensesReport(filters: ExpenseFilters!): DocumentReadable  # for ops users only
        @authorize(operations: ["view.operations.expenses"])
    expense(id: ID!): Expense   # for external users to view expense
       @authorize(operations: ["view.operations.expenses.{resource_id}"])
    expenses(filters: ExpenseFilters, pageRequest: PageRequest): ExpensesResponse
        @authorize(operations: ["view.operations.expenses"])
    expenseCategories(filter: ExpenseCategoryFilter): [ExpenseCategory!] # for 86cvptuja/86cvptv6d
        @authorize(
            company: ["view.company.expenses"],
            member: ["view.member.expenses"],
            operations: ["view.operations.expenses"]
        )

    # For v2
    expensesWithPagination(filters: ExpensesWithPaginationFilters!, pageRequest: PageRequest!): ExpensesResponse
        @authorize(
            company: ["view.company.expenses"]
        )
    expenseDetailsV2(expenseId: ID!): Expense
        @authorize(
            company: ["view.company.expenses"]
        )
}

extend type Mutation {
    expenseUpdate(input: ExpenseUpdateInput!): Expense # Update the expense. Can call by the person (COMPANY/MEMBER) who raised it. Will send an email to Ops if some conditions are met
        @authorize(
            company: ["update.company.expenses"],
            member: ["update.member.expenses"]
        )
    expenseDelete(id: ID!): Expense  # Delete. Can call by OPS and COMPANY (if created by a company) <-- [previous comment need rechecking] only called by member and company from BE @PreAuth
        @authorize(
            company: ["delete.company.expenses"],
            member: ["delete.member.expenses"]
        )
    expenseRevoke(id: ID!): Expense # Move to DRAFT
        @authorize(
            company: ["revoke.company.expenses"],
            member: ["revoke.member.expenses"]
        )
    expenseBulkRevoke(ids: [ID!]!): ExpenseUpdateBulkResponse # actually move to DRAFT
        @authorize(operations: ["update.operations.expenses"])

    expenseSaveAsDraft(input: ExpenseSaveAsDraftInput!): Expense
        @authorize(
            operations: ["create.operations.expenses"],
            company: ["create.company.expenses"],
            member: ["create.member.expenses"]
        )
    expenseSubmit(input: ExpenseSubmitInput!): Expense
        @authorize(
            operations: ["create.operations.expenses"],
            company: ["create.company.expenses"],
            member: ["create.member.expenses"]
        )
    expenseUpdateBulkAsPaid(ids: [ID]!): ExpenseUpdateBulkResponse
        @authorize(operations: ["update.operations.expenses"])
    expenseUpdateBulkAsToBePaid(ids: [ID]!): ExpenseUpdateBulkResponse
        @authorize(operations: ["update.operations.expenses"])
    expenseNotificationDenyListAdd(input: ExpenseNotificationDenyListAddInput!): TaskResponse
        @authorize(operations: ["update.operations.expenses"])
    expenseNotificationDenyListDelete(ids: [ID!]!): TaskResponse  
        @authorize(operations: ["update.operations.expenses"])  
    # Get pre-signed URLs for expense receipt uploads
    expenseGetUploadUrls(input: ExpenseUploadUrlsInput): ExpenseUploadUrlsResponse
        @authorize(
            operations: ["create.operations.expenses"],
            company: ["create.company.expenses"],
            member: ["create.member.expenses"]
        )
}

# inputs

input ExpenseReportInput {
    startDate: DateTime!
    endDate: DateTime!
    status: ExpenseStatus
    country: [CountryCode]!
    category: [String]!
}

input ExpenseFilters {
    startDate: DateTime
    endDate: DateTime
    status: ExpenseStatus @deprecated(reason: "status is deprecated. Use statuses instead.")
    statuses: [ExpenseStatus!]  # this will override the status filter
    contractStatus: ContractStatus
    contractType: ContractType @deprecated(reason: "contractType is deprecated. Use contractTypes instead.")
    contractTypes: [ContractType!] # this will override the status filter
    country: [CountryCode!]!
    contractIds: [ID!]!
    expenseIds: [ID!]!
    companyIds: [ID!]!
    eorPartnerIds: [ID!]
    isMultiplierEorPartner: Boolean
    payrollCycleIds: [ID!]
}

input ExpensesWithPaginationFilters {
    memberName: String                              # first or last name of the member
    approver: ApproverFilter @deprecated            # filter by approver
    statuses: [ExpenseStatus!]
    view: ExpenseView                               # Predefined filter views: ALL_PENDING, PENDING_ON_ME, HISTORY
}

input ApproverFilter {
    userId: ID!
}

input ExpenseSubmitInput {
    expenseId: ID               # Optional. null -> a new expense; not null -> an existing expense, but if the existing expense has status != DRAFT (already submitted) -> error
    contractId: ID            # Optional. null -> this is a member; not null -> this is an admin submitting an expense on behalf of a member
    title: String
    skipsPayrollProcessing: Boolean = false
    items: [ExpenseItemInput]!
}

input ExpenseSaveAsDraftInput {
    contractId: ID                  # Optional. null -> this is a member; not null -> this is an admin saving as draft an expense on behalf of a member
    title: String
    skipsPayrollProcessing: Boolean = false
    items: [ExpenseItemInput]!
}

input ExpenseCreateInput {
    expenseId: ID               # Optional. null -> a new expense; not null -> an existing expense, but if the existing expense has status != DRAFT (already submitted) -> error
    contractId: ID            # Optional for MEMBER. Can get from JWT token for MEMBER experience. null -> currentUser is member; non-null -> currentUser is creating an expense on behalf of the `contractId`
    title: String
    items: [ExpenseItemInput]!
    isDraft: Boolean = false    # true -> save as draft; false -> add (i.e. submit)
}

input ExpenseItemInput {
    title: String
    date: DateTime!              # Date of expense
    category: String            @deprecated(reason: "As part of 86cvptuja/86cvptv6d. Use `categoryId` instead")
    categoryId: ID              # As part of 86cvptuja/86cvptv6d. The `expense_category.id` in DB, selected by the user from the category dropdown
    description: String
    merchant: String @deprecated(reason: "Found no usages. All existing records have it null.")
    amount: Float!
    receipts: [Upload]       # Proof of receipt upload is optional for COMPANY. Mandatory for MEMBERS.
        @deprecated(reason: "Use receiptUrls instead for uploading via pre-signed URLs")
        @authorize(
            company: ["view.company.expenses"],
            member: ["view.member.expenses"],
            operations: ["view.operations.expenses"]
        )
    receiptUrls: [String]      # S3 object IDs of receipts uploaded via pre-signed URL
    amountInForeignCurrency: ExpenseAmountInfoInput
    receiptsInForeignCurrency: [Upload!]
        @deprecated(reason: "Use receiptsInForeignCurrencyUrls instead for uploading via pre-signed URLs")
        @authorize(
            company: ["view.company.expenses"],
            member: ["view.member.expenses"],
            operations: ["view.operations.expenses"]
        )
    receiptsInForeignCurrencyUrls: [String!] # S3 object IDs of foreign currency receipts uploaded via pre-signed URL
    externalId: String
}

input ExpenseAmountInfoInput {
    amount: Float!
    currency: CurrencyCode!
}

input ExpenseUpdateInput {
    expenseId: ID!
    title: String
    skipsPayrollProcessing: Boolean = false
    items: [ExpenseItemInput]   # Can add/remove/modify expense items. Existing list will replace with this updated list.
    clarification: ExpenseClarificationReviewInput
}

input ExpenseCategoryFilter {
    contractId: ID                      # Pass this when company/ops is submitting an expense for the contract. In case of member exp, leave it null (BE will use currentUser)

    # may have more filter fields for other use cases of fetching `expenseCategories`.
}

input ExpenseClarificationReviewInput {
    comment: String
}

# types

type Expense @key(fields: "id") {
    id: ID
    contract: Contract
    items: [ExpenseItem]
    status: ExpenseStatus
    title: String
    changes(latest: Boolean = true, status: ExpenseStatus): [ExpenseChange]    # Has a ordered list of all the changes that happened to the expense since creation.
        @authorize(
            company: ["view.company.expenses"],
            member: ["view.member.expenses"],
            operations: ["view.operations.expenses"]
        )
    currency: CurrencyCode                              # Currency of the expense.
    totalAmount: Float
    totalInFunctionalCurrency: Float @deprecated(reason: "Hardcoded with USD. No usages. Seems a legacy one. BE still calculates and returns this but will be removed")
    changeReason: String                                # the rejection reason
    submittedAt: DateTime                               # matches the field name on Alpha for now
    createdOn: DateTime
    updatedOn: DateTime
    createdBy: ID                                       # this is user_id, not member.id, company_user.id. Can be null (migrated from Alpha)
    updatedBy: ID                                       # this is user_id, not member.id, company_user.id. Can be null (migrated from Alpha) or -1 (async related)
    approvers: [CompanyUser] @deprecated(reason: "Legacy field. No implementations or usages found in the code")
    isApprovable: Boolean
    skipsPayrollProcessing: Boolean                     # If true, this expense will not be included in the payroll (ex: employee used company card for expense -> no reimbursement required)

    # Expense needs to approve before this cutoff date to be included in the payroll.
    # This will be populated for expenses that are pending approval (i.e. status = APPROVAL_IN_PROGRESS)
    payrollCutOffDate: Date
        @authorize(
            company: ["view.company.expenses"],
            member: ["view.member.expenses"],
            operations: ["view.operations.expenses"]
        )
    externalId: String

    approval: ApprovalRequest

    actionRules: [ExpenseActionRule!] # Currently not affected by the policy/rule configurations yet.
}

type ExpenseUpdateBulkResponse {
    success: Boolean
    message: String
    updateItemCount: Int
}

type ExpenseItem {
    id: ID
    title: String
    date: DateTime                                      # Date of expense
    category: String                                    @deprecated(reason: "As part of 86cvptuja/86cvptv6d. Use `categoryV2` instead")
    categoryV2: ExpenseCategory                         # As part of 86cvptuja/86cvptv6d. Details of the category of this expense-item (in DB: expense_item.category_id)
    description: String
    merchant: String @deprecated(reason: "Found no usages. All existing records have it null.")
    amount: Float
    amountInFunctionalCurrency: Float @deprecated(reason: "Hardcoded with USD. No usages. Seems a legacy one. BE still calculates and returns this but will be removed")
    receipts: [DocumentReadable]
    amountInForeignCurrency: ExpenseAmountInfo
    receiptsInForeignCurrency: [DocumentReadable!]
}

type ExpenseAmountInfo {
    amount: Float
    currency: CurrencyCode
}

type ExpenseChange {
    status: ExpenseStatus
    actionedBy: Person
    actionedOn: DateTime
    reason: String
}

type ExpensesResponse {
    page: PageResult
    expenses: [Expense]
}

type ExpenseCategory {
    id: ID                                      # Auto incremental
    name: String                                # E.g. "Travel - Public Transport", "Medical Expenses"
    glCode: CompanyGLCode                       # A financial term to classify all the expenses and stuff. Managed by company-svc
    company: Company                            # In case company.otherFields are needed
    entityId: ID                                # The LegalEntity is in common folder and not a "graph entity", so simply using ID here
}

"""  
If wholeCompany = true:  
- contractIds is ignored  
- a single record of (companyId, null) is inserted  

If wholeCompany = false:  
- each of contractIds will become a (companyId, contractId1) record. [] means 0 records.  
"""  
input ExpenseNotificationDenyListAddInput {
    companyId: ID!
    contractIds: [ID!]!
    notificationType: NotificationType!
    wholeCompany: Boolean! = false
}

# Response type for pre-signed URL generation
# Contains file object with upload URL and metadata
type ExpenseUploadUrlFile {
    id: ID!  # Getting from input
    fileName: String! # Getting from input
    url: String! # Uploadable url
    objectId: String! # S3 object ID, used to submit expense
    expiresAt: DateTime!
}

type ExpenseUploadUrlsResponse {
    fileUrls: [ExpenseUploadUrlFile!]!
}

input ExpenseUploadUrlFileInput {
    id: ID!
    fileName: String!
}

input ExpenseUploadUrlsInput {
    fileUrls: [ExpenseUploadUrlFileInput!]!
}
