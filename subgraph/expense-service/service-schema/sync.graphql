extend type Mutation {
    # Trigger reminders for member to submit their expenses
    triggerMemberExpenseSubmissionReminders(simulatedExecutionDate: Date): TaskResponse @authorize(operations: ["trigger.operations.expense.reminder"])

    # Sets expense_item.category_id for existing records. https://usemultiplier.atlassian.net/browse/HRX-95
    expenseItemMigrateToCategoryId: TaskResponse @authorize(operations: ["update.operations.expenses"]) @deprecated(reason: "One-time mutation")
}
