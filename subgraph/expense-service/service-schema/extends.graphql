type Company @key(fields: "id") @extends {
    id: ID @external
    expenses(expenseId: ID): [Expense] @authorize(company: ["view.company.expenses"])
    expenseReport(input: ExpenseReportInput!): DocumentReadable @deprecated(reason: "Confirmed in https://multiplier-group.slack.com/archives/C07610QUNJ2/p1736242777716749")
}

type Contract @key(fields: "id") @extends {
    id: ID @external
    expenses(
        id: ID,
        status: ExpenseStatus @deprecated(reason: "status is deprecated. Use statuses instead. Unused"),
        fromDate: DateTime @deprecated(reason: "Unused"),
        toDate: DateTime @deprecated(reason: "Unused"),
        statuses: [ExpenseStatus!] @deprecated(reason: "Unused"), # This will override the status filter
        payrollCycleIds: [ID!] @deprecated(reason: "Unused")
    ): [Expense]
        @authorize(
            member: ["view.member.expenses"],
            operations: ["view.operations.expenses"], # Still being used in Ops detail view https://dev-ops.frontend.acc.staging.usemultiplier.com/operations/expenses/509229/518254
        )
}

interface MemberPay @key(fields: "id") @extends {
    id: ID @external
    expenses: [Expense] @deprecated(reason: "Unused & unimplemented since very long ago")
}

type CompanyMemberPay implements MemberPay @key(fields: "id") @extends {
    id: ID @external
    expenses: [Expense] @deprecated(reason: "Unused & unimplemented since very long ago")
}

type PartnerMemberPay implements MemberPay @key(fields: "id") @extends {
    id: ID @external
    expenses: [Expense] @deprecated(reason: "Unused & unimplemented since very long ago")
}

type CompanyUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type Member implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type OperationsUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

type EmergencyPointOfContact implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

interface ApprovalItem @key(fields: "id") @extends{
    id: ID @external
}

type ExpenseApprovalItem implements ApprovalItem @key(fields: "id") @extends{
    id: ID @external
    expenseId: ID @external
    expenseItem: Expense @requires(fields: "expenseId")
        @authorize(
            company: ["view.company.expenses"],
            member: ["view.member.expenses"],
            operations: ["view.operations.expenses"]
        )
}

type PayrollCycle @key(fields: "id") @extends {
    id: ID! @external
}

type ContractSnapshot @key(fields: "id") @extends {
    id: ID @external
    contractId: ID @external
    # probably only has `id` inside as a result of https://github.com/Multiplier-Core/payroll-service/pull/883/files#diff-342fd1e7a2f6f61d5b439074ec4e1b762ab9522a07b01c7217bf62117baa001e
    payrollCycle: PayrollCycle @external

    # not included expenses = not eligible for this payroll or excluded from this payroll
    notIncludedExpenses: [Expense!]! @requires(fields: "contractId payrollCycle { id }")
        @authorize(
            company: ["view.company.expenses"],
            member: ["view.member.expenses"],
            operations: ["view.operations.expenses"]
        )
}

type CompanyGLCode @key(fields: "id") @extends {
    id: ID @external
}

type ApprovalRequest @key(fields: "id") @extends {
    id: ID! @external
}

type ApprovalConfig @key(fields: "id") @extends {
    id: UUID! @external
}