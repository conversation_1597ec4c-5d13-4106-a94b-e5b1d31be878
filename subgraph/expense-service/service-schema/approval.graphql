extend type Mutation {
    expenseApprove(input: ExpenseApproveInput!): Expense
        @authorize(company: ["approve.company.expenses"])
    expenseReject(input: ExpenseRejectInput!): Expense
        @authorize(company: ["reject.company.expenses"])
    expenseRevokeV2(input: ExpenseRevokeInput!): Expense
        @authorize(
            company: ["revoke.company.expenses"],
            member: ["revoke.member.expenses"]
        )
    expenseRequestClarification(input: ExpenseRequestClarificationInput!): Expense
        @authorize(company: ["request-clarification.company.expenses"])

    expenseStartApproval(expenseId: ID!): Expense # meant for cases where submission does not start the approval as expected 
        @authorize(
            company: ["start-approval.company.expenses"],
            member: ["start-approval.member.expenses"],
            operations: ["start-approval.operations.expenses"]
        )
}

input ExpenseApproveInput {
    expenseId: ID!
    comment: String
}

input ExpenseRejectInput {
    expenseId: ID!
    comment: String
}

input ExpenseRevokeInput {
    expenseId: ID!
    comment: String
}

input ExpenseRequestClarificationInput {
    expenseId: ID!
    targetUserId: ID! # A `platform_user.id`. Always member for now
    comment: String
}

type ExpenseActionRule {
    action: ExpenseAction
    allowed: Boolean
    reason: String
}

enum ExpenseAction {
    APPROVE
    REJECT
    REVOKE # this is WITHDRAW in approval domain language
    REQUEST_CLARIFICATION
    RESOLVE_CLARIFICATION
}

