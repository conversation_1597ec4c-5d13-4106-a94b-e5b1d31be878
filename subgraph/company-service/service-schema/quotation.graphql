extend type Query {
    companyQuotation(id: ID!): CompanyQuotation @authorize(operations: ["view.operations.company-quotation"])
    companyQuotations(filters: CompanyQuotationFilter, pageRequest: PageRequest): CompanyQuotationsResponse @authorize(operations: ["view.operations.company-quotation"])
}

extend type Mutation {
    generateCompanyQuotation(input: CompanyQuotationGenerationInput!): CompanyQuotation @authorize(operations: ["create.operations.company-quotation"])
    downloadCompanyQuotation(quotationId: ID!): FileLink @deprecated(reason: "Use saveAndDownloadCompanyQuotation instead") @authorize(operations: ["create.operations.company-quotation"])
    saveAndDownloadCompanyQuotation(input: CompanyQuotationGenerationInput!): CompanyQuotation @authorize(operations: ["create.operations.company-quotation"])
}

type CompanyQuotationsResponse {
    data: [CompanyQuotation]
    pageResult: PageResult
}

input CompanyQuotationFilter {
    id: ID
    companyName: String
    createdBy: ID
    createdOn: DateRange
}

input CompanyQuotationGenerationInput {
    companyName: String!
    companyCountry: CountryCode
    billingCurrency: CurrencyCode!
    memberInputs: [QuotationMemberInput!]!
    includeManagementFee: Boolean!
    includeDepositAmount: Boolean!
}

input QuotationMemberInput {
    memberCountry: CountryCode!
    memberCountryState: String
    memberCurrency: CurrencyCode!
    grossMonthly: Float @deprecated(reason: "Use grossSalary and grossSalaryFrequency")
    grossSalary: Float
    grossSalaryFrequency: RateFrequency
    benefitPackageId: String
    depositAmount: Float
    depositAmountCurrency: CurrencyCode
    managementFee: Float
    managementFeeCurrency: CurrencyCode
    discounts: [QuotationMemberDiscountInput]
}

input QuotationMemberDiscountInput {
    planType: QuotationDiscountPlanType!
    discountType: QuotationDiscountType!
    amount: Float!
    currency: CurrencyCode!
}

type CompanyQuotation {
    id: ID
    companyName: String
    companyCountry: CountryCode
    billingCurrency: CurrencyCode
    memberInputs: [QuotationMemberInputData]
    costOverview: QuotationCostOverview
    countryWiseSummaries: [QuotationCountryWiseCostSummary]
    countryWiseBreakdowns: [QuotationCountryWiseCostBreakdown] # member wise breakdown
    memberWiseSummaries: [QuotationMemberWiseSummary]
    includeManagementFee: Boolean
    includeDepositAmount: Boolean
    document: FileLink
    createdBy: OperationsUser
    createdOn: DateTime
}

type QuotationMemberInputData {
    memberCountry: CountryCode
    memberCountryState: String
    memberCurrency: CurrencyCode
    grossMonthly: Float @deprecated(reason: "Use grossSalary and grossSalaryFrequency")
    grossSalary: Float
    grossSalaryFrequency: RateFrequency
    benefitPackageId: String
    depositAmount: Float
    depositAmountCurrency: CurrencyCode
    managementFee: Float
    managementFeeCurrency: CurrencyCode
    discounts: [QuotationMemberDiscountInputData]
}

type QuotationMemberDiscountInputData {
    planType: QuotationDiscountPlanType
    discountType: QuotationDiscountType
    amount: Float
    currency: CurrencyCode
}

type QuotationCostOverview {
    currency: CurrencyCode
    totalMonthlyCost: Float
    totalAnnualCost: Float
    totalMemberDeposit: Float
    totalMemberCount: Int
}

type QuotationCountryWiseCostSummary {
    country: CountryCode
    currency: CurrencyCode
    monthly: Float
    annually: Float
    memberDeposit: Float
    memberCount: Int
}

type QuotationCountryWiseCostBreakdown {
    country: CountryCode
    memberDeposit: AmountWithCurrency
    monthly: QuotationCostCalculation
    annually: QuotationCostCalculation
}

type QuotationMemberWiseSummary {
    country: CountryCode,
    currency: CurrencyCode,
    monthlyPayrollCost: Float,
    benefitsCost: Float,
    managementFee: Float,
    totalMonthlyCost: Float,
    totalAnnualCost: Float
}

type QuotationCostCalculation {
    total: QuotationCostEntry
    entryGroups: [QuotationCostEntryGroup]
}

type QuotationCostEntryGroup {
    total: QuotationCostEntry
    entries: [QuotationCostEntry]
}

type QuotationCostEntry {
    name: String
    amountInBillingCurrency: AmountWithCurrency
    amountInLocalCurrency: AmountWithCurrency
}

type AmountWithCurrency {
    amount: Float
    currency: CurrencyCode
}

enum QuotationDiscountPlanType {
    ANNUAL_PAYMENT
}

enum QuotationDiscountType {
    MANAGEMENT_FEE
}
