extend type Query {
    offerings: [Offering] @authorize(operations: ["view.operations.company"], company: ["view.company.company"])
}

type CompanyOffering @key(fields: "id") {
    id: ID
    companyId: ID
    offering: OfferingCode
    status: OfferingStatus
    capabilities: [Capability]
}

input CompanyOfferingsAddInput {
    companyId: ID
    offerings: [CompanyOfferingInput]
}

input CompanyOfferingsUpdateInput {
    companyId: ID
    offerings: [CompanyOfferingInput]
}

input CompanyOfferingInput {
    offering: OfferingCode
    capabilities: [Capability]
}

enum OfferingStatus {
    ONBOARDING
    ACTIVE
}

type Offering {
    code: OfferingCode
    name: String
    capabilities: [Capability]
}

enum Capability {
    EOR
    AOR
    FREELANCER
    GROSS_TO_NET
    SALARY_DISBURSEMENT
    ORG_MANAGEMENT
    TIME_OFF
    TIME_SHEET
    EXPENSES
}
