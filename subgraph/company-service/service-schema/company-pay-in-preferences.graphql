extend type Mutation {
    toggleAutoDebit(companyId: ID!, status: Boolean!): CompanyPayInPreferenceAutoDebit @authorize(company: ["update.company.pay-in-preference"])
    updateAutoDebitSettings(request: AutoDebitPreferenceUpdateRequest) : CompanyPayInPreferenceAutoDebit @authorize(company:["update.company.pay-in-preference"], operations: ["update.operations.pay-in-preference"])
}

input AutoDebitPreferenceUpdateRequest {
    id: ID!
    defaultInvoiceChargeGracePeriodDays: Int
    metadata: KeyValueInput
}

enum CompanyPayInPreferenceType {
    AUTO_DEBIT
}

interface CompanyPayInPreference {
    id: ID!
    company: Company!
    isPreferenceActive: Boolean!
    preferenceType: CompanyPayInPreferenceType!
}

type CompanyPayInPreferenceAutoDebit implements CompanyPayInPreference {
    id: ID!
    company: Company!
    isPreferenceActive: Boolean!
    preferenceType: CompanyPayInPreferenceType!

    # AutoDebit specific data
    isAutoDebitEnabled: Boolean!
    defaultAutoDebitPayInMethod: CompanyPayInMethod
    defaultInvoiceChargeGracePeriod: Int
}
