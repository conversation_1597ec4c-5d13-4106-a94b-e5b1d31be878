extend type Mutation {
    upsertCompanyPayInMethodBankDetails(input: CompanyPayInMethodInput) : CompanyPayInMethod @authorize(company: ["create.company.pay-in-method"], operations: ["update.operations.pay-in-method"])
    uploadSignedAgreement(companyPayInMethodId: ID!, agreement: Upload) : CompanyPayInMethod @authorize(operations: ["update.operations.pay-in-method"]) # agreement could be optional
    enableCompanyPayInMethod(companyPayInMethodId: ID!) : CompanyPayInMethod @authorize(operations: ["update.operations.pay-in-method"])
    markFailCompanyPayInMethod(companyPayInMethodId: ID!, reason: String!) : CompanyPayInMethod @authorize(operations: ["update.operations.pay-in-method"])
    deleteCompanyPayInMethod(companyPayInMethodId: ID!): TaskResponse @authorize(company: ["delete.company.pay-in-method"], operations: ["update.operations.pay-in-method"])
    reInitiateActivation(companyPayInMethodId: ID!, reason: String) : TaskResponse @authorize(operations: ["update.operations.pay-in-method"])
    registerDirectDebitMandate(companyPayInMethodId: ID!) : CompanyPayInMethod @authorize(operations: ["update.operations.pay-in-method"])
    updateCompanyPayInMethodChargeBack(request: UpdateCompanyPayInMethodChargeBackRequest!): CompanyPayInMethod @authorize(operations: ["update.operations.company-payin-chargeback"])
    acceptEgiroTermsAndConditions(input: AcceptEgiroTermsAndConditionsInput!): CompanyPayInMethod @authorize(company: ["create.company.pay-in-method"], operations: ["update.operations.pay-in-method"])
    toggleCompanyPanInPayMethod(request: ToggleCompanyPayInMethodRequest!) : TaskResponse @authorize(operations: ["update.operations.pay-in-method"])
    generateBankAccountVerificationToken(input: CompanyPayInMethodVerificationInput): CompanyPayInMethod  @authorize(company: ["create.company.pay-in-method"])
    processBankAccountVerification(input: CompanyPayInMethodVerificationInput): CompanyPayInMethod @authorize(company: ["create.company.pay-in-method"])
    cancelDirectDebit(input: CancelDirectDebitInput!): CompanyPayInMethod @authorize(company: ["update.company.pay-in-method"], operations: ["update.operations.pay-in-method"])
    sendAgreementDocForCompanySignature(companyPayInMethodId: ID!) : CompanyPayInMethod @authorize(operations: ["update.operations.pay-in-method"])
}

extend type Query {
    getCompanyPayInMethod(id: ID): CompanyPayInMethod @authorize(operations: ["view.operations.pay-in-method"])
    getSEPAIBANDetailsFieldInformation(iban: String): SEPAIBANDetailsFieldInformationResponse @authorize(company: ["create.company.pay-in-method"] , operations: ["update.operations.pay-in-method"], member: [])
}

input CompanyPayInMethodVerificationInput {
    id: ID
    metaData: [KeyValueInput]
}

input CompanyPayInMethodInput {
    id: ID # provide this when updating
    companyId: ID
    payInMethodType: PayInMethodType
    setUpData: CompanyPayInMethodSetUpData
    metaData: [KeyValueInput]
}

input CompanyPayInMethodSetUpData {
    step:  PayInMethodDefinitionStep
    accountRequirements: [AccountRequirementInput]
}

input AccountRequirementInput {
    payInAccountRequirementType: String
    data: [KeyValueInput]
    documents: [DocumentKeyValueInput]
}

interface CompanyPayInMethod implements PayInMethod  @key(fields: "id"){
    id: ID!
    company: Company
    payInMethodType: PayInMethodType
    context: PayInMethodContext
    isActive: Boolean
    isEnabled: Boolean

    # Company Payin Method Specific Data
    currentStep: PayInMethodDefinitionStep
    currentStatus: CompanyPayInMethodWorkflowStatus
    workflowSteps: [CompanyPayInMethodWorkflowStep]!
    bankDetails: [CompanyPayInMethodBankDetails]
    documents: [DocumentData]
    data: [KeyValue]
}

type CompanyPayInMethodBankDetails {
    payInAccountRequirementType: String
    data: [KeyValue]
    dataVerificationDetails: DataVerificationDetails
    documents: [DocumentData]
}

type DataVerificationDetails {
    additionalDetails: [KeyValue]
    fieldDetails: [FieldDetail]
    verifiedBy: VerifiedBy
}

type FieldDetail {
    key: String
    values: [KeyValue]
}

enum VerifiedBy {
    PLAID
    MULTIPLIER
}

type ACHCompanyPayinMethod implements CompanyPayInMethod & PayInMethod @key(fields: "id") {
    id: ID!
    company: Company
    payInMethodType: PayInMethodType
    context: PayInMethodContext
    isActive: Boolean
    isEnabled: Boolean

    # Company Payin Method Specific Data
    currentStep: PayInMethodDefinitionStep
    currentStatus: CompanyPayInMethodWorkflowStatus
    workflowSteps: [CompanyPayInMethodWorkflowStep]!

    # ACH Company Payin Method Specific Data
    bankDetails: [CompanyPayInMethodBankDetails]
    documents: [DocumentData]
    data: [KeyValue]
    chargeBackPeriod: Int
}

type SEPACompanyPayinMethod implements CompanyPayInMethod & PayInMethod @key(fields: "id") {
    id: ID!
    company: Company
    payInMethodType: PayInMethodType
    context: PayInMethodContext
    isActive: Boolean
    isEnabled: Boolean

    # Company Payin Method Specific Data
    currentStep: PayInMethodDefinitionStep
    currentStatus: CompanyPayInMethodWorkflowStatus
    workflowSteps: [CompanyPayInMethodWorkflowStep]!

    # SEPA Company Payin Method Specific Data
    bankDetails: [CompanyPayInMethodBankDetails]
    documents: [DocumentData]
    data: [KeyValue]
    chargeBackPeriod: Int
}

enum EGIROTermsAndConditionsStatus {
    PENDING
    ACCEPTED
}

type EGIROTermsAndConditions {
    content: String!
    acceptedBy: ID!
    acceptedOn: DateTime
    status: EGIROTermsAndConditionsStatus!
}

type EGIROCompanyPayinMethod implements CompanyPayInMethod & PayInMethod @key(fields: "id") {
    id: ID!
    company: Company
    payInMethodType: PayInMethodType
    context: PayInMethodContext
    isActive: Boolean
    isEnabled: Boolean

    # Company Payin Method Specific Data
    currentStep: PayInMethodDefinitionStep
    currentStatus: CompanyPayInMethodWorkflowStatus
    workflowSteps: [CompanyPayInMethodWorkflowStep]!

    # EGIRO Company Payin Method Specific Data
    bankDetails: [CompanyPayInMethodBankDetails]
    documents: [DocumentData]
    data: [KeyValue]

    termsAndConditions: EGIROTermsAndConditions
    redirectUrl: String
}

type CompanyPayInMethodWorkflowStep implements PayInMethodWorkflowStep {
    step:  PayInMethodDefinitionStep
    status: CompanyPayInMethodWorkflowStatus
    data: [AccountDetails]! @deprecated(reason: "will not be populated as it is unnecessary")
    updatedOn: DateTime
}

type SEPAIBANDetailsFieldInformationResponse {
    data : [KeyValue]
}


enum CompanyPayInMethodWorkflowStatus {
    ACTIVATION_INITIATED @deprecated(reason: "will use ACTIVATION PENDING to represent all the statuses till its activated")
    ACTIVATION_PENDING
    SETUP_INITIATED
    BANK_DETAILS_COLLECTED
    BANK_AUTHORISATION_IN_PROGRESS
    BANK_AUTHORISED
    AGREEMENT_SIGNED
    AGREEMENT_SENT_TO_SIGNATORY
    AGREEMENT_SIGNING_IN_PROGRESS
    APPROVED_BY_CHECKER
    MANDATE_REGISTERED
    MANDATE_SUBMISSION_IN_PROGRESS
    ACTIVATION_IN_PROGRESS
    EXPIRED
    ENABLED
    DELETED
    FAILED
    CANCELLATION_IN_PROGRESS
    CANCELLATION_AUTHORISED
    CANCELLED
    VERIFICATION_IN_PROGRESS
}

input DocumentKeyValueInput {
    key: String!
    documentId: ID
    file: Upload
}

input UpdateCompanyPayInMethodChargeBackRequest {
    companyPayInMethodId: ID!
    chargebackPeriodDays: Int!
    description: String!
}

input AcceptEgiroTermsAndConditionsInput {
    companyPayInMethodId: ID!
}

input ToggleCompanyPayInMethodRequest {
    companyPayInMethodId: ID!
    isEnabled: Boolean!
    reason: String!
}

input CancelDirectDebitInput {
    companyPayInMethodId: ID!
}
