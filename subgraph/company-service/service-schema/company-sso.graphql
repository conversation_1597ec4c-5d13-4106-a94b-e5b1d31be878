extend type Query {
    companySSOConfigs: [CompanySSOConfig!]!
    @authorize(
        company: ["view.company.sso-config"]
    )
}

extend type Mutation {
    initializeCompanySamlConfig: CompanySamlSSOConfig!
    @authorize(
        company: ["create.company.sso-config"]
    )
    updateCompanySamlConfig(input: UpdateCompanySamlSSOConfigInput!): CompanySamlSSOConfig!
    @authorize(
        company: ["create.company.sso-config"]
    )
    toggleCompanySamlConfig(input: ToggleCompanySamlSSOConfigInput!): CompanySamlSSOConfig!
    @authorize(
        company: ["create.company.sso-config"]
    )

    prepareSamlCertUpload(input: PrepareSamlCertUploadInput!): PrepareSamlCertUploadResult!
    @authorize(
        company: ["create.company.sso-config"]
    )

    # removeCompanySamlConfig(id: ID!): CompanySSOSamlConfig!
}

# in the future, add CompanyOidcSSOConfig type to the union
union CompanySSOConfig = CompanySamlSSOConfig

type CompanySamlSSOConfig {
    id: ID!
    enabled: Boolean!
    status: CompanySSOConfigStatus!
    identityProvider: IdentityProvider
    identityProviderMetadataUrl: String
    identityProviderMetadata: IdentityProviderSamlMetadata
    companySigningCertificate: String!
    companyEncryptionCertificate: String!
    serviceProviderEntityId: String!
    assertionConsumerServiceUrl: String!
    registrationId: String!
}

type IdentityProviderSamlMetadata {
    entityId: String!
    ssoServiceUrl: String!
    signingCertificate: CertificateFileData
    encryptionCertificate: CertificateFileData
}

type CertificateFileData {
    blob: String!
    fileName: String!
}

input UpdateCompanySamlSSOConfigInput {
    id: ID!
    identityProvider: IdentityProvider
    enabled: Boolean!
    # metadata url setup
    metadataUrl: String
    # manual setup
    metadata: IdentityProviderSamlMetadataInput
}

input IdentityProviderSamlMetadataInput {
    entityId: String!
    ssoServiceUrl: String!
    signingCertificate: CertificateInput!
    encryptionCertificate: CertificateInput!
}

input CertificateInput {
    isRemovingCertificate: Boolean!
    uploadFileId: String
}

input ToggleCompanySamlSSOConfigInput {
    id: ID!
    enabled: Boolean!
}

input PrepareSamlCertUploadInput {
    fileName: String!
    registrationId: String!
}

type PrepareSamlCertUploadResult {
    uploadUrl: String!
    uploadFileId: String!
}

enum CompanySSOConfigStatus {
    INITIALIZED
    ACTIVE
    ARCHIVED
}
