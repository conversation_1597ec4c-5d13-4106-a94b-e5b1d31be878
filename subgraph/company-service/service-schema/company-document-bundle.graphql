extend type Mutation {
    companyDocumentBundleUpsert(request: CompanyDocumentBundleUpsertRequest): CompanyDocumentBundle @authorize(operations: ["update.operations.company"])
}

enum CompanyDocumentBundleType {
    PRODUCT_CATALOGUE
}

type CompanyDocumentBundleAvailability {
    companyId: ID!
    type: CompanyDocumentBundleType!
    isAvailable: Boolean!
}
type CompanyDocumentBundle {
    companyId: ID!
    type: CompanyDocumentBundleType!
    masterDocument: DocumentReadable!
    subDocuments: [DocumentReadable!]
    status: CompanyDocumentBundleStatus
}

enum CompanyDocumentBundleStatus {
    CREATED,
    UPLOADED,
    GENERATION_IN_PROGRESS,
    GENERATION_SUCCESS,
    GENERATION_FAILED,
    SENT_FOR_SIGNING,
    VIEWED,
    SIGNING,
    DROPPED,
    SIGNED
}

input CompanyDocumentBundleAvailabilityFilters {
    types: [CompanyDocumentBundleType!]!
}

input CompanyDocumentBundleFilters {
    types: [CompanyDocumentBundleType!]!
    statuses: [CompanyDocumentBundleStatus!]
}

input CompanyDocumentBundleUpsertRequest {
    companyId: ID!
    masterDocumentId: ID
    type: CompanyDocumentBundleType!
}
