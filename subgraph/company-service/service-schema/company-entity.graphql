extend type Mutation {
    companyLegalEntityCreate(input: CreateCompanyLegalEntityInput!): LegalEntity @authorize(operations: ["create.operations.company.legal_entity"])
    companyLegalEntityUpdate(input: UpdateCompanyLegalEntityInput!): LegalEntity @authorize(operations: ["update.operations.company.legal_entity"])
    companyLegalEntityActivate(id: ID!): LegalEntity  @authorize(operations: ["update.operations.company.legal_entity"])
    companyLegalEntityCopyCountryHolidays(id: ID!, year: Int!) : TaskResponse @authorize(operations: ["update.operations.company"])
}

input CreateCompanyLegalEntityInput {
    companyId: ID!
    legalName: String!
    address: AddressInput!
    payrollData: [DataFieldInput!]
    offeringCodes: [OfferingCode!]
    capabilities: [Capability!]
}

input UpdateCompanyLegalEntityInput {
    id: ID!
    legalName: String!
    address: AddressInput!
    payrollData: [DataFieldInput!]
    offeringCodes: [OfferingCode!]
    capabilities: [Capability!]
}

input DataFieldInput {
    key: String
    value: String
    documents: [Upload!]
}
