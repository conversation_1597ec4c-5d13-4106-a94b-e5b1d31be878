type Contract @key(fields: "id") @extends {
    id: ID @external
}

type Request @key(fields: "id") @extends {
    id: String @external
}

type DiscountTerm @key(fields: "id") @extends {
    id: ID @external
}

type OperationsUser implements Person @key(fields: "id") @extends {
    id: ID @external
    persona: Persona @external
    firstName: String @external
    lastName: String @external
    userId: String @external
    emails: [EmailAddress] @external
    phoneNos: [PhoneNumber] @external
}

# TODO Remove this hierarchy of PricingInput after decoupling company from pricing
# =============== Begin =============== #
input PricingInput {
    services: String!
    setupFee: Float!
    serviceFee: Float   #deprecated will be removed after UI changes are done
    deposit: Float
    resourceClaim: String   #deprecated will be removed after UI changes are done
    billingCurrencyCode: CurrencyCode
    paymentTermInDays: Int   # period from invoice generation date to due date
    depositTerm: DepositTermInput
    discountTerms: [DiscountTermInput]!
    customDiscountTerm: String
    globalPricing: [CompanyGlobalPricingInput]
    visaGlobalPricing: [CompanyVisaGlobalPricingInput]
    employeePricing: [CompanyEmployeePricingInput]!
    visaPricing: [CompanyVisaPricingInput]
    offboardingGlobalPricing: [OffboardingGlobalPricingInput!]
}

input DepositTermInput {
    termInMonths: Int
    maxDeposit: Float
}

input DiscountTermInput {
    discount: Float!
    discountType: DiscountType!
    discountRules: [DiscountRuleInput]
}

input CompanyEmployeePricingInput {
    region: Region
    country: CountryCode
    employeeType: ContractType!
    fixedRate: Float!
}

input CompanyVisaPricingInput {
    country: CountryCode
    employeeType: ContractType!
    fixedRate: Float!
    validUntil: Date
}

input CompanyGlobalPricingInput {
    employeeType: ContractType!
    fixedRate: Float!
}

input CompanyVisaGlobalPricingInput {
    employeeType: ContractType!
    fixedRate: Float!
}

input DiscountTermInput {
    discount: Float!
    discountType: DiscountType!
    discountRules: [DiscountRuleInput]
}

input DiscountRuleInput {
    memberBasedDiscount: DiscountRuleMemberBasedInput
    salaryBasedDiscount: DiscountRuleSalaryBasedInput
    timeBasedDiscount: DiscountRuleTimeBoundInput
    countryBasedDiscount: DiscountRuleCountryInput
    regionBasedDiscount: DiscountRuleRegionInput
    deadlineBasedDiscount: DiscountRuleDeadlineBasedInput
    globalDiscount: Boolean
}

input DiscountRuleSalaryBasedInput {
    salaryLowerBound: Float
    salaryUpperBound: Float
}

input DiscountRuleMemberBasedInput {
    memberLowerBound: Int
    memberUpperBound: Int
}

input DiscountRuleTimeBoundInput {
    fromMonth: Int
    toMonth: Int
}

input DiscountRuleCountryInput {
    anyCountry: Boolean!
    countries: [CountryCode]!
    excludedCountries: [CountryCode!]
}

input DiscountRuleRegionInput {
    region: Region
}

input DiscountRuleDeadlineBasedInput {
    deadline: Date!
    deadlineType: DiscountRuleDeadlineType!
}

input OffboardingGlobalPricingInput {
    employeeType: ContractType!
    offboardingFeeType: OffboardingFeeType!
    status: OffboardingFeeStatus!
}

enum DiscountType {
    """The 'discount' field contains the percentage value. i.e. fee = 500, discount 50, discounted value = 250 [500 - (500 * 50%)]"""
    PERCENT
    """The 'discount' field contains the absolute discount value. i.e. fee = 500, discount 50, discounted value = 450 [500 - 50]"""
    ABSOLUTE
    """The 'discount' field contains the flat price value. i.e. fee = 500, discount 50, discounted value = 50 [regardless the fee]"""
    FLAT_PRICE
}

enum Region {
    ASIA
    AFRICA
    # TODO: @Teja to confirm
}

enum DiscountRuleDeadlineType {
    MSA_SIGNED
    MEMBER_ONBOARDED
}

enum OffboardingFeeType {
    STANDARD,
    COMPLEX
}

enum OffboardingFeeStatus {
    ACTIVE,
    INACTIVE
}
# =============== End =============== #
