
extend type Mutation {
    backFillCompanyPaymentAccount(companyIds: [ID]) : TaskResponse @authorize(operations: ["backfill.operations.company"])
}


type CompanyPayInSettings {
    company: Company
    payInMethodRequirements : [PayInMethodRequirement]
    payInMethods (filters: PayInMethodFilters): [CompanyPayInMethod]!
    payInPreferences: [CompanyPayInPreference]
}

input PayInMethodFilters {
    isActive: Boolean
    isEnabled: Boolean
    payInMethodTypes : [PayInMethodType]
}
