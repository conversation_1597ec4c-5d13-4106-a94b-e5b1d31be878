extend type Query {
    # TODO : Filters {userId} to be implemented.
    company(id: ID, userId: ID): Company @authorize(member: ["view.member.company"],operations: ["view.operations.company"],company: ["view.company.company"])

    # All the companies will be returned for the operations users
    companies(filters: CompanyFilters): [Company] @authorize(operations: ["view.operations.company"])

    companyNames: [Company] @authorize(operations: ["view.operations.company"])

    companiesWithPagination(filters: CompanyFilters,
        pageRequest: PageRequest,    # supportes sorting on ["createdOn", "updatedOn"]. default = "createdOn" (descending)
        countRequest: CompaniesCountRequest
    ) : CompaniesResult @authorize(operations: ["view.operations.company"])

    companyHasDuplicatedDomain(id: ID): <PERSON>olean @authorize(operations: ["view.operations.company"], company: ["view.company.company"])
    getCompanyPrefillData(input: CompanyPreFillDataInput) : CompanyPreFillData @authorize(company: ["prefill.company.zombie"])
    companyPrePopulatedPaySupplementReport(id: ID): DocumentReadable @authorize(company: ["view.company.paysupplement.report"])
    companyRoles: [CompanyRoleResult]  @authorize(company: ["view.company.company"], operations: ["view.operations.company"])
    companyDocumentFolder(input: CompanyDocumentFolderInput!): String  @authorize(operations: ["view.operations.company"])
    companyDocumentFolders(input: CompanyDocumentFoldersInput!): [String]  @authorize(operations: ["view.operations.company"])
}

extend type Mutation {
    # TODO Refactor this mutation to decouple from pricing i.e. PricingInput
    #  because company is parent, pricing is child; pricing should depend on company, not the other way around.
    companyCreate(company: CreateCompanyInput, companyLogo: Upload, isTest: Boolean): Company @authorize(operations: ["create.operations.company"], company: ["create.company.zombie"])

    companyUpdate(company: CompanyUpdateInput, companyLogo: Upload): Company @authorize(operations: ["update.operations.company"], company: ["update.company.company-details"]) # CompanyUsers can call this
    companyInactivate(companyId: ID!, inactivateReason: CompanyInactiveReason): Company @authorize(operations: ["inactivate.operations.company | update.operations.company.deactivation"]) # Only OpsAdmin will be able to call this

    companyInvite(companyId: ID!, companyUserIds: [ID]): Company @authorize(operations: ["invite.operations.company.users"],member: [], company: []) #Sends invite to each and every (SUPER)ADMINs for initial login if only companyId is provided, otherwise only to the mentioned user
    companyAcceptInvite(companyId: ID): Company @authorize(operations: ["update.operations.company"], member: [], company: ["update.company.company"])
    companyUserAcceptInvite(companyUserId: ID): CompanyUser @authorize(operations: ["update.operations.company.users"], member: [], company: ["invite.company.user.accept"])
    companyUserAdd(user: AddCompanyUserInput): CompanyUser @authorize(operations: ["create.operations.company.users"], company: ["create.company.users"] )#Sends invite to user if the company's status is not in CREATED
    companyUpdateUser(user: UpdateUserInput ): CompanyUser @authorize(operations: ["update.operations.company.users"], company: ["update.company.users"])
    companyRemoveUser(companyUserId: ID!): CompanyUser @authorize(company: ["delete.company.users"], operations: ["delete.operations.company.users"])
    """This removes only the SIGNATORY role. Details: https://app.clickup.com/t/865cghnvm"""
    companyRemoveSignatory(companyUserId: ID!): CompanyUser @authorize(company: ["delete.company.users"], operations: ["delete.operations.company.users"], member: [])

    companyUsersAdd(users: [AddCompanyUserInput]): [CompanyUser] @authorize(operations: ["create.operations.company.users"],company: ["create.company.users"] )
    companyUpdateUsers(users: [UpdateUserInput] ): [CompanyUser] @authorize(operations: ["update.operations.company.users"], company: ["update.company.users"]) # Update User cannot be used to remove any permissions! Please use appropriate mutations for that.
    companyRemoveUsers(companyUserIds: [ID!]): [CompanyUser] @authorize(operations: ["delete.operations.company.users"], company: ["delete.company.users"]) # Set of successfully removed company users will be returned

    companyUserAddApproverInfo(input: AddApproverInfoInput!): CompanyUser @authorize(operations: ["create.operations.approver"], company: ["create.company.approver"])   # Add approver details into the company user.
    companyUserUpdateApproverInfo(input: UpdateApproverInfoInput!): CompanyUser @authorize(operations: ["update.operations.approver"], company: ["update.company.approver"]) # Update approver details of the company user.
    companyUserRevokeApproverRole(companyUserId: ID!): CompanyUser  @authorize(operations: ["revoke.operations.approver"], company: ["revoke.company.approver"])              # Remove manager/approver role from the company user.
    companyUserResendApproverInvite(companyUserId: ID!): CompanyUser  @authorize(operations: ["resend.operations.approver.invite"], company: ["resend.company.approver.invite"])     # Resend invite email the manager/approver.
    addOrAssociatePartnerCompanyUser: TaskResponse @authorize(company: ["*.company.zombie|add-associate.company.company-partner-user"], member: [], operations: [])                           # Add/associate company user for partner user who is coming from partner portal like Trinet, etc.

    companyMSARequest : Request @authorize(operations: ["request.company.msa"], company: ["request.company.msa"])
    companyMsaUpload(companyId: ID!, file: Upload!): Company @authorize(operations: ["upload.operations.company.msa"])
    companyGenerateMSA(companyId: ID!): Company @authorize(operations: ["generate.operations.company.msa"])
    companyMSASendForSignature(companyId: ID!): FileLink @authorize(operations: ["sendforsignature.operations.company.msa"])
    companyUpdateBasicDetails(input: CompanyUpdateBasicDetailsInput!, companyLogo: Upload): Company @authorize(operations: ["update.operations.company.basicdetails"], member: [], company: []) # Only ops can call this - sales, etc..
    assignOperationUsers(companyId: ID!, csmUserId: ID, salesUserId:ID, accountManagerUserId: ID): Company  @authorize(operations: ["assign.operations.company.operationusers"])
    companyUpdateContactDetails(input: CompanyUpdateContactDetailsInput!): Company @authorize(operations: ["update.operations.company.contactdetails"],member: [], company: []) # Only ops can call this - sales, etc..
    companyBackfillPricing(pricing: Upload!, employeePricing: Upload!): TaskResponse @authorize(operations: ["update.operations.company.pricing"], member: [], company: [])
    companyBackfillEmployeePricing(employeePricing: Upload!): TaskResponse @authorize(operations: ["update.operations.company.pricing"], member: [], company: [])
    companyOfferingsAdd(input: CompanyOfferingsAddInput): [CompanyOffering] @authorize(operations: ["update.operations.company"], member: [], company: [])
    companyOfferingsUpdate(input: CompanyOfferingsUpdateInput): [CompanyOffering] @authorize(operations: ["update.operations.company"], member: [], company: [])
    backFillKybForCompanies(companyIds: [ID]!) : TaskResponse @authorize(operations: ["update.operations.kyb"],company: [], member: [])
    backfillHrisManagersFromCompanyUsers(companyIds: [ID!]): TaskResponse   @authorize(operations: ["backfill.operations.hris"],company: [], member: [])    # companyIds: null/empty indicates all companies. Details: https://app.clickup.com/t/86cua75mu

    companyUserAssignRole(input: CompanyUserAssignRoleInput): [CompanyUser] @authorize(operations: ["create.operations.company.users"], company: ["create.company.users"])
    companyUserAssignRoleAndAttributes(input: CompanyUserAssignRoleAndAttributesInput): CompanyUser @authorize(operations: ["create.operations.company.users"], company: ["create.company.users"])     # assign the given role and attributes to a company user
    companyUserEditRoleAndAttributes(input: CompanyUserEditRoleAndAttributesInput): CompanyUser @authorize(operations: ["update.operations.company.users"], company: ["update.company.users"])   # edit the role and attributes of a company user
    companyUserRevokeRole(companyUserId: ID!, role: CompanyUserRole): CompanyUser @authorize(operations: ["delete.operations.company.users"], company: ["delete.company.users"])   # revoke the given role from the company user
    migrateUserToNewRoles(file: Upload!): String @authorize(operations: ["update.operations.company.users"])
    companyUserAddAndAssignAttributes(input: CompanyUserAddAndAssignAttributes): CompanyUser @authorize(operations: ["create.operations.company.users"], company: ["create.company.users"])     # assign the given role and attributes to a company user
}

type Company @key(fields: "id") {
    id: ID
    displayName: String
    status: CompanyStatus
    fxSensitivity: FxSensitivity
    financialYear: Int # Starts from January - 1
    primaryEntity: LegalEntity @authorize(company:["view.company.company"], operations: ["view.operations.company"])
    otherEntities(legalEntityId: ID): [LegalEntity] @authorize(company:["view.company.company"], operations: ["view.operations.company.legal_entity"], member: ["view.member.company.otherEntities"])
    gpEnabledEntities: [LegalEntity] @authorize(company:["view.company.company"], operations: ["view.operations.company.legal_entity"], member: ["view.member.company.legal_entity"])
    managers (userId: ID): [CompanyUser] @authorize(company:["view.company.company"], operations: ["view.operations.company"])
    companyUsers(statuses: [CompanyUserStatus!]): [CompanyUser!] @authorize(company:["view.company.company"], operations: ["view.operations.company"]) # if statuses [] or null, all except DELETED
    signatories: [CompanyUser] @authorize(company:["view.company.company"], operations: ["view.operations.company"])
    billingContact: CompanyUser  @authorize(company:["view.company.company"], operations: ["view.operations.company"] )
    msa: DocumentReadable @authorize(company:["view.company.msa"], operations: ["view.operations.company.msa"] )
    onboarding: CompanyOnboarding @authorize(operations: ["view.operations.company"], company: ["view.company.company"])
    channel: CompanyChannel
    managementFees(contractFilters: ContractFilters monthYear: MonthYearInput): [ManagementFee] @authorize(operations: ["view.operations.company"], company: ["view.company.company"])
    companyLogo: DocumentReadable # there is a child fetcher for `companyLogo`. Should fetch it ONLY if needed. Never fetch it in a "list of companies" query!
    companyLogoId: ID
    domain: String
    inactiveReason: CompanyInactiveReason
    isTest: Boolean
    createdOn: DateTime
    updatedOn: DateTime
    payrollCutOffDate: Int @authorize(company:["view.company.company"], operations: ["view.operations.company"], member: ["view.member.company"]) @deprecated(reason: "Should use currentMonthPayrollCutoffDate or nextPayrollCutoffDate")
    currentMonthPayrollCutoffDate: Date @authorize(company:["view.company.company"], operations: ["view.operations.company"], member: ["view.member.company"])
    nextPayrollCutoffDate: Date @authorize(company:["view.company.company"], operations: ["view.operations.company"], member: ["view.member.company"])
    sfdcAccountNo: String
    msaAddendums (filters: MsaAddendumFilters!): [MsaAddendum] @authorize(company: ["view.company.payables"], operations: ["view.operations.payables"], member: [])
    payInSettings: CompanyPayInSettings # for company related payIn settings
    offerings: [CompanyOffering]
    ssoPreference: [SSOPreference]  @authorize(company: ["get.company.preferences"],operations: [],member: [])# only active sso preference + all previous configurations for other providers
    companyDocumentBundleAvailabilities(filters: CompanyDocumentBundleAvailabilityFilters!): [CompanyDocumentBundleAvailability!]
    companyDocumentBundles(filters: CompanyDocumentBundleFilters!): [CompanyDocumentBundle!]
}

# This will be needed in future for us to be able to allow reporting strucutre.
type CompanyUser implements Person @key(fields: "id") {
    id: ID
    persona: Persona
    userId: String
    firstName: String
    lastName: String
    title: String
    status: CompanyUserStatus
    hasLogin: Boolean
    company: Company
    emails: [EmailAddress]
    phoneNos: [PhoneNumber]
    role: CompanyUserRole       @deprecated(reason: "Use 'roles' instead. Now compnay user can have multiple roles: Manager, Admin, etc.")
    isSignatory: Boolean        @deprecated(reason: "Use 'capabilities' instead. Will be removed by 1st of December 2021")
    isBillingContact: Boolean   @deprecated(reason: "Use 'capabilities' instead. Will be removed by 1st of December 2021")
    capabilities: [CompanyUserCapability]
    roles: [CompanyUserRole]
    rolesWithAttributes: [CompanyUserRoleWithAttributes]
}

type CompanyUserRoleWithAttributes {
    role: CompanyUserRole
    legalEntityIds: [ID]
    departmentIds: [ID]
}


type CompanyPreFillData {
    name: String
    domain: String
    logo: DocumentReadable
}

type CompanyOnboarding {
    msaWorkflow: MSAWorkflow
    hasDataChanged: Boolean
    msaGenerationRequirement: [String]
}

type MSAWorkflow {
    currentStep: MSAWorkflowStep
    currentStatus: MSAWorkflowStatus
    steps: [MSAWorkflowStep]
}

type ManagementFee {
    contract: Contract
    originalFee: Float
    discountedFee: Float
    appliedDiscount: String
    calculatedDateTime: DateTime
}

input CompanyUserEditRoleAndAttributesInput {
    companyUserId: ID
    role: CompanyUserRole
    legalEntityIds: [ID]
    departmentIds: [ID]
}

input CompanyUserAssignRoleAndAttributesInput {
    contractId: ID
    companyUserId: ID
    role: CompanyUserRole!
    legalEntityIds: [ID]
    departmentIds: [ID]
    isBillingContact: Boolean
}

input CompanyUserAddAndAssignAttributes {
    companyId: ID!
    firstName: String
    lastName: String
    email: String! @ValidEmail
    title: String
    role: CompanyUserRole
    capabilities: [CompanyUserCapability]
    """Deprecated. use 'capabilities' instead. Will be removed by 1st of December 2021"""
    isBillingContact: Boolean
    """Deprecated. use 'capabilities' instead. Will be removed by 1st of December 2021"""
    isSignatory: Boolean
    legalEntityIds: [ID]
    departmentIds: [ID]
}

input CompanyUserAssignRoleInput {
    contractIds: [ID!]
    companyUserIds: [ID!]
    role: CompanyUserRole!
    legalEntityIds: [ID!] @deprecated(reason: "use attributes instead")
    attributes: [AttributeInput]
}

input AttributeInput {
    legalEntityId: ID
}

input CompanyPreFillDataInput {
    domain: String!
}

input CompanyDocumentFolderInput {
    companyId: ID!,
    templateType: TemplateType!
}

input CompanyDocumentFoldersInput {
    companyId: ID!,
    templateTypes: [TemplateType!]!
}

input CompanyUpdateContactDetailsInput {
    companyId: ID!
    address: AddressInput!
    phoneNo: String!
}

input CompanyUpdateBasicDetailsInput {
    companyId: ID!
    displayName: String!
    legalName: String!
    registrationNo: String!
    currency: CurrencyCode!
    sfdcAccountNo: String
}

input AddApproverInfoInput {
    # Imagine `company_user.company_id`=100 (correct value) but I provide 101 (wrong value) here -> corrupted data
    # TODO: deprecated. BE is not using this `companyId`. The `company_user.company_id` will be used. To be removed from both BE and FE later
    companyId: ID!
    companyUserId: ID!
    approverInfo: [ApproverInfoInput]
}

input UpdateApproverInfoInput {
    companyUserId: ID!
    approverInfo: [ApproverInfoInput]  # Existing approver info will get replaced with this.
}

input ApproverInfoInput {
    category: ApprovalCategory
    contractIds: [ID]
}

input UpdateUserInput{
    companyUserId: ID!
    firstName: String
    lastName: String
    title: String
    role: CompanyUserRole
    """Deprecated. use 'companyUser[Assign|Revoke]Capabilities' instead. Will be removed by 1st of December 2021"""
    isBillingContact: Boolean
    """Deprecated. use 'companyUser[Assign|Revoke]Capabilities' instead. Will be removed by 1st of December 2021"""
    isSignatory: Boolean
}

input CreateCompanyInput {
    displayName: String!
    legalName: String!
    currency: CurrencyCode!
    registrationNo: String # Non compulsory for both sales channel and organic channel
    countryCode: CountryCode
    phoneNo: String
    address: AddressInput
    salesUserId: ID # Non compulsory for organic channel
    csmId: ID # Optional customer success manager id
    pricing: PricingInput
    sfdcAccountNo: String
    fxSensitivity: FxSensitivity
}

input CompanyUpdateInput{
    companyId: ID
    displayName: String
    phoneNo: String
    address: AddressInput
    financialYear: Int # Starts from January as 1
    sfdcAccountNo: String
    fxSensitivity: FxSensitivity
}

input AddCompanyUserInput {
    companyId: ID!
    firstName: String
    lastName: String
    email: String! @ValidEmail
    title: String
    role: CompanyUserRole
    capabilities: [CompanyUserCapability]
    """Deprecated. use 'capabilities' instead. Will be removed by 1st of December 2021"""
    isBillingContact: Boolean
    """Deprecated. use 'capabilities' instead. Will be removed by 1st of December 2021"""
    isSignatory: Boolean
}

enum CompanyChannel {
    ORGANIC
    SALES
}

enum CompanyInactiveReason {
    LOST
    CHURN
    DISQUALIFIED
    DUPLICATE
    INVALID
    PASSIVE
    NON_RESPONSIVE
}

type CompaniesResult {
    data: [Company]
    pageResult: PageResult
    countResult: CompaniesCountResult
}

input CompaniesCountRequest {
    active: Boolean
    inactive: Boolean
    pending: Boolean
}

type CompaniesCountResult {
    active: Int
    inactive: Int
    pending: Int
}

type CompanyRoleResult {
    name: String
    helpPageUrl: String
}

type CompanyGLCode @key(fields: "id") {
    id: ID                                      # Auto incremental
    code: String                                # The "GL Code" or "Cost Account". Usually an id/code-like string depending on each company's external tool. E.g. "1234", "SOME_CODE", "ABC123"...
    name: String                                # The "GL Account Name" or "Cost Account Name". E.g.: "Travel cost", "Other expenses"
    companyId: ID
    entityId: ID
}

enum TemplateType {
    TEMPLATE_TYPE_UNSPECIFIED
    CONTRACT_EOR
    CONTRACT_EOR_FIXED
    CONTRACT_FREELANCE
    COMPANY_MSA
    PAYROLL_REPORT
    MEMBER_LEGAL_DOCUMENT
    CONTRACT_HR_MEMBER_CUSTOM
    CONTRACT_FREELANCE_CUSTOM
    EXPENSE_RECEIPT
    PAYROLL_SALARY_CALCULATION
    PAYROLL_PAYSLIP
    CONTRACT_OFFER_LETTER
    UNKNOWN
    PERFORMANCE_REVIEW
    SALARY_REVIEW
    CONTRACT_ESOP
    CONTRACT_FREELANCE_RECURRING
    CONTRACT_ADDENDUM
    COMPANY_MSA_ADDENDUM
    CONTRACT_CONTRACTOR_ORDER_FORM
    CONTRACT_CONTRACTOR
    PAYROLL_INPUT
    COMPANY_PAY_IN_DOCUMENT_WITH_MSA
    COMPANY_PAY_IN_DOCUMENT_WITHOUT_MSA
    CONTRACT_QUESTIONNAIRE
    COMPANY_MSA_WITH_ORDER_FORM,
    CONTRACT_CONTRACTOR_PRE_SIGNED,
    RELIEVING_LETTER,
    COMPANY_DOCUMENT_BUNDLE,
    RESIGNATION_LETTER,
    OFFBOARDING_WET_INK_LETTER
}
