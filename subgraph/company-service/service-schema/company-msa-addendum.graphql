extend type Query {
    msaAddendumDraftSample(msaAddendumInput: MsaAddendumInput!): Msa<PERSON><PERSON>du<PERSON> @authorize(operations: ["view.operations.company.pricing"], member: [], company: [])
}

extend type Mutation {
    msaAddendumDraftDelete(companyId: ID!): ID @authorize(operations: ["update.operations.company.pricing|update.operations.payables"], member: [], company: [])
    msaAddendumDraftEdit(msaAddendumInput: MsaAddendumInput!): MsaAddendum @authorize(operations: ["update.operations.company.pricing|update.operations.payables"], member: [], company: [])
    msaAddendumDocumentGenerate(msaAddendumId: ID!): Msa<PERSON>ddendum @authorize(operations: ["update.operations.company.pricing|update.operations.payables"], member: [], company: [])
    msaAddendumDocumentSendForSignature(msaAddendumId: ID!): MsaA<PERSON>dum @authorize(operations: ["update.operations.company.pricing|update.operations.payables"], member: [], company: [])
    msaAddendumDocumentRevoke(msaAddendumId: ID!): MsaAddendum @authorize(operations: ["update.operations.company.pricing|update.operations.payables"], member: [], company: [])
    msaAddendumConfirm(msaAddendumId: ID!): MsaAddendum @authorize(operations: ["update.operations.company.pricing|update.operations.payables"], member: [], company: []) # To manually update pricing
    msaAddendumUploadCustom(companyId: ID!, file: Upload!): MsaAddendum @authorize(operations: ["update.operations.company.pricing|update.operations.payables"], member: [], company: []) # Upload Custom Msa Addendum for a company
}

type MsaAddendum {
    id : ID
    addendumDocId: ID
    document : DocumentReadable @authorize(company: ["view.company.payables"], operations: ["view.operations.payables"], member: [])
    msaAddendumChanges: [MsaAddendumChange] @authorize(company: ["view.company.payables"], operations: ["view.operations.payables"], member: [])
    msaAddendumChange: MsaAddendumChange @authorize(company: ["view.company.payables"], operations: ["view.operations.payables"], member: [])
    status: MsaAddendumStatus
    operationType: MsaAddendumOperationType
    createdOn : DateTime
    updatedOn : DateTime
}

type MsaAddendumChange {
    fieldName: MsaDataChangeFieldName
    paymentTermInDays: MsaAddendumChangePaymentTermInDays
    customDiscountTerm: MsaAddendumChangeCustomDiscountTerm
    employeePricing: MsaAddendumChangeEmployeePricing
    discountTerms: MsaAddendumChangeDiscountTerms
    globalFreelancerPricing: MsaAddendumChangeGlobalFreelancerPricing
    pricingPlan: MsaAddendumChangePricingPlan
}

type MsaAddendumChangeCustomDiscountTerm {
    oldValue: String
    newValue: String
}

type MsaAddendumChangeDiscountTerms {
    oldValue: [DiscountTerm]
    newValue: [DiscountTerm]
}

type MsaAddendumChangeEmployeePricing {
    oldValue: [EmployeePricing]
    newValue: [EmployeePricing]
}

type MsaAddendumChangeGlobalFreelancerPricing {
    oldValue: Float
    newValue: Float
}

type MsaAddendumChangePricingPlan {
    oldId: Int
    newId: Int
}

type MsaAddendumChangePaymentTermInDays {
    oldValue: Int
    newValue: Int
}

interface EmployeePricing {
    id: ID!
    employeeType: ContractType
    fixedRate: Float
}

type CountryEmployeePricing implements EmployeePricing {
    id: ID!
    employeeType: ContractType
    fixedRate: Float
    country: CountryCode
}

type RegionEmployeePricing implements EmployeePricing {
    id: ID!
    employeeType: ContractType
    fixedRate: Float
    region: Region
}

type VisaEmployeePricing implements EmployeePricing {
    id: ID!
    employeeType: ContractType
    fixedRate: Float
    country: CountryCode
    validUntil: Date
}

enum MsaAddendumOperationType {
    DEFAULT,
    CUSTOM,
}

input MsaAddendumInput {
    companyId: ID!
    pricingInput: PricingInput
}

input MsaAddendumFilters {
    statuses: [MsaAddendumStatus!]!
}

enum MsaDataChangeFieldName {
    PAYMENT_TERM_IN_DAYS,
    CUSTOM_DISCOUNT_TERM,
    EMPLOYEE_PRICING,
    DISCOUNT_TERMS,
    GLOBAL_FREELANCER_PRICING,
    PRICING_PLAN
}

enum MsaAddendumStatus {
    ADDENDUM_DRAFT
    ADDENDUM_GENERATED
    ADDENDUM_SENT_FOR_SIGNING
    ADDENDUM_SIGNED_BY_MULTIPLIER
    ADDENDUM_SIGNED_BY_CUSTOMER
    ADDENDUM_COMPLETED
    ADDENDUM_DELETED
}
