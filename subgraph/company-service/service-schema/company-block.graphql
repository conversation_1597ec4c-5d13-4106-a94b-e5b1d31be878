extend type Query {
    getCompanyBlockingRules(companyId: ID!): [BlockingRule!]!  @authorize(member: ["view.member.company"], company: ["view.company.company"])
    getCompanyBlockingRuleMessage(id: ID!): CompanyBlockingRuleMessage @authorize(operations: ["view.operations.company"])
    getCompanyBlockingRuleMessagesByBlockReasonType(blockReasonType: BlockReasonType!): [CompanyBlockingRuleMessage!]! @authorize(operations: ["view.operations.company"])
}

extend type Mutation {
    createCompanyBlockingRuleMessage(input: CompanyBlockingRuleMessageCreateInput!): CompanyBlockingRuleMessage! @authorize(operations: ["view.operations.company"])
    updateCompanyBlockingRuleMessage(input: CompanyBlockingRuleMessageUpdateInput!): CompanyBlockingRuleMessage! @authorize(operations: ["view.operations.company"])
    deleteCompanyBlockingRuleMessage(id: ID!): Boolean! @authorize(operations: ["view.operations.company"])
}

enum BlockType {
    GLOBAL
    MODULE_ONLY
}

enum BlockLevel {
    HIDE
    DISABLE
    FULL_BLOCK
}

enum Audience {
    ADMINS
    MEMBERS
    ALL
}

enum BlockReasonType {
    PENDING_ACH_DD_SETUP
}

enum BlockingStatus {
    ACTIVE
    INACTIVE
}

type BlockingRule {
    id: ID!
    companyId: ID!
    startTime: DateTime
    endTime: DateTime
    blockType: BlockType!
    blockLevel: BlockLevel!
    module: String
    audience: Audience!
    blockReasonType: BlockReasonType!
    actionRedirectUrl: String
    status: BlockingStatus!
    createdAt: DateTime!
    updatedAt: DateTime!
    createdBy: String!
    updatedBy: String!
    messages: [BlockingMessage!]!
}

type BlockingMessage {
    id: ID!
    audience: Audience!
    title: String!
    description: String!
    actionLabel: String
    actionRedirectUrl: String
    createdAt: DateTime!
    updatedAt: DateTime!
    createdBy: String!
    updatedBy: String!
}

type BlockingResponse {
    isBlocked: Boolean!
    blockLevel: BlockLevel
    activeRules: [BlockingRule!]!
    messages: [BlockingMessage!]!
}
# Company Blocking Rule Message Types
type CompanyBlockingRuleMessage {
    id: ID!
    blockReasonType: BlockReasonType!
    audience: Audience!
    title: String!
    description: String!
    actionLabel: String
    actionRedirectUrl: String
    createdAt: DateTime!
    updatedAt: DateTime!
    createdBy: String!
    updatedBy: String!
}

# Input types for mutations
input CompanyBlockingRuleMessageCreateInput {
    blockReasonType: BlockReasonType!
    audience: Audience!
    title: String!
    description: String!
    actionLabel: String
    actionRedirectUrl: String
}

input CompanyBlockingRuleMessageUpdateInput {
    id: ID!
    blockReasonType: BlockReasonType
    audience: Audience
    title: String
    description: String
    actionLabel: String
    actionRedirectUrl: String
}
