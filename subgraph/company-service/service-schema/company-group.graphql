extend type Query {
    companyGroup(id: ID, userId: ID) : [CompanyGroup] @authorize(operations: ["view.operations.company.company-group"], member: ["view.member.company.company-group"], company: ["view.company.company.company-group"])
}

extend type Mutation {
    companyGroupCreate(input: CompanyGroupCreateInput!) : CompanyGroup @authorize(operations: ["create.operations.company.company-group"],member: [], company: [])
    companyGroupUpdate(id: ID, input: CompanyGroupUpdateInput!) : CompanyGroup @authorize(operations: ["update.operations.company.company-group"],member: [], company: [])

    companyGroupCompanyAdd(input: CompanyGroupAddRemoveCompanyInput) : CompanyGroup @authorize(operations: ["add.operations.company.company-group.company"],member: [], company: [])
    companyGroupCompanyRemove(input: CompanyGroupAddRemoveCompanyInput) : CompanyGroup @authorize(operations: ["remove.operations.company.company-group.company"],member: [], company: [])

    companyGroupUserAdd(input: CompanyGroupAddRemoveUserInput): CompanyGroup @authorize(operations: ["add.operations.company.company-group.user"],member: [], company: [])
    companyGroupUserRemove(input: CompanyGroupAddRemoveUserInput): CompanyGroup  @authorize(operations: ["remove.operations.company.company-group.user"],member: [], company: [])

}



# --------------------------------------
# Query types....
# --------------------------------------

type CompanyGroup {
    id: ID!
    name: String
    children: [Company]
}

# --------------------------------------
# Mutation types....
# --------------------------------------

input CompanyGroupNodeInput {
    id: ID
}

input CompanyGroupUserInput {
    userId: ID!
}

input CompanyGroupCreateInput {
    name: String!
    children: [CompanyGroupNodeInput!]
    users: [CompanyGroupAddUserInput!] #deprecated
    userList: [CompanyGroupUserInput!]
}

input CompanyGroupUpdateInput {
    id: ID
    name: String
}

input CompanyGroupAddRemoveCompanyInput {
    id: ID
    children: [CompanyGroupNodeInput!]
}

input CompanyGroupAddRemoveUserInput {
    id: ID
    users: [CompanyGroupUserInput!]
}


# --------------------------------------
# Deprecated...
# --------------------------------------

input CompanyGroupAddUserInput {
    userId: ID!
    roles: [CompanyGroupRole]
}

input CompanyGroupAddCompanyInput {
    id: ID
    children: [CompanyGroupNodeInput!]
    users: [CompanyGroupAddUserInput!]
}

enum CompanyGroupRole {
    COMPANY_GROUP_PRIMARY_ADMIN
}
