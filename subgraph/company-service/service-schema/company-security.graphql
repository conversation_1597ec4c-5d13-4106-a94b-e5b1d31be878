extend type Mutation {
    upsertSSOPreferenceForCompany(companyId: ID!, input: UpsertSSOPreferenceInput!): SSOPreference @authorize(company: ["update.company.preferences"],operations: [],member: [])
}

input UpsertSSOPreferenceInput {
    id: ID
    identityProvider: IdentityProvider!
    enabled: Boolean!
    #configuration: object # TODO: add this when supported
}

type SSOPreference {
    id: ID!
    company: Company
    identityProvider: IdentityProvider
    enabled: Boolean
    #configuration: object # this will house extra preferences like exclusions, etc.
}
