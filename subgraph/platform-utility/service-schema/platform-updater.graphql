extend type Mutation {
    platformUpdate(input: PlatformUpdateInput!): PlatformUpdateTaskResponse!
    platformBatchedUpdate(input: PlatformBatchedUpdateInput!): [PlatformUpdateTaskResponse!]!
}

extend type Query {
    platformUpdaterJobs(input: PlatformJobMetaDataFilter!, pageRequest: PageRequest): JobMetaDataPageResult!
}

type PlatformUpdateTaskResponse {
    success: Boolean,
    request: String,
    message: String,
    ticketId: String,
    id: ID
}

type JobMetaDataPageResult {
    data: [JobMetaData],
    page: PageResult
}

type JobMetaData {
    action: String!
    description: String!
    ownerBy: String!
    inputTypes: [KeyValue]
}


input PlatformJobMetaDataFilter {
    ownerBy: String,
    action: [String!],
}

input PlatformUpdateInput {
    ticketId: String!
    action: String!
    payload: String!
}

input PlatformBatchedUpdateInput {
    ticketId: String!
    action: String!
    payload: [String!]!
}

