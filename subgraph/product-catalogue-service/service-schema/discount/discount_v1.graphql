

input OrderDiscountInput {
    id: ID
    productDiscounts: [ProductDiscountInput]
    customTerm: String
}

input ProductDiscountInput {
    type: ProductDiscountType!
    discountValue: Float
    terms: [ProductDiscountRulesInput]
}

input ProductDiscountTermInput {
    rules: [ProductDiscountRulesInput]
}

input ProductDiscountRulesInput {
    ruleBase : DiscountTermBaseType
    countryBasedRules: CountryBasedDiscountRuleInput
    memberBasedRules : MemberCountBasedDiscountRuleInput
    timePeriodBasedRules: TimePeriodBasedDiscountRuleInput
    salaryBasedRules: SalaryBasedDiscountRuleInput
    deadlineBasedRules: DeadlineBasedDiscountRuleInput
}

input DeadlineBasedDiscountRuleInput {
    type: OrderDiscountRuleDeadlineType
    deadLine: DateTime
}

input CountryBasedDiscountRuleInput {
    ruleBase : DiscountTermBaseType
    appliesGlobally: Boolean
    applicableCountries: [CountryCode]
    excludedCountries: [CountryCode]
}

input MemberCountBasedDiscountRuleInput {
    memberCountLowerBound : Int
    memberCountUpperBound : Int
    validTill: DateTime
}

input TimePeriodBasedDiscountRuleInput {
    fromMonth : Int
    toMonth : Int
    validTill: DateTime
}

input SalaryBasedDiscountRuleInput {
    salaryLowerBound: Float
    salaryUpperBound: Float
}

type OrderDiscount {
    id: ID
    productDiscounts: [ProductDiscount]
    customTerm: String
    totalDiscount: Float
}

type ProductDiscount {
    id: ID!
    type: ProductDiscountType!
    discountValue: Float
    terms: [ProductDiscountRule]

}

interface ProductDiscountRule {
    ruleBase : DiscountTermBaseType
}

type MemberSalaryBasedDiscountRule implements ProductDiscountRule {
    ruleBase : DiscountTermBaseType
    salaryLowerBound: Float
    salaryUpperBound: Float
}

type MemberCountBasedDiscountRule implements ProductDiscountRule {
    ruleBase : DiscountTermBaseType
    memberCountLowerBound : Int
    memberCountUpperBound : Int
    validTill: DateTime
}

type TimePeriodBasedDiscountRule implements ProductDiscountRule {
    ruleBase : DiscountTermBaseType
    fromMonth : Int
    ToMonth : Int
    validTill: DateTime
}

type DeadLineBasedDiscountRule implements ProductDiscountRule {
    ruleBase : DiscountTermBaseType
    type: OrderDiscountRuleDeadlineType
    deadLine: DateTime
}

type CountryBasedDiscountRule implements ProductDiscountRule {
    ruleBase : DiscountTermBaseType
    appliesGlobally: Boolean
    applicableCountries: [CountryCode]
    excludedCountries: [CountryCode]
}

enum DiscountTermBaseType {
    MEMBER_COUNT,
    TIME_PERIOD,
    MEMBER_SALARY,
    DEADLINE,
    COUNTRY
}

enum ProductDiscountType {
    PERCENT,
    FLAT
}

enum OrderDiscountRuleDeadlineType {
    MSA_SIGNED,
    MEMBER_ONBOARDED
}
