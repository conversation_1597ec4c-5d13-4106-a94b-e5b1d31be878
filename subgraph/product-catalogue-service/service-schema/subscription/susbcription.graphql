input OrderSubscriptionInput {
    id: ID
    startDate: DateTime
    endDate: DateTime
    subscriptionProducts: [SubscriptionProductInput]
}

input SubscriptionProductInput {
    offeringCode: OfferingCode
    lineItemId: ID!
    configuration: SubscriptionConfigurationInput
    duration: DatePeriodInput
}

input DatePeriodInput {
    startDate: DateTime!
    endDate: DateTime
}

input SubscriptionConfigurationInput {
    type: SubscriptionConfigurationType
    seatingConfigInput : SeatingConfigInput
}

input SeatingConfigInput {
    autoFill: Boolean
    autoExpand: Boolean
    initialTargetGroup: TargetGroupInput
    seatsConfig: [SeatingConfigurationInput]
}

input SeatingConfigurationInput {
    country: CountryCode
    workStatus: CountryWorkStatus
    count: Int!
    effectiveDate: Date
    discount: SubscriptionProductDiscountInput
}

input SubscriptionProductDiscountInput {
    id: ID
    policy: SubscriptionDiscountPolicyInput
}

input SubscriptionDiscountPolicyInput {
    policyType: SubscriptionDiscountPolicyType
    value: String
}

input TargetGroupInput {
    type: EntityIDType!
    ids: [ID!]!
}

type OrderSubscription {
    id: ID!
    startDate: DateTime!
    endDate: DateTime
    subscriptionProducts: [SubscriptionProductInfo]!
}

type SubscriptionProductInfo {
    product: ProductLineItem!
    configurations: SubscriptionConfiguration
    paymentTerm: SubscriptionPaymentTerm
    duration: DatePeriod
}

type DatePeriod {
    startDate: DateTime!
    endDate: DateTime
}

type SubscriptionPaymentTerm {
    interval: Int!
    timeUnit: SubscriptionPaymentTermTimeUnit!
}

interface SubscriptionConfiguration {
    type: SubscriptionConfigurationType
}

type SubscriptionSeatingInfo implements SubscriptionConfiguration {
    type: SubscriptionConfigurationType
    autoFill: Boolean
    autoExpand: Boolean
    initialTargetGroup: SubscriptionTargetGroup
    seatConfigs: [SubscriptionSeatConfiguration]
}

type SubscriptionTargetGroup {
    type: EntityIDType!
    ids: [ID!]!
}

type SubscriptionSeatConfiguration {
    country: CountryCode
    workStatus: CountryWorkStatus
    count: Int!
    effectiveDate: Date!
    discountPolicy: SubscriptionDiscountPolicy
}

interface SubscriptionDiscountPolicy {
    type: SubscriptionDiscountType!
}

type PercentageSubscriptionDiscount implements SubscriptionDiscountPolicy {
    type: SubscriptionDiscountType!
    percentageValue: Float!
}

enum SubscriptionPaymentTermTimeUnit {
    MONTH
    YEAR
}

enum SubscriptionConfigurationType {
    SEATING
}

enum SubscriptionDiscountPolicyType {
    FLAT,
    PERCENTAGE,
    CUSTOM
}

enum SubscriptionDiscountType {
    PERCENT
    ABSOLUTE
    FLAT_PRICE
}
