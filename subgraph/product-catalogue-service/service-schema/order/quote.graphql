extend type Mutation {
    quoteCreate(input: QuoteInput!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteUpdate(id: ID!, input: QuoteInput!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteSendForInternalApproval(id: ID!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteInternalApprove(id: ID!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteInternalReject(id: ID!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteSentForCustomerReview(id: ID!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteCustomerAccept(id: ID!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteCustomerReject(id: ID!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteDelete(id: ID!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteClone(id: ID!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteCloneByOrderId(orderId: ID!): Quote! @authorize(operations: ["update.operations.quote"])
    quoteDeleteSubscription(id: ID!): Quote! @authorize(operations: ["update.operations.quote"])
}

extend type Query {
    quotes(filter: QuoteFilter, page: PageRequest): QuoteResponse! @authorize(operations: ["view.operations.quote"])
    quote(id: ID!): Quote! @authorize(operations: ["view.operations.quote"])
}

input QuoteFilter {
    companyId: ID
    status: QuoteStatus
}

input QuoteInput {
    companyId: ID!
    billingFrequency: AgreementTermInput!
    paymentDuePeriod: AgreementTermInput!
    orderTerm: AgreementTermInput!,
    products: [QuoteProductInput!]!
    subscription: OrderSubscriptionInput
    discount: OrderDiscountInput
}

input QuoteProductInput {
    lineItemId: ID!
    dimensions: [DimensionInput!]!
    chargePolicy: ChargePolicyInput
    startDate: DateTime!
    endDate: DateTime
    mtplSubsidiaryName: String
}

type QuoteResponse {
    quotes: [Quote!]!
    page: PageResult!
}

type Quote {
    id: ID!
    status: QuoteStatus!
    quoteProducts: [QuoteProduct!]!
    subscription: OrderSubscription
    discount: OrderDiscount
    billingFrequency: AgreementTerm!
    paymentDuePeriod: AgreementTerm!
    orderTerm: AgreementTerm!
    billingStartDate: DateTime!
    billingEndDate: DateTime
    createdOn: DateTime!
    createdBy: OperationsUser
    company: Company!
    quoteDocument: DocumentReadable @deprecated(reason: "use document")
    document: Document
    sourceOrderId: ID
    isOrderFormConvertible: Boolean
    orderFormConversionErrors: [String!]
}

enum QuoteStatus {
    CREATED,
    INTERNAL_APPROVAL_IN_PROGRESS,
    INTERNAL_APPROVED,
    INTERNAL_REJECTED,
    SENT_FOR_REVIEW,
    CUSTOMER_ACCEPTED,
    CUSTOMER_REJECTED,
    DELETED,
}
