interface DimensionDefinition {
    type: DimensionType!
    required: Boolean!
}

type CountryDimensionDefinition implements DimensionDefinition {
    type: DimensionType!
    required: Boolean!
    allowedValues: [CountryCode!]!
}

type OfferingDimensionDefinition implements DimensionDefinition {
    type: DimensionType!
    required: Boolean!
    allowedValues: [OfferingCode!]!
}

type CountryWorkStatusDimensionDefinition implements DimensionDefinition {
    type: DimensionType!
    required: Boolean!
    allowedValues: [CountryWorkStatus!]!
}

type GlobalDimensionDefinition implements DimensionDefinition {
    type: DimensionType!
    required: Boolean!
    allowedValues: [DimensionType!]!
}

type AssetTypeDimensionDefinition implements DimensionDefinition {
    type: DimensionType!
    required: Boolean!
    allowedValues: [AssetType!]!
}

type LogisticsDimensionDefinition implements DimensionDefinition {
    type: DimensionType!
    required: Boolean!
    allowedValues: [LogisticsType!]!
}

type StorageDimensionDefinition implements DimensionDefinition {
    type: DimensionType!
    required: Boolean!
    allowedValues: [StorageType!]!
}

type LegalAdvisoryDimensionDefinition implements DimensionDefinition {
    type: DimensionType!
    required: Boolean!
    allowedValues: [LegalAdvisoryType!]!
}

input DimensionDefinitionInput {
    type: DimensionType!
    countryDimensionDefinition: CountryDimensionDefinitionInput
    offeringDimensionDefinition: OfferingDimensionDefinitionInput
    countryWorkStatusDimensionDefinition: CountryWorkStatusDimensionDefinitionInput
    assetDimensionDefinition: AssetTypeDimensionDefinitionInput
    globalDimensionDefinition: GlobalDimensionDefinitionInput
    logisticsDimensionDefinition: LogisticsDimensionDefinitionInput
    storageDimensionDefinition: StorageDimensionDefinitionInput
    legalAdvisoryDimensionDefinition: LegalAdvisoryDimensionDefinitionInput
}

input CountryDimensionDefinitionInput {
    required: Boolean!
    allowedValues: [CountryCode!]!
}

input OfferingDimensionDefinitionInput {
    required: Boolean!
    allowedValues: [OfferingCode!]!
}

input CountryWorkStatusDimensionDefinitionInput {
    required: Boolean!
    allowedValues: [CountryWorkStatus!]!
}

input GlobalDimensionDefinitionInput {
    required: Boolean!
    allowedValues: [DimensionType!]!
}

input AssetTypeDimensionDefinitionInput {
    required: Boolean!
    allowedValues: [AssetType!]!
}

input LogisticsDimensionDefinitionInput {
    required: Boolean!
    allowedValues: [LogisticsType!]!
}

input StorageDimensionDefinitionInput {
    required: Boolean!
    allowedValues: [StorageType!]!
}

input LegalAdvisoryDimensionDefinitionInput {
    required: Boolean!
    allowedValues: [LegalAdvisoryType!]!
}
