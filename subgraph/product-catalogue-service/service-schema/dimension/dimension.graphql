interface Dimension {
    dimensionType: DimensionType!
}

type CountryDimension implements Dimension {
    dimensionType: DimensionType!
    countryCode: CountryCode!
}

type OfferingDimension implements Dimension {
    dimensionType: DimensionType!
    offeringCode: OfferingCode!
}

type CountryWorkStatusDimension implements Dimension {
    dimensionType: DimensionType!
    workStatus: CountryWorkStatus!
}

type GlobalDimension implements Dimension {
    dimensionType: DimensionType! ## GLOBAL
    globalDimension: DimensionType! ## COUNTRY
}

type AssetTypeDimension implements Dimension {
    dimensionType: DimensionType! 
    assetType: AssetType! 
}

type LogisticsDimension implements Dimension {
    dimensionType: DimensionType!
    logisticsType: LogisticsType!
}

type StorageDimension implements Dimension {
    dimensionType: DimensionType!
    storageType: StorageType!
}

type LegalAdvisoryDimension implements Dimension {
    dimensionType: DimensionType!
    legalAdvisoryType: LegalAdvisoryType!
}

enum DimensionType {
    COUNTRY
    OFFERING
    VISA_STATUS
    PARTNER
    GLOBAL
    ASSET_TYPE
    LOGISTICS
    STORAGE
    LEGAL_ADVISORY
}

enum AssetType {
    LAPTOP,
    MONITOR,
    ACCESSORIES
}

enum LogisticsType {
    PICKUP_DELIVERY
}

enum StorageType {
    ITEMS_STORAGE
}

enum LegalAdvisoryType {
    CONTRACT_CUSTOMISATION,
    CONSULTATION
}

input DimensionInput {
    dimensionType: DimensionType!
    countryDimension: CountryDimensionInput
    offeringDimension: OfferingDimensionInput
    countryWorkStatusDimension: CountryWorkStatusDimensionInput
    assetTypeDimension: AssetTypeDimensionInput
    globalDimension: GlobalDimensionInput
    logisticsDimension: LogisticsDimensionInput
    storageDimension: StorageDimensionInput
    legalAdvisoryDimension: LegalAdvisoryDimensionInput
}

input DimensionQuery {
    dimensionType: DimensionType!
    countryDimensionValues: [CountryCode!]
    offeringDimensionValues: [OfferingCode!]
    countryWorkStatusValues: [CountryWorkStatus!]
    assetTypeDimensionValues: [AssetType!]
    globalDimensionValues: [DimensionType!]
    logisticsDimensionValues: [LogisticsType!]
    storageDimensionValues: [StorageType!]
    legalAdvisoryDimensionValues: [LegalAdvisoryType!]
}

input CountryDimensionInput {
    countryCode: CountryCode!
}

input OfferingDimensionInput {
    offeringCode: OfferingCode!
}

input CountryWorkStatusDimensionInput {
    workStatus: CountryWorkStatus!
}

input AssetTypeDimensionInput {
    assetType: AssetType!
}

input GlobalDimensionInput {
    globalDimension: DimensionType!
}

input LogisticsDimensionInput {
    logisticsType: LogisticsType!
}

input StorageDimensionInput {
    storageType: StorageType!
}

input LegalAdvisoryDimensionInput {
    legalAdvisoryType: LegalAdvisoryType!
}
