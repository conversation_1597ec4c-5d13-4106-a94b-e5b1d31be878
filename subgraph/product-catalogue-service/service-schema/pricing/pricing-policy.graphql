interface ChargePolicy {
    chargeType: ChargeType!
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadata
}

type TierChargePolicy implements ChargePolicy {
    chargeType: ChargeType!
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadata
    meteringUnit: MeteringUnit!
    pricingTiers: [PricingTier!]!
}

type VolumeChargePolicy implements ChargePolicy {
    chargeType: ChargeType!
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadata
    meteringUnit: MeteringUnit!
    pricingTiers: [PricingTier!]!
}

type OverageChargePolicy implements ChargePolicy {
    chargeType: ChargeType!
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadata
    entitledUnit: Int!
    meteringUnit: MeteringUnit!
    overagePrice: Amount!
}

type UnitChargePolicy implements ChargePolicy {
    chargeType: ChargeType!
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadata
    meteringUnit: MeteringUnit!
    unitPrice: Amount!
}

type FlatChargePolicy implements ChargePolicy {
    chargeType: ChargeType!
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadata
    flatRate: Amount!
}

type MatrixChargePolicy implements ChargePolicy {
    chargeType: ChargeType!
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadata
    matrixEntries: [MatrixEntry!]!
}

type ReferenceChargePolicy implements ChargePolicy {
    chargeType: ChargeType!
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadata
    currencyCode: CurrencyCode!
    targets: [TargetReference!]!
}

type TargetReference {
    lineItem: ProductLineItem!
    dimensions: [Dimension!]!
    referenceTargetType: ReferenceTargetType!
    percentage: Float!
    chargePolicy: ChargePolicy!
}

enum ReferenceTargetType {
    COMPANY_PRODUCT
    SUBSCRIPTION_PRODUCT
}

type MeteringUnit {
    type: MeteringType!
    minimumCommitment: Float
}

type ChargePolicyMetadata {
    data: [KeyValue!]!
}

input ChargePolicyInput {
    chargeType: ChargeType!
    tierChargePolicy: TierChargePolicyInput
    volumeChargePolicy: VolumeChargePolicyInput
    overageChargePolicy: OverageChargePolicyInput
    unitChargePolicy: UnitChargePolicyInput
    flatChargePolicy: FlatChargePolicyInput
    matrixChargePolicy: MatrixChargePolicyInput
    referenceChargePolicy: ReferenceChargePolicyInput
}

input TierChargePolicyInput {
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadataInput
    meteringUnit: MeteringUnitInput!
    pricingTiers: [PricingTierInput!]!
}

input PricingTierInput {
    start: Int!
    end: Int
    price: AmountInput!
    type: TierPriceType!
}

input VolumeChargePolicyInput {
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadataInput
    meteringUnit: MeteringUnitInput!
    pricingTiers: [PricingTierInput!]!
}

input OverageChargePolicyInput {
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadataInput
    entitledUnit: Int!
    meteringUnit: MeteringUnitInput!
    overagePrice: AmountInput!
}

input UnitChargePolicyInput {
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadataInput
    meteringUnit: MeteringUnitInput!
    unitPrice: AmountInput!
}

input FlatChargePolicyInput {
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadataInput
    flatRate: AmountInput!
}

input MatrixChargePolicyInput {
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadataInput
    matrixEntries: [MatrixEntryInput!]!
}

input ReferenceChargePolicyInput {
    chargeFrequency: ChargeFrequency!
    metadata: ChargePolicyMetadataInput
    currencyCode: CurrencyCode!
    targets: [TargetReferenceInput!]!
}

input TargetReferenceInput {
    lineItemId: ID!
    dimensions: [DimensionInput!]!
    referenceTargetType: ReferenceTargetType!
    percentage: Float!
    chargePolicy: ChargePolicyInput!
}

input MeteringUnitInput {
    type: MeteringType!
    minimumCommitment: Float #Master Product does not need commitment
}

input ChargePolicyMetadataInput {
    data: [KeyValueInput!]!
}
