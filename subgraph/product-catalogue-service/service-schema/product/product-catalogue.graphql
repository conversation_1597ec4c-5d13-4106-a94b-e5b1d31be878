extend type Mutation {
    productLineCreate(input: ProductLineCreateInput!): ProductLine! @authorize(operations: ["update.operations.product-catalogue"])
    productLineItemCreate(input: ProductLineItemCreateInput!): ProductLineItem! @authorize(operations: ["update.operations.product-catalogue"])
    productLineItemUpdate(id: ID!, input: ProductLineItemUpdateInput!): ProductLineItem! @authorize(operations: ["update.operations.product-catalogue"])

    masterProductCreate(input: MasterProductCreateInput!): MasterProduct! @authorize(operations: ["update.operations.product-catalogue"])
    masterProductUpdate(id: ID!, input: MasterProductUpdateInput!): MasterProduct! @authorize(operations: ["update.operations.product-catalogue"])
    masterProductDelete(id: ID!): ID! @authorize(operations: ["update.operations.product-catalogue"])
}

extend type Query {
    products(filter: ProductSourceFilter, page: PageRequest): ProductResponse! @authorize(operations: ["view.operations.product-catalogue"])
    productLines: [ProductLine!]! @authorize(operations: ["view.operations.product-catalogue"])

    masterProductPrefill(input: MasterProductPrefillInput): [MasterProduct!]! @authorize(operations: ["view.operations.product-catalogue"])
}

input ProductLineCreateInput {
    name: String!
    code: String!
    serviceType: ServiceType!
}

input ProductLineItemMetadataInput {
    signatureRequired: Boolean!
}

input ProductLineItemCreateInput {
    name: String!
    description: String!
    lineId: ID!
    dimensionDefinitions: [DimensionDefinitionInput!]!
    allowedMeteringUnits: [MeteringType!]!
    allowedForSubscription: Boolean
    allowedChargeFrequencies: [ChargeFrequency!]!
    allowedChargeTypes: [ChargeType!]!
    metadata: ProductLineItemMetadataInput!
}

input ProductLineItemUpdateInput {
    name: String
    description: String
    dimensionDefinitions: [DimensionDefinitionInput!]
    allowedMeteringUnits: [MeteringType!]
    allowedChargeFrequencies: [ChargeFrequency!]
    allowedChargeTypes: [ChargeType!]
    metadata: ProductLineItemMetadataInput
}

input MasterProductCreateInput {
    lineItemId: ID
    prefix: String # https://www.notion.so/Product-Catalogue-and-Order-Forms-b5dad025ec5946929c2a6613bf5beb5d?d=119f9e1ba7a780079fa2001c92b8bf15&pvs=4#08175d390a354c63827adf9bc5d901f3
    dimensions: [DimensionInput!]!
    chargePolicy: ChargePolicyInput!
    startDate: DateTime!
    endDate: DateTime
}

input MasterProductUpdateInput {
    dimensions: [DimensionInput!]
    chargePolicy: ChargePolicyInput
    startDate: DateTime
    endDate: DateTime
}

input ProductSourceFilter {
    dimensions: [DimensionQuery!],
    meteringUnits: [MeteringUnitInput!]
    chargeFrequencies: [ChargeFrequency!]
    chargeTypes: [ChargeType!]
}

input MasterProductPrefillInput {
    lineItemId: ID!
    dimensions: [DimensionQuery!],
}

type ProductResponse {
    products: [CatalogueProduct!]!
    page: PageResult!
}

type ProductLine {
    id: ID!
    code: String!
    name: String!
    serviceType: ServiceType!
    items: [ProductLineItem!]!
    latestItem: ProductLineItem
}

type ProductLineItemMetadata {
    signatureRequired: Boolean!
}

type ProductLineItem {
    id: ID!
    name: String!
    code: String!
    serviceType: ServiceType!
    version: Int!
    description: String!
    dimensionDefinitions: [DimensionDefinition!]!
    allowedMeteringUnits: [MeteringType!]!
    allowedForSubscription: Boolean!
    allowedChargeFrequencies: [ChargeFrequency!]!
    allowedChargeTypes: [ChargeType!]!
    products: [CompanyProduct!]!
    metadata: ProductLineItemMetadata
}

interface CatalogueProduct {
    type: ProductType!
    code: String! # Combination of lineItem and dimensions and charge policy
    dimensions: [Dimension!]!
    chargePolicy: ChargePolicy!
    startDate: DateTime!
    endDate: DateTime
}

type MasterProduct implements CatalogueProduct {
    id: ID!
    type: ProductType!
    code: String!
    dimensions: [Dimension!]! # There must be only one kind of DimensionType
    chargePolicy: ChargePolicy!
    startDate: DateTime!
    endDate: DateTime
    # FinancialConfig will come here
}

type CompanyProduct implements CatalogueProduct {
    type: ProductType!
    code: String!
    lineItem: ProductLineItem!
    dimensions: [Dimension!]!
    chargePolicy: ChargePolicy!
    startDate: DateTime!
    endDate: DateTime
    status: CompanyProductStatus!
    order: CompanyOrder!
    companyOrderBundle: CompanyOrderBundle
    mtplSubsidiaryName: String
}

type QuoteProduct implements CatalogueProduct {
    type: ProductType!
    code: String!
    lineItem: ProductLineItem!
    dimensions: [Dimension!]!
    chargePolicy: ChargePolicy!
    startDate: DateTime!
    endDate: DateTime
    source: CatalogueProduct!
    quote: Quote!
    mtplSubsidiaryName: String
}

enum CompanyProductStatus {
    DRAFT
    SIGNED
    ACTIVE
    INACTIVE
    ON_HOLD
}

enum ProductType {
    MASTER,
    QUOTE,
    COMPANY
}

enum ServiceType {
    ON_DEMAND,
    PRODUCT,
}
