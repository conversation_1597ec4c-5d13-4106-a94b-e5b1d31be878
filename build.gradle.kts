import io.spring.gradle.dependencymanagement.DependencyManagementPlugin
import io.spring.gradle.dependencymanagement.dsl.DependencyManagementExtension
import org.sonarqube.gradle.SonarTask

group = "com.multiplier"
version = "0.0.1-SNAPSHOT"

plugins {
    val kotlinVersion = "2.1.0"

    kotlin("jvm") version kotlinV<PERSON><PERSON>
    kotlin("kapt") version kotlinV<PERSON><PERSON>
    kotlin("plugin.lombok") version kotlinVersion
    kotlin("plugin.spring") version kotlinVersion
    kotlin("plugin.jpa") version kotlinVersion
    kotlin("plugin.allopen") version kotlinVersion
    kotlin("plugin.serialization") version kotlinVersion

    id("org.springframework.boot") version "3.3.9"
    id("org.liquibase.gradle") version "2.2.0"
    id("io.spring.dependency-management") version "1.1.0"

    id("java")
    id("jacoco")
    id("com.google.protobuf") version "0.8.19"
    id("org.sonarqube") version "4.4.1.3373"
    id("com.diffplug.spotless") version "6.25.0"

    alias(multiplier.plugins.lib.resolution)
}

dependencyManagement {
    imports {
        // This lets us use the Spring Cloud dependency management
        // so that we don't have to specify versions for Spring Cloud dependencies
        mavenBom("org.springframework.cloud:spring-cloud-dependencies:2023.0.3")
    }
}

subprojects {
    val grpcVersion by extra("1.63.0")
    val grpcKotlinVersion by extra("1.4.1")
    val protobufVersion by extra("3.25.5")
    val mapStructVersion by extra("1.5.3.Final")
    val contractServiceVersion by extra("1.15.23")
    val timeoffServiceVersion by extra("2.1.5")

    apply<DependencyManagementPlugin>()
    extensions.getByType<DependencyManagementExtension>().apply {
        imports {
            mavenBom(org.springframework.boot.gradle.plugin.SpringBootPlugin.BOM_COORDINATES)
        }
        dependencies {
            dependency("com.netflix.graphql.dgs:graphql-dgs-spring-boot-starter:8.7.1")
            dependency("com.netflix.graphql.dgs:graphql-dgs-extended-scalars:8.7.1")
            dependency("org.codehaus.janino:janino:3.1.9")
            dependency("net.logstash.logback:logstash-logback-encoder:7.3") // Structured (JSON) logging

            dependency("net.devh:grpc-spring-boot-starter:3.1.0.RELEASE")
            dependency("io.sentry:sentry-spring-boot-starter:6.15.0")

            dependency("com.multiplier.platform:spring-starter:2.3.4")
            dependency("com.multiplier:access-control-spring:2.0.3")
            dependency("com.multiplier.transaction:transaction-database-jpa-spring:1.0.37")
            dependency("com.multiplier:core-service-schema:1.1.173")
            dependency("com.multiplier:contract-service-schema:$contractServiceVersion")
            dependency("com.multiplier:member-service-schema:1.1.22")
            dependency("com.multiplier:company-service-schema:3.5.385")
            dependency("com.multiplier:country-service-schema:1.6.4")
            dependency("com.multiplier:contract-onboarding-service-graph:1.14.822")
            dependency("com.multiplier:pigeon-service-schema:1.1.29")
            dependency("com.multiplier:pigeon-service-client:1.1.29")
            dependency("com.multiplier:pay-se-schema:1.1.198")
            dependency("com.multiplier:payroll-service-schema:1.1.193")
            dependency("com.multiplier:org-management-service-schema:0.2.2")
            dependency("com.multiplier.grpc:grpc-common:1.2.32")
            dependency("com.multiplier.grpc:grpc-bulkupload:1.2.24")
            dependency("com.multiplier:timeoff-service-schema:$timeoffServiceVersion")
            dependency("com.multiplier:compensation-service-grpc-schema:0.0.294")
            dependency("com.multiplier.messaging:bulk-upload-service:0.2.59")

            dependency("org.apache.kafka:kafka-clients:3.8.1")
            dependency("org.liquibase:liquibase-core:4.20.0")
            dependency("io.hypersistence:hypersistence-utils-hibernate-62:3.5.1")
            dependency("org.zalando:problem-spring-web:0.29.1")
            dependency("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.9.0")
            dependency("io.jsonwebtoken:jjwt-api:0.11.5")
            dependency("io.jsonwebtoken:jjwt-impl:0.11.5")
            dependency("io.jsonwebtoken:jjwt-jackson:0.11.5")
            dependency("org.jetbrains.kotlinx:kotlinx-serialization-json:1.5.1")
            dependency("com.github.daniel-shuy:kafka-protobuf-serde:2.2.0")

            dependency("com.google.protobuf:protobuf-kotlin:$protobufVersion")
            dependency("com.google.protobuf:protobuf-java-util:$protobufVersion")
            dependency("io.grpc:grpc-kotlin-stub:$grpcKotlinVersion")
            dependency("io.grpc:grpc-protobuf:$grpcVersion")
            dependency("io.grpc:grpc-core:$grpcVersion")
            dependency("io.grpc:grpc-stub:$grpcVersion")
            dependency("io.grpc:grpc-testing:$grpcVersion")

            dependency("io.growthbook.sdk:GrowthBook:1.1.28")

            dependency("io.mockk:mockk:1.13.8")
            dependency("com.ninja-squad:springmockk:4.0.2")
            dependency("com.h2database:h2:2.2.220")

            dependency("org.mapstruct:mapstruct:$mapStructVersion")
            dependency("org.mapstruct:mapstruct-processor:$mapStructVersion")

            dependency("org.projectlombok:lombok:1.18.26")
            dependency("org.mockito:mockito-inline:5.2.0")
            dependency("org.json:json:20231013")
            dependency("com.fasterxml.jackson.module:jackson-module-kotlin:2.17.1")
            dependency("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.2")
            dependency("io.github.serpro69:kotlin-faker:1.14.0")
            dependency("me.moallemi.tools:kotlin-date-range:1.0.0")
            dependency("org.testcontainers:testcontainers:1.17.6")
            dependency("org.testcontainers:junit-jupiter:1.17.6")
            dependency("org.testcontainers:postgresql:1.17.6")
            dependency("net.javacrumbs.shedlock:shedlock-core:4.33.0")
            dependency("net.javacrumbs.shedlock:shedlock-spring:4.33.0")
            dependency("net.javacrumbs.shedlock:shedlock-provider-jdbc-template:4.33.0")
            dependency("org.apache.httpcomponents:fluent-hc:4.5.13")
            dependency("com.github.ben-manes.caffeine:caffeine:3.1.8")
            dependency("io.github.microutils:kotlin-logging-jvm:3.0.5")
            dependency("com.github.ozlerhakan:poiji:4.1.2")

            // Define explicit version for the dependencies to fix vulnerabilities
            dependency("commons-io:commons-io:2.14.0")
            dependency("org.apache.commons:commons-compress:1.26.0")
            dependency("org.bouncycastle:bcprov-jdk18on:1.78")
            dependency("io.netty:netty-common:4.1.118.Final")
            dependency("io.netty:netty-handler:4.1.118.Final")
            dependency("net.minidev:json-smart:2.5.2")
        }
    }

    apply<JacocoPlugin>()
    tasks.withType<JacocoReport> {
        dependsOn(tasks.test)

        reports {
            html.required.set(false)
            xml.required.set(false)
        }
    }
}

allprojects {
    apply<IdeaPlugin>()
}

sonarqube {
    properties {
        property("sonar.java.coveragePlugin", "jacoco")
        property("sonar.host.url", "https://sonarcloud.io")
        property("sonar.projectKey", "Multiplier-Core_contract-onboarding-service")
        property("sonar.projectName", "contract-onboarding-service")
        property("sonar.organization", "multiplier")
        property("sonar.coverage.jacoco.xmlReportPaths", "${buildDir}/reports/jacoco.xml")
    }
}

configure<com.diffplug.gradle.spotless.SpotlessExtension> {
    kotlin {
        target("**/*.kt")
        ktfmt("0.42").dropboxStyle()
    }
}

tasks.withType<Jar> {
    enabled = false
}

tasks.withType<SonarTask> {
    dependsOn(tasks.jacocoTestReport)
}

tasks.withType<JacocoReport> {
    this.let {
        subprojects {
            tasks.withType<JacocoReport> {
                it.dependsOn(this)
                it.executionData(executionData)
                it.sourceSets(sourceSets.named("main").get())
            }
        }

        reports {
            xml.required.set(true)
            html.required.set(false)
            xml.outputLocation.set(File("${buildDir}/reports/jacoco.xml"))
        }
    }
}

tasks.register("downloadDependencies") {
    allprojects {
        dependsOn(tasks.named("dependencies"))
    }
}

tasks.register("setupGitHooks") {
    description = "Sets up Git hooks for the project"
    group = "git"

    doFirst {
        // Set up hooks based on OS
        val osName = System.getProperty("os.name")
        val isWindows = osName.contains("windows", ignoreCase = true)
        logger.lifecycle("Setting up Git hooks for $osName")

        val process = if (isWindows) {
            ProcessBuilder().apply {
                command("cmd", "/c", ".github\\.git-hooks\\setup-git-hooks.sh")
                redirectErrorStream(true)
            }.start()
        } else {
            ProcessBuilder().apply {
                command("bash", ".github/.git-hooks/setup-git-hooks.sh")
                redirectErrorStream(true)
            }.start()
        }

        val output = process.inputStream.bufferedReader().readText()
        if (output.isNotEmpty()) {
            logger.lifecycle(output)
        }

        val exitCode = process.waitFor()
        if (exitCode != 0) {
            throw GradleException("Failed to set up Git hooks")
        }
    }
}

// Add the setupGitHooks task as a dependency to compileKotlin
tasks.named("compileKotlin") {
    dependsOn("setupGitHooks")

    // Force setupGitHooks to run first
    tasks.findByName("setupGitHooks")?.let { setupTask ->
        this.dependsOn.forEach { dep ->
            if (dep != setupTask) {
                (dep as? Task)?.mustRunAfter(setupTask)
            }
        }
    }
}