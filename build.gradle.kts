tasks.withType<Wrapper> {
    gradleVersion = "8.8"
}

group = "com.multiplier"
version = "unspecified"

val graphGroup by extra { "graph" }

val serviceSchemaPath by extra { "service-schema" }
val generatedResourcesPath by extra { "generated/resources" }
val generatedSchemaPath by extra { "$generatedResourcesPath/schema" }
val mergedResourcesPath by extra { "$generatedResourcesPath/merged" }
val mergedSchemaPath by extra { "$mergedResourcesPath/schema" }

plugins {
    alias(libs.plugins.kotlin.jvm) apply false
    alias(multiplier.plugins.lib.resolution)
    alias(multiplier.plugins.publish.with.summary) apply false
}

allprojects {
    apply<IdeaPlugin>()

    tasks.withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile> {
        compilerOptions {
            jvmTarget = org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_11
        }
        kotlinOptions {
            freeCompilerArgs += "-Xjsr305=strict"
        }
    }

    plugins.withType<JavaPlugin> {
        configure<JavaPluginExtension> {
            sourceCompatibility = JavaVersion.VERSION_11
            targetCompatibility = JavaVersion.VERSION_11

            withSourcesJar()
        }

        tasks.withType<Test> {
            useJUnitPlatform()
        }
    }
}

tasks.withType<Jar> {
    enabled = false
}

tasks.register("downloadDependencies") {
    allprojects {
        dependsOn(tasks.named("dependencies"))
    }
}
