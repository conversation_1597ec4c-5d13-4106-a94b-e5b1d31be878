import pandas

def read_excel(file_path):
    return pandas.read_excel(file_path, dtype=str)

def get_headers(file_path):
    all_sheets = pandas.read_excel(file_path, sheet_name=None, dtype=str)
    return {sheet_name: list(sheet.columns[1:]) for sheet_name, sheet in all_sheets.items()}

def get_contract_data(file_path):
    # TODO: read contract data from all sheets
    return read_excel(file_path).iloc[2:, 1:].to_dict(orient='records')

def update_contract_data(file_path, employee_id_to_contract_id, dest_file_path):
    df = read_excel(file_path)
    for index, row in df.iterrows():
        employee_id = row['employeeId']
        if employee_id in employee_id_to_contract_id:
            df.at[index, 'contractId'] = employee_id_to_contract_id[employee_id]
    df.to_excel(dest_file_path, index=False)

def get_validation_errors(file_path):
    df = read_excel(file_path)
    validation_errors = df.iloc[2:, 0].dropna().tolist()
    return [error.split('\n') for error in validation_errors]

def test():
    file_path = './templates/eor/can-validation-ok.xlsx'
    df = read_excel(file_path)
    print(df.columns.values) # get all keys
    print(df.columns.values[0])
    print(df.columns.values[1])
    print(df.columns.values[10])
    print(df.index)
    print(df.iloc[2]) # get full row
    print(df.iloc[2]['firstName']) # get specific cell
    # skip row 0, 1
    print(df.iloc[2:, :].to_dict(orient='records'))
    # skip row 0, 1, skip column 0
    print('-' * 10)
    print(df.iloc[2:, 1:].to_dict(orient='records'))

    # update row 2, 'contractId' column
    df.at[2, 'contractId'] = 'updated1'
    df.at[3, 'contractId'] = 'updated2'
    df.to_excel('./responses/test.xlsx', index=False)

    all_sheets = pandas.read_excel(file_path, sheet_name=None, dtype=str)
    for sheet_name, sheet in all_sheets.items():
        print(sheet_name)
        print(sheet.columns.values)

    print(get_headers('./templates/eor/can-validation-ok.xlsx'))

    print(get_validation_errors('./responses/bulk_onboarding_validation_report.xlsx'))

if (__name__ == '__main__'):
    test()