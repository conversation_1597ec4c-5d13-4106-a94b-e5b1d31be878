import time
import sys
import traceback
from testcase import TestCase, TestResult
from actions import download_template, validate, onboard, update
from db_queries import delete_contracts
from rich import print

def run_test(testcase_file_name):
    print()
    print('-' * 80)
    print('Running test:', testcase_file_name)

    try:
        testcase = TestCase.parse(testcase_file_name)
        testcase.print()

        if (testcase.delete_contract):
            delete_contracts(testcase.member_email_pattern)

        result = run_test_action(testcase)
        return result
    except Exception as e:
        print(f'Exception occurred while running test case "{testcase_file_name}": {e}')
        traceback.print_exc()
        return TestResult.failed(testcase_file_name)

def run_test_action(testcase):
    start_time = time.time()
    result = None
    if 'validate' == testcase.action:
        result = validate(testcase)
    if 'template' == testcase.action:
        result = download_template(testcase)
    if 'onboard' == testcase.action:
        result = onboard(testcase)
    if 'update' == testcase.action:
        result = update(testcase)

    if result:
        result.print()
    elapsed_time = time.time() - start_time
    print('Test case completed in {:.2f} seconds'.format(elapsed_time))

    return result

if (__name__ == '__main__'):
    start_time = time.time()

    args = sys.argv[1:]
    print('args', args)

    for arg in args:
        run_test(arg)

    elapsed_time = time.time() - start_time
    print('Completed in {:.2f} seconds'.format(elapsed_time))