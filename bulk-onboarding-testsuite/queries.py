import requests
import configparser
from helpers import get_auth_header
from helpers import gql_client
from helpers import gql_client_local
from helpers import read
from helpers import read_oneline
from helpers import pretty_print

def get_team():
    client = gql_client(auth_url, gql_url, username, password)
    query = read('./graphql/get-team.graphql')
    response = client.execute(query=query)
    return response


def delete_member(member_id):
    client = gql_client(auth_url, gql_url, username, password)

    query = read('./graphql/delete-member.graphql')

    variables = {
        "id": f"{member_id}"
    }

    response = client.execute(query=query, variables=variables)
    return response


def bulk_validate(testcase):
    return upload_bulk_file('./graphql/bulk-validate.graphql', testcase)


def bulk_create(testcase):
    return upload_bulk_file('./graphql/bulk-create.graphql', testcase)

def read_env(env, experience):
    if env and experience:
        env_config = configparser.ConfigParser()
        env_config.read(f'env/{env}.properties')
        auth_url = env_config.get('default', 'auth_url')
        gql_url = env_config.get('default', 'graphql_url')
        username = env_config.get(experience, 'username')
        password = env_config.get(experience, 'password')
    else:
        raise Exception('env and experience are required')

    return auth_url, gql_url, username, password

def read_user_context_headers(env, exp):
    env_config = configparser.ConfigParser()
    env_config.read(f'env/{env}.properties')
    user_id = env_config.get(exp, 'user-context-user-id')
    username = env_config.get(exp, 'user-context-user-name')
    auth = env_config.get(exp, 'user-context-auth')
    experience = env_config.get(exp, 'user-context-experience')

    return {
        'user-context-user-id': user_id,
        'user-context-user-name': username,
        'user-context-auth': auth,
        'user-context-experience': experience
    }

def read_gql_url(env):
    env_config = configparser.ConfigParser()
    env_config.read(f'env/{env}.properties')
    return env_config.get('default', 'graphql_url')

def upload_bulk_file(query_file_name, testcase):
    if testcase.env == 'local':
        headers = read_user_context_headers(testcase.env, testcase.experience)
    else:
        auth_url, gql_url, username, password = read_env(testcase.env, testcase.experience)
        headers = get_auth_header(auth_url, username, password)

    q = get_bulk_mutation(
            query_file_name,
            testcase,
        )

    res = requests.post(gql_url, headers=headers, data=q['data'], files=q['files'])
    if res.status_code != 200:
        pretty_print(res.text)
        raise Exception(f'Error: {res.status_code}')
    json = res.json()
    if 'errors' in json and json['errors']:
        pretty_print(json)
        return None

    return json

def get_bulk_mutation(query_file_name, testcase):
    query = read_oneline(query_file_name)
    variables = f"""
        {{
            "options": {{
              "context": "{testcase.context}",
              "contractType": "HR_MEMBER",
              "countryCode": "{testcase.country_code}",
              "companyId": {testcase.company_id},
              "entityId": "{testcase.entity_id}"
            }}
        }}
    """
    operations_payload = f"""
        {{
          "query": "{query}",
          "variables": {variables}
        }}
    """

    map_payload = """
        {"file":["variables.employeeDataFile"]}
    """

    files = [
        ('file', (testcase.onboarding_file, open(testcase.onboarding_file, 'rb'), 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'))
    ]

    return {
        'data': {
            'operations': operations_payload,
            'map': map_payload,
        },
        'files': files
    }

def execute_query(client, query, variables):
    try:
        response = client.execute(query=query, variables=variables)
        if 'errors' in response and response['errors']:
            pretty_print(response)
            return None
        return response
    except requests.exceptions.HTTPError as e:
        print(e.response.text)
        print(e)
        return None
    except Exception as e:
        print(e)
        return None

def download_template(testcase):
    if testcase.env == 'local':
        gql_url = read_gql_url(testcase.env)
        headers = read_user_context_headers(testcase.env, testcase.experience)
        headers['Content-Type'] = 'application/json'
        headers['Content-Length'] = '43'
        headers['User-Agent'] = 'PostmanRuntime'
        headers['Host'] = 'localhost:8080'
        client = gql_client_local(gql_url, headers)
    else:
        auth_url, gql_url, username, password = read_env(testcase.env, testcase.experience)
        client = gql_client(auth_url, gql_url, username, password)
    query = read('./graphql/download-template.graphql')

    variables = f"""
        {{
            "options": {{
              "context": "{testcase.context}",
              "contractType": "{get_contract_type(testcase.context)}",
              "countryCode": "{testcase.country_code}",
              "companyId": "{testcase.company_id}",
              "entityId": "{testcase.entity_id}"
            }}
        }}
    """

    return execute_query(client, query, variables)

def get_contract_type(context):
    context_to_contract_type = {
        "GLOBAL_PAYROLL": "HR_MEMBER",
        "EOR": "EMPLOYEE",
        "AOR": "CONTRACTOR",
        "HRIS_PROFILE_DATA": "HR_MEMBER"
    }
    return context_to_contract_type.get(context, "HR_MEMBER")