import json
import configparser
from enum import Enum
from rich import print

class TestCase:
    DEFAULT_MEMBER_EMAIL_PATTERN = 'automated-e2e'

    def __init__(self,
                 name,
                 action,
                 env,
                 experience,
                 context,
                 company_id,
                 entity_id,
                 country_code,
                 onboarding_file = None,
                 delete_contract = False,
                 member_email_pattern = DEFAULT_MEMBER_EMAIL_PATTERN,
                 expected_result = None,
                 expected_columns = None):
        self.name = name
        self.action = action
        self.env = env
        self.experience = experience
        self.context = context
        self.company_id = company_id
        self.entity_id = entity_id
        self.country_code = country_code
        self.onboarding_file = onboarding_file
        self.delete_contract = delete_contract
        self.member_email_pattern = member_email_pattern
        self.expected_result = expected_result
        self.expected_columns = expected_columns

    @staticmethod
    def parse(testcase_file_name):
        config = configparser.ConfigParser()
        config.read(f'testcases/{testcase_file_name}')
        name = testcase_file_name
        env = config.get('default', 'env')
        experience = config.get('default', 'experience')
        action = config.get('default', 'action')
        context = config.get('default', 'context')
        company_id = config.get('default', 'company_id')
        entity_id = config.get('default', 'entity_id')
        country_code = config.get('default', 'country_code')
        onboarding_file = config.get('default', 'onboarding_file')
        delete_contract = config.getboolean('default', 'delete_contract') if config.has_option('default', 'delete_contract') else False
        member_email_pattern = config.get('default', 'member_email_pattern') if config.has_option('default', 'member_email_pattern') else TestCase.DEFAULT_MEMBER_EMAIL_PATTERN
        expected_result = ActionResult[config.get('default', 'expected_result')] if config.has_option('default', 'expected_result') else None
        expected_columns = json.loads(config.get('default', 'expected_columns')) if config.has_option('default', 'expected_columns') else None

        return TestCase(name,
                        action,
                        env,
                        experience,
                        context,
                        company_id,
                        entity_id,
                        country_code,
                        onboarding_file,
                        delete_contract,
                        member_email_pattern,
                        expected_result,
                        expected_columns)

    def print(self):
        print('TestCase:')
        print('name:', self.name)
        print('action:', self.action)
        print('env:', self.env)
        print('experience:', self.experience)
        print('context:', self.context)
        print('company_id:', self.company_id)
        print('entity_id:', self.entity_id)
        print('country_code:', self.country_code)
        print('onboarding_file:', self.onboarding_file)
        print('delete_contract:', self.delete_contract)
        print('member_email_pattern:', self.member_email_pattern)
        print('expected_result:', self.expected_result.styled() if self.expected_result else None)
        if self.expected_columns:
            print('expected sheets:', list(self.expected_columns.keys()))
            print('total number of columns:', sum([len(columns) for columns in self.expected_columns.values()]))

    def complete(self, actual_result):
        return TestResult(self.name, self.expected_result, actual_result)

class TestStatus(Enum):
    PASSED = "PASSED"
    FAILED = "FAILED"

    def styled(self):
        return f'[bold green]{self.value}[/bold green]' if self == TestStatus.PASSED else f'[bold red]{self.value}[/bold red]'

class ActionResult(Enum):
    VALIDATION_OK = "VALIDATION_OK"
    VALIDATION_FAILED = "VALIDATION_FAILED"
    TEMPLATE_OK = "TEMPLATE_OK"
    TEMPLATE_NOT_MATCH = "TEMPLATE_MISSING_COLUMNS"
    TEMPLATE_DOWNLOAD_FAILED = "TEMPLATE_DOWNLOAD_FAILED"
    ONBOARD_OK = "ONBOARD_OK"
    ONBOARD_FAILED = "ONBOARD_FAILED"
    UPDATE_OK = "UPDATE_OK"
    UPDATE_FAILED = "UPDATE_FAILED"

    def styled(self):
        return f'[green]{self.value}[/green]' if self == ActionResult.VALIDATION_OK else f'[red]{self.value}[/red]'

class TestResult:
    def __init__(self, testcase_name, expected_result, actual_result):
        self.name = testcase_name
        self.expected_result = expected_result
        self.actual_result = actual_result
        self.status = TestStatus.PASSED if expected_result == actual_result else TestStatus.FAILED

    @staticmethod
    def failed(testcase_name):
        return TestResult(testcase_name, 0, 1)

    def is_passed(self):
        return self.status == TestStatus.PASSED

    def is_failed(self):
        return self.status == TestStatus.FAILED

    def print(self):
        print('TestResult:')
        print('name:', self.name)
        print('status:', self.status.styled())


if (__name__ == '__main__'):
    testcase = TestCase.parse('staging.eor-template-can')
    testcase.print()
    print(testcase.expected_columns['Basic Details'])