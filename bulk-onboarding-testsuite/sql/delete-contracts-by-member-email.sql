DO $$
DECLARE
    _member_email_pattern TEXT := '%{}%';
BEGIN

-- Create a temporary table to store member IDs
CREATE TEMP TABLE temp_member_ids AS
SELECT id FROM member.member
WHERE emails::TEXT ILIKE _member_email_pattern;

-- Create a temporary table to store contract IDs
CREATE TEMP TABLE temp_contract_ids AS
SELECT id FROM contract.contract
WHERE member_id IN (SELECT id FROM temp_member_ids);


-- Delete addresses
DELETE FROM member.address
WHERE member_id IN (
    SELECT id FROM temp_member_ids
);
DELETE FROM member.bank_account
WHERE member_id IN (
    SELECT id FROM temp_member_ids
);
DELETE FROM member.legal_data
WHERE member_id IN (
    SELECT id FROM temp_member_ids
);
DELETE FROM member.legal_document
WHERE member_id IN (
    SELECT id FROM temp_member_ids
);
DELETE FROM member.emergency_contact
WHERE member_id IN (
    SELECT id FROM temp_member_ids
);
DELETE FROM member.education_detail
WHERE member_id IN (
    SELECT id FROM temp_member_ids
);
DELETE FROM member.previous_employer
WHERE member_id IN (
    SELECT id FROM temp_member_ids
);

-- Delete onboardings
DELETE FROM contract.onboarding
WHERE contract_id IN (
    SELECT id from temp_contract_ids
);
delete from performance.salary_review_compensations
where compensation_id in (
	select id from contract.compensation
	where contract_id in (
		select id from temp_contract_ids
	)
);
DELETE FROM contract.compensation
WHERE contract_id IN (
    SELECT id from temp_contract_ids
);
DELETE FROM contract.deduction
WHERE contract_id IN (
    SELECT id from temp_contract_ids
);
DELETE FROM contract.compliance
WHERE contract_id IN (
    SELECT id from temp_contract_ids
);
DELETE FROM contract.benefit_dependent
WHERE benefit_id IN (
    select id from contract.benefit
    where contract_id in (
    	SELECT id from temp_contract_ids
    )
);
DELETE FROM contract.benefit
WHERE contract_id IN (
    SELECT id from temp_contract_ids
);
DELETE FROM contract.contract_payment_account
WHERE contract_id IN (
    SELECT id from temp_contract_ids
);

-- Delete managers
delete from organization.manager_direct_report
where manager_id in (
	select id from organization.manager
	where contract_id in (
		select id from temp_contract_ids
	)
);
delete from organization.manager
where contract_id in (
	select id from temp_contract_ids
);

-- Delete contracts
DELETE FROM contract.contract
WHERE id in (
	select id from temp_contract_ids
);

-- Delete members
DELETE FROM member.member
WHERE id IN (
    SELECT id FROM temp_member_ids
);

DROP TABLE temp_member_ids;
DROP TABLE temp_contract_ids;

END $$;