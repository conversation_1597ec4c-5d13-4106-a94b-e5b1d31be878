## Bulk onboarding automation

This requires python to be installed. Then run (from this folder)

`python3 -m pip install -r requirements.txt`

## How to run
```commandline
python3 test.py [testcase]
```
or
```commandline
python3 testsuite.py [testsuite]
```

For example: `python3 test.py staging.hris-validate`

You can run multiple testcases at once by providing multiple testcase names separated by space.

For example: `python3 test.py staging.hris-validate staging.hris-onboard`

You can also run multiple testcases by providing a testsuite file that contains names of testcases.

For example: `python3 testsuite.py staging.testsuite`

List of available testcases and test suites can be found in the `testcases` folder.

After the test is finished, the result will be printed to the console, validation result file can be found in `response` folder.

## How to add a new testcase
#### 1. Prepare a new upload file and put it in the `templates` folder
#### 2. Create a new file in the `testcases` folder

You can copy existing test cases and modify it to your needs.

Change the `description` field to the description of the test case.

Change the `file` field to the path of the upload file you prepared in step 1.

Change the `action` field to the action you want to test.

Available actions are:
- template
- validate
- onboard

More actions will be added soon.
#### 3. If you need a new environment, create a new file in the `env` folder

You can copy existing environments and modify it to your needs.

In each environment file, you need to provide:
- `graph_url`: the url of the graph api server
- `auth_url`: the url of the auth server
- `username` and `password` of the test user in `[ops]` or `[customer]` section
