import os
import sys
import psycopg2
from dotenv import load_dotenv
from helpers import read

def connect():
    load_dotenv()
    db_host = os.getenv('DB_HOST')
    db_name = os.getenv('DB_NAME')
    db_user = os.getenv('DB_USER')
    db_password = os.getenv('DB_PASSWORD')
    if db_host is None or db_name is None or db_user is None or db_password is None:
        db_host = os.environ.get('DB_HOST')
        db_name = os.environ.get('DB_NAME')
        db_user = os.environ.get('DB_USER')
        db_password = os.environ.get('DB_PASSWORD')

    print(f'Database Host: {db_host}')
    print(f'Database User: {db_user}')
    return psycopg2.connect(database = db_name,
                            host = db_host,
                            user = db_user,
                            password = db_password,
                            port = 5432)

def delete_contracts(member_email_pattern):
    print('Deleting contracts with member_email_pattern: "%{}%"'.format(member_email_pattern))
    query = read('./sql/delete-contracts-by-member-email.sql')
    query = query.format(member_email_pattern)
    conn = connect()
    cur = conn.cursor()
    cur.execute(query)
    conn.commit()
    conn.close()
    print('Contracts deleted successfully')


def test():
    conn = connect()
    cur = conn.cursor()
    cur.execute('SELECT id FROM contract.contract LIMIT 10;')
    rows = cur.fetchall()
    conn.commit()
    conn.close()
    if (len(rows) == 10):
        print('Database connected successfully, print first 10 contract ids:')
        for row in rows:
            print(row)
    else:
        print('Database connection failed')

if (__name__ == '__main__'):
    args = sys.argv[1:]
    if (len(args) == 0):
        print('Doing nothing, please provide an argument')
        sys.exit()
    arg = args[0]
    if arg == 'test':
        test()
    if arg == 'delete-contracts':
        if (len(args) == 2):
            delete_contracts(args[1])
        else:
            print('Please provide a member email pattern')
