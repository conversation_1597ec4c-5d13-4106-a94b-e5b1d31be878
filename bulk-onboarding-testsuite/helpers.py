import json
import requests
import os
import base64
import time
from python_graphql_client import GraphqlClient

def read(filename):
    with open(filename, 'r') as file:
        # Read the entire contents of the file into a string
        return file.read()


def read_oneline(filename):
    with open(filename, 'r') as file:
        # Read the entire contents of the file into a string
        return file.read().replace('\n', ' ')

def gql_client(auth_url, gql_url, username, password):
    headers = get_auth_header(auth_url, username, password)
    return GraphqlClient(endpoint=gql_url, headers=headers)

def gql_client_local(gql_url, headers):
    return GraphqlClient(endpoint=gql_url, headers=headers)

def get_auth_header(url, username, password):
    jwt = get_jwt(url, username, password)
    decoded_jwt = decode_jwt_payload(jwt)
    userId = json.loads(decoded_jwt)['details']['id']
    headers = {
        'Authorization': f'Bearer {jwt}',
        'User-Agent': 'PostmanRuntime',
        'user-id': str(userId)
    }
    return headers


def pretty_print(data):
    print(json.dumps(data, indent=4))

jwt_cache = {}

def get_jwt(url, username, password):
    cache_key = f'{url}:{username}'
    current_time = time.time()

    if cache_key in jwt_cache:
        jwt, expiration_time = jwt_cache[cache_key]
        if current_time - expiration_time:
            print(f'Using cached JWT for {username}')
            return jwt

    print(f'Getting JWT for {username}')
    auth = {
        'username': username,
        'password': password
    }

    headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'PostmanRuntime'
    }
    response = requests.post(url, json=auth, headers=headers)
    if response.status_code != 200:
        raise Exception(f'Error: {response.status_code}')
    jwt = response.json()['id_token']
    decoded_jwt = decode_jwt_payload(jwt)
    expiration_time = json.loads(decoded_jwt)['exp']
    jwt_cache[cache_key] = (jwt, expiration_time)

    # print(token)
    return jwt

def decode_jwt_payload(jwt):
    # Split the JWT into its three parts
    parts = jwt.split('.')

    # The payload is the second part
    payload = parts[1]

    # Base64 decode the payload
    # JWT uses a variant of base64 that replaces '+' with '-' and '/' with '_'
    # We need to replace these characters back to their original form before decoding
    payload = payload.replace('-', '+').replace('_', '/')

    # Add padding if necessary
    padding = len(payload) % 4
    if padding > 0:
        payload += '=' * (4 - padding)

    decoded_payload = base64.b64decode(payload).decode('utf-8')

    return decoded_payload

def write_json(path, data):
    directory = os.path.dirname(path)
    if not os.path.exists(directory):
        os.makedirs(directory)

    with open(path, 'w') as json_file:
        json.dump(data, json_file, indent=4)

def write_binary(path, data):
    directory = os.path.dirname(path)
    if not os.path.exists(directory):
        os.makedirs(directory)

    with open(path, 'wb') as file:
        file.write(data)

def write_file_from_response(response):
    if 'blob' not in response or 'name' not in response or 'extension' not in response:
        print('Invalid response, missing blob, name or extension')
        return

    blob = response['blob']
    file_name = response['name']
    extension = response['extension']
    data = base64.b64decode(blob)
    file_path = f'./responses/{file_name}.{extension}'
    write_binary(file_path, data)

    return file_path
