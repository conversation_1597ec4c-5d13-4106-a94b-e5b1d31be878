mutation($employeeDataFile: Upload!, $options: BulkOnboardingOptions!) {
    bulkOnboardingValidate(employeeDataFile: $employeeDataFile, options: $options) {
        validEmployeeCount
        errorEmployeeCount
        totalEmployeeCount
        errors {
            message
            rowNumber
        }
        warnings {
            message
            rowNumber
        }
        validationErrors {
            message
            rowNumber
        }
        validationResultFile {
            id
            name
            extension
            contentType
            size
            link
            blob
        }
    }
}