mutation($employeeDataFile: Upload!, $options: BulkOnboardingOptions!) {
    bulkOnboardingTrigger(employeeDataFile: $employeeDataFile, options: $options) {
        id
        status
        startTime
        validationResult {
            validEmployeeCount
            errorEmployeeCount
            totalEmployeeCount
            errors {
                message
                rowNumber
            }
            warnings {
                message
                rowNumber
            }
            validationErrors {
                message
                rowNumber
            }
            validationResultFile {
                id
                name
                extension
                contentType
                size
                link
                blob
            }
        }
        onboardingResult {
            onboardedEmployeeCount
              errors {
                  message
                  rowNumber
              }
              warnings {
                  message
                  rowNumber
              }
        }
    }
}