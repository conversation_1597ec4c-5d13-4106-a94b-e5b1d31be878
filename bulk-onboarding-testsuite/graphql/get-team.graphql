query GetTeam {
    company {
        id
        contracts(includeAnyStatus: true) {
            id
            type
            term
            startOn
            country
            status
            member {
                id
                firstName
                lastName
                status
                emails {
                    email
                }
                phoneNos {
                    phoneNo
                }
                legalData {
                    key
                    value
                }
                legalDocuments {
                    key
                    label
                }
                bankAccounts {
                    accountName
                    accountNumber
                    bankName
                    branchName
                }
            }
            compensation {
                basePay {
                    amount
                    frequency
                    currency
                }
            }
            onboarding {
                status
            }
        }
    }
}