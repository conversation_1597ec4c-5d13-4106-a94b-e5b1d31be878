import queries as q
import pandas as pd
import os
from helpers import pretty_print
from helpers import write_file_from_response
from testcase import TestCase, ActionResult
from excel import get_headers, get_contract_data, update_contract_data, get_validation_errors
from fuzzy_matches import FUZZY_MATCHES
from deepdiff import DeepDiff

CONTRACT_ID = 'contractId'
EMAIL = 'email'
EMPLOYEE_ID = 'employeeId'

def validate(testcase):
    print('Bulk validating...')
    result = q.bulk_validate(testcase)
    if result:
        error_count = result['data']['bulkOnboardingValidate']['errorEmployeeCount']
        if error_count > 0 or (
                'errors' in result['data']['bulkOnboardingValidate'] and result['data']['bulkOnboardingValidate']['errors']):
            print('Validation failed, please check validation report file in responses folder')
            if 'validationResultFile' in result['data']['bulkOnboardingValidate'] and result['data']['bulkOnboardingValidate']['validationResultFile']:
                file_path = write_file_from_response(result['data']['bulkOnboardingValidate']['validationResultFile'])
                validation_errors = get_validation_errors(file_path)
                print(f'Validation errors: {validation_errors}')
            else:
                pretty_print(result)
        else:
            print('Validation completed successfully, no errors found')
            return testcase.complete(ActionResult.VALIDATION_OK)
    else:
        print('Validation failed')

    return testcase.complete(ActionResult.VALIDATION_FAILED)

def download_template(testcase):
    print('Downloading template...')
    result = q.download_template(testcase)
    if result:
        file_path = write_file_from_response(result['data']['bulkOnboardingTemplateDownload'])
        return verify_template(testcase, file_path)
    else:
        print('Downloading template failed')

    return testcase.complete(ActionResult.TEMPLATE_DOWNLOAD_FAILED)

def verify_template(testcase, file_path):
    headers = get_headers(file_path)
    diff = DeepDiff(testcase.expected_columns, headers, ignore_order=True)
    if not diff:
        print('Template downloaded successfully')
        return testcase.complete(ActionResult.TEMPLATE_OK)
    else:
        print(f'Downloaded template does not match, diff: {diff}')
        return testcase.complete(ActionResult.TEMPLATE_NOT_MATCH)

def onboard(testcase):
    print('Bulk onboarding...')
    result = q.bulk_create(testcase)
    if result:
        status = result['data']['bulkOnboardingTrigger']['status']
        if status == 'SUCCESS':
            pretty_print(result['data']['bulkOnboardingTrigger']['onboardingResult'])
            print('Bulk onboarding completed successfully')
            return verify_onboarded_contracts(testcase)
        else:
            print(f'Bulk onboarding failed with status {status}, please check error logs')
            if 'validationResult' in result['data']['bulkOnboardingTrigger'] and result['data']['bulkOnboardingTrigger']['validationResult']:
                write_file_from_response(result['data']['bulkOnboardingTrigger']['validationResult'])
    else:
        print('Bulk onboarding failed')

    return testcase.complete(ActionResult.ONBOARD_FAILED)

def update(testcase):
    print('Bulk updating...')
    print('Downloading template with existing contracts...')
    result = q.download_template(testcase)
    file_path = write_file_from_response(result['data']['bulkOnboardingTemplateDownload'])
    contract_data_existing = get_contract_data(file_path)
    contract_data_for_update = get_contract_data(testcase.onboarding_file)
    missing_identifiers = []
    identifier_to_contract_id = {}
    for contract_for_update in contract_data_for_update:
        identifier_key = EMPLOYEE_ID if EMPLOYEE_ID in contract_for_update else EMAIL
        identifier = contract_for_update[identifier_key]
        contract_existing = find_contract_by_identifier(contract_data_existing, identifier_key, identifier)
        if contract_existing:
            identifier_to_contract_id[identifier] = contract_existing[CONTRACT_ID]
        else:
            missing_identifiers.append(identifier)

    if missing_identifiers:
        print(f'Contracts not found for: {missing_identifiers}')
        return testcase.complete(ActionResult.UPDATE_FAILED)

    new_onboarding_file_path = f'./responses/{os.path.basename(testcase.onboarding_file)}'
    update_contract_data(testcase.onboarding_file, identifier_to_contract_id, new_onboarding_file_path)
    testcase.onboarding_file = new_onboarding_file_path

    result = onboard(testcase)
    if result.actual_result == ActionResult.ONBOARD_OK:
        print('Bulk updating completed successfully')
        return testcase.complete(ActionResult.UPDATE_OK)
    else:
        print('Bulk updating failed')
        return testcase.complete(ActionResult.UPDATE_FAILED)


def verify_onboarded_contracts(testcase):
    print('Downloading onboarded contracts...')
    result = q.download_template(testcase)
    if result:
        file_path = write_file_from_response(result['data']['bulkOnboardingTemplateDownload'])
        contract_data_from_template = get_contract_data(testcase.onboarding_file)
        contract_data_from_response = get_contract_data(file_path)
        missing_identifiers = []
        mismatched_identifiers = []
        for contract_from_template in contract_data_from_template:
            identifier_key = EMPLOYEE_ID if EMPLOYEE_ID in contract_from_template else EMAIL
            identifier = contract_from_template[identifier_key]
            contract_from_response = find_contract_by_identifier(contract_data_from_response, identifier_key, identifier)
            if not contract_from_response:
                missing_identifiers.append(identifier)
            elif not contract_matches(contract_from_template, contract_from_response, identifier):
                mismatched_identifiers.append(identifier)

        if missing_identifiers or mismatched_identifiers:
            print(f'Onboarded contracts do not match, missing contracts: {missing_identifiers}, mismatched contracts: {mismatched_identifiers}')
        else:
            print('Onboarded contracts match')
            return testcase.complete(ActionResult.ONBOARD_OK)
    else:
        print('Downloading template failed')

    return testcase.complete(ActionResult.ONBOARD_FAILED)

def find_contract_by_identifier(contracts, identifier_key, identifier):
    for contract in contracts:
        if not pd.isna(contract[identifier_key]) and contract[identifier_key].lower() == identifier.lower():
            return contract
    return None

def contract_matches(contract_from_template, contract_from_response, identifier):
    match = True
    for key in contract_from_template:
        expected = contract_from_template[key]
        actual = contract_from_response[key]
        if key in (EMAIL, 'workEmail'):
            expected = expected.lower()
            actual = actual.lower()
        if not value_matches(expected, actual):
            if (key == CONTRACT_ID or key == 'workStatus') and pd.isna(expected):
                continue
            if key == 'rowIdentifier':
                continue
            match = False
            print(f'Contract data does not match for {identifier}, key: {key}, expected: {expected}, actual: {actual}')

    return match

def value_matches(expected, actual):
    if expected == actual:
        return True
    if pd.isna(expected) and pd.isna(actual):
        return True
    if (expected, actual) in FUZZY_MATCHES or (actual, expected) in FUZZY_MATCHES:
        return True

    return False

if (__name__ == '__main__'):
    testcase = TestCase.parse('staging.eor-template-can')
    verify_template(testcase, './templates/eor/can-validation-ok.xlsx')