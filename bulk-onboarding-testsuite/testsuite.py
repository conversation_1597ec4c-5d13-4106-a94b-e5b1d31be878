import sys
import time
from enum import Enum
from rich import print
from test import run_test

class TestSuiteStatus(Enum):
    PASSED = "PASSED"
    FAILED = "FAILED"

    def styled(self):
        return f'[bold green]{self.value}[/bold green]' if self == TestSuiteStatus.PASSED else f'[bold red]{self.value}[/bold red]'

class TestSuiteResult:
    def __init__(self, suite_name, status):
        self.name = suite_name
        self.results = results
        self.status = status

def run_suite(suite_name):
    print('Running suite:', suite_name)
    file = open(f'testcases/{suite_name}', 'r')
    testcases = [line.strip() for line in file if line.strip()]
    file.close()
    print('Testcases:', len(testcases))
    print(testcases)
    results = []
    for testcase in testcases:
        result = run_test(testcase)
        results.append(result)

    print()
    print()
    print('=' * 80)
    print('Suite:', suite_name)
    for result in results:
        print(f' \u2022 {result.name}', result.status.styled())
    passed_results = list(filter(lambda result: result.is_passed(), results))
    failed_results = list(filter(lambda result: result.is_failed(), results))
    if (len(failed_results) > 0):
        print(f'Results: {len(passed_results)} [bold green]PASSED[/bold green], {len(failed_results)} [bold red]FAILED[/bold red]')
        return TestSuiteResult(suite_name, TestSuiteStatus.FAILED)
    else:
        print(f'Results: {len(passed_results)} [bold green]PASSED[/bold green]')
        return TestSuiteResult(suite_name, TestSuiteStatus.PASSED)

start_time = time.time()

args = sys.argv[1:]
print('args', args)

results = []
for arg in args:
    result = run_suite(arg)
    results.append(result)

finalResult = 0
print('!' * 80)
for result in results:
    print(f'- Suite: {result.name}, result: {result.status.styled()}')
if any(result.status == TestSuiteStatus.FAILED for result in results):
    finalResult = 1
    print(f'==> Final result: {TestSuiteStatus.FAILED.styled()}')
else:
    print(f'==> Final result: {TestSuiteStatus.PASSED.styled()}')

elapsed_time = time.time() - start_time
print('All test suite(s) completed in {:.2f} seconds'.format(elapsed_time))
sys.exit(finalResult)
