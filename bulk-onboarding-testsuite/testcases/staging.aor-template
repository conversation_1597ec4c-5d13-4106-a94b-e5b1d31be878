[default]
description = Download AOR bulk onboarding template, since we haven't built it yet, we use GP template for a rare country, VAT

env=staging
experience=ops

action = template
context = AOR
company_id = 508699
entity_id = 10258358
country_code = VAT
onboarding_file = #does not apply
expected_result = TEMPLATE_OK
expected_columns =
   {
        "AOR Employee Data": [
            "employeeId",
            "firstName", "lastName", "email", "gender", "phoneNumber",
            "address.line1", "address.line2", "address.city", "address.state", "address.country", "address.postalCode",
            "position", "startOn", "endOn", "scope",
            "noticeAfterProbation.value", "noticeAfterProbation.unit",
            "payrollFrequency", "rateFrequency", "currency", "basePay"
        ]
   }

