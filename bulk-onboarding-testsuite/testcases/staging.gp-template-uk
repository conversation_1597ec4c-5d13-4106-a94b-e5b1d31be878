[default]
description = Download GP bulk onboarding template for UK

env=staging
experience=ops

action = template
context = GLOBAL_PAYROLL
company_id = 508726
entity_id = ********
country_code = GBR
onboarding_file = #does not apply
expected_result = TEMPLATE_OK
expected_columns =
  {
    "Employee Data":
      [
        "employeeId", "contractId", "position", "startOn",
        "firstName", "lastName", "email", "gender", "phoneNumber",
        "address.line1", "address.city", "address.state", "address.country", "address.postalCode",
        "dateOfBirth", "nationality", "nationalInsuranceNumber",
        "bank.address.country", "bank.accountHolderName", "bank.bankName", "bank.accountNumber", "bank.sortCode", "bank.swiftCode", "bank.branchName",
        "payrollFrequency", "rateFrequency", "basePay",
        "homeOfficeAllowance", "internetAllowance", "phoneAllowance", "travelAllowance", "otherAllowance"
      ]
  }