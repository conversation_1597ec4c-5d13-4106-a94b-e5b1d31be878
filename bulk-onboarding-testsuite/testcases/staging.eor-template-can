[default]
description = Download EOR bulk onboarding template for Canada

env=staging
experience=ops

action = template
context = EOR
company_id = 508726
entity_id = 0
country_code = CAN
onboarding_file = #does not apply
expected_result = TEMPLATE_OK
expected_columns =
  {
    "Basic Details":
      [
        "rowIdentifier", "contractId", "country", "state", "workStatus",
        "firstName", "lastName", "gender", "email",
        "position", "startOn", "endOn", "scope", "phoneNumber", "nationality"
      ],

    "Insurance":
      [
        "rowIdentifier", "contractId", "country", "insuranceType", "insurancePlan", "numberOfDependant"
      ],

    "Compliance & Leaves":
      [
        "rowIdentifier", "contractId", "probationPolicy.value", "probationPolicy.unit",
        "noticeAfterProbation.value", "noticeAfterProbation.unit", "nonSolicit.value", "nonSolicit.unit"
      ],

    "Compensation":
      [
        "rowIdentifier", "contractId", "currency", "basePay", "rateFrequency", "payrollFrequency",
        "joiningBonus.amount", "joiningBonus.payOn", "variablePerformanceBonus.frequency", "variablePerformanceBonus.payOn",
        "mobileAndPhoneAllowance.amountType", "mobileAndPhoneAllowance.amount", "mobileAndPhoneAllowance.frequency", "mobileAndPhoneAllowance.payOn",
        "internetAllowance.amountType", "internetAllowance.amount", "internetAllowance.frequency", "internetAllowance.payOn",
        "otherAllowance.amountType", "otherAllowance.amount", "otherAllowance.frequency", "otherAllowance.payOn"
      ]
  }
