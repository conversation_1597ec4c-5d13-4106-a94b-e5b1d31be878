[default]
description = Download GP bulk onboarding template for USA

env=staging
experience=ops

action = template
context = GLOBAL_PAYROLL
company_id = 508726
entity_id = ********
country_code = USA
onboarding_file = #does not apply
expected_result = TEMPLATE_OK
expected_columns =
  {
    "Employee Data":
      [
        "employeeId", "contractId", "position", "startOn",
        "firstName", "lastName", "email", "gender", "phoneNumber",
        "address.line1", "address.city", "address.state", "address.country", "address.postalCode",
        "dateOfBirth", "nationality", "socialSecurityNumber", "maritalStatus", "numberOfDependants",
        "bank.address.country", "bank.accountHolderName", "bank.accountNumber", "bank.swiftCode", "bank.bankName", "bank.transitNumber",
        "payrollFrequency", "rateFrequency", "basePay",
        "healthcareAllowance", "internetAllowance", "phoneAllowance", "otherAllowance"
      ]
  }