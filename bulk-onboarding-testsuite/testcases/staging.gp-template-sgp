[default]
description = Download GP bulk onboarding template for Singapore

env=staging
experience=ops

action = template
context = GLOBAL_PAYROLL
company_id = 508726
entity_id = ********
country_code = SGP
onboarding_file = #does not apply
expected_result = TEMPLATE_OK
expected_columns =
  {
    "Employee Data":
      [
        "employeeId", "contractId", "position", "startOn",
        "firstName", "lastName", "email", "gender", "race", "phoneNumber",
        "address.line1", "address.city", "address.state", "address.country", "address.postalCode",
        "nationality", "immigrationStatus", "workPassType", "nid", "passportNumber", "workPassNumber",
        "bank.accountHolderName", "bank.accountNumber", "bank.swiftCode", "bank.bankName", "bank.address.country",
        "payrollFrequency", "rateFrequency", "basePay",
        "medicalAllowance", "internetAllowance", "carAllowance", "transportAllowance", "insuranceAllowance", "phoneAllowance", "rentalAllowance", "mealAllowance", "homeOfficeAllowance"
      ]
  }