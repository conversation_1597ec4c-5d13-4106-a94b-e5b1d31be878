[default]
description = Download GP bulk onboarding template for Canada

env=staging
experience=ops

action = template
context = GLOBAL_PAYROLL
company_id = 508726
entity_id = ********
country_code = CAN
onboarding_file = #does not apply
expected_result = TEMPLATE_OK
expected_columns =
  {
    "Employee Data":
      [
        "employeeId", "contractId", "position", "startOn",
        "firstName", "lastName", "email", "gender", "phoneNumber",
        "address.line1", "address.city", "address.state", "address.country", "address.zipcode",
        "dateOfBirth", "nationality", "socialInsuranceNumber", "socialInsuranceNumberExpiryDate", "maritalStatus", "numberOfDependants",
        "bank.address.country", "bank.accountHolderName", "bank.accountNumber", "bank.swiftCode", "bank.bankName", "bank.transitNumber", "bank.institutionNumber",
        "payrollFrequency", "rateFrequency", "basePay",
        "internetAllowance", "phoneAllowance", "otherAllowance"
      ]
  }