{"name": "graph-registry", "version": "1.0.0", "description": "A graph registry for supporting the version control and federation of graphql across multiplier. This registry uses `graphql-inspector` and `rover` to compose subgraphs from each schema for each microservice and to generate a supergraph respectively.", "main": "merge-subgraphs.js", "dependencies": {"@graphql-tools/utils": "^10.8.3", "apollo-server": "^3.6.7", "graphql": "^16.3.0"}, "devDependencies": {"@apollo/rover": "^0.10.0", "@graphql-inspector/cli": "^3.4.0", "@graphql-tools/graphql-file-loader": "^7.5.16", "@graphql-tools/load": "7.7.7", "@graphql-tools/load-files": "6.5.3", "@graphql-tools/merge": "^8.2.10", "@types/node": "^14.14.31", "js-yaml": "^4.1.0"}, "scripts": {"clean": "node supergraph/clean-supergraph.js", "clean-auth": "node supergraph/clean-supergraph-auth.js", "compose": "rover supergraph compose --config supergraph/supergraph-config.yml > supergraph/supergraph-schema.graphql", "diff": "graphql-inspector diff supergraph-schema.graphql supergraph/supergraph-schema.graphql", "generate-supergraph": "npm run compose && npm run introspect", "generate-supergraph-auth": "node supergraph/generate-supergraph-auth.js", "generate-split-auth": "node supergraph/generate-split-auth.js", "introspect": "graphql-inspector introspect supergraph/supergraph-schema.graphql --federation --write supergraph/supergraph-api.json"}, "repository": {"type": "git", "url": "git+https://github.com/Multiplier-Core/graph-registry.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Multiplier-Core/graph-registry/issues"}, "homepage": "https://github.com/Multiplier-Core/graph-registry#readme", "volta": {"node": "18.20.5"}}