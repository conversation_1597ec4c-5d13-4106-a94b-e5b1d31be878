{"name": "graph-registry", "version": "1.0.0", "lockfileVersion": 2, "requires": true, "packages": {"": {"name": "graph-registry", "version": "1.0.0", "license": "ISC", "dependencies": {"@graphql-tools/utils": "^10.8.3", "apollo-server": "^3.6.7", "graphql": "^16.3.0"}, "devDependencies": {"@apollo/rover": "^0.10.0", "@graphql-inspector/cli": "^3.4.0", "@graphql-tools/graphql-file-loader": "^7.5.16", "@graphql-tools/load": "7.7.7", "@graphql-tools/load-files": "6.5.3", "@graphql-tools/merge": "^8.2.10", "@types/node": "^14.14.31", "js-yaml": "^4.1.0"}}, "node_modules/@ampproject/remapping": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz", "integrity": "sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==", "dev": true, "peer": true, "dependencies": {"@jridgewell/gen-mapping": "^0.1.0", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@apollo/protobufjs": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/@apollo/protobufjs/-/protobufjs-1.2.2.tgz", "integrity": "sha512-vF+zxhPiLtkwxONs6YanSt1EpwpGilThpneExUN5K3tCymuxNnVq2yojTvnpRjv2QfsEIt/n7ozPIIzBLwGIDQ==", "hasInstallScript": true, "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.0", "@types/node": "^10.1.0", "long": "^4.0.0"}, "bin": {"apollo-pbjs": "bin/pbjs", "apollo-pbts": "bin/pbts"}}, "node_modules/@apollo/protobufjs/node_modules/@types/node": {"version": "10.17.60", "resolved": "https://registry.npmjs.org/@types/node/-/node-10.17.60.tgz", "integrity": "sha512-F0KIgDJfy2nA3zMLmWGKxcH2ZVEtCZXHHdOQs2gSaQ27+lNeEfGxzkIw90aXswATX7AZ33tahPbzy6KAfUreVw=="}, "node_modules/@apollo/rover": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/@apollo/rover/-/rover-0.10.0.tgz", "integrity": "sha512-5zJCsPIZVO9gEVH1vgrys5vgys1GamQbbh8emAyR8fMAkdpAKmKoiYm23Cxz23tV9KxU8X9omIA+8ycnqRBd9g==", "dev": true, "hasInstallScript": true, "dependencies": {"axios-proxy-builder": "^0.1.1", "binary-install": "^1.0.6", "console.table": "^0.10.0", "detect-libc": "^2.0.0"}, "bin": {"rover": "run.js"}, "engines": {"node": ">=14", "npm": ">=6"}}, "node_modules/@apollographql/apollo-tools": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/@apollographql/apollo-tools/-/apollo-tools-0.5.3.tgz", "integrity": "sha512-VcsXHfTFoCodDAgJZxN04GdFK1kqOhZQnQY/9Fa147P+I8xfvOSz5d+lKAPB+hwSgBNyd7ncAKGIs4+utbL+yA==", "engines": {"node": ">=8", "npm": ">=6"}, "peerDependencies": {"graphql": "^14.2.1 || ^15.0.0 || ^16.0.0"}}, "node_modules/@apollographql/graphql-playground-html": {"version": "1.6.29", "resolved": "https://registry.npmjs.org/@apollographql/graphql-playground-html/-/graphql-playground-html-1.6.29.tgz", "integrity": "sha512-xCcXpoz52rI4ksJSdOCxeOCn2DLocxwHf9dVT/Q90Pte1LX+LY+91SFtJF3KXVHH8kEin+g1KKCQPKBjZJfWNA==", "dependencies": {"xss": "^1.0.8"}}, "node_modules/@ardatan/sync-fetch": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@ardatan/sync-fetch/-/sync-fetch-0.0.1.tgz", "integrity": "sha512-xhlTqH0m31mnsG0tIP4ETgfSB6gXDaYYsUWTrlUV93fFQPI9dd8hE0Ot6MHLCtqgB32hwJAC3YZMWlXZw7AleA==", "dev": true, "dependencies": {"node-fetch": "^2.6.1"}, "engines": {"node": ">=14"}}, "node_modules/@babel/code-frame": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "integrity": "sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==", "dev": true, "dependencies": {"@babel/highlight": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.20.5.tgz", "integrity": "sha512-KZXo2t10+/jxmkhNXc7pZTqRvSOIvVv/+lJwHS+B2rErwOyjuVRh60yVpb7liQ1U5t7lLJ1bz+t8tSypUZdm0g==", "dev": true, "peer": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.20.5.tgz", "integrity": "sha512-UdOWmk4pNWTm/4DlPUl/Pt4Gz4rcEMb7CY0Y3eJl5Yz1vI8ZJGmHWaVE55LoxRjdpx0z259GE9U5STA9atUinQ==", "dev": true, "peer": true, "dependencies": {"@ampproject/remapping": "^2.1.0", "@babel/code-frame": "^7.18.6", "@babel/generator": "^7.20.5", "@babel/helper-compilation-targets": "^7.20.0", "@babel/helper-module-transforms": "^7.20.2", "@babel/helpers": "^7.20.5", "@babel/parser": "^7.20.5", "@babel/template": "^7.18.10", "@babel/traverse": "^7.20.5", "@babel/types": "^7.20.5", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.1", "semver": "^6.3.0"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "peer": true, "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@babel/core/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true, "peer": true}, "node_modules/@babel/generator": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.5.tgz", "integrity": "sha512-jl7JY2Ykn9S0yj4DQP82sYvPU+T3g0HFcWTqDLqiuA9tGRNIj9VfbtXGAYTTkyNEnQk1jkMGOdYka8aG/lulCA==", "dev": true, "dependencies": {"@babel/types": "^7.20.5", "@jridgewell/gen-mapping": "^0.3.2", "jsesc": "^2.5.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/generator/node_modules/@jridgewell/gen-mapping": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "integrity": "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==", "dev": true, "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.20.0", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz", "integrity": "sha512-0jp//vDGp9e8hZzBc6N/KwA5ZK3Wsm/pfm4CrY7vzegkVxc65SgSn6wYOnwHe9Js9HRQ1YTCKLGPzDtaS3RoLQ==", "dev": true, "peer": true, "dependencies": {"@babel/compat-data": "^7.20.0", "@babel/helper-validator-option": "^7.18.6", "browserslist": "^4.21.3", "semver": "^6.3.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-environment-visitor": {"version": "7.18.9", "resolved": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz", "integrity": "sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-function-name": {"version": "7.19.0", "resolved": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.19.0.tgz", "integrity": "sha512-WAwHBINyrpqywkUH0nTnNgI5ina5TFn85HKS0pbPDfxFfhyR/aNQEn4hGi1P1JyT//I0t4OgXUlofzWILRvS5w==", "dev": true, "dependencies": {"@babel/template": "^7.18.10", "@babel/types": "^7.19.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-hoist-variables": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "integrity": "sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==", "dev": true, "dependencies": {"@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "integrity": "sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==", "dev": true, "peer": true, "dependencies": {"@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.20.2", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.20.2.tgz", "integrity": "sha512-zvBKyJXRbmK07XhMuujYoJ48B5yvvmM6+wcpv6Ivj4Yg6qO7NOZOSnvZN9CRl1zz1Z4cKf8YejmCMh8clOoOeA==", "dev": true, "peer": true, "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-simple-access": "^7.20.2", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-validator-identifier": "^7.19.1", "@babel/template": "^7.18.10", "@babel/traverse": "^7.20.1", "@babel/types": "^7.20.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.20.2", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "integrity": "sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-simple-access": {"version": "7.20.2", "resolved": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz", "integrity": "sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA==", "dev": true, "peer": true, "dependencies": {"@babel/types": "^7.20.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-split-export-declaration": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "integrity": "sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==", "dev": true, "dependencies": {"@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.19.4", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz", "integrity": "sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.19.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "integrity": "sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz", "integrity": "sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==", "dev": true, "peer": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.20.6", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.20.6.tgz", "integrity": "sha512-Pf/OjgfgFRW5bApskEz5pvidpim7tEDPlFtKcNRXWmfHGn9IEI2W2flqRQXTFb7gIPTyK++N6rVHuwKut4XK6w==", "dev": true, "peer": true, "dependencies": {"@babel/template": "^7.18.10", "@babel/traverse": "^7.20.5", "@babel/types": "^7.20.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz", "integrity": "sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==", "dev": true, "dependencies": {"@babel/helper-validator-identifier": "^7.18.6", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight/node_modules/ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "dependencies": {"color-name": "1.1.3"}}, "node_modules/@babel/highlight/node_modules/color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==", "dev": true}, "node_modules/@babel/highlight/node_modules/has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==", "dev": true, "engines": {"node": ">=4"}}, "node_modules/@babel/highlight/node_modules/supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/@babel/parser": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.5.tgz", "integrity": "sha512-r27t/cy/m9uKLXQNWWebeCUHgnAZq0CpG1OwKRxzJMP1vpSU4bSIK2hq+/cp0bQxetkXx38n09rNu8jVkcK/zA==", "dev": true, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-syntax-import-assertions": {"version": "7.20.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.20.0.tgz", "integrity": "sha512-IUh1vakzNoWalR8ch/areW7qFopR2AEw03JlG7BbrDqmQ4X3q9uuipQwSGrUn7oGiemKjtSLDhNtQHzMHr1JdQ==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/template": {"version": "7.18.10", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.18.10.tgz", "integrity": "sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==", "dev": true, "dependencies": {"@babel/code-frame": "^7.18.6", "@babel/parser": "^7.18.10", "@babel/types": "^7.18.10"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.20.5.tgz", "integrity": "sha512-WM5ZNN3JITQIq9tFZaw1ojLU3WgWdtkxnhM1AegMS+PvHjkM5IXjmYEGY7yukz5XS4sJyEf2VzWjI8uAavhxBQ==", "dev": true, "dependencies": {"@babel/code-frame": "^7.18.6", "@babel/generator": "^7.20.5", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/parser": "^7.20.5", "@babel/types": "^7.20.5", "debug": "^4.1.0", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/@babel/traverse/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true}, "node_modules/@babel/types": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.20.5.tgz", "integrity": "sha512-c9fst/h2/dcF7H+MJKZ2T0KjEQ8hY/BNnDk/H3XY8C4Aw/eWQXWn/lWntHF9ooUBnGmEvbfGrTgLWc+um0YDUg==", "dev": true, "dependencies": {"@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "to-fast-properties": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@colors/colors": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@colors/colors/-/colors-1.5.0.tgz", "integrity": "sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==", "dev": true, "optional": true, "engines": {"node": ">=0.1.90"}}, "node_modules/@envelop/core": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/@envelop/core/-/core-2.6.0.tgz", "integrity": "sha512-yTptKinJN//i6m1kXUbnLBl/FobzddI4ehURAMS08eRUOQwAuXqJU9r8VdTav8nIZLb4t6cuDWFb3n331LiwLw==", "dev": true, "dependencies": {"@envelop/types": "2.4.0", "tslib": "2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@envelop/core/node_modules/tslib": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz", "integrity": "sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==", "dev": true}, "node_modules/@envelop/disable-introspection": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/@envelop/disable-introspection/-/disable-introspection-3.6.0.tgz", "integrity": "sha512-Zf505LqWo2efM0EtTBvXyYPynSTSL2+Ps0X+dCH33vQ3wF6gxMui9g1pGa4MDcGjaXWG67qZcT4eojpGlmmavA==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"@envelop/core": "^2.6.0", "graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@envelop/disable-introspection/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@envelop/parser-cache": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/@envelop/parser-cache/-/parser-cache-4.7.0.tgz", "integrity": "sha512-63NfXDcW/vGn4U6NFxaZ0JbYWAcJb9A6jhTvghsSz1ZS+Dny/ci8bVSgVmM1q+N56hPyGsVPuyI+rIc71mPU5g==", "dev": true, "dependencies": {"lru-cache": "^6.0.0", "tslib": "^2.4.0"}, "peerDependencies": {"@envelop/core": "^2.6.0", "graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@envelop/parser-cache/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@envelop/types": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@envelop/types/-/types-2.4.0.tgz", "integrity": "sha512-pjxS98cDQBS84X29VcwzH3aJ/KiLCGwyMxuj7/5FkdiaCXAD1JEvKEj9LARWlFYj1bY43uII4+UptFebrhiIaw==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@envelop/types/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@envelop/validation-cache": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/@envelop/validation-cache/-/validation-cache-4.7.0.tgz", "integrity": "sha512-PzL+GfWJRT+JjsJqZAIxHKEkvkM3hxkeytS5O0QLXT8kURNBV28r+Kdnn2RCF5+6ILhyGpiDb60vaquBi7g4lw==", "dev": true, "dependencies": {"lru-cache": "^6.0.0", "tslib": "^2.4.0"}, "peerDependencies": {"@envelop/core": "^2.6.0", "graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@envelop/validation-cache/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-inspector/audit-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/audit-command/-/audit-command-3.4.0.tgz", "integrity": "sha512-q4bgl/TaQLIGrQZVg95wUbzfkzkyUOhWfni3JaKpbc75ZLz6kU1c5lHL3do637QFqttxgWwmMLXJi1jO0FL+tA==", "dev": true, "dependencies": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "cli-table3": "0.6.2", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/audit-command/node_modules/@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "yargs": "17.2.1"}}, "node_modules/@graphql-inspector/audit-command/node_modules/@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-tools/utils": "^6.0.0 || ^7.0.0 || ^8.0.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/audit-command/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-inspector/audit-command/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-inspector/cli": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/cli/-/cli-3.4.0.tgz", "integrity": "sha512-4+fY42ktYKymCGJ78hADHskqHpi2X08+uYAiE7EBfqqueUFrMj1pLmJYXNnK/5TQ6/XaLvyvussw/W/eQNmuYg==", "dev": true, "dependencies": {"@graphql-inspector/audit-command": "3.4.0", "@graphql-inspector/code-loader": "3.4.0", "@graphql-inspector/commands": "3.4.0", "@graphql-inspector/coverage-command": "3.4.0", "@graphql-inspector/diff-command": "3.4.0", "@graphql-inspector/docs-command": "3.4.0", "@graphql-inspector/git-loader": "3.4.0", "@graphql-inspector/github-loader": "3.4.0", "@graphql-inspector/graphql-loader": "3.4.0", "@graphql-inspector/introspect-command": "3.4.0", "@graphql-inspector/json-loader": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "@graphql-inspector/serve-command": "3.4.0", "@graphql-inspector/similar-command": "3.4.0", "@graphql-inspector/url-loader": "3.4.0", "@graphql-inspector/validate-command": "3.4.0", "tslib": "^2.0.0", "yargs": "17.2.1"}, "bin": {"graphql-inspector": "index.js"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/cli/node_modules/@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "yargs": "17.2.1"}}, "node_modules/@graphql-inspector/cli/node_modules/@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "license": "MIT", "dependencies": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-tools/utils": "^6.0.0 || ^7.0.0 || ^8.0.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/cli/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-inspector/cli/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-inspector/code-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/code-loader/-/code-loader-3.4.0.tgz", "integrity": "sha512-rgC6x56QLIHGHzz6+QeBhPZz3CgwxqSwKJO0LZgi6wEkurbrrVlEZTUTy3LE9RSfHFdL7w3v9vfBjls3vp9wIg==", "dev": true, "dependencies": {"@graphql-tools/code-file-loader": "^7.2.2", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/config": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/config/-/config-3.4.0.tgz", "integrity": "sha512-h9qLXP+f7IA6v3WT+3hMOl3hdxhVSdYcgajzXkJx5p5OYyibyTzsMJPghplXxoORcjQL3MMpRXg90+OVHxq+zw==", "dev": true, "peer": true, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/core": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/core/-/core-3.4.0.tgz", "integrity": "sha512-035TchqbbyWaNDOJKxp+RG6Ho/nGMXKKgzojQ6Dn1N20jgR/oX9rTVvR8qkzpy0/IxxP8PD9RcZ52vsTAehxdg==", "dev": true, "dependencies": {"dependency-graph": "0.11.0", "object-inspect": "1.10.3", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/core/node_modules/object-inspect": {"version": "1.10.3", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.10.3.tgz", "integrity": "sha512-e5mCJlSH7poANfC8z8S9s9S2IN5/4Zb3aZ33f5s8YqoazCFzNLloLU8r5VCG+G7WoqLvAAZoVMcy3tp/3X0Plw==", "dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/@graphql-inspector/coverage-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/coverage-command/-/coverage-command-3.4.0.tgz", "integrity": "sha512-TmBg0I+DUsxGzuasZs9WkB/MR3iH4KUhkRRQsSlT+PKzunt1njUyYd1UeTpaZCmiTypF4m1jb7lAeDw3HDw2Ww==", "dev": true, "dependencies": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/coverage-command/node_modules/@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "yargs": "17.2.1"}}, "node_modules/@graphql-inspector/coverage-command/node_modules/@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-tools/utils": "^6.0.0 || ^7.0.0 || ^8.0.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/coverage-command/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-inspector/coverage-command/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-inspector/diff-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/diff-command/-/diff-command-3.4.0.tgz", "integrity": "sha512-x2sgPwVpHzG4ranETqszjy/WL+TVmxRirtNuF3yBMQS7XcfNIEXljIcTU2m+Ar7TNsTFZNMcDcw4xLmUrTnrRw==", "dev": true, "dependencies": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/diff-command/node_modules/@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "yargs": "17.2.1"}}, "node_modules/@graphql-inspector/diff-command/node_modules/@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-tools/utils": "^6.0.0 || ^7.0.0 || ^8.0.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/diff-command/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-inspector/diff-command/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-inspector/docs-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/docs-command/-/docs-command-3.4.0.tgz", "integrity": "sha512-+GN8bdUduKYQvzwygzNtWeOaKzIFpifihTsrAspErHIq+rXs1MWerktTIj9m/Y7AF9uF/cI04SfxzHXhfvUEig==", "dev": true, "dependencies": {"@graphql-inspector/commands": "3.4.0", "open": "8.2.1", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/docs-command/node_modules/@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "yargs": "17.2.1"}}, "node_modules/@graphql-inspector/docs-command/node_modules/@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-tools/utils": "^6.0.0 || ^7.0.0 || ^8.0.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/docs-command/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-inspector/docs-command/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-inspector/git-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/git-loader/-/git-loader-3.4.0.tgz", "integrity": "sha512-/5zdyFXMgMadyuAN6mDTfcien1vvO31X/hU1dOAdyOGjhl5ZkH/D5ZgYjYc94VSsoixGaQOweq5Kl95cZeTlgA==", "dev": true, "dependencies": {"@graphql-tools/git-loader": "^7.1.2", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/github-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/github-loader/-/github-loader-3.4.0.tgz", "integrity": "sha512-+kV2PT5BIDMFIdgkS04ESmwcyuV8SBQ3Furp7snXtGAK+8cYi8PmL0/qIztzp7/3nzQ9WV//P/RloT8dEdRDCw==", "dev": true, "dependencies": {"@graphql-tools/github-loader": "^7.1.1", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/graphql-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/graphql-loader/-/graphql-loader-3.4.0.tgz", "integrity": "sha512-igDqEhmYa5FTrw/Q25a8iTs3qNc//5DR9CIzxnYS9/auuhYz3q7NWistO76WWhNu2IAs1ReMM1xrexzsTxB5QA==", "dev": true, "dependencies": {"@graphql-tools/graphql-file-loader": "^7.3.2", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/introspect-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/introspect-command/-/introspect-command-3.4.0.tgz", "integrity": "sha512-rASIY8eo+IKwDMaOTT8GYxbqSazgHcdrUJzxjCkD1Rir7U0nCs8NvGiQPM0sbcpelEGNsy+wjRuxdy3iBxI8HA==", "dev": true, "dependencies": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/introspect-command/node_modules/@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "yargs": "17.2.1"}}, "node_modules/@graphql-inspector/introspect-command/node_modules/@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-tools/utils": "^6.0.0 || ^7.0.0 || ^8.0.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/introspect-command/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-inspector/introspect-command/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-inspector/json-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/json-loader/-/json-loader-3.4.0.tgz", "integrity": "sha512-prXZ0UaSBMTqbp+CAP9Rc2P03EwF8yfnKfP7HlXJ0NT2G8bmdUD1fODphUO2mqB/Hdsp4zo7SxIoq6D6Qq9sOQ==", "dev": true, "dependencies": {"@graphql-tools/json-file-loader": "^7.3.2", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/logger": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/logger/-/logger-3.4.0.tgz", "integrity": "sha512-r2C0tCLQMInn6knQNUN4Gm9qGE98Bo070JwXpJkRXW51Vt9eAF8WEt4rjg2rMYeVjMLR7ROF328sjMo1v0ihVQ==", "dev": true, "dependencies": {"chalk": "4.1.2", "figures": "3.2.0", "log-symbols": "^4.1.0", "std-env": "2.3.0", "tslib": "^2.0.0"}}, "node_modules/@graphql-inspector/serve-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/serve-command/-/serve-command-3.4.0.tgz", "integrity": "sha512-7xLsNRKwiXG8te9w2QCy/Od+E4kLvU3Icao36/ohmsBcFXgj9M6juWt3FaZK5crOHaH2wa7G34Lr3gGIMz4gbw==", "dev": true, "dependencies": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/logger": "3.4.0", "@graphql-yoga/node": "2.0.0", "open": "8.2.1", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/serve-command/node_modules/@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "yargs": "17.2.1"}}, "node_modules/@graphql-inspector/serve-command/node_modules/@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-tools/utils": "^6.0.0 || ^7.0.0 || ^8.0.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/serve-command/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-inspector/serve-command/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-inspector/similar-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/similar-command/-/similar-command-3.4.0.tgz", "integrity": "sha512-I4UM2AdIHilUnizaw5g45M2ZwA89+zGYN7QdtXQGhMQbniuBQ5ZNoJC/YavMFamF3Y6ROElYutSuqbMm3EgfJg==", "dev": true, "dependencies": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/similar-command/node_modules/@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "yargs": "17.2.1"}}, "node_modules/@graphql-inspector/similar-command/node_modules/@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-tools/utils": "^6.0.0 || ^7.0.0 || ^8.0.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/similar-command/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-inspector/similar-command/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-inspector/url-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/url-loader/-/url-loader-3.4.0.tgz", "integrity": "sha512-q81y0mwGNKno/RyBaLXhtxtza9LZPwjkfY8WRqxytQmPrNDEeg2I3MdhcNOwctA2Gh124Xw0D5foj3KTYebJqQ==", "dev": true, "dependencies": {"@graphql-tools/url-loader": "^7.4.2", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/validate-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/validate-command/-/validate-command-3.4.0.tgz", "integrity": "sha512-DhlBHsv46xWX7XXzs/STLj4vQJnG8G5jsW0GuipmFgIcUlJdB46eNSoR8GWjUbsjgncPBS1KbMgdjlrcc9lecA==", "dev": true, "dependencies": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "peerDependencies": {"graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/validate-command/node_modules/@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0", "yargs": "17.2.1"}}, "node_modules/@graphql-inspector/validate-command/node_modules/@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}, "peerDependencies": {"@graphql-inspector/config": "3.4.0", "@graphql-tools/utils": "^6.0.0 || ^7.0.0 || ^8.0.0", "graphql": "^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-inspector/validate-command/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-inspector/validate-command/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-tools/batch-execute": {"version": "8.5.14", "resolved": "https://registry.npmjs.org/@graphql-tools/batch-execute/-/batch-execute-8.5.14.tgz", "integrity": "sha512-m6yXqqmFAH2V5JuSIC/geiGLBQA1Y6RddOJfUtkc9Z7ttkULRCd1W39TpYS6IlrCwYyTj+klO1/kdWiny38f5g==", "dev": true, "dependencies": {"@graphql-tools/utils": "9.1.3", "dataloader": "2.1.0", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/batch-execute/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/batch-execute/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/code-file-loader": {"version": "7.3.15", "resolved": "https://registry.npmjs.org/@graphql-tools/code-file-loader/-/code-file-loader-7.3.15.tgz", "integrity": "sha512-cF8VNc/NANTyVSIK8BkD/KSXRF64DvvomuJ0evia7tJu4uGTXgDjimTMWsTjKRGOOBSTEbL6TA8e4DdIYq6Udw==", "dev": true, "dependencies": {"@graphql-tools/graphql-tag-pluck": "7.4.2", "@graphql-tools/utils": "9.1.3", "globby": "^11.0.3", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/code-file-loader/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/code-file-loader/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/delegate": {"version": "9.0.20", "resolved": "https://registry.npmjs.org/@graphql-tools/delegate/-/delegate-9.0.20.tgz", "integrity": "sha512-m/de++kSxa/JABQ15tyt6SMlc4qfl3MgFFWmwbPcWgJxlAvZ7oIdGXcUvU6VJcXiyXfo6MZ/zr4V2E2UwMehew==", "dev": true, "dependencies": {"@graphql-tools/batch-execute": "8.5.14", "@graphql-tools/executor": "0.0.11", "@graphql-tools/schema": "9.0.12", "@graphql-tools/utils": "9.1.3", "dataloader": "2.1.0", "tslib": "~2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/delegate/node_modules/@graphql-tools/merge": {"version": "8.3.14", "resolved": "https://registry.npmjs.org/@graphql-tools/merge/-/merge-8.3.14.tgz", "integrity": "sha512-zV0MU1DnxJLIB0wpL4N3u21agEiYFsjm6DI130jqHpwF0pR9HkF+Ni65BNfts4zQelP0GjkHltG+opaozAJ1NA==", "dev": true, "dependencies": {"@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/delegate/node_modules/@graphql-tools/schema": {"version": "9.0.12", "resolved": "https://registry.npmjs.org/@graphql-tools/schema/-/schema-9.0.12.tgz", "integrity": "sha512-DmezcEltQai0V1y96nwm0Kg11FDS/INEFekD4nnVgzBqawvznWqK6D6bujn+cw6kivoIr3Uq//QmU/hBlBzUlQ==", "dev": true, "dependencies": {"@graphql-tools/merge": "8.3.14", "@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/delegate/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/delegate/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/executor": {"version": "0.0.11", "resolved": "https://registry.npmjs.org/@graphql-tools/executor/-/executor-0.0.11.tgz", "integrity": "sha512-GjtXW0ZMGZGKad6A1HXFPArkfxE0AIpznusZuQdy4laQx+8Ut3Zx8SAFJNnDfZJ2V5kU29B5Xv3Fr0/DiMBHOQ==", "dev": true, "dependencies": {"@graphql-tools/utils": "9.1.3", "@graphql-typed-document-node/core": "3.1.1", "@repeaterjs/repeater": "3.0.4", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-graphql-ws": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/@graphql-tools/executor-graphql-ws/-/executor-graphql-ws-0.0.5.tgz", "integrity": "sha512-1bJfZdSBPCJWz1pJ5g/YHMtGt6YkNRDdmqNQZ8v+VlQTNVfuBpY2vzj15uvf5uDrZLg2MSQThrKlL8av4yFpsA==", "dev": true, "dependencies": {"@graphql-tools/utils": "9.1.3", "@repeaterjs/repeater": "3.0.4", "@types/ws": "^8.0.0", "graphql-ws": "5.11.2", "isomorphic-ws": "5.0.0", "tslib": "^2.4.0", "ws": "8.11.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-graphql-ws/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-graphql-ws/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/executor-http": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/@graphql-tools/executor-http/-/executor-http-0.0.7.tgz", "integrity": "sha512-g0NV4HVZVABsylk6SIA/gfjQbMIsy3NjZYW0k0JZmTcp9698J37uG50GZC2mKe0F8pIlDvPLvrPloqdKGX3ZAA==", "dev": true, "dependencies": {"@graphql-tools/utils": "9.1.3", "@repeaterjs/repeater": "3.0.4", "@whatwg-node/fetch": "0.5.3", "dset": "3.1.2", "extract-files": "^11.0.0", "meros": "1.2.1", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-http/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-http/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/executor-legacy-ws": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/@graphql-tools/executor-legacy-ws/-/executor-legacy-ws-0.0.5.tgz", "integrity": "sha512-j2ZQVTI4rKIT41STzLPK206naYDhHxmGHot0siJKBKX1vMqvxtWBqvL66v7xYEOaX79wJrFc8l6oeURQP2LE6g==", "dev": true, "dependencies": {"@graphql-tools/utils": "9.1.3", "@types/ws": "^8.0.0", "isomorphic-ws": "5.0.0", "tslib": "^2.4.0", "ws": "8.11.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-legacy-ws/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor-legacy-ws/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/executor/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/executor/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/git-loader": {"version": "7.2.15", "resolved": "https://registry.npmjs.org/@graphql-tools/git-loader/-/git-loader-7.2.15.tgz", "integrity": "sha512-1d5HmeuxhSNjQ2+k2rfKgcKcnZEC6H5FM2pY5lSXHMv8VdBELZd7pYDs5/JxoZarDVYfYOJ5xTeVzxf+Du3VNg==", "dev": true, "dependencies": {"@graphql-tools/graphql-tag-pluck": "7.4.2", "@graphql-tools/utils": "9.1.3", "is-glob": "4.0.3", "micromatch": "^4.0.4", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/git-loader/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/git-loader/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/github-loader": {"version": "7.3.22", "resolved": "https://registry.npmjs.org/@graphql-tools/github-loader/-/github-loader-7.3.22.tgz", "integrity": "sha512-JE5F/ObbwknO7+gDfeuKAZtLS831WV8/SsLzQLMGY0hdgTbsAg2/xziAGprNToK4GMSD7ygCer9ZryvxBKMwbQ==", "dev": true, "dependencies": {"@ardatan/sync-fetch": "0.0.1", "@graphql-tools/graphql-tag-pluck": "7.4.2", "@graphql-tools/utils": "9.1.3", "@whatwg-node/fetch": "^0.5.0", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/github-loader/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/github-loader/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/graphql-file-loader": {"version": "7.5.16", "resolved": "https://registry.npmjs.org/@graphql-tools/graphql-file-loader/-/graphql-file-loader-7.5.16.tgz", "integrity": "sha512-lK1N3Y2I634FS12nd4bu7oAJbai3bUc28yeX+boT+C83KTO4ujGHm+6hPC8X/FRGwhKOnZBxUM7I5nvb3HiUxw==", "dev": true, "dependencies": {"@graphql-tools/import": "6.7.17", "@graphql-tools/utils": "9.2.1", "globby": "^11.0.3", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/graphql-file-loader/node_modules/@graphql-tools/utils": {"version": "9.2.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.2.1.tgz", "integrity": "sha512-WUw506Ql6xzmOORlriNrD6Ugx+HjVgYxt9KCXD9mHAak+eaXSwuGGPyE60hy9xaDEoXKBsG7SkG69ybitaVl6A==", "dev": true, "dependencies": {"@graphql-typed-document-node/core": "^3.1.1", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/graphql-file-loader/node_modules/tslib": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==", "dev": true}, "node_modules/@graphql-tools/graphql-tag-pluck": {"version": "7.4.2", "resolved": "https://registry.npmjs.org/@graphql-tools/graphql-tag-pluck/-/graphql-tag-pluck-7.4.2.tgz", "integrity": "sha512-SXM1wR5TExrxocQTxZK5r74jTbg8GxSYLY3mOPCREGz6Fu7PNxMxfguUzGUAB43Mf44Dn8oVztzd2eitv2Qgww==", "dev": true, "dependencies": {"@babel/parser": "^7.16.8", "@babel/plugin-syntax-import-assertions": "7.20.0", "@babel/traverse": "^7.16.8", "@babel/types": "^7.16.8", "@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/graphql-tag-pluck/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/graphql-tag-pluck/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/import": {"version": "6.7.17", "resolved": "https://registry.npmjs.org/@graphql-tools/import/-/import-6.7.17.tgz", "integrity": "sha512-bn9SgrECXq3WIasgNP7ful/uON51wBajPXtxdY+z/ce7jLWaFE6lzwTDB/GAgiZ+jo7nb0ravlxteSAz2qZmuA==", "dev": true, "dependencies": {"@graphql-tools/utils": "9.2.1", "resolve-from": "5.0.0", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/import/node_modules/@graphql-tools/utils": {"version": "9.2.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.2.1.tgz", "integrity": "sha512-WUw506Ql6xzmOORlriNrD6Ugx+HjVgYxt9KCXD9mHAak+eaXSwuGGPyE60hy9xaDEoXKBsG7SkG69ybitaVl6A==", "dev": true, "dependencies": {"@graphql-typed-document-node/core": "^3.1.1", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/import/node_modules/tslib": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==", "dev": true}, "node_modules/@graphql-tools/json-file-loader": {"version": "7.4.14", "resolved": "https://registry.npmjs.org/@graphql-tools/json-file-loader/-/json-file-loader-7.4.14.tgz", "integrity": "sha512-AD9v3rN08wvVqgbrUSiHa8Ztrlk3EgwctcxuNE5qm47zPNL4gLaJ7Tw/KlGOR7Cm+pjlQylJHMUKNfaRLPZ0og==", "dev": true, "dependencies": {"@graphql-tools/utils": "9.1.3", "globby": "^11.0.3", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/json-file-loader/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/json-file-loader/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/load": {"version": "7.7.7", "resolved": "https://registry.npmjs.org/@graphql-tools/load/-/load-7.7.7.tgz", "integrity": "sha512-IpI2672zcoAX4FLjcH5kvHc7eqjPyLP1svrIcZKQenv0GRS6dW0HI9E5UCBs0y/yy8yW6s+SvpmNsfIlkMj3Kw==", "dev": true, "dependencies": {"@graphql-tools/schema": "9.0.4", "@graphql-tools/utils": "8.12.0", "p-limit": "3.1.0", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/load-files": {"version": "6.5.3", "resolved": "https://registry.npmjs.org/@graphql-tools/load-files/-/load-files-6.5.3.tgz", "integrity": "sha512-hu6hw2fj3ltno7ezbyAQc5VdLCymgx0XIwvk0l9sWAlV2xnaNcX7p/z1qm6U/db2MiCumRBBIm/QuR7Jn71qZg==", "dev": true, "dependencies": {"globby": "11.0.4", "tslib": "~2.3.0", "unixify": "1.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-tools/load/node_modules/@graphql-tools/merge": {"version": "8.3.6", "resolved": "https://registry.npmjs.org/@graphql-tools/merge/-/merge-8.3.6.tgz", "integrity": "sha512-uUBokxXi89bj08P+iCvQk3Vew4vcfL5ZM6NTylWi8PIpoq4r5nJ625bRuN8h2uubEdRiH8ntN9M4xkd/j7AybQ==", "dev": true, "dependencies": {"@graphql-tools/utils": "8.12.0", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/load/node_modules/@graphql-tools/schema": {"version": "9.0.4", "resolved": "https://registry.npmjs.org/@graphql-tools/schema/-/schema-9.0.4.tgz", "integrity": "sha512-B/b8ukjs18fq+/s7p97P8L1VMrwapYc3N2KvdG/uNThSazRRn8GsBK0Nr+FH+mVKiUfb4Dno79e3SumZVoHuOQ==", "dev": true, "dependencies": {"@graphql-tools/merge": "8.3.6", "@graphql-tools/utils": "8.12.0", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/load/node_modules/@graphql-tools/utils": {"version": "8.12.0", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.12.0.tgz", "integrity": "sha512-Te<PERSON>+MJWGXjUTS52qfK4R8HiPoF/R7X+qmgtOYd8DTH0l6b+5Y/tlg5aGeUJefqImRq7nvi93Ms40k/Uz4D5CWw==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/load/node_modules/tslib": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz", "integrity": "sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==", "dev": true}, "node_modules/@graphql-tools/merge": {"version": "8.2.10", "resolved": "https://registry.npmjs.org/@graphql-tools/merge/-/merge-8.2.10.tgz", "integrity": "sha512-wpg22seOTNfkIO8jFAgo8w1BsT3IS2OTMpkCNf+dvcKSP09SVidYCOliyWHgjDCmpCrvvSjOX855NUKDx/Biew==", "dependencies": {"@graphql-tools/utils": "8.6.9", "tslib": "~2.3.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-tools/merge/node_modules/@graphql-tools/utils": {"version": "8.6.9", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.6.9.tgz", "integrity": "sha512-Z1X4d4GCT81+8CSt6SgU4t1w1UAUsAIRb67mI90k/zAs+ArkB95iE3bWXuJCUmd1+r8DGGtmUNOArtd6wkt+OQ==", "license": "MIT", "dependencies": {"tslib": "~2.3.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-tools/mock": {"version": "8.6.8", "resolved": "https://registry.npmjs.org/@graphql-tools/mock/-/mock-8.6.8.tgz", "integrity": "sha512-zBZApp8dDAovWKZ0rkZ4CwDT8Z+B35pIyRjeHkxvtKt5XyEAabEwkuSYMyFdsghDWwhMD/VAZ/6DXtA62Hnf+A==", "dependencies": {"@graphql-tools/schema": "8.3.10", "@graphql-tools/utils": "8.6.9", "fast-json-stable-stringify": "^2.1.0", "tslib": "~2.3.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-tools/mock/node_modules/@graphql-tools/utils": {"version": "8.6.9", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.6.9.tgz", "integrity": "sha512-Z1X4d4GCT81+8CSt6SgU4t1w1UAUsAIRb67mI90k/zAs+ArkB95iE3bWXuJCUmd1+r8DGGtmUNOArtd6wkt+OQ==", "license": "MIT", "dependencies": {"tslib": "~2.3.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-tools/schema": {"version": "8.3.10", "resolved": "https://registry.npmjs.org/@graphql-tools/schema/-/schema-8.3.10.tgz", "integrity": "sha512-tfhjSTi3OzheDrVzG7rkPZg2BbQjmZRLM2vvQoM2b1TnUwgUIbpAgcnf+AWDLRsoCOWlezeLgij1BLeAR0Q0jg==", "dependencies": {"@graphql-tools/merge": "8.2.10", "@graphql-tools/utils": "8.6.9", "tslib": "~2.3.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-tools/schema/node_modules/@graphql-tools/utils": {"version": "8.6.9", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.6.9.tgz", "integrity": "sha512-Z1X4d4GCT81+8CSt6SgU4t1w1UAUsAIRb67mI90k/zAs+ArkB95iE3bWXuJCUmd1+r8DGGtmUNOArtd6wkt+OQ==", "license": "MIT", "dependencies": {"tslib": "~2.3.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-tools/url-loader": {"version": "7.16.26", "resolved": "https://registry.npmjs.org/@graphql-tools/url-loader/-/url-loader-7.16.26.tgz", "integrity": "sha512-mrbFhShlthSdvMD3GPeKYxUt54CBUteN0eR6WgJJSXibycTSA6h0LAn6iyCAg+uUsBP/KR6GcjvQHifl00Q7rQ==", "dev": true, "dependencies": {"@ardatan/sync-fetch": "0.0.1", "@graphql-tools/delegate": "9.0.20", "@graphql-tools/executor-graphql-ws": "0.0.5", "@graphql-tools/executor-http": "0.0.7", "@graphql-tools/executor-legacy-ws": "0.0.5", "@graphql-tools/utils": "9.1.3", "@graphql-tools/wrap": "9.2.21", "@types/ws": "^8.0.0", "@whatwg-node/fetch": "^0.5.0", "isomorphic-ws": "5.0.0", "tslib": "^2.4.0", "value-or-promise": "^1.0.11", "ws": "8.11.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/url-loader/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/url-loader/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-tools/utils": {"version": "10.8.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-10.8.3.tgz", "integrity": "sha512-4QCvx3SWRsbH7wnktl51mBek+zE9hsjsv796XVlJlOUdWpAghJmA3ID2P7/Vwuy7BivVNfuAKe4ucUdE1fG7vA==", "license": "MIT", "dependencies": {"@graphql-typed-document-node/core": "^3.1.1", "@whatwg-node/promise-helpers": "^1.0.0", "cross-inspect": "1.0.1", "dset": "^3.1.4", "tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/utils/node_modules/dset": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/dset/-/dset-3.1.4.tgz", "integrity": "sha512-2QF/g9/zTaPDc3BjNcVTGoBbXBgYfMTTceLaYcFJ/W9kggFUkhxD/hMEeuLKbugyef9SqAx8cpgwlIP/jinUTA==", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@graphql-tools/utils/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/@graphql-tools/wrap": {"version": "9.2.21", "resolved": "https://registry.npmjs.org/@graphql-tools/wrap/-/wrap-9.2.21.tgz", "integrity": "sha512-h4S9x9Rh8aSKkBGv+nq43Z0qK7CH8ySl+9RDO0MluscWlqNNECJ34FytwPVd8sXSrS7JwgwmVNcT51EH1n0wRw==", "dev": true, "dependencies": {"@graphql-tools/delegate": "9.0.20", "@graphql-tools/schema": "9.0.12", "@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/wrap/node_modules/@graphql-tools/merge": {"version": "8.3.14", "resolved": "https://registry.npmjs.org/@graphql-tools/merge/-/merge-8.3.14.tgz", "integrity": "sha512-zV0MU1DnxJLIB0wpL4N3u21agEiYFsjm6DI130jqHpwF0pR9HkF+Ni65BNfts4zQelP0GjkHltG+opaozAJ1NA==", "dev": true, "dependencies": {"@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/wrap/node_modules/@graphql-tools/schema": {"version": "9.0.12", "resolved": "https://registry.npmjs.org/@graphql-tools/schema/-/schema-9.0.12.tgz", "integrity": "sha512-DmezcEltQai0V1y96nwm0Kg11FDS/INEFekD4nnVgzBqawvznWqK6D6bujn+cw6kivoIr3Uq//QmU/hBlBzUlQ==", "dev": true, "dependencies": {"@graphql-tools/merge": "8.3.14", "@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/wrap/node_modules/@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-tools/wrap/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@graphql-typed-document-node/core": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/@graphql-typed-document-node/core/-/core-3.1.1.tgz", "integrity": "sha512-NQ17ii0rK1b34VZonlmT2QMJFI70m0TRwbknO/ihlbatXyaktDhN/98vBiUU6kNBPljqGqyIrl2T4nY2RpFANg==", "peerDependencies": {"graphql": "^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/@graphql-yoga/common": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@graphql-yoga/common/-/common-2.0.0.tgz", "integrity": "sha512-nSENt7yANcnAIHdjTWOeTz4feXnGkgiShUU9MzukFbETWL7pwaK6fjgghUCDVuL2axm155Z3lAhG4sLA4MgvlQ==", "dev": true, "dependencies": {"@envelop/core": "^2.0.0", "@envelop/disable-introspection": "^3.0.0", "@envelop/parser-cache": "^4.0.0", "@envelop/validation-cache": "^4.0.0", "@graphql-tools/schema": "^8.3.1", "@graphql-tools/utils": "^8.6.0", "@graphql-typed-document-node/core": "^3.1.1", "@graphql-yoga/render-graphiql": "2.0.0", "@graphql-yoga/subscription": "2.0.0", "chalk": "4.1.2", "cross-undici-fetch": "^0.1.25", "dset": "^3.1.1", "tslib": "^2.3.1"}, "peerDependencies": {"graphql": "^15.2.0 || ^16.0.0"}}, "node_modules/@graphql-yoga/common/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-yoga/common/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-yoga/node": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@graphql-yoga/node/-/node-2.0.0.tgz", "integrity": "sha512-fjKHT8UR1DcOgvUfrnVhM7Ggj5vnxVUwlUbkY7NZC8vZiXfVbA9UfLYUYl+hXFmOCDK+S19kszLp7rw0s0LdWg==", "dev": true, "dependencies": {"@envelop/core": "^2.0.0", "@graphql-tools/utils": "^8.6.0", "@graphql-yoga/common": "2.0.0", "@graphql-yoga/subscription": "2.0.0", "cross-undici-fetch": "^0.1.25", "tslib": "^2.3.1"}, "peerDependencies": {"graphql": "^15.2.0 || ^16.0.0"}}, "node_modules/@graphql-yoga/node/node_modules/@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.4.0"}, "peerDependencies": {"graphql": "^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0"}}, "node_modules/@graphql-yoga/node/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/@graphql-yoga/render-graphiql": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@graphql-yoga/render-graphiql/-/render-graphiql-2.0.0.tgz", "integrity": "sha512-GZuZPCVBpYI0MwaE6nBtrzMoSFyBtiWOUm7i9jbRxu5Sqxuj3Zc0zTaD7V0UQzuPZKzytmMWq2+nbWPIu5FWtg==", "dev": true}, "node_modules/@graphql-yoga/subscription": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@graphql-yoga/subscription/-/subscription-2.0.0.tgz", "integrity": "sha512-HlG+gIddjIP3/BDrMZymdzmzDjNdYuSGMxx6+1JA83gAEVRDR4yOoT4QeNKYqRhLK9xca/Hxp1PfBpquSa244Q==", "dev": true, "dependencies": {"@repeaterjs/repeater": "^3.0.4", "tslib": "^2.3.1"}}, "node_modules/@josephg/resolvable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@josephg/resolvable/-/resolvable-1.0.1.tgz", "integrity": "sha512-CtzORUwWTTOTqfVtHaKRJ0I1kNQd1bpn3sUh8I3nJDVY+5/M/Oe1DnEWzPQvqq/xPIIkzzzIP7mfCoAjFRvDhg=="}, "node_modules/@jridgewell/gen-mapping": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "integrity": "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==", "dev": true, "peer": true, "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.14", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "dev": true}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.17", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz", "integrity": "sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==", "dev": true, "dependencies": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@peculiar/asn1-schema": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/@peculiar/asn1-schema/-/asn1-schema-2.3.3.tgz", "integrity": "sha512-6GptMYDMyWBHTUKndHaDsRZUO/XMSgIns2krxcm2L7SEExRHwawFvSwNBhqNPR9HJwv3MruAiF1bhN0we6j6GQ==", "dev": true, "dependencies": {"asn1js": "^3.0.5", "pvtsutils": "^1.3.2", "tslib": "^2.4.0"}}, "node_modules/@peculiar/asn1-schema/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@peculiar/json-schema": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/@peculiar/json-schema/-/json-schema-1.1.12.tgz", "integrity": "sha512-coUfuoMeIB7B8/NMekxaDzLhaYmp0HZNPEjYRm9goRou8UZIC3z21s0sL9AWoCw4EG876QyO3kYrc61WNF9B/w==", "dev": true, "dependencies": {"tslib": "^2.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@peculiar/webcrypto": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/@peculiar/webcrypto/-/webcrypto-1.4.1.tgz", "integrity": "sha512-eK4C6WTNYxoI7JOabMoZICiyqRRtJB220bh0Mbj5RwRycleZf9BPyZoxsTvpP0FpmVS2aS13NKOuh5/tN3sIRw==", "dev": true, "dependencies": {"@peculiar/asn1-schema": "^2.3.0", "@peculiar/json-schema": "^1.1.12", "pvtsutils": "^1.3.2", "tslib": "^2.4.1", "webcrypto-core": "^1.7.4"}, "engines": {"node": ">=10.12.0"}}, "node_modules/@peculiar/webcrypto/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/@protobufjs/aspromise": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "integrity": "sha1-m4sMxmPWaafY9vXQiToU00jzD78="}, "node_modules/@protobufjs/base64": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz", "integrity": "sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg=="}, "node_modules/@protobufjs/codegen": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz", "integrity": "sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg=="}, "node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "integrity": "sha1-NVy8mLr61ZePntCV85diHx0Ga3A="}, "node_modules/@protobufjs/fetch": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz", "integrity": "sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=", "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "node_modules/@protobufjs/float": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz", "integrity": "sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E="}, "node_modules/@protobufjs/inquire": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz", "integrity": "sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik="}, "node_modules/@protobufjs/path": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz", "integrity": "sha1-bMKyDFya1q0NzP0hynZz2Nf79o0="}, "node_modules/@protobufjs/pool": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz", "integrity": "sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q="}, "node_modules/@protobufjs/utf8": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz", "integrity": "sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA="}, "node_modules/@repeaterjs/repeater": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@repeaterjs/repeater/-/repeater-3.0.4.tgz", "integrity": "sha512-AW8PKd6iX3vAZ0vA43nOUOnbq/X5ihgU+mSXXqunMkeQADGiqw/PY0JNeYtD5sr0PAy51YPgAPbDoeapv9r8WA==", "dev": true}, "node_modules/@types/accepts": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/@types/accepts/-/accepts-1.3.5.tgz", "integrity": "sha512-jOdnI/3qTpHABjM5cx1Hc0sKsPoYCp+DP/GJRGtDlPd7fiV9oXGGIcjW/ZOxLIvjGz8MA+uMZI9metHlgqbgwQ==", "dependencies": {"@types/node": "*"}}, "node_modules/@types/body-parser": {"version": "1.19.2", "resolved": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.2.tgz", "integrity": "sha512-<PERSON><PERSON>one6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.35", "resolved": "https://registry.npmjs.org/@types/connect/-/connect-3.4.35.tgz", "integrity": "sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==", "dependencies": {"@types/node": "*"}}, "node_modules/@types/cors": {"version": "2.8.12", "resolved": "https://registry.npmjs.org/@types/cors/-/cors-2.8.12.tgz", "integrity": "sha512-vt+kDhq/M2ayberEtJcIN/hxXy1Pk+59g2FV/ZQceeaTyCtCucjL2Q7FXlFjtWn4n15KCr1NE2lNNFhp0lEThw=="}, "node_modules/@types/express": {"version": "4.17.13", "resolved": "https://registry.npmjs.org/@types/express/-/express-4.17.13.tgz", "integrity": "sha512-6bSZTPaTIACxn48l50SR+axgrqm6qXFIxrdAKaG6PaJk3+zuUr35hBlgT7vOmJcum+OEaIBLtHV/qloEAFITeA==", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "4.17.28", "resolved": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.28.tgz", "integrity": "sha512-P1BJAEAW3E2DJUlkgq4tOL3RyMunoWXqbSCygWo5ZIWTjUgN1YnaXWW4VWl/oc8vs/XoYibEGBKP0uZyF4AHig==", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "node_modules/@types/long": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/@types/long/-/long-4.0.2.tgz", "integrity": "sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA=="}, "node_modules/@types/mime": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/@types/mime/-/mime-1.3.2.tgz", "integrity": "sha512-YATxVxgRqNH6nHEIsvg6k2Boc1JHI9ZbH5iWFFv/MTkchz3b1ieGDa5T0a9RznNdI0KhVbdbWSN+KWWrQZRxTw=="}, "node_modules/@types/node": {"version": "14.18.33", "resolved": "https://registry.npmjs.org/@types/node/-/node-14.18.33.tgz", "integrity": "sha512-qelS/Ra6sacc4loe/3MSjXNL1dNQ/GjxNHVzuChwMfmk7HuycRLVQN2qNY3XahK+fZc5E2szqQSKUyAF0E+2bg=="}, "node_modules/@types/qs": {"version": "6.9.7", "resolved": "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz", "integrity": "sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw=="}, "node_modules/@types/range-parser": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.4.tgz", "integrity": "sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw=="}, "node_modules/@types/serve-static": {"version": "1.13.10", "resolved": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.10.tgz", "integrity": "sha512-nCkHGI4w7ZgAdNkrEu0bv+4xNV/XDqW+DydknebMOQwkpDGx8G+HTlj7R7ABI8i8nKxVw0wtKPi1D+lPOkh4YQ==", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/ws": {"version": "8.5.3", "resolved": "https://registry.npmjs.org/@types/ws/-/ws-8.5.3.tgz", "integrity": "sha512-6YOoWjruKj1uLf3INHH7D3qTXwFfEsg1kf3c0uDdSBJwfa/llkwIjrAGV7j7mVgGNbzTQ3HiHKKDXl6bJPD97w==", "dev": true, "dependencies": {"@types/node": "*"}}, "node_modules/@whatwg-node/fetch": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/@whatwg-node/fetch/-/fetch-0.5.3.tgz", "integrity": "sha512-cuAKL3Z7lrJJuUrfF1wxkQTb24Qd1QO/lsjJpM5ZSZZzUMms5TPnbGeGUKWA3hVKNHh30lVfr2MyRCT5Jfkucw==", "dev": true, "dependencies": {"@peculiar/webcrypto": "^1.4.0", "abort-controller": "^3.0.0", "busboy": "^1.6.0", "form-data-encoder": "^1.7.1", "formdata-node": "^4.3.1", "node-fetch": "^2.6.7", "undici": "^5.12.0", "web-streams-polyfill": "^3.2.0"}}, "node_modules/@whatwg-node/promise-helpers": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@whatwg-node/promise-helpers/-/promise-helpers-1.2.1.tgz", "integrity": "sha512-+faGtJlS4U8NSaSzRVN37xAprPdhoobYzUSUo4DgH8APtfFyizmNxp0ckwKcURoL8cy2B+bKxOWU/VIH2nFeLg==", "license": "MIT", "dependencies": {"tslib": "^2.6.3"}, "engines": {"node": ">=18.0.0"}}, "node_modules/@whatwg-node/promise-helpers/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/abort-controller": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz", "integrity": "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==", "dev": true, "dependencies": {"event-target-shim": "^5.0.0"}, "engines": {"node": ">=6.5"}}, "node_modules/accepts": {"version": "1.3.8", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "integrity": "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/apollo-datasource": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/apollo-datasource/-/apollo-datasource-3.3.1.tgz", "integrity": "sha512-Z3a8rEUXVPIZ1p8xrFL8bcNhWmhOmovgDArvwIwmJOBnh093ZpRfO+ESJEDAN4KswmyzCLDAwjsW4zQOONdRUw==", "dependencies": {"apollo-server-caching": "^3.3.0", "apollo-server-env": "^4.2.1"}, "engines": {"node": ">=12.0"}}, "node_modules/apollo-reporting-protobuf": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/apollo-reporting-protobuf/-/apollo-reporting-protobuf-3.3.1.tgz", "integrity": "sha512-tyvj3Vj71TCh6c8PtdHOLgHHBSJ05DF/A/Po3q8yfHTBkOPcOJZE/GGN/PT/pwKg7HHxKcAeHDw7+xciVvGx0w==", "dependencies": {"@apollo/protobufjs": "1.2.2"}}, "node_modules/apollo-server": {"version": "3.6.7", "resolved": "https://registry.npmjs.org/apollo-server/-/apollo-server-3.6.7.tgz", "integrity": "sha512-WERZqaVCkZJMoOc9y692NribgNtKbHDjOwiAmgXI2WBlON2oUvCwgxPvsMg+bXVpQx4itrMyj31a2N6BeKmbmQ==", "dependencies": {"apollo-server-core": "^3.6.7", "apollo-server-express": "^3.6.7", "express": "^4.17.1"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-caching": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/apollo-server-caching/-/apollo-server-caching-3.3.0.tgz", "integrity": "sha512-Wgcb0ArjZ5DjQ7ID+tvxUcZ7Yxdbk5l1MxZL8D8gkyjooOkhPNzjRVQ7ubPoXqO54PrOMOTm1ejVhsF+AfIirQ==", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=12.0"}}, "node_modules/apollo-server-core": {"version": "3.6.7", "resolved": "https://registry.npmjs.org/apollo-server-core/-/apollo-server-core-3.6.7.tgz", "integrity": "sha512-OnZ9vu7LrYy2rvEu+nbgqucw6VyTSIPAEjK87c4rkzlVOxpwtGUaQ4FMWD9zBIj7yLq9q22b638E8LdYoaTAjQ==", "dependencies": {"@apollographql/apollo-tools": "^0.5.3", "@apollographql/graphql-playground-html": "1.6.29", "@graphql-tools/mock": "^8.1.2", "@graphql-tools/schema": "^8.0.0", "@josephg/resolvable": "^1.0.0", "apollo-datasource": "^3.3.1", "apollo-reporting-protobuf": "^3.3.1", "apollo-server-caching": "^3.3.0", "apollo-server-env": "^4.2.1", "apollo-server-errors": "^3.3.1", "apollo-server-plugin-base": "^3.5.2", "apollo-server-types": "^3.5.2", "async-retry": "^1.2.1", "fast-json-stable-stringify": "^2.1.0", "graphql-tag": "^2.11.0", "lodash.sortby": "^4.7.0", "loglevel": "^1.6.8", "lru-cache": "^6.0.0", "sha.js": "^2.4.11", "uuid": "^8.0.0"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-env": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/apollo-server-env/-/apollo-server-env-4.2.1.tgz", "integrity": "sha512-vm/7c7ld+zFMxibzqZ7SSa5tBENc4B0uye9LTfjJwGoQFY5xsUPH5FpO5j0bMUDZ8YYNbrF9SNtzc5Cngcr90g==", "dependencies": {"node-fetch": "^2.6.7"}, "engines": {"node": ">=12.0"}}, "node_modules/apollo-server-errors": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/apollo-server-errors/-/apollo-server-errors-3.3.1.tgz", "integrity": "sha512-xnZJ5QWs6FixHICXHxUfm+ZWqqxrNuPlQ+kj5m6RtEgIpekOPssH/SD9gf2B4HuWV0QozorrygwZnux8POvyPA==", "engines": {"node": ">=12.0"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-express": {"version": "3.6.7", "resolved": "https://registry.npmjs.org/apollo-server-express/-/apollo-server-express-3.6.7.tgz", "integrity": "sha512-B4gH5j8t3XxTCIa9bl7Iq/F3YFzMxX/LV4Sc+L/3xkHm648u576G5Lkskl8HsoTGSzzyeVcVsPDoYHiBjCAN0Q==", "dependencies": {"@types/accepts": "^1.3.5", "@types/body-parser": "1.19.2", "@types/cors": "2.8.12", "@types/express": "4.17.13", "@types/express-serve-static-core": "4.17.28", "accepts": "^1.3.5", "apollo-server-core": "^3.6.7", "apollo-server-types": "^3.5.2", "body-parser": "^1.19.0", "cors": "^2.8.5", "parseurl": "^1.3.3"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"express": "^4.17.1", "graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-plugin-base": {"version": "3.5.2", "resolved": "https://registry.npmjs.org/apollo-server-plugin-base/-/apollo-server-plugin-base-3.5.2.tgz", "integrity": "sha512-SwIf1waDmNDb0kmn57QR++InwK6Iv/X2slpm/aFIoqFBe91r6uJfakJvQZuh8dLEgk68gxqFsT8zHRpxBclE+g==", "dependencies": {"apollo-server-types": "^3.5.2"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/apollo-server-types": {"version": "3.5.2", "resolved": "https://registry.npmjs.org/apollo-server-types/-/apollo-server-types-3.5.2.tgz", "integrity": "sha512-vhcbIWsBkoNibABOym4AAPBoNDjokhjUQokKYdwZMeqrb850PMQdNJFrGyXT5onP408Ghv4O8PfgBuPQmeJhVQ==", "dependencies": {"apollo-reporting-protobuf": "^3.3.1", "apollo-server-caching": "^3.3.0", "apollo-server-env": "^4.2.1"}, "engines": {"node": ">=12.0"}, "peerDependencies": {"graphql": "^15.3.0 || ^16.0.0"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "dev": true}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}, "node_modules/array-union": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz", "integrity": "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/asn1js": {"version": "3.0.5", "resolved": "https://registry.npmjs.org/asn1js/-/asn1js-3.0.5.tgz", "integrity": "sha512-FVnvrKJwpt9LP2lAMl8qZswRNm3T4q9CON+bxldk2iwk3FFpuwhx2FfinyitizWHsVYyaY+y5JzDR0rCMV5yTQ==", "dev": true, "dependencies": {"pvtsutils": "^1.3.2", "pvutils": "^1.1.3", "tslib": "^2.4.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/asn1js/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/async-retry": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/async-retry/-/async-retry-1.3.3.tgz", "integrity": "sha512-wfr/jstw9xNi/0teMHrRW7dsz3Lt5ARhYNZ2ewpadnhaIp5mbALhOAP+EAdsC7t4Z6wqsDVv9+W6gm1Dk9mEyw==", "dependencies": {"retry": "0.13.1"}}, "node_modules/axios": {"version": "0.26.1", "resolved": "https://registry.npmjs.org/axios/-/axios-0.26.1.tgz", "integrity": "sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==", "dev": true, "dependencies": {"follow-redirects": "^1.14.8"}}, "node_modules/axios-proxy-builder": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/axios-proxy-builder/-/axios-proxy-builder-0.1.2.tgz", "integrity": "sha512-6uBVsBZzkB3tCC8iyx59mCjQckhB8+GQrI9Cop8eC7ybIsvs/KtnNgEBfRMSEa7GqK2VBGUzgjNYMdPIfotyPA==", "dev": true, "dependencies": {"tunnel": "^0.0.6"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "dev": true}, "node_modules/binary-install": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/binary-install/-/binary-install-1.0.6.tgz", "integrity": "sha512-h3K4jaC4jEauK3csXI9GxGBJldkpuJlHCIBv8i+XBNhPuxnlERnD1PWVczQYDqvhJfv0IHUbB3lhDrZUMHvSgw==", "dev": true, "dependencies": {"axios": "^0.26.1", "rimraf": "^3.0.2", "tar": "^6.1.11"}, "engines": {"node": ">=10"}}, "node_modules/body-parser": {"version": "1.20.0", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.0.tgz", "integrity": "sha512-DfJ+q6EPcGKZD1QWUjSpqp+Q7bDQTsQIF4zfUAtZ6qk+H/3/QRhg9CEp39ss+/T2vw0+HaidC0ecJj/DRLIaKg==", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.10.3", "raw-body": "2.5.1", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "dev": true, "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.21.4", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.4.tgz", "integrity": "sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}], "peer": true, "dependencies": {"caniuse-lite": "^1.0.30001400", "electron-to-chromium": "^1.4.251", "node-releases": "^2.0.6", "update-browserslist-db": "^1.0.9"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/busboy": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz", "integrity": "sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==", "dev": true, "dependencies": {"streamsearch": "^1.1.0"}, "engines": {"node": ">=10.16.0"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/caniuse-lite": {"version": "1.0.30001439", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001439.tgz", "integrity": "sha512-1MgUzEkoMO6gKfXflStpYgZDlFM7M/ck/bgfVCACO5vnAf0fXoNVHdWtqGU+MYca+4bL9Z5bpOVmR33cWW9G2A==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}], "peer": true}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chownr": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz", "integrity": "sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==", "dev": true, "engines": {"node": ">=10"}}, "node_modules/ci-info": {"version": "3.7.0", "resolved": "https://registry.npmjs.org/ci-info/-/ci-info-3.7.0.tgz", "integrity": "sha512-2CpRNYmImPx+RXKLq6jko/L07phmS9I02TyqkcNU20GCF/GgaWvc58hPtjxDX8lPpkdwc9sNh72V9k00S7ezog==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/cli-table3": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/cli-table3/-/cli-table3-0.6.2.tgz", "integrity": "sha512-QyavHCaIC80cMivimWu4aWHilIpiDpfm3hGmqAmXVL1UsnbLuBSMd21hTX6VY4ZSDSM73ESLeF8TOYId3rBTbw==", "dev": true, "dependencies": {"string-width": "^4.2.0"}, "engines": {"node": "10.* || >= 12.*"}, "optionalDependencies": {"@colors/colors": "1.5.0"}}, "node_modules/cliui": {"version": "7.0.4", "resolved": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "integrity": "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==", "dev": true, "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/clone": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "integrity": "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==", "dev": true, "optional": true, "engines": {"node": ">=0.8"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true}, "node_modules/commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true}, "node_modules/console.table": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/console.table/-/console.table-0.10.0.tgz", "integrity": "sha512-dPyZofqggxuvSf7WXvNjuRfnsOk1YazkVP8FdxH4tcH2c37wc79/Yl6Bhr7Lsu00KMgy2ql/qCMuNu8xctZM8g==", "dev": true, "dependencies": {"easy-table": "1.1.0"}, "engines": {"node": "> 0.10"}}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz", "integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA==", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==", "dev": true, "peer": true}, "node_modules/cookie": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz", "integrity": "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "node_modules/cors": {"version": "2.8.5", "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/cross-inspect": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cross-inspect/-/cross-inspect-1.0.1.tgz", "integrity": "sha512-Pcw1JTvZLSJH83iiGWt6fRcT+BjZlCDRVwYLbUcHzv/CRpB7r0MlSrGbIyQvVSNyGnbt7G4AXuyCiDR3POvZ1A==", "license": "MIT", "dependencies": {"tslib": "^2.4.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/cross-inspect/node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "license": "0BSD"}, "node_modules/cross-undici-fetch": {"version": "0.1.33", "resolved": "https://registry.npmjs.org/cross-undici-fetch/-/cross-undici-fetch-0.1.33.tgz", "integrity": "sha512-2IljikrWuhEroSlidX0i/TXjL01IIQUigWC/9tq7U3C/GoQQBqc02xNIVTqoZG6nPn/P9C9YMH78A4aTLUticQ==", "dev": true, "dependencies": {"abort-controller": "^3.0.0", "form-data-encoder": "^1.7.1", "formdata-node": "^4.3.1", "node-fetch": "^2.6.7", "undici": "^5.0.0", "web-streams-polyfill": "^3.2.0"}}, "node_modules/cssfilter": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/cssfilter/-/cssfilter-0.0.10.tgz", "integrity": "sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4="}, "node_modules/dataloader": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/dataloader/-/dataloader-2.1.0.tgz", "integrity": "sha512-qTcEYLen3r7ojZNgVUaRggOI+KM7jrKxXeSHhogh/TWxYMeONEMqY+hmkobiYQozsGIyg9OYVzO4ZIfoB4I0pQ==", "dev": true}, "node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dependencies": {"ms": "2.0.0"}}, "node_modules/defaults": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz", "integrity": "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==", "dev": true, "optional": true, "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "integrity": "sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "engines": {"node": ">= 0.8"}}, "node_modules/dependency-graph": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/dependency-graph/-/dependency-graph-0.11.0.tgz", "integrity": "sha512-JeMq7fEshyepOWDfcfHK06N3MhyPhz++vtqWhMT5O9A3K42rdsEDpfdVqjaqaAhsw6a+ZqeDvQVtD0hFHQWrzg==", "dev": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-libc": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.1.tgz", "integrity": "sha512-463v3ZeIrcWtdgIg6vI6XUncguvr2TnGl4SzDXinkt9mSLpBJKXT3mW6xT3VQdDN11+WVs29pgvivTc4Lp8v+w==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/dir-glob": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==", "dev": true, "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dset": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/dset/-/dset-3.1.2.tgz", "integrity": "sha512-g/M9sqy3oHe477Ar4voQxWtaPIFw1jTdKZuomOjhCcBx9nHUNn0pu6NopuFFrTh/TRZIKEj+76vLWFu9BNKk+Q==", "dev": true, "engines": {"node": ">=4"}}, "node_modules/easy-table": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/easy-table/-/easy-table-1.1.0.tgz", "integrity": "sha512-oq33hWOSSnl2Hoh00tZWaIPi1ievrD9aFG82/IgjlycAnW9hHx5PkJiXpxPsgEE+H7BsbVQXFVFST8TEXS6/pA==", "dev": true, "optionalDependencies": {"wcwidth": ">=1.0.1"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "node_modules/electron-to-chromium": {"version": "1.4.284", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.284.tgz", "integrity": "sha512-M8WEXFuKXMYMVr45fo8mq0wUrrJHheiKZf6BArTKk9ZBYCKJEOU5H8cdWgDT+qCVZf7Na4lVUaZsA+h6uA9+PA==", "dev": true, "peer": true}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "dev": true}, "node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "engines": {"node": ">= 0.8"}}, "node_modules/escalade": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "integrity": "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "engines": {"node": ">= 0.6"}}, "node_modules/event-target-shim": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/express": {"version": "4.18.0", "resolved": "https://registry.npmjs.org/express/-/express-4.18.0.tgz", "integrity": "sha512-EJEXxiTQJS3lIPrU1AE2vRuT7X7E+0KBbpm5GSoK524yl0K8X+er8zS2P14E64eqsVNoWbMCT7MpmQ+ErAhgRg==", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.0", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.5.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.10.3", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/extract-files": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/extract-files/-/extract-files-11.0.0.tgz", "integrity": "sha512-FuoE1qtbJ4bBVvv94CC7s0oTnKUGvQs+Rjf1L2SJFfS+HTVVjhPFtehPdQ0JiGPqVNfSSZvL5yzHHQq2Z4WNhQ==", "dev": true, "engines": {"node": "^12.20 || >= 14.13"}, "funding": {"url": "https://github.com/sponsors/jaydenseric"}}, "node_modules/fast-glob": {"version": "3.2.11", "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.11.tgz", "integrity": "sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew==", "dev": true, "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="}, "node_modules/fastq": {"version": "1.13.0", "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz", "integrity": "sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==", "dev": true, "dependencies": {"reusify": "^1.0.4"}}, "node_modules/figures": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz", "integrity": "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==", "dev": true, "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/fill-range": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==", "dev": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz", "integrity": "sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/follow-redirects": {"version": "1.15.2", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz", "integrity": "sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/form-data-encoder": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/form-data-encoder/-/form-data-encoder-1.7.2.tgz", "integrity": "sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==", "dev": true}, "node_modules/formdata-node": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/formdata-node/-/formdata-node-4.4.1.tgz", "integrity": "sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==", "dev": true, "dependencies": {"node-domexception": "1.0.0", "web-streams-polyfill": "4.0.0-beta.3"}, "engines": {"node": ">= 12.20"}}, "node_modules/formdata-node/node_modules/web-streams-polyfill": {"version": "4.0.0-beta.3", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.3.tgz", "integrity": "sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==", "dev": true, "engines": {"node": ">= 14"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "engines": {"node": ">= 0.6"}}, "node_modules/fs-minipass": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz", "integrity": "sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==", "dev": true, "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "resolved": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "dev": true, "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true}, "node_modules/function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true, "peer": true, "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "dev": true, "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.1.tgz", "integrity": "sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q==", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true, "engines": {"node": ">=4"}}, "node_modules/globby": {"version": "11.0.4", "resolved": "https://registry.npmjs.org/globby/-/globby-11.0.4.tgz", "integrity": "sha512-9O4MVG9ioZJ08ffbcyVYyLOJLk5JQ688pJ4eMGLpdWLHq/Wr1D9BlriLQyL0E+jbkuePVZXYFj47QM/v093wHg==", "dev": true, "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.1.1", "ignore": "^5.1.4", "merge2": "^1.3.0", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/graphql": {"version": "16.4.0", "resolved": "https://registry.npmjs.org/graphql/-/graphql-16.4.0.tgz", "integrity": "sha512-tYDNcRvKCcfHREZYje3v33NSrSD/ZpbWWdPtBtUUuXx9NCo/2QDxYzNqCnMvfsrnbwRpEHMovVrPu/ERoLrIRg==", "engines": {"node": "^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0"}}, "node_modules/graphql-tag": {"version": "2.12.6", "resolved": "https://registry.npmjs.org/graphql-tag/-/graphql-tag-2.12.6.tgz", "integrity": "sha512-FdSNcu2QQcWnM2VNvSCCDCVS5PpPqpzgFT8+GXzqJuoDd0CBncxCY278u4mhRO7tMgo2JjgJA5aZ+nWSQ/Z+xg==", "dependencies": {"tslib": "^2.1.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"graphql": "^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0"}}, "node_modules/graphql-ws": {"version": "5.11.2", "resolved": "https://registry.npmjs.org/graphql-ws/-/graphql-ws-5.11.2.tgz", "integrity": "sha512-4EiZ3/UXYcjm+xFGP544/yW1+DVI8ZpKASFbzrV5EDTFWJp0ZvLl4Dy2fSZAzz9imKp5pZMIcjB0x/H69Pv/6w==", "dev": true, "engines": {"node": ">=10"}, "peerDependencies": {"graphql": ">=0.11 <=16"}}, "node_modules/has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/has-symbols": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/ignore/-/ignore-5.2.0.tgz", "integrity": "sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==", "dev": true, "engines": {"node": ">= 4"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "dev": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==", "engines": {"node": ">= 0.10"}}, "node_modules/is-docker": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "integrity": "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==", "dev": true, "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true, "engines": {"node": ">=0.12.0"}}, "node_modules/is-unicode-supported": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "integrity": "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-wsl": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "integrity": "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==", "dev": true, "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isomorphic-ws": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/isomorphic-ws/-/isomorphic-ws-5.0.0.tgz", "integrity": "sha512-muId7Zzn9ywDsyXgTIafTry2sV3nySZeUDe6YedVd1Hvuuep5AsIlqK+XefWpYTyJG5e503F2xIuT2lcU6rCSw==", "dev": true, "peerDependencies": {"ws": "*"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "dev": true, "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==", "dev": true, "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/json5": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz", "integrity": "sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==", "dev": true, "peer": true, "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/lodash.sortby": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz", "integrity": "sha1-7dFMgk4sycHgsKG0K7UhBRakJDg="}, "node_modules/log-symbols": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz", "integrity": "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==", "dev": true, "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/loglevel": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/loglevel/-/loglevel-1.8.0.tgz", "integrity": "sha512-G6A/nJLRgWOuuwdNuA6koovfEV1YpqqAG4pRUlFaz3jj2QNZ8M4vBqnVA+HBTmU/AMNUtlOsMmSpF6NyOjztbA==", "engines": {"node": ">= 0.6.0"}, "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/loglevel"}}, "node_modules/long": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/long/-/long-4.0.0.tgz", "integrity": "sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA=="}, "node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/meros": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/meros/-/meros-1.2.1.tgz", "integrity": "sha512-R2f/jxYqCAGI19KhAvaxSOxALBMkaXWH2a7rOyqQw+ZmizX5bKkEYWLzdhC+U82ZVVPVp6MCXe3EkVligh+12g==", "dev": true, "engines": {"node": ">=13"}, "peerDependencies": {"@types/node": ">=13"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.5", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz", "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "dev": true, "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minipass": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/minipass/-/minipass-4.0.0.tgz", "integrity": "sha512-g2Uuh2jEKoht+zvO6vJqXmYpflPqzRBT+Th2h01DKh5z7wbY/AZ2gCQ78cP70YoHPyFdY30YBV5WxgLOEwOykw==", "dev": true, "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minizlib": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz", "integrity": "sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==", "dev": true, "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "resolved": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "dev": true, "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/mkdirp": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "dev": true, "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "node_modules/negotiator": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==", "engines": {"node": ">= 0.6"}}, "node_modules/node-domexception": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/jimmywarting"}, {"type": "github", "url": "https://paypal.me/jimmywarting"}], "engines": {"node": ">=10.5.0"}}, "node_modules/node-fetch": {"version": "2.6.7", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.7.tgz", "integrity": "sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==", "dependencies": {"whatwg-url": "^5.0.0"}, "engines": {"node": "4.x || >=6.0.0"}, "peerDependencies": {"encoding": "^0.1.0"}, "peerDependenciesMeta": {"encoding": {"optional": true}}}, "node_modules/node-releases": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.7.tgz", "integrity": "sha512-EJ3rzxL9pTWPjk5arA0s0dgXpnyiAbJDE6wHT62g7VsgrgQgmmZ+Ru++M1BFofncWja+Pnn3rEr3fieRySAdKQ==", "dev": true, "peer": true}, "node_modules/normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.0.tgz", "integrity": "sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g==", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "dependencies": {"wrappy": "1"}}, "node_modules/open": {"version": "8.2.1", "resolved": "https://registry.npmjs.org/open/-/open-8.2.1.tgz", "integrity": "sha512-rXILpcQlkF/QuFez2BJDf3GsqpjGKbkUUToAIGo9A0Q6ZkoSGogZJulrUdwRkrAsoQvoZsrjCYt8+zblOk7JQQ==", "dev": true, "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "dev": true, "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "engines": {"node": ">= 0.8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-to-regexp": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "node_modules/path-type": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/picocolors": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==", "dev": true, "peer": true}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/pvtsutils": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/pvtsutils/-/pvtsutils-1.3.2.tgz", "integrity": "sha512-+Ipe2iNUyrZz+8K/2IOo+kKikdtfhRKzNpQbruF2URmqPtoqAs8g3xS7TJvFF2GcPXjh7DkqMnpVveRFq4PgEQ==", "dev": true, "dependencies": {"tslib": "^2.4.0"}}, "node_modules/pvtsutils/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/pvutils": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/pvutils/-/pvutils-1.1.3.tgz", "integrity": "sha512-pMpnA0qRdFp32b1sJl1wOJNxZLQ2cbQx+k6tjNtZ8CpvVhNqEPRgivZ2WOUev2YMajecdH7ctUPDvEe87nariQ==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/qs": {"version": "6.10.3", "resolved": "https://registry.npmjs.org/qs/-/qs-6.10.3.tgz", "integrity": "sha512-wr7M2E0OFRfIfJZjKGieI8lBKb7fRCH4Fv5KNPEs7gJ8jadvotdsS08PzOKR7opXhZ/Xkjtt3WF9g38drmyRqQ==", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz", "integrity": "sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve-from": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/retry": {"version": "0.13.1", "resolved": "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz", "integrity": "sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz", "integrity": "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==", "dev": true, "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "dev": true, "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "node_modules/semver": {"version": "6.3.0", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true, "peer": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/send": {"version": "0.18.0", "resolved": "https://registry.npmjs.org/send/-/send-0.18.0.tgz", "integrity": "sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}, "node_modules/serve-static": {"version": "1.15.0", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz", "integrity": "sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.18.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="}, "node_modules/sha.js": {"version": "2.4.11", "resolved": "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz", "integrity": "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/side-channel": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/slash": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "engines": {"node": ">= 0.8"}}, "node_modules/std-env": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/std-env/-/std-env-2.3.0.tgz", "integrity": "sha512-4qT5B45+Kjef2Z6pE0BkskzsH0GO7GrND0wGlTM1ioUe3v0dGYx9ZJH0Aro/YyA8fqQ5EyIKDRjZojJYMFTflw==", "dev": true, "dependencies": {"ci-info": "^3.0.0"}}, "node_modules/streamsearch": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz", "integrity": "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==", "dev": true, "engines": {"node": ">=10.0.0"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/tar": {"version": "6.1.13", "resolved": "https://registry.npmjs.org/tar/-/tar-6.1.13.tgz", "integrity": "sha512-jdIBIN6LTIe2jqzay/2vtYLlBHa3JF42ot3h1dW8Q0PaAG4v8rm0cvpVePtau5C6OKXGGcgO9q2AMNSWxiLqKw==", "dev": true, "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^4.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/to-fast-properties": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==", "dev": true, "engines": {"node": ">=4"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "engines": {"node": ">=0.6"}}, "node_modules/tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="}, "node_modules/tslib": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.3.1.tgz", "integrity": "sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw=="}, "node_modules/tunnel": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/tunnel/-/tunnel-0.0.6.tgz", "integrity": "sha512-1h/Lnq9yajKY2PEbBadPXj3VxsDDu844OnaAo52UVmIzIvwwtBPIuNvkjuzBlTWpfJyUbG3ez0KSBibQkj4ojg==", "dev": true, "engines": {"node": ">=0.6.11 <=0.7.0 || >=0.7.3"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/undici": {"version": "5.14.0", "resolved": "https://registry.npmjs.org/undici/-/undici-5.14.0.tgz", "integrity": "sha512-yJlHYw6yXPPsuOH0x2Ib1Km61vu4hLiRRQoafs+WUgX1vO64vgnxiCEN9dpIrhZyHFsai3F0AEj4P9zy19enEQ==", "dev": true, "dependencies": {"busboy": "^1.6.0"}, "engines": {"node": ">=12.18"}}, "node_modules/unixify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unixify/-/unixify-1.0.0.tgz", "integrity": "sha1-OmQcjC/7zk2mg6XHDwOkYpQMIJA=", "dev": true, "dependencies": {"normalize-path": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "engines": {"node": ">= 0.8"}}, "node_modules/update-browserslist-db": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz", "integrity": "sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}], "peer": true, "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "bin": {"browserslist-lint": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/value-or-promise": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/value-or-promise/-/value-or-promise-1.0.11.tgz", "integrity": "sha512-41BrgH+dIbCFXClcSapVs5M6GkENd3gQOJpEfPDNa71LsUGMXDL0jMWpI/Rh7WhX+Aalfz2TTS3Zt5pUsbnhLg==", "engines": {"node": ">=12"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "engines": {"node": ">= 0.8"}}, "node_modules/wcwidth": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz", "integrity": "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==", "dev": true, "optional": true, "dependencies": {"defaults": "^1.0.3"}}, "node_modules/web-streams-polyfill": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.2.1.tgz", "integrity": "sha512-e0MO3wdXWKrLbL0DgGnUV7WHVuw9OUvL4hjgnPkIeEvESk74gAITi5G606JtZPp39cd8HA9VQzCIvA49LpPN5Q==", "dev": true, "engines": {"node": ">= 8"}}, "node_modules/webcrypto-core": {"version": "1.7.5", "resolved": "https://registry.npmjs.org/webcrypto-core/-/webcrypto-core-1.7.5.tgz", "integrity": "sha512-gaExY2/3EHQlRNNNVSrbG2Cg94Rutl7fAaKILS1w8ZDhGxdFOaw6EbCfHIxPy9vt/xwp5o0VQAx9aySPF6hU1A==", "dev": true, "dependencies": {"@peculiar/asn1-schema": "^2.1.6", "@peculiar/json-schema": "^1.1.12", "asn1js": "^3.0.1", "pvtsutils": "^1.3.2", "tslib": "^2.4.0"}}, "node_modules/webcrypto-core/node_modules/tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}, "node_modules/webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="}, "node_modules/whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "dependencies": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true}, "node_modules/ws": {"version": "8.11.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.11.0.tgz", "integrity": "sha512-HPG3wQd9sNQoT9xHyNCXoDUa+Xw/VevmY9FoHyQ+g+rrMn4j6FB4np7Z0OhdTgjx6MgQLK7jwSy1YecU1+4Asg==", "dev": true, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xss": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/xss/-/xss-1.0.11.tgz", "integrity": "sha512-EimjrjThZeK2MO7WKR9mN5ZC1CSqivSl55wvUK5EtU6acf0rzEE1pN+9ZDrFXJ82BRp3JL38pPE6S4o/rpp1zQ==", "dependencies": {"commander": "^2.20.3", "cssfilter": "0.0.10"}, "bin": {"xss": "bin/xss"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "dev": true, "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}, "node_modules/yargs": {"version": "17.2.1", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.2.1.tgz", "integrity": "sha512-XfR8du6ua4K6uLGm5S6fA+FIJom/MdJcFNVY8geLlp2v8GYbOXD4EB1tPNZsRn4vBzKGMgb5DRZMeWuFc2GO8Q==", "dev": true, "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "20.2.9", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz", "integrity": "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==", "dev": true, "engines": {"node": ">=10"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}, "dependencies": {"@ampproject/remapping": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.2.0.tgz", "integrity": "sha512-qRmjj8nj9qmLTQXXmaR1cck3UXSRMPrbsLJAasZpF+t3riI71BXed5ebIOYwQntykeZuhjsdweEc9BxH5Jc26w==", "dev": true, "peer": true, "requires": {"@jridgewell/gen-mapping": "^0.1.0", "@jridgewell/trace-mapping": "^0.3.9"}}, "@apollo/protobufjs": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/@apollo/protobufjs/-/protobufjs-1.2.2.tgz", "integrity": "sha512-vF+zxhPiLtkwxONs6YanSt1EpwpGilThpneExUN5K3tCymuxNnVq2yojTvnpRjv2QfsEIt/n7ozPIIzBLwGIDQ==", "requires": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.0", "@types/node": "^10.1.0", "long": "^4.0.0"}, "dependencies": {"@types/node": {"version": "10.17.60", "resolved": "https://registry.npmjs.org/@types/node/-/node-10.17.60.tgz", "integrity": "sha512-F0KIgDJfy2nA3zMLmWGKxcH2ZVEtCZXHHdOQs2gSaQ27+lNeEfGxzkIw90aXswATX7AZ33tahPbzy6KAfUreVw=="}}}, "@apollo/rover": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/@apollo/rover/-/rover-0.10.0.tgz", "integrity": "sha512-5zJCsPIZVO9gEVH1vgrys5vgys1GamQbbh8emAyR8fMAkdpAKmKoiYm23Cxz23tV9KxU8X9omIA+8ycnqRBd9g==", "dev": true, "requires": {"axios-proxy-builder": "^0.1.1", "binary-install": "^1.0.6", "console.table": "^0.10.0", "detect-libc": "^2.0.0"}}, "@apollographql/apollo-tools": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/@apollographql/apollo-tools/-/apollo-tools-0.5.3.tgz", "integrity": "sha512-VcsXHfTFoCodDAgJZxN04GdFK1kqOhZQnQY/9Fa147P+I8xfvOSz5d+lKAPB+hwSgBNyd7ncAKGIs4+utbL+yA==", "requires": {}}, "@apollographql/graphql-playground-html": {"version": "1.6.29", "resolved": "https://registry.npmjs.org/@apollographql/graphql-playground-html/-/graphql-playground-html-1.6.29.tgz", "integrity": "sha512-xCcXpoz52rI4ksJSdOCxeOCn2DLocxwHf9dVT/Q90Pte1LX+LY+91SFtJF3KXVHH8kEin+g1KKCQPKBjZJfWNA==", "requires": {"xss": "^1.0.8"}}, "@ardatan/sync-fetch": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/@ardatan/sync-fetch/-/sync-fetch-0.0.1.tgz", "integrity": "sha512-xhlTqH0m31mnsG0tIP4ETgfSB6gXDaYYsUWTrlUV93fFQPI9dd8hE0Ot6MHLCtqgB32hwJAC3YZMWlXZw7AleA==", "dev": true, "requires": {"node-fetch": "^2.6.1"}}, "@babel/code-frame": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.18.6.tgz", "integrity": "sha512-TDCmlK5eOvH+eH7cdAFlNXeVJqWIQ7gW9tY1GJIpUtFb6CmjVyq2VM3u71bOyR8CRihcCgMUYoDNyLXao3+70Q==", "dev": true, "requires": {"@babel/highlight": "^7.18.6"}}, "@babel/compat-data": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.20.5.tgz", "integrity": "sha512-KZXo2t10+/jxmkhNXc7pZTqRvSOIvVv/+lJwHS+B2rErwOyjuVRh60yVpb7liQ1U5t7lLJ1bz+t8tSypUZdm0g==", "dev": true, "peer": true}, "@babel/core": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.20.5.tgz", "integrity": "sha512-UdOWmk4pNWTm/4DlPUl/Pt4Gz4rcEMb7CY0Y3eJl5Yz1vI8ZJGmHWaVE55LoxRjdpx0z259GE9U5STA9atUinQ==", "dev": true, "peer": true, "requires": {"@ampproject/remapping": "^2.1.0", "@babel/code-frame": "^7.18.6", "@babel/generator": "^7.20.5", "@babel/helper-compilation-targets": "^7.20.0", "@babel/helper-module-transforms": "^7.20.2", "@babel/helpers": "^7.20.5", "@babel/parser": "^7.20.5", "@babel/template": "^7.18.10", "@babel/traverse": "^7.20.5", "@babel/types": "^7.20.5", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.1", "semver": "^6.3.0"}, "dependencies": {"debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "peer": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true, "peer": true}}}, "@babel/generator": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.5.tgz", "integrity": "sha512-jl7JY2Ykn9S0yj4DQP82sYvPU+T3g0HFcWTqDLqiuA9tGRNIj9VfbtXGAYTTkyNEnQk1jkMGOdYka8aG/lulCA==", "dev": true, "requires": {"@babel/types": "^7.20.5", "@jridgewell/gen-mapping": "^0.3.2", "jsesc": "^2.5.1"}, "dependencies": {"@jridgewell/gen-mapping": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.2.tgz", "integrity": "sha512-mh65xKQAzI6iBcFzwv28KVWSmCkdRBWoOh+bYQGW3+6OZvbbN3TqMGo5hqYxQniRcH9F2VZIoJCm4pa3BPDK/A==", "dev": true, "requires": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}}}}, "@babel/helper-compilation-targets": {"version": "7.20.0", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.20.0.tgz", "integrity": "sha512-0jp//vDGp9e8hZzBc6N/KwA5ZK3Wsm/pfm4CrY7vzegkVxc65SgSn6wYOnwHe9Js9HRQ1YTCKLGPzDtaS3RoLQ==", "dev": true, "peer": true, "requires": {"@babel/compat-data": "^7.20.0", "@babel/helper-validator-option": "^7.18.6", "browserslist": "^4.21.3", "semver": "^6.3.0"}}, "@babel/helper-environment-visitor": {"version": "7.18.9", "resolved": "https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.18.9.tgz", "integrity": "sha512-3r/aACDJ3fhQ/EVgFy0hpj8oHyHpQc+LPtJoY9SzTThAsStm4Ptegq92vqKoE3vD706ZVFWITnMnxucw+S9Ipg==", "dev": true}, "@babel/helper-function-name": {"version": "7.19.0", "resolved": "https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.19.0.tgz", "integrity": "sha512-WAwHBINyrpqywkUH0nTnNgI5ina5TFn85HKS0pbPDfxFfhyR/aNQEn4hGi1P1JyT//I0t4OgXUlofzWILRvS5w==", "dev": true, "requires": {"@babel/template": "^7.18.10", "@babel/types": "^7.19.0"}}, "@babel/helper-hoist-variables": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.18.6.tgz", "integrity": "sha512-UlJQPkFqFULIcyW5sbzgbkxn2FKRgwWiRexcuaR8RNJRy8+LLveqPjwZV/bwrLZCN0eUHD/x8D0heK1ozuoo6Q==", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-module-imports": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.18.6.tgz", "integrity": "sha512-0NFvs3VkuSYbFi1x2Vd6tKrywq+z/cLeYC/RJNFrIX/30Bf5aiGYbtvGXolEktzJH8o5E5KJ3tT+nkxuuZFVlA==", "dev": true, "peer": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-module-transforms": {"version": "7.20.2", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.20.2.tgz", "integrity": "sha512-zvBKyJXRbmK07XhMuujYoJ48B5yvvmM6+wcpv6Ivj4Yg6qO7NOZOSnvZN9CRl1zz1Z4cKf8YejmCMh8clOoOeA==", "dev": true, "peer": true, "requires": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-simple-access": "^7.20.2", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/helper-validator-identifier": "^7.19.1", "@babel/template": "^7.18.10", "@babel/traverse": "^7.20.1", "@babel/types": "^7.20.2"}}, "@babel/helper-plugin-utils": {"version": "7.20.2", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.20.2.tgz", "integrity": "sha512-8RvlJG2mj4huQ4pZ+rU9lqKi9ZKiRmuvGuM2HlWmkmgOhbs6zEAw6IEiJ5cQqGbDzGZOhwuOQNtZMi/ENLjZoQ==", "dev": true}, "@babel/helper-simple-access": {"version": "7.20.2", "resolved": "https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.20.2.tgz", "integrity": "sha512-+0woI/WPq59IrqDYbVGfshjT5Dmk/nnbdpcF8SnMhhXObpTq2KNBdLFRFrkVdbDOyUmHBCxzm5FHV1rACIkIbA==", "dev": true, "peer": true, "requires": {"@babel/types": "^7.20.2"}}, "@babel/helper-split-export-declaration": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.18.6.tgz", "integrity": "sha512-bde1etTx6ZyTmobl9LLMMQsaizFVZrquTEHOqKeQESMKo4PlObf+8+JA25ZsIpZhT/WEd39+vOdLXAFG/nELpA==", "dev": true, "requires": {"@babel/types": "^7.18.6"}}, "@babel/helper-string-parser": {"version": "7.19.4", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.19.4.tgz", "integrity": "sha512-nHtDoQcuqFmwYNYPz3Rah5ph2p8PFeFCsZk9A/48dPc/rGocJ5J3hAAZ7pb76VWX3fZKu+uEr/FhH5jLx7umrw==", "dev": true}, "@babel/helper-validator-identifier": {"version": "7.19.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "integrity": "sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w==", "dev": true}, "@babel/helper-validator-option": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.18.6.tgz", "integrity": "sha512-XO7gESt5ouv/LRJdrVjkShckw6STTaB7l9BrpBaAHDeF5YZT+01PCwmR0SJHnkW6i8OwW/EVWRShfi4j2x+KQw==", "dev": true, "peer": true}, "@babel/helpers": {"version": "7.20.6", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.20.6.tgz", "integrity": "sha512-Pf/OjgfgFRW5bApskEz5pvidpim7tEDPlFtKcNRXWmfHGn9IEI2W2flqRQXTFb7gIPTyK++N6rVHuwKut4XK6w==", "dev": true, "peer": true, "requires": {"@babel/template": "^7.18.10", "@babel/traverse": "^7.20.5", "@babel/types": "^7.20.5"}}, "@babel/highlight": {"version": "7.18.6", "resolved": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.18.6.tgz", "integrity": "sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==", "dev": true, "requires": {"@babel/helper-validator-identifier": "^7.18.6", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "dev": true, "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "dev": true, "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "dev": true, "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "integrity": "sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==", "dev": true}, "has-flag": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==", "dev": true}, "supports-color": {"version": "5.5.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "dev": true, "requires": {"has-flag": "^3.0.0"}}}}, "@babel/parser": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.20.5.tgz", "integrity": "sha512-r27t/cy/m9uKLXQNWWebeCUHgnAZq0CpG1OwKRxzJMP1vpSU4bSIK2hq+/cp0bQxetkXx38n09rNu8jVkcK/zA==", "dev": true}, "@babel/plugin-syntax-import-assertions": {"version": "7.20.0", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.20.0.tgz", "integrity": "sha512-IUh1vakzNoWalR8ch/areW7qFopR2AEw03JlG7BbrDqmQ4X3q9uuipQwSGrUn7oGiemKjtSLDhNtQHzMHr1JdQ==", "dev": true, "requires": {"@babel/helper-plugin-utils": "^7.19.0"}}, "@babel/template": {"version": "7.18.10", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.18.10.tgz", "integrity": "sha512-TI+rCtooWHr3QJ27kJxfjutghu44DLnasDMwpDqCXVTal9RLp3RSYNh4NdBrRP2cQAoG9A8juOQl6P6oZG4JxA==", "dev": true, "requires": {"@babel/code-frame": "^7.18.6", "@babel/parser": "^7.18.10", "@babel/types": "^7.18.10"}}, "@babel/traverse": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.20.5.tgz", "integrity": "sha512-WM5ZNN3JITQIq9tFZaw1ojLU3WgWdtkxnhM1AegMS+PvHjkM5IXjmYEGY7yukz5XS4sJyEf2VzWjI8uAavhxBQ==", "dev": true, "requires": {"@babel/code-frame": "^7.18.6", "@babel/generator": "^7.20.5", "@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-function-name": "^7.19.0", "@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-split-export-declaration": "^7.18.6", "@babel/parser": "^7.20.5", "@babel/types": "^7.20.5", "debug": "^4.1.0", "globals": "^11.1.0"}, "dependencies": {"debug": {"version": "4.3.4", "resolved": "https://registry.npmjs.org/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "dev": true, "requires": {"ms": "2.1.2"}}, "ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "dev": true}}}, "@babel/types": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.20.5.tgz", "integrity": "sha512-c9fst/h2/dcF7H+MJKZ2T0KjEQ8hY/BNnDk/H3XY8C4Aw/eWQXWn/lWntHF9ooUBnGmEvbfGrTgLWc+um0YDUg==", "dev": true, "requires": {"@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1", "to-fast-properties": "^2.0.0"}}, "@colors/colors": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@colors/colors/-/colors-1.5.0.tgz", "integrity": "sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==", "dev": true, "optional": true}, "@envelop/core": {"version": "2.6.0", "resolved": "https://registry.npmjs.org/@envelop/core/-/core-2.6.0.tgz", "integrity": "sha512-yTptKinJN//i6m1kXUbnLBl/FobzddI4ehURAMS08eRUOQwAuXqJU9r8VdTav8nIZLb4t6cuDWFb3n331LiwLw==", "dev": true, "requires": {"@envelop/types": "2.4.0", "tslib": "2.4.0"}, "dependencies": {"tslib": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz", "integrity": "sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==", "dev": true}}}, "@envelop/disable-introspection": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/@envelop/disable-introspection/-/disable-introspection-3.6.0.tgz", "integrity": "sha512-Zf505LqWo2efM0EtTBvXyYPynSTSL2+Ps0X+dCH33vQ3wF6gxMui9g1pGa4MDcGjaXWG67qZcT4eojpGlmmavA==", "dev": true, "requires": {"tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@envelop/parser-cache": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/@envelop/parser-cache/-/parser-cache-4.7.0.tgz", "integrity": "sha512-63NfXDcW/vGn4U6NFxaZ0JbYWAcJb9A6jhTvghsSz1ZS+Dny/ci8bVSgVmM1q+N56hPyGsVPuyI+rIc71mPU5g==", "dev": true, "requires": {"lru-cache": "^6.0.0", "tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@envelop/types": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/@envelop/types/-/types-2.4.0.tgz", "integrity": "sha512-pjxS98cDQBS84X29VcwzH3aJ/KiLCGwyMxuj7/5FkdiaCXAD1JEvKEj9LARWlFYj1bY43uII4+UptFebrhiIaw==", "dev": true, "requires": {"tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@envelop/validation-cache": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/@envelop/validation-cache/-/validation-cache-4.7.0.tgz", "integrity": "sha512-PzL+GfWJRT+JjsJqZAIxHKEkvkM3hxkeytS5O0QLXT8kURNBV28r+Kdnn2RCF5+6ILhyGpiDb60vaquBi7g4lw==", "dev": true, "requires": {"lru-cache": "^6.0.0", "tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-inspector/audit-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/audit-command/-/audit-command-3.4.0.tgz", "integrity": "sha512-q4bgl/TaQLIGrQZVg95wUbzfkzkyUOhWfni3JaKpbc75ZLz6kU1c5lHL3do637QFqttxgWwmMLXJi1jO0FL+tA==", "dev": true, "requires": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "cli-table3": "0.6.2", "tslib": "^2.0.0"}, "dependencies": {"@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "peer": true, "requires": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}}, "@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "peer": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-inspector/cli": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/cli/-/cli-3.4.0.tgz", "integrity": "sha512-4+fY42ktYKymCGJ78hADHskqHpi2X08+uYAiE7EBfqqueUFrMj1pLmJYXNnK/5TQ6/XaLvyvussw/W/eQNmuYg==", "dev": true, "requires": {"@graphql-inspector/audit-command": "3.4.0", "@graphql-inspector/code-loader": "3.4.0", "@graphql-inspector/commands": "3.4.0", "@graphql-inspector/coverage-command": "3.4.0", "@graphql-inspector/diff-command": "3.4.0", "@graphql-inspector/docs-command": "3.4.0", "@graphql-inspector/git-loader": "3.4.0", "@graphql-inspector/github-loader": "3.4.0", "@graphql-inspector/graphql-loader": "3.4.0", "@graphql-inspector/introspect-command": "3.4.0", "@graphql-inspector/json-loader": "3.4.0", "@graphql-inspector/loaders": "3.4.0", "@graphql-inspector/serve-command": "3.4.0", "@graphql-inspector/similar-command": "3.4.0", "@graphql-inspector/url-loader": "3.4.0", "@graphql-inspector/validate-command": "3.4.0", "tslib": "^2.0.0", "yargs": "17.2.1"}, "dependencies": {"@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "requires": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}}, "@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "peer": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-inspector/code-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/code-loader/-/code-loader-3.4.0.tgz", "integrity": "sha512-rgC6x56QLIHGHzz6+QeBhPZz3CgwxqSwKJO0LZgi6wEkurbrrVlEZTUTy3LE9RSfHFdL7w3v9vfBjls3vp9wIg==", "dev": true, "requires": {"@graphql-tools/code-file-loader": "^7.2.2", "tslib": "^2.0.0"}}, "@graphql-inspector/config": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/config/-/config-3.4.0.tgz", "integrity": "sha512-h9qLXP+f7IA6v3WT+3hMOl3hdxhVSdYcgajzXkJx5p5OYyibyTzsMJPghplXxoORcjQL3MMpRXg90+OVHxq+zw==", "dev": true, "peer": true, "requires": {}}, "@graphql-inspector/core": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/core/-/core-3.4.0.tgz", "integrity": "sha512-035TchqbbyWaNDOJKxp+RG6Ho/nGMXKKgzojQ6Dn1N20jgR/oX9rTVvR8qkzpy0/IxxP8PD9RcZ52vsTAehxdg==", "dev": true, "requires": {"dependency-graph": "0.11.0", "object-inspect": "1.10.3", "tslib": "^2.0.0"}, "dependencies": {"object-inspect": {"version": "1.10.3", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.10.3.tgz", "integrity": "sha512-e5mCJlSH7poANfC8z8S9s9S2IN5/4Zb3aZ33f5s8YqoazCFzNLloLU8r5VCG+G7WoqLvAAZoVMcy3tp/3X0Plw==", "dev": true}}}, "@graphql-inspector/coverage-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/coverage-command/-/coverage-command-3.4.0.tgz", "integrity": "sha512-TmBg0I+DUsxGzuasZs9WkB/MR3iH4KUhkRRQsSlT+PKzunt1njUyYd1UeTpaZCmiTypF4m1jb7lAeDw3HDw2Ww==", "dev": true, "requires": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "dependencies": {"@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "peer": true, "requires": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}}, "@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "peer": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-inspector/diff-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/diff-command/-/diff-command-3.4.0.tgz", "integrity": "sha512-x2sgPwVpHzG4ranETqszjy/WL+TVmxRirtNuF3yBMQS7XcfNIEXljIcTU2m+Ar7TNsTFZNMcDcw4xLmUrTnrRw==", "dev": true, "requires": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "dependencies": {"@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "peer": true, "requires": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}}, "@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "peer": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-inspector/docs-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/docs-command/-/docs-command-3.4.0.tgz", "integrity": "sha512-+GN8bdUduKYQvzwygzNtWeOaKzIFpifihTsrAspErHIq+rXs1MWerktTIj9m/Y7AF9uF/cI04SfxzHXhfvUEig==", "dev": true, "requires": {"@graphql-inspector/commands": "3.4.0", "open": "8.2.1", "tslib": "^2.0.0"}, "dependencies": {"@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "peer": true, "requires": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}}, "@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "peer": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-inspector/git-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/git-loader/-/git-loader-3.4.0.tgz", "integrity": "sha512-/5zdyFXMgMadyuAN6mDTfcien1vvO31X/hU1dOAdyOGjhl5ZkH/D5ZgYjYc94VSsoixGaQOweq5Kl95cZeTlgA==", "dev": true, "requires": {"@graphql-tools/git-loader": "^7.1.2", "tslib": "^2.0.0"}}, "@graphql-inspector/github-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/github-loader/-/github-loader-3.4.0.tgz", "integrity": "sha512-+kV2PT5BIDMFIdgkS04ESmwcyuV8SBQ3Furp7snXtGAK+8cYi8PmL0/qIztzp7/3nzQ9WV//P/RloT8dEdRDCw==", "dev": true, "requires": {"@graphql-tools/github-loader": "^7.1.1", "tslib": "^2.0.0"}}, "@graphql-inspector/graphql-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/graphql-loader/-/graphql-loader-3.4.0.tgz", "integrity": "sha512-igDqEhmYa5FTrw/Q25a8iTs3qNc//5DR9CIzxnYS9/auuhYz3q7NWistO76WWhNu2IAs1ReMM1xrexzsTxB5QA==", "dev": true, "requires": {"@graphql-tools/graphql-file-loader": "^7.3.2", "tslib": "^2.0.0"}}, "@graphql-inspector/introspect-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/introspect-command/-/introspect-command-3.4.0.tgz", "integrity": "sha512-rASIY8eo+IKwDMaOTT8GYxbqSazgHcdrUJzxjCkD1Rir7U0nCs8NvGiQPM0sbcpelEGNsy+wjRuxdy3iBxI8HA==", "dev": true, "requires": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "dependencies": {"@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "peer": true, "requires": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}}, "@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "peer": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-inspector/json-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/json-loader/-/json-loader-3.4.0.tgz", "integrity": "sha512-prXZ0UaSBMTqbp+CAP9Rc2P03EwF8yfnKfP7HlXJ0NT2G8bmdUD1fODphUO2mqB/Hdsp4zo7SxIoq6D6Qq9sOQ==", "dev": true, "requires": {"@graphql-tools/json-file-loader": "^7.3.2", "tslib": "^2.0.0"}}, "@graphql-inspector/logger": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/logger/-/logger-3.4.0.tgz", "integrity": "sha512-r2C0tCLQMInn6knQNUN4Gm9qGE98Bo070JwXpJkRXW51Vt9eAF8WEt4rjg2rMYeVjMLR7ROF328sjMo1v0ihVQ==", "dev": true, "requires": {"chalk": "4.1.2", "figures": "3.2.0", "log-symbols": "^4.1.0", "std-env": "2.3.0", "tslib": "^2.0.0"}}, "@graphql-inspector/serve-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/serve-command/-/serve-command-3.4.0.tgz", "integrity": "sha512-7xLsNRKwiXG8te9w2QCy/Od+E4kLvU3Icao36/ohmsBcFXgj9M6juWt3FaZK5crOHaH2wa7G34Lr3gGIMz4gbw==", "dev": true, "requires": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/logger": "3.4.0", "@graphql-yoga/node": "2.0.0", "open": "8.2.1", "tslib": "^2.0.0"}, "dependencies": {"@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "peer": true, "requires": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}}, "@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "peer": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-inspector/similar-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/similar-command/-/similar-command-3.4.0.tgz", "integrity": "sha512-I4UM2AdIHilUnizaw5g45M2ZwA89+zGYN7QdtXQGhMQbniuBQ5ZNoJC/YavMFamF3Y6ROElYutSuqbMm3EgfJg==", "dev": true, "requires": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "dependencies": {"@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "peer": true, "requires": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}}, "@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "peer": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-inspector/url-loader": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/url-loader/-/url-loader-3.4.0.tgz", "integrity": "sha512-q81y0mwGNKno/RyBaLXhtxtza9LZPwjkfY8WRqxytQmPrNDEeg2I3MdhcNOwctA2Gh124Xw0D5foj3KTYebJqQ==", "dev": true, "requires": {"@graphql-tools/url-loader": "^7.4.2", "tslib": "^2.0.0"}}, "@graphql-inspector/validate-command": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/validate-command/-/validate-command-3.4.0.tgz", "integrity": "sha512-DhlBHsv46xWX7XXzs/STLj4vQJnG8G5jsW0GuipmFgIcUlJdB46eNSoR8GWjUbsjgncPBS1KbMgdjlrcc9lecA==", "dev": true, "requires": {"@graphql-inspector/commands": "3.4.0", "@graphql-inspector/core": "3.4.0", "@graphql-inspector/logger": "3.4.0", "tslib": "^2.0.0"}, "dependencies": {"@graphql-inspector/commands": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/commands/-/commands-3.4.0.tgz", "integrity": "sha512-Nrq1N0FBxa53+Cbzdr59qrsKN68an6SAb2IMbYaNi16nJUzvjT9ZcT8p98BK6RyDP4kGos4GxZD9lqAt1tvEgA==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@graphql-inspector/loaders": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@graphql-inspector/loaders/-/loaders-3.4.0.tgz", "integrity": "sha512-hBz6mBSIUrg8WrWH2H91w+KDeV8hWIMY3L2917k713DfKuE8jz7hRkiNnBwP+n7uISGEYXHYsCKY4Ok30oH+Jg==", "dev": true, "peer": true, "requires": {"@graphql-tools/load": "^7.4.1", "tslib": "^2.0.0"}}, "@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "peer": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-tools/batch-execute": {"version": "8.5.14", "resolved": "https://registry.npmjs.org/@graphql-tools/batch-execute/-/batch-execute-8.5.14.tgz", "integrity": "sha512-m6yXqqmFAH2V5JuSIC/geiGLBQA1Y6RddOJfUtkc9Z7ttkULRCd1W39TpYS6IlrCwYyTj+klO1/kdWiny38f5g==", "dev": true, "requires": {"@graphql-tools/utils": "9.1.3", "dataloader": "2.1.0", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/code-file-loader": {"version": "7.3.15", "resolved": "https://registry.npmjs.org/@graphql-tools/code-file-loader/-/code-file-loader-7.3.15.tgz", "integrity": "sha512-cF8VNc/NANTyVSIK8BkD/KSXRF64DvvomuJ0evia7tJu4uGTXgDjimTMWsTjKRGOOBSTEbL6TA8e4DdIYq6Udw==", "dev": true, "requires": {"@graphql-tools/graphql-tag-pluck": "7.4.2", "@graphql-tools/utils": "9.1.3", "globby": "^11.0.3", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/delegate": {"version": "9.0.20", "resolved": "https://registry.npmjs.org/@graphql-tools/delegate/-/delegate-9.0.20.tgz", "integrity": "sha512-m/de++kSxa/JABQ15tyt6SMlc4qfl3MgFFWmwbPcWgJxlAvZ7oIdGXcUvU6VJcXiyXfo6MZ/zr4V2E2UwMehew==", "dev": true, "requires": {"@graphql-tools/batch-execute": "8.5.14", "@graphql-tools/executor": "0.0.11", "@graphql-tools/schema": "9.0.12", "@graphql-tools/utils": "9.1.3", "dataloader": "2.1.0", "tslib": "~2.4.0", "value-or-promise": "1.0.11"}, "dependencies": {"@graphql-tools/merge": {"version": "8.3.14", "resolved": "https://registry.npmjs.org/@graphql-tools/merge/-/merge-8.3.14.tgz", "integrity": "sha512-zV0MU1DnxJLIB0wpL4N3u21agEiYFsjm6DI130jqHpwF0pR9HkF+Ni65BNfts4zQelP0GjkHltG+opaozAJ1NA==", "dev": true, "requires": {"@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0"}}, "@graphql-tools/schema": {"version": "9.0.12", "resolved": "https://registry.npmjs.org/@graphql-tools/schema/-/schema-9.0.12.tgz", "integrity": "sha512-DmezcEltQai0V1y96nwm0Kg11FDS/INEFekD4nnVgzBqawvznWqK6D6bujn+cw6kivoIr3Uq//QmU/hBlBzUlQ==", "dev": true, "requires": {"@graphql-tools/merge": "8.3.14", "@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}}, "@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/executor": {"version": "0.0.11", "resolved": "https://registry.npmjs.org/@graphql-tools/executor/-/executor-0.0.11.tgz", "integrity": "sha512-GjtXW0ZMGZGKad6A1HXFPArkfxE0AIpznusZuQdy4laQx+8Ut3Zx8SAFJNnDfZJ2V5kU29B5Xv3Fr0/DiMBHOQ==", "dev": true, "requires": {"@graphql-tools/utils": "9.1.3", "@graphql-typed-document-node/core": "3.1.1", "@repeaterjs/repeater": "3.0.4", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/executor-graphql-ws": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/@graphql-tools/executor-graphql-ws/-/executor-graphql-ws-0.0.5.tgz", "integrity": "sha512-1bJfZdSBPCJWz1pJ5g/YHMtGt6YkNRDdmqNQZ8v+VlQTNVfuBpY2vzj15uvf5uDrZLg2MSQThrKlL8av4yFpsA==", "dev": true, "requires": {"@graphql-tools/utils": "9.1.3", "@repeaterjs/repeater": "3.0.4", "@types/ws": "^8.0.0", "graphql-ws": "5.11.2", "isomorphic-ws": "5.0.0", "tslib": "^2.4.0", "ws": "8.11.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/executor-http": {"version": "0.0.7", "resolved": "https://registry.npmjs.org/@graphql-tools/executor-http/-/executor-http-0.0.7.tgz", "integrity": "sha512-g0NV4HVZVABsylk6SIA/gfjQbMIsy3NjZYW0k0JZmTcp9698J37uG50GZC2mKe0F8pIlDvPLvrPloqdKGX3ZAA==", "dev": true, "requires": {"@graphql-tools/utils": "9.1.3", "@repeaterjs/repeater": "3.0.4", "@whatwg-node/fetch": "0.5.3", "dset": "3.1.2", "extract-files": "^11.0.0", "meros": "1.2.1", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/executor-legacy-ws": {"version": "0.0.5", "resolved": "https://registry.npmjs.org/@graphql-tools/executor-legacy-ws/-/executor-legacy-ws-0.0.5.tgz", "integrity": "sha512-j2ZQVTI4rKIT41STzLPK206naYDhHxmGHot0siJKBKX1vMqvxtWBqvL66v7xYEOaX79wJrFc8l6oeURQP2LE6g==", "dev": true, "requires": {"@graphql-tools/utils": "9.1.3", "@types/ws": "^8.0.0", "isomorphic-ws": "5.0.0", "tslib": "^2.4.0", "ws": "8.11.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/git-loader": {"version": "7.2.15", "resolved": "https://registry.npmjs.org/@graphql-tools/git-loader/-/git-loader-7.2.15.tgz", "integrity": "sha512-1d5HmeuxhSNjQ2+k2rfKgcKcnZEC6H5FM2pY5lSXHMv8VdBELZd7pYDs5/JxoZarDVYfYOJ5xTeVzxf+Du3VNg==", "dev": true, "requires": {"@graphql-tools/graphql-tag-pluck": "7.4.2", "@graphql-tools/utils": "9.1.3", "is-glob": "4.0.3", "micromatch": "^4.0.4", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/github-loader": {"version": "7.3.22", "resolved": "https://registry.npmjs.org/@graphql-tools/github-loader/-/github-loader-7.3.22.tgz", "integrity": "sha512-JE5F/ObbwknO7+gDfeuKAZtLS831WV8/SsLzQLMGY0hdgTbsAg2/xziAGprNToK4GMSD7ygCer9ZryvxBKMwbQ==", "dev": true, "requires": {"@ardatan/sync-fetch": "0.0.1", "@graphql-tools/graphql-tag-pluck": "7.4.2", "@graphql-tools/utils": "9.1.3", "@whatwg-node/fetch": "^0.5.0", "tslib": "^2.4.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/graphql-file-loader": {"version": "7.5.16", "resolved": "https://registry.npmjs.org/@graphql-tools/graphql-file-loader/-/graphql-file-loader-7.5.16.tgz", "integrity": "sha512-lK1N3Y2I634FS12nd4bu7oAJbai3bUc28yeX+boT+C83KTO4ujGHm+6hPC8X/FRGwhKOnZBxUM7I5nvb3HiUxw==", "dev": true, "requires": {"@graphql-tools/import": "6.7.17", "@graphql-tools/utils": "9.2.1", "globby": "^11.0.3", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.2.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.2.1.tgz", "integrity": "sha512-WUw506Ql6xzmOORlriNrD6Ugx+HjVgYxt9KCXD9mHAak+eaXSwuGGPyE60hy9xaDEoXKBsG7SkG69ybitaVl6A==", "dev": true, "requires": {"@graphql-typed-document-node/core": "^3.1.1", "tslib": "^2.4.0"}}, "tslib": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==", "dev": true}}}, "@graphql-tools/graphql-tag-pluck": {"version": "7.4.2", "resolved": "https://registry.npmjs.org/@graphql-tools/graphql-tag-pluck/-/graphql-tag-pluck-7.4.2.tgz", "integrity": "sha512-SXM1wR5TExrxocQTxZK5r74jTbg8GxSYLY3mOPCREGz6Fu7PNxMxfguUzGUAB43Mf44Dn8oVztzd2eitv2Qgww==", "dev": true, "requires": {"@babel/parser": "^7.16.8", "@babel/plugin-syntax-import-assertions": "7.20.0", "@babel/traverse": "^7.16.8", "@babel/types": "^7.16.8", "@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/import": {"version": "6.7.17", "resolved": "https://registry.npmjs.org/@graphql-tools/import/-/import-6.7.17.tgz", "integrity": "sha512-bn9SgrECXq3WIasgNP7ful/uON51wBajPXtxdY+z/ce7jLWaFE6lzwTDB/GAgiZ+jo7nb0ravlxteSAz2qZmuA==", "dev": true, "requires": {"@graphql-tools/utils": "9.2.1", "resolve-from": "5.0.0", "tslib": "^2.4.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.2.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.2.1.tgz", "integrity": "sha512-WUw506Ql6xzmOORlriNrD6Ugx+HjVgYxt9KCXD9mHAak+eaXSwuGGPyE60hy9xaDEoXKBsG7SkG69ybitaVl6A==", "dev": true, "requires": {"@graphql-typed-document-node/core": "^3.1.1", "tslib": "^2.4.0"}}, "tslib": {"version": "2.5.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.5.0.tgz", "integrity": "sha512-336iVw3rtn2BUK7ORdIAHTyxHGRIHVReokCR3XjbckJMK7ms8FysBfhLR8IXnAgy7T0PTPNBWKiH514FOW/WSg==", "dev": true}}}, "@graphql-tools/json-file-loader": {"version": "7.4.14", "resolved": "https://registry.npmjs.org/@graphql-tools/json-file-loader/-/json-file-loader-7.4.14.tgz", "integrity": "sha512-AD9v3rN08wvVqgbrUSiHa8Ztrlk3EgwctcxuNE5qm47zPNL4gLaJ7Tw/KlGOR7Cm+pjlQylJHMUKNfaRLPZ0og==", "dev": true, "requires": {"@graphql-tools/utils": "9.1.3", "globby": "^11.0.3", "tslib": "^2.4.0", "unixify": "^1.0.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/load": {"version": "7.7.7", "resolved": "https://registry.npmjs.org/@graphql-tools/load/-/load-7.7.7.tgz", "integrity": "sha512-IpI2672zcoAX4FLjcH5kvHc7eqjPyLP1svrIcZKQenv0GRS6dW0HI9E5UCBs0y/yy8yW6s+SvpmNsfIlkMj3Kw==", "dev": true, "requires": {"@graphql-tools/schema": "9.0.4", "@graphql-tools/utils": "8.12.0", "p-limit": "3.1.0", "tslib": "^2.4.0"}, "dependencies": {"@graphql-tools/merge": {"version": "8.3.6", "resolved": "https://registry.npmjs.org/@graphql-tools/merge/-/merge-8.3.6.tgz", "integrity": "sha512-uUBokxXi89bj08P+iCvQk3Vew4vcfL5ZM6NTylWi8PIpoq4r5nJ625bRuN8h2uubEdRiH8ntN9M4xkd/j7AybQ==", "dev": true, "requires": {"@graphql-tools/utils": "8.12.0", "tslib": "^2.4.0"}}, "@graphql-tools/schema": {"version": "9.0.4", "resolved": "https://registry.npmjs.org/@graphql-tools/schema/-/schema-9.0.4.tgz", "integrity": "sha512-B/b8ukjs18fq+/s7p97P8L1VMrwapYc3N2KvdG/uNThSazRRn8GsBK0Nr+FH+mVKiUfb4Dno79e3SumZVoHuOQ==", "dev": true, "requires": {"@graphql-tools/merge": "8.3.6", "@graphql-tools/utils": "8.12.0", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}}, "@graphql-tools/utils": {"version": "8.12.0", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.12.0.tgz", "integrity": "sha512-Te<PERSON>+MJWGXjUTS52qfK4R8HiPoF/R7X+qmgtOYd8DTH0l6b+5Y/tlg5aGeUJefqImRq7nvi93Ms40k/Uz4D5CWw==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.0.tgz", "integrity": "sha512-d6xOpEDfsi2CZVlPQzGeux8XMwLT9hssAsaPYExaQMuYskwb+x1x7J371tWlbBdWHroy99KnVB6qIkUbs5X3UQ==", "dev": true}}}, "@graphql-tools/load-files": {"version": "6.5.3", "resolved": "https://registry.npmjs.org/@graphql-tools/load-files/-/load-files-6.5.3.tgz", "integrity": "sha512-hu6hw2fj3ltno7ezbyAQc5VdLCymgx0XIwvk0l9sWAlV2xnaNcX7p/z1qm6U/db2MiCumRBBIm/QuR7Jn71qZg==", "dev": true, "requires": {"globby": "11.0.4", "tslib": "~2.3.0", "unixify": "1.0.0"}}, "@graphql-tools/merge": {"version": "8.2.10", "resolved": "https://registry.npmjs.org/@graphql-tools/merge/-/merge-8.2.10.tgz", "integrity": "sha512-wpg22seOTNfkIO8jFAgo8w1BsT3IS2OTMpkCNf+dvcKSP09SVidYCOliyWHgjDCmpCrvvSjOX855NUKDx/Biew==", "requires": {"@graphql-tools/utils": "8.6.9", "tslib": "~2.3.0"}, "dependencies": {"@graphql-tools/utils": {"version": "8.6.9", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.6.9.tgz", "integrity": "sha512-Z1X4d4GCT81+8CSt6SgU4t1w1UAUsAIRb67mI90k/zAs+ArkB95iE3bWXuJCUmd1+r8DGGtmUNOArtd6wkt+OQ==", "requires": {"tslib": "~2.3.0"}}}}, "@graphql-tools/mock": {"version": "8.6.8", "resolved": "https://registry.npmjs.org/@graphql-tools/mock/-/mock-8.6.8.tgz", "integrity": "sha512-zBZApp8dDAovWKZ0rkZ4CwDT8Z+B35pIyRjeHkxvtKt5XyEAabEwkuSYMyFdsghDWwhMD/VAZ/6DXtA62Hnf+A==", "requires": {"@graphql-tools/schema": "8.3.10", "@graphql-tools/utils": "8.6.9", "fast-json-stable-stringify": "^2.1.0", "tslib": "~2.3.0"}, "dependencies": {"@graphql-tools/utils": {"version": "8.6.9", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.6.9.tgz", "integrity": "sha512-Z1X4d4GCT81+8CSt6SgU4t1w1UAUsAIRb67mI90k/zAs+ArkB95iE3bWXuJCUmd1+r8DGGtmUNOArtd6wkt+OQ==", "requires": {"tslib": "~2.3.0"}}}}, "@graphql-tools/schema": {"version": "8.3.10", "resolved": "https://registry.npmjs.org/@graphql-tools/schema/-/schema-8.3.10.tgz", "integrity": "sha512-tfhjSTi3OzheDrVzG7rkPZg2BbQjmZRLM2vvQoM2b1TnUwgUIbpAgcnf+AWDLRsoCOWlezeLgij1BLeAR0Q0jg==", "requires": {"@graphql-tools/merge": "8.2.10", "@graphql-tools/utils": "8.6.9", "tslib": "~2.3.0", "value-or-promise": "1.0.11"}, "dependencies": {"@graphql-tools/utils": {"version": "8.6.9", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.6.9.tgz", "integrity": "sha512-Z1X4d4GCT81+8CSt6SgU4t1w1UAUsAIRb67mI90k/zAs+ArkB95iE3bWXuJCUmd1+r8DGGtmUNOArtd6wkt+OQ==", "requires": {"tslib": "~2.3.0"}}}}, "@graphql-tools/url-loader": {"version": "7.16.26", "resolved": "https://registry.npmjs.org/@graphql-tools/url-loader/-/url-loader-7.16.26.tgz", "integrity": "sha512-mrbFhShlthSdvMD3GPeKYxUt54CBUteN0eR6WgJJSXibycTSA6h0LAn6iyCAg+uUsBP/KR6GcjvQHifl00Q7rQ==", "dev": true, "requires": {"@ardatan/sync-fetch": "0.0.1", "@graphql-tools/delegate": "9.0.20", "@graphql-tools/executor-graphql-ws": "0.0.5", "@graphql-tools/executor-http": "0.0.7", "@graphql-tools/executor-legacy-ws": "0.0.5", "@graphql-tools/utils": "9.1.3", "@graphql-tools/wrap": "9.2.21", "@types/ws": "^8.0.0", "@whatwg-node/fetch": "^0.5.0", "isomorphic-ws": "5.0.0", "tslib": "^2.4.0", "value-or-promise": "^1.0.11", "ws": "8.11.0"}, "dependencies": {"@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-tools/utils": {"version": "10.8.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-10.8.3.tgz", "integrity": "sha512-4QCvx3SWRsbH7wnktl51mBek+zE9hsjsv796XVlJlOUdWpAghJmA3ID2P7/Vwuy7BivVNfuAKe4ucUdE1fG7vA==", "requires": {"@graphql-typed-document-node/core": "^3.1.1", "@whatwg-node/promise-helpers": "^1.0.0", "cross-inspect": "1.0.1", "dset": "^3.1.4", "tslib": "^2.4.0"}, "dependencies": {"dset": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/dset/-/dset-3.1.4.tgz", "integrity": "sha512-2QF/g9/zTaPDc3BjNcVTGoBbXBgYfMTTceLaYcFJ/W9kggFUkhxD/hMEeuLKbugyef9SqAx8cpgwlIP/jinUTA=="}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="}}}, "@graphql-tools/wrap": {"version": "9.2.21", "resolved": "https://registry.npmjs.org/@graphql-tools/wrap/-/wrap-9.2.21.tgz", "integrity": "sha512-h4S9x9Rh8aSKkBGv+nq43Z0qK7CH8ySl+9RDO0MluscWlqNNECJ34FytwPVd8sXSrS7JwgwmVNcT51EH1n0wRw==", "dev": true, "requires": {"@graphql-tools/delegate": "9.0.20", "@graphql-tools/schema": "9.0.12", "@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}, "dependencies": {"@graphql-tools/merge": {"version": "8.3.14", "resolved": "https://registry.npmjs.org/@graphql-tools/merge/-/merge-8.3.14.tgz", "integrity": "sha512-zV0MU1DnxJLIB0wpL4N3u21agEiYFsjm6DI130jqHpwF0pR9HkF+Ni65BNfts4zQelP0GjkHltG+opaozAJ1NA==", "dev": true, "requires": {"@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0"}}, "@graphql-tools/schema": {"version": "9.0.12", "resolved": "https://registry.npmjs.org/@graphql-tools/schema/-/schema-9.0.12.tgz", "integrity": "sha512-DmezcEltQai0V1y96nwm0Kg11FDS/INEFekD4nnVgzBqawvznWqK6D6bujn+cw6kivoIr3Uq//QmU/hBlBzUlQ==", "dev": true, "requires": {"@graphql-tools/merge": "8.3.14", "@graphql-tools/utils": "9.1.3", "tslib": "^2.4.0", "value-or-promise": "1.0.11"}}, "@graphql-tools/utils": {"version": "9.1.3", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-9.1.3.tgz", "integrity": "sha512-bbJyKhs6awp1/OmP+WKA1GOyu9UbgZGkhIj5srmiMGLHohEOKMjW784Sk0BZil1w2x95UPu0WHw6/d/HVCACCg==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@graphql-typed-document-node/core": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/@graphql-typed-document-node/core/-/core-3.1.1.tgz", "integrity": "sha512-NQ17ii0rK1b34VZonlmT2QMJFI70m0TRwbknO/ihlbatXyaktDhN/98vBiUU6kNBPljqGqyIrl2T4nY2RpFANg==", "requires": {}}, "@graphql-yoga/common": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@graphql-yoga/common/-/common-2.0.0.tgz", "integrity": "sha512-nSENt7yANcnAIHdjTWOeTz4feXnGkgiShUU9MzukFbETWL7pwaK6fjgghUCDVuL2axm155Z3lAhG4sLA4MgvlQ==", "dev": true, "requires": {"@envelop/core": "^2.0.0", "@envelop/disable-introspection": "^3.0.0", "@envelop/parser-cache": "^4.0.0", "@envelop/validation-cache": "^4.0.0", "@graphql-tools/schema": "^8.3.1", "@graphql-tools/utils": "^8.6.0", "@graphql-typed-document-node/core": "^3.1.1", "@graphql-yoga/render-graphiql": "2.0.0", "@graphql-yoga/subscription": "2.0.0", "chalk": "4.1.2", "cross-undici-fetch": "^0.1.25", "dset": "^3.1.1", "tslib": "^2.3.1"}, "dependencies": {"@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-yoga/node": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@graphql-yoga/node/-/node-2.0.0.tgz", "integrity": "sha512-fjKHT8UR1DcOgvUfrnVhM7Ggj5vnxVUwlUbkY7NZC8vZiXfVbA9UfLYUYl+hXFmOCDK+S19kszLp7rw0s0LdWg==", "dev": true, "requires": {"@envelop/core": "^2.0.0", "@graphql-tools/utils": "^8.6.0", "@graphql-yoga/common": "2.0.0", "@graphql-yoga/subscription": "2.0.0", "cross-undici-fetch": "^0.1.25", "tslib": "^2.3.1"}, "dependencies": {"@graphql-tools/utils": {"version": "8.13.1", "resolved": "https://registry.npmjs.org/@graphql-tools/utils/-/utils-8.13.1.tgz", "integrity": "sha512-qIh9yYpdUFmctVqovwMdheVNJqFh+DQNWIhX87FJStfXYnmweBUDATok9fWPleKeFwxnW8IapKmY8m8toJEkAw==", "dev": true, "requires": {"tslib": "^2.4.0"}}, "tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true}}}, "@graphql-yoga/render-graphiql": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@graphql-yoga/render-graphiql/-/render-graphiql-2.0.0.tgz", "integrity": "sha512-GZuZPCVBpYI0MwaE6nBtrzMoSFyBtiWOUm7i9jbRxu5Sqxuj3Zc0zTaD7V0UQzuPZKzytmMWq2+nbWPIu5FWtg==", "dev": true}, "@graphql-yoga/subscription": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/@graphql-yoga/subscription/-/subscription-2.0.0.tgz", "integrity": "sha512-HlG+gIddjIP3/BDrMZymdzmzDjNdYuSGMxx6+1JA83gAEVRDR4yOoT4QeNKYqRhLK9xca/Hxp1PfBpquSa244Q==", "dev": true, "requires": {"@repeaterjs/repeater": "^3.0.4", "tslib": "^2.3.1"}}, "@josephg/resolvable": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@josephg/resolvable/-/resolvable-1.0.1.tgz", "integrity": "sha512-CtzORUwWTTOTqfVtHaKRJ0I1kNQd1bpn3sUh8I3nJDVY+5/M/Oe1DnEWzPQvqq/xPIIkzzzIP7mfCoAjFRvDhg=="}, "@jridgewell/gen-mapping": {"version": "0.1.1", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.1.1.tgz", "integrity": "sha512-sQXCasFk+U8lWYEe66WxRDOE9PjVz4vSM51fTu3Hw+ClTpUSQb718772vH3pyS5pShp6lvQM7SxgIDXXXmOX7w==", "dev": true, "peer": true, "requires": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "@jridgewell/resolve-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "dev": true}, "@jridgewell/set-array": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==", "dev": true}, "@jridgewell/sourcemap-codec": {"version": "1.4.14", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.14.tgz", "integrity": "sha512-XPSJHWmi394fuUuzDnGz1wiKqWfo1yXecHQMRf2l6hztTO+nPru658AyDngaBe7isIxEkRsPR3FZh+s7iVa4Uw==", "dev": true}, "@jridgewell/trace-mapping": {"version": "0.3.17", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.17.tgz", "integrity": "sha512-MCNzAp77qzKca9+W/+I0+sEpaUnZoeasnghNeVc41VZCEKaCH73Vq3BZZ/SzWIgrqE4H4ceI+p+b6C0mHf9T4g==", "dev": true, "requires": {"@jridgewell/resolve-uri": "3.1.0", "@jridgewell/sourcemap-codec": "1.4.14"}}, "@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "requires": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}}, "@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true}, "@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "requires": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}}, "@peculiar/asn1-schema": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/@peculiar/asn1-schema/-/asn1-schema-2.3.3.tgz", "integrity": "sha512-6GptMYDMyWBHTUKndHaDsRZUO/XMSgIns2krxcm2L7SEExRHwawFvSwNBhqNPR9HJwv3MruAiF1bhN0we6j6GQ==", "dev": true, "requires": {"asn1js": "^3.0.5", "pvtsutils": "^1.3.2", "tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@peculiar/json-schema": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/@peculiar/json-schema/-/json-schema-1.1.12.tgz", "integrity": "sha512-coUfuoMeIB7B8/NMekxaDzLhaYmp0HZNPEjYRm9goRou8UZIC3z21s0sL9AWoCw4EG876QyO3kYrc61WNF9B/w==", "dev": true, "requires": {"tslib": "^2.0.0"}}, "@peculiar/webcrypto": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/@peculiar/webcrypto/-/webcrypto-1.4.1.tgz", "integrity": "sha512-eK4C6WTNYxoI7JOabMoZICiyqRRtJB220bh0Mbj5RwRycleZf9BPyZoxsTvpP0FpmVS2aS13NKOuh5/tN3sIRw==", "dev": true, "requires": {"@peculiar/asn1-schema": "^2.3.0", "@peculiar/json-schema": "^1.1.12", "pvtsutils": "^1.3.2", "tslib": "^2.4.1", "webcrypto-core": "^1.7.4"}, "dependencies": {"tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "@protobufjs/aspromise": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/aspromise/-/aspromise-1.1.2.tgz", "integrity": "sha1-m4sMxmPWaafY9vXQiToU00jzD78="}, "@protobufjs/base64": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/base64/-/base64-1.1.2.tgz", "integrity": "sha512-AZkcAA5vnN/v4PDqKyMR5lx7hZttPDgClv83E//FMNhR2TMcLUhfRUBHCmSl0oi9zMgDDqRUJkSxO3wm85+XLg=="}, "@protobufjs/codegen": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/@protobufjs/codegen/-/codegen-2.0.4.tgz", "integrity": "sha512-YyFaikqM5sH0ziFZCN3xDC7zeGaB/d0IUb9CATugHWbd1FRFwWwt4ld4OYMPWu5a3Xe01mGAULCdqhMlPl29Jg=="}, "@protobufjs/eventemitter": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/eventemitter/-/eventemitter-1.1.0.tgz", "integrity": "sha1-NVy8mLr61ZePntCV85diHx0Ga3A="}, "@protobufjs/fetch": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/fetch/-/fetch-1.1.0.tgz", "integrity": "sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=", "requires": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "@protobufjs/float": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/@protobufjs/float/-/float-1.0.2.tgz", "integrity": "sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E="}, "@protobufjs/inquire": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/inquire/-/inquire-1.1.0.tgz", "integrity": "sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik="}, "@protobufjs/path": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@protobufjs/path/-/path-1.1.2.tgz", "integrity": "sha1-bMKyDFya1q0NzP0hynZz2Nf79o0="}, "@protobufjs/pool": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/pool/-/pool-1.1.0.tgz", "integrity": "sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q="}, "@protobufjs/utf8": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@protobufjs/utf8/-/utf8-1.1.0.tgz", "integrity": "sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA="}, "@repeaterjs/repeater": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@repeaterjs/repeater/-/repeater-3.0.4.tgz", "integrity": "sha512-AW8PKd6iX3vAZ0vA43nOUOnbq/X5ihgU+mSXXqunMkeQADGiqw/PY0JNeYtD5sr0PAy51YPgAPbDoeapv9r8WA==", "dev": true}, "@types/accepts": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/@types/accepts/-/accepts-1.3.5.tgz", "integrity": "sha512-jOdnI/3qTpHABjM5cx1Hc0sKsPoYCp+DP/GJRGtDlPd7fiV9oXGGIcjW/ZOxLIvjGz8MA+uMZI9metHlgqbgwQ==", "requires": {"@types/node": "*"}}, "@types/body-parser": {"version": "1.19.2", "resolved": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.2.tgz", "integrity": "sha512-<PERSON><PERSON>one6pm6QmwZoAgeyNksccT9Q4AWZQ6PvfwR37GT6r6FWUPguq6sUmNGSMV2Wr761oQoBxwGGa6DR5o1DC9g==", "requires": {"@types/connect": "*", "@types/node": "*"}}, "@types/connect": {"version": "3.4.35", "resolved": "https://registry.npmjs.org/@types/connect/-/connect-3.4.35.tgz", "integrity": "sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==", "requires": {"@types/node": "*"}}, "@types/cors": {"version": "2.8.12", "resolved": "https://registry.npmjs.org/@types/cors/-/cors-2.8.12.tgz", "integrity": "sha512-vt+kDhq/M2ayberEtJcIN/hxXy1Pk+59g2FV/ZQceeaTyCtCucjL2Q7FXlFjtWn4n15KCr1NE2lNNFhp0lEThw=="}, "@types/express": {"version": "4.17.13", "resolved": "https://registry.npmjs.org/@types/express/-/express-4.17.13.tgz", "integrity": "sha512-6bSZTPaTIACxn48l50SR+axgrqm6qXFIxrdAKaG6PaJk3+zuUr35hBlgT7vOmJcum+OEaIBLtHV/qloEAFITeA==", "requires": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.18", "@types/qs": "*", "@types/serve-static": "*"}}, "@types/express-serve-static-core": {"version": "4.17.28", "resolved": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.17.28.tgz", "integrity": "sha512-P1BJAEAW3E2DJUlkgq4tOL3RyMunoWXqbSCygWo5ZIWTjUgN1YnaXWW4VWl/oc8vs/XoYibEGBKP0uZyF4AHig==", "requires": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*"}}, "@types/long": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/@types/long/-/long-4.0.2.tgz", "integrity": "sha512-MqTGEo5bj5t157U6fA/BiDynNkn0YknVdh48CMPkTSpFTVmvao5UQmm7uEF6xBEo7qIMAlY/JSleYaE6VOdpaA=="}, "@types/mime": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/@types/mime/-/mime-1.3.2.tgz", "integrity": "sha512-YATxVxgRqNH6nHEIsvg6k2Boc1JHI9ZbH5iWFFv/MTkchz3b1ieGDa5T0a9RznNdI0KhVbdbWSN+KWWrQZRxTw=="}, "@types/node": {"version": "14.18.33", "resolved": "https://registry.npmjs.org/@types/node/-/node-14.18.33.tgz", "integrity": "sha512-qelS/Ra6sacc4loe/3MSjXNL1dNQ/GjxNHVzuChwMfmk7HuycRLVQN2qNY3XahK+fZc5E2szqQSKUyAF0E+2bg=="}, "@types/qs": {"version": "6.9.7", "resolved": "https://registry.npmjs.org/@types/qs/-/qs-6.9.7.tgz", "integrity": "sha512-FGa1F62FT09qcrueBA6qYTrJPVDzah9a+493+o2PCXsesWHIn27G98TsSMs3WPNbZIEj4+VJf6saSFpvD+3Zsw=="}, "@types/range-parser": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.4.tgz", "integrity": "sha512-EEhsLsD6UsDM1yFhAvy0Cjr6VwmpMWqFBCb9w07wVugF7w9nfajxLuVmngTIpgS6svCnm6Vaw+MZhoDCKnOfsw=="}, "@types/serve-static": {"version": "1.13.10", "resolved": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.13.10.tgz", "integrity": "sha512-nCkHGI4w7ZgAdNkrEu0bv+4xNV/XDqW+DydknebMOQwkpDGx8G+HTlj7R7ABI8i8nKxVw0wtKPi1D+lPOkh4YQ==", "requires": {"@types/mime": "^1", "@types/node": "*"}}, "@types/ws": {"version": "8.5.3", "resolved": "https://registry.npmjs.org/@types/ws/-/ws-8.5.3.tgz", "integrity": "sha512-6YOoWjruKj1uLf3INHH7D3qTXwFfEsg1kf3c0uDdSBJwfa/llkwIjrAGV7j7mVgGNbzTQ3HiHKKDXl6bJPD97w==", "dev": true, "requires": {"@types/node": "*"}}, "@whatwg-node/fetch": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/@whatwg-node/fetch/-/fetch-0.5.3.tgz", "integrity": "sha512-cuAKL3Z7lrJJuUrfF1wxkQTb24Qd1QO/lsjJpM5ZSZZzUMms5TPnbGeGUKWA3hVKNHh30lVfr2MyRCT5Jfkucw==", "dev": true, "requires": {"@peculiar/webcrypto": "^1.4.0", "abort-controller": "^3.0.0", "busboy": "^1.6.0", "form-data-encoder": "^1.7.1", "formdata-node": "^4.3.1", "node-fetch": "^2.6.7", "undici": "^5.12.0", "web-streams-polyfill": "^3.2.0"}}, "@whatwg-node/promise-helpers": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@whatwg-node/promise-helpers/-/promise-helpers-1.2.1.tgz", "integrity": "sha512-+faGtJlS4U8NSaSzRVN37xAprPdhoobYzUSUo4DgH8APtfFyizmNxp0ckwKcURoL8cy2B+bKxOWU/VIH2nFeLg==", "requires": {"tslib": "^2.6.3"}, "dependencies": {"tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="}}}, "abort-controller": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz", "integrity": "sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==", "dev": true, "requires": {"event-target-shim": "^5.0.0"}}, "accepts": {"version": "1.3.8", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "integrity": "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==", "requires": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}}, "ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true}, "ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "requires": {"color-convert": "^2.0.1"}}, "apollo-datasource": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/apollo-datasource/-/apollo-datasource-3.3.1.tgz", "integrity": "sha512-Z3a8rEUXVPIZ1p8xrFL8bcNhWmhOmovgDArvwIwmJOBnh093ZpRfO+ESJEDAN4KswmyzCLDAwjsW4zQOONdRUw==", "requires": {"apollo-server-caching": "^3.3.0", "apollo-server-env": "^4.2.1"}}, "apollo-reporting-protobuf": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/apollo-reporting-protobuf/-/apollo-reporting-protobuf-3.3.1.tgz", "integrity": "sha512-tyvj3Vj71TCh6c8PtdHOLgHHBSJ05DF/A/Po3q8yfHTBkOPcOJZE/GGN/PT/pwKg7HHxKcAeHDw7+xciVvGx0w==", "requires": {"@apollo/protobufjs": "1.2.2"}}, "apollo-server": {"version": "3.6.7", "resolved": "https://registry.npmjs.org/apollo-server/-/apollo-server-3.6.7.tgz", "integrity": "sha512-WERZqaVCkZJMoOc9y692NribgNtKbHDjOwiAmgXI2WBlON2oUvCwgxPvsMg+bXVpQx4itrMyj31a2N6BeKmbmQ==", "requires": {"apollo-server-core": "^3.6.7", "apollo-server-express": "^3.6.7", "express": "^4.17.1"}}, "apollo-server-caching": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/apollo-server-caching/-/apollo-server-caching-3.3.0.tgz", "integrity": "sha512-Wgcb0ArjZ5DjQ7ID+tvxUcZ7Yxdbk5l1MxZL8D8gkyjooOkhPNzjRVQ7ubPoXqO54PrOMOTm1ejVhsF+AfIirQ==", "requires": {"lru-cache": "^6.0.0"}}, "apollo-server-core": {"version": "3.6.7", "resolved": "https://registry.npmjs.org/apollo-server-core/-/apollo-server-core-3.6.7.tgz", "integrity": "sha512-OnZ9vu7LrYy2rvEu+nbgqucw6VyTSIPAEjK87c4rkzlVOxpwtGUaQ4FMWD9zBIj7yLq9q22b638E8LdYoaTAjQ==", "requires": {"@apollographql/apollo-tools": "^0.5.3", "@apollographql/graphql-playground-html": "1.6.29", "@graphql-tools/mock": "^8.1.2", "@graphql-tools/schema": "^8.0.0", "@josephg/resolvable": "^1.0.0", "apollo-datasource": "^3.3.1", "apollo-reporting-protobuf": "^3.3.1", "apollo-server-caching": "^3.3.0", "apollo-server-env": "^4.2.1", "apollo-server-errors": "^3.3.1", "apollo-server-plugin-base": "^3.5.2", "apollo-server-types": "^3.5.2", "async-retry": "^1.2.1", "fast-json-stable-stringify": "^2.1.0", "graphql-tag": "^2.11.0", "lodash.sortby": "^4.7.0", "loglevel": "^1.6.8", "lru-cache": "^6.0.0", "sha.js": "^2.4.11", "uuid": "^8.0.0"}}, "apollo-server-env": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/apollo-server-env/-/apollo-server-env-4.2.1.tgz", "integrity": "sha512-vm/7c7ld+zFMxibzqZ7SSa5tBENc4B0uye9LTfjJwGoQFY5xsUPH5FpO5j0bMUDZ8YYNbrF9SNtzc5Cngcr90g==", "requires": {"node-fetch": "^2.6.7"}}, "apollo-server-errors": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/apollo-server-errors/-/apollo-server-errors-3.3.1.tgz", "integrity": "sha512-xnZJ5QWs6FixHICXHxUfm+ZWqqxrNuPlQ+kj5m6RtEgIpekOPssH/SD9gf2B4HuWV0QozorrygwZnux8POvyPA==", "requires": {}}, "apollo-server-express": {"version": "3.6.7", "resolved": "https://registry.npmjs.org/apollo-server-express/-/apollo-server-express-3.6.7.tgz", "integrity": "sha512-B4gH5j8t3XxTCIa9bl7Iq/F3YFzMxX/LV4Sc+L/3xkHm648u576G5Lkskl8HsoTGSzzyeVcVsPDoYHiBjCAN0Q==", "requires": {"@types/accepts": "^1.3.5", "@types/body-parser": "1.19.2", "@types/cors": "2.8.12", "@types/express": "4.17.13", "@types/express-serve-static-core": "4.17.28", "accepts": "^1.3.5", "apollo-server-core": "^3.6.7", "apollo-server-types": "^3.5.2", "body-parser": "^1.19.0", "cors": "^2.8.5", "parseurl": "^1.3.3"}}, "apollo-server-plugin-base": {"version": "3.5.2", "resolved": "https://registry.npmjs.org/apollo-server-plugin-base/-/apollo-server-plugin-base-3.5.2.tgz", "integrity": "sha512-SwIf1waDmNDb0kmn57QR++InwK6Iv/X2slpm/aFIoqFBe91r6uJfakJvQZuh8dLEgk68gxqFsT8zHRpxBclE+g==", "requires": {"apollo-server-types": "^3.5.2"}}, "apollo-server-types": {"version": "3.5.2", "resolved": "https://registry.npmjs.org/apollo-server-types/-/apollo-server-types-3.5.2.tgz", "integrity": "sha512-vhcbIWsBkoNibABOym4AAPBoNDjokhjUQokKYdwZMeqrb850PMQdNJFrGyXT5onP408Ghv4O8PfgBuPQmeJhVQ==", "requires": {"apollo-reporting-protobuf": "^3.3.1", "apollo-server-caching": "^3.3.0", "apollo-server-env": "^4.2.1"}}, "argparse": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "dev": true}, "array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI="}, "array-union": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz", "integrity": "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==", "dev": true}, "asn1js": {"version": "3.0.5", "resolved": "https://registry.npmjs.org/asn1js/-/asn1js-3.0.5.tgz", "integrity": "sha512-FVnvrKJwpt9LP2lAMl8qZswRNm3T4q9CON+bxldk2iwk3FFpuwhx2FfinyitizWHsVYyaY+y5JzDR0rCMV5yTQ==", "dev": true, "requires": {"pvtsutils": "^1.3.2", "pvutils": "^1.1.3", "tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "async-retry": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/async-retry/-/async-retry-1.3.3.tgz", "integrity": "sha512-wfr/jstw9xNi/0teMHrRW7dsz3Lt5ARhYNZ2ewpadnhaIp5mbALhOAP+EAdsC7t4Z6wqsDVv9+W6gm1Dk9mEyw==", "requires": {"retry": "0.13.1"}}, "axios": {"version": "0.26.1", "resolved": "https://registry.npmjs.org/axios/-/axios-0.26.1.tgz", "integrity": "sha512-fPwcX4EvnSHuInCMItEhAGnaSEXRBjtzh9fOtsE6E1G6p7vl7edEeZe11QHf18+6+9gR5PbKV/sGKNaD8YaMeA==", "dev": true, "requires": {"follow-redirects": "^1.14.8"}}, "axios-proxy-builder": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/axios-proxy-builder/-/axios-proxy-builder-0.1.2.tgz", "integrity": "sha512-6uBVsBZzkB3tCC8iyx59mCjQckhB8+GQrI9Cop8eC7ybIsvs/KtnNgEBfRMSEa7GqK2VBGUzgjNYMdPIfotyPA==", "dev": true, "requires": {"tunnel": "^0.0.6"}}, "balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "dev": true}, "binary-install": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/binary-install/-/binary-install-1.0.6.tgz", "integrity": "sha512-h3K4jaC4jEauK3csXI9GxGBJldkpuJlHCIBv8i+XBNhPuxnlERnD1PWVczQYDqvhJfv0IHUbB3lhDrZUMHvSgw==", "dev": true, "requires": {"axios": "^0.26.1", "rimraf": "^3.0.2", "tar": "^6.1.11"}}, "body-parser": {"version": "1.20.0", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.0.tgz", "integrity": "sha512-DfJ+q6EPcGKZD1QWUjSpqp+Q7bDQTsQIF4zfUAtZ6qk+H/3/QRhg9CEp39ss+/T2vw0+HaidC0ecJj/DRLIaKg==", "requires": {"bytes": "3.1.2", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.10.3", "raw-body": "2.5.1", "type-is": "~1.6.18", "unpipe": "1.0.0"}}, "brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "braces": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.2.tgz", "integrity": "sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==", "dev": true, "requires": {"fill-range": "^7.0.1"}}, "browserslist": {"version": "4.21.4", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.21.4.tgz", "integrity": "sha512-CBHJJdDmgjl3daYjN5Cp5kbTf1mUhZoS+beLklHIvkOWscs83YAhLlF3Wsh/lciQYAcbBJgTOD44VtG31ZM4Hw==", "dev": true, "peer": true, "requires": {"caniuse-lite": "^1.0.30001400", "electron-to-chromium": "^1.4.251", "node-releases": "^2.0.6", "update-browserslist-db": "^1.0.9"}}, "busboy": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/busboy/-/busboy-1.6.0.tgz", "integrity": "sha512-8SFQbg/0hQ9xy3UNTB0YEnsNBbWfhf7RtnzpL7TkBiTBRfrQ9Fxcnz7VJsleJpyp6rVLvXiuORqjlHi5q+PYuA==", "dev": true, "requires": {"streamsearch": "^1.1.0"}}, "bytes": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg=="}, "call-bind": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.2.tgz", "integrity": "sha512-7O+FbCihrB5WGbFYesctwmTKae6rOiIzmz1icreWJ+0aA7LJfuqhEso2T9ncpcFtzMQtzXf2QGGueWJGTYsqrA==", "requires": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}}, "caniuse-lite": {"version": "1.0.30001439", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001439.tgz", "integrity": "sha512-1MgUzEkoMO6gKfXflStpYgZDlFM7M/ck/bgfVCACO5vnAf0fXoNVHdWtqGU+MYca+4bL9Z5bpOVmR33cWW9G2A==", "dev": true, "peer": true}, "chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "chownr": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/chownr/-/chownr-2.0.0.tgz", "integrity": "sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==", "dev": true}, "ci-info": {"version": "3.7.0", "resolved": "https://registry.npmjs.org/ci-info/-/ci-info-3.7.0.tgz", "integrity": "sha512-2CpRNYmImPx+RXKLq6jko/L07phmS9I02TyqkcNU20GCF/GgaWvc58hPtjxDX8lPpkdwc9sNh72V9k00S7ezog==", "dev": true}, "cli-table3": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/cli-table3/-/cli-table3-0.6.2.tgz", "integrity": "sha512-QyavHCaIC80cMivimWu4aWHilIpiDpfm3hGmqAmXVL1UsnbLuBSMd21hTX6VY4ZSDSM73ESLeF8TOYId3rBTbw==", "dev": true, "requires": {"@colors/colors": "1.5.0", "string-width": "^4.2.0"}}, "cliui": {"version": "7.0.4", "resolved": "https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz", "integrity": "sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==", "dev": true, "requires": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "clone": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "integrity": "sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==", "dev": true, "optional": true}, "color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true}, "commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="}, "concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true}, "console.table": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/console.table/-/console.table-0.10.0.tgz", "integrity": "sha512-dPyZofqggxuvSf7WXvNjuRfnsOk1YazkVP8FdxH4tcH2c37wc79/Yl6Bhr7Lsu00KMgy2ql/qCMuNu8xctZM8g==", "dev": true, "requires": {"easy-table": "1.1.0"}}, "content-disposition": {"version": "0.5.4", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==", "requires": {"safe-buffer": "5.2.1"}}, "content-type": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.4.tgz", "integrity": "sha512-hIP3EEPs8tB9AT1L+NUqtwOAps4mk2Zob89MWXMHjHWg9milF/j4osnnQLXBCBFBk/tvIG/tUc9mOUJiPBhPXA=="}, "convert-source-map": {"version": "1.9.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==", "dev": true, "peer": true}, "cookie": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.5.0.tgz", "integrity": "sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw=="}, "cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw="}, "cors": {"version": "2.8.5", "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "requires": {"object-assign": "^4", "vary": "^1"}}, "cross-inspect": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/cross-inspect/-/cross-inspect-1.0.1.tgz", "integrity": "sha512-Pcw1JTvZLSJH83iiGWt6fRcT+BjZlCDRVwYLbUcHzv/CRpB7r0MlSrGbIyQvVSNyGnbt7G4AXuyCiDR3POvZ1A==", "requires": {"tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="}}}, "cross-undici-fetch": {"version": "0.1.33", "resolved": "https://registry.npmjs.org/cross-undici-fetch/-/cross-undici-fetch-0.1.33.tgz", "integrity": "sha512-2IljikrWuhEroSlidX0i/TXjL01IIQUigWC/9tq7U3C/GoQQBqc02xNIVTqoZG6nPn/P9C9YMH78A4aTLUticQ==", "dev": true, "requires": {"abort-controller": "^3.0.0", "form-data-encoder": "^1.7.1", "formdata-node": "^4.3.1", "node-fetch": "^2.6.7", "undici": "^5.0.0", "web-streams-polyfill": "^3.2.0"}}, "cssfilter": {"version": "0.0.10", "resolved": "https://registry.npmjs.org/cssfilter/-/cssfilter-0.0.10.tgz", "integrity": "sha1-xtJnJjKi5cg+AT5oZKQs6N79IK4="}, "dataloader": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/dataloader/-/dataloader-2.1.0.tgz", "integrity": "sha512-qTcEYLen3r7ojZNgVUaRggOI+KM7jrKxXeSHhogh/TWxYMeONEMqY+hmkobiYQozsGIyg9OYVzO4ZIfoB4I0pQ==", "dev": true}, "debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "requires": {"ms": "2.0.0"}}, "defaults": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz", "integrity": "sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==", "dev": true, "optional": true, "requires": {"clone": "^1.0.2"}}, "define-lazy-prop": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "integrity": "sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==", "dev": true}, "depd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw=="}, "dependency-graph": {"version": "0.11.0", "resolved": "https://registry.npmjs.org/dependency-graph/-/dependency-graph-0.11.0.tgz", "integrity": "sha512-JeMq7fEshyepOWDfcfHK06N3MhyPhz++vtqWhMT5O9A3K42rdsEDpfdVqjaqaAhsw6a+ZqeDvQVtD0hFHQWrzg==", "dev": true}, "destroy": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg=="}, "detect-libc": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.1.tgz", "integrity": "sha512-463v3ZeIrcWtdgIg6vI6XUncguvr2TnGl4SzDXinkt9mSLpBJKXT3mW6xT3VQdDN11+WVs29pgvivTc4Lp8v+w==", "dev": true}, "dir-glob": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==", "dev": true, "requires": {"path-type": "^4.0.0"}}, "dset": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/dset/-/dset-3.1.2.tgz", "integrity": "sha512-g/M9sqy3oHe477Ar4voQxWtaPIFw1jTdKZuomOjhCcBx9nHUNn0pu6NopuFFrTh/TRZIKEj+76vLWFu9BNKk+Q==", "dev": true}, "easy-table": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/easy-table/-/easy-table-1.1.0.tgz", "integrity": "sha512-oq33hWOSSnl2Hoh00tZWaIPi1ievrD9aFG82/IgjlycAnW9hHx5PkJiXpxPsgEE+H7BsbVQXFVFST8TEXS6/pA==", "dev": true, "requires": {"wcwidth": ">=1.0.1"}}, "ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0="}, "electron-to-chromium": {"version": "1.4.284", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.284.tgz", "integrity": "sha512-M8WEXFuKXMYMVr45fo8mq0wUrrJHheiKZf6BArTKk9ZBYCKJEOU5H8cdWgDT+qCVZf7Na4lVUaZsA+h6uA9+PA==", "dev": true, "peer": true}, "emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "dev": true}, "encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k="}, "escalade": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.1.1.tgz", "integrity": "sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==", "dev": true}, "escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==", "dev": true}, "etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc="}, "event-target-shim": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "integrity": "sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==", "dev": true}, "express": {"version": "4.18.0", "resolved": "https://registry.npmjs.org/express/-/express-4.18.0.tgz", "integrity": "sha512-EJEXxiTQJS3lIPrU1AE2vRuT7X7E+0KBbpm5GSoK524yl0K8X+er8zS2P14E64eqsVNoWbMCT7MpmQ+ErAhgRg==", "requires": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.0", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.5.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.10.3", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}}, "extract-files": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/extract-files/-/extract-files-11.0.0.tgz", "integrity": "sha512-FuoE1qtbJ4bBVvv94CC7s0oTnKUGvQs+Rjf1L2SJFfS+HTVVjhPFtehPdQ0JiGPqVNfSSZvL5yzHHQq2Z4WNhQ==", "dev": true}, "fast-glob": {"version": "3.2.11", "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.2.11.tgz", "integrity": "sha512-xrO3+1bxSo3ZVHAnqzyuewYT6aMFHRAd4Kcs92MAonjwQZLsK9d0SF1IyQ3k5PoirxTW0Oe/RqFgMQ6TcNE5Ew==", "dev": true, "requires": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="}, "fastq": {"version": "1.13.0", "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.13.0.tgz", "integrity": "sha512-YpkpUnK8od0o1hmeSc7UUs/eB/vIPWJYjKck2QKIzAf71Vm1AAQ3EbuZB3g2JIy+pg+ERD0vqI79KyZiB2e2Nw==", "dev": true, "requires": {"reusify": "^1.0.4"}}, "figures": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/figures/-/figures-3.2.0.tgz", "integrity": "sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==", "dev": true, "requires": {"escape-string-regexp": "^1.0.5"}}, "fill-range": {"version": "7.0.1", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==", "dev": true, "requires": {"to-regex-range": "^5.0.1"}}, "finalhandler": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz", "integrity": "sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==", "requires": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}}, "follow-redirects": {"version": "1.15.2", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.2.tgz", "integrity": "sha512-VQLG33o04KaQ8uYi2tVNbdrWp1QWxNNea+nmIB4EVM28v0hmP17z7aG1+wAkNzVq4KeXTq3221ye5qTJP91JwA==", "dev": true}, "form-data-encoder": {"version": "1.7.2", "resolved": "https://registry.npmjs.org/form-data-encoder/-/form-data-encoder-1.7.2.tgz", "integrity": "sha512-qfqtYan3rxrnCk1VYaA4H+Ms9xdpPqvLZa6xmMgFvhO32x7/3J/ExcTd6qpxM0vH2GdMI+poehyBZvqfMTto8A==", "dev": true}, "formdata-node": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/formdata-node/-/formdata-node-4.4.1.tgz", "integrity": "sha512-0iirZp3uVDjVGt9p49aTaqjk84TrglENEDuqfdlZQ1roC9CWlPk6Avf8EEnZNcAqPonwkG35x4n3ww/1THYAeQ==", "dev": true, "requires": {"node-domexception": "1.0.0", "web-streams-polyfill": "4.0.0-beta.3"}, "dependencies": {"web-streams-polyfill": {"version": "4.0.0-beta.3", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-4.0.0-beta.3.tgz", "integrity": "sha512-QW95TCTaHmsYfHDybGMwO5IJIM93I/6vTRk+daHTWFPhwh+C8Cg7j7XyKrwrj8Ib6vYXe0ocYNrmzY4xAAN6ug==", "dev": true}}}, "forwarded": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow=="}, "fresh": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac="}, "fs-minipass": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fs-minipass/-/fs-minipass-2.1.0.tgz", "integrity": "sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==", "dev": true, "requires": {"minipass": "^3.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "resolved": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "dev": true, "requires": {"yallist": "^4.0.0"}}}}, "fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true}, "function-bind": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true, "peer": true}, "get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "dev": true}, "get-intrinsic": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.1.1.tgz", "integrity": "sha512-kWZrnVM42QCiEA2Ig1bG8zjoIMOgxWwYCEeNdwY6Tv/cOSeGpcoX4pXHfKUxNKVoArnrEr2e9srnAxxGIraS9Q==", "requires": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-symbols": "^1.0.1"}}, "glob": {"version": "7.2.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "dev": true, "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "requires": {"is-glob": "^4.0.1"}}, "globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true}, "globby": {"version": "11.0.4", "resolved": "https://registry.npmjs.org/globby/-/globby-11.0.4.tgz", "integrity": "sha512-9O4MVG9ioZJ08ffbcyVYyLOJLk5JQ688pJ4eMGLpdWLHq/Wr1D9BlriLQyL0E+jbkuePVZXYFj47QM/v093wHg==", "dev": true, "requires": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.1.1", "ignore": "^5.1.4", "merge2": "^1.3.0", "slash": "^3.0.0"}}, "graphql": {"version": "16.4.0", "resolved": "https://registry.npmjs.org/graphql/-/graphql-16.4.0.tgz", "integrity": "sha512-tYDNcRvKCcfHREZYje3v33NSrSD/ZpbWWdPtBtUUuXx9NCo/2QDxYzNqCnMvfsrnbwRpEHMovVrPu/ERoLrIRg=="}, "graphql-tag": {"version": "2.12.6", "resolved": "https://registry.npmjs.org/graphql-tag/-/graphql-tag-2.12.6.tgz", "integrity": "sha512-FdSNcu2QQcWnM2VNvSCCDCVS5PpPqpzgFT8+GXzqJuoDd0CBncxCY278u4mhRO7tMgo2JjgJA5aZ+nWSQ/Z+xg==", "requires": {"tslib": "^2.1.0"}}, "graphql-ws": {"version": "5.11.2", "resolved": "https://registry.npmjs.org/graphql-ws/-/graphql-ws-5.11.2.tgz", "integrity": "sha512-4EiZ3/UXYcjm+xFGP544/yW1+DVI8ZpKASFbzrV5EDTFWJp0ZvLl4Dy2fSZAzz9imKp5pZMIcjB0x/H69Pv/6w==", "dev": true, "requires": {}}, "has": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true}, "has-symbols": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A=="}, "http-errors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "requires": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}}, "iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "requires": {"safer-buffer": ">= 2.1.2 < 3"}}, "ignore": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/ignore/-/ignore-5.2.0.tgz", "integrity": "sha512-CmxgYGiEPCLhfLnpPp1MoRmifwEIOgjcHXxOBjv7mY96c+eWScsOP9c112ZyLdWHi0FxHjI+4uVhKYp/gcdRmQ==", "dev": true}, "inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "dev": true, "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "ipaddr.js": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g=="}, "is-docker": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "integrity": "sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==", "dev": true}, "is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "dev": true}, "is-glob": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "requires": {"is-extglob": "^2.1.1"}}, "is-number": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true}, "is-unicode-supported": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz", "integrity": "sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==", "dev": true}, "is-wsl": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "integrity": "sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==", "dev": true, "requires": {"is-docker": "^2.0.0"}}, "isomorphic-ws": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/isomorphic-ws/-/isomorphic-ws-5.0.0.tgz", "integrity": "sha512-muId7Zzn9ywDsyXgTIafTry2sV3nySZeUDe6YedVd1Hvuuep5AsIlqK+XefWpYTyJG5e503F2xIuT2lcU6rCSw==", "dev": true, "requires": {}}, "js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "js-yaml": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "dev": true, "requires": {"argparse": "^2.0.1"}}, "jsesc": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==", "dev": true}, "json5": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.1.tgz", "integrity": "sha512-1hqLFMSrGHRHxav9q9gNjJ5EXznIxGVO09xQRrwplcS8qs28pZ8s8hupZAmqDwZUmVZ2Qb2jnyPOWcDH8m8dlA==", "dev": true, "peer": true}, "lodash.sortby": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/lodash.sortby/-/lodash.sortby-4.7.0.tgz", "integrity": "sha1-7dFMgk4sycHgsKG0K7UhBRakJDg="}, "log-symbols": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz", "integrity": "sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==", "dev": true, "requires": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}}, "loglevel": {"version": "1.8.0", "resolved": "https://registry.npmjs.org/loglevel/-/loglevel-1.8.0.tgz", "integrity": "sha512-G6A/nJLRgWOuuwdNuA6koovfEV1YpqqAG4pRUlFaz3jj2QNZ8M4vBqnVA+HBTmU/AMNUtlOsMmSpF6NyOjztbA=="}, "long": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/long/-/long-4.0.0.tgz", "integrity": "sha512-XsP+KhQif4bjX1kbuSiySJFNAehNxgLb6hPRGJ9QsUr8ajHkuXGdrHmFUTUUXhDwVX2R5bY4JNZEwbUiMhV+MA=="}, "lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "requires": {"yallist": "^4.0.0"}}, "media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g="}, "merge-descriptors": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E="}, "merge2": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true}, "meros": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/meros/-/meros-1.2.1.tgz", "integrity": "sha512-R2f/jxYqCAGI19KhAvaxSOxALBMkaXWH2a7rOyqQw+ZmizX5bKkEYWLzdhC+U82ZVVPVp6MCXe3EkVligh+12g==", "dev": true, "requires": {}}, "methods": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4="}, "micromatch": {"version": "4.0.5", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz", "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "dev": true, "requires": {"braces": "^3.0.2", "picomatch": "^2.3.1"}}, "mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg=="}, "mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="}, "mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "requires": {"mime-db": "1.52.0"}}, "minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "requires": {"brace-expansion": "^1.1.7"}}, "minipass": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/minipass/-/minipass-4.0.0.tgz", "integrity": "sha512-g2Uuh2jEKoht+zvO6vJqXmYpflPqzRBT+Th2h01DKh5z7wbY/AZ2gCQ78cP70YoHPyFdY30YBV5WxgLOEwOykw==", "dev": true, "requires": {"yallist": "^4.0.0"}}, "minizlib": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/minizlib/-/minizlib-2.1.2.tgz", "integrity": "sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==", "dev": true, "requires": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "dependencies": {"minipass": {"version": "3.3.6", "resolved": "https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz", "integrity": "sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==", "dev": true, "requires": {"yallist": "^4.0.0"}}}}, "mkdirp": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "integrity": "sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==", "dev": true}, "ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g="}, "negotiator": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg=="}, "node-domexception": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/node-domexception/-/node-domexception-1.0.0.tgz", "integrity": "sha512-/jKZoMpw0F8GRwl4/eLROPA3cfcXtLApP0QzLmUT/HuPCZWyB7IY9ZrMeKw2O/nFIqPQB3PVM9aYm0F312AXDQ==", "dev": true}, "node-fetch": {"version": "2.6.7", "resolved": "https://registry.npmjs.org/node-fetch/-/node-fetch-2.6.7.tgz", "integrity": "sha512-ZjMPFEfVx5j+y2yF35Kzx5sF7kDzxuDj6ziH4FFbOp87zKDZNx8yExJIb05OGF4Nlt9IHFIMBkRl41VdvcNdbQ==", "requires": {"whatwg-url": "^5.0.0"}}, "node-releases": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.7.tgz", "integrity": "sha512-EJ3rzxL9pTWPjk5arA0s0dgXpnyiAbJDE6wHT62g7VsgrgQgmmZ+Ru++M1BFofncWja+Pnn3rEr3fieRySAdKQ==", "dev": true, "peer": true}, "normalize-path": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "requires": {"remove-trailing-separator": "^1.0.1"}}, "object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM="}, "object-inspect": {"version": "1.12.0", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.12.0.tgz", "integrity": "sha512-Ho2z80bVIvJloH+YzRmpZVQe87+qASmBUKZDWgx9cu+KDrX2ZDH/3tMy+gXbZETVGs2M8YdxObOh7XAtim9Y0g=="}, "on-finished": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "requires": {"ee-first": "1.1.1"}}, "once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "requires": {"wrappy": "1"}}, "open": {"version": "8.2.1", "resolved": "https://registry.npmjs.org/open/-/open-8.2.1.tgz", "integrity": "sha512-rXILpcQlkF/QuFez2BJDf3GsqpjGKbkUUToAIGo9A0Q6ZkoSGogZJulrUdwRkrAsoQvoZsrjCYt8+zblOk7JQQ==", "dev": true, "requires": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}}, "p-limit": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "dev": true, "requires": {"yocto-queue": "^0.1.0"}}, "parseurl": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ=="}, "path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true}, "path-to-regexp": {"version": "0.1.7", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w="}, "path-type": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "dev": true}, "picocolors": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==", "dev": true, "peer": true}, "picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true}, "proxy-addr": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==", "requires": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}}, "pvtsutils": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/pvtsutils/-/pvtsutils-1.3.2.tgz", "integrity": "sha512-+Ipe2iNUyrZz+8K/2IOo+kKikdtfhRKzNpQbruF2URmqPtoqAs8g3xS7TJvFF2GcPXjh7DkqMnpVveRFq4PgEQ==", "dev": true, "requires": {"tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "pvutils": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/pvutils/-/pvutils-1.1.3.tgz", "integrity": "sha512-pMpnA0qRdFp32b1sJl1wOJNxZLQ2cbQx+k6tjNtZ8CpvVhNqEPRgivZ2WOUev2YMajecdH7ctUPDvEe87nariQ==", "dev": true}, "qs": {"version": "6.10.3", "resolved": "https://registry.npmjs.org/qs/-/qs-6.10.3.tgz", "integrity": "sha512-wr7M2E0OFRfIfJZjKGieI8lBKb7fRCH4Fv5KNPEs7gJ8jadvotdsS08PzOKR7opXhZ/Xkjtt3WF9g38drmyRqQ==", "requires": {"side-channel": "^1.0.4"}}, "queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true}, "range-parser": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg=="}, "raw-body": {"version": "2.5.1", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.1.tgz", "integrity": "sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==", "requires": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}}, "remove-trailing-separator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/remove-trailing-separator/-/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "dev": true}, "resolve-from": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "dev": true}, "retry": {"version": "0.13.1", "resolved": "https://registry.npmjs.org/retry/-/retry-0.13.1.tgz", "integrity": "sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg=="}, "reusify": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz", "integrity": "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==", "dev": true}, "rimraf": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "dev": true, "requires": {"glob": "^7.1.3"}}, "run-parallel": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "requires": {"queue-microtask": "^1.2.2"}}, "safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ=="}, "safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg=="}, "semver": {"version": "6.3.0", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.0.tgz", "integrity": "sha512-b39TBaTSfV6yBrapU89p5fKekE2m/NwnDocOVruQFS1/veMgdzuPcnOM34M6CwxW8jH/lxEa5rBoDeUwu5HHTw==", "dev": true, "peer": true}, "send": {"version": "0.18.0", "resolved": "https://registry.npmjs.org/send/-/send-0.18.0.tgz", "integrity": "sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==", "requires": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "dependencies": {"ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="}}}, "serve-static": {"version": "1.15.0", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz", "integrity": "sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==", "requires": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.18.0"}}, "setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw=="}, "sha.js": {"version": "2.4.11", "resolved": "https://registry.npmjs.org/sha.js/-/sha.js-2.4.11.tgz", "integrity": "sha512-QMEp5B7cftE7APOjk5Y6xgrbWu+WkLVQwk8JNjZ8nKRciZaByEW6MubieAiToS7+dwvrjGhH8jRXz3MVd0AYqQ==", "requires": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "side-channel": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.0.4.tgz", "integrity": "sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==", "requires": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}}, "slash": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "dev": true}, "statuses": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ=="}, "std-env": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/std-env/-/std-env-2.3.0.tgz", "integrity": "sha512-4qT5B45+Kjef2Z6pE0BkskzsH0GO7GrND0wGlTM1ioUe3v0dGYx9ZJH0Aro/YyA8fqQ5EyIKDRjZojJYMFTflw==", "dev": true, "requires": {"ci-info": "^3.0.0"}}, "streamsearch": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/streamsearch/-/streamsearch-1.1.0.tgz", "integrity": "sha512-Mcc5wHehp9aXz1ax6bZUyY5afg9u2rv5cqQI3mRrYkGC8rW2hM02jWuwjtL++LS5qinSyhj2QfLyNsuc+VsExg==", "dev": true}, "string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dev": true, "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "requires": {"ansi-regex": "^5.0.1"}}, "supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "requires": {"has-flag": "^4.0.0"}}, "tar": {"version": "6.1.13", "resolved": "https://registry.npmjs.org/tar/-/tar-6.1.13.tgz", "integrity": "sha512-jdIBIN6LTIe2jqzay/2vtYLlBHa3JF42ot3h1dW8Q0PaAG4v8rm0cvpVePtau5C6OKXGGcgO9q2AMNSWxiLqKw==", "dev": true, "requires": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^4.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}}, "to-fast-properties": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz", "integrity": "sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==", "dev": true}, "to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "requires": {"is-number": "^7.0.0"}}, "toidentifier": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA=="}, "tr46": {"version": "0.0.3", "resolved": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o="}, "tslib": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.3.1.tgz", "integrity": "sha512-77EbyPPpMz+FRFRuAFlWMtmgUWGe9UOG2Z25NqCwiIjRhOf5iKGuzSe5P2w1laq+FkRy4p+PCuVkJSGkzTEKVw=="}, "tunnel": {"version": "0.0.6", "resolved": "https://registry.npmjs.org/tunnel/-/tunnel-0.0.6.tgz", "integrity": "sha512-1h/Lnq9yajKY2PEbBadPXj3VxsDDu844OnaAo52UVmIzIvwwtBPIuNvkjuzBlTWpfJyUbG3ez0KSBibQkj4ojg==", "dev": true}, "type-is": {"version": "1.6.18", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "requires": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}}, "undici": {"version": "5.14.0", "resolved": "https://registry.npmjs.org/undici/-/undici-5.14.0.tgz", "integrity": "sha512-yJlHYw6yXPPsuOH0x2Ib1Km61vu4hLiRRQoafs+WUgX1vO64vgnxiCEN9dpIrhZyHFsai3F0AEj4P9zy19enEQ==", "dev": true, "requires": {"busboy": "^1.6.0"}}, "unixify": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unixify/-/unixify-1.0.0.tgz", "integrity": "sha1-OmQcjC/7zk2mg6XHDwOkYpQMIJA=", "dev": true, "requires": {"normalize-path": "^2.1.1"}}, "unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw="}, "update-browserslist-db": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.10.tgz", "integrity": "sha512-OztqDenkfFkbSG+tRxBeAnCVPckDBcvibKd35yDONx6OU8N7sqgwc7rCbkJ/WcYtVRZ4ba68d6byhC21GFh7sQ==", "dev": true, "peer": true, "requires": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}}, "utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM="}, "uuid": {"version": "8.3.2", "resolved": "https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz", "integrity": "sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg=="}, "value-or-promise": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/value-or-promise/-/value-or-promise-1.0.11.tgz", "integrity": "sha512-41BrgH+dIbCFXClcSapVs5M6GkENd3gQOJpEfPDNa71LsUGMXDL0jMWpI/Rh7WhX+Aalfz2TTS3Zt5pUsbnhLg=="}, "vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw="}, "wcwidth": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz", "integrity": "sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==", "dev": true, "optional": true, "requires": {"defaults": "^1.0.3"}}, "web-streams-polyfill": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/web-streams-polyfill/-/web-streams-polyfill-3.2.1.tgz", "integrity": "sha512-e0MO3wdXWKrLbL0DgGnUV7WHVuw9OUvL4hjgnPkIeEvESk74gAITi5G606JtZPp39cd8HA9VQzCIvA49LpPN5Q==", "dev": true}, "webcrypto-core": {"version": "1.7.5", "resolved": "https://registry.npmjs.org/webcrypto-core/-/webcrypto-core-1.7.5.tgz", "integrity": "sha512-gaExY2/3EHQlRNNNVSrbG2Cg94Rutl7fAaKILS1w8ZDhGxdFOaw6EbCfHIxPy9vt/xwp5o0VQAx9aySPF6hU1A==", "dev": true, "requires": {"@peculiar/asn1-schema": "^2.1.6", "@peculiar/json-schema": "^1.1.12", "asn1js": "^3.0.1", "pvtsutils": "^1.3.2", "tslib": "^2.4.0"}, "dependencies": {"tslib": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.4.1.tgz", "integrity": "sha512-tGyy4dAjRIEwI7BzsB0lynWgOpfqjUdq91XXAlIWD2OwKBH7oCl/GZG/HT4BOHrTlPMOASlMQ7veyTqpmRcrNA==", "dev": true}}}, "webidl-conversions": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "integrity": "sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE="}, "whatwg-url": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "integrity": "sha1-lmRU6HZUYuN2RNNib2dCzotwll0=", "requires": {"tr46": "~0.0.3", "webidl-conversions": "^3.0.0"}}, "wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "dev": true, "requires": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}}, "wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true}, "ws": {"version": "8.11.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.11.0.tgz", "integrity": "sha512-HPG3wQd9sNQoT9xHyNCXoDUa+Xw/VevmY9FoHyQ+g+rrMn4j6FB4np7Z0OhdTgjx6MgQLK7jwSy1YecU1+4Asg==", "dev": true, "requires": {}}, "xss": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/xss/-/xss-1.0.11.tgz", "integrity": "sha512-EimjrjThZeK2MO7WKR9mN5ZC1CSqivSl55wvUK5EtU6acf0rzEE1pN+9ZDrFXJ82BRp3JL38pPE6S4o/rpp1zQ==", "requires": {"commander": "^2.20.3", "cssfilter": "0.0.10"}}, "y18n": {"version": "5.0.8", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "dev": true}, "yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}, "yargs": {"version": "17.2.1", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.2.1.tgz", "integrity": "sha512-XfR8du6ua4K6uLGm5S6fA+FIJom/MdJcFNVY8geLlp2v8GYbOXD4EB1tPNZsRn4vBzKGMgb5DRZMeWuFc2GO8Q==", "dev": true, "requires": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}}, "yargs-parser": {"version": "20.2.9", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz", "integrity": "sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==", "dev": true}, "yocto-queue": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "dev": true}}}