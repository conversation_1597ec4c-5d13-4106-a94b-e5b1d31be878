### Gradle ###
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/
gradle.properties

### STS ###
.apt_generated
.classpath
.factorypath
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

######################
# Project Specific
######################
/target/classes/static/**
/src/test/javascript/coverage/

######################
# Node
######################
/node/
node_tmp/
node_modules/
npm-debug.log.*
/.awcache/*
/.cache-loader/*

######################
# SASS
######################
.sass-cache/

######################
# Eclipse
######################
*.pydevproject
.project
.metadata
tmp/
tmp/**/*
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
/src/main/resources/rebel.xml

# External tool builders
.externalToolBuilders/**

# Locally stored "Eclipse launch configurations"
*.launch

# CDT-specific
.cproject

# PDT-specific
.buildpath

# STS-specific
/.sts4-cache/*

######################
# Gradle
######################
.gradle/
/build/

######################
# Package Files
######################
#*.jar
*.war
*.ear
*.db

######################
# Windows
######################
# Windows image file caches
Thumbs.db

# Folder config file
Desktop.ini

######################
# Mac OSX
######################
.DS_Store
.svn

# Thumbnails
._*

# Files that might appear on external disk
.Spotlight-V100
.Trashes

######################
# Directories
######################
/bin/
/deploy/

######################
# Logs
######################
*.log*

######################
# Others
######################
*.class
*.*~
*~
.merge_file*
xero-auth-test.txt
/src/main/java/com/multiplier/core/contract/kafka/proto/DocumentStatus.java

######################
# Subgraph and Supergraph
######################
supergraph/package.json
supergraph/supergraph-api.json
supergraph/supergraph-schema.graphql
supergraph/supergraph-auth.graphql

######################
# ESLint
######################
.eslintcache
/infrastructure/.terraform/


######################
# Claude AI
######################
.claude/settings.local.json
