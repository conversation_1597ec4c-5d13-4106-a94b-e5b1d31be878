# Introduction
A graph registry for supporting the version control and federation of graphql across multiplier.
This registry uses `graphql-inspector` and `rover` to compose subgraphs from each schema for each microservice and to generate a supergraph respectively.

# Building in Local
## Prerequisites

[Node](https://nodejs.org/en/) - v14 is mandatory prior to installing rover.

```
brew install node@14
```

NPM installation:

```
npm install
```

AWS CLI: [Follow this guide](https://www.notion.so/usemultiplier/Developer-Guide-3512c623331d4c7191f1f8cc1ef86cc9)

## AWS CodeArtifact
The access token to CodeArtifact expires every 12 hours. Refresh it with

```
export CODEARTIFACT_AUTH_TOKEN=`aws codeartifact get-authorization-token --domain multiplier-artifacts --domain-owner ************ --query authorizationToken --output text`
```

## Making a graph change
1. Make the required change to the graphql schema in the `service-schema` folder of your service.
2. To make sure the supergraph is still valid, from the root of the project, run `./gradlew generateSupergraph`.
   - Gradle build cache should evaluate if the supergraph is up-to-date and only regenerate it if necessary.
   - To force regenerate run `./gradlew clean generateSupergraph`.
3. Once your PR is merged, create a new tag on github with the next version number. This version will then be published to CodeArtifact.

### Local testing
To test graph types locally first, you can publish them to your local maven repository. It is recommended to pass a specific version to the
build so that you can easily reference it in your service.

```
./gradlew clean build publishToMavenLocal -PpublishVersion=1.0.2-SNAPSHOT
```

You can now find your version in `~/.m2/repository/com/multiplier/your-service-name/1.0.2-SNAPSHOT`
and have your project depend on it like any other dependency.

```
build.gradle

implementation 'com.multiplier:your-service-name:1.0.2-SNAPSHOT'
```

It is recommended to do a `./gradlew clean build` when you update the dependency to make sure you are pulling the correct version.
